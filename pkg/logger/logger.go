package logger

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/spf13/viper"
)

// Level 日志级别
type Level int

const (
	DebugLevel Level = iota
	InfoLevel
	WarnLevel
	ErrorLevel
	FatalLevel
)

// String 返回日志级别字符串
func (l Level) String() string {
	switch l {
	case DebugLevel:
		return "DEBUG"
	case InfoLevel:
		return "INFO"
	case WarnLevel:
		return "WARN"
	case ErrorLevel:
		return "ERROR"
	case FatalLevel:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Fatal(msg string, fields ...interface{})
	With(fields ...interface{}) Logger
}

// logger 日志实现
type logger struct {
	level   Level
	format  string
	service string
	fields  map[string]interface{}
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp string                 `json:"timestamp"`
	Level     string                 `json:"level"`
	Service   string                 `json:"service"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
}

// NewLogger 创建新的日志实例
func NewLogger() Logger {
	level := parseLevel(viper.GetString("log.level"))
	format := viper.GetString("log.format")
	service := viper.GetString("service.name")
	
	if service == "" {
		service = "paas-platform"
	}
	
	return &logger{
		level:   level,
		format:  format,
		service: service,
		fields:  make(map[string]interface{}),
	}
}

// parseLevel 解析日志级别
func parseLevel(levelStr string) Level {
	switch levelStr {
	case "debug":
		return DebugLevel
	case "info":
		return InfoLevel
	case "warn":
		return WarnLevel
	case "error":
		return ErrorLevel
	case "fatal":
		return FatalLevel
	default:
		return InfoLevel
	}
}

// Debug 输出调试日志
func (l *logger) Debug(msg string, fields ...interface{}) {
	if l.level <= DebugLevel {
		l.log(DebugLevel, msg, fields...)
	}
}

// Info 输出信息日志
func (l *logger) Info(msg string, fields ...interface{}) {
	if l.level <= InfoLevel {
		l.log(InfoLevel, msg, fields...)
	}
}

// Warn 输出警告日志
func (l *logger) Warn(msg string, fields ...interface{}) {
	if l.level <= WarnLevel {
		l.log(WarnLevel, msg, fields...)
	}
}

// Error 输出错误日志
func (l *logger) Error(msg string, fields ...interface{}) {
	if l.level <= ErrorLevel {
		l.log(ErrorLevel, msg, fields...)
	}
}

// Fatal 输出致命错误日志并退出程序
func (l *logger) Fatal(msg string, fields ...interface{}) {
	l.log(FatalLevel, msg, fields...)
	os.Exit(1)
}

// With 添加字段到日志上下文
func (l *logger) With(fields ...interface{}) Logger {
	newFields := make(map[string]interface{})
	
	// 复制现有字段
	for k, v := range l.fields {
		newFields[k] = v
	}
	
	// 添加新字段
	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			key := fmt.Sprintf("%v", fields[i])
			value := fields[i+1]
			newFields[key] = value
		}
	}
	
	return &logger{
		level:   l.level,
		format:  l.format,
		service: l.service,
		fields:  newFields,
	}
}

// log 输出日志
func (l *logger) log(level Level, msg string, fields ...interface{}) {
	entry := LogEntry{
		Timestamp: time.Now().Format(time.RFC3339),
		Level:     level.String(),
		Service:   l.service,
		Message:   msg,
		Fields:    make(map[string]interface{}),
	}
	
	// 添加上下文字段
	for k, v := range l.fields {
		entry.Fields[k] = v
	}
	
	// 添加当前日志字段
	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			key := fmt.Sprintf("%v", fields[i])
			value := fields[i+1]
			entry.Fields[key] = value
		}
	}
	
	// 如果没有额外字段，则不输出 fields
	if len(entry.Fields) == 0 {
		entry.Fields = nil
	}
	
	// 根据格式输出日志
	switch l.format {
	case "json":
		l.outputJSON(entry)
	default:
		l.outputText(entry)
	}
}

// outputJSON 输出 JSON 格式日志
func (l *logger) outputJSON(entry LogEntry) {
	data, err := json.Marshal(entry)
	if err != nil {
		fmt.Fprintf(os.Stderr, "日志序列化失败: %v\n", err)
		return
	}
	
	fmt.Println(string(data))
}

// outputText 输出文本格式日志
func (l *logger) outputText(entry LogEntry) {
	output := fmt.Sprintf("[%s] %s %s: %s",
		entry.Timestamp,
		entry.Level,
		entry.Service,
		entry.Message,
	)
	
	// 添加字段信息
	if entry.Fields != nil && len(entry.Fields) > 0 {
		fieldsStr := ""
		for k, v := range entry.Fields {
			if fieldsStr != "" {
				fieldsStr += " "
			}
			fieldsStr += fmt.Sprintf("%s=%v", k, v)
		}
		output += fmt.Sprintf(" [%s]", fieldsStr)
	}
	
	fmt.Println(output)
}

// NewContextLogger 创建带上下文的日志实例
func NewContextLogger(service string, fields ...interface{}) Logger {
	l := &logger{
		level:   parseLevel(viper.GetString("log.level")),
		format:  viper.GetString("log.format"),
		service: service,
		fields:  make(map[string]interface{}),
	}
	
	return l.With(fields...)
}
