package middleware

import (
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

// AuthConfig JWT 认证配置
type AuthConfig struct {
	Enabled          bool          `mapstructure:"enabled"`           // 是否启用 JWT 校验
	Secret           string        `mapstructure:"secret"`            // JWT 密钥
	ExpiresIn        time.Duration `mapstructure:"expires_in"`        // Token 过期时间
	RefreshExpiresIn time.Duration `mapstructure:"refresh_expires_in"` // 刷新 Token 过期时间
	SkipPaths        []string      `mapstructure:"skip_paths"`        // 跳过认证的路径
	DevMode          bool          `mapstructure:"dev_mode"`          // 开发模式
	DevToken         string        `mapstructure:"dev_token"`         // 开发模式令牌
	DevUser          DevUserConfig `mapstructure:"dev_user"`          // 开发模式用户信息
}

// DevUserConfig 开发模式用户配置
type DevUserConfig struct {
	ID       string   `mapstructure:"id"`       // 用户ID
	TenantID string   `mapstructure:"tenant_id"` // 租户ID
	Username string   `mapstructure:"username"` // 用户名
	Email    string   `mapstructure:"email"`    // 邮箱
	Roles    []string `mapstructure:"roles"`    // 角色列表
}

// Claims JWT 声明
type Claims struct {
	UserID      string   `json:"user_id"`
	TenantID    string   `json:"tenant_id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
}

// JWTService JWT 服务接口
type JWTService interface {
	ValidateToken(token string) (*Claims, error)
}

// AuthMiddleware JWT 认证中间件
type AuthMiddleware struct {
	config     *AuthConfig
	jwtService JWTService
	logger     logger.Logger
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(jwtService JWTService, logger logger.Logger) *AuthMiddleware {
	config := loadAuthConfig()

	// 🚨 显示开发模式警告
	if config.DevMode || !config.Enabled {
		showDevModeWarning := os.Getenv("SHOW_DEV_MODE_WARNING")
		if showDevModeWarning == "" || showDevModeWarning == "true" {
			logger.Warn("⚠️  开发模式已启用 - 用户认证配置",
				"auth_enabled", config.Enabled,
				"dev_mode", config.DevMode,
				"dev_user_id", config.DevUser.ID,
				"dev_user_roles", config.DevUser.Roles)

			if banner := os.Getenv("DEV_MODE_BANNER"); banner != "" {
				logger.Warn(banner)
			} else {
				logger.Warn("🔓 认证已禁用或使用开发模式，请确保仅在开发环境使用！")
			}
		}
	}

	return &AuthMiddleware{
		config:     config,
		jwtService: jwtService,
		logger:     logger,
	}
}

// loadAuthConfig 加载认证配置
func loadAuthConfig() *AuthConfig {
	config := &AuthConfig{
		Enabled:          viper.GetBool("security.jwt.enabled"),
		Secret:           viper.GetString("security.jwt.secret"),
		ExpiresIn:        viper.GetDuration("security.jwt.expires_in"),
		RefreshExpiresIn: viper.GetDuration("security.jwt.refresh_expires_in"),
		SkipPaths:        viper.GetStringSlice("security.jwt.skip_paths"),
		DevMode:          viper.GetBool("security.jwt.dev_mode"),
		DevToken:         viper.GetString("security.jwt.dev_token"),
	}

	// 🔧 优先从环境变量加载认证配置
	if envEnabled := os.Getenv("PAAS_AUTH_ENABLED"); envEnabled != "" {
		if enabled, err := strconv.ParseBool(envEnabled); err == nil {
			config.Enabled = enabled
		}
	}

	if envDevMode := os.Getenv("PAAS_DEV_MODE"); envDevMode != "" {
		if devMode, err := strconv.ParseBool(envDevMode); err == nil {
			config.DevMode = devMode
		}
	}

	if envDevToken := os.Getenv("PAAS_DEV_TOKEN"); envDevToken != "" {
		config.DevToken = envDevToken
	}

	if envJWTSecret := os.Getenv("PAAS_JWT_SECRET"); envJWTSecret != "" {
		config.Secret = envJWTSecret
	}

	// 加载开发模式用户配置 - 优先从环境变量
	config.DevUser = DevUserConfig{
		ID:       getStringFromEnvOrConfig("PAAS_DEV_USER_ID", "security.jwt.dev_user.id"),
		TenantID: getStringFromEnvOrConfig("PAAS_DEV_USER_TENANT_ID", "security.jwt.dev_user.tenant_id"),
		Username: getStringFromEnvOrConfig("PAAS_DEV_USER_USERNAME", "security.jwt.dev_user.username"),
		Email:    getStringFromEnvOrConfig("PAAS_DEV_USER_EMAIL", "security.jwt.dev_user.email"),
		Roles:    getStringSliceFromEnvOrConfig("PAAS_DEV_USER_ROLES", "security.jwt.dev_user.roles"),
	}

	// 设置默认值 - 智能环境检测
	if !viper.IsSet("security.jwt.enabled") && os.Getenv("PAAS_AUTH_ENABLED") == "" {
		// 🔍 多重环境检测机制
		env := strings.ToLower(os.Getenv("ENV"))
		nodeEnv := strings.ToLower(os.Getenv("NODE_ENV"))
		goEnv := strings.ToLower(os.Getenv("GO_ENV"))
		serverMode := strings.ToLower(viper.GetString("server.mode"))

		// 开发环境标识符
		devEnvs := []string{"development", "dev", "debug", "local"}

		isDevelopment := false
		for _, devEnv := range devEnvs {
			if env == devEnv || nodeEnv == devEnv || goEnv == devEnv || serverMode == devEnv {
				isDevelopment = true
				break
			}
		}

		if isDevelopment {
			config.Enabled = false // 🔓 开发环境默认禁用认证
			config.DevMode = true   // ✅ 开发环境默认启用开发模式
		} else {
			config.Enabled = true  // 🔒 生产环境默认启用认证
			config.DevMode = false // ❌ 生产环境禁用开发模式
		}
	}
	
	if config.ExpiresIn == 0 {
		config.ExpiresIn = 24 * time.Hour
	}
	
	if config.RefreshExpiresIn == 0 {
		config.RefreshExpiresIn = 7 * 24 * time.Hour
	}
	
	if config.DevToken == "" {
		config.DevToken = "dev-token"
	}
	
	// 设置默认的开发用户信息
	if config.DevUser.ID == "" {
		config.DevUser.ID = "dev-user-123"
	}
	if config.DevUser.TenantID == "" {
		config.DevUser.TenantID = "dev-tenant-123"
	}
	if config.DevUser.Username == "" {
		config.DevUser.Username = "开发用户"
	}
	if config.DevUser.Email == "" {
		config.DevUser.Email = "<EMAIL>"
	}
	if len(config.DevUser.Roles) == 0 {
		config.DevUser.Roles = []string{"admin"}
	}
	
	// 设置默认跳过路径
	if len(config.SkipPaths) == 0 {
		config.SkipPaths = []string{
			"/health",
			"/ready",
			"/metrics",
			"/api/v1/auth/login",
			"/api/v1/auth/register",
			"/api/v1/auth/refresh",
			"/swagger/",
			"/docs",
			"/api/docs",
		}
	}
	
	return config
}

// getStringFromEnvOrConfig 优先从环境变量获取字符串值，否则从配置文件获取
func getStringFromEnvOrConfig(envKey, configKey string) string {
	if envValue := os.Getenv(envKey); envValue != "" {
		return envValue
	}
	return viper.GetString(configKey)
}

// getStringSliceFromEnvOrConfig 优先从环境变量获取字符串切片，否则从配置文件获取
func getStringSliceFromEnvOrConfig(envKey, configKey string) []string {
	if envValue := os.Getenv(envKey); envValue != "" {
		// 环境变量中使用逗号分隔
		return strings.Split(envValue, ",")
	}
	return viper.GetStringSlice(configKey)
}

// Handler 返回 Gin 中间件处理函数
func (m *AuthMiddleware) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用 JWT 校验
		if !m.config.Enabled {
			m.handleDisabledAuth(c)
			return
		}
		
		// 检查是否跳过认证
		if m.shouldSkipAuth(c.Request.URL.Path) {
			c.Next()
			return
		}
		
		// 执行 JWT 认证
		m.handleJWTAuth(c)
	}
}

// handleDisabledAuth 处理禁用认证的情况
func (m *AuthMiddleware) handleDisabledAuth(c *gin.Context) {
	// JWT 校验被禁用时的处理逻辑
	m.logger.Debug("JWT 校验已禁用", "path", c.Request.URL.Path)
	
	// 检查是否为开发模式
	if m.config.DevMode {
		// 开发模式：设置默认用户信息
		m.setDevUserContext(c)
		m.logger.Debug("开发模式：设置默认用户上下文", 
			"user_id", m.config.DevUser.ID,
			"username", m.config.DevUser.Username)
	} else {
		// 非开发模式：设置匿名用户上下文
		m.setAnonymousUserContext(c)
		m.logger.Debug("设置匿名用户上下文")
	}
	
	c.Next()
}

// handleJWTAuth 处理 JWT 认证
func (m *AuthMiddleware) handleJWTAuth(c *gin.Context) {
	// 获取 Authorization 头
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		m.respondUnauthorized(c, "MISSING_TOKEN", "缺少认证令牌")
		return
	}
	
	// 检查 Bearer 前缀
	if !strings.HasPrefix(authHeader, "Bearer ") {
		m.respondUnauthorized(c, "INVALID_TOKEN_FORMAT", "认证令牌格式错误")
		return
	}
	
	// 提取 token
	token := strings.TrimPrefix(authHeader, "Bearer ")
	if token == "" {
		m.respondUnauthorized(c, "EMPTY_TOKEN", "认证令牌为空")
		return
	}
	
	// 开发模式特殊处理
	if m.config.DevMode && token == m.config.DevToken {
		m.setDevUserContext(c)
		m.logger.Debug("开发模式：使用开发令牌", "token", token)
		c.Next()
		return
	}
	
	// 验证 JWT token
	if m.jwtService == nil {
		m.logger.Error("JWT 服务未初始化")
		m.respondInternalError(c, "JWT 服务未初始化")
		return
	}
	
	claims, err := m.jwtService.ValidateToken(token)
	if err != nil {
		m.logger.Warn("JWT 令牌验证失败", "error", err.Error())
		m.respondUnauthorized(c, "INVALID_TOKEN", "认证令牌无效")
		return
	}
	
	// 设置用户上下文
	m.setUserContext(c, claims)
	m.logger.Debug("JWT 认证成功", 
		"user_id", claims.UserID,
		"username", claims.Username)
	
	c.Next()
}

// shouldSkipAuth 检查是否应该跳过认证
func (m *AuthMiddleware) shouldSkipAuth(path string) bool {
	for _, skipPath := range m.config.SkipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// setUserContext 设置用户上下文（来自 JWT）
func (m *AuthMiddleware) setUserContext(c *gin.Context, claims *Claims) {
	c.Set("user_id", claims.UserID)
	c.Set("tenant_id", claims.TenantID)
	c.Set("username", claims.Username)
	c.Set("email", claims.Email)
	c.Set("roles", claims.Roles)
	c.Set("permissions", claims.Permissions)
	c.Set("auth_method", "jwt")
	c.Set("authenticated", true)
}

// setDevUserContext 设置开发用户上下文
func (m *AuthMiddleware) setDevUserContext(c *gin.Context) {
	c.Set("user_id", m.config.DevUser.ID)
	c.Set("tenant_id", m.config.DevUser.TenantID)
	c.Set("username", m.config.DevUser.Username)
	c.Set("email", m.config.DevUser.Email)
	c.Set("roles", m.config.DevUser.Roles)
	c.Set("permissions", []string{"*"}) // 开发模式给予所有权限
	c.Set("auth_method", "dev")
	c.Set("authenticated", true)
}

// setAnonymousUserContext 设置匿名用户上下文
func (m *AuthMiddleware) setAnonymousUserContext(c *gin.Context) {
	c.Set("user_id", "anonymous")
	c.Set("tenant_id", "")
	c.Set("username", "匿名用户")
	c.Set("email", "")
	c.Set("roles", []string{"anonymous"})
	c.Set("permissions", []string{})
	c.Set("auth_method", "anonymous")
	c.Set("authenticated", false)
}

// respondUnauthorized 返回未授权响应
func (m *AuthMiddleware) respondUnauthorized(c *gin.Context, code, message string) {
	c.JSON(http.StatusUnauthorized, gin.H{
		"code":      code,
		"message":   message,
		"timestamp": time.Now().Format(time.RFC3339),
	})
	c.Abort()
}

// respondInternalError 返回内部错误响应
func (m *AuthMiddleware) respondInternalError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"code":      "INTERNAL_SERVER_ERROR",
		"message":   message,
		"timestamp": time.Now().Format(time.RFC3339),
	})
	c.Abort()
}

// IsEnabled 检查 JWT 校验是否启用
func (m *AuthMiddleware) IsEnabled() bool {
	return m.config.Enabled
}

// GetConfig 获取认证配置
func (m *AuthMiddleware) GetConfig() *AuthConfig {
	return m.config
}

// UpdateConfig 更新认证配置（运行时）
func (m *AuthMiddleware) UpdateConfig(config *AuthConfig) {
	m.config = config
	m.logger.Info("认证配置已更新", 
		"enabled", config.Enabled,
		"dev_mode", config.DevMode)
}

// GetUserFromContext 从上下文获取用户信息
func GetUserFromContext(c *gin.Context) *UserContext {
	return &UserContext{
		UserID:       getStringFromContext(c, "user_id"),
		TenantID:     getStringFromContext(c, "tenant_id"),
		Username:     getStringFromContext(c, "username"),
		Email:        getStringFromContext(c, "email"),
		Roles:        getStringSliceFromContext(c, "roles"),
		Permissions:  getStringSliceFromContext(c, "permissions"),
		AuthMethod:   getStringFromContext(c, "auth_method"),
		Authenticated: getBoolFromContext(c, "authenticated"),
	}
}

// UserContext 用户上下文
type UserContext struct {
	UserID        string   `json:"user_id"`
	TenantID      string   `json:"tenant_id"`
	Username      string   `json:"username"`
	Email         string   `json:"email"`
	Roles         []string `json:"roles"`
	Permissions   []string `json:"permissions"`
	AuthMethod    string   `json:"auth_method"`
	Authenticated bool     `json:"authenticated"`
}

// HasRole 检查用户是否具有指定角色
func (u *UserContext) HasRole(role string) bool {
	for _, r := range u.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasPermission 检查用户是否具有指定权限
func (u *UserContext) HasPermission(permission string) bool {
	for _, p := range u.Permissions {
		if p == "*" || p == permission {
			return true
		}
	}
	return false
}

// IsAdmin 检查用户是否为管理员
func (u *UserContext) IsAdmin() bool {
	return u.HasRole("admin") || u.HasRole("super_admin")
}

// 辅助函数
func getStringFromContext(c *gin.Context, key string) string {
	if value, exists := c.Get(key); exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

func getStringSliceFromContext(c *gin.Context, key string) []string {
	if value, exists := c.Get(key); exists {
		if slice, ok := value.([]string); ok {
			return slice
		}
	}
	return []string{}
}

func getBoolFromContext(c *gin.Context, key string) bool {
	if value, exists := c.Get(key); exists {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return false
}
