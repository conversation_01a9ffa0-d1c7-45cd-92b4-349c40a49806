package middleware

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
)

// ProxyConfig 代理配置
type ProxyConfig struct {
	TargetURL    string        // 目标服务URL
	PathPrefix   string        // 路径前缀
	Timeout      time.Duration // 请求超时时间
	RetryCount   int           // 重试次数
	StripPrefix  bool          // 是否去除路径前缀
}

// ProxyMiddleware 代理中间件
type ProxyMiddleware struct {
	config ProxyConfig
	client *http.Client
	logger logger.Logger
}

// NewProxyMiddleware 创建代理中间件
func NewProxyMiddleware(config ProxyConfig, logger logger.Logger) *ProxyMiddleware {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.RetryCount == 0 {
		config.RetryCount = 3
	}

	client := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &ProxyMiddleware{
		config: config,
		client: client,
		logger: logger,
	}
}

// Handle 处理代理请求
func (p *ProxyMiddleware) Handle() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 构建目标URL
		targetURL, err := p.buildTargetURL(c)
		if err != nil {
			p.logger.Error("构建目标URL失败", "error", err, "path", c.Request.URL.Path)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "代理配置错误",
				"message": "无法构建目标URL",
			})
			return
		}

		// 执行代理请求
		if err := p.proxyRequest(c, targetURL); err != nil {
			p.logger.Error("代理请求失败", "error", err, "target_url", targetURL)
			c.JSON(http.StatusBadGateway, gin.H{
				"error":   "服务不可用",
				"message": "无法连接到目标服务",
				"target":  p.config.TargetURL,
			})
			return
		}
	}
}

// buildTargetURL 构建目标URL
func (p *ProxyMiddleware) buildTargetURL(c *gin.Context) (string, error) {
	targetURL, err := url.Parse(p.config.TargetURL)
	if err != nil {
		return "", fmt.Errorf("解析目标URL失败: %w", err)
	}

	// 处理路径
	path := c.Request.URL.Path
	if p.config.StripPrefix && p.config.PathPrefix != "" {
		path = strings.TrimPrefix(path, p.config.PathPrefix)
	}

	// 构建完整URL
	targetURL.Path = path
	targetURL.RawQuery = c.Request.URL.RawQuery

	return targetURL.String(), nil
}

// proxyRequest 执行代理请求
func (p *ProxyMiddleware) proxyRequest(c *gin.Context, targetURL string) error {
	// 读取请求体
	var bodyBytes []byte
	if c.Request.Body != nil {
		bodyBytes, _ = io.ReadAll(c.Request.Body)
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	// 创建代理请求
	req, err := http.NewRequest(c.Request.Method, targetURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("创建代理请求失败: %w", err)
	}

	// 复制请求头
	p.copyHeaders(c.Request.Header, req.Header)

	// 设置代理相关头部
	req.Header.Set("X-Forwarded-For", c.ClientIP())
	req.Header.Set("X-Forwarded-Proto", c.Request.Header.Get("X-Forwarded-Proto"))
	req.Header.Set("X-Real-IP", c.ClientIP())

	// 执行请求（带重试）
	var resp *http.Response
	for i := 0; i <= p.config.RetryCount; i++ {
		resp, err = p.client.Do(req)
		if err == nil {
			break
		}
		
		if i < p.config.RetryCount {
			p.logger.Warn("代理请求失败，正在重试", 
				"attempt", i+1, 
				"max_retries", p.config.RetryCount,
				"error", err)
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	if err != nil {
		return fmt.Errorf("代理请求执行失败: %w", err)
	}
	defer resp.Body.Close()

	// 复制响应头
	p.copyHeaders(resp.Header, c.Writer.Header())

	// 设置状态码
	c.Status(resp.StatusCode)

	// 复制响应体
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		return fmt.Errorf("复制响应体失败: %w", err)
	}

	return nil
}

// copyHeaders 复制HTTP头部
func (p *ProxyMiddleware) copyHeaders(src, dst http.Header) {
	for key, values := range src {
		// 跳过某些头部
		if p.shouldSkipHeader(key) {
			continue
		}
		
		for _, value := range values {
			dst.Add(key, value)
		}
	}
}

// shouldSkipHeader 判断是否应该跳过某个头部
func (p *ProxyMiddleware) shouldSkipHeader(header string) bool {
	skipHeaders := []string{
		"Connection",
		"Keep-Alive",
		"Proxy-Authenticate",
		"Proxy-Authorization",
		"Te",
		"Trailers",
		"Transfer-Encoding",
		"Upgrade",
	}
	
	header = strings.ToLower(header)
	for _, skip := range skipHeaders {
		if strings.ToLower(skip) == header {
			return true
		}
	}
	
	return false
}

// ProxyToService 创建服务代理中间件的便捷函数
func ProxyToService(targetURL, pathPrefix string, logger logger.Logger) gin.HandlerFunc {
	config := ProxyConfig{
		TargetURL:   targetURL,
		PathPrefix:  pathPrefix,
		Timeout:     30 * time.Second,
		RetryCount:  3,
		StripPrefix: false,
	}
	
	proxy := NewProxyMiddleware(config, logger)
	return proxy.Handle()
}

// ProxyToServiceWithConfig 使用自定义配置创建服务代理中间件
func ProxyToServiceWithConfig(config ProxyConfig, logger logger.Logger) gin.HandlerFunc {
	proxy := NewProxyMiddleware(config, logger)
	return proxy.Handle()
}
