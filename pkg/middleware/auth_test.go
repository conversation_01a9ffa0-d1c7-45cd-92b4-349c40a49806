package middleware

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

// MockJWTService 模拟 JWT 服务
type MockJWTService struct {
	validateFunc func(token string) (*Claims, error)
}

func (m *MockJWTService) ValidateToken(token string) (*Claims, error) {
	if m.validateFunc != nil {
		return m.validateFunc(token)
	}
	return nil, nil
}

// MockLogger 模拟日志记录器
type MockLogger struct{}

func (l *MockLogger) Debug(msg string, fields ...interface{}) {}
func (l *MockLogger) Info(msg string, fields ...interface{})  {}
func (l *MockLogger) Warn(msg string, fields ...interface{})  {}
func (l *<PERSON>ckLogger) Error(msg string, fields ...interface{}) {}
func (l *<PERSON>ckLogger) Fatal(msg string, fields ...interface{}) {}
func (l *MockLogger) With(fields ...interface{}) logger.Logger { return l }

func TestAuthMiddleware_JWTDisabled(t *testing.T) {
	// 设置配置：禁用 JWT
	viper.Set("security.jwt.enabled", false)
	viper.Set("security.jwt.dev_mode", true)
	
	// 创建认证中间件
	mockJWT := &MockJWTService{}
	mockLogger := &MockLogger{}
	authMiddleware := NewAuthMiddleware(mockJWT, mockLogger)
	
	// 创建 Gin 路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(authMiddleware.Handler())
	router.GET("/test", func(c *gin.Context) {
		user := GetUserFromContext(c)
		c.JSON(http.StatusOK, gin.H{
			"user_id":       user.UserID,
			"username":      user.Username,
			"authenticated": user.Authenticated,
			"auth_method":   user.AuthMethod,
		})
	})
	
	// 测试请求（无 Authorization 头）
	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	
	// 验证响应
	if w.Code != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", w.Code)
	}
	
	// 验证响应内容包含开发用户信息
	body := w.Body.String()
	if !contains(body, "dev-user-123") {
		t.Errorf("响应应包含开发用户ID，实际: %s", body)
	}
}

func TestAuthMiddleware_JWTEnabled(t *testing.T) {
	// 设置配置：启用 JWT
	viper.Set("security.jwt.enabled", true)
	viper.Set("security.jwt.dev_mode", false)
	
	// 创建认证中间件
	mockJWT := &MockJWTService{
		validateFunc: func(token string) (*Claims, error) {
			if token == "valid-token" {
				return &Claims{
					UserID:   "user-123",
					Username: "测试用户",
					Email:    "<EMAIL>",
					Roles:    []string{"user"},
				}, nil
			}
			return nil, ErrInvalidToken
		},
	}
	mockLogger := &MockLogger{}
	authMiddleware := NewAuthMiddleware(mockJWT, mockLogger)
	
	// 创建 Gin 路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(authMiddleware.Handler())
	router.GET("/test", func(c *gin.Context) {
		user := GetUserFromContext(c)
		c.JSON(http.StatusOK, gin.H{
			"user_id":       user.UserID,
			"username":      user.Username,
			"authenticated": user.Authenticated,
		})
	})
	
	// 测试用例 1: 无 Authorization 头
	t.Run("无Authorization头", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		if w.Code != http.StatusUnauthorized {
			t.Errorf("期望状态码 401，实际 %d", w.Code)
		}
	})
	
	// 测试用例 2: 无效的 token 格式
	t.Run("无效token格式", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "InvalidFormat token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		if w.Code != http.StatusUnauthorized {
			t.Errorf("期望状态码 401，实际 %d", w.Code)
		}
	})
	
	// 测试用例 3: 有效的 token
	t.Run("有效token", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer valid-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		if w.Code != http.StatusOK {
			t.Errorf("期望状态码 200，实际 %d", w.Code)
		}
		
		body := w.Body.String()
		if !contains(body, "user-123") {
			t.Errorf("响应应包含用户ID，实际: %s", body)
		}
	})
	
	// 测试用例 4: 无效的 token
	t.Run("无效token", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		if w.Code != http.StatusUnauthorized {
			t.Errorf("期望状态码 401，实际 %d", w.Code)
		}
	})
}

func TestAuthMiddleware_DevMode(t *testing.T) {
	// 设置配置：启用 JWT 和开发模式
	viper.Set("security.jwt.enabled", true)
	viper.Set("security.jwt.dev_mode", true)
	viper.Set("security.jwt.dev_token", "dev-token")
	
	// 创建认证中间件
	mockJWT := &MockJWTService{}
	mockLogger := &MockLogger{}
	authMiddleware := NewAuthMiddleware(mockJWT, mockLogger)
	
	// 创建 Gin 路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(authMiddleware.Handler())
	router.GET("/test", func(c *gin.Context) {
		user := GetUserFromContext(c)
		c.JSON(http.StatusOK, gin.H{
			"user_id":     user.UserID,
			"auth_method": user.AuthMethod,
		})
	})
	
	// 测试开发模式令牌
	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Bearer dev-token")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	
	if w.Code != http.StatusOK {
		t.Errorf("期望状态码 200，实际 %d", w.Code)
	}
	
	body := w.Body.String()
	if !contains(body, "dev-user-123") || !contains(body, "dev") {
		t.Errorf("响应应包含开发用户信息，实际: %s", body)
	}
}

func TestAuthMiddleware_SkipPaths(t *testing.T) {
	// 设置配置：启用 JWT
	viper.Set("security.jwt.enabled", true)
	viper.Set("security.jwt.skip_paths", []string{"/health", "/api/v1/auth/login"})
	
	// 创建认证中间件
	mockJWT := &MockJWTService{}
	mockLogger := &MockLogger{}
	authMiddleware := NewAuthMiddleware(mockJWT, mockLogger)
	
	// 创建 Gin 路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(authMiddleware.Handler())
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	router.POST("/api/v1/auth/login", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "login"})
	})
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "protected"})
	})
	
	// 测试跳过认证的路径
	t.Run("跳过认证路径", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		if w.Code != http.StatusOK {
			t.Errorf("健康检查路径应该跳过认证，状态码: %d", w.Code)
		}
	})
	
	// 测试需要认证的路径
	t.Run("需要认证路径", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/protected", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		if w.Code != http.StatusUnauthorized {
			t.Errorf("受保护路径应该需要认证，状态码: %d", w.Code)
		}
	})
}

func TestUserContext(t *testing.T) {
	user := &UserContext{
		UserID:      "user-123",
		Username:    "测试用户",
		Roles:       []string{"user", "editor"},
		Permissions: []string{"read", "write"},
	}
	
	// 测试角色检查
	if !user.HasRole("user") {
		t.Error("用户应该具有 user 角色")
	}
	
	if user.HasRole("admin") {
		t.Error("用户不应该具有 admin 角色")
	}
	
	// 测试权限检查
	if !user.HasPermission("read") {
		t.Error("用户应该具有 read 权限")
	}
	
	if user.HasPermission("delete") {
		t.Error("用户不应该具有 delete 权限")
	}
	
	// 测试管理员检查
	if user.IsAdmin() {
		t.Error("用户不应该是管理员")
	}
	
	// 测试管理员用户
	adminUser := &UserContext{
		Roles: []string{"admin"},
	}
	
	if !adminUser.IsAdmin() {
		t.Error("管理员用户应该被识别为管理员")
	}
}

// 定义错误类型用于测试
var ErrInvalidToken = errors.New("invalid token")
