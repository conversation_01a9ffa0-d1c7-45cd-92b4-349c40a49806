package middleware

import (
	"net/http"
	"strings"
	"time"

	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Logger 日志中间件
func Logger(logger logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID
		requestID := uuid.New().String()
		c.Set("request_id", requestID)
		
		// 记录请求开始时间
		start := time.Now()
		
		// 处理请求
		c.Next()
		
		// 计算处理时间
		duration := time.Since(start)
		
		// 记录请求日志
		logger.Info("HTTP请求处理完成",
			"request_id", requestID,
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"status", c.Writer.Status(),
			"duration", duration.String(),
			"client_ip", c.ClientIP(),
			"user_agent", c.Request.UserAgent(),
		)
	}
}

// Recovery 恢复中间件
func Recovery(logger logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				requestID, _ := c.Get("request_id")
				
				logger.Error("服务器内部错误",
					"request_id", requestID,
					"error", err,
					"method", c.Request.Method,
					"path", c.Request.URL.Path,
				)
				
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":       "INTERNAL_SERVER_ERROR",
					"message":    "服务器内部错误",
					"request_id": requestID,
					"timestamp":  time.Now().Format(time.RFC3339),
				})
				c.Abort()
			}
		}()
		
		c.Next()
	}
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 设置 CORS 头
		c.Header("Access-Control-Allow-Origin", origin)
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")
		
		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}

// Auth 认证中间件
func Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取 Authorization 头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    "MISSING_TOKEN",
				"message": "缺少认证令牌",
			})
			c.Abort()
			return
		}
		
		// 检查 Bearer 前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    "INVALID_TOKEN_FORMAT",
				"message": "认证令牌格式错误",
			})
			c.Abort()
			return
		}
		
		// 提取 token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    "EMPTY_TOKEN",
				"message": "认证令牌为空",
			})
			c.Abort()
			return
		}
		
		// TODO: 验证 JWT token 并提取用户信息
		// 这里暂时使用模拟数据
		if token == "dev-token" {
			c.Set("user_id", "dev-user-123")
			c.Set("tenant_id", "dev-tenant-123")
			c.Set("username", "开发用户")
			c.Set("roles", []string{"admin"})
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    "INVALID_TOKEN",
				"message": "认证令牌无效",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RateLimit 限流中间件
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现限流逻辑
		// 可以使用 Redis 实现分布式限流
		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取或生成新的请求ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		
		c.Next()
	}
}

// Tenant 租户中间件
func Tenant() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头或路径参数获取租户ID
		tenantID := c.GetHeader("X-Tenant-ID")
		if tenantID == "" {
			tenantID = c.Param("tenant_id")
		}
		
		if tenantID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    "MISSING_TENANT_ID",
				"message": "缺少租户ID",
			})
			c.Abort()
			return
		}
		
		c.Set("tenant_id", tenantID)
		c.Next()
	}
}

// Metrics 指标中间件
func Metrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		c.Next()
		
		// 记录指标
		duration := time.Since(start)
		status := c.Writer.Status()
		method := c.Request.Method
		path := c.Request.URL.Path
		
		// TODO: 发送指标到监控系统
		_ = duration
		_ = status
		_ = method
		_ = path
	}
}
