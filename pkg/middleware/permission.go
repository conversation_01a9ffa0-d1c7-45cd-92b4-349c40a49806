package middleware

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// RequireAuth 要求用户已认证
func RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		user := GetUserFromContext(c)
		
		if !user.Authenticated {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":      "AUTHENTICATION_REQUIRED",
				"message":   "需要用户认证",
				"timestamp": time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RequireRole 要求用户具有指定角色
func RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user := GetUserFromContext(c)
		
		if !user.Authenticated {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"code":      "AUTHENTICATION_REQUIRED",
				"message":   "需要用户认证",
				"timestamp": time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		// 检查是否具有任一指定角色
		hasRole := false
		for _, role := range roles {
			if user.HasRole(role) {
				hasRole = true
				break
			}
		}
		
		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{
				"code":      "INSUFFICIENT_PERMISSIONS",
				"message":   "权限不足",
				"required":  roles,
				"user_roles": user.Roles,
				"timestamp": time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RequirePermission 要求用户具有指定权限
func RequirePermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user := GetUserFromContext(c)
		
		if !user.Authenticated {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":      "AUTHENTICATION_REQUIRED",
				"message":   "需要用户认证",
				"timestamp": time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		// 检查是否具有任一指定权限
		hasPermission := false
		for _, permission := range permissions {
			if user.HasPermission(permission) {
				hasPermission = true
				break
			}
		}
		
		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"code":         "INSUFFICIENT_PERMISSIONS",
				"message":      "权限不足",
				"required":     permissions,
				"user_permissions": user.Permissions,
				"timestamp":    time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RequireAdmin 要求管理员权限
func RequireAdmin() gin.HandlerFunc {
	return RequireRole("admin", "super_admin")
}

// RequireTenant 要求用户属于指定租户
func RequireTenant(tenantID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user := GetUserFromContext(c)
		
		if !user.Authenticated {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":      "AUTHENTICATION_REQUIRED",
				"message":   "需要用户认证",
				"timestamp": time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		if user.TenantID != tenantID {
			c.JSON(http.StatusForbidden, gin.H{
				"code":        "TENANT_ACCESS_DENIED",
				"message":     "租户访问被拒绝",
				"required":    tenantID,
				"user_tenant": user.TenantID,
				"timestamp":   time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RequireTenantFromParam 要求用户属于路径参数中指定的租户
func RequireTenantFromParam(paramName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		tenantID := c.Param(paramName)
		if tenantID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":      "MISSING_TENANT_PARAM",
				"message":   "缺少租户参数",
				"param":     paramName,
				"timestamp": time.Now().Format(time.RFC3339),
			})
			c.Abort()
			return
		}
		
		RequireTenant(tenantID)(c)
	}
}

// OptionalAuth 可选认证（不强制要求认证）
func OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这个中间件不做任何检查，只是确保用户上下文存在
		// 实际的认证由 AuthMiddleware 处理
		c.Next()
	}
}

// ConditionalAuth 条件认证（根据配置决定是否需要认证）
func ConditionalAuth(authMiddleware *AuthMiddleware) gin.HandlerFunc {
	return func(c *gin.Context) {
		if authMiddleware.IsEnabled() {
			// JWT 校验启用时，执行认证检查
			RequireAuth()(c)
		} else {
			// JWT 校验禁用时，直接通过
			c.Next()
		}
	}
}

// RateLimitByUser 按用户限流
func RateLimitByUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		user := GetUserFromContext(c)
		
		// TODO: 实现基于用户的限流逻辑
		// 可以使用 Redis 或内存存储来跟踪用户请求频率
		
		// 示例：管理员用户不受限流限制
		if user.IsAdmin() {
			c.Next()
			return
		}
		
		// 普通用户的限流逻辑
		// ...
		
		c.Next()
	}
}

// AuditLog 审计日志中间件
func AuditLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		user := GetUserFromContext(c)
		
		// 记录请求开始时间
		start := time.Now()
		
		// 处理请求
		c.Next()
		
		// 记录审计日志
		duration := time.Since(start)
		
		// TODO: 将审计日志发送到日志系统或数据库
		auditData := map[string]interface{}{
			"user_id":    user.UserID,
			"tenant_id":  user.TenantID,
			"username":   user.Username,
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"status":     c.Writer.Status(),
			"duration":   duration.Milliseconds(),
			"ip":         c.ClientIP(),
			"user_agent": c.Request.UserAgent(),
			"timestamp":  start.Format(time.RFC3339),
		}
		
		// 记录敏感操作
		if isSensitiveOperation(c.Request.Method, c.Request.URL.Path) {
			// TODO: 发送到安全审计系统
			_ = auditData
		}
	}
}

// isSensitiveOperation 判断是否为敏感操作
func isSensitiveOperation(method, path string) bool {
	// 定义敏感操作的模式
	sensitivePatterns := []string{
		"DELETE",
		"/api/v1/users",
		"/api/v1/auth",
		"/api/v1/admin",
	}
	
	for _, pattern := range sensitivePatterns {
		if method == pattern || contains(path, pattern) {
			return true
		}
	}
	
	return false
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      indexOf(s, substr) >= 0)))
}

// indexOf 查找子字符串位置
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}
