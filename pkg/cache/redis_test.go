package cache

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/spf13/viper"
)

func TestRedisClient_ConditionalEnable(t *testing.T) {
	// 测试用例 1: Redis 配置禁用
	t.Run("Redis配置禁用", func(t *testing.T) {
		// 设置配置
		viper.Set("redis.enabled", false)
		viper.Set("redis.addr", "localhost:6379")
		
		client := NewRedisClient()
		
		if client.IsEnabled() {
			t.Error("Redis 应该被禁用")
		}
	})
	
	// 测试用例 2: Redis 地址未配置
	t.Run("Redis地址未配置", func(t *testing.T) {
		// 清除配置
		viper.Set("redis.enabled", true)
		viper.Set("redis.addr", "")
		
		client := NewRedisClient()
		
		if client.IsEnabled() {
			t.Error("Redis 应该被禁用（地址未配置）")
		}
	})
	
	// 测试用例 3: Redis 连接失败
	t.Run("Redis连接失败", func(t *testing.T) {
		// 设置无效的 Redis 地址
		viper.Set("redis.enabled", true)
		viper.Set("redis.addr", "localhost:9999") // 无效端口
		viper.Set("redis.dial_timeout", "1s")
		
		client := NewRedisClient()
		
		if client.IsEnabled() {
			t.Error("Redis 应该被禁用（连接失败）")
		}
	})
	
	// 测试用例 4: Redis 连接成功（需要 Redis 服务器运行）
	t.Run("Redis连接成功", func(t *testing.T) {
		// 检查是否有 Redis 服务器运行
		if os.Getenv("REDIS_URL") == "" && !isRedisAvailable() {
			t.Skip("跳过测试：Redis 服务器不可用")
		}
		
		// 设置有效的 Redis 配置
		viper.Set("redis.enabled", true)
		viper.Set("redis.addr", "localhost:6379")
		viper.Set("redis.password", "")
		viper.Set("redis.db", 0)
		
		client := NewRedisClient()
		defer client.Close()
		
		if !client.IsEnabled() {
			t.Error("Redis 应该被启用（连接成功）")
		}
		
		// 测试基本操作
		ctx := context.Background()
		
		// 测试 Set 和 Get
		testKey := "test:key"
		testValue := map[string]string{"name": "test", "value": "123"}
		
		err := client.Set(ctx, testKey, testValue, time.Minute)
		if err != nil {
			t.Errorf("设置缓存失败: %v", err)
		}
		
		var result map[string]string
		err = client.Get(ctx, testKey, &result)
		if err != nil {
			t.Errorf("获取缓存失败: %v", err)
		}
		
		if result["name"] != "test" || result["value"] != "123" {
			t.Errorf("缓存值不匹配: %+v", result)
		}
		
		// 清理测试数据
		client.Delete(ctx, testKey)
	})
}

func TestRedisClient_GracefulDegradation(t *testing.T) {
	// 测试 Redis 禁用时的优雅降级
	viper.Set("redis.enabled", false)
	
	client := NewRedisClient()
	ctx := context.Background()
	
	// 所有操作都应该静默成功（不报错）
	err := client.Set(ctx, "test", "value", time.Minute)
	if err != nil {
		t.Errorf("禁用状态下 Set 操作应该静默成功: %v", err)
	}
	
	var result string
	err = client.Get(ctx, "test", &result)
	if err != ErrCacheMiss {
		t.Errorf("禁用状态下 Get 操作应该返回缓存未命中: %v", err)
	}
	
	err = client.Delete(ctx, "test")
	if err != nil {
		t.Errorf("禁用状态下 Delete 操作应该静默成功: %v", err)
	}
	
	count, err := client.Exists(ctx, "test")
	if err != nil || count != 0 {
		t.Errorf("禁用状态下 Exists 操作应该返回 0: count=%d, err=%v", count, err)
	}
}

func TestCacheWrapper(t *testing.T) {
	// 创建内存缓存作为降级方案
	memCache := NewMemoryCache()
	defer memCache.Close()
	
	// 创建禁用的 Redis 客户端
	viper.Set("redis.enabled", false)
	redisClient := NewRedisClient()
	
	// 创建缓存包装器
	wrapper := NewCacheWrapper(redisClient, memCache)
	
	ctx := context.Background()
	testKey := "test:wrapper"
	testValue := map[string]string{"type": "wrapper_test"}
	
	// 测试设置缓存（应该使用内存缓存）
	err := wrapper.Set(ctx, testKey, testValue, time.Minute)
	if err != nil {
		t.Errorf("缓存包装器设置失败: %v", err)
	}
	
	// 测试获取缓存（应该从内存缓存获取）
	var result map[string]string
	err = wrapper.Get(ctx, testKey, &result)
	if err != nil {
		t.Errorf("缓存包装器获取失败: %v", err)
	}
	
	if result["type"] != "wrapper_test" {
		t.Errorf("缓存值不匹配: %+v", result)
	}
	
	// 测试删除缓存
	err = wrapper.Delete(ctx, testKey)
	if err != nil {
		t.Errorf("缓存包装器删除失败: %v", err)
	}
	
	// 验证删除成功
	err = wrapper.Get(ctx, testKey, &result)
	if err != ErrCacheMiss {
		t.Errorf("删除后应该返回缓存未命中: %v", err)
	}
}

func TestCacheService(t *testing.T) {
	// 创建内存缓存
	memCache := NewMemoryCache()
	defer memCache.Close()
	
	// 创建缓存服务
	cacheService := NewCacheService(memCache, "test", time.Minute)
	
	ctx := context.Background()
	
	// 测试 GetOrSet 功能
	var result string
	callCount := 0
	
	err := cacheService.GetOrSet(ctx, "key1", &result, func() (interface{}, error) {
		callCount++
		return "value1", nil
	})
	
	if err != nil {
		t.Errorf("GetOrSet 失败: %v", err)
	}
	
	if callCount != 1 {
		t.Errorf("setter 函数应该被调用 1 次，实际调用 %d 次", callCount)
	}
	
	// 再次调用，应该从缓存获取
	err = cacheService.GetOrSet(ctx, "key1", &result, func() (interface{}, error) {
		callCount++
		return "value2", nil
	})
	
	if err != nil {
		t.Errorf("第二次 GetOrSet 失败: %v", err)
	}
	
	if callCount != 1 {
		t.Errorf("setter 函数应该只被调用 1 次，实际调用 %d 次", callCount)
	}
}

// isRedisAvailable 检查 Redis 是否可用
func isRedisAvailable() bool {
	viper.Set("redis.enabled", true)
	viper.Set("redis.addr", "localhost:6379")
	viper.Set("redis.dial_timeout", "1s")
	
	client := NewRedisClient()
	defer client.Close()
	
	return client.IsEnabled()
}

func TestMemoryCache(t *testing.T) {
	cache := NewMemoryCache()
	defer cache.Close()
	
	ctx := context.Background()
	
	// 测试基本操作
	testKey := "memory:test"
	testValue := map[string]interface{}{
		"name": "内存缓存测试",
		"id":   123,
	}
	
	// 设置缓存
	err := cache.Set(ctx, testKey, testValue, time.Second*2)
	if err != nil {
		t.Errorf("内存缓存设置失败: %v", err)
	}
	
	// 获取缓存
	var result map[string]interface{}
	err = cache.Get(ctx, testKey, &result)
	if err != nil {
		t.Errorf("内存缓存获取失败: %v", err)
	}
	
	if result["name"] != "内存缓存测试" {
		t.Errorf("缓存值不匹配: %+v", result)
	}
	
	// 测试过期
	time.Sleep(time.Second * 3)
	err = cache.Get(ctx, testKey, &result)
	if err != ErrCacheMiss {
		t.Errorf("过期缓存应该返回未命中: %v", err)
	}
	
	// 测试 Exists
	cache.Set(ctx, "exist:test", "value", time.Minute)
	count, err := cache.Exists(ctx, "exist:test", "not:exist")
	if err != nil {
		t.Errorf("Exists 操作失败: %v", err)
	}
	if count != 1 {
		t.Errorf("Exists 应该返回 1，实际返回 %d", count)
	}
}
