package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
)

// RedisConfig Redis 配置结构
type RedisConfig struct {
	Addr         string        `mapstructure:"addr"`          // Redis 地址
	Password     string        `mapstructure:"password"`      // Redis 密码
	DB           int           `mapstructure:"db"`            // Redis 数据库编号
	PoolSize     int           `mapstructure:"pool_size"`     // 连接池大小
	MinIdleConns int           `mapstructure:"min_idle_conns"` // 最小空闲连接数
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`  // 连接超时
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`  // 读取超时
	WriteTimeout time.Duration `mapstructure:"write_timeout"` // 写入超时
	Enabled      bool          `mapstructure:"enabled"`       // 是否启用 Redis 缓存
}

// RedisClient Redis 客户端包装器
type RedisClient struct {
	client  *redis.Client
	enabled bool
	config  *RedisConfig
}

// NewRedisClient 创建 Redis 客户端
// 支持条件启用：当配置不存在或连接失败时自动禁用
func NewRedisClient() *RedisClient {
	config := loadRedisConfig()
	
	redisClient := &RedisClient{
		config:  config,
		enabled: false, // 默认禁用，通过连接测试后启用
	}
	
	// 如果配置中明确禁用，直接返回
	if !config.Enabled {
		fmt.Printf("Redis 缓存已在配置中禁用\n")
		return redisClient
	}
	
	// 如果没有配置地址，禁用 Redis
	if config.Addr == "" {
		fmt.Printf("Redis 地址未配置，禁用 Redis 缓存\n")
		return redisClient
	}
	
	// 尝试连接 Redis
	if err := redisClient.connect(); err != nil {
		fmt.Printf("Redis 连接失败，禁用 Redis 缓存: %v\n", err)
		return redisClient
	}
	
	// 连接成功，启用 Redis
	redisClient.enabled = true
	fmt.Printf("Redis 缓存已启用，地址: %s\n", config.Addr)
	
	return redisClient
}

// loadRedisConfig 加载 Redis 配置
func loadRedisConfig() *RedisConfig {
	config := &RedisConfig{
		Addr:         viper.GetString("redis.addr"),
		Password:     viper.GetString("redis.password"),
		DB:           viper.GetInt("redis.db"),
		PoolSize:     viper.GetInt("redis.pool_size"),
		MinIdleConns: viper.GetInt("redis.min_idle_conns"),
		DialTimeout:  viper.GetDuration("redis.dial_timeout"),
		ReadTimeout:  viper.GetDuration("redis.read_timeout"),
		WriteTimeout: viper.GetDuration("redis.write_timeout"),
		Enabled:      viper.GetBool("redis.enabled"),
	}
	
	// 设置默认值
	if config.PoolSize == 0 {
		config.PoolSize = 10
	}
	if config.MinIdleConns == 0 {
		config.MinIdleConns = 5
	}
	if config.DialTimeout == 0 {
		config.DialTimeout = 5 * time.Second
	}
	if config.ReadTimeout == 0 {
		config.ReadTimeout = 3 * time.Second
	}
	if config.WriteTimeout == 0 {
		config.WriteTimeout = 3 * time.Second
	}
	
	// 如果没有明确设置 enabled，默认为 true（通过连接测试决定）
	if !viper.IsSet("redis.enabled") {
		config.Enabled = true
	}
	
	return config
}

// connect 连接 Redis
func (r *RedisClient) connect() error {
	// 创建 Redis 客户端
	r.client = redis.NewClient(&redis.Options{
		Addr:         r.config.Addr,
		Password:     r.config.Password,
		DB:           r.config.DB,
		PoolSize:     r.config.PoolSize,
		MinIdleConns: r.config.MinIdleConns,
		DialTimeout:  r.config.DialTimeout,
		ReadTimeout:  r.config.ReadTimeout,
		WriteTimeout: r.config.WriteTimeout,
	})
	
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := r.client.Ping(ctx).Err(); err != nil {
		r.client.Close()
		r.client = nil
		return fmt.Errorf("Redis ping 失败: %w", err)
	}
	
	return nil
}

// IsEnabled 检查 Redis 是否启用
func (r *RedisClient) IsEnabled() bool {
	return r.enabled && r.client != nil
}

// Set 设置缓存值
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if !r.IsEnabled() {
		// Redis 未启用时，静默忽略缓存操作
		return nil
	}
	
	// 序列化值
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("序列化缓存值失败: %w", err)
	}
	
	return r.client.Set(ctx, key, data, expiration).Err()
}

// Get 获取缓存值
func (r *RedisClient) Get(ctx context.Context, key string, dest interface{}) error {
	if !r.IsEnabled() {
		// Redis 未启用时，返回缓存未命中错误
		return ErrCacheMiss
	}
	
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheMiss
		}
		return fmt.Errorf("获取缓存值失败: %w", err)
	}
	
	// 反序列化值
	if err := json.Unmarshal([]byte(data), dest); err != nil {
		return fmt.Errorf("反序列化缓存值失败: %w", err)
	}
	
	return nil
}

// Delete 删除缓存值
func (r *RedisClient) Delete(ctx context.Context, keys ...string) error {
	if !r.IsEnabled() {
		// Redis 未启用时，静默忽略删除操作
		return nil
	}
	
	return r.client.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func (r *RedisClient) Exists(ctx context.Context, keys ...string) (int64, error) {
	if !r.IsEnabled() {
		// Redis 未启用时，返回 0（不存在）
		return 0, nil
	}
	
	return r.client.Exists(ctx, keys...).Result()
}

// Expire 设置键的过期时间
func (r *RedisClient) Expire(ctx context.Context, key string, expiration time.Duration) error {
	if !r.IsEnabled() {
		// Redis 未启用时，静默忽略过期设置
		return nil
	}
	
	return r.client.Expire(ctx, key, expiration).Err()
}

// Close 关闭 Redis 连接
func (r *RedisClient) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// HealthCheck 健康检查
func (r *RedisClient) HealthCheck(ctx context.Context) error {
	if !r.IsEnabled() {
		return fmt.Errorf("Redis 缓存未启用")
	}
	
	return r.client.Ping(ctx).Err()
}

// GetStats 获取 Redis 统计信息
func (r *RedisClient) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"enabled": r.enabled,
		"config":  r.config,
	}
	
	if r.IsEnabled() {
		poolStats := r.client.PoolStats()
		stats["pool_stats"] = map[string]interface{}{
			"hits":        poolStats.Hits,
			"misses":      poolStats.Misses,
			"timeouts":    poolStats.Timeouts,
			"total_conns": poolStats.TotalConns,
			"idle_conns":  poolStats.IdleConns,
			"stale_conns": poolStats.StaleConns,
		}
	}
	
	return stats
}

// 全局 Redis 客户端实例
var globalRedisClient *RedisClient

// InitGlobalRedisClient 初始化全局 Redis 客户端
func InitGlobalRedisClient() {
	globalRedisClient = NewRedisClient()
}

// GetGlobalRedisClient 获取全局 Redis 客户端
func GetGlobalRedisClient() *RedisClient {
	if globalRedisClient == nil {
		InitGlobalRedisClient()
	}
	return globalRedisClient
}

// CloseGlobalRedisClient 关闭全局 Redis 客户端
func CloseGlobalRedisClient() error {
	if globalRedisClient != nil {
		return globalRedisClient.Close()
	}
	return nil
}
