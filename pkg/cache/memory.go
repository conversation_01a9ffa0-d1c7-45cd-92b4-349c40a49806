package cache

import (
	"context"
	"encoding/json"
	"sync"
	"time"
)

// MemoryCache 内存缓存实现（作为 Redis 的降级方案）
type MemoryCache struct {
	data    map[string]*memoryCacheItem
	mutex   sync.RWMutex
	enabled bool
}

// memoryCacheItem 内存缓存项
type memoryCacheItem struct {
	Value     []byte
	ExpiresAt time.Time
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		data:    make(map[string]*memoryCacheItem),
		enabled: true,
	}
	
	// 启动清理协程
	go cache.cleanup()
	
	return cache
}

// cleanup 清理过期项
func (m *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		m.mutex.Lock()
		now := time.Now()
		for key, item := range m.data {
			if !item.ExpiresAt.IsZero() && now.After(item.ExpiresAt) {
				delete(m.data, key)
			}
		}
		m.mutex.Unlock()
	}
}

// Set 设置缓存值
func (m *MemoryCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if !m.enabled {
		return ErrCacheDisabled
	}
	
	// 序列化值
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	
	item := &memoryCacheItem{
		Value: data,
	}
	
	if expiration > 0 {
		item.ExpiresAt = time.Now().Add(expiration)
	}
	
	m.mutex.Lock()
	m.data[key] = item
	m.mutex.Unlock()
	
	return nil
}

// Get 获取缓存值
func (m *MemoryCache) Get(ctx context.Context, key string, dest interface{}) error {
	if !m.enabled {
		return ErrCacheDisabled
	}
	
	m.mutex.RLock()
	item, exists := m.data[key]
	m.mutex.RUnlock()
	
	if !exists {
		return ErrCacheMiss
	}
	
	// 检查是否过期
	if !item.ExpiresAt.IsZero() && time.Now().After(item.ExpiresAt) {
		m.mutex.Lock()
		delete(m.data, key)
		m.mutex.Unlock()
		return ErrCacheMiss
	}
	
	// 反序列化值
	return json.Unmarshal(item.Value, dest)
}

// Delete 删除缓存值
func (m *MemoryCache) Delete(ctx context.Context, keys ...string) error {
	if !m.enabled {
		return ErrCacheDisabled
	}
	
	m.mutex.Lock()
	for _, key := range keys {
		delete(m.data, key)
	}
	m.mutex.Unlock()
	
	return nil
}

// Exists 检查键是否存在
func (m *MemoryCache) Exists(ctx context.Context, keys ...string) (int64, error) {
	if !m.enabled {
		return 0, ErrCacheDisabled
	}
	
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	var count int64
	now := time.Now()
	
	for _, key := range keys {
		if item, exists := m.data[key]; exists {
			// 检查是否过期
			if item.ExpiresAt.IsZero() || now.Before(item.ExpiresAt) {
				count++
			}
		}
	}
	
	return count, nil
}

// Expire 设置键的过期时间
func (m *MemoryCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	if !m.enabled {
		return ErrCacheDisabled
	}
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if item, exists := m.data[key]; exists {
		if expiration > 0 {
			item.ExpiresAt = time.Now().Add(expiration)
		} else {
			item.ExpiresAt = time.Time{} // 永不过期
		}
	}
	
	return nil
}

// IsEnabled 检查缓存是否启用
func (m *MemoryCache) IsEnabled() bool {
	return m.enabled
}

// HealthCheck 健康检查
func (m *MemoryCache) HealthCheck(ctx context.Context) error {
	if !m.enabled {
		return ErrCacheDisabled
	}
	return nil
}

// Close 关闭缓存
func (m *MemoryCache) Close() error {
	m.mutex.Lock()
	m.enabled = false
	m.data = nil
	m.mutex.Unlock()
	return nil
}

// GetStats 获取内存缓存统计信息
func (m *MemoryCache) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	stats := map[string]interface{}{
		"enabled":    m.enabled,
		"total_keys": len(m.data),
		"type":       "memory",
	}
	
	// 计算过期键数量
	now := time.Now()
	expiredCount := 0
	for _, item := range m.data {
		if !item.ExpiresAt.IsZero() && now.After(item.ExpiresAt) {
			expiredCount++
		}
	}
	stats["expired_keys"] = expiredCount
	
	return stats
}

// Clear 清空所有缓存
func (m *MemoryCache) Clear() error {
	m.mutex.Lock()
	m.data = make(map[string]*memoryCacheItem)
	m.mutex.Unlock()
	return nil
}

// Size 获取缓存大小
func (m *MemoryCache) Size() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return len(m.data)
}
