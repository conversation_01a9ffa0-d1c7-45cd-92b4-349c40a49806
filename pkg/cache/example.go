package cache

import (
	"context"
	"fmt"
	"time"
)

// ExampleUsage 展示如何使用缓存服务
func ExampleUsage() {
	// 1. 初始化 Redis 客户端（支持条件启用）
	redisClient := NewRedisClient()
	
	// 2. 创建内存缓存作为降级方案
	memoryCache := NewMemoryCache()
	
	// 3. 创建缓存包装器，提供优雅降级
	cacheWrapper := NewCacheWrapper(redisClient, memoryCache)
	
	// 4. 创建缓存服务
	cacheService := NewCacheService(cacheWrapper, "paas", 10*time.Minute)
	
	// 5. 使用缓存服务
	ctx := context.Background()
	
	// 设置缓存
	user := map[string]interface{}{
		"id":   "123",
		"name": "张三",
		"role": "admin",
	}
	
	if err := cacheService.Set(ctx, "user:123", user); err != nil {
		fmt.Printf("设置缓存失败: %v\n", err)
	} else {
		fmt.Println("缓存设置成功")
	}
	
	// 获取缓存
	var cachedUser map[string]interface{}
	if err := cacheService.Get(ctx, "user:123", &cachedUser); err != nil {
		if err == ErrCacheMiss {
			fmt.Println("缓存未命中")
		} else {
			fmt.Printf("获取缓存失败: %v\n", err)
		}
	} else {
		fmt.Printf("缓存命中: %+v\n", cachedUser)
	}
	
	// 使用 GetOrSet 模式
	var userData map[string]interface{}
	err := cacheService.GetOrSet(ctx, "user:456", &userData, func() (interface{}, error) {
		// 模拟从数据库获取数据
		fmt.Println("从数据库获取用户数据...")
		return map[string]interface{}{
			"id":   "456",
			"name": "李四",
			"role": "user",
		}, nil
	})
	
	if err != nil {
		fmt.Printf("GetOrSet 失败: %v\n", err)
	} else {
		fmt.Printf("获取用户数据: %+v\n", userData)
	}
	
	// 检查缓存状态
	if cacheService.IsEnabled() {
		fmt.Println("缓存服务已启用")
	} else {
		fmt.Println("缓存服务已禁用")
	}
	
	// 健康检查
	if err := cacheService.HealthCheck(ctx); err != nil {
		fmt.Printf("缓存健康检查失败: %v\n", err)
	} else {
		fmt.Println("缓存健康检查通过")
	}
}

// ApplicationCacheService 应用缓存服务示例
type ApplicationCacheService struct {
	cache *CacheService
}

// NewApplicationCacheService 创建应用缓存服务
func NewApplicationCacheService() *ApplicationCacheService {
	// 初始化缓存（支持条件启用）
	redisClient := NewRedisClient()
	memoryCache := NewMemoryCache()
	cacheWrapper := NewCacheWrapper(redisClient, memoryCache)
	cacheService := NewCacheService(cacheWrapper, "app", 30*time.Minute)
	
	return &ApplicationCacheService{
		cache: cacheService,
	}
}

// Application 应用结构
type Application struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Status      string            `json:"status"`
	Config      map[string]string `json:"config"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// GetApplication 获取应用信息（带缓存）
func (s *ApplicationCacheService) GetApplication(ctx context.Context, appID string, dbGetter func(string) (*Application, error)) (*Application, error) {
	cacheKey := fmt.Sprintf("info:%s", appID)
	
	var app Application
	err := s.cache.GetOrSet(ctx, cacheKey, &app, func() (interface{}, error) {
		// 从数据库获取应用信息
		return dbGetter(appID)
	})
	
	if err != nil {
		return nil, fmt.Errorf("获取应用信息失败: %w", err)
	}
	
	return &app, nil
}

// CacheApplicationConfig 缓存应用配置
func (s *ApplicationCacheService) CacheApplicationConfig(ctx context.Context, appID string, config map[string]string) error {
	cacheKey := fmt.Sprintf("config:%s", appID)
	return s.cache.SetWithTTL(ctx, cacheKey, config, 1*time.Hour)
}

// GetApplicationConfig 获取应用配置（带缓存）
func (s *ApplicationCacheService) GetApplicationConfig(ctx context.Context, appID string) (map[string]string, error) {
	cacheKey := fmt.Sprintf("config:%s", appID)
	
	var config map[string]string
	err := s.cache.Get(ctx, cacheKey, &config)
	if err != nil {
		return nil, err
	}
	
	return config, nil
}

// InvalidateApplication 使应用缓存失效
func (s *ApplicationCacheService) InvalidateApplication(ctx context.Context, appID string) error {
	keys := []string{
		fmt.Sprintf("info:%s", appID),
		fmt.Sprintf("config:%s", appID),
		fmt.Sprintf("status:%s", appID),
	}
	
	return s.cache.Delete(ctx, keys...)
}

// GetCacheStats 获取缓存统计信息
func (s *ApplicationCacheService) GetCacheStats() map[string]interface{} {
	stats := map[string]interface{}{
		"enabled": s.cache.IsEnabled(),
		"prefix":  "app",
	}
	
	// 如果是 Redis 客户端，获取详细统计
	if redisClient, ok := s.cache.cache.(*RedisClient); ok {
		stats["redis"] = redisClient.GetStats()
	}
	
	// 如果是缓存包装器，获取详细统计
	if wrapper, ok := s.cache.cache.(*CacheWrapper); ok {
		stats["wrapper"] = map[string]interface{}{
			"enabled": wrapper.IsEnabled(),
		}
		
		if redisClient, ok := wrapper.primary.(*RedisClient); ok {
			stats["primary"] = redisClient.GetStats()
		}
		
		if memCache, ok := wrapper.fallback.(*MemoryCache); ok {
			stats["fallback"] = memCache.GetStats()
		}
	}
	
	return stats
}

// Close 关闭缓存服务
func (s *ApplicationCacheService) Close() error {
	if s.cache != nil && s.cache.cache != nil {
		return s.cache.cache.Close()
	}
	return nil
}
