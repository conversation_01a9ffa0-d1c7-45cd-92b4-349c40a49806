package cache

import (
	"context"
	"errors"
	"time"
)

// 缓存相关错误定义
var (
	ErrCacheMiss = errors.New("缓存未命中")
	ErrCacheDisabled = errors.New("缓存已禁用")
)

// Cache 缓存接口
type Cache interface {
	// Set 设置缓存值
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	
	// Get 获取缓存值
	Get(ctx context.Context, key string, dest interface{}) error
	
	// Delete 删除缓存值
	Delete(ctx context.Context, keys ...string) error
	
	// Exists 检查键是否存在
	Exists(ctx context.Context, keys ...string) (int64, error)
	
	// Expire 设置键的过期时间
	Expire(ctx context.Context, key string, expiration time.Duration) error
	
	// IsEnabled 检查缓存是否启用
	IsEnabled() bool
	
	// HealthCheck 健康检查
	HealthCheck(ctx context.Context) error
	
	// Close 关闭缓存连接
	Close() error
}

// CacheService 缓存服务
type CacheService struct {
	cache  Cache
	prefix string
	ttl    time.Duration
}

// NewCacheService 创建缓存服务
func NewCacheService(cache Cache, prefix string, ttl time.Duration) *CacheService {
	return &CacheService{
		cache:  cache,
		prefix: prefix,
		ttl:    ttl,
	}
}

// buildKey 构建缓存键
func (s *CacheService) buildKey(key string) string {
	if s.prefix == "" {
		return key
	}
	return s.prefix + ":" + key
}

// Set 设置缓存值
func (s *CacheService) Set(ctx context.Context, key string, value interface{}) error {
	return s.SetWithTTL(ctx, key, value, s.ttl)
}

// SetWithTTL 设置缓存值（指定TTL）
func (s *CacheService) SetWithTTL(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	fullKey := s.buildKey(key)
	return s.cache.Set(ctx, fullKey, value, ttl)
}

// Get 获取缓存值
func (s *CacheService) Get(ctx context.Context, key string, dest interface{}) error {
	fullKey := s.buildKey(key)
	return s.cache.Get(ctx, fullKey, dest)
}

// Delete 删除缓存值
func (s *CacheService) Delete(ctx context.Context, keys ...string) error {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = s.buildKey(key)
	}
	return s.cache.Delete(ctx, fullKeys...)
}

// Exists 检查键是否存在
func (s *CacheService) Exists(ctx context.Context, keys ...string) (int64, error) {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = s.buildKey(key)
	}
	return s.cache.Exists(ctx, fullKeys...)
}

// IsEnabled 检查缓存是否启用
func (s *CacheService) IsEnabled() bool {
	return s.cache.IsEnabled()
}

// HealthCheck 健康检查
func (s *CacheService) HealthCheck(ctx context.Context) error {
	return s.cache.HealthCheck(ctx)
}

// GetOrSet 获取缓存值，如果不存在则设置
func (s *CacheService) GetOrSet(ctx context.Context, key string, dest interface{}, setter func() (interface{}, error)) error {
	// 尝试从缓存获取
	err := s.Get(ctx, key, dest)
	if err == nil {
		return nil // 缓存命中
	}
	
	if err != ErrCacheMiss {
		// 缓存错误，但不影响业务逻辑
		// 继续执行 setter 函数
	}
	
	// 缓存未命中或出错，执行 setter 函数
	value, err := setter()
	if err != nil {
		return err
	}
	
	// 设置缓存（忽略缓存设置错误）
	s.Set(ctx, key, value)
	
	// 将值复制到目标变量
	if dest != nil {
		switch v := value.(type) {
		case []byte:
			if destBytes, ok := dest.(*[]byte); ok {
				*destBytes = v
			}
		case string:
			if destStr, ok := dest.(*string); ok {
				*destStr = v
			}
		default:
			// 对于复杂类型，需要进行类型转换
			// 这里简化处理，实际使用中可能需要更复杂的逻辑
		}
	}
	
	return nil
}

// CacheWrapper 缓存包装器，提供优雅降级功能
type CacheWrapper struct {
	primary   Cache
	fallback  Cache
	enabled   bool
}

// NewCacheWrapper 创建缓存包装器
func NewCacheWrapper(primary, fallback Cache) *CacheWrapper {
	return &CacheWrapper{
		primary:  primary,
		fallback: fallback,
		enabled:  primary != nil && primary.IsEnabled(),
	}
}

// Set 设置缓存值
func (w *CacheWrapper) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if w.enabled && w.primary != nil {
		if err := w.primary.Set(ctx, key, value, expiration); err == nil {
			return nil
		}
		// 主缓存失败，尝试降级
		w.enabled = false
	}
	
	if w.fallback != nil {
		return w.fallback.Set(ctx, key, value, expiration)
	}
	
	return nil // 静默忽略
}

// Get 获取缓存值
func (w *CacheWrapper) Get(ctx context.Context, key string, dest interface{}) error {
	if w.enabled && w.primary != nil {
		err := w.primary.Get(ctx, key, dest)
		if err == nil {
			return nil
		}
		if err != ErrCacheMiss {
			// 主缓存出错，尝试降级
			w.enabled = false
		} else {
			return err // 缓存未命中
		}
	}
	
	if w.fallback != nil {
		return w.fallback.Get(ctx, key, dest)
	}
	
	return ErrCacheMiss
}

// Delete 删除缓存值
func (w *CacheWrapper) Delete(ctx context.Context, keys ...string) error {
	var lastErr error
	
	if w.enabled && w.primary != nil {
		if err := w.primary.Delete(ctx, keys...); err != nil {
			lastErr = err
			w.enabled = false
		}
	}
	
	if w.fallback != nil {
		if err := w.fallback.Delete(ctx, keys...); err != nil {
			lastErr = err
		}
	}
	
	return lastErr
}

// Exists 检查键是否存在
func (w *CacheWrapper) Exists(ctx context.Context, keys ...string) (int64, error) {
	if w.enabled && w.primary != nil {
		count, err := w.primary.Exists(ctx, keys...)
		if err == nil {
			return count, nil
		}
		w.enabled = false
	}
	
	if w.fallback != nil {
		return w.fallback.Exists(ctx, keys...)
	}
	
	return 0, nil
}

// Expire 设置键的过期时间
func (w *CacheWrapper) Expire(ctx context.Context, key string, expiration time.Duration) error {
	if w.enabled && w.primary != nil {
		if err := w.primary.Expire(ctx, key, expiration); err == nil {
			return nil
		}
		w.enabled = false
	}
	
	if w.fallback != nil {
		return w.fallback.Expire(ctx, key, expiration)
	}
	
	return nil
}

// IsEnabled 检查缓存是否启用
func (w *CacheWrapper) IsEnabled() bool {
	return (w.enabled && w.primary != nil && w.primary.IsEnabled()) || 
		   (w.fallback != nil && w.fallback.IsEnabled())
}

// HealthCheck 健康检查
func (w *CacheWrapper) HealthCheck(ctx context.Context) error {
	if w.enabled && w.primary != nil {
		if err := w.primary.HealthCheck(ctx); err == nil {
			return nil
		}
		w.enabled = false
	}
	
	if w.fallback != nil {
		return w.fallback.HealthCheck(ctx)
	}
	
	return errors.New("所有缓存都不可用")
}

// Close 关闭缓存连接
func (w *CacheWrapper) Close() error {
	var lastErr error
	
	if w.primary != nil {
		if err := w.primary.Close(); err != nil {
			lastErr = err
		}
	}
	
	if w.fallback != nil {
		if err := w.fallback.Close(); err != nil {
			lastErr = err
		}
	}
	
	return lastErr
}
