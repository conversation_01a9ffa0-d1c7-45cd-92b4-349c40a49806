package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"paas-platform/internal/container"
	"paas-platform/internal/database"
	"paas-platform/internal/runtime"
	"paas-platform/internal/script"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"

	_ "paas-platform/docs" // 导入生成的 docs 包
)

// @title PaaS 平台脚本执行服务 API
// @version 1.0
// @description PaaS 平台的脚本执行服务，提供脚本执行、模板管理、任务调度等功能
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8084
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// 初始化配置
	initConfig()
	
	// 初始化日志
	logger := logger.NewLogger()
	
	// 初始化数据库
	db, err := database.NewConnection()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	
	// 自动迁移数据库表
	if err := database.AutoMigrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 初始化容器管理器
	containerManager, err := container.NewDockerManager(logger)
	if err != nil {
		log.Fatalf("初始化容器管理器失败: %v", err)
	}
	
	// 初始化运行时管理器
	runtimeManager := runtime.NewRuntimeManager(logger)
	
	// 注册默认运行时适配器
	if err := registerRuntimeAdapters(runtimeManager, logger); err != nil {
		log.Fatalf("注册运行时适配器失败: %v", err)
	}
	
	// 初始化脚本执行服务配置
	serviceConfig := script.ServiceConfig{
		DefaultTimeout:      time.Duration(viper.GetInt("script.default_timeout")) * time.Second,
		MaxConcurrentTasks:  viper.GetInt("script.max_concurrent_tasks"),
		TaskRetentionDays:   viper.GetInt("script.task_retention_days"),
		DefaultPythonImage:  viper.GetString("script.default_python_image"),
		WorkspaceBasePath:   viper.GetString("script.workspace_base_path"),
		ArtifactBasePath:    viper.GetString("script.artifact_base_path"),
		CallbackTimeout:     time.Duration(viper.GetInt("script.callback_timeout")) * time.Second,
	}
	
	// 初始化脚本执行服务
	scriptService := script.NewScriptExecutionService(db, containerManager, runtimeManager, logger, serviceConfig)
	scriptHandler := script.NewHandler(scriptService, logger)
	
	// 初始化 Gin 路由
	router := setupRouter(scriptHandler, logger)
	
	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler: router,
	}
	
	// 优雅启动
	go func() {
		logger.Info("脚本执行服务启动", "port", viper.GetInt("server.port"))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()
	
	// 启动后台任务清理协程
	go startBackgroundTasks(scriptService, logger)
	
	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("正在关闭脚本执行服务...")
	
	// 优雅关闭，超时时间为 30 秒
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("服务关闭失败: %v", err)
	}
	
	logger.Info("脚本执行服务已关闭")
}

// initConfig 初始化配置
func initConfig() {
	// 解析命令行参数
	var configFile string
	flag.StringVar(&configFile, "config", "", "配置文件路径")
	flag.Parse()

	// 设置默认值
	viper.SetDefault("server.port", 8084)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/script-service.db")
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")

	// 脚本执行服务配置
	viper.SetDefault("script.default_timeout", 300)
	viper.SetDefault("script.max_concurrent_tasks", 10)
	viper.SetDefault("script.task_retention_days", 30)
	viper.SetDefault("script.default_python_image", "python:3.11-alpine")
	viper.SetDefault("script.workspace_base_path", "./data/workspaces")
	viper.SetDefault("script.artifact_base_path", "./data/artifacts")
	viper.SetDefault("script.callback_timeout", 30)

	// 支持环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("SCRIPT_SERVICE")

	// 如果指定了配置文件，直接使用
	if configFile != "" {
		viper.SetConfigFile(configFile)
	} else {
		// 否则按默认方式查找配置文件
		viper.SetConfigName("script-service")
		viper.SetConfigType("yaml")
		viper.AddConfigPath("./configs")
		viper.AddConfigPath(".")
	}

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("配置文件未找到，使用默认配置")
		} else {
			log.Fatalf("读取配置文件失败: %v", err)
		}
	} else {
		// 输出使用的配置文件信息
		configPath := viper.ConfigFileUsed()
		if configPath != "" {
			configName := filepath.Base(configPath)
			if strings.Contains(configName, "dev") {
				log.Printf("✅ 使用开发环境配置文件: %s", configPath)
			} else {
				log.Printf("📄 使用配置文件: %s", configPath)
			}
		}
	}
}

// setupRouter 设置路由
func setupRouter(scriptHandler *script.Handler, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(viper.GetString("server.mode"))
	
	router := gin.New()
	
	// 添加中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	
	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "script-service",
			"version":   "1.0.0",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// 就绪检查接口
	router.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"ready":     true,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// Swagger 文档 (开发环境)
	if gin.Mode() == gin.DebugMode {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})
		router.GET("/api/docs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"title":       "PaaS 平台脚本执行服务 API",
				"version":     "1.0.0",
				"description": "PaaS 平台的脚本执行服务，提供脚本执行、模板管理、任务调度等功能",
				"swagger_url": "/swagger/index.html",
				"base_path":   "/api/v1",
				"host":        fmt.Sprintf("localhost:%d", viper.GetInt("server.port")),
			})
		})
	}

	// API 路由组
	v1 := router.Group("/api/v1")
	{
		// 添加认证中间件
		v1.Use(middleware.Auth())

		// 注册脚本执行路由
		scriptHandler.RegisterRoutes(v1)
	}
	
	return router
}

// registerRuntimeAdapters 注册运行时适配器
func registerRuntimeAdapters(runtimeManager runtime.RuntimeManager, logger logger.Logger) error {
	// 注册 Python 运行时适配器
	pythonAdapter := runtime.NewPythonAdapter(logger)
	if err := runtimeManager.RegisterRuntimeAdapter(runtime.RuntimeTypePython, pythonAdapter); err != nil {
		return fmt.Errorf("注册Python适配器失败: %w", err)
	}
	
	// 注册 Node.js 运行时适配器
	nodeAdapter := runtime.NewNodeJSAdapter(logger)
	if err := runtimeManager.RegisterRuntimeAdapter(runtime.RuntimeTypeNodeJS, nodeAdapter); err != nil {
		return fmt.Errorf("注册Node.js适配器失败: %w", err)
	}
	
	logger.Info("运行时适配器注册完成")
	return nil
}

// startBackgroundTasks 启动后台任务
func startBackgroundTasks(scriptService script.ScriptExecutionService, logger logger.Logger) {
	// 定期清理过期任务
	ticker := time.NewTicker(24 * time.Hour) // 每天清理一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			ctx := context.Background()
			retentionDays := viper.GetInt("script.task_retention_days")
			
			logger.Info("开始清理过期任务", "retention_days", retentionDays)
			if err := scriptService.CleanupExpiredTasks(ctx, retentionDays); err != nil {
				logger.Error("清理过期任务失败", "error", err)
			} else {
				logger.Info("过期任务清理完成")
			}
		}
	}
}
