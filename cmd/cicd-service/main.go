package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"paas-platform/internal/cicd"
	"paas-platform/internal/database"
	"paas-platform/pkg/logger"
	"paas-platform/pkg/middleware"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"
	"gorm.io/gorm"

	_ "paas-platform/docs" // 导入生成的 docs 包
)

// @title PaaS 平台 CI/CD 服务 API
// @version 1.0
// @description PaaS 平台的持续集成和持续部署服务，提供完整的 CI/CD 流水线管理功能
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8082
// @BasePath /api/v1

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization

// initConfig 初始化配置
func initConfig() {
	// 解析命令行参数
	var configFile string
	flag.StringVar(&configFile, "config", "", "配置文件路径")
	flag.Parse()

	// 设置默认值
	viper.SetDefault("server.port", 8082)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("database.driver", "sqlite")
	viper.SetDefault("database.dsn", "./data/cicd.db")

	// 如果指定了配置文件，直接使用
	if configFile != "" {
		viper.SetConfigFile(configFile)
	} else {
		// 否则按默认方式查找配置文件
		viper.SetConfigName("cicd-service")
		viper.SetConfigType("yaml")
		viper.AddConfigPath("./configs")
		viper.AddConfigPath(".")
	}

	if err := viper.ReadInConfig(); err != nil {
		log.Printf("配置文件读取失败，使用默认配置: %v", err)
	} else {
		// 输出使用的配置文件信息
		configPath := viper.ConfigFileUsed()
		if configPath != "" {
			configName := filepath.Base(configPath)
			if strings.Contains(configName, "dev") {
				log.Printf("✅ 使用开发环境配置文件: %s", configPath)
			} else {
				log.Printf("📄 使用配置文件: %s", configPath)
			}
		}
	}
}

func main() {
	// 初始化配置
	initConfig()
	
	// 初始化日志
	logger := logger.NewLogger()
	
	// 初始化数据库
	db, err := database.NewConnection()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	
	// 自动迁移数据库表
	if err := migrateCICDTables(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 初始化服务
	cicdService := cicd.NewCICDService(db, logger)
	cicdHandler := cicd.NewHandler(cicdService, logger)
	
	// 初始化 Gin 路由
	router := setupRouter(cicdHandler, logger)
	
	// 启动 HTTP 服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", viper.GetInt("server.port")),
		Handler: router,
	}
	
	// 优雅启动
	go func() {
		logger.Info("CI/CD 服务启动", "port", viper.GetInt("server.port"))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务启动失败: %v", err)
		}
	}()
	
	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("正在关闭 CI/CD 服务...")
	
	// 设置 5 秒的超时时间来关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("服务关闭失败:", err)
	}
	
	// 等待超时或服务器关闭完成
	select {
	case <-ctx.Done():
		logger.Info("服务关闭超时")
	default:
		logger.Info("CI/CD 服务已关闭")
	}
}

// migrateCICDTables 迁移 CI/CD 相关数据库表
func migrateCICDTables(db *gorm.DB) error {
	// 迁移 CI/CD 相关表
	err := db.AutoMigrate(
		&cicd.Pipeline{},
		&cicd.Build{},
		&cicd.BuildStage{},
		&cicd.BuildStep{},
		&cicd.Webhook{},
		&cicd.BuildNode{},
		&cicd.BuildQueue{},
		&cicd.Deployment{},
		&cicd.Environment{},
	)
	if err != nil {
		return fmt.Errorf("CI/CD 表迁移失败: %w", err)
	}
	
	return nil
}

// setupRouter 设置路由
func setupRouter(cicdHandler *cicd.Handler, logger logger.Logger) *gin.Engine {
	// 设置 Gin 模式
	gin.SetMode(viper.GetString("server.mode"))
	
	router := gin.New()
	
	// 添加中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	
	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "cicd-service",
			"version":   "1.0.0",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// 就绪检查接口
	router.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"ready":     true,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// Swagger 文档 (开发环境)
	if gin.Mode() == gin.DebugMode {
		// 集成 Swagger UI
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

		// 添加 API 文档重定向
		router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})

		// 添加 API 文档信息接口
		router.GET("/api/docs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"title":       "PaaS 平台 CI/CD 服务 API",
				"version":     "1.0.0",
				"description": "PaaS 平台的持续集成和持续部署服务，提供完整的 CI/CD 流水线管理功能",
				"swagger_url": "/swagger/index.html",
				"base_path":   "/api/v1",
				"host":        fmt.Sprintf("localhost:%d", viper.GetInt("server.port")),
			})
		})
	}
	
	// API 路由组
	v1 := router.Group("/api/v1")
	{
		// 添加认证中间件 (开发环境跳过)
		// TODO: 实现 JWT 认证中间件
		// 当前在开发模式下跳过认证
		if gin.Mode() != gin.DebugMode {
			// v1.Use(middleware.NewAuthMiddleware(jwtAdapter, logger).Handler())
		}
		
		// 注册 CI/CD 路由
		cicdHandler.RegisterRoutes(v1)
	}
	
	return router
}
