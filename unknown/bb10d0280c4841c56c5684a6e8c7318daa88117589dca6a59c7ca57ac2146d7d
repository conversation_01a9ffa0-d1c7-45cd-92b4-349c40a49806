package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// IDPService IDP 服务接口
type IDPService interface {
	// IDP 提供商管理
	CreateIDPProvider(ctx context.Context, req *CreateIDPProviderRequest) (*IDPProvider, error)
	GetIDPProvider(ctx context.Context, id string) (*IDPProvider, error)
	GetIDPProviderByName(ctx context.Context, name string, tenantID string) (*IDPProvider, error)
	ListIDPProviders(ctx context.Context, tenantID string) ([]*IDPProvider, error)
	UpdateIDPProvider(ctx context.Context, id string, req *UpdateIDPProviderRequest) (*IDPProvider, error)
	DeleteIDPProvider(ctx context.Context, id string) error
	
	// IDP 认证流程
	InitiateIDPAuth(ctx context.Context, providerID string, redirectURL string) (*IDPAuthInitResponse, error)
	HandleIDPCallback(ctx context.Context, providerID string, code string, state string) (*IDPAuthResult, error)
	ValidateIDPToken(ctx context.Context, providerID string, token string) (*IDPUserInfo, error)
	
	// 账号关联管理
	LinkIDPAccount(ctx context.Context, userID string, providerID string, idpUserID string) (*IDPAccount, error)
	UnlinkIDPAccount(ctx context.Context, userID string, accountID string) error
	GetUserIDPAccounts(ctx context.Context, userID string) ([]*IDPAccount, error)
	FindUserByIDPAccount(ctx context.Context, providerID string, idpUserID string) (*User, error)
	
	// 用户信息同步
	SyncUserFromIDP(ctx context.Context, accountID string) error
	SyncAllUsersFromIDP(ctx context.Context, providerID string) error
	
	// IDP 会话管理
	CreateIDPSession(ctx context.Context, req *CreateIDPSessionRequest) (*IDPSession, error)
	GetIDPSession(ctx context.Context, sessionID string) (*IDPSession, error)
	RefreshIDPSession(ctx context.Context, sessionID string) (*IDPSession, error)
	RevokeIDPSession(ctx context.Context, sessionID string) error
	
	// 审计日志
	LogIDPEvent(ctx context.Context, event *IDPAuditLog) error
}

// idpService IDP 服务实现
type idpService struct {
	db     *gorm.DB
	logger Logger
}

// NewIDPService 创建 IDP 服务
func NewIDPService(db *gorm.DB, logger Logger) IDPService {
	return &idpService{
		db:     db,
		logger: logger,
	}
}

// CreateIDPProvider 创建 IDP 提供商
func (s *idpService) CreateIDPProvider(ctx context.Context, req *CreateIDPProviderRequest) (*IDPProvider, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查名称是否已存在
	var existing IDPProvider
	if err := s.db.Where("name = ? AND tenant_id = ?", req.Name, req.TenantID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("IDP 提供商名称已存在")
	}
	
	provider := &IDPProvider{
		ID:             generateID(),
		Name:           req.Name,
		Type:           req.Type,
		Enabled:        req.Enabled,
		Config:         req.Config,
		Mapping:        req.Mapping,
		TenantID:       req.TenantID,
		Priority:       req.Priority,
		AutoCreateUser: req.AutoCreateUser,
		AutoLinkUser:   req.AutoLinkUser,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	
	if err := s.db.Create(provider).Error; err != nil {
		return nil, fmt.Errorf("创建 IDP 提供商失败: %w", err)
	}
	
	s.logger.Info("IDP 提供商创建成功", "provider_id", provider.ID, "name", provider.Name)
	return provider, nil
}

// GetIDPProvider 获取 IDP 提供商
func (s *idpService) GetIDPProvider(ctx context.Context, id string) (*IDPProvider, error) {
	var provider IDPProvider
	if err := s.db.First(&provider, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	return &provider, nil
}

// GetIDPProviderByName 根据名称获取 IDP 提供商
func (s *idpService) GetIDPProviderByName(ctx context.Context, name string, tenantID string) (*IDPProvider, error) {
	var provider IDPProvider
	if err := s.db.Where("name = ? AND tenant_id = ?", name, tenantID).First(&provider).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	return &provider, nil
}

// ListIDPProviders 获取 IDP 提供商列表
func (s *idpService) ListIDPProviders(ctx context.Context, tenantID string) ([]*IDPProvider, error) {
	var providers []*IDPProvider
	query := s.db.Where("tenant_id = ?", tenantID).Order("priority ASC, created_at ASC")
	
	if err := query.Find(&providers).Error; err != nil {
		return nil, fmt.Errorf("获取 IDP 提供商列表失败: %w", err)
	}
	
	return providers, nil
}

// UpdateIDPProvider 更新 IDP 提供商
func (s *idpService) UpdateIDPProvider(ctx context.Context, id string, req *UpdateIDPProviderRequest) (*IDPProvider, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	var provider IDPProvider
	if err := s.db.First(&provider, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	// 更新字段
	if req.Name != "" {
		provider.Name = req.Name
	}
	if req.Enabled != nil {
		provider.Enabled = *req.Enabled
	}
	if req.Config != nil {
		provider.Config = *req.Config
	}
	if req.Mapping != nil {
		provider.Mapping = *req.Mapping
	}
	if req.Priority != nil {
		provider.Priority = *req.Priority
	}
	if req.AutoCreateUser != nil {
		provider.AutoCreateUser = *req.AutoCreateUser
	}
	if req.AutoLinkUser != nil {
		provider.AutoLinkUser = *req.AutoLinkUser
	}
	
	provider.UpdatedAt = time.Now()
	
	if err := s.db.Save(&provider).Error; err != nil {
		return nil, fmt.Errorf("更新 IDP 提供商失败: %w", err)
	}
	
	s.logger.Info("IDP 提供商更新成功", "provider_id", provider.ID, "name", provider.Name)
	return &provider, nil
}

// DeleteIDPProvider 删除 IDP 提供商
func (s *idpService) DeleteIDPProvider(ctx context.Context, id string) error {
	// 检查是否有关联的账号
	var count int64
	if err := s.db.Model(&IDPAccount{}).Where("idp_provider_id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("检查关联账号失败: %w", err)
	}
	
	if count > 0 {
		return fmt.Errorf("无法删除 IDP 提供商，存在 %d 个关联账号", count)
	}
	
	if err := s.db.Delete(&IDPProvider{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("删除 IDP 提供商失败: %w", err)
	}
	
	s.logger.Info("IDP 提供商删除成功", "provider_id", id)
	return nil
}

// LinkIDPAccount 关联 IDP 账号
func (s *idpService) LinkIDPAccount(ctx context.Context, userID string, providerID string, idpUserID string) (*IDPAccount, error) {
	// 检查用户是否存在
	var user User
	if err := s.db.First(&user, "id = ?", userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	
	// 检查 IDP 提供商是否存在
	var provider IDPProvider
	if err := s.db.First(&provider, "id = ?", providerID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("IDP 提供商不存在")
		}
		return nil, fmt.Errorf("获取 IDP 提供商失败: %w", err)
	}
	
	// 检查是否已经关联
	var existing IDPAccount
	if err := s.db.Where("user_id = ? AND idp_provider_id = ?", userID, providerID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("用户已关联此 IDP 提供商")
	}
	
	// 检查 IDP 用户 ID 是否已被其他用户关联
	if err := s.db.Where("idp_provider_id = ? AND idp_user_id = ?", providerID, idpUserID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("此 IDP 账号已被其他用户关联")
	}
	
	account := &IDPAccount{
		ID:            generateID(),
		UserID:        userID,
		IDPProviderID: providerID,
		IDPUserID:     idpUserID,
		Status:        IDPAccountStatusActive,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	
	if err := s.db.Create(account).Error; err != nil {
		return nil, fmt.Errorf("关联 IDP 账号失败: %w", err)
	}
	
	// 如果这是用户的第一个 IDP 账号，设置为主要账号
	if user.PrimaryIDPID == "" {
		user.PrimaryIDPID = account.ID
		user.IsIDPUser = true
		s.db.Save(&user)
	}
	
	s.logger.Info("IDP 账号关联成功", "user_id", userID, "provider_id", providerID, "idp_user_id", idpUserID)
	return account, nil
}

// LogIDPEvent 记录 IDP 事件
func (s *idpService) LogIDPEvent(ctx context.Context, event *IDPAuditLog) error {
	event.ID = generateID()
	event.Timestamp = time.Now()
	
	if err := s.db.Create(event).Error; err != nil {
		s.logger.Error("记录 IDP 审计日志失败", "error", err)
		return fmt.Errorf("记录 IDP 审计日志失败: %w", err)
	}
	
	return nil
}

// generateID 生成唯一 ID
func generateID() string {
	// 这里应该使用 UUID 生成器，简化示例
	return fmt.Sprintf("id_%d", time.Now().UnixNano())
}
