package config

import (
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"github.com/xeipuuv/gojsonschema"
)

// Validator 配置验证器接口
type Validator interface {
	// Validate 验证配置
	Validate(config *Config) error
	
	// ValidateValue 验证配置值
	ValidateValue(configType ConfigType, value interface{}) error
	
	// ValidateSchema 验证配置模式
	ValidateSchema(schema interface{}) error
}

// ConfigValidator 配置验证器实现
type ConfigValidator struct {
	logger Logger
	rules  map[ConfigType]ValidationRule
}

// ValidationRule 验证规则
type ValidationRule struct {
	Required    bool                   `json:"required"`
	MinLength   int                    `json:"min_length,omitempty"`
	MaxLength   int                    `json:"max_length,omitempty"`
	Pattern     string                 `json:"pattern,omitempty"`
	Enum        []interface{}          `json:"enum,omitempty"`
	Min         *float64               `json:"min,omitempty"`
	Max         *float64               `json:"max,omitempty"`
	Format      string                 `json:"format,omitempty"`
	CustomRules []string               `json:"custom_rules,omitempty"`
	Schema      map[string]interface{} `json:"schema,omitempty"`
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator(logger Logger) *ConfigValidator {
	validator := &ConfigValidator{
		logger: logger,
		rules:  make(map[ConfigType]ValidationRule),
	}
	
	// 初始化默认验证规则
	validator.initDefaultRules()
	
	return validator
}

// initDefaultRules 初始化默认验证规则
func (v *ConfigValidator) initDefaultRules() {
	// 字符串类型规则
	v.rules[ConfigTypeString] = ValidationRule{
		Required:  true,
		MinLength: 0,
		MaxLength: 10000,
	}
	
	// 数字类型规则
	v.rules[ConfigTypeNumber] = ValidationRule{
		Required: true,
	}
	
	// 布尔类型规则
	v.rules[ConfigTypeBool] = ValidationRule{
		Required: true,
	}
	
	// JSON 类型规则
	v.rules[ConfigTypeJSON] = ValidationRule{
		Required: true,
		Format:   "json",
	}
	
	// YAML 类型规则
	v.rules[ConfigTypeYAML] = ValidationRule{
		Required: true,
		Format:   "yaml",
	}
	
	// 密钥类型规则
	v.rules[ConfigTypeSecret] = ValidationRule{
		Required:  true,
		MinLength: 1,
		MaxLength: 1000,
	}
	
	// 文件类型规则
	v.rules[ConfigTypeFile] = ValidationRule{
		Required:  true,
		MinLength: 1,
		MaxLength: 1000000, // 1MB
	}
}

// Validate 验证配置
func (v *ConfigValidator) Validate(config *Config) error {
	errors := &ValidationErrors{}
	
	// 验证基本字段
	if config.Key == "" {
		errors.Add("key", "", "配置键不能为空", "required")
	} else if !v.isValidKey(config.Key) {
		errors.Add("key", config.Key, "配置键格式无效", "invalid_format")
	}
	
	if config.Type == "" {
		errors.Add("type", "", "配置类型不能为空", "required")
	}
	
	if config.Scope == "" {
		errors.Add("scope", "", "配置作用域不能为空", "required")
	}
	
	if config.Environment == "" {
		errors.Add("environment", "", "配置环境不能为空", "required")
	}
	
	// 验证配置值
	if err := v.ValidateValue(config.Type, config.Value); err != nil {
		if validationErr, ok := err.(*ValidationErrors); ok {
			errors.Errors = append(errors.Errors, validationErr.Errors...)
		} else {
			errors.Add("value", fmt.Sprintf("%v", config.Value), err.Error(), "invalid_value")
		}
	}
	
	// 验证 JSON Schema
	if config.Schema != nil {
		if err := v.ValidateSchema(config.Schema); err != nil {
			errors.Add("schema", fmt.Sprintf("%v", config.Schema), err.Error(), "invalid_schema")
		}
		
		// 使用 Schema 验证值
		if err := v.validateValueWithSchema(config.Value, config.Schema); err != nil {
			errors.Add("value", fmt.Sprintf("%v", config.Value), err.Error(), "schema_validation_failed")
		}
	}
	
	// 验证标签
	if err := v.validateTags(config.Tags); err != nil {
		errors.Add("tags", fmt.Sprintf("%v", config.Tags), err.Error(), "invalid_tags")
	}
	
	if errors.HasErrors() {
		return errors
	}
	
	return nil
}

// ValidateValue 验证配置值
func (v *ConfigValidator) ValidateValue(configType ConfigType, value interface{}) error {
	if value == nil {
		return fmt.Errorf("配置值不能为空")
	}
	
	rule, exists := v.rules[configType]
	if !exists {
		return fmt.Errorf("不支持的配置类型: %s", configType)
	}
	
	switch configType {
	case ConfigTypeString:
		return v.validateString(value, rule)
	case ConfigTypeNumber:
		return v.validateNumber(value, rule)
	case ConfigTypeBool:
		return v.validateBool(value, rule)
	case ConfigTypeJSON:
		return v.validateJSON(value, rule)
	case ConfigTypeYAML:
		return v.validateYAML(value, rule)
	case ConfigTypeSecret:
		return v.validateSecret(value, rule)
	case ConfigTypeFile:
		return v.validateFile(value, rule)
	default:
		return fmt.Errorf("未知的配置类型: %s", configType)
	}
}

// ValidateSchema 验证配置模式
func (v *ConfigValidator) ValidateSchema(schema interface{}) error {
	// 将 schema 转换为 JSON 字符串
	schemaBytes, err := json.Marshal(schema)
	if err != nil {
		return fmt.Errorf("序列化 schema 失败: %w", err)
	}
	
	// 验证是否为有效的 JSON Schema
	schemaLoader := gojsonschema.NewBytesLoader(schemaBytes)
	_, err = gojsonschema.NewSchema(schemaLoader)
	if err != nil {
		return fmt.Errorf("无效的 JSON Schema: %w", err)
	}
	
	return nil
}

// validateString 验证字符串
func (v *ConfigValidator) validateString(value interface{}, rule ValidationRule) error {
	str, ok := value.(string)
	if !ok {
		return fmt.Errorf("值必须是字符串类型")
	}
	
	if rule.MinLength > 0 && len(str) < rule.MinLength {
		return fmt.Errorf("字符串长度不能少于 %d", rule.MinLength)
	}
	
	if rule.MaxLength > 0 && len(str) > rule.MaxLength {
		return fmt.Errorf("字符串长度不能超过 %d", rule.MaxLength)
	}
	
	if rule.Pattern != "" {
		matched, err := regexp.MatchString(rule.Pattern, str)
		if err != nil {
			return fmt.Errorf("正则表达式错误: %w", err)
		}
		if !matched {
			return fmt.Errorf("字符串不匹配模式: %s", rule.Pattern)
		}
	}
	
	if len(rule.Enum) > 0 {
		found := false
		for _, enum := range rule.Enum {
			if str == enum {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("值不在允许的枚举中: %v", rule.Enum)
		}
	}
	
	// 格式验证
	if rule.Format != "" {
		if err := v.validateFormat(str, rule.Format); err != nil {
			return err
		}
	}
	
	return nil
}

// validateNumber 验证数字
func (v *ConfigValidator) validateNumber(value interface{}, rule ValidationRule) error {
	var num float64
	
	switch v := value.(type) {
	case int:
		num = float64(v)
	case int64:
		num = float64(v)
	case float32:
		num = float64(v)
	case float64:
		num = v
	case string:
		var err error
		num, err = strconv.ParseFloat(v, 64)
		if err != nil {
			return fmt.Errorf("无法解析为数字: %s", v)
		}
	default:
		return fmt.Errorf("值必须是数字类型")
	}
	
	if rule.Min != nil && num < *rule.Min {
		return fmt.Errorf("数字不能小于 %f", *rule.Min)
	}
	
	if rule.Max != nil && num > *rule.Max {
		return fmt.Errorf("数字不能大于 %f", *rule.Max)
	}
	
	return nil
}

// validateBool 验证布尔值
func (v *ConfigValidator) validateBool(value interface{}, rule ValidationRule) error {
	switch value.(type) {
	case bool:
		return nil
	case string:
		str := value.(string)
		if str == "true" || str == "false" {
			return nil
		}
		return fmt.Errorf("字符串布尔值必须是 'true' 或 'false'")
	default:
		return fmt.Errorf("值必须是布尔类型")
	}
}

// validateJSON 验证 JSON
func (v *ConfigValidator) validateJSON(value interface{}, rule ValidationRule) error {
	var jsonStr string
	
	switch v := value.(type) {
	case string:
		jsonStr = v
	default:
		// 尝试序列化为 JSON
		bytes, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("无法序列化为 JSON: %w", err)
		}
		jsonStr = string(bytes)
	}
	
	// 验证是否为有效的 JSON
	var temp interface{}
	if err := json.Unmarshal([]byte(jsonStr), &temp); err != nil {
		return fmt.Errorf("无效的 JSON 格式: %w", err)
	}
	
	return nil
}

// validateYAML 验证 YAML
func (v *ConfigValidator) validateYAML(value interface{}, rule ValidationRule) error {
	str, ok := value.(string)
	if !ok {
		return fmt.Errorf("YAML 值必须是字符串类型")
	}
	
	// TODO: 添加 YAML 验证逻辑
	// 这里可以使用 gopkg.in/yaml.v3 进行验证
	if strings.TrimSpace(str) == "" {
		return fmt.Errorf("YAML 内容不能为空")
	}
	
	return nil
}

// validateSecret 验证密钥
func (v *ConfigValidator) validateSecret(value interface{}, rule ValidationRule) error {
	str, ok := value.(string)
	if !ok {
		return fmt.Errorf("密钥值必须是字符串类型")
	}
	
	if len(str) < rule.MinLength {
		return fmt.Errorf("密钥长度不能少于 %d", rule.MinLength)
	}
	
	if len(str) > rule.MaxLength {
		return fmt.Errorf("密钥长度不能超过 %d", rule.MaxLength)
	}
	
	return nil
}

// validateFile 验证文件
func (v *ConfigValidator) validateFile(value interface{}, rule ValidationRule) error {
	str, ok := value.(string)
	if !ok {
		return fmt.Errorf("文件内容必须是字符串类型")
	}
	
	if len(str) > rule.MaxLength {
		return fmt.Errorf("文件大小不能超过 %d 字节", rule.MaxLength)
	}
	
	return nil
}

// validateFormat 验证格式
func (v *ConfigValidator) validateFormat(value, format string) error {
	switch format {
	case "email":
		return v.validateEmail(value)
	case "url":
		return v.validateURL(value)
	case "ipv4":
		return v.validateIPv4(value)
	case "ipv6":
		return v.validateIPv6(value)
	case "hostname":
		return v.validateHostname(value)
	case "json":
		var temp interface{}
		return json.Unmarshal([]byte(value), &temp)
	default:
		return nil // 未知格式，跳过验证
	}
}

// validateEmail 验证邮箱
func (v *ConfigValidator) validateEmail(email string) error {
	pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	matched, err := regexp.MatchString(pattern, email)
	if err != nil {
		return err
	}
	if !matched {
		return fmt.Errorf("无效的邮箱格式")
	}
	return nil
}

// validateURL 验证 URL
func (v *ConfigValidator) validateURL(urlStr string) error {
	_, err := url.Parse(urlStr)
	if err != nil {
		return fmt.Errorf("无效的 URL 格式: %w", err)
	}
	return nil
}

// validateIPv4 验证 IPv4
func (v *ConfigValidator) validateIPv4(ip string) error {
	pattern := `^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`
	matched, err := regexp.MatchString(pattern, ip)
	if err != nil {
		return err
	}
	if !matched {
		return fmt.Errorf("无效的 IPv4 格式")
	}
	return nil
}

// validateIPv6 验证 IPv6
func (v *ConfigValidator) validateIPv6(ip string) error {
	// 简化的 IPv6 验证
	if strings.Contains(ip, ":") {
		return nil
	}
	return fmt.Errorf("无效的 IPv6 格式")
}

// validateHostname 验证主机名
func (v *ConfigValidator) validateHostname(hostname string) error {
	pattern := `^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`
	matched, err := regexp.MatchString(pattern, hostname)
	if err != nil {
		return err
	}
	if !matched {
		return fmt.Errorf("无效的主机名格式")
	}
	return nil
}

// validateValueWithSchema 使用 Schema 验证值
func (v *ConfigValidator) validateValueWithSchema(value, schema interface{}) error {
	// 将值转换为 JSON
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("序列化值失败: %w", err)
	}
	
	// 将 schema 转换为 JSON
	schemaBytes, err := json.Marshal(schema)
	if err != nil {
		return fmt.Errorf("序列化 schema 失败: %w", err)
	}
	
	// 创建验证器
	schemaLoader := gojsonschema.NewBytesLoader(schemaBytes)
	documentLoader := gojsonschema.NewBytesLoader(valueBytes)
	
	result, err := gojsonschema.Validate(schemaLoader, documentLoader)
	if err != nil {
		return fmt.Errorf("Schema 验证失败: %w", err)
	}
	
	if !result.Valid() {
		var errors []string
		for _, desc := range result.Errors() {
			errors = append(errors, desc.String())
		}
		return fmt.Errorf("Schema 验证失败: %s", strings.Join(errors, "; "))
	}
	
	return nil
}

// validateTags 验证标签
func (v *ConfigValidator) validateTags(tags []string) error {
	if len(tags) > 10 {
		return fmt.Errorf("标签数量不能超过 10 个")
	}
	
	for _, tag := range tags {
		if len(tag) == 0 {
			return fmt.Errorf("标签不能为空")
		}
		if len(tag) > 50 {
			return fmt.Errorf("标签长度不能超过 50 个字符")
		}
		if !v.isValidTag(tag) {
			return fmt.Errorf("标签格式无效: %s", tag)
		}
	}
	
	return nil
}

// isValidKey 验证配置键格式
func (v *ConfigValidator) isValidKey(key string) bool {
	// 配置键只能包含字母、数字、点、下划线和连字符
	pattern := `^[a-zA-Z0-9._-]+$`
	matched, _ := regexp.MatchString(pattern, key)
	return matched && len(key) <= 200
}

// isValidTag 验证标签格式
func (v *ConfigValidator) isValidTag(tag string) bool {
	// 标签只能包含字母、数字、下划线和连字符
	pattern := `^[a-zA-Z0-9_-]+$`
	matched, _ := regexp.MatchString(pattern, tag)
	return matched
}

// SetRule 设置验证规则
func (v *ConfigValidator) SetRule(configType ConfigType, rule ValidationRule) {
	v.rules[configType] = rule
}

// GetRule 获取验证规则
func (v *ConfigValidator) GetRule(configType ConfigType) (ValidationRule, bool) {
	rule, exists := v.rules[configType]
	return rule, exists
}
