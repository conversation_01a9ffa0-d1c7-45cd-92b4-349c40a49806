package config

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// CacheManager 缓存管理器
type CacheManager struct {
	redis  *redis.Client
	logger Logger
	ttl    time.Duration
	prefix string
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(redis *redis.Client, logger Logger, ttl time.Duration) *CacheManager {
	return &CacheManager{
		redis:  redis,
		logger: logger,
		ttl:    ttl,
		prefix: "config:",
	}
}

// GetConfig 从缓存获取配置
func (c *CacheManager) GetConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*Config, error) {
	cacheKey := c.buildConfigKey(key, scope, scopeID, env)
	
	data, err := c.redis.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("从缓存获取配置失败: %w", err)
	}
	
	var config Config
	if err := json.Unmarshal([]byte(data), &config); err != nil {
		c.logger.Error("反序列化缓存配置失败", "error", err, "cache_key", cacheKey)
		// 删除损坏的缓存
		c.redis.Del(ctx, cacheKey)
		return nil, nil
	}
	
	c.logger.Debug("从缓存获取配置成功", "cache_key", cacheKey)
	return &config, nil
}

// SetConfig 设置配置到缓存
func (c *CacheManager) SetConfig(ctx context.Context, config *Config) error {
	cacheKey := c.buildConfigKey(config.Key, config.Scope, config.ScopeID, config.Environment)
	
	data, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}
	
	err = c.redis.Set(ctx, cacheKey, data, c.ttl).Err()
	if err != nil {
		return fmt.Errorf("设置配置到缓存失败: %w", err)
	}
	
	c.logger.Debug("设置配置到缓存成功", "cache_key", cacheKey)
	return nil
}

// DeleteConfig 从缓存删除配置
func (c *CacheManager) DeleteConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) error {
	cacheKey := c.buildConfigKey(key, scope, scopeID, env)
	
	err := c.redis.Del(ctx, cacheKey).Err()
	if err != nil {
		return fmt.Errorf("从缓存删除配置失败: %w", err)
	}
	
	c.logger.Debug("从缓存删除配置成功", "cache_key", cacheKey)
	return nil
}

// InvalidatePattern 按模式失效缓存
func (c *CacheManager) InvalidatePattern(ctx context.Context, pattern string) error {
	fullPattern := c.prefix + pattern
	
	keys, err := c.redis.Keys(ctx, fullPattern).Result()
	if err != nil {
		return fmt.Errorf("获取缓存键失败: %w", err)
	}
	
	if len(keys) > 0 {
		err = c.redis.Del(ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("批量删除缓存失败: %w", err)
		}
		
		c.logger.Info("批量失效缓存", "pattern", fullPattern, "count", len(keys))
	}
	
	return nil
}

// InvalidateScope 失效作用域缓存
func (c *CacheManager) InvalidateScope(ctx context.Context, scope ConfigScope, scopeID string) error {
	pattern := fmt.Sprintf("*:%s:%s:*", scope, scopeID)
	return c.InvalidatePattern(ctx, pattern)
}

// InvalidateEnvironment 失效环境缓存
func (c *CacheManager) InvalidateEnvironment(ctx context.Context, env string) error {
	pattern := fmt.Sprintf("*:%s", env)
	return c.InvalidatePattern(ctx, pattern)
}

// GetStats 获取缓存统计
func (c *CacheManager) GetStats(ctx context.Context) (*CacheStats, error) {
	info, err := c.redis.Info(ctx, "memory").Result()
	if err != nil {
		return nil, fmt.Errorf("获取 Redis 内存信息失败: %w", err)
	}
	
	dbSize, err := c.redis.DBSize(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("获取 Redis 数据库大小失败: %w", err)
	}
	
	// 获取配置相关的键数量
	configKeys, err := c.redis.Keys(ctx, c.prefix+"*").Result()
	if err != nil {
		return nil, fmt.Errorf("获取配置缓存键失败: %w", err)
	}
	
	return &CacheStats{
		TotalKeys:   dbSize,
		ConfigKeys:  int64(len(configKeys)),
		MemoryUsage: info, // 简化处理，实际应解析内存使用信息
	}, nil
}

// Warm 预热缓存
func (c *CacheManager) Warm(ctx context.Context, configs []*Config) error {
	if len(configs) == 0 {
		return nil
	}
	
	pipe := c.redis.Pipeline()
	
	for _, config := range configs {
		cacheKey := c.buildConfigKey(config.Key, config.Scope, config.ScopeID, config.Environment)
		
		data, err := json.Marshal(config)
		if err != nil {
			c.logger.Error("序列化配置失败", "error", err, "config_id", config.ID)
			continue
		}
		
		pipe.Set(ctx, cacheKey, data, c.ttl)
	}
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("批量预热缓存失败: %w", err)
	}
	
	c.logger.Info("缓存预热完成", "count", len(configs))
	return nil
}

// buildConfigKey 构建配置缓存键
func (c *CacheManager) buildConfigKey(key string, scope ConfigScope, scopeID string, env string) string {
	return fmt.Sprintf("%s%s:%s:%s:%s", c.prefix, key, scope, scopeID, env)
}

// CacheStats 缓存统计
type CacheStats struct {
	TotalKeys   int64  `json:"total_keys"`
	ConfigKeys  int64  `json:"config_keys"`
	MemoryUsage string `json:"memory_usage"`
}

// ConfigCacheService 配置缓存服务
type ConfigCacheService struct {
	cache   *CacheManager
	service Service
	logger  Logger
}

// NewConfigCacheService 创建配置缓存服务
func NewConfigCacheService(cache *CacheManager, service Service, logger Logger) *ConfigCacheService {
	return &ConfigCacheService{
		cache:   cache,
		service: service,
		logger:  logger,
	}
}

// GetConfig 获取配置（带缓存）
func (s *ConfigCacheService) GetConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*Config, error) {
	// 先从缓存获取
	config, err := s.cache.GetConfig(ctx, key, scope, scopeID, env)
	if err != nil {
		s.logger.Error("从缓存获取配置失败", "error", err)
		// 缓存失败不影响业务，继续从数据库获取
	}
	
	if config != nil {
		s.logger.Debug("缓存命中", "key", key, "scope", scope)
		return config, nil
	}
	
	// 缓存未命中，从数据库获取
	config, err = s.service.GetConfig(ctx, key, scope, scopeID, env)
	if err != nil {
		return nil, err
	}
	
	// 异步设置缓存
	go func() {
		if err := s.cache.SetConfig(context.Background(), config); err != nil {
			s.logger.Error("设置配置缓存失败", "error", err, "key", key)
		}
	}()
	
	return config, nil
}

// UpdateConfig 更新配置（清除缓存）
func (s *ConfigCacheService) UpdateConfig(ctx context.Context, configID string, req *UpdateConfigRequest) (*Config, error) {
	// 先获取旧配置信息用于清除缓存
	oldConfig, err := s.service.GetConfigByID(ctx, configID)
	if err != nil {
		return nil, err
	}
	
	// 更新配置
	config, err := s.service.UpdateConfig(ctx, configID, req)
	if err != nil {
		return nil, err
	}
	
	// 清除相关缓存
	go func() {
		// 清除旧配置缓存
		if err := s.cache.DeleteConfig(context.Background(), oldConfig.Key, oldConfig.Scope, oldConfig.ScopeID, oldConfig.Environment); err != nil {
			s.logger.Error("清除旧配置缓存失败", "error", err)
		}
		
		// 如果键或环境发生变化，也要清除新的缓存位置
		if config.Key != oldConfig.Key || config.Environment != oldConfig.Environment {
			if err := s.cache.DeleteConfig(context.Background(), config.Key, config.Scope, config.ScopeID, config.Environment); err != nil {
				s.logger.Error("清除新配置缓存失败", "error", err)
			}
		}
	}()
	
	return config, nil
}

// DeleteConfig 删除配置（清除缓存）
func (s *ConfigCacheService) DeleteConfig(ctx context.Context, configID string) error {
	// 先获取配置信息用于清除缓存
	config, err := s.service.GetConfigByID(ctx, configID)
	if err != nil {
		return err
	}
	
	// 删除配置
	err = s.service.DeleteConfig(ctx, configID)
	if err != nil {
		return err
	}
	
	// 清除缓存
	go func() {
		if err := s.cache.DeleteConfig(context.Background(), config.Key, config.Scope, config.ScopeID, config.Environment); err != nil {
			s.logger.Error("清除配置缓存失败", "error", err)
		}
	}()
	
	return nil
}

// BatchGetConfigs 批量获取配置（带缓存）
func (s *ConfigCacheService) BatchGetConfigs(ctx context.Context, keys []string, scope ConfigScope, scopeID string, env string) (map[string]*Config, error) {
	result := make(map[string]*Config)
	missedKeys := make([]string, 0)
	
	// 先从缓存批量获取
	for _, key := range keys {
		config, err := s.cache.GetConfig(ctx, key, scope, scopeID, env)
		if err != nil {
			s.logger.Error("从缓存获取配置失败", "error", err, "key", key)
			missedKeys = append(missedKeys, key)
			continue
		}
		
		if config != nil {
			result[key] = config
		} else {
			missedKeys = append(missedKeys, key)
		}
	}
	
	// 从数据库获取缓存未命中的配置
	if len(missedKeys) > 0 {
		dbConfigs, err := s.service.BatchGetConfigs(ctx, missedKeys, scope, scopeID, env)
		if err != nil {
			return nil, err
		}
		
		// 合并结果并异步设置缓存
		for key, config := range dbConfigs {
			result[key] = config
			
			// 异步设置缓存
			go func(c *Config) {
				if err := s.cache.SetConfig(context.Background(), c); err != nil {
					s.logger.Error("设置配置缓存失败", "error", err, "key", c.Key)
				}
			}(config)
		}
	}
	
	return result, nil
}

// InvalidateAll 清除所有配置缓存
func (s *ConfigCacheService) InvalidateAll(ctx context.Context) error {
	return s.cache.InvalidatePattern(ctx, "*")
}
