import { request } from '@/utils/request'
import type { ApiResponse, User } from '@/types'

// 登录请求参数
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  remember?: boolean
}

// 登录响应
export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

// 注册请求参数
export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  displayName: string
  captcha?: string
}

// 修改密码请求参数
export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 重置密码请求参数
export interface ResetPasswordRequest {
  email: string
  captcha?: string
}

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return request.post('/v1/auth/login', data)
  },

  /**
   * 用户登出
   */
  logout(): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/logout')
  },

  /**
   * 刷新token
   */
  refreshToken(): Promise<ApiResponse<{ access_token: string }>> {
    return request.post('/v1/auth/refresh')
  },

  /**
   * 用户注册
   */
  register(data: RegisterRequest): Promise<ApiResponse<User>> {
    return request.post('/v1/auth/register', data)
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): Promise<ApiResponse<User>> {
    return request.get('/v1/auth/me')
  },

  /**
   * 修改密码
   */
  changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/change-password', data)
  },

  /**
   * 重置密码
   */
  resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/reset-password', data)
  },

  /**
   * 确认重置密码
   */
  confirmResetPassword(data: { token: string; password: string }): Promise<ApiResponse<void>> {
    return request.post('/v1/auth/confirm-reset', data)
  },

  /**
   * 获取用户权限列表
   */
  getUserPermissions(userId: string): Promise<ApiResponse<any[]>> {
    return request.get(`/v1/users/${userId}/permissions`)
  },

  /**
   * 获取验证码
   */
  getCaptcha(): Promise<ApiResponse<{ image: string; key: string }>> {
    return request.get('/v1/auth/captcha')
  }
}
