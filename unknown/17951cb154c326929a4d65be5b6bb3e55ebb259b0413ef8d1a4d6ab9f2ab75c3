# PaaS 平台 Kubernetes 命名空间配置

apiVersion: v1
kind: Namespace
metadata:
  name: paas-platform
  labels:
    name: paas-platform
    app.kubernetes.io/name: paas-platform
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: platform
    app.kubernetes.io/part-of: paas-platform
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "PaaS 平台核心服务命名空间"
    contact: "<EMAIL>"
    version: "1.0.0"

---
# 资源配额
apiVersion: v1
kind: ResourceQuota
metadata:
  name: paas-platform-quota
  namespace: paas-platform
spec:
  hard:
    # 计算资源限制
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    
    # 存储资源限制
    requests.storage: 100Gi
    persistentvolumeclaims: "20"
    
    # 对象数量限制
    pods: "50"
    services: "20"
    secrets: "20"
    configmaps: "20"
    deployments.apps: "20"
    statefulsets.apps: "10"
    
    # 网络资源限制
    services.loadbalancers: "5"
    services.nodeports: "10"

---
# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: paas-platform-network-policy
  namespace: paas-platform
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # 入站规则
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: paas-platform
    - namespaceSelector:
        matchLabels:
          name: kube-system
    - namespaceSelector:
        matchLabels:
          name: monitoring
  
  # 出站规则
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: paas-platform
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
# 服务账户
apiVersion: v1
kind: ServiceAccount
metadata:
  name: paas-platform-sa
  namespace: paas-platform
  labels:
    app.kubernetes.io/name: paas-platform
    app.kubernetes.io/component: serviceaccount
automountServiceAccountToken: true

---
# 集群角色
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: paas-platform-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses", "networkpolicies"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]

---
# 集群角色绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: paas-platform-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: paas-platform-role
subjects:
- kind: ServiceAccount
  name: paas-platform-sa
  namespace: paas-platform

---
# Pod 安全策略
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: paas-platform-psp
  namespace: paas-platform
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'

---
# 限制范围
apiVersion: v1
kind: LimitRange
metadata:
  name: paas-platform-limits
  namespace: paas-platform
spec:
  limits:
  # Pod 限制
  - type: Pod
    max:
      cpu: "4"
      memory: 8Gi
    min:
      cpu: 100m
      memory: 128Mi
  
  # 容器限制
  - type: Container
    default:
      cpu: 500m
      memory: 512Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    max:
      cpu: "2"
      memory: 4Gi
    min:
      cpu: 50m
      memory: 64Mi
  
  # PVC 限制
  - type: PersistentVolumeClaim
    max:
      storage: 50Gi
    min:
      storage: 1Gi
