<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="layout-header">
      <div class="header-left">
        <!-- 侧边栏折叠按钮 -->
        <el-button 
          type="text" 
          @click="toggleSidebar"
          class="sidebar-toggle"
        >
          <el-icon><Expand v-if="sidebarCollapsed" /><Fold v-else /></el-icon>
        </el-button>
        
        <!-- 平台Logo和标题 -->
        <div class="logo-container">
          <img src="/logo.svg" alt="PaaS平台" class="logo" />
          <h1 v-show="!sidebarCollapsed" class="platform-title">PaaS 管理平台</h1>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 全局搜索 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索应用、配置..."
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <!-- 通知中心 -->
        <el-badge :value="notificationCount" class="notification-badge">
          <el-button type="text" @click="showNotifications">
            <el-icon><Bell /></el-icon>
          </el-button>
        </el-badge>
        
        <!-- 主题切换 -->
        <el-button type="text" @click="toggleTheme">
          <el-icon><Sunny v-if="isDark" /><Moon v-else /></el-icon>
        </el-button>
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand" class="user-dropdown">
          <div class="user-info">
            <el-avatar :src="userStore.user?.avatar" :size="32">
              {{ userStore.user?.displayName?.charAt(0) }}
            </el-avatar>
            <span class="username">{{ userStore.user?.displayName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside 
        :width="sidebarCollapsed ? '64px' : '240px'" 
        class="layout-sidebar"
      >
        <SidebarMenu :collapsed="sidebarCollapsed" />
      </el-aside>
      
      <!-- 主内容区域 -->
      <el-main class="layout-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item 
              v-for="item in breadcrumbItems" 
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useDark, useToggle } from '@vueuse/core'
import SidebarMenu from '@/components/layout/SidebarMenu.vue'

// 响应式状态
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 侧边栏状态
const sidebarCollapsed = ref(false)
const searchKeyword = ref('')
const notificationCount = ref(3) // 示例通知数量

// 主题切换
const isDark = useDark()
const toggleTheme = useToggle(isDark)

// 面包屑导航
const breadcrumbItems = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title as string
  }))
})

// 方法定义
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    // TODO: 实现全局搜索功能
    console.log('搜索:', searchKeyword.value)
  }
}

const showNotifications = () => {
  // TODO: 显示通知面板
  ElMessage.info('通知功能开发中...')
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      // TODO: 跳转到系统设置
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
        router.push('/login')
        ElMessage.success('已成功退出登录')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}

// 监听路由变化，自动收起移动端侧边栏
watch(route, () => {
  if (window.innerWidth <= 768) {
    sidebarCollapsed.value = true
  }
})
</script>

<style lang="scss" scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  
  .header-left {
    display: flex;
    align-items: center;
    
    .sidebar-toggle {
      margin-right: 16px;
      font-size: 18px;
    }
    
    .logo-container {
      display: flex;
      align-items: center;
      
      .logo {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
      
      .platform-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .search-input {
      width: 300px;
      
      @media (max-width: 768px) {
        width: 200px;
      }
    }
    
    .notification-badge {
      .el-button {
        font-size: 18px;
      }
    }
    
    .user-dropdown {
      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.3s;
        
        &:hover {
          background-color: var(--el-fill-color-light);
        }
        
        .username {
          font-size: 14px;
          color: var(--el-text-color-primary);
          
          @media (max-width: 768px) {
            display: none;
          }
        }
      }
    }
  }
}

.layout-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.layout-sidebar {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-lighter);
  transition: width 0.3s ease;
  overflow: hidden;
}

.layout-main {
  background: var(--el-bg-color-page);
  padding: 0;
  overflow-y: auto;
  
  .breadcrumb-container {
    background: var(--el-bg-color);
    padding: 12px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .el-breadcrumb {
      font-size: 14px;
    }
  }
  
  .page-content {
    padding: 20px;
    min-height: calc(100vh - 60px - 45px);
  }
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
