import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getToken, isTokenExpiringSoon } from './auth'
import router from '@/router'

// 🔧 防止token刷新循环的标志和队列
let isRefreshingToken = false
let refreshTokenPromise: Promise<void> | null = null

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: '/api', // API基础路径
  timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  async (config) => {
    const token = getToken()

    if (token) {
      // 🔧 防止循环刷新：如果正在刷新token或者是刷新token的请求，跳过检查
      const isRefreshRequest = config.url?.includes('/auth/refresh') || config.url?.includes('/health')

      // 🔧 检查是否为开发模式token，开发模式token不需要刷新
      const isDevToken = token.includes('dev-signature') || token.split('.').length === 3 &&
        (() => {
          try {
            const payload = JSON.parse(atob(token.split('.')[1]))
            return payload.dev_mode === true
          } catch {
            return false
          }
        })()

      if (!isRefreshingToken && !isRefreshRequest && !isDevToken && isTokenExpiringSoon(token)) {
        // 🔧 使用Promise确保同时只有一个刷新请求
        if (!refreshTokenPromise) {
          refreshTokenPromise = (async () => {
            try {
              console.log('🔄 Token即将过期，开始自动刷新')
              isRefreshingToken = true

              const userStore = useUserStore()
              await userStore.refreshToken()

              console.log('✅ Token刷新成功')
            } catch (error) {
              console.error('自动刷新token失败:', error)
              // 刷新失败，跳转到登录页
              router.push('/login')
              throw error
            } finally {
              isRefreshingToken = false
              refreshTokenPromise = null
            }
          })()
        }

        await refreshTokenPromise

        // 获取新的token
        const newToken = getToken()
        if (newToken && newToken !== token) {
          config.headers.Authorization = `Bearer ${newToken}`
        } else {
          config.headers.Authorization = `Bearer ${token}`
        }
      } else {
        // 添加认证头（使用当前token）
        config.headers.Authorization = `Bearer ${token}`
      }
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    isRefreshingToken = false // 重置刷新状态
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message, data } = response.data
    
    // 成功响应
    if (code === 0 || code === 200) {
      return response.data
    }
    
    // 业务错误
    ElMessage.error(message || '请求失败')
    return Promise.reject(new Error(message || '请求失败'))
  },
  async (error) => {
    const { response } = error
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          // 未授权，清除用户数据并跳转到登录页
          ElMessage.error('登录已过期，请重新登录')
          const userStore = useUserStore()
          userStore.clearUserData()
          router.push('/login')
          break
          
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
          
        case 404:
          ElMessage.error('请求的资源不存在')
          break
          
        case 422:
          // 表单验证错误
          const message = data?.message || '请求参数错误'
          ElMessage.error(message)
          break
          
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
          
        case 500:
          ElMessage.error('服务器内部错误，请稍后再试')
          break
          
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

/**
 * 通用请求方法
 */
export const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.get(url, config)
  },
  
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return service.post(url, data, config)
  },
  
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return service.put(url, data, config)
  },
  
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.delete(url, config)
  },
  
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return service.patch(url, data, config)
  }
}

/**
 * 文件上传请求
 * @param url 上传地址
 * @param file 文件对象
 * @param onProgress 上传进度回调
 */
export const uploadFile = (
  url: string, 
  file: File, 
  onProgress?: (progress: number) => void
): Promise<any> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return service.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

/**
 * 下载文件
 * @param url 下载地址
 * @param filename 文件名
 */
export const downloadFile = async (url: string, filename?: string): Promise<void> => {
  try {
    const response = await service.get(url, {
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

export default service
