import { describe, it, expect, beforeEach, vi } from 'vitest'
import Cookies from 'js-cookie'
import {
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
  isTokenExpiringSoon,
  parseTokenPayload
} from '@/utils/auth'

// Mock js-cookie
vi.mock('js-cookie')
const mockCookies = vi.mocked(Cookies)

/**
 * 认证工具函数单元测试
 * 测试token存储、解析、过期检查等功能
 */
describe('auth utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('token 存储管理', () => {
    it('应该正确获取token', () => {
      mockCookies.get.mockReturnValue('test-token')
      
      const token = getToken()
      
      expect(token).toBe('test-token')
      expect(mockCookies.get).toHaveBeenCalledWith('paas_access_token')
    })

    it('应该正确设置token', () => {
      setToken('new-token')
      
      expect(mockCookies.set).toHaveBeenCalledWith('paas_access_token', 'new-token', {
        expires: 1,
        secure: false, // 因为测试环境不是https
        sameSite: 'strict'
      })
    })

    it('应该在https环境下设置secure标志', () => {
      // Mock location.protocol
      Object.defineProperty(window, 'location', {
        value: { protocol: 'https:' },
        writable: true
      })

      setToken('secure-token')
      
      expect(mockCookies.set).toHaveBeenCalledWith('paas_access_token', 'secure-token', {
        expires: 1,
        secure: true,
        sameSite: 'strict'
      })

      // 恢复原始值
      Object.defineProperty(window, 'location', {
        value: { protocol: 'http:' },
        writable: true
      })
    })

    it('应该正确移除token', () => {
      removeToken()
      
      expect(mockCookies.remove).toHaveBeenCalledWith('paas_access_token')
      expect(mockCookies.remove).toHaveBeenCalledWith('paas_refresh_token')
    })

    it('应该正确获取刷新token', () => {
      mockCookies.get.mockReturnValue('refresh-token')
      
      const refreshToken = getRefreshToken()
      
      expect(refreshToken).toBe('refresh-token')
      expect(mockCookies.get).toHaveBeenCalledWith('paas_refresh_token')
    })

    it('应该正确设置刷新token', () => {
      setRefreshToken('new-refresh-token')
      
      expect(mockCookies.set).toHaveBeenCalledWith('paas_refresh_token', 'new-refresh-token', {
        expires: 7,
        secure: false,
        sameSite: 'strict',
        httpOnly: false
      })
    })
  })

  describe('token 解析和验证', () => {
    it('应该正确解析token载荷', () => {
      // 创建一个模拟的JWT token
      const payload = { 
        sub: '1234567890', 
        name: 'John Doe', 
        exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
      }
      const encodedPayload = btoa(JSON.stringify(payload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = parseTokenPayload(mockToken)
      
      expect(result).toEqual(payload)
    })

    it('应该处理无效的token格式', () => {
      const invalidToken = 'invalid-token'
      
      const result = parseTokenPayload(invalidToken)
      
      expect(result).toBeNull()
    })

    it('应该处理无效的JSON载荷', () => {
      const invalidPayload = btoa('invalid-json')
      const invalidToken = `header.${invalidPayload}.signature`
      
      const result = parseTokenPayload(invalidToken)
      
      expect(result).toBeNull()
    })

    it('应该正确检查token是否即将过期', () => {
      // 创建一个20分钟后过期的token
      const payload = { 
        exp: Math.floor(Date.now() / 1000) + 1200 // 20分钟后过期
      }
      const encodedPayload = btoa(JSON.stringify(payload))
      const token = `header.${encodedPayload}.signature`

      const isExpiring = isTokenExpiringSoon(token)
      
      expect(isExpiring).toBe(true) // 因为少于30分钟
    })

    it('应该正确检查token未即将过期', () => {
      // 创建一个2小时后过期的token
      const payload = { 
        exp: Math.floor(Date.now() / 1000) + 7200 // 2小时后过期
      }
      const encodedPayload = btoa(JSON.stringify(payload))
      const token = `header.${encodedPayload}.signature`

      const isExpiring = isTokenExpiringSoon(token)
      
      expect(isExpiring).toBe(false) // 因为超过30分钟
    })

    it('应该将已过期的token标记为即将过期', () => {
      // 创建一个已过期的token
      const payload = { 
        exp: Math.floor(Date.now() / 1000) - 3600 // 1小时前过期
      }
      const encodedPayload = btoa(JSON.stringify(payload))
      const token = `header.${encodedPayload}.signature`

      const isExpiring = isTokenExpiringSoon(token)
      
      expect(isExpiring).toBe(true)
    })

    it('应该将无效token标记为即将过期', () => {
      const invalidToken = 'invalid-token'
      
      const isExpiring = isTokenExpiringSoon(invalidToken)
      
      expect(isExpiring).toBe(true)
    })

    it('应该处理没有exp字段的token', () => {
      const payload = { 
        sub: '1234567890', 
        name: 'John Doe'
        // 没有exp字段
      }
      const encodedPayload = btoa(JSON.stringify(payload))
      const token = `header.${encodedPayload}.signature`

      const isExpiring = isTokenExpiringSoon(token)
      
      expect(isExpiring).toBe(true) // 没有过期时间应该被认为是即将过期
    })
  })

  describe('边界情况', () => {
    it('应该处理空token', () => {
      expect(parseTokenPayload('')).toBeNull()
      expect(isTokenExpiringSoon('')).toBe(true)
    })

    it('应该处理undefined token', () => {
      expect(parseTokenPayload(undefined as any)).toBeNull()
      expect(isTokenExpiringSoon(undefined as any)).toBe(true)
    })

    it('应该处理只有两个部分的token', () => {
      const token = 'header.payload'
      
      expect(parseTokenPayload(token)).toBeNull()
      expect(isTokenExpiringSoon(token)).toBe(true)
    })

    it('应该处理base64解码失败的情况', () => {
      const token = 'header.invalid-base64!@#.signature'
      
      expect(parseTokenPayload(token)).toBeNull()
      expect(isTokenExpiringSoon(token)).toBe(true)
    })
  })
})
