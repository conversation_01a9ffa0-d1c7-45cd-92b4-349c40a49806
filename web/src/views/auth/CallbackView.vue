<template>
  <div class="callback-container">
    <div class="callback-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-icon class="loading-icon" :size="48">
          <Loading />
        </el-icon>
        <h2>正在处理登录...</h2>
        <p>请稍候，我们正在验证您的身份信息</p>
      </div>
      
      <!-- 成功状态 -->
      <div v-else-if="success" class="success-section">
        <el-icon class="success-icon" :size="48">
          <SuccessFilled />
        </el-icon>
        <h2>登录成功</h2>
        <p>欢迎回来！正在跳转到主页...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else class="error-section">
        <el-icon class="error-icon" :size="48">
          <CircleCloseFilled />
        </el-icon>
        <h2>登录失败</h2>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <el-button type="primary" @click="retryLogin">
            重试登录
          </el-button>
          <el-button @click="backToLogin">
            返回登录页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { idpApi } from '@/api/auth'
import { ElMessage } from 'element-plus'
import { Loading, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 状态管理
const loading = ref(true)
const success = ref(false)
const errorMessage = ref('')

// 处理 IDP 回调
const handleCallback = async () => {
  try {
    // 从 URL 参数获取回调信息
    const code = route.query.code as string
    const state = route.query.state as string
    const provider = route.params.provider as string
    
    if (!code || !state || !provider) {
      throw new Error('缺少必要的回调参数')
    }
    
    // 调用回调处理 API
    const response = await idpApi.handleCallback({
      provider_name: provider,
      code,
      state,
      tenant_id: 'default' // 或从配置获取
    })
    
    const result = response.data
    
    if (!result.success) {
      throw new Error(result.error_msg || '认证失败')
    }
    
    // 登录成功，保存用户信息和令牌
    if (result.user && result.token_pair) {
      // 保存令牌到本地存储
      localStorage.setItem('access_token', result.token_pair.access_token)
      if (result.token_pair.refresh_token) {
        localStorage.setItem('refresh_token', result.token_pair.refresh_token)
      }
      
      // 更新用户状态
      await userStore.setUser(result.user)
      
      // 显示成功消息
      if (result.is_new_user) {
        ElMessage.success('账号创建成功，欢迎使用！')
      } else if (result.is_linked) {
        ElMessage.success('账号关联成功，登录完成！')
      } else {
        ElMessage.success('登录成功，欢迎回来！')
      }
      
      success.value = true
      
      // 延迟跳转到主页
      setTimeout(() => {
        const redirect = route.query.redirect as string
        router.push(redirect || '/dashboard')
      }, 2000)
    } else {
      throw new Error('登录响应数据不完整')
    }
    
  } catch (error: any) {
    console.error('IDP 回调处理失败:', error)
    errorMessage.value = error.message || '登录处理失败，请重试'
    success.value = false
  } finally {
    loading.value = false
  }
}

// 重试登录
const retryLogin = () => {
  const provider = route.params.provider as string
  if (provider) {
    router.push(`/auth/login?provider=${provider}`)
  } else {
    router.push('/auth/login')
  }
}

// 返回登录页
const backToLogin = () => {
  router.push('/auth/login')
}

// 组件挂载时处理回调
onMounted(() => {
  handleCallback()
})
</script>

<style lang="scss" scoped>
.callback-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.callback-content {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 48px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 480px;
  width: 90%;
}

.loading-section,
.success-section,
.error-section {
  h2 {
    margin: 24px 0 16px;
    color: var(--el-text-color-primary);
    font-size: 24px;
    font-weight: 600;
  }
  
  p {
    margin: 0 0 24px;
    color: var(--el-text-color-regular);
    font-size: 16px;
    line-height: 1.5;
  }
}

.loading-icon {
  color: var(--el-color-primary);
  animation: rotate 2s linear infinite;
}

.success-icon {
  color: var(--el-color-success);
}

.error-icon {
  color: var(--el-color-danger);
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .callback-content {
    padding: 32px 24px;
    margin: 20px;
  }
  
  .error-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
