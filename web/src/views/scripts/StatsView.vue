<template>
  <div class="stats-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">脚本执行统计</h2>
        <p class="page-description">监控脚本执行性能和资源使用情况</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshStats">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportStats">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="overview-section">
      <div class="overview-cards">
        <el-card class="overview-card today">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="card-info">
              <h3>今日任务</h3>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="label">总数</span>
                  <span class="value">{{ statsOverview?.today.total_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功</span>
                  <span class="value success">{{ statsOverview?.today.completed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">失败</span>
                  <span class="value error">{{ statsOverview?.today.failed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功率</span>
                  <span class="value">{{ formatPercentage(statsOverview?.today.success_rate) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card yesterday">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="card-info">
              <h3>昨日任务</h3>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="label">总数</span>
                  <span class="value">{{ statsOverview?.yesterday.total_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功</span>
                  <span class="value success">{{ statsOverview?.yesterday.completed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">失败</span>
                  <span class="value error">{{ statsOverview?.yesterday.failed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功率</span>
                  <span class="value">{{ formatPercentage(statsOverview?.yesterday.success_rate) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card week">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="card-info">
              <h3>本周任务</h3>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="label">总数</span>
                  <span class="value">{{ statsOverview?.this_week.total_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功</span>
                  <span class="value success">{{ statsOverview?.this_week.completed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">失败</span>
                  <span class="value error">{{ statsOverview?.this_week.failed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">平均时长</span>
                  <span class="value">{{ formatDuration(statsOverview?.this_week.avg_duration) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card month">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-info">
              <h3>本月任务</h3>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="label">总数</span>
                  <span class="value">{{ statsOverview?.this_month.total_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">成功</span>
                  <span class="value success">{{ statsOverview?.this_month.completed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">失败</span>
                  <span class="value error">{{ statsOverview?.this_month.failed_tasks || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">平均时长</span>
                  <span class="value">{{ formatDuration(statsOverview?.this_month.avg_duration) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 时间范围选择 -->
      <div class="chart-controls">
        <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
          <el-radio-button label="1h">1小时</el-radio-button>
          <el-radio-button label="6h">6小时</el-radio-button>
          <el-radio-button label="24h">24小时</el-radio-button>
          <el-radio-button label="7d">7天</el-radio-button>
          <el-radio-button label="30d">30天</el-radio-button>
        </el-radio-group>
      </div>

      <div class="charts-grid">
        <!-- 任务执行趋势图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>任务执行趋势</span>
              <el-tag size="small">{{ getTimeRangeLabel(timeRange) }}</el-tag>
            </div>
          </template>
          <div ref="taskTrendChart" class="chart-container"></div>
        </el-card>

        <!-- 成功率趋势图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>成功率趋势</span>
              <el-tag size="small" type="success">{{ getTimeRangeLabel(timeRange) }}</el-tag>
            </div>
          </template>
          <div ref="successRateChart" class="chart-container"></div>
        </el-card>

        <!-- 平均执行时长图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>平均执行时长</span>
              <el-tag size="small" type="warning">{{ getTimeRangeLabel(timeRange) }}</el-tag>
            </div>
          </template>
          <div ref="durationChart" class="chart-container"></div>
        </el-card>

        <!-- 资源使用情况图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>资源使用情况</span>
              <el-tag size="small" type="info">{{ getTimeRangeLabel(timeRange) }}</el-tag>
            </div>
          </template>
          <div ref="resourceChart" class="chart-container"></div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useScriptsStore } from '@/stores/scripts'
import type { StatsOverview, MetricPoint } from '@/api/scripts'
import * as echarts from 'echarts'

// 响应式数据
const scriptsStore = useScriptsStore()
const statsOverview = ref<StatsOverview | null>(null)
const metrics = ref<MetricPoint[]>([])
const timeRange = ref('24h')

// 图表实例
const taskTrendChart = ref<HTMLElement>()
const successRateChart = ref<HTMLElement>()
const durationChart = ref<HTMLElement>()
const resourceChart = ref<HTMLElement>()

let taskTrendChartInstance: echarts.ECharts | null = null
let successRateChartInstance: echarts.ECharts | null = null
let durationChartInstance: echarts.ECharts | null = null
let resourceChartInstance: echarts.ECharts | null = null

// 方法定义
const loadStatsData = async () => {
  try {
    // 加载统计概览
    await scriptsStore.fetchStatsOverview()
    statsOverview.value = scriptsStore.statsOverview
    
    // 加载指标数据
    await scriptsStore.fetchMetrics({
      time_range: timeRange.value as any,
      interval: getInterval(timeRange.value)
    })
    metrics.value = scriptsStore.metrics
    
    // 更新图表
    await nextTick()
    updateCharts()
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const refreshStats = () => {
  loadStatsData()
}

const exportStats = () => {
  // TODO: 实现统计报告导出
  ElMessage.info('统计报告导出功能开发中...')
}

const handleTimeRangeChange = () => {
  loadStatsData()
}

const initCharts = () => {
  if (taskTrendChart.value) {
    taskTrendChartInstance = echarts.init(taskTrendChart.value)
  }
  if (successRateChart.value) {
    successRateChartInstance = echarts.init(successRateChart.value)
  }
  if (durationChart.value) {
    durationChartInstance = echarts.init(durationChart.value)
  }
  if (resourceChart.value) {
    resourceChartInstance = echarts.init(resourceChart.value)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

const updateCharts = () => {
  updateTaskTrendChart()
  updateSuccessRateChart()
  updateDurationChart()
  updateResourceChart()
}

const updateTaskTrendChart = () => {
  if (!taskTrendChartInstance || !metrics.value.length) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['任务总数']
    },
    xAxis: {
      type: 'category',
      data: metrics.value.map(m => formatChartTime(m.timestamp))
    },
    yAxis: {
      type: 'value',
      name: '任务数量'
    },
    series: [{
      name: '任务总数',
      type: 'line',
      data: metrics.value.map(m => m.task_count),
      smooth: true,
      areaStyle: {
        opacity: 0.3
      }
    }]
  }
  
  taskTrendChartInstance.setOption(option)
}

const updateSuccessRateChart = () => {
  if (!successRateChartInstance || !metrics.value.length) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>{a}: {c}%'
    },
    xAxis: {
      type: 'category',
      data: metrics.value.map(m => formatChartTime(m.timestamp))
    },
    yAxis: {
      type: 'value',
      name: '成功率 (%)',
      min: 0,
      max: 100
    },
    series: [{
      name: '成功率',
      type: 'line',
      data: metrics.value.map(m => m.success_rate),
      smooth: true,
      itemStyle: {
        color: '#67C23A'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
          ]
        }
      }
    }]
  }
  
  successRateChartInstance.setOption(option)
}

const updateDurationChart = () => {
  if (!durationChartInstance || !metrics.value.length) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const value = params[0].value
        return `${params[0].name}<br/>${params[0].seriesName}: ${formatDuration(value)}`
      }
    },
    xAxis: {
      type: 'category',
      data: metrics.value.map(m => formatChartTime(m.timestamp))
    },
    yAxis: {
      type: 'value',
      name: '执行时长 (秒)'
    },
    series: [{
      name: '平均执行时长',
      type: 'bar',
      data: metrics.value.map(m => m.avg_duration),
      itemStyle: {
        color: '#E6A23C'
      }
    }]
  }
  
  durationChartInstance.setOption(option)
}

const updateResourceChart = () => {
  if (!resourceChartInstance || !metrics.value.length) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['CPU使用率', '内存使用']
    },
    xAxis: {
      type: 'category',
      data: metrics.value.map(m => formatChartTime(m.timestamp))
    },
    yAxis: [
      {
        type: 'value',
        name: 'CPU使用率 (%)',
        position: 'left'
      },
      {
        type: 'value',
        name: '内存使用 (MB)',
        position: 'right'
      }
    ],
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        yAxisIndex: 0,
        data: metrics.value.map(m => m.cpu_usage),
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '内存使用',
        type: 'line',
        yAxisIndex: 1,
        data: metrics.value.map(m => m.memory_usage / 1024 / 1024), // 转换为MB
        smooth: true,
        itemStyle: { color: '#F56C6C' }
      }
    ]
  }
  
  resourceChartInstance.setOption(option)
}

const handleResize = () => {
  taskTrendChartInstance?.resize()
  successRateChartInstance?.resize()
  durationChartInstance?.resize()
  resourceChartInstance?.resize()
}

// 工具函数
const formatPercentage = (value?: number) => {
  return value !== undefined ? `${value.toFixed(1)}%` : '-'
}

const formatDuration = (duration?: number) => {
  if (!duration) return '-'
  
  if (duration < 60) {
    return `${duration.toFixed(0)}秒`
  } else if (duration < 3600) {
    return `${(duration / 60).toFixed(1)}分钟`
  } else {
    return `${(duration / 3600).toFixed(1)}小时`
  }
}

const getTimeRangeLabel = (range: string) => {
  const labelMap: Record<string, string> = {
    '1h': '最近1小时',
    '6h': '最近6小时',
    '24h': '最近24小时',
    '7d': '最近7天',
    '30d': '最近30天'
  }
  return labelMap[range] || range
}

const getInterval = (range: string) => {
  const intervalMap: Record<string, string> = {
    '1h': '5m',
    '6h': '15m',
    '24h': '1h',
    '7d': '6h',
    '30d': '1d'
  }
  return intervalMap[range] || '1h'
}

const formatChartTime = (timestamp: string) => {
  const date = new Date(timestamp)
  if (timeRange.value === '1h' || timeRange.value === '6h') {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (timeRange.value === '24h') {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 组件生命周期
onMounted(async () => {
  await loadStatsData()
  await nextTick()
  initCharts()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  taskTrendChartInstance?.dispose()
  successRateChartInstance?.dispose()
  durationChartInstance?.dispose()
  resourceChartInstance?.dispose()
})
</script>

<style lang="scss" scoped>
// 导入混合宏以使用样式函数
@use '@/styles/mixins' as mixins;

.stats-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      .page-description {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
    
    .header-right {
      display: flex;
      gap: 8px;
    }
  }
  
  .overview-section {
    margin-bottom: 24px;
    
    .overview-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .overview-card {
        .card-content {
          display: flex;
          align-items: center;
          gap: 16px;
          
          .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
          }
          
          .card-info {
            flex: 1;
            
            h3 {
              margin: 0 0 12px 0;
              font-size: 16px;
              font-weight: 600;
              color: var(--el-text-color-primary);
            }
            
            .stats-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 8px;
              
              .stat-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .label {
                  font-size: 12px;
                  color: var(--el-text-color-secondary);
                }
                
                .value {
                  font-size: 14px;
                  font-weight: 600;
                  color: var(--el-text-color-primary);
                  
                  &.success {
                    color: var(--el-color-success);
                  }
                  
                  &.error {
                    color: var(--el-color-danger);
                  }
                }
              }
            }
          }
        }
        
        &.today .card-icon {
          background: linear-gradient(135deg, #409EFF, #66B1FF);
        }
        
        &.yesterday .card-icon {
          background: linear-gradient(135deg, #67C23A, #85CE61);
        }
        
        &.week .card-icon {
          background: linear-gradient(135deg, #E6A23C, #EEBE77);
        }
        
        &.month .card-icon {
          background: linear-gradient(135deg, #F56C6C, #F78989);
        }
      }
    }
  }
  
  .charts-section {
    .chart-controls {
      @include mixins.card-style;
      padding: 16px 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
    }
    
    .charts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
      gap: 20px;
      
      .chart-card {
        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
        }
        
        .chart-container {
          height: 300px;
          width: 100%;
        }
      }
    }
  }
}
</style>
