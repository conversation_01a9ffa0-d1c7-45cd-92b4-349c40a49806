<template>
  <div class="create-template-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          创建脚本模板
        </h1>
        <p class="page-description">创建可重用的脚本模板，提高脚本执行效率</p>
      </div>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          <el-icon><Check /></el-icon>
          保存模板
        </el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-section">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="template-form"
      >
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>

          <el-form-item label="模板名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入模板名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="模板描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模板描述"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="分类" prop="category">
            <el-select v-model="form.category" placeholder="选择分类">
              <el-option label="系统管理" value="system" />
              <el-option label="数据处理" value="data" />
              <el-option label="部署脚本" value="deployment" />
              <el-option label="监控脚本" value="monitoring" />
              <el-option label="备份脚本" value="backup" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>

          <el-form-item label="标签">
            <el-tag
              v-for="tag in form.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              style="margin-right: 8px;"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="tagInputVisible"
              ref="tagInputRef"
              v-model="tagInputValue"
              size="small"
              style="width: 100px;"
              @keyup.enter="addTag"
              @blur="addTag"
            />
            <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
          </el-form-item>
        </el-card>

        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>脚本配置</span>
            </div>
          </template>

          <el-form-item label="脚本类型" prop="scriptType">
            <el-radio-group v-model="form.scriptType">
              <el-radio label="shell">Shell脚本</el-radio>
              <el-radio label="python">Python脚本</el-radio>
              <el-radio label="nodejs">Node.js脚本</el-radio>
              <el-radio label="sql">SQL脚本</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="执行环境" prop="environment">
            <el-select v-model="form.environment" placeholder="选择执行环境">
              <el-option label="生产环境" value="production" />
              <el-option label="测试环境" value="testing" />
              <el-option label="开发环境" value="development" />
              <el-option label="通用环境" value="general" />
            </el-select>
          </el-form-item>

          <el-form-item label="超时时间" prop="timeout">
            <el-input-number
              v-model="form.timeout"
              :min="1"
              :max="3600"
              controls-position="right"
              style="width: 200px;"
            />
            <span style="margin-left: 8px; color: #606266;">秒</span>
          </el-form-item>

          <el-form-item label="重试次数" prop="retryCount">
            <el-input-number
              v-model="form.retryCount"
              :min="0"
              :max="10"
              controls-position="right"
              style="width: 200px;"
            />
          </el-form-item>
        </el-card>

        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>参数配置</span>
              <el-button size="small" @click="addParameter">添加参数</el-button>
            </div>
          </template>

          <div class="parameters-section">
            <div
              v-for="(param, index) in form.parameters"
              :key="index"
              class="parameter-item"
            >
              <el-row :gutter="16">
                <el-col :span="6">
                  <el-input v-model="param.name" placeholder="参数名" />
                </el-col>
                <el-col :span="4">
                  <el-select v-model="param.type" placeholder="类型">
                    <el-option label="字符串" value="string" />
                    <el-option label="数字" value="number" />
                    <el-option label="布尔值" value="boolean" />
                    <el-option label="文件" value="file" />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-input v-model="param.defaultValue" placeholder="默认值" />
                </el-col>
                <el-col :span="6">
                  <el-input v-model="param.description" placeholder="参数描述" />
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeParameter(index)"
                    :icon="Delete"
                  />
                </el-col>
              </el-row>
            </div>
            
            <div v-if="form.parameters.length === 0" class="empty-parameters">
              <el-empty description="暂无参数配置" :image-size="80" />
            </div>
          </div>
        </el-card>

        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>脚本内容</span>
              <div class="header-actions">
                <el-button size="small" @click="formatScript">格式化</el-button>
                <el-button size="small" @click="validateScript">验证语法</el-button>
              </div>
            </div>
          </template>

          <el-form-item prop="content">
            <div class="script-editor">
              <el-input
                v-model="form.content"
                type="textarea"
                :rows="20"
                placeholder="请输入脚本内容..."
                class="script-textarea"
              />
            </div>
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Document, Check, Delete } from '@element-plus/icons-vue'

// 路由实例
const router = useRouter()

// 响应式数据
const saving = ref(false)
const formRef = ref()
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInputRef = ref()

// 表单数据
const form = reactive({
  name: '',
  description: '',
  category: '',
  tags: [],
  scriptType: 'shell',
  environment: 'general',
  timeout: 300,
  retryCount: 0,
  parameters: [],
  content: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入模板描述', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  scriptType: [
    { required: true, message: '请选择脚本类型', trigger: 'change' }
  ],
  environment: [
    { required: true, message: '请选择执行环境', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入脚本内容', trigger: 'blur' }
  ]
}

/**
 * 显示标签输入框
 */
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

/**
 * 添加标签
 */
const addTag = () => {
  const value = tagInputValue.value.trim()
  if (value && !form.tags.includes(value)) {
    form.tags.push(value)
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

/**
 * 移除标签
 */
const removeTag = (tag: string) => {
  const index = form.tags.indexOf(tag)
  if (index > -1) {
    form.tags.splice(index, 1)
  }
}

/**
 * 添加参数
 */
const addParameter = () => {
  form.parameters.push({
    name: '',
    type: 'string',
    defaultValue: '',
    description: '',
    required: false
  })
}

/**
 * 移除参数
 */
const removeParameter = (index: number) => {
  form.parameters.splice(index, 1)
}

/**
 * 格式化脚本
 */
const formatScript = () => {
  // TODO: 实现脚本格式化逻辑
  ElMessage.info('脚本格式化功能开发中...')
}

/**
 * 验证脚本语法
 */
const validateScript = () => {
  // TODO: 实现脚本语法验证逻辑
  ElMessage.info('脚本语法验证功能开发中...')
}

/**
 * 保存模板
 */
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true
    
    // TODO: 调用API保存模板
    // await templateApi.create(form)
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('模板创建成功')
    router.push('/scripts/templates')
  } catch (error) {
    console.error('保存模板失败:', error)
    ElMessage.error('保存模板失败')
  } finally {
    saving.value = false
  }
}

/**
 * 取消创建
 */
const handleCancel = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.create-template-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.form-section {
  .template-form {
    max-width: 1000px;
  }

  .form-card {
    margin-bottom: 24px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .parameters-section {
    .parameter-item {
      margin-bottom: 16px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
    }

    .empty-parameters {
      text-align: center;
      padding: 40px;
    }
  }

  .script-editor {
    .script-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}
</style>
