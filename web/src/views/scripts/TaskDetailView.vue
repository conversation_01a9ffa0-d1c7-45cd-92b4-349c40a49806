<template>
  <div class="task-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.push('/scripts')" class="back-btn">
          <el-icon><Back /></el-icon>
          返回任务列表
        </el-button>
        <div class="title-section">
          <h2 class="page-title">任务详情</h2>
          <p class="page-description">{{ taskId }}</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="refreshTask">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button 
          v-if="canCancel"
          type="danger" 
          @click="cancelTask"
        >
          <el-icon><Close /></el-icon>
          取消任务
        </el-button>
      </div>
    </div>

    <div v-if="task" class="task-content">
      <!-- 任务概览 -->
      <el-card class="overview-card">
        <template #header>
          <div class="card-header">
            <span>任务概览</span>
            <el-tag :type="getStatusTagType(task.status)" size="large">
              {{ getStatusLabel(task.status) }}
            </el-tag>
          </div>
        </template>
        
        <div class="overview-grid">
          <div class="overview-item">
            <label>任务ID</label>
            <span class="task-id">{{ task.id }}</span>
          </div>
          <div class="overview-item">
            <label>应用</label>
            <span>{{ task.app_id }}</span>
          </div>
          <div class="overview-item">
            <label>脚本路径</label>
            <span>{{ task.script_path }}</span>
          </div>
          <div class="overview-item">
            <label>运行时</label>
            <el-tag :type="getRuntimeTagType(task.runtime_type)">
              {{ getRuntimeLabel(task.runtime_type) }}
            </el-tag>
          </div>
          <div class="overview-item">
            <label>优先级</label>
            <el-tag :type="getPriorityTagType(task.priority)" size="small">
              {{ task.priority }}
            </el-tag>
          </div>
          <div class="overview-item">
            <label>超时时间</label>
            <span>{{ task.timeout }}秒</span>
          </div>
          <div class="overview-item">
            <label>创建时间</label>
            <span>{{ formatTime(task.created_at) }}</span>
          </div>
          <div class="overview-item">
            <label>开始时间</label>
            <span>{{ task.started_at ? formatTime(task.started_at) : '-' }}</span>
          </div>
          <div class="overview-item">
            <label>结束时间</label>
            <span>{{ task.finished_at ? formatTime(task.finished_at) : '-' }}</span>
          </div>
          <div class="overview-item">
            <label>执行时长</label>
            <span>{{ task.duration ? formatDuration(task.duration) : '-' }}</span>
          </div>
          <div class="overview-item">
            <label>退出码</label>
            <span :class="{ 'error-code': task.exit_code !== 0 }">
              {{ task.exit_code !== undefined ? task.exit_code : '-' }}
            </span>
          </div>
          <div v-if="task.error_message" class="overview-item full-width">
            <label>错误信息</label>
            <span class="error-message">{{ task.error_message }}</span>
          </div>
        </div>
      </el-card>

      <!-- 详细信息标签页 -->
      <el-card class="detail-card">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <!-- 参数配置 -->
          <el-tab-pane label="参数配置" name="parameters">
            <div class="tab-content">
              <div class="section">
                <h4>脚本参数</h4>
                <el-input
                  :model-value="formatJSON(task.parameters)"
                  type="textarea"
                  :rows="8"
                  readonly
                  class="json-viewer"
                />
              </div>
              
              <div class="section">
                <h4>环境变量</h4>
                <el-table :data="environmentList" class="env-table">
                  <el-table-column prop="key" label="变量名" width="200" />
                  <el-table-column prop="value" label="变量值" />
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <!-- 执行日志 -->
          <el-tab-pane label="执行日志" name="logs">
            <div class="tab-content">
              <div class="log-controls">
                <el-button @click="refreshLogs" size="small">
                  <el-icon><Refresh /></el-icon>
                  刷新日志
                </el-button>
                <el-button @click="downloadLogs" size="small">
                  <el-icon><Download /></el-icon>
                  下载日志
                </el-button>
                <el-switch
                  v-model="autoRefreshLogs"
                  active-text="自动刷新"
                  @change="toggleAutoRefresh"
                />
              </div>
              
              <div class="log-viewer">
                <div
                  v-for="(log, index) in logs"
                  :key="index"
                  :class="['log-entry', `log-${log.level}`]"
                >
                  <span class="log-timestamp">{{ formatTime(log.timestamp) }}</span>
                  <span class="log-level">{{ log.level.toUpperCase() }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
                <div v-if="logs.length === 0" class="no-logs">
                  暂无日志数据
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 执行结果 -->
          <el-tab-pane label="执行结果" name="result" :disabled="task.status !== 'completed'">
            <div class="tab-content">
              <div v-if="taskResult" class="result-content">
                <div class="section">
                  <h4>标准输出</h4>
                  <el-input
                    :model-value="taskResult.stdout"
                    type="textarea"
                    :rows="10"
                    readonly
                    class="output-viewer"
                  />
                </div>
                
                <div v-if="taskResult.stderr" class="section">
                  <h4>标准错误</h4>
                  <el-input
                    :model-value="taskResult.stderr"
                    type="textarea"
                    :rows="6"
                    readonly
                    class="error-viewer"
                  />
                </div>
                
                <div v-if="taskResult.artifacts?.length" class="section">
                  <h4>产物文件</h4>
                  <el-table :data="taskResult.artifacts" class="artifacts-table">
                    <el-table-column prop="name" label="文件名" />
                    <el-table-column prop="size" label="大小" :formatter="formatFileSize" />
                    <el-table-column prop="mime_type" label="类型" />
                    <el-table-column label="操作" width="120">
                      <template #default="{ row }">
                        <el-button size="small" @click="downloadArtifact(row)">
                          <el-icon><Download /></el-icon>
                          下载
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                
                <div v-if="taskResult.resource_usage" class="section">
                  <h4>资源使用情况</h4>
                  <div class="resource-grid">
                    <div class="resource-item">
                      <label>CPU使用率</label>
                      <span>{{ taskResult.resource_usage.cpu_usage.toFixed(2) }}%</span>
                    </div>
                    <div class="resource-item">
                      <label>内存使用</label>
                      <span>{{ formatBytes(taskResult.resource_usage.memory_usage) }}</span>
                    </div>
                    <div class="resource-item">
                      <label>磁盘使用</label>
                      <span>{{ formatBytes(taskResult.resource_usage.disk_usage) }}</span>
                    </div>
                    <div class="resource-item">
                      <label>网络IO</label>
                      <span>{{ formatBytes(taskResult.resource_usage.network_io) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="no-result">
                <el-empty description="暂无执行结果" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <div v-else-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else class="error-container">
      <el-empty description="任务不存在或加载失败" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useScriptsStore } from '@/stores/scripts'
import { scriptsApi } from '@/api/scripts'
import type { ScriptTask, LogEntry } from '@/types'
import dayjs from 'dayjs'

// 响应式数据
const route = useRoute()
const router = useRouter()
const scriptsStore = useScriptsStore()
const loading = ref(false)
const task = ref<ScriptTask | null>(null)
const taskResult = ref<any>(null)
const logs = ref<LogEntry[]>([])
const activeTab = ref('parameters')
const autoRefreshLogs = ref(false)
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const taskId = computed(() => route.params.id as string)

const canCancel = computed(() => {
  return task.value && ['pending', 'running'].includes(task.value.status)
})

const environmentList = computed(() => {
  if (!task.value?.environment) return []
  return Object.entries(task.value.environment).map(([key, value]) => ({
    key,
    value
  }))
})

// 方法定义
const loadTaskDetail = async () => {
  try {
    loading.value = true
    task.value = await scriptsStore.fetchTaskById(taskId.value)
    
    // 如果任务已完成，加载执行结果
    if (task.value.status === 'completed') {
      await loadTaskResult()
    }
  } catch (error) {
    console.error('加载任务详情失败:', error)
    ElMessage.error('加载任务详情失败')
  } finally {
    loading.value = false
  }
}

const loadTaskResult = async () => {
  try {
    const response = await scriptsApi.getTaskResult(taskId.value)
    taskResult.value = response.data
  } catch (error) {
    console.error('加载任务结果失败:', error)
  }
}

const loadTaskLogs = async () => {
  try {
    const response = await scriptsApi.getTaskLogs(taskId.value)
    logs.value = response.data
  } catch (error) {
    console.error('加载任务日志失败:', error)
  }
}

const refreshTask = () => {
  loadTaskDetail()
}

const refreshLogs = () => {
  loadTaskLogs()
}

const cancelTask = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消这个任务吗？',
      '确认取消',
      { type: 'warning' }
    )
    
    await scriptsStore.cancelTask(taskId.value)
    ElMessage.success('任务已取消')
    await loadTaskDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

const handleTabChange = (tabName: string) => {
  if (tabName === 'logs') {
    loadTaskLogs()
  } else if (tabName === 'result' && task.value?.status === 'completed') {
    loadTaskResult()
  }
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshTimer.value = setInterval(() => {
      if (activeTab.value === 'logs') {
        refreshLogs()
      }
      // 如果任务还在运行，也刷新任务状态
      if (task.value && ['pending', 'running'].includes(task.value.status)) {
        loadTaskDetail()
      }
    }, 5000) // 每5秒刷新一次
  } else {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
  }
}

const downloadLogs = () => {
  // TODO: 实现日志下载
  ElMessage.info('日志下载功能开发中...')
}

const downloadArtifact = async (artifact: any) => {
  try {
    const blob = await scriptsApi.downloadArtifact(taskId.value, artifact.name)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = artifact.name
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载产物失败:', error)
    ElMessage.error('下载产物失败')
  }
}

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    canceled: ''
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    canceled: '已取消'
  }
  return labelMap[status] || status
}

const getRuntimeTagType = (runtime: string) => {
  const typeMap: Record<string, string> = {
    python: 'warning',
    nodejs: 'success',
    go: 'info',
    java: 'danger'
  }
  return typeMap[runtime] || ''
}

const getRuntimeLabel = (runtime: string) => {
  const labelMap: Record<string, string> = {
    python: 'Python',
    nodejs: 'Node.js',
    go: 'Go',
    java: 'Java'
  }
  return labelMap[runtime] || runtime
}

const getPriorityTagType = (priority: number) => {
  if (priority >= 8) return 'danger'
  if (priority >= 6) return 'warning'
  if (priority >= 4) return 'success'
  return 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const formatDuration = (duration: number) => {
  if (duration < 60) {
    return `${duration}秒`
  } else if (duration < 3600) {
    return `${Math.floor(duration / 60)}分${duration % 60}秒`
  } else {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    return `${hours}时${minutes}分`
  }
}

const formatJSON = (obj: any) => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch {
    return String(obj)
  }
}

const formatFileSize = (row: any, column: any, cellValue: number) => {
  return formatBytes(cellValue)
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 组件挂载和卸载
onMounted(() => {
  loadTaskDetail()
  
  // 根据URL参数设置默认标签页
  const tab = route.query.tab as string
  if (tab && ['parameters', 'logs', 'result'].includes(tab)) {
    activeTab.value = tab
    if (tab === 'logs') {
      loadTaskLogs()
    }
  }
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.task-detail-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .back-btn {
        flex-shrink: 0;
      }
      
      .title-section {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0 0 4px 0;
        }
        
        .page-description {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          margin: 0;
          font-family: monospace;
        }
      }
    }
    
    .header-right {
      display: flex;
      gap: 8px;
    }
  }
  
  .task-content {
    .overview-card {
      margin-bottom: 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .overview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        
        .overview-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          &.full-width {
            grid-column: 1 / -1;
          }
          
          label {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            font-weight: 500;
          }
          
          span {
            font-size: 14px;
            color: var(--el-text-color-primary);
            
            &.task-id {
              font-family: monospace;
              font-size: 12px;
            }
            
            &.error-code {
              color: var(--el-color-danger);
              font-weight: 500;
            }
            
            &.error-message {
              color: var(--el-color-danger);
              word-break: break-all;
            }
          }
        }
      }
    }
    
    .detail-card {
      .tab-content {
        .section {
          margin-bottom: 24px;
          
          h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
          
          .json-viewer,
          .output-viewer {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
          }
          
          .error-viewer {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            
            :deep(.el-textarea__inner) {
              background-color: var(--el-color-danger-light-9);
              border-color: var(--el-color-danger-light-7);
            }
          }
        }
        
        .log-controls {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding: 12px;
          background: var(--el-bg-color-page);
          border-radius: 4px;
          
          .el-switch {
            margin-left: auto;
          }
        }
        
        .log-viewer {
          background: #1e1e1e;
          color: #d4d4d4;
          padding: 16px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          max-height: 500px;
          overflow-y: auto;
          
          .log-entry {
            display: flex;
            gap: 12px;
            margin-bottom: 4px;
            
            .log-timestamp {
              color: #569cd6;
              flex-shrink: 0;
            }
            
            .log-level {
              width: 50px;
              flex-shrink: 0;
              font-weight: bold;
              
              &.log-debug { color: #9cdcfe; }
              &.log-info { color: #4ec9b0; }
              &.log-warn { color: #dcdcaa; }
              &.log-error { color: #f44747; }
            }
            
            .log-message {
              flex: 1;
              word-break: break-all;
            }
          }
          
          .no-logs {
            text-align: center;
            color: #6a6a6a;
            padding: 32px;
          }
        }
        
        .resource-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          
          .resource-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: var(--el-bg-color-page);
            border-radius: 4px;
            
            label {
              font-size: 14px;
              color: var(--el-text-color-secondary);
            }
            
            span {
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);
            }
          }
        }
        
        .no-result {
          padding: 32px;
          text-align: center;
        }
      }
    }
  }
  
  .loading-container,
  .error-container {
    padding: 32px;
  }
}
</style>
