<template>
  <div class="execute-script-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">执行脚本</h2>
        <p class="page-description">提交脚本执行任务，支持多种运行时环境</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.push('/scripts')">
          <el-icon><Back /></el-icon>
          返回任务列表
        </el-button>
      </div>
    </div>

    <div class="execute-form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="execute-form"
      >
        <!-- 基础信息 -->
        <el-card class="form-section" header="基础信息">
          <el-form-item label="应用" prop="app_id" required>
            <el-select
              v-model="form.app_id"
              placeholder="选择应用"
              filterable
              class="full-width"
            >
              <el-option
                v-for="app in applications"
                :key="app.id"
                :label="app.displayName || app.name"
                :value="app.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="脚本路径" prop="script_path" required>
            <el-input
              v-model="form.script_path"
              placeholder="例如: main.py, index.js, main.go"
              class="full-width"
            />
          </el-form-item>

          <el-form-item label="运行时" prop="runtime_type" required>
            <el-select
              v-model="form.runtime_type"
              placeholder="选择运行时"
              class="full-width"
            >
              <el-option label="Python" value="python" />
              <el-option label="Node.js" value="nodejs" />
              <el-option label="Go" value="go" />
              <el-option label="Java" value="java" />
            </el-select>
          </el-form-item>

          <el-form-item label="优先级" prop="priority">
            <el-slider
              v-model="form.priority"
              :min="1"
              :max="10"
              :marks="priorityMarks"
              show-tooltip
              class="priority-slider"
            />
          </el-form-item>

          <el-form-item label="超时时间" prop="timeout">
            <el-input-number
              v-model="form.timeout"
              :min="30"
              :max="3600"
              :step="30"
              controls-position="right"
              class="timeout-input"
            />
            <span class="input-suffix">秒</span>
          </el-form-item>
        </el-card>

        <!-- 参数配置 -->
        <el-card class="form-section" header="参数配置">
          <el-form-item label="脚本参数">
            <div class="parameter-editor">
              <div class="editor-header">
                <span>JSON 格式参数</span>
                <el-button size="small" text @click="formatParameters">
                  <el-icon><MagicStick /></el-icon>
                  格式化
                </el-button>
              </div>
              <el-input
                v-model="parametersText"
                type="textarea"
                :rows="6"
                placeholder='{"key": "value", "number": 123, "boolean": true}'
                class="parameter-textarea"
              />
            </div>
          </el-form-item>

          <el-form-item label="环境变量">
            <div class="env-variables">
              <div
                v-for="(env, index) in form.environment"
                :key="index"
                class="env-item"
              >
                <el-input
                  v-model="env.key"
                  placeholder="变量名"
                  class="env-key"
                />
                <el-input
                  v-model="env.value"
                  placeholder="变量值"
                  class="env-value"
                />
                <el-button
                  type="danger"
                  size="small"
                  @click="removeEnvVariable(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <el-button
                type="primary"
                size="small"
                @click="addEnvVariable"
                class="add-env-btn"
              >
                <el-icon><Plus /></el-icon>
                添加环境变量
              </el-button>
            </div>
          </el-form-item>
        </el-card>

        <!-- 高级选项 -->
        <el-card class="form-section" header="高级选项">
          <el-form-item label="回调地址">
            <el-input
              v-model="form.callback_url"
              placeholder="任务完成后的回调URL（可选）"
              class="full-width"
            />
          </el-form-item>

          <el-form-item label="使用模板">
            <el-select
              v-model="selectedTemplate"
              placeholder="选择脚本模板（可选）"
              clearable
              filterable
              class="full-width"
              @change="applyTemplate"
            >
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span class="template-name">{{ template.name }}</span>
                  <el-tag size="small" :type="getLanguageTagType(template.language)">
                    {{ template.language }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-card>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button size="large" @click="resetForm">
            重置
          </el-button>
          <el-button
            type="primary"
            size="large"
            :loading="submitting"
            @click="submitForm"
          >
            <el-icon><VideoPlay /></el-icon>
            执行脚本
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useScriptsStore } from '@/stores/scripts'
import { appsApi } from '@/api/apps'
import type { Application, ScriptTemplate } from '@/types'
import type { FormInstance, FormRules } from 'element-plus'

// 响应式数据
const router = useRouter()
const scriptsStore = useScriptsStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)
const applications = ref<Application[]>([])
const templates = ref<ScriptTemplate[]>([])
const selectedTemplate = ref('')
const parametersText = ref('{}')

// 表单数据
const form = reactive({
  app_id: '',
  script_path: '',
  runtime_type: 'python',
  priority: 5,
  timeout: 300,
  parameters: {},
  environment: [{ key: '', value: '' }],
  callback_url: ''
})

// 优先级标记
const priorityMarks = {
  1: '低',
  5: '中',
  10: '高'
}

// 表单验证规则
const rules: FormRules = {
  app_id: [
    { required: true, message: '请选择应用', trigger: 'change' }
  ],
  script_path: [
    { required: true, message: '请输入脚本路径', trigger: 'blur' },
    { min: 1, max: 255, message: '脚本路径长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  runtime_type: [
    { required: true, message: '请选择运行时', trigger: 'change' }
  ],
  priority: [
    { type: 'number', min: 1, max: 10, message: '优先级范围为 1-10', trigger: 'change' }
  ],
  timeout: [
    { type: 'number', min: 30, max: 3600, message: '超时时间范围为 30-3600 秒', trigger: 'change' }
  ]
}

// 监听参数文本变化
watch(parametersText, (newValue) => {
  try {
    form.parameters = JSON.parse(newValue || '{}')
  } catch (error) {
    // 忽略JSON解析错误，用户输入过程中可能不是有效JSON
  }
})

// 方法定义
const loadApplications = async () => {
  try {
    const response = await appsApi.getApps()
    applications.value = response.data.items
  } catch (error) {
    console.error('加载应用列表失败:', error)
  }
}

const loadTemplates = async () => {
  try {
    await scriptsStore.fetchTemplates({ status: 'active' })
    templates.value = scriptsStore.activeTemplates
  } catch (error) {
    console.error('加载模板列表失败:', error)
  }
}

const formatParameters = () => {
  try {
    const parsed = JSON.parse(parametersText.value || '{}')
    parametersText.value = JSON.stringify(parsed, null, 2)
  } catch (error) {
    ElMessage.warning('参数格式不正确，无法格式化')
  }
}

const addEnvVariable = () => {
  form.environment.push({ key: '', value: '' })
}

const removeEnvVariable = (index: number) => {
  if (form.environment.length > 1) {
    form.environment.splice(index, 1)
  }
}

const applyTemplate = async (templateId: string) => {
  if (!templateId) return
  
  try {
    const template = await scriptsStore.fetchTemplateById(templateId)
    
    // 应用模板配置
    form.runtime_type = template.language
    form.script_path = template.content.includes('main.') ? 
      template.content.split('\n')[0].replace(/^#\s*/, '') : 
      form.script_path
    
    // 应用模板参数
    if (template.parameters) {
      parametersText.value = JSON.stringify(template.parameters, null, 2)
    }
    
    // 应用模板环境变量
    if (template.environment) {
      form.environment = Object.entries(template.environment).map(([key, value]) => ({
        key,
        value: value as string
      }))
    }
    
    ElMessage.success('模板已应用')
  } catch (error) {
    ElMessage.error('应用模板失败')
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  form.environment = [{ key: '', value: '' }]
  parametersText.value = '{}'
  selectedTemplate.value = ''
}

const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    // 构建环境变量对象
    const environment: Record<string, string> = {}
    form.environment.forEach(env => {
      if (env.key && env.value) {
        environment[env.key] = env.value
      }
    })
    
    // 提交执行请求
    const result = await scriptsStore.executeScript({
      app_id: form.app_id,
      script_path: form.script_path,
      runtime_type: form.runtime_type as any,
      parameters: form.parameters,
      environment,
      timeout: form.timeout,
      priority: form.priority,
      callback_url: form.callback_url || undefined
    })
    
    ElMessage.success('脚本执行任务已提交')
    
    // 跳转到任务详情页
    router.push(`/scripts/tasks/${result.task_id}`)
    
  } catch (error) {
    console.error('提交执行任务失败:', error)
    ElMessage.error('提交执行任务失败')
  } finally {
    submitting.value = false
  }
}

const getLanguageTagType = (language: string) => {
  const typeMap: Record<string, string> = {
    python: 'warning',
    nodejs: 'success',
    go: 'info',
    java: 'danger'
  }
  return typeMap[language] || ''
}

// 组件挂载时加载数据
onMounted(() => {
  loadApplications()
  loadTemplates()
})
</script>

<style lang="scss" scoped>
.execute-script-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      .page-description {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .execute-form-container {
    max-width: 800px;
    margin: 0 auto;
    
    .execute-form {
      .form-section {
        margin-bottom: 24px;
        
        :deep(.el-card__header) {
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }
      
      .full-width {
        width: 100%;
      }
      
      .priority-slider {
        width: 300px;
      }
      
      .timeout-input {
        width: 200px;
      }
      
      .input-suffix {
        margin-left: 8px;
        color: var(--el-text-color-secondary);
      }
      
      .parameter-editor {
        .editor-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          span {
            font-size: 14px;
            color: var(--el-text-color-secondary);
          }
        }
        
        .parameter-textarea {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }
      
      .env-variables {
        .env-item {
          display: flex;
          gap: 8px;
          margin-bottom: 8px;
          align-items: center;
          
          .env-key {
            flex: 1;
          }
          
          .env-value {
            flex: 2;
          }
        }
        
        .add-env-btn {
          margin-top: 8px;
        }
      }
      
      .template-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .template-name {
          flex: 1;
        }
      }
      
      .form-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid var(--el-border-color-light);
      }
    }
  }
}
</style>
