<template>
  <div class="template-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">脚本模板管理</h2>
        <p class="page-description">管理可重用的脚本模板，提高开发效率</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/scripts/templates/create')">
          <el-icon><Plus /></el-icon>
          创建模板
        </el-button>
      </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索模板名称或描述..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="filterCategory"
          placeholder="模板分类"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部分类" value="" />
          <el-option label="数据处理" value="data-processing" />
          <el-option label="机器学习" value="machine-learning" />
          <el-option label="Web开发" value="web-development" />
          <el-option label="自动化" value="automation" />
          <el-option label="测试" value="testing" />
        </el-select>
        
        <el-select
          v-model="filterLanguage"
          placeholder="编程语言"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部语言" value="" />
          <el-option label="Python" value="python" />
          <el-option label="Node.js" value="nodejs" />
          <el-option label="Go" value="go" />
          <el-option label="Java" value="java" />
        </el-select>
        
        <el-select
          v-model="filterStatus"
          placeholder="模板状态"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="草稿" value="draft" />
          <el-option label="活跃" value="active" />
          <el-option label="已弃用" value="deprecated" />
        </el-select>
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="showBatchActions = !showBatchActions">
          <el-icon><Operation /></el-icon>
          批量操作
        </el-button>
      </div>
    </div>
    
    <!-- 批量操作栏 -->
    <div v-show="showBatchActions" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedTemplates.length }} 个模板
      </div>
      <div class="batch-buttons">
        <el-button size="small" @click="batchActivate">批量激活</el-button>
        <el-button size="small" @click="batchDeactivate">批量停用</el-button>
        <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>
    
    <!-- 模板列表 -->
    <div class="template-list">
      <el-table
        v-loading="scriptsStore.templateLoading"
        :data="scriptsStore.templates"
        @selection-change="handleSelectionChange"
        class="template-table"
        empty-text="暂无模板数据"
      >
        <el-table-column 
          v-if="showBatchActions"
          type="selection" 
          width="55" 
        />
        
        <el-table-column label="模板信息" min-width="250">
          <template #default="{ row }">
            <div class="template-info">
              <div class="template-header">
                <span class="template-name">{{ row.name }}</span>
                <el-tag size="small" :type="getLanguageTagType(row.language)">
                  {{ row.language }}
                </el-tag>
              </div>
              <div class="template-description">{{ row.description || '暂无描述' }}</div>
              <div class="template-meta">
                <span class="template-version">v{{ row.version }}</span>
                <span class="template-category">{{ getCategoryLabel(row.category) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="标签" width="200">
          <template #default="{ row }">
            <div class="template-tags">
              <el-tag
                v-for="tag in row.tags.slice(0, 3)"
                :key="tag"
                size="small"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
              <el-tag
                v-if="row.tags.length > 3"
                size="small"
                type="info"
                class="tag-more"
              >
                +{{ row.tags.length - 3 }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建者" width="120">
          <template #default="{ row }">
            <span class="creator">{{ row.created_by }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="150">
          <template #default="{ row }">
            <span class="create-time">
              {{ formatTime(row.created_at) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                type="primary" 
                @click="viewTemplate(row.id)"
              >
                查看
              </el-button>
              
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="use">
                      <el-icon><VideoPlay /></el-icon>使用模板
                    </el-dropdown-item>
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="copy">
                      <el-icon><CopyDocument /></el-icon>复制
                    </el-dropdown-item>
                    <el-dropdown-item 
                      command="activate" 
                      :disabled="row.status === 'active'"
                    >
                      <el-icon><Check /></el-icon>激活
                    </el-dropdown-item>
                    <el-dropdown-item 
                      command="deactivate" 
                      :disabled="row.status !== 'active'"
                    >
                      <el-icon><Close /></el-icon>停用
                    </el-dropdown-item>
                    <el-dropdown-item divided command="delete" class="danger-item">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="scriptsStore.templatePagination.page"
          v-model:page-size="scriptsStore.templatePagination.pageSize"
          :total="scriptsStore.templatePagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useScriptsStore } from '@/stores/scripts'
import type { ScriptTemplate } from '@/types'
import dayjs from 'dayjs'

// 响应式数据
const router = useRouter()
const scriptsStore = useScriptsStore()
const showBatchActions = ref(false)
const searchKeyword = ref('')
const filterCategory = ref('')
const filterLanguage = ref('')
const filterStatus = ref('')
const selectedTemplates = ref<ScriptTemplate[]>([])

// 计算属性
const queryParams = computed(() => ({
  page: scriptsStore.templatePagination.page,
  pageSize: scriptsStore.templatePagination.pageSize,
  category: filterCategory.value || undefined,
  language: filterLanguage.value || undefined,
  status: filterStatus.value || undefined
}))

// 方法定义
const loadTemplateList = async () => {
  try {
    await scriptsStore.fetchTemplates(queryParams.value)
  } catch (error) {
    ElMessage.error('加载模板列表失败')
  }
}

const handleSearch = debounce(() => {
  scriptsStore.templatePagination.page = 1
  loadTemplateList()
}, 300)

const handleFilter = () => {
  scriptsStore.templatePagination.page = 1
  loadTemplateList()
}

const refreshList = () => {
  loadTemplateList()
}

const handleSelectionChange = (selection: ScriptTemplate[]) => {
  selectedTemplates.value = selection
}

const handleSizeChange = (size: number) => {
  scriptsStore.templatePagination.pageSize = size
  scriptsStore.templatePagination.page = 1
  loadTemplateList()
}

const handleCurrentChange = (page: number) => {
  scriptsStore.templatePagination.page = page
  loadTemplateList()
}

const viewTemplate = (id: string) => {
  router.push(`/scripts/templates/${id}`)
}

const handleAction = async (command: string, template: ScriptTemplate) => {
  switch (command) {
    case 'use':
      useTemplate(template)
      break
    case 'edit':
      editTemplate(template)
      break
    case 'copy':
      copyTemplate(template)
      break
    case 'activate':
      await activateTemplate(template)
      break
    case 'deactivate':
      await deactivateTemplate(template)
      break
    case 'delete':
      await deleteTemplate(template)
      break
  }
}

const useTemplate = (template: ScriptTemplate) => {
  router.push({
    path: '/scripts/execute',
    query: { template: template.id }
  })
}

const editTemplate = (template: ScriptTemplate) => {
  router.push(`/scripts/templates/${template.id}/edit`)
}

const copyTemplate = (template: ScriptTemplate) => {
  router.push({
    path: '/scripts/templates/create',
    query: { copy: template.id }
  })
}

const activateTemplate = async (template: ScriptTemplate) => {
  try {
    await scriptsStore.updateTemplate(template.id, { status: 'active' })
    ElMessage.success('模板已激活')
  } catch (error) {
    ElMessage.error('激活模板失败')
  }
}

const deactivateTemplate = async (template: ScriptTemplate) => {
  try {
    await scriptsStore.updateTemplate(template.id, { status: 'deprecated' })
    ElMessage.success('模板已停用')
  } catch (error) {
    ElMessage.error('停用模板失败')
  }
}

const deleteTemplate = async (template: ScriptTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？此操作不可恢复！`,
      '确认删除',
      { 
        type: 'error',
        confirmButtonText: '确定删除',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    await scriptsStore.deleteTemplate(template.id)
    ElMessage.success('模板已删除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除模板失败')
    }
  }
}

// 批量操作
const batchActivate = async () => {
  // TODO: 实现批量激活
  ElMessage.info('批量激活功能开发中...')
}

const batchDeactivate = async () => {
  // TODO: 实现批量停用
  ElMessage.info('批量停用功能开发中...')
}

const batchDelete = async () => {
  // TODO: 实现批量删除
  ElMessage.info('批量删除功能开发中...')
}

// 工具函数
const getLanguageTagType = (language: string) => {
  const typeMap: Record<string, string> = {
    python: 'warning',
    nodejs: 'success',
    go: 'info',
    java: 'danger'
  }
  return typeMap[language] || ''
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: 'info',
    active: 'success',
    deprecated: 'danger'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    draft: '草稿',
    active: '活跃',
    deprecated: '已弃用'
  }
  return labelMap[status] || status
}

const getCategoryLabel = (category: string) => {
  const labelMap: Record<string, string> = {
    'data-processing': '数据处理',
    'machine-learning': '机器学习',
    'web-development': 'Web开发',
    'automation': '自动化',
    'testing': '测试'
  }
  return labelMap[category] || category
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadTemplateList()
})
</script>

<style lang="scss" scoped>
// 导入混合宏以使用样式函数
@use '@/styles/mixins' as mixins;

.template-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      .page-description {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .filter-section {
    @include mixins.card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      flex: 1;
      
      .search-input {
        width: 300px;
      }
      
      .filter-select {
        width: 120px;
      }
    }
    
    .filter-right {
      display: flex;
      gap: 8px;
    }
  }
  
  .batch-actions {
    @include mixins.card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px 20px;
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-7);
    
    .batch-info {
      font-size: 14px;
      color: var(--el-color-primary);
    }
    
    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .template-list {
    @include mixins.card-style;
    padding: 0;
    
    .template-table {
      .template-info {
        .template-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;
          
          .template-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }
        
        .template-description {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-bottom: 4px;
          @include mixins.text-ellipsis;
          max-width: 200px;
        }
        
        .template-meta {
          display: flex;
          gap: 8px;
          font-size: 11px;
          color: var(--el-text-color-placeholder);
          
          .template-version {
            font-family: monospace;
          }
        }
      }
      
      .template-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        
        .tag-item,
        .tag-more {
          margin: 0;
        }
      }
      
      .creator {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
      
      .create-time {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
      
      .action-buttons {
        display: flex;
        gap: 8px;
      }
      
      :deep(.danger-item) {
        color: var(--el-color-danger);
      }
    }
    
    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
