<template>
  <div class="template-detail-container">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          脚本模板详情
        </h1>
        <p class="page-description">查看脚本模板的详细信息</p>
      </div>
      <div class="header-actions">
        <el-button @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑模板
        </el-button>
        <el-button type="primary" @click="handleExecute">
          <el-icon><VideoPlay /></el-icon>
          执行脚本
        </el-button>
      </div>
    </div>

    <div class="content-section">
      <el-card>
        <div class="placeholder-content">
          <el-empty description="脚本模板详情功能开发中..." />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Document, Edit, VideoPlay } from '@element-plus/icons-vue'

const handleEdit = () => {
  ElMessage.info('编辑模板功能开发中...')
}

const handleExecute = () => {
  ElMessage.info('执行脚本功能开发中...')
}
</script>

<style lang="scss" scoped>
.template-detail-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.content-section {
  .placeholder-content {
    padding: 60px;
    text-align: center;
  }
}
</style>
