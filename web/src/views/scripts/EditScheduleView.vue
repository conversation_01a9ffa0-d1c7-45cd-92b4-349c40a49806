<template>
  <div class="edit-schedule-container">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Edit /></el-icon>
          编辑任务调度
        </h1>
        <p class="page-description">编辑定时执行的脚本任务</p>
      </div>
    </div>
    <div class="content-section">
      <el-card>
        <div class="placeholder-content">
          <el-empty description="编辑任务调度功能开发中..." />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Edit } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.edit-schedule-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.content-section {
  .placeholder-content {
    padding: 60px;
    text-align: center;
  }
}
</style>
