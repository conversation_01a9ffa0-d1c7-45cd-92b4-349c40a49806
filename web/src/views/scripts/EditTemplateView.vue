<template>
  <div class="edit-template-container">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Edit /></el-icon>
          编辑脚本模板
        </h1>
        <p class="page-description">编辑现有的脚本模板</p>
      </div>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          <el-icon><Check /></el-icon>
          保存更改
        </el-button>
      </div>
    </div>

    <div class="content-section">
      <el-card>
        <div class="placeholder-content">
          <el-empty description="编辑脚本模板功能开发中..." />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Edit, Check } from '@element-plus/icons-vue'

const router = useRouter()
const saving = ref(false)

const handleSave = () => {
  ElMessage.info('编辑脚本模板功能开发中...')
}

const handleCancel = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.edit-template-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.content-section {
  .placeholder-content {
    padding: 60px;
    text-align: center;
  }
}
</style>
