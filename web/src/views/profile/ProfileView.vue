<template>
  <div class="profile-view-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><User /></el-icon>
          个人中心
        </h1>
        <p class="page-description">管理您的个人信息、安全设置和偏好配置</p>
      </div>
    </div>

    <div class="profile-content">
      <!-- 左侧导航 -->
      <div class="profile-sidebar">
        <el-menu
          v-model="activeTab"
          mode="vertical"
          @select="handleTabChange"
        >
          <el-menu-item index="basic">
            <el-icon><UserFilled /></el-icon>
            <span>基本信息</span>
          </el-menu-item>
          <el-menu-item index="security">
            <el-icon><Lock /></el-icon>
            <span>安全设置</span>
          </el-menu-item>
          <el-menu-item index="preferences">
            <el-icon><Setting /></el-icon>
            <span>偏好设置</span>
          </el-menu-item>
          <el-menu-item index="notifications">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧内容 -->
      <div class="profile-main">
        <!-- 基本信息 -->
        <div v-show="activeTab === 'basic'" class="tab-content">
          <div class="section-header">
            <h3>基本信息</h3>
            <p>更新您的个人基本信息</p>
          </div>
          
          <el-form
            ref="basicFormRef"
            :model="basicForm"
            :rules="basicRules"
            label-width="100px"
            class="profile-form"
          >
            <div class="avatar-section">
              <div class="avatar-container">
                <el-avatar :size="80" :src="basicForm.avatar">
                  <el-icon><UserFilled /></el-icon>
                </el-avatar>
                <el-button type="text" @click="changeAvatar">更换头像</el-button>
              </div>
            </div>

            <el-form-item label="用户名" prop="username">
              <el-input v-model="basicForm.username" disabled />
            </el-form-item>

            <el-form-item label="姓名" prop="realName">
              <el-input v-model="basicForm.realName" placeholder="请输入真实姓名" />
            </el-form-item>

            <el-form-item label="邮箱" prop="email">
              <el-input v-model="basicForm.email" placeholder="请输入邮箱地址" />
            </el-form-item>

            <el-form-item label="手机号" prop="phone">
              <el-input v-model="basicForm.phone" placeholder="请输入手机号码" />
            </el-form-item>

            <el-form-item label="部门">
              <el-input v-model="basicForm.department" placeholder="请输入所属部门" />
            </el-form-item>

            <el-form-item label="职位">
              <el-input v-model="basicForm.position" placeholder="请输入职位" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveBasicInfo" :loading="saving">
                保存更改
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 安全设置 -->
        <div v-show="activeTab === 'security'" class="tab-content">
          <div class="section-header">
            <h3>安全设置</h3>
            <p>管理您的账户安全相关设置</p>
          </div>

          <!-- 修改密码 -->
          <div class="security-section">
            <h4>修改密码</h4>
            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-width="120px"
              class="profile-form"
            >
              <el-form-item label="当前密码" prop="currentPassword">
                <el-input
                  v-model="passwordForm.currentPassword"
                  type="password"
                  placeholder="请输入当前密码"
                  show-password
                />
              </el-form-item>

              <el-form-item label="新密码" prop="newPassword">
                <el-input
                  v-model="passwordForm.newPassword"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                />
              </el-form-item>

              <el-form-item label="确认新密码" prop="confirmPassword">
                <el-input
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="changePassword" :loading="saving">
                  修改密码
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 两步验证 -->
          <div class="security-section">
            <h4>两步验证</h4>
            <div class="security-item">
              <div class="item-info">
                <div class="item-title">短信验证</div>
                <div class="item-desc">通过短信接收验证码</div>
              </div>
              <el-switch v-model="securitySettings.smsAuth" @change="updateSecuritySetting" />
            </div>
            <div class="security-item">
              <div class="item-info">
                <div class="item-title">邮箱验证</div>
                <div class="item-desc">通过邮箱接收验证码</div>
              </div>
              <el-switch v-model="securitySettings.emailAuth" @change="updateSecuritySetting" />
            </div>
          </div>
        </div>

        <!-- 偏好设置 -->
        <div v-show="activeTab === 'preferences'" class="tab-content">
          <div class="section-header">
            <h3>偏好设置</h3>
            <p>自定义您的使用偏好</p>
          </div>

          <div class="preferences-section">
            <div class="preference-item">
              <div class="item-info">
                <div class="item-title">主题模式</div>
                <div class="item-desc">选择您喜欢的界面主题</div>
              </div>
              <el-radio-group v-model="preferences.theme" @change="updatePreference">
                <el-radio label="light">浅色</el-radio>
                <el-radio label="dark">深色</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </div>

            <div class="preference-item">
              <div class="item-info">
                <div class="item-title">语言</div>
                <div class="item-desc">选择界面显示语言</div>
              </div>
              <el-select v-model="preferences.language" @change="updatePreference">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </div>

            <div class="preference-item">
              <div class="item-info">
                <div class="item-title">时区</div>
                <div class="item-desc">设置您所在的时区</div>
              </div>
              <el-select v-model="preferences.timezone" @change="updatePreference">
                <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
              </el-select>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div v-show="activeTab === 'notifications'" class="tab-content">
          <div class="section-header">
            <h3>通知设置</h3>
            <p>管理您接收通知的方式和类型</p>
          </div>

          <div class="notifications-section">
            <div class="notification-item">
              <div class="item-info">
                <div class="item-title">系统通知</div>
                <div class="item-desc">接收系统重要通知</div>
              </div>
              <el-switch v-model="notifications.system" @change="updateNotificationSetting" />
            </div>

            <div class="notification-item">
              <div class="item-info">
                <div class="item-title">应用状态通知</div>
                <div class="item-desc">应用部署、启动、停止等状态变化通知</div>
              </div>
              <el-switch v-model="notifications.appStatus" @change="updateNotificationSetting" />
            </div>

            <div class="notification-item">
              <div class="item-info">
                <div class="item-title">告警通知</div>
                <div class="item-desc">系统监控告警通知</div>
              </div>
              <el-switch v-model="notifications.alerts" @change="updateNotificationSetting" />
            </div>

            <div class="notification-item">
              <div class="item-info">
                <div class="item-title">邮件通知</div>
                <div class="item-desc">通过邮件接收通知</div>
              </div>
              <el-switch v-model="notifications.email" @change="updateNotificationSetting" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User,
  UserFilled,
  Lock,
  Setting,
  Bell
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('basic')
const saving = ref(false)
const basicFormRef = ref()
const passwordFormRef = ref()

// 基本信息表单
const basicForm = reactive({
  username: 'admin',
  realName: '管理员',
  email: '<EMAIL>',
  phone: '13800138000',
  department: '技术部',
  position: '系统管理员',
  avatar: ''
})

// 基本信息验证规则
const basicRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 安全设置
const securitySettings = reactive({
  smsAuth: false,
  emailAuth: true
})

// 偏好设置
const preferences = reactive({
  theme: 'light',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai'
})

// 通知设置
const notifications = reactive({
  system: true,
  appStatus: true,
  alerts: true,
  email: false
})

/**
 * 切换标签页
 */
const handleTabChange = (key: string) => {
  activeTab.value = key
}

/**
 * 更换头像
 */
const changeAvatar = () => {
  ElMessage.info('更换头像功能开发中...')
}

/**
 * 保存基本信息
 */
const saveBasicInfo = async () => {
  try {
    await basicFormRef.value?.validate()
    saving.value = true
    
    // TODO: 调用API保存基本信息
    // await userApi.updateProfile(basicForm)
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('基本信息保存成功')
  } catch (error) {
    console.error('保存基本信息失败:', error)
  } finally {
    saving.value = false
  }
}

/**
 * 修改密码
 */
const changePassword = async () => {
  try {
    await passwordFormRef.value?.validate()
    saving.value = true
    
    // TODO: 调用API修改密码
    // await userApi.changePassword(passwordForm)
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 清空表单
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    ElMessage.success('密码修改成功')
  } catch (error) {
    console.error('修改密码失败:', error)
  } finally {
    saving.value = false
  }
}

/**
 * 更新安全设置
 */
const updateSecuritySetting = () => {
  // TODO: 调用API更新安全设置
  ElMessage.success('安全设置已更新')
}

/**
 * 更新偏好设置
 */
const updatePreference = () => {
  // TODO: 调用API更新偏好设置
  ElMessage.success('偏好设置已更新')
}

/**
 * 更新通知设置
 */
const updateNotificationSetting = () => {
  // TODO: 调用API更新通知设置
  ElMessage.success('通知设置已更新')
}

// 组件挂载时加载用户信息
onMounted(() => {
  // TODO: 加载用户信息
})
</script>

<style lang="scss" scoped>
.profile-view-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

.profile-content {
  display: grid;
  grid-template-columns: 240px 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.profile-sidebar {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .el-menu {
    border: none;
  }
}

.profile-main {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .tab-content {
    padding: 24px;
  }

  .section-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .profile-form {
    max-width: 500px;

    .avatar-section {
      margin-bottom: 24px;

      .avatar-container {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }

  .security-section {
    margin-bottom: 32px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .security-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #f5f7fa;

      &:last-child {
        border-bottom: none;
      }

      .item-info {
        .item-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .item-desc {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  .preferences-section {
    .preference-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid #f5f7fa;

      &:last-child {
        border-bottom: none;
      }

      .item-info {
        .item-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .item-desc {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  .notifications-section {
    .notification-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid #f5f7fa;

      &:last-child {
        border-bottom: none;
      }

      .item-info {
        .item-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .item-desc {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}
</style>
