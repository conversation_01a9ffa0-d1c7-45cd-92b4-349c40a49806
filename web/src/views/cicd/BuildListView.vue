<template>
  <div class="build-list-container">
    <!-- 页面头部 -->
    <PageHeader
      title="构建历史"
      description="查看所有构建任务的执行历史和状态"
      icon="Tools"
    />
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-select
          v-model="filterPipeline"
          placeholder="选择流水线"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部流水线" value="" />
          <el-option
            v-for="pipeline in pipelineOptions"
            :key="pipeline.id"
            :label="pipeline.name"
            :value="pipeline.id"
          />
        </el-select>
        
        <el-select
          v-model="filterStatus"
          placeholder="构建状态"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="运行中" value="running" />
          <el-option label="成功" value="success" />
          <el-option label="失败" value="failed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
        
        <el-input
          v-model="filterBranch"
          placeholder="分支名称"
          clearable
          class="filter-input"
          @input="handleSearch"
        />
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 构建列表 -->
    <LoadingCard :loading="loading">
      <el-table :data="buildList" class="build-table">
        <el-table-column label="构建编号" width="100">
          <template #default="{ row }">
            <el-button 
              type="text" 
              @click="viewBuild(row.id)"
              class="build-number"
            >
              #{{ row.number }}
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column label="流水线" min-width="150">
          <template #default="{ row }">
            <div class="pipeline-info">
              <div class="pipeline-name">{{ row.pipelineName }}</div>
              <div class="pipeline-branch">
                <el-icon><Branch /></el-icon>
                {{ row.branch }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="提交信息" min-width="200">
          <template #default="{ row }">
            <div class="commit-info">
              <div class="commit-message">{{ row.commit?.message || '无提交信息' }}</div>
              <div class="commit-details">
                <span class="commit-hash">{{ row.commit?.hash?.substring(0, 8) }}</span>
                <span class="commit-author">by {{ row.commit?.author }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <StatusTag :status="row.status" type="build" />
          </template>
        </el-table-column>
        
        <el-table-column label="持续时间" width="100">
          <template #default="{ row }">
            <span v-if="row.duration" class="duration">
              {{ formatDuration(row.duration) }}
            </span>
            <span v-else-if="row.status === 'running'" class="duration running">
              {{ getRunningDuration(row.startedAt) }}
            </span>
            <span v-else class="text-placeholder">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="开始时间" width="150">
          <template #default="{ row }">
            <span v-if="row.startedAt">{{ formatTime(row.startedAt) }}</span>
            <span v-else class="text-placeholder">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                @click="viewBuildLogs(row.id)"
              >
                日志
              </el-button>
              
              <el-dropdown @command="(cmd) => handleBuildAction(cmd, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      command="retry"
                      :disabled="row.status === 'running'"
                    >
                      <el-icon><RefreshRight /></el-icon>重试
                    </el-dropdown-item>
                    <el-dropdown-item 
                      command="cancel"
                      :disabled="row.status !== 'running'"
                    >
                      <el-icon><CircleClose /></el-icon>取消
                    </el-dropdown-item>
                    <el-dropdown-item command="artifacts">
                      <el-icon><FolderOpened /></el-icon>产物
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </LoadingCard>
    
    <!-- 构建日志对话框 -->
    <el-dialog
      v-model="logsDialogVisible"
      :title="`构建 #${currentBuild?.number} 日志`"
      width="80%"
      top="5vh"
    >
      <div class="logs-container">
        <div class="logs-header">
          <el-button size="small" @click="refreshBuildLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" @click="downloadBuildLogs">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
        
        <div class="logs-content">
          <pre class="logs-text">{{ buildLogs || '暂无日志数据' }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { cicdApi } from '@/api/cicd'
import type { Build, Pipeline } from '@/types'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import LoadingCard from '@/components/common/LoadingCard.vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 响应式数据
const router = useRouter()
const loading = ref(false)
const filterPipeline = ref('')
const filterStatus = ref('')
const filterBranch = ref('')
const buildList = ref<Build[]>([])
const pipelineOptions = ref<Pipeline[]>([])
const logsDialogVisible = ref(false)
const currentBuild = ref<Build | null>(null)
const buildLogs = ref('')

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const queryParams = computed(() => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  pipelineId: filterPipeline.value,
  status: filterStatus.value,
  branch: filterBranch.value
}))

// 方法定义
const loadBuildList = async () => {
  try {
    loading.value = true
    const response = await cicdApi.getBuilds(queryParams.value)
    buildList.value = response.data.items
    pagination.total = response.data.pagination.total
  } catch (error) {
    console.error('加载构建列表失败:', error)
    ElMessage.error('加载构建列表失败')
  } finally {
    loading.value = false
  }
}

const loadPipelineOptions = async () => {
  try {
    const response = await cicdApi.getPipelines({ pageSize: 1000 })
    pipelineOptions.value = response.data.items
  } catch (error) {
    console.error('加载流水线选项失败:', error)
  }
}

const handleSearch = debounce(() => {
  pagination.page = 1
  loadBuildList()
}, 300)

const handleFilter = () => {
  pagination.page = 1
  loadBuildList()
}

const refreshList = () => {
  loadBuildList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadBuildList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadBuildList()
}

const viewBuild = (id: string) => {
  router.push(`/cicd/builds/${id}`)
}

const viewBuildLogs = async (buildId: string) => {
  try {
    const build = buildList.value.find(b => b.id === buildId)
    if (!build) return
    
    currentBuild.value = build
    logsDialogVisible.value = true
    
    const response = await cicdApi.getBuildLogs(buildId)
    buildLogs.value = response.data.logs
  } catch (error) {
    console.error('加载构建日志失败:', error)
    ElMessage.error('加载构建日志失败')
  }
}

const refreshBuildLogs = () => {
  if (currentBuild.value) {
    viewBuildLogs(currentBuild.value.id)
  }
}

const downloadBuildLogs = () => {
  if (!buildLogs.value || !currentBuild.value) return
  
  const blob = new Blob([buildLogs.value], { type: 'text/plain' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `build-${currentBuild.value.number}-logs.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

const handleBuildAction = async (command: string, build: Build) => {
  switch (command) {
    case 'retry':
      await retryBuild(build)
      break
    case 'cancel':
      await cancelBuild(build)
      break
    case 'artifacts':
      await viewArtifacts(build)
      break
  }
}

const retryBuild = async (build: Build) => {
  try {
    await cicdApi.retryBuild(build.id)
    ElMessage.success('构建重试成功')
    await loadBuildList()
  } catch (error) {
    console.error('重试构建失败:', error)
  }
}

const cancelBuild = async (build: Build) => {
  try {
    await ElMessageBox.confirm('确定要取消这个构建吗？', '确认取消', {
      type: 'warning'
    })
    
    await cicdApi.cancelBuild(build.id)
    ElMessage.success('构建已取消')
    await loadBuildList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消构建失败:', error)
    }
  }
}

const viewArtifacts = async (build: Build) => {
  try {
    const response = await cicdApi.getBuildArtifacts(build.id)
    if (response.data.length === 0) {
      ElMessage.info('该构建没有产物')
      return
    }
    
    // TODO: 显示产物列表
    ElMessage.info('产物查看功能开发中...')
  } catch (error) {
    console.error('获取构建产物失败:', error)
  }
}

// 工具函数
const formatTime = (time?: string) => {
  return time ? dayjs(time).format('MM-DD HH:mm') : '-'
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const getRunningDuration = (startTime?: string) => {
  if (!startTime) return '-'
  const start = dayjs(startTime)
  const now = dayjs()
  const duration = now.diff(start, 'second')
  return formatDuration(duration)
}

function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadPipelineOptions()
  loadBuildList()
})
</script>

<style lang="scss" scoped>
// 导入混合宏以使用 card-style
@use '@/styles/mixins' as mixins;

.build-list-container {
  .filter-section {
    @include mixins.card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      
      .filter-select {
        width: 150px;
      }
      
      .filter-input {
        width: 200px;
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      
      .filter-left {
        width: 100%;
        flex-wrap: wrap;
        
        .filter-select,
        .filter-input {
          width: 100%;
        }
      }
    }
  }
  
  .build-table {
    .build-number {
      font-weight: 600;
      color: var(--el-color-primary);
    }
    
    .pipeline-info {
      .pipeline-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }
      
      .pipeline-branch {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        
        .el-icon {
          font-size: 12px;
        }
      }
    }
    
    .commit-info {
      .commit-message {
        font-size: 14px;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
        @include mixins.text-ellipsis;
        max-width: 200px;
      }
      
      .commit-details {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        
        .commit-hash {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          background: var(--el-fill-color-light);
          padding: 2px 4px;
          border-radius: 2px;
          margin-right: 8px;
        }
      }
    }
    
    .duration {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      
      &.running {
        color: var(--el-color-warning);
        font-weight: 500;
      }
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  .logs-container {
    .logs-header {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .logs-content {
      background: #1e1e1e;
      border-radius: 4px;
      padding: 16px;
      max-height: 60vh;
      overflow-y: auto;
      
      .logs-text {
        color: #d4d4d4;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        margin: 0;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
</style>
