<template>
  <div class="app-create-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">创建应用</h2>
        <p class="page-description">配置您的应用基本信息、运行时环境和部署参数</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>
    
    <!-- 创建表单 -->
    <div class="form-container">
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
        class="create-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          
          <el-form-item label="应用名称" prop="name">
            <el-input
              v-model="createForm.name"
              placeholder="请输入应用名称（英文、数字、连字符）"
              maxlength="50"
              show-word-limit
            />
            <div class="form-tip">
              应用名称将作为唯一标识，创建后不可修改
            </div>
          </el-form-item>
          
          <el-form-item label="显示名称" prop="displayName">
            <el-input
              v-model="createForm.displayName"
              placeholder="请输入应用显示名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="应用描述" prop="description">
            <el-input
              v-model="createForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入应用描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>
        
        <!-- 运行时配置 -->
        <div class="form-section">
          <h3 class="section-title">运行时配置</h3>
          
          <el-form-item label="编程语言" prop="runtime.language">
            <el-select
              v-model="createForm.runtime.language"
              placeholder="选择编程语言"
              @change="handleLanguageChange"
            >
              <el-option
                v-for="lang in supportedLanguages"
                :key="lang.value"
                :label="lang.label"
                :value="lang.value"
              >
                <div class="language-option">
                  <span class="language-name">{{ lang.label }}</span>
                  <span class="language-desc">{{ lang.description }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="语言版本" prop="runtime.version">
            <el-select
              v-model="createForm.runtime.version"
              placeholder="选择版本"
              :disabled="!createForm.runtime.language"
            >
              <el-option
                v-for="version in availableVersions"
                :key="version"
                :label="version"
                :value="version"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="框架" prop="runtime.framework">
            <el-select
              v-model="createForm.runtime.framework"
              placeholder="选择框架（可选）"
              clearable
              :disabled="!createForm.runtime.language"
            >
              <el-option
                v-for="framework in availableFrameworks"
                :key="framework"
                :label="framework"
                :value="framework"
              />
            </el-select>
          </el-form-item>
        </div>
        
        <!-- 代码仓库配置 -->
        <div class="form-section">
          <h3 class="section-title">
            代码仓库
            <el-switch
              v-model="enableRepository"
              class="section-switch"
              active-text="启用"
              inactive-text="禁用"
            />
          </h3>
          
          <template v-if="enableRepository">
            <el-form-item label="仓库地址" prop="repository.url">
              <el-input
                v-model="createForm.repository.url"
                placeholder="https://github.com/user/repo.git"
              />
            </el-form-item>
            
            <el-form-item label="分支" prop="repository.branch">
              <el-input
                v-model="createForm.repository.branch"
                placeholder="main"
              />
            </el-form-item>
            
            <el-form-item label="仓库类型" prop="repository.type">
              <el-radio-group v-model="createForm.repository.type">
                <el-radio label="gitea">Gitea</el-radio>
                <el-radio label="github">GitHub</el-radio>
                <el-radio label="gitlab">GitLab</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
        </div>
        
        <!-- 部署配置 -->
        <div class="form-section">
          <h3 class="section-title">部署配置</h3>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="服务端口" prop="deployConfig.port">
                <el-input-number
                  v-model="createForm.deployConfig.port"
                  :min="1"
                  :max="65535"
                  placeholder="8080"
                  class="w-full"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实例数量" prop="deployConfig.instances">
                <el-input-number
                  v-model="createForm.deployConfig.instances"
                  :min="1"
                  :max="100"
                  placeholder="1"
                  class="w-full"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="CPU限制" prop="deployConfig.resources.cpu">
                <el-input
                  v-model="createForm.deployConfig.resources.cpu"
                  placeholder="500m"
                />
                <div class="form-tip">
                  例如：500m (0.5核心)、1000m (1核心)
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="内存限制" prop="deployConfig.resources.memory">
                <el-input
                  v-model="createForm.deployConfig.resources.memory"
                  placeholder="512Mi"
                />
                <div class="form-tip">
                  例如：512Mi、1Gi、2Gi
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="启动命令">
            <el-input
              v-model="startCommand"
              placeholder="npm start"
            />
            <div class="form-tip">
              留空将使用默认启动命令
            </div>
          </el-form-item>
          
          <el-form-item label="工作目录">
            <el-input
              v-model="createForm.deployConfig.workingDir"
              placeholder="/app"
            />
          </el-form-item>
        </div>
        
        <!-- 环境变量 -->
        <div class="form-section">
          <h3 class="section-title">
            环境变量
            <el-button type="text" @click="addEnvVar">
              <el-icon><Plus /></el-icon>
              添加
            </el-button>
          </h3>
          
          <div class="env-vars">
            <div
              v-for="(envVar, index) in envVars"
              :key="index"
              class="env-var-item"
            >
              <el-input
                v-model="envVar.key"
                placeholder="变量名"
                class="env-key"
              />
              <el-input
                v-model="envVar.value"
                placeholder="变量值"
                class="env-value"
              />
              <el-button
                type="danger"
                text
                @click="removeEnvVar(index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 健康检查 -->
        <div class="form-section">
          <h3 class="section-title">
            健康检查
            <el-switch
              v-model="enableHealthCheck"
              class="section-switch"
              active-text="启用"
              inactive-text="禁用"
            />
          </h3>
          
          <template v-if="enableHealthCheck">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="检查路径">
                  <el-input
                    v-model="createForm.deployConfig.healthCheck.path"
                    placeholder="/health"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查端口">
                  <el-input-number
                    v-model="createForm.deployConfig.healthCheck.port"
                    :min="1"
                    :max="65535"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="初始延迟">
                  <el-input-number
                    v-model="createForm.deployConfig.healthCheck.initialDelaySeconds"
                    :min="0"
                    :max="300"
                    class="w-full"
                  />
                  <div class="form-tip">秒</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检查间隔">
                  <el-input-number
                    v-model="createForm.deployConfig.healthCheck.periodSeconds"
                    :min="1"
                    :max="300"
                    class="w-full"
                  />
                  <div class="form-tip">秒</div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </div>
        
        <!-- 提交按钮 -->
        <div class="form-actions">
          <el-button @click="$router.back()">取消</el-button>
          <el-button 
            type="primary" 
            :loading="creating"
            @click="handleCreate"
          >
            {{ creating ? '创建中...' : '创建应用' }}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { appsApi } from '@/api/apps'
import type { CreateAppRequest } from '@/api/apps'
import type { FormInstance, FormRules } from 'element-plus'

// 响应式数据
const router = useRouter()
const createFormRef = ref<FormInstance>()
const creating = ref(false)
const enableRepository = ref(false)
const enableHealthCheck = ref(true)

// 表单数据
const createForm = reactive<CreateAppRequest>({
  name: '',
  displayName: '',
  description: '',
  runtime: {
    language: '',
    version: '',
    framework: ''
  },
  repository: {
    url: '',
    branch: 'main',
    type: 'gitea'
  },
  deployConfig: {
    port: 8080,
    instances: 1,
    resources: {
      cpu: '500m',
      memory: '512Mi'
    },
    workingDir: '/app',
    healthCheck: {
      path: '/health',
      port: 8080,
      initialDelaySeconds: 30,
      periodSeconds: 10
    }
  }
})

// 环境变量
const envVars = ref<Array<{ key: string; value: string }>>([])
const startCommand = ref('')

// 支持的编程语言
const supportedLanguages = ref([
  {
    value: 'nodejs',
    label: 'Node.js',
    description: '基于V8引擎的JavaScript运行时'
  },
  {
    value: 'python',
    label: 'Python',
    description: '简洁优雅的编程语言'
  },
  {
    value: 'go',
    label: 'Go',
    description: '高性能的编译型语言'
  },
  {
    value: 'java',
    label: 'Java',
    description: '企业级应用开发语言'
  }
])

// 可用版本
const availableVersions = computed(() => {
  const versionMap: Record<string, string[]> = {
    nodejs: ['16', '18', '20'],
    python: ['3.8', '3.9', '3.10', '3.11'],
    go: ['1.19', '1.20', '1.21'],
    java: ['8', '11', '17', '21']
  }
  return versionMap[createForm.runtime.language] || []
})

// 可用框架
const availableFrameworks = computed(() => {
  const frameworkMap: Record<string, string[]> = {
    nodejs: ['express', 'koa', 'fastify', 'nest'],
    python: ['flask', 'django', 'fastapi', 'tornado'],
    go: ['gin', 'echo', 'fiber'],
    java: ['spring-boot', 'quarkus', 'micronaut']
  }
  return frameworkMap[createForm.runtime.language] || []
})

// 表单验证规则
const createRules: FormRules = {
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { 
      pattern: /^[a-z0-9-]+$/, 
      message: '应用名称只能包含小写字母、数字和连字符', 
      trigger: 'blur' 
    },
    { min: 3, max: 50, message: '应用名称长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { max: 100, message: '显示名称不能超过 100 个字符', trigger: 'blur' }
  ],
  'runtime.language': [
    { required: true, message: '请选择编程语言', trigger: 'change' }
  ],
  'runtime.version': [
    { required: true, message: '请选择语言版本', trigger: 'change' }
  ],
  'deployConfig.port': [
    { required: true, message: '请输入服务端口', trigger: 'blur' }
  ],
  'deployConfig.instances': [
    { required: true, message: '请输入实例数量', trigger: 'blur' }
  ],
  'deployConfig.resources.cpu': [
    { required: true, message: '请输入CPU限制', trigger: 'blur' },
    { 
      pattern: /^\d+m$|^\d+(\.\d+)?$/, 
      message: 'CPU格式错误，例如：500m 或 0.5', 
      trigger: 'blur' 
    }
  ],
  'deployConfig.resources.memory': [
    { required: true, message: '请输入内存限制', trigger: 'blur' },
    { 
      pattern: /^\d+(Mi|Gi)$/, 
      message: '内存格式错误，例如：512Mi 或 1Gi', 
      trigger: 'blur' 
    }
  ]
}

// 方法定义
const handleLanguageChange = () => {
  // 重置版本和框架选择
  createForm.runtime.version = ''
  createForm.runtime.framework = ''
  
  // 设置默认端口
  const defaultPorts: Record<string, number> = {
    nodejs: 3000,
    python: 5000,
    go: 8080,
    java: 8080
  }
  createForm.deployConfig.port = defaultPorts[createForm.runtime.language] || 8080
  
  // 设置健康检查端口
  if (createForm.deployConfig.healthCheck) {
    createForm.deployConfig.healthCheck.port = createForm.deployConfig.port
  }
}

const addEnvVar = () => {
  envVars.value.push({ key: '', value: '' })
}

const removeEnvVar = (index: number) => {
  envVars.value.splice(index, 1)
}

const handleCreate = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    creating.value = true
    
    // 处理环境变量
    const environment: Record<string, string> = {}
    envVars.value.forEach(envVar => {
      if (envVar.key && envVar.value) {
        environment[envVar.key] = envVar.value
      }
    })
    createForm.deployConfig.environment = environment
    
    // 处理启动命令
    if (startCommand.value) {
      createForm.deployConfig.command = startCommand.value.split(' ')
    }
    
    // 处理可选配置
    if (!enableRepository.value) {
      delete createForm.repository
    }
    
    if (!enableHealthCheck.value) {
      delete createForm.deployConfig.healthCheck
    }
    
    // 创建应用
    await appsApi.createApp(createForm)
    
    ElMessage.success('应用创建成功')
    router.push('/apps')
    
  } catch (error) {
    console.error('创建应用失败:', error)
  } finally {
    creating.value = false
  }
}

// 监听端口变化，同步健康检查端口
watch(
  () => createForm.deployConfig.port,
  (newPort) => {
    if (createForm.deployConfig.healthCheck) {
      createForm.deployConfig.healthCheck.port = newPort
    }
  }
)
</script>

<style lang="scss" scoped>
// 导入混合宏以使用样式函数
@use '@/styles/mixins' as mixins;

.app-create-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      .page-description {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .form-container {
    @include mixins.card-style;
    
    .create-form {
      max-width: 800px;
      
      .form-section {
        margin-bottom: 32px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0 0 20px 0;
          padding-bottom: 8px;
          border-bottom: 1px solid var(--el-border-color-extra-light);
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .section-switch {
            margin-left: auto;
          }
        }
        
        .form-tip {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-top: 4px;
        }
        
        .language-option {
          display: flex;
          flex-direction: column;
          
          .language-name {
            font-weight: 500;
          }
          
          .language-desc {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
        
        .env-vars {
          .env-var-item {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
            align-items: center;
            
            .env-key {
              width: 200px;
            }
            
            .env-value {
              flex: 1;
            }
          }
        }
      }
      
      .form-actions {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid var(--el-border-color-extra-light);
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-create-container {
    .form-container {
      .create-form {
        .env-vars {
          .env-var-item {
            flex-direction: column;
            
            .env-key,
            .env-value {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
