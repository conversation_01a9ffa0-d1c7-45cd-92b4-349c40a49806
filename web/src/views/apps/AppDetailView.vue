<template>
  <div class="app-detail-container">
    <LoadingCard :loading="loading">
      <!-- 页面头部 -->
      <PageHeader
        :title="app?.displayName || app?.name || '应用详情'"
        :description="app?.description"
        icon="Box"
        show-back
        :tabs="tabs"
        :active-tab="activeTab"
        :stats="appStats"
        @tab-change="handleTabChange"
      >
        <template #actions>
          <el-button-group>
            <el-button 
              type="primary"
              :disabled="app?.status !== 'stopped'"
              @click="startApp"
            >
              <el-icon><VideoPlay /></el-icon>
              启动
            </el-button>
            <el-button 
              :disabled="app?.status === 'stopped'"
              @click="stopApp"
            >
              <el-icon><VideoPause /></el-icon>
              停止
            </el-button>
            <el-button @click="restartApp">
              <el-icon><Refresh /></el-icon>
              重启
            </el-button>
          </el-button-group>
          
          <el-dropdown @command="handleMoreAction">
            <el-button>
              更多操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="deploy">
                  <el-icon><Upload /></el-icon>重新部署
                </el-dropdown-item>
                <el-dropdown-item command="scale">
                  <el-icon><Rank /></el-icon>扩缩容
                </el-dropdown-item>
                <el-dropdown-item command="edit">
                  <el-icon><Edit /></el-icon>编辑配置
                </el-dropdown-item>
                <el-dropdown-item divided command="delete" class="danger-item">
                  <el-icon><Delete /></el-icon>删除应用
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </PageHeader>
      
      <!-- 标签页内容 -->
      <div class="tab-content">
        <!-- 概览标签页 -->
        <div v-show="activeTab === 'overview'" class="tab-pane">
          <div class="overview-grid">
            <!-- 基本信息 -->
            <div class="info-card">
              <h3 class="card-title">基本信息</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">应用ID:</span>
                  <span class="info-value">{{ app?.id }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">运行时:</span>
                  <el-tag>{{ getRuntimeLabel(app?.runtime) }}</el-tag>
                </div>
                <div class="info-item">
                  <span class="info-label">状态:</span>
                  <StatusTag :status="app?.status || ''" type="app" />
                </div>
                <div class="info-item">
                  <span class="info-label">创建时间:</span>
                  <span class="info-value">{{ formatTime(app?.createdAt) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">最后部署:</span>
                  <span class="info-value">
                    {{ app?.lastDeployedAt ? formatTime(app.lastDeployedAt) : '未部署' }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- 资源配置 -->
            <div class="info-card">
              <h3 class="card-title">资源配置</h3>
              <div class="resource-grid">
                <div class="resource-item">
                  <div class="resource-icon cpu">
                    <el-icon><Cpu /></el-icon>
                  </div>
                  <div class="resource-info">
                    <div class="resource-label">CPU</div>
                    <div class="resource-value">{{ app?.resources?.cpu || 'N/A' }}</div>
                  </div>
                </div>
                
                <div class="resource-item">
                  <div class="resource-icon memory">
                    <el-icon><MemoryCard /></el-icon>
                  </div>
                  <div class="resource-info">
                    <div class="resource-label">内存</div>
                    <div class="resource-value">{{ app?.resources?.memory || 'N/A' }}</div>
                  </div>
                </div>
                
                <div class="resource-item">
                  <div class="resource-icon storage">
                    <el-icon><FolderOpened /></el-icon>
                  </div>
                  <div class="resource-info">
                    <div class="resource-label">存储</div>
                    <div class="resource-value">{{ app?.resources?.storage || 'N/A' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 实例列表 -->
          <div class="instances-section">
            <div class="section-header">
              <h3>应用实例</h3>
              <el-button @click="refreshInstances">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            
            <el-table :data="instances" class="instances-table">
              <el-table-column label="实例ID" prop="id" width="200" />
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <StatusTag :status="row.status" type="general" />
                </template>
              </el-table-column>
              <el-table-column label="容器ID" prop="containerId" width="150">
                <template #default="{ row }">
                  <span v-if="row.containerId" class="container-id">
                    {{ row.containerId.substring(0, 12) }}
                  </span>
                  <span v-else class="text-placeholder">-</span>
                </template>
              </el-table-column>
              <el-table-column label="端口" prop="port" width="80" />
              <el-table-column label="CPU使用" width="100">
                <template #default="{ row }">
                  <div class="usage-info">
                    <span>{{ row.cpu || 0 }}%</span>
                    <el-progress 
                      :percentage="row.cpu || 0" 
                      :show-text="false" 
                      :stroke-width="4"
                      class="usage-bar"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="内存使用" width="100">
                <template #default="{ row }">
                  <div class="usage-info">
                    <span>{{ row.memory || 0 }}%</span>
                    <el-progress 
                      :percentage="row.memory || 0" 
                      :show-text="false" 
                      :stroke-width="4"
                      class="usage-bar"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="启动时间" width="150">
                <template #default="{ row }">
                  <span v-if="row.startedAt">{{ formatTime(row.startedAt) }}</span>
                  <span v-else class="text-placeholder">-</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button 
                    size="small" 
                    type="danger" 
                    text
                    @click="restartInstance(row)"
                  >
                    重启
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        
        <!-- 监控标签页 -->
        <div v-show="activeTab === 'monitoring'" class="tab-pane">
          <div class="monitoring-grid">
            <!-- CPU使用率图表 -->
            <div class="chart-card">
              <h3 class="card-title">CPU 使用率</h3>
              <div class="chart-container">
                <v-chart class="chart" :option="cpuChartOption" autoresize />
              </div>
            </div>
            
            <!-- 内存使用率图表 -->
            <div class="chart-card">
              <h3 class="card-title">内存使用率</h3>
              <div class="chart-container">
                <v-chart class="chart" :option="memoryChartOption" autoresize />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 日志标签页 -->
        <div v-show="activeTab === 'logs'" class="tab-pane">
          <div class="logs-section">
            <div class="logs-header">
              <div class="logs-controls">
                <el-select v-model="logLevel" size="small">
                  <el-option label="全部级别" value="" />
                  <el-option label="ERROR" value="error" />
                  <el-option label="WARN" value="warn" />
                  <el-option label="INFO" value="info" />
                  <el-option label="DEBUG" value="debug" />
                </el-select>
                
                <el-input-number
                  v-model="logLines"
                  :min="100"
                  :max="10000"
                  :step="100"
                  size="small"
                  controls-position="right"
                />
                
                <el-button size="small" @click="refreshLogs">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                
                <el-button size="small" @click="downloadLogs">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
              </div>
              
              <el-switch
                v-model="autoRefreshLogs"
                active-text="自动刷新"
                inactive-text="手动刷新"
              />
            </div>
            
            <div class="logs-content">
              <pre class="logs-text">{{ appLogs || '暂无日志数据' }}</pre>
            </div>
          </div>
        </div>
        
        <!-- 配置标签页 -->
        <div v-show="activeTab === 'config'" class="tab-pane">
          <div class="config-section">
            <h3 class="section-title">环境变量</h3>
            <el-table :data="envVarsList" class="config-table">
              <el-table-column label="变量名" prop="key" />
              <el-table-column label="变量值" prop="value">
                <template #default="{ row }">
                  <span v-if="row.isSecret" class="secret-value">******</span>
                  <span v-else>{{ row.value }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" width="80">
                <template #default="{ row }">
                  <el-tag v-if="row.isSecret" type="warning" size="small">密钥</el-tag>
                  <el-tag v-else type="info" size="small">普通</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        
        <!-- 事件标签页 -->
        <div v-show="activeTab === 'events'" class="tab-pane">
          <div class="events-section">
            <div class="events-header">
              <h3>应用事件</h3>
              <el-button @click="refreshEvents">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            
            <el-timeline class="events-timeline">
              <el-timeline-item
                v-for="event in appEvents"
                :key="event.id"
                :timestamp="formatTime(event.timestamp)"
                :type="getEventType(event.type)"
              >
                <div class="event-content">
                  <div class="event-title">{{ event.title }}</div>
                  <div class="event-description">{{ event.description }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </LoadingCard>
    
    <!-- 扩缩容对话框 -->
    <el-dialog
      v-model="scaleDialogVisible"
      title="应用扩缩容"
      width="400px"
    >
      <el-form label-width="80px">
        <el-form-item label="实例数量">
          <el-input-number
            v-model="scaleInstances"
            :min="1"
            :max="100"
            class="w-full"
          />
          <div class="form-tip">
            当前实例数：{{ app?.instances?.length || 0 }}
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="scaleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleScale">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { appsApi } from '@/api/apps'
import type { Application, ApplicationInstance } from '@/types'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import LoadingCard from '@/components/common/LoadingCard.vue'
import dayjs from 'dayjs'

// 响应式数据
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const app = ref<Application | null>(null)
const instances = ref<ApplicationInstance[]>([])
const activeTab = ref('overview')
const scaleDialogVisible = ref(false)
const scaleInstances = ref(1)
const appLogs = ref('')
const appEvents = ref<any[]>([])
const logLevel = ref('')
const logLines = ref(1000)
const autoRefreshLogs = ref(false)

// 标签页配置
const tabs = [
  { name: 'overview', label: '概览' },
  { name: 'monitoring', label: '监控' },
  { name: 'logs', label: '日志' },
  { name: 'config', label: '配置' },
  { name: 'events', label: '事件' }
]

// 应用统计信息
const appStats = computed(() => {
  if (!app.value) return []
  
  return [
    {
      key: 'instances',
      label: '实例数量',
      value: app.value.instances?.length || 0
    },
    {
      key: 'running',
      label: '运行实例',
      value: instances.value.filter(i => i.status === 'running').length
    },
    {
      key: 'cpu',
      label: 'CPU配额',
      value: app.value.resources?.cpu || 'N/A'
    },
    {
      key: 'memory',
      label: '内存配额',
      value: app.value.resources?.memory || 'N/A'
    }
  ]
})

// 环境变量列表
const envVarsList = computed(() => {
  if (!app.value?.environment) return []
  
  return Object.entries(app.value.environment).map(([key, value]) => ({
    key,
    value,
    isSecret: key.toLowerCase().includes('password') || 
              key.toLowerCase().includes('secret') ||
              key.toLowerCase().includes('token')
  }))
})

// 图表配置
const cpuChartOption = computed(() => ({
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
  },
  yAxis: {
    type: 'value',
    max: 100,
    axisLabel: { formatter: '{value}%' }
  },
  series: [{
    name: 'CPU使用率',
    type: 'line',
    data: [20, 25, 30, 45, 35, 40],
    smooth: true,
    areaStyle: { opacity: 0.3 }
  }]
}))

const memoryChartOption = computed(() => ({
  tooltip: { trigger: 'axis' },
  xAxis: {
    type: 'category',
    data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
  },
  yAxis: {
    type: 'value',
    max: 100,
    axisLabel: { formatter: '{value}%' }
  },
  series: [{
    name: '内存使用率',
    type: 'line',
    data: [30, 35, 40, 55, 45, 50],
    smooth: true,
    areaStyle: { opacity: 0.3 }
  }]
}))

// 方法定义
const loadAppDetail = async () => {
  const appId = route.params.id as string
  if (!appId) return
  
  try {
    loading.value = true
    const response = await appsApi.getApp(appId)
    app.value = response.data
    
    // 加载实例信息
    await loadInstances()
  } catch (error) {
    console.error('加载应用详情失败:', error)
    ElMessage.error('加载应用详情失败')
  } finally {
    loading.value = false
  }
}

const loadInstances = async () => {
  if (!app.value) return
  
  try {
    const response = await appsApi.getAppInstances(app.value.id)
    instances.value = response.data
  } catch (error) {
    console.error('加载实例信息失败:', error)
  }
}

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  
  // 根据标签页加载对应数据
  switch (tabName) {
    case 'logs':
      loadLogs()
      break
    case 'events':
      loadEvents()
      break
  }
}

const startApp = async () => {
  if (!app.value) return
  
  try {
    await appsApi.startApp(app.value.id)
    ElMessage.success('应用启动成功')
    await loadAppDetail()
  } catch (error) {
    console.error('启动应用失败:', error)
  }
}

const stopApp = async () => {
  if (!app.value) return
  
  try {
    await ElMessageBox.confirm('确定要停止应用吗？', '确认停止', {
      type: 'warning'
    })
    
    await appsApi.stopApp(app.value.id)
    ElMessage.success('应用已停止')
    await loadAppDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止应用失败:', error)
    }
  }
}

const restartApp = async () => {
  if (!app.value) return
  
  try {
    await appsApi.restartApp(app.value.id)
    ElMessage.success('应用重启成功')
    await loadAppDetail()
  } catch (error) {
    console.error('重启应用失败:', error)
  }
}

const handleMoreAction = (command: string) => {
  switch (command) {
    case 'deploy':
      deployApp()
      break
    case 'scale':
      showScaleDialog()
      break
    case 'edit':
      // TODO: 实现编辑功能
      ElMessage.info('编辑功能开发中...')
      break
    case 'delete':
      deleteApp()
      break
  }
}

const deployApp = async () => {
  if (!app.value) return
  
  try {
    await appsApi.deployApp(app.value.id)
    ElMessage.success('应用部署已开始')
    await loadAppDetail()
  } catch (error) {
    console.error('部署应用失败:', error)
  }
}

const showScaleDialog = () => {
  scaleInstances.value = app.value?.instances?.length || 1
  scaleDialogVisible.value = true
}

const handleScale = async () => {
  if (!app.value) return
  
  try {
    await appsApi.scaleApp(app.value.id, { instances: scaleInstances.value })
    ElMessage.success('扩缩容操作成功')
    scaleDialogVisible.value = false
    await loadAppDetail()
  } catch (error) {
    console.error('扩缩容失败:', error)
  }
}

const deleteApp = async () => {
  if (!app.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除应用 "${app.value.displayName}" 吗？此操作不可恢复！`,
      '确认删除',
      { type: 'error' }
    )
    
    await appsApi.deleteApp(app.value.id)
    ElMessage.success('应用已删除')
    router.push('/apps')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除应用失败:', error)
    }
  }
}

const restartInstance = async (instance: ApplicationInstance) => {
  try {
    await appsApi.instanceAction(app.value!.id, {
      action: 'restart',
      instanceIds: [instance.id]
    })
    ElMessage.success('实例重启成功')
    await loadInstances()
  } catch (error) {
    console.error('重启实例失败:', error)
  }
}

const refreshInstances = () => {
  loadInstances()
}

const loadLogs = async () => {
  if (!app.value) return
  
  try {
    const response = await appsApi.getAppLogs(app.value.id, {
      lines: logLines.value
    })
    appLogs.value = response.data.logs
  } catch (error) {
    console.error('加载日志失败:', error)
  }
}

const refreshLogs = () => {
  loadLogs()
}

const downloadLogs = () => {
  // TODO: 实现日志下载
  ElMessage.info('日志下载功能开发中...')
}

const loadEvents = async () => {
  if (!app.value) return
  
  try {
    const response = await appsApi.getAppEvents(app.value.id)
    appEvents.value = response.data
  } catch (error) {
    console.error('加载事件失败:', error)
  }
}

const refreshEvents = () => {
  loadEvents()
}

// 工具函数
const getRuntimeLabel = (runtime: any) => {
  if (!runtime) return 'N/A'
  return `${runtime.language} ${runtime.version}${runtime.framework ? ` (${runtime.framework})` : ''}`
}

const formatTime = (time?: string) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : 'N/A'
}

const getEventType = (type: string) => {
  const typeMap: Record<string, string> = {
    info: 'primary',
    success: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return typeMap[type] || 'primary'
}

// 组件挂载时加载数据
onMounted(() => {
  loadAppDetail()
})
</script>

<style lang="scss" scoped>
// 导入混合宏以使用样式函数
@use '@/styles/mixins' as mixins;

.app-detail-container {
  .tab-content {
    .tab-pane {
      @include mixins.fade-in;
    }
    
    .overview-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 24px;
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
      
      .info-card {
        @include mixins.card-style;
        
        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0 0 16px 0;
        }
        
        .info-list {
          .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--el-border-color-extra-light);
            
            &:last-child {
              border-bottom: none;
            }
            
            .info-label {
              font-size: 14px;
              color: var(--el-text-color-secondary);
            }
            
            .info-value {
              font-size: 14px;
              color: var(--el-text-color-primary);
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }
          }
        }
        
        .resource-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 16px;
          
          .resource-item {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .resource-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              
              .el-icon {
                font-size: 20px;
                color: white;
              }
              
              &.cpu {
                background: var(--el-color-primary);
              }
              
              &.memory {
                background: var(--el-color-success);
              }
              
              &.storage {
                background: var(--el-color-warning);
              }
            }
            
            .resource-info {
              .resource-label {
                font-size: 12px;
                color: var(--el-text-color-secondary);
                margin-bottom: 2px;
              }
              
              .resource-value {
                font-size: 14px;
                font-weight: 500;
                color: var(--el-text-color-primary);
              }
            }
          }
        }
      }
    }
    
    .instances-section,
    .monitoring-grid,
    .logs-section,
    .config-section,
    .events-section {
      @include mixins.card-style;
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }
      }
      
      .instances-table {
        .container-id {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
        
        .usage-info {
          .usage-bar {
            margin-top: 4px;
          }
        }
      }
    }
    
    .monitoring-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
      
      .chart-card {
        .card-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin: 0 0 16px 0;
        }
        
        .chart-container {
          height: 200px;
          
          .chart {
            height: 100%;
            width: 100%;
          }
        }
      }
    }
    
    .logs-section {
      .logs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .logs-controls {
          display: flex;
          gap: 12px;
          align-items: center;
        }
      }
      
      .logs-content {
        background: #1e1e1e;
        border-radius: 4px;
        padding: 16px;
        max-height: 500px;
        overflow-y: auto;
        
        .logs-text {
          color: #d4d4d4;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          line-height: 1.5;
          margin: 0;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
    
    .config-section {
      .config-table {
        .secret-value {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          color: var(--el-text-color-placeholder);
        }
      }
    }
    
    .events-section {
      .events-timeline {
        .event-content {
          .event-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }
          
          .event-description {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }
  }
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

:deep(.danger-item) {
  color: var(--el-color-danger);
}
</style>
