<template>
  <div class="logs-view-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          日志查看
        </h1>
        <p class="page-description">实时查看和搜索应用程序日志，支持多种过滤条件</p>
      </div>
      <div class="header-actions">
        <el-button @click="toggleAutoRefresh" :type="autoRefresh ? 'danger' : 'primary'">
          <el-icon><VideoPlay v-if="!autoRefresh" /><VideoPause v-else /></el-icon>
          {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
        </el-button>
        <el-button @click="clearLogs">
          <el-icon><Delete /></el-icon>
          清空日志
        </el-button>
      </div>
    </div>

    <!-- 过滤器 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="应用">
          <el-select v-model="filterForm.appId" placeholder="选择应用" clearable>
            <el-option
              v-for="app in appList"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日志级别">
          <el-select v-model="filterForm.level" placeholder="选择级别" clearable>
            <el-option label="DEBUG" value="debug" />
            <el-option label="INFO" value="info" />
            <el-option label="WARN" value="warn" />
            <el-option label="ERROR" value="error" />
            <el-option label="FATAL" value="fatal" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="filterForm.keyword"
            placeholder="搜索日志内容"
            clearable
            @keyup.enter="searchLogs"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchLogs">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 日志显示区域 -->
    <div class="logs-section">
      <div class="logs-header">
        <div class="logs-info">
          <span>共 {{ totalLogs }} 条日志</span>
          <span v-if="autoRefresh" class="auto-refresh-indicator">
            <el-icon class="rotating"><Loading /></el-icon>
            自动刷新中
          </span>
        </div>
        <div class="logs-actions">
          <el-button size="small" @click="exportLogs">
            <el-icon><Download /></el-icon>
            导出日志
          </el-button>
        </div>
      </div>
      
      <div class="logs-content" ref="logsContainer">
        <div
          v-for="log in logList"
          :key="log.id"
          class="log-item"
          :class="log.level"
        >
          <div class="log-time">{{ formatTime(log.timestamp) }}</div>
          <div class="log-level">
            <el-tag :type="getLevelTagType(log.level)" size="small">
              {{ log.level.toUpperCase() }}
            </el-tag>
          </div>
          <div class="log-app">{{ log.appName }}</div>
          <div class="log-message">{{ log.message }}</div>
          <div class="log-actions">
            <el-button type="text" size="small" @click="viewLogDetail(log)">
              详情
            </el-button>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <el-button @click="loadMoreLogs" :loading="loading">
            加载更多
          </el-button>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!loading && logList.length === 0" class="empty-state">
          <el-empty description="暂无日志数据" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  VideoPlay,
  VideoPause,
  Delete,
  Search,
  Loading,
  Download
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const refreshTimer = ref<NodeJS.Timeout | null>(null)
const logsContainer = ref<HTMLElement>()
const totalLogs = ref(0)
const hasMore = ref(true)

// 应用列表
const appList = ref([
  { id: '1', name: 'user-service' },
  { id: '2', name: 'order-service' },
  { id: '3', name: 'payment-service' },
  { id: '4', name: 'notification-service' }
])

// 过滤表单
const filterForm = reactive({
  appId: '',
  level: '',
  timeRange: [],
  keyword: ''
})

// 日志列表
const logList = ref([])

/**
 * 获取日志级别标签类型
 */
const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    debug: 'info',
    info: 'success',
    warn: 'warning',
    error: 'danger',
    fatal: 'danger'
  }
  return typeMap[level] || ''
}

/**
 * 格式化时间
 */
const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('MM-DD HH:mm:ss.SSS')
}

/**
 * 生成模拟日志数据
 */
const generateMockLogs = (count = 20) => {
  const levels = ['debug', 'info', 'warn', 'error', 'fatal']
  const messages = [
    '用户登录成功',
    '订单创建完成',
    '支付处理中',
    '数据库连接超时',
    '缓存更新失败',
    'API调用成功',
    '文件上传完成',
    '邮件发送失败',
    '定时任务执行',
    '系统启动完成'
  ]
  
  const logs = []
  for (let i = 0; i < count; i++) {
    const app = appList.value[Math.floor(Math.random() * appList.value.length)]
    logs.push({
      id: `log_${Date.now()}_${i}`,
      timestamp: new Date(Date.now() - Math.random() * 3600000).toISOString(),
      level: levels[Math.floor(Math.random() * levels.length)],
      appName: app.name,
      appId: app.id,
      message: messages[Math.floor(Math.random() * messages.length)] + ` - ${Math.random().toString(36).substr(2, 9)}`
    })
  }
  
  return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
}

/**
 * 加载日志数据
 */
const loadLogs = async (append = false) => {
  loading.value = true
  try {
    // TODO: 调用API获取日志数据
    // const response = await logsApi.getLogs({
    //   ...filterForm,
    //   page: append ? Math.ceil(logList.value.length / 20) + 1 : 1,
    //   size: 20
    // })
    
    // 模拟数据
    const mockLogs = generateMockLogs()
    
    if (append) {
      logList.value.push(...mockLogs)
    } else {
      logList.value = mockLogs
    }
    
    totalLogs.value = append ? totalLogs.value + mockLogs.length : mockLogs.length
    hasMore.value = logList.value.length < 100 // 模拟最多100条
    
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索日志
 */
const searchLogs = () => {
  loadLogs(false)
}

/**
 * 重置过滤条件
 */
const resetFilter = () => {
  Object.assign(filterForm, {
    appId: '',
    level: '',
    timeRange: [],
    keyword: ''
  })
  searchLogs()
}

/**
 * 加载更多日志
 */
const loadMoreLogs = () => {
  loadLogs(true)
}

/**
 * 切换自动刷新
 */
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    startAutoRefresh()
    ElMessage.success('已开启自动刷新')
  } else {
    stopAutoRefresh()
    ElMessage.info('已停止自动刷新')
  }
}

/**
 * 开始自动刷新
 */
const startAutoRefresh = () => {
  refreshTimer.value = setInterval(() => {
    loadLogs(false)
  }, 5000) // 5秒刷新一次
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

/**
 * 清空日志
 */
const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    logList.value = []
    totalLogs.value = 0
    ElMessage.success('日志已清空')
  } catch (error) {
    // 用户取消操作
  }
}

/**
 * 导出日志
 */
const exportLogs = () => {
  ElMessage.info('导出日志功能开发中...')
}

/**
 * 查看日志详情
 */
const viewLogDetail = (log: any) => {
  ElMessage.info(`查看日志详情: ${log.id}`)
}

// 组件挂载时加载数据
onMounted(() => {
  loadLogs()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.logs-view-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.filter-section {
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logs-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;

    .logs-info {
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 14px;
      color: #606266;

      .auto-refresh-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #409eff;

        .rotating {
          animation: rotate 1s linear infinite;
        }
      }
    }
  }

  .logs-content {
    max-height: 600px;
    overflow-y: auto;

    .log-item {
      display: grid;
      grid-template-columns: 120px 80px 120px 1fr 60px;
      gap: 12px;
      align-items: center;
      padding: 12px 20px;
      border-bottom: 1px solid #f5f7fa;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;

      &:hover {
        background-color: #f5f7fa;
      }

      &.error, &.fatal {
        background-color: #fef0f0;
        border-left: 3px solid #f56c6c;
      }

      &.warn {
        background-color: #fdf6ec;
        border-left: 3px solid #e6a23c;
      }

      .log-time {
        color: #909399;
        font-size: 12px;
      }

      .log-level {
        display: flex;
        justify-content: center;
      }

      .log-app {
        color: #606266;
        font-weight: 500;
      }

      .log-message {
        color: #303133;
        word-break: break-all;
      }

      .log-actions {
        display: flex;
        justify-content: center;
      }
    }

    .load-more {
      padding: 20px;
      text-align: center;
    }

    .empty-state {
      padding: 40px;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .logs-section .logs-content .log-item {
    grid-template-columns: 1fr;
    gap: 8px;

    .log-time, .log-level, .log-app {
      font-size: 11px;
    }
  }
}
</style>
