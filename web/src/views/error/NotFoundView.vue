<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <!-- 404 图标 -->
      <div class="error-icon">
        <el-icon><WarningFilled /></el-icon>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-code">404</h1>
        <h2 class="error-title">页面不存在</h2>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
          <br>
          请检查URL是否正确，或返回首页继续浏览。
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
      </div>
      
      <!-- 快速导航 -->
      <div class="quick-nav">
        <h3>您可能想要访问：</h3>
        <div class="nav-links">
          <router-link to="/dashboard" class="nav-link">
            <el-icon><Dashboard /></el-icon>
            <span>控制台</span>
          </router-link>
          <router-link to="/apps" class="nav-link">
            <el-icon><Box /></el-icon>
            <span>应用管理</span>
          </router-link>
          <router-link to="/cicd/pipelines" class="nav-link">
            <el-icon><Connection /></el-icon>
            <span>CI/CD</span>
          </router-link>
          <router-link to="/monitor" class="nav-link">
            <el-icon><Monitor /></el-icon>
            <span>监控中心</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.not-found-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page);
  padding: 20px;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  
  .error-icon {
    margin-bottom: 24px;
    
    .el-icon {
      font-size: 120px;
      color: var(--el-color-warning);
    }
  }
  
  .error-info {
    margin-bottom: 32px;
    
    .error-code {
      font-size: 72px;
      font-weight: 700;
      color: var(--el-color-primary);
      margin: 0 0 16px 0;
      line-height: 1;
    }
    
    .error-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0 0 16px 0;
    }
    
    .error-description {
      font-size: 16px;
      color: var(--el-text-color-secondary);
      line-height: 1.6;
      margin: 0;
    }
  }
  
  .error-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 40px;
    
    @media (max-width: 480px) {
      flex-direction: column;
      align-items: center;
    }
  }
  
  .quick-nav {
    h3 {
      font-size: 16px;
      color: var(--el-text-color-primary);
      margin: 0 0 20px 0;
    }
    
    .nav-links {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      
      .nav-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        background: var(--el-bg-color);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: var(--el-color-primary);
          background: var(--el-color-primary-light-9);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
          margin-bottom: 8px;
        }
        
        span {
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .not-found-container {
    .not-found-content {
      .error-icon {
        .el-icon {
          font-size: 80px;
        }
      }
      
      .error-info {
        .error-code {
          font-size: 48px;
        }
        
        .error-title {
          font-size: 20px;
        }
        
        .error-description {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
