<template>
  <div class="secret-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Key /></el-icon>
          密钥管理
        </h1>
        <p class="page-description">管理应用程序的敏感配置信息，如数据库密码、API密钥等</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          创建密钥
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="密钥名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入密钥名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchForm.type" placeholder="选择类型" clearable>
            <el-option label="通用密钥" value="generic" />
            <el-option label="数据库密钥" value="database" />
            <el-option label="API密钥" value="api" />
            <el-option label="证书" value="certificate" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 密钥列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="secretList"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="密钥名称" min-width="200">
          <template #default="{ row }">
            <div class="secret-name">
              <el-icon><Key /></el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="updatedAt" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Key, Plus, Search } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据定义
const loading = ref(false)
const secretList = ref([])
const selectedSecrets = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  type: ''
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

/**
 * 获取密钥类型标签样式
 */
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    generic: '',
    database: 'success',
    api: 'warning',
    certificate: 'danger'
  }
  return typeMap[type] || ''
}

/**
 * 获取密钥类型标签文本
 */
const getTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    generic: '通用密钥',
    database: '数据库密钥',
    api: 'API密钥',
    certificate: '证书'
  }
  return labelMap[type] || type
}

/**
 * 格式化日期
 */
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 加载密钥列表
 */
const loadSecretList = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取密钥列表
    // const response = await secretApi.getList({
    //   ...searchForm,
    //   page: pagination.page,
    //   size: pagination.size
    // })
    
    // 模拟数据
    secretList.value = [
      {
        id: '1',
        name: 'database-password',
        type: 'database',
        description: '数据库连接密码',
        createdAt: '2024-01-15 10:30:00',
        updatedAt: '2024-01-15 10:30:00'
      },
      {
        id: '2',
        name: 'api-key',
        type: 'api',
        description: '第三方API访问密钥',
        createdAt: '2024-01-14 15:20:00',
        updatedAt: '2024-01-16 09:15:00'
      }
    ]
    pagination.total = 2
  } catch (error) {
    console.error('加载密钥列表失败:', error)
    ElMessage.error('加载密钥列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadSecretList()
}

/**
 * 重置搜索条件
 */
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    type: ''
  })
  handleSearch()
}

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: any[]) => {
  selectedSecrets.value = selection
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadSecretList()
}

/**
 * 处理页面大小变化
 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadSecretList()
}

/**
 * 创建密钥
 */
const handleCreate = () => {
  // TODO: 跳转到创建密钥页面或打开创建对话框
  ElMessage.info('创建密钥功能开发中...')
}

/**
 * 编辑密钥
 */
const handleEdit = (row: any) => {
  // TODO: 跳转到编辑密钥页面或打开编辑对话框
  ElMessage.info(`编辑密钥: ${row.name}`)
}

/**
 * 查看密钥详情
 */
const handleView = (row: any) => {
  // TODO: 跳转到密钥详情页面或打开详情对话框
  ElMessage.info(`查看密钥: ${row.name}`)
}

/**
 * 删除密钥
 */
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除密钥 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    // await secretApi.delete(row.id)
    
    ElMessage.success('删除成功')
    loadSecretList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除密钥失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSecretList()
})
</script>

<style lang="scss" scoped>
.secret-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.filter-section {
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .secret-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pagination-section {
    padding: 16px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ebeef5;
  }
}
</style>
