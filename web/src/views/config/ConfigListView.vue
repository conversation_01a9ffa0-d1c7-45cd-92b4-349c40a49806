<template>
  <div class="config-list-container">
    <!-- 页面头部 -->
    <PageHeader
      title="配置管理"
      description="管理应用配置项、环境变量和系统参数"
      icon="Setting"
    >
      <template #actions>
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加配置
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索配置键名..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="filterScope"
          placeholder="配置范围"
          clearable
          @change="handleFilter"
        >
          <el-option label="全部范围" value="" />
          <el-option label="全局配置" value="global" />
          <el-option label="租户配置" value="tenant" />
          <el-option label="应用配置" value="application" />
        </el-select>
        
        <el-select
          v-model="filterEnvironment"
          placeholder="环境"
          clearable
          @change="handleFilter"
        >
          <el-option label="全部环境" value="" />
          <el-option label="开发环境" value="dev" />
          <el-option label="测试环境" value="test" />
          <el-option label="生产环境" value="prod" />
        </el-select>
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportConfigs">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>
    
    <!-- 配置列表 -->
    <LoadingCard :loading="loading">
      <el-table :data="configList" class="config-table">
        <el-table-column label="配置键" min-width="200">
          <template #default="{ row }">
            <div class="config-key">
              <span class="key-name">{{ row.key }}</span>
              <el-tag v-if="row.isSecret" type="warning" size="small">密钥</el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="配置值" min-width="250">
          <template #default="{ row }">
            <div class="config-value">
              <span v-if="row.isSecret" class="secret-value">
                ******
                <el-button 
                  type="text" 
                  size="small"
                  @click="toggleSecretVisibility(row)"
                >
                  <el-icon><View v-if="!row.showSecret" /><Hide v-else /></el-icon>
                </el-button>
              </span>
              <span v-else-if="row.type === 'json'" class="json-value">
                <el-button 
                  type="text" 
                  size="small"
                  @click="viewJsonValue(row)"
                >
                  查看JSON
                </el-button>
              </span>
              <span v-else class="normal-value">{{ row.value }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)" size="small">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="范围" width="100">
          <template #default="{ row }">
            <el-tag :type="getScopeTagType(row.scope)" size="small">
              {{ getScopeLabel(row.scope) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="环境" width="80">
          <template #default="{ row }">
            <el-tag :type="getEnvironmentTagType(row.environment)" size="small">
              {{ row.environment }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="版本" width="60">
          <template #default="{ row }">
            <span class="version">v{{ row.version }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="更新时间" width="150">
          <template #default="{ row }">
            <span>{{ formatTime(row.updatedAt) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                @click="editConfig(row)"
              >
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="danger"
                @click="deleteConfig(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </LoadingCard>
    
    <!-- 创建/编辑配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      :title="isEditing ? '编辑配置' : '添加配置'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="100px"
      >
        <el-form-item label="配置键" prop="key">
          <el-input
            v-model="configForm.key"
            placeholder="例如：database.host"
            :disabled="isEditing"
          />
        </el-form-item>
        
        <el-form-item label="配置值" prop="value">
          <el-input
            v-if="configForm.type !== 'json'"
            v-model="configForm.value"
            :type="configForm.isSecret ? 'password' : 'text'"
            placeholder="请输入配置值"
            show-password
          />
          <el-input
            v-else
            v-model="configForm.value"
            type="textarea"
            :rows="6"
            placeholder="请输入JSON格式的配置值"
          />
        </el-form-item>
        
        <el-form-item label="数据类型" prop="type">
          <el-radio-group v-model="configForm.type">
            <el-radio label="string">字符串</el-radio>
            <el-radio label="number">数字</el-radio>
            <el-radio label="boolean">布尔值</el-radio>
            <el-radio label="json">JSON</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="配置范围" prop="scope">
          <el-select v-model="configForm.scope" @change="handleScopeChange">
            <el-option label="全局配置" value="global" />
            <el-option label="租户配置" value="tenant" />
            <el-option label="应用配置" value="application" />
          </el-select>
        </el-form-item>
        
        <el-form-item 
          v-if="configForm.scope === 'application'" 
          label="应用" 
          prop="scopeId"
        >
          <el-select v-model="configForm.scopeId" placeholder="选择应用">
            <el-option
              v-for="app in applicationOptions"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="环境" prop="environment">
          <el-select v-model="configForm.environment">
            <el-option label="开发环境" value="dev" />
            <el-option label="测试环境" value="test" />
            <el-option label="生产环境" value="prod" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="configForm.description"
            type="textarea"
            :rows="3"
            placeholder="配置项描述（可选）"
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="configForm.isSecret">
            这是一个敏感配置（密钥）
          </el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="saving"
          @click="handleSave"
        >
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
    
    <!-- JSON查看对话框 -->
    <el-dialog
      v-model="jsonDialogVisible"
      title="JSON配置值"
      width="600px"
    >
      <div class="json-viewer">
        <pre class="json-content">{{ formatJson(currentJsonValue) }}</pre>
      </div>
      
      <template #footer>
        <el-button @click="jsonDialogVisible = false">关闭</el-button>
        <el-button @click="copyJsonValue">复制</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import type { Config } from '@/types'
import type { FormInstance, FormRules } from 'element-plus'
import PageHeader from '@/components/common/PageHeader.vue'
import LoadingCard from '@/components/common/LoadingCard.vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const configDialogVisible = ref(false)
const jsonDialogVisible = ref(false)
const isEditing = ref(false)
const searchKeyword = ref('')
const filterScope = ref('')
const filterEnvironment = ref('')
const configList = ref<Config[]>([])
const applicationOptions = ref<any[]>([])
const currentJsonValue = ref('')
const configFormRef = ref<FormInstance>()

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 配置表单
const configForm = reactive({
  key: '',
  value: '',
  type: 'string',
  scope: 'global',
  scopeId: '',
  environment: 'dev',
  description: '',
  isSecret: false
})

// 表单验证规则
const configRules: FormRules = {
  key: [
    { required: true, message: '请输入配置键', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z][a-zA-Z0-9._-]*$/, 
      message: '配置键格式错误，只能包含字母、数字、点、下划线和连字符', 
      trigger: 'blur' 
    }
  ],
  value: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  scope: [
    { required: true, message: '请选择配置范围', trigger: 'change' }
  ],
  environment: [
    { required: true, message: '请选择环境', trigger: 'change' }
  ]
}

// 计算属性
const queryParams = computed(() => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  search: searchKeyword.value,
  scope: filterScope.value,
  environment: filterEnvironment.value
}))

// 方法定义
const loadConfigList = async () => {
  try {
    loading.value = true
    // TODO: 实现配置列表API调用
    // const response = await configApi.getConfigs(queryParams.value)
    // configList.value = response.data.items
    // pagination.total = response.data.pagination.total
    
    // 模拟数据
    configList.value = [
      {
        id: '1',
        key: 'database.host',
        value: 'localhost',
        type: 'string',
        scope: 'global',
        scopeId: '',
        environment: 'dev',
        description: '数据库主机地址',
        isSecret: false,
        version: 1,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        key: 'database.password',
        value: 'secret123',
        type: 'string',
        scope: 'global',
        scopeId: '',
        environment: 'prod',
        description: '数据库密码',
        isSecret: true,
        version: 2,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z'
      }
    ]
    pagination.total = 2
  } catch (error) {
    console.error('加载配置列表失败:', error)
    ElMessage.error('加载配置列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = debounce(() => {
  pagination.page = 1
  loadConfigList()
}, 300)

const handleFilter = () => {
  pagination.page = 1
  loadConfigList()
}

const refreshList = () => {
  loadConfigList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadConfigList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadConfigList()
}

const showCreateDialog = () => {
  isEditing.value = false
  resetConfigForm()
  configDialogVisible.value = true
}

const editConfig = (config: Config) => {
  isEditing.value = true
  Object.assign(configForm, {
    key: config.key,
    value: config.isSecret ? '' : config.value,
    type: config.type,
    scope: config.scope,
    scopeId: config.scopeId,
    environment: config.environment,
    description: config.description,
    isSecret: config.isSecret
  })
  configDialogVisible.value = true
}

const handleSave = async () => {
  if (!configFormRef.value) return
  
  try {
    await configFormRef.value.validate()
    saving.value = true
    
    // TODO: 实现保存配置API调用
    if (isEditing.value) {
      // await configApi.updateConfig(currentConfigId, configForm)
      ElMessage.success('配置更新成功')
    } else {
      // await configApi.createConfig(configForm)
      ElMessage.success('配置创建成功')
    }
    
    configDialogVisible.value = false
    await loadConfigList()
  } catch (error) {
    console.error('保存配置失败:', error)
  } finally {
    saving.value = false
  }
}

const deleteConfig = async (config: Config) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${config.key}" 吗？此操作不可恢复！`,
      '确认删除',
      { type: 'error' }
    )
    
    // TODO: 实现删除配置API调用
    // await configApi.deleteConfig(config.id)
    ElMessage.success('配置已删除')
    await loadConfigList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error)
    }
  }
}

const toggleSecretVisibility = (config: Config) => {
  // TODO: 实现密钥显示/隐藏逻辑
  config.showSecret = !config.showSecret
  if (config.showSecret) {
    // 获取真实的密钥值
    ElMessage.info('密钥查看功能开发中...')
  }
}

const viewJsonValue = (config: Config) => {
  currentJsonValue.value = config.value
  jsonDialogVisible.value = true
}

const copyJsonValue = async () => {
  try {
    await navigator.clipboard.writeText(currentJsonValue.value)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const exportConfigs = () => {
  // TODO: 实现配置导出功能
  ElMessage.info('配置导出功能开发中...')
}

const handleScopeChange = () => {
  configForm.scopeId = ''
}

const resetConfigForm = () => {
  Object.assign(configForm, {
    key: '',
    value: '',
    type: 'string',
    scope: 'global',
    scopeId: '',
    environment: 'dev',
    description: '',
    isSecret: false
  })
}

// 工具函数
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    string: '',
    number: 'success',
    boolean: 'warning',
    json: 'info'
  }
  return typeMap[type] || ''
}

const getTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    string: '字符串',
    number: '数字',
    boolean: '布尔值',
    json: 'JSON'
  }
  return labelMap[type] || type
}

const getScopeTagType = (scope: string) => {
  const typeMap: Record<string, string> = {
    global: 'primary',
    tenant: 'success',
    application: 'warning'
  }
  return typeMap[scope] || ''
}

const getScopeLabel = (scope: string) => {
  const labelMap: Record<string, string> = {
    global: '全局',
    tenant: '租户',
    application: '应用'
  }
  return labelMap[scope] || scope
}

const getEnvironmentTagType = (env: string) => {
  const typeMap: Record<string, string> = {
    dev: 'info',
    test: 'warning',
    prod: 'danger'
  }
  return typeMap[env] || ''
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const formatJson = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadConfigList()
})
</script>

<style lang="scss" scoped>
// 导入混合宏以使用样式函数
@use '@/styles/mixins' as mixins;

.config-list-container {
  .filter-section {
    @include mixins.card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      
      .search-input {
        width: 250px;
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      
      .filter-left {
        width: 100%;
        flex-wrap: wrap;
        
        .search-input {
          width: 100%;
        }
      }
    }
  }
  
  .config-table {
    .config-key {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .key-name {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        color: var(--el-text-color-primary);
      }
    }
    
    .config-value {
      .secret-value {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        color: var(--el-text-color-placeholder);
      }
      
      .json-value {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
      
      .normal-value {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        color: var(--el-text-color-primary);
        word-break: break-all;
      }
    }
    
    .version {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  .json-viewer {
    .json-content {
      background: #f8f9fa;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      padding: 16px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
      color: var(--el-text-color-primary);
      max-height: 400px;
      overflow-y: auto;
      margin: 0;
    }
  }
}
</style>
