<template>
  <div class="role-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Avatar /></el-icon>
          角色管理
        </h1>
        <p class="page-description">管理系统角色和权限配置</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          创建角色
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="角色名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入角色名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 角色列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="roleList"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="角色名称" min-width="150">
          <template #default="{ row }">
            <div class="role-name">
              <el-icon><Avatar /></el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="角色代码" width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="userCount" label="用户数量" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ row.userCount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="permissions" label="权限数量" width="100">
          <template #default="{ row }">
            <el-tag type="success">{{ row.permissions?.length || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="updatedAt" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleViewPermissions(row)">
              权限
            </el-button>
            <el-button
              :type="row.status === 'active' ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
              :disabled="row.code === 'super_admin'"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :disabled="row.code === 'super_admin'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 权限查看对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      :title="`${currentRole?.name} - 权限列表`"
      width="600px"
    >
      <div class="permissions-tree">
        <el-tree
          :data="permissionTree"
          :props="treeProps"
          :default-checked-keys="currentRolePermissions"
          show-checkbox
          node-key="id"
          ref="permissionTreeRef"
        />
      </div>
      <template #footer>
        <el-button @click="permissionDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="savePermissions">保存权限</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Avatar, Plus, Search } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据定义
const loading = ref(false)
const roleList = ref([])
const selectedRoles = ref([])
const permissionDialogVisible = ref(false)
const currentRole = ref(null)
const currentRolePermissions = ref([])
const permissionTreeRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: ''
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 权限树配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 权限树数据
const permissionTree = ref([
  {
    id: '1',
    name: '用户管理',
    children: [
      { id: '1-1', name: '查看用户' },
      { id: '1-2', name: '创建用户' },
      { id: '1-3', name: '编辑用户' },
      { id: '1-4', name: '删除用户' }
    ]
  },
  {
    id: '2',
    name: '角色管理',
    children: [
      { id: '2-1', name: '查看角色' },
      { id: '2-2', name: '创建角色' },
      { id: '2-3', name: '编辑角色' },
      { id: '2-4', name: '删除角色' }
    ]
  },
  {
    id: '3',
    name: '应用管理',
    children: [
      { id: '3-1', name: '查看应用' },
      { id: '3-2', name: '创建应用' },
      { id: '3-3', name: '编辑应用' },
      { id: '3-4', name: '删除应用' },
      { id: '3-5', name: '部署应用' }
    ]
  },
  {
    id: '4',
    name: '系统监控',
    children: [
      { id: '4-1', name: '查看监控' },
      { id: '4-2', name: '查看日志' },
      { id: '4-3', name: '系统配置' }
    ]
  }
])

/**
 * 获取状态标签样式
 */
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    disabled: 'warning'
  }
  return typeMap[status] || ''
}

/**
 * 获取状态标签文本
 */
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    active: '启用',
    disabled: '禁用'
  }
  return labelMap[status] || status
}

/**
 * 格式化日期
 */
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 加载角色列表
 */
const loadRoleList = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取角色列表
    // const response = await roleApi.getList({
    //   ...searchForm,
    //   page: pagination.page,
    //   size: pagination.size
    // })
    
    // 模拟数据
    roleList.value = [
      {
        id: '1',
        name: '超级管理员',
        code: 'super_admin',
        description: '系统超级管理员，拥有所有权限',
        userCount: 1,
        permissions: ['1-1', '1-2', '1-3', '1-4', '2-1', '2-2', '2-3', '2-4', '3-1', '3-2', '3-3', '3-4', '3-5', '4-1', '4-2', '4-3'],
        status: 'active',
        createdAt: '2024-01-01 00:00:00',
        updatedAt: '2024-01-01 00:00:00'
      },
      {
        id: '2',
        name: '管理员',
        code: 'admin',
        description: '系统管理员，拥有大部分管理权限',
        userCount: 3,
        permissions: ['1-1', '1-2', '1-3', '3-1', '3-2', '3-3', '3-4', '3-5', '4-1', '4-2'],
        status: 'active',
        createdAt: '2024-01-02 10:00:00',
        updatedAt: '2024-01-15 14:30:00'
      },
      {
        id: '3',
        name: '开发者',
        code: 'developer',
        description: '开发人员，拥有应用管理和监控权限',
        userCount: 8,
        permissions: ['3-1', '3-2', '3-3', '3-5', '4-1', '4-2'],
        status: 'active',
        createdAt: '2024-01-03 14:30:00',
        updatedAt: '2024-01-10 09:15:00'
      },
      {
        id: '4',
        name: '运维人员',
        code: 'operator',
        description: '运维人员，拥有部署和监控权限',
        userCount: 5,
        permissions: ['3-1', '3-5', '4-1', '4-2', '4-3'],
        status: 'active',
        createdAt: '2024-01-04 16:20:00',
        updatedAt: '2024-01-12 11:45:00'
      },
      {
        id: '5',
        name: '普通用户',
        code: 'user',
        description: '普通用户，只能查看基本信息',
        userCount: 15,
        permissions: ['3-1', '4-1'],
        status: 'active',
        createdAt: '2024-01-05 09:30:00',
        updatedAt: '2024-01-08 15:20:00'
      }
    ]
    pagination.total = 5
  } catch (error) {
    console.error('加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadRoleList()
}

/**
 * 重置搜索条件
 */
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: ''
  })
  handleSearch()
}

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: any[]) => {
  selectedRoles.value = selection
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadRoleList()
}

/**
 * 处理页面大小变化
 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadRoleList()
}

/**
 * 创建角色
 */
const handleCreate = () => {
  // TODO: 跳转到创建角色页面或打开创建对话框
  ElMessage.info('创建角色功能开发中...')
}

/**
 * 编辑角色
 */
const handleEdit = (row: any) => {
  // TODO: 跳转到编辑角色页面或打开编辑对话框
  ElMessage.info(`编辑角色: ${row.name}`)
}

/**
 * 查看权限
 */
const handleViewPermissions = (row: any) => {
  currentRole.value = row
  currentRolePermissions.value = row.permissions || []
  permissionDialogVisible.value = true
}

/**
 * 保存权限
 */
const savePermissions = async () => {
  try {
    const checkedKeys = permissionTreeRef.value.getCheckedKeys()
    
    // TODO: 调用API保存权限
    // await roleApi.updatePermissions(currentRole.value.id, checkedKeys)
    
    // 更新本地数据
    const roleIndex = roleList.value.findIndex(role => role.id === currentRole.value.id)
    if (roleIndex !== -1) {
      roleList.value[roleIndex].permissions = checkedKeys
    }
    
    ElMessage.success('权限保存成功')
    permissionDialogVisible.value = false
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  }
}

/**
 * 切换角色状态
 */
const handleToggleStatus = async (row: any) => {
  const action = row.status === 'active' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}角色 "${row.name}" 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用API切换状态
    // await roleApi.toggleStatus(row.id)
    
    // 模拟状态切换
    row.status = row.status === 'active' ? 'disabled' : 'active'
    
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}角色失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

/**
 * 删除角色
 */
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用删除API
    // await roleApi.delete(row.id)
    
    ElMessage.success('删除成功')
    loadRoleList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadRoleList()
})
</script>

<style lang="scss" scoped>
.role-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.filter-section {
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .role-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pagination-section {
    padding: 16px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ebeef5;
  }
}

.permissions-tree {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
}
</style>
