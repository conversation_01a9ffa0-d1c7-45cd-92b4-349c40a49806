<template>
  <div class="user-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><User /></el-icon>
          用户管理
        </h1>
        <p class="page-description">管理系统用户和权限</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createUser">
          <el-icon><Plus /></el-icon>
          添加用户
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalUsers }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><CircleCheckFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.activeUsers }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon online">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.onlineUsers }}</div>
              <div class="stat-label">在线用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon new">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.newUsersToday }}</div>
              <div class="stat-label">今日新增</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索用户名、邮箱或姓名"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="禁用" value="disabled" />
            <el-option label="待激活" value="pending" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="roleFilter" placeholder="角色筛选" clearable @change="handleFilter">
            <el-option label="全部角色" value="" />
            <el-option label="管理员" value="admin" />
            <el-option label="开发者" value="developer" />
            <el-option label="运维" value="operator" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleFilter"
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置筛选</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="user-list-card">
      <div class="list-header">
        <span class="total-count">共 {{ filteredUsers.length }} 个用户</span>
        <div class="header-actions">
          <el-button @click="exportUsers">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="refreshUsers">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <el-table :data="paginatedUsers" style="width: 100%" v-loading="loading">
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="用户信息" width="250">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="40" :src="row.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="info-content">
                <div class="username">{{ row.username }}</div>
                <div class="email">{{ row.email }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="real_name" label="真实姓名" width="120" />
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="角色" width="120">
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role"
              :type="getRoleType(role)"
              size="small"
              style="margin-right: 4px;"
            >
              {{ getRoleText(role) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="最后登录" width="180">
          <template #default="{ row }">
            <div v-if="row.last_login_at">
              <div>{{ formatDate(row.last_login_at) }}</div>
              <div class="login-ip">{{ row.last_login_ip }}</div>
            </div>
            <span v-else class="never-login">从未登录</span>
          </template>
        </el-table-column>
        
        <el-table-column label="在线状态" width="100">
          <template #default="{ row }">
            <div class="online-status">
              <el-icon :color="row.is_online ? '#67C23A' : '#C0C4CC'">
                <CircleFilled />
              </el-icon>
              <span>{{ row.is_online ? '在线' : '离线' }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewUserDetail(row)">详情</el-button>
            <el-button size="small" type="primary" @click="editUser(row)">编辑</el-button>
            <el-dropdown @command="handleUserAction" trigger="click">
              <el-button size="small">
                更多
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'resetPassword', user: row }">
                    重置密码
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'toggleStatus', user: row }">
                    {{ row.status === 'active' ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'permissions', user: row }">
                    权限管理
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'loginHistory', user: row }">
                    登录历史
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'delete', user: row }" divided>
                    删除用户
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredUsers.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="userDetailVisible"
      title="用户详情"
      width="600px"
      @close="closeUserDetail"
    >
      <div v-if="selectedUser" class="user-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedUser.avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="header-info">
            <h3>{{ selectedUser.real_name || selectedUser.username }}</h3>
            <p>{{ selectedUser.email }}</p>
            <el-tag :type="getStatusType(selectedUser.status)">
              {{ getStatusText(selectedUser.status) }}
            </el-tag>
          </div>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">{{ selectedUser.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ selectedUser.email }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ selectedUser.real_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedUser.phone || '-' }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ selectedUser.department || '-' }}</el-descriptions-item>
          <el-descriptions-item label="职位">{{ selectedUser.position || '-' }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag
              v-for="role in selectedUser.roles"
              :key="role"
              :type="getRoleType(role)"
              size="small"
              style="margin-right: 4px;"
            >
              {{ getRoleText(role) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedUser.status)">
              {{ getStatusText(selectedUser.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedUser.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ selectedUser.last_login_at ? formatDate(selectedUser.last_login_at) : '从未登录' }}
          </el-descriptions-item>
          <el-descriptions-item label="登录IP">{{ selectedUser.last_login_ip || '-' }}</el-descriptions-item>
          <el-descriptions-item label="在线状态">
            <div class="online-status">
              <el-icon :color="selectedUser.is_online ? '#67C23A' : '#C0C4CC'">
                <CircleFilled />
              </el-icon>
              <span>{{ selectedUser.is_online ? '在线' : '离线' }}</span>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <el-button @click="closeUserDetail">关闭</el-button>
        <el-button type="primary" @click="editUser(selectedUser)">编辑用户</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  Plus,
  CircleCheckFilled,
  Connection,
  UserFilled,
  Search,
  Download,
  Refresh,
  ArrowDown,
  CircleFilled
} from '@element-plus/icons-vue'
import { userApi } from '@/api/user'
import type { User as UserType } from '@/types/user'

const router = useRouter()

// 响应式数据
const users = ref<UserType[]>([])
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const roleFilter = ref('')
const dateRange = ref<[string, string] | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const userDetailVisible = ref(false)
const selectedUser = ref<UserType | null>(null)

const stats = ref({
  totalUsers: 0,
  activeUsers: 0,
  onlineUsers: 0,
  newUsersToday: 0
})

// 计算属性
const filteredUsers = computed(() => {
  let result = [...users.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(user => 
      user.username.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query) ||
      (user.real_name && user.real_name.toLowerCase().includes(query))
    )
  }
  
  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(user => user.status === statusFilter.value)
  }
  
  // 角色过滤
  if (roleFilter.value) {
    result = result.filter(user => user.roles.includes(roleFilter.value))
  }
  
  // 日期范围过滤
  if (dateRange.value) {
    const [startDate, endDate] = dateRange.value
    result = result.filter(user => {
      const userDate = new Date(user.created_at).toISOString().split('T')[0]
      return userDate >= startDate && userDate <= endDate
    })
  }
  
  return result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
})

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredUsers.value.slice(start, end)
})

// 方法
const loadUsers = async () => {
  loading.value = true
  try {
    const response = await userApi.getUsers()
    users.value = response.data
    updateStats()
  } catch (error) {
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  stats.value = {
    totalUsers: users.value.length,
    activeUsers: users.value.filter(u => u.status === 'active').length,
    onlineUsers: users.value.filter(u => u.is_online).length,
    newUsersToday: users.value.filter(u => {
      const today = new Date().toDateString()
      return new Date(u.created_at).toDateString() === today
    }).length
  }
}

const createUser = () => {
  router.push('/users/create')
}

const viewUserDetail = (user: UserType) => {
  selectedUser.value = user
  userDetailVisible.value = true
}

const closeUserDetail = () => {
  userDetailVisible.value = false
  selectedUser.value = null
}

const editUser = (user: UserType) => {
  router.push(`/users/${user.id}/edit`)
}

const handleUserAction = async ({ action, user }: { action: string; user: UserType }) => {
  switch (action) {
    case 'resetPassword':
      await resetUserPassword(user)
      break
    case 'toggleStatus':
      await toggleUserStatus(user)
      break
    case 'permissions':
      router.push(`/users/${user.id}/permissions`)
      break
    case 'loginHistory':
      router.push(`/users/${user.id}/login-history`)
      break
    case 'delete':
      await deleteUser(user)
      break
  }
}

const resetUserPassword = async (user: UserType) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 ${user.username} 的密码吗？`,
      '确认重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await userApi.resetPassword(user.id)
    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置密码失败')
    }
  }
}

const toggleUserStatus = async (user: UserType) => {
  const action = user.status === 'active' ? '禁用' : '启用'
  const newStatus = user.status === 'active' ? 'disabled' : 'active'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 ${user.username} 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await userApi.updateUserStatus(user.id, newStatus)
    ElMessage.success(`用户${action}成功`)
    await loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}用户失败`)
    }
  }
}

const deleteUser = async (user: UserType) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${user.username} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await userApi.deleteUser(user.id)
    ElMessage.success('用户删除成功')
    await loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除用户失败')
    }
  }
}

const exportUsers = () => {
  // 导出用户数据
  ElMessage.info('导出功能开发中...')
}

const refreshUsers = () => {
  loadUsers()
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  roleFilter.value = ''
  dateRange.value = null
  currentPage.value = 1
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'disabled': return 'danger'
    case 'pending': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'disabled': return '禁用'
    case 'pending': return '待激活'
    default: return '未知'
  }
}

const getRoleType = (role: string) => {
  switch (role) {
    case 'admin': return 'danger'
    case 'developer': return 'primary'
    case 'operator': return 'warning'
    case 'user': return 'success'
    default: return 'info'
  }
}

const getRoleText = (role: string) => {
  switch (role) {
    case 'admin': return '管理员'
    case 'developer': return '开发者'
    case 'operator': return '运维'
    case 'user': return '普通用户'
    default: return role
  }
}

const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadUsers()
})

// 模拟数据
users.value = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    real_name: '系统管理员',
    phone: '13800138000',
    department: 'IT部门',
    position: '系统管理员',
    status: 'active',
    roles: ['admin'],
    is_online: true,
    last_login_at: new Date('2024-01-15T14:30:00'),
    last_login_ip: '*************',
    created_at: new Date('2024-01-01T00:00:00')
  },
  {
    id: '2',
    username: 'developer1',
    email: '<EMAIL>',
    real_name: '张三',
    phone: '13800138001',
    department: '开发部门',
    position: '高级开发工程师',
    status: 'active',
    roles: ['developer'],
    is_online: false,
    last_login_at: new Date('2024-01-15T10:20:00'),
    last_login_ip: '*************',
    created_at: new Date('2024-01-02T00:00:00')
  },
  {
    id: '3',
    username: 'operator1',
    email: '<EMAIL>',
    real_name: '李四',
    phone: '13800138002',
    department: '运维部门',
    position: '运维工程师',
    status: 'active',
    roles: ['operator'],
    is_online: true,
    last_login_at: new Date('2024-01-15T13:45:00'),
    last_login_ip: '*************',
    created_at: new Date('2024-01-03T00:00:00')
  }
]

updateStats()
</script>

<style lang="scss" scoped>
.user-management-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  
  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0 0 8px 0;
    }
    
    .page-description {
      color: var(--el-text-color-regular);
      margin: 0;
    }
  }
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      
      &.total {
        background: rgba(64, 158, 255, 0.1);
        color: var(--el-color-primary);
      }
      
      &.active {
        background: rgba(103, 194, 58, 0.1);
        color: var(--el-color-success);
      }
      
      &.online {
        background: rgba(230, 162, 60, 0.1);
        color: var(--el-color-warning);
      }
      
      &.new {
        background: rgba(245, 108, 108, 0.1);
        color: var(--el-color-danger);
      }
    }
    
    .stat-info {
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.filter-card {
  margin-bottom: 24px;
}

.user-list-card {
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .total-count {
      color: var(--el-text-color-regular);
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .info-content {
    .username {
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 2px;
    }
    
    .email {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

.login-ip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.never-login {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.online-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.user-detail {
  .detail-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--el-border-color-light);
    
    .header-info {
      h3 {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      p {
        color: var(--el-text-color-regular);
        margin: 0 0 12px 0;
      }
    }
  }
}
</style>
