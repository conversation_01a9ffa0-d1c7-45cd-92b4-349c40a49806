<template>
  <div class="task-progress-container">
    <div class="progress-header">
      <div class="task-info">
        <h4 class="task-title">{{ task.script_path }}</h4>
        <p class="task-id">任务ID: {{ task.id }}</p>
      </div>
      <div class="task-status">
        <TaskStatusBadge :status="task.status" size="large" />
      </div>
    </div>
    
    <div class="progress-content">
      <!-- 进度条 -->
      <div class="progress-bar-section">
        <el-progress
          :percentage="progressPercentage"
          :status="getProgressStatus(task.status)"
          :stroke-width="8"
          :show-text="false"
          class="main-progress"
        />
        <div class="progress-info">
          <span class="progress-text">{{ getProgressText() }}</span>
          <span class="progress-time">{{ getTimeInfo() }}</span>
        </div>
      </div>
      
      <!-- 执行阶段 -->
      <div class="execution-stages">
        <div
          v-for="stage in executionStages"
          :key="stage.key"
          :class="['stage-item', getStageClass(stage.key)]"
        >
          <div class="stage-icon">
            <el-icon v-if="getStageStatus(stage.key) === 'completed'">
              <SuccessFilled />
            </el-icon>
            <el-icon v-else-if="getStageStatus(stage.key) === 'running'">
              <Loading />
            </el-icon>
            <el-icon v-else-if="getStageStatus(stage.key) === 'failed'">
              <CircleCloseFilled />
            </el-icon>
            <el-icon v-else>
              <Clock />
            </el-icon>
          </div>
          <div class="stage-content">
            <div class="stage-title">{{ stage.title }}</div>
            <div class="stage-description">{{ stage.description }}</div>
            <div v-if="stage.key === currentStage && task.status === 'running'" class="stage-time">
              执行中... {{ getStageElapsedTime(stage.key) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 资源使用情况 -->
      <div v-if="showResourceUsage && resourceUsage" class="resource-usage">
        <h5>资源使用情况</h5>
        <div class="resource-grid">
          <div class="resource-item">
            <span class="resource-label">CPU</span>
            <el-progress
              :percentage="resourceUsage.cpu_usage"
              :stroke-width="6"
              :show-text="false"
              class="resource-progress"
            />
            <span class="resource-value">{{ resourceUsage.cpu_usage.toFixed(1) }}%</span>
          </div>
          <div class="resource-item">
            <span class="resource-label">内存</span>
            <el-progress
              :percentage="getMemoryPercentage()"
              :stroke-width="6"
              :show-text="false"
              class="resource-progress"
            />
            <span class="resource-value">{{ formatBytes(resourceUsage.memory_usage) }}</span>
          </div>
          <div class="resource-item">
            <span class="resource-label">磁盘</span>
            <el-progress
              :percentage="getDiskPercentage()"
              :stroke-width="6"
              :show-text="false"
              class="resource-progress"
            />
            <span class="resource-value">{{ formatBytes(resourceUsage.disk_usage) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 实时日志 -->
      <div v-if="showLogs && logs.length > 0" class="live-logs">
        <div class="logs-header">
          <h5>实时日志</h5>
          <el-button size="small" @click="toggleLogsExpanded">
            {{ logsExpanded ? '收起' : '展开' }}
          </el-button>
        </div>
        <div v-show="logsExpanded" class="logs-content">
          <div
            v-for="(log, index) in recentLogs"
            :key="index"
            :class="['log-entry', `log-${log.level}`]"
          >
            <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="progress-actions">
      <el-button
        v-if="canCancel"
        type="danger"
        size="small"
        @click="$emit('cancel')"
      >
        <el-icon><Close /></el-icon>
        取消任务
      </el-button>
      <el-button
        size="small"
        @click="$emit('view-details')"
      >
        <el-icon><View /></el-icon>
        查看详情
      </el-button>
      <el-button
        v-if="task.status === 'completed'"
        type="primary"
        size="small"
        @click="$emit('view-result')"
      >
        <el-icon><Document /></el-icon>
        查看结果
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import TaskStatusBadge from './TaskStatusBadge.vue'
import type { ScriptTask, LogEntry, ResourceUsage } from '@/types'
import dayjs from 'dayjs'

interface Props {
  task: ScriptTask
  logs?: LogEntry[]
  resourceUsage?: ResourceUsage
  showResourceUsage?: boolean
  showLogs?: boolean
  autoRefresh?: boolean
}

interface Emits {
  (e: 'cancel'): void
  (e: 'view-details'): void
  (e: 'view-result'): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  logs: () => [],
  showResourceUsage: true,
  showLogs: true,
  autoRefresh: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const logsExpanded = ref(false)
const refreshTimer = ref<NodeJS.Timeout | null>(null)
const stageStartTimes = ref<Record<string, Date>>({})

// 执行阶段定义
const executionStages = [
  {
    key: 'queued',
    title: '排队等待',
    description: '任务已提交，等待执行'
  },
  {
    key: 'preparing',
    title: '环境准备',
    description: '准备执行环境和依赖'
  },
  {
    key: 'executing',
    title: '脚本执行',
    description: '正在执行脚本代码'
  },
  {
    key: 'finishing',
    title: '结果处理',
    description: '处理执行结果和产物'
  },
  {
    key: 'completed',
    title: '执行完成',
    description: '任务执行完成'
  }
]

// 计算属性
const progressPercentage = computed(() => {
  switch (props.task.status) {
    case 'pending':
      return 10
    case 'running':
      return 60
    case 'completed':
      return 100
    case 'failed':
    case 'canceled':
      return 100
    default:
      return 0
  }
})

const currentStage = computed(() => {
  switch (props.task.status) {
    case 'pending':
      return 'queued'
    case 'running':
      return 'executing'
    case 'completed':
      return 'completed'
    case 'failed':
    case 'canceled':
      return 'completed'
    default:
      return 'queued'
  }
})

const canCancel = computed(() => {
  return ['pending', 'running'].includes(props.task.status)
})

const recentLogs = computed(() => {
  return props.logs.slice(-10) // 只显示最近10条日志
})

// 方法定义
const getProgressStatus = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'failed':
      return 'exception'
    case 'canceled':
      return 'warning'
    default:
      return undefined
  }
}

const getProgressText = () => {
  switch (props.task.status) {
    case 'pending':
      return '等待执行...'
    case 'running':
      return '正在执行...'
    case 'completed':
      return '执行完成'
    case 'failed':
      return '执行失败'
    case 'canceled':
      return '已取消'
    default:
      return '未知状态'
  }
}

const getTimeInfo = () => {
  const now = new Date()
  const createdAt = new Date(props.task.created_at)
  
  if (props.task.status === 'completed' && props.task.finished_at) {
    const finishedAt = new Date(props.task.finished_at)
    const duration = Math.floor((finishedAt.getTime() - createdAt.getTime()) / 1000)
    return `耗时: ${formatDuration(duration)}`
  } else if (props.task.status === 'running' && props.task.started_at) {
    const startedAt = new Date(props.task.started_at)
    const elapsed = Math.floor((now.getTime() - startedAt.getTime()) / 1000)
    return `已运行: ${formatDuration(elapsed)}`
  } else {
    const elapsed = Math.floor((now.getTime() - createdAt.getTime()) / 1000)
    return `已等待: ${formatDuration(elapsed)}`
  }
}

const getStageStatus = (stageKey: string) => {
  const stageIndex = executionStages.findIndex(s => s.key === stageKey)
  const currentIndex = executionStages.findIndex(s => s.key === currentStage.value)
  
  if (props.task.status === 'failed' && stageIndex === currentIndex) {
    return 'failed'
  } else if (stageIndex < currentIndex) {
    return 'completed'
  } else if (stageIndex === currentIndex && props.task.status === 'running') {
    return 'running'
  } else {
    return 'pending'
  }
}

const getStageClass = (stageKey: string) => {
  const status = getStageStatus(stageKey)
  return `stage-${status}`
}

const getStageElapsedTime = (stageKey: string) => {
  const startTime = stageStartTimes.value[stageKey]
  if (!startTime) return ''
  
  const elapsed = Math.floor((new Date().getTime() - startTime.getTime()) / 1000)
  return formatDuration(elapsed)
}

const getMemoryPercentage = () => {
  if (!props.resourceUsage) return 0
  // 假设最大内存为2GB
  const maxMemory = 2 * 1024 * 1024 * 1024
  return Math.min((props.resourceUsage.memory_usage / maxMemory) * 100, 100)
}

const getDiskPercentage = () => {
  if (!props.resourceUsage) return 0
  // 假设最大磁盘为10GB
  const maxDisk = 10 * 1024 * 1024 * 1024
  return Math.min((props.resourceUsage.disk_usage / maxDisk) * 100, 100)
}

const toggleLogsExpanded = () => {
  logsExpanded.value = !logsExpanded.value
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}时${minutes}分`
  }
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatLogTime = (timestamp: string) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

// 生命周期
onMounted(() => {
  // 记录当前阶段开始时间
  stageStartTimes.value[currentStage.value] = new Date()
  
  // 自动刷新
  if (props.autoRefresh && ['pending', 'running'].includes(props.task.status)) {
    refreshTimer.value = setInterval(() => {
      emit('refresh')
    }, 3000)
  }
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.task-progress-container {
  @include card-style;
  padding: 20px;
  
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .task-info {
      .task-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 4px 0;
      }
      
      .task-id {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin: 0;
        font-family: monospace;
      }
    }
  }
  
  .progress-content {
    .progress-bar-section {
      margin-bottom: 24px;
      
      .main-progress {
        margin-bottom: 8px;
      }
      
      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        
        .progress-text {
          color: var(--el-text-color-primary);
          font-weight: 500;
        }
        
        .progress-time {
          color: var(--el-text-color-secondary);
          font-family: monospace;
        }
      }
    }
    
    .execution-stages {
      margin-bottom: 24px;
      
      .stage-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 8px 0;
        position: relative;
        
        &:not(:last-child)::after {
          content: '';
          position: absolute;
          left: 11px;
          top: 32px;
          width: 2px;
          height: 24px;
          background: var(--el-border-color-light);
        }
        
        .stage-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          flex-shrink: 0;
          background: var(--el-bg-color-page);
          border: 2px solid var(--el-border-color-light);
        }
        
        .stage-content {
          flex: 1;
          
          .stage-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 2px;
          }
          
          .stage-description {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
          
          .stage-time {
            font-size: 11px;
            color: var(--el-color-primary);
            margin-top: 2px;
          }
        }
        
        &.stage-completed {
          .stage-icon {
            background: var(--el-color-success);
            border-color: var(--el-color-success);
            color: white;
          }
          
          &::after {
            background: var(--el-color-success);
          }
        }
        
        &.stage-running {
          .stage-icon {
            background: var(--el-color-warning);
            border-color: var(--el-color-warning);
            color: white;
            animation: pulse 1.5s ease-in-out infinite;
          }
        }
        
        &.stage-failed {
          .stage-icon {
            background: var(--el-color-danger);
            border-color: var(--el-color-danger);
            color: white;
          }
        }
      }
    }
    
    .resource-usage {
      margin-bottom: 24px;
      
      h5 {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 12px 0;
      }
      
      .resource-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        
        .resource-item {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .resource-label {
            width: 40px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
          
          .resource-progress {
            flex: 1;
          }
          
          .resource-value {
            width: 60px;
            font-size: 12px;
            color: var(--el-text-color-primary);
            text-align: right;
            font-family: monospace;
          }
        }
      }
    }
    
    .live-logs {
      .logs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        h5 {
          font-size: 14px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }
      }
      
      .logs-content {
        background: #1e1e1e;
        color: #d4d4d4;
        padding: 12px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 11px;
        max-height: 200px;
        overflow-y: auto;
        
        .log-entry {
          display: flex;
          gap: 8px;
          margin-bottom: 2px;
          
          .log-time {
            color: #569cd6;
            width: 60px;
            flex-shrink: 0;
          }
          
          .log-level {
            width: 40px;
            flex-shrink: 0;
            font-weight: bold;
            
            &.log-info { color: #4ec9b0; }
            &.log-warn { color: #dcdcaa; }
            &.log-error { color: #f44747; }
          }
          
          .log-message {
            flex: 1;
            word-break: break-all;
          }
        }
      }
    }
  }
  
  .progress-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-light);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
