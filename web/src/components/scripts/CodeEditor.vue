<template>
  <div class="code-editor-container">
    <div class="editor-header">
      <div class="header-left">
        <span class="editor-title">{{ title }}</span>
        <el-tag v-if="language" size="small" :type="getLanguageTagType(language)">
          {{ getLanguageLabel(language) }}
        </el-tag>
      </div>
      <div class="header-right">
        <el-button-group size="small">
          <el-button @click="formatCode" :disabled="readonly">
            <el-icon><MagicStick /></el-icon>
            格式化
          </el-button>
          <el-button @click="copyCode">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-button>
          <el-button @click="downloadCode" v-if="allowDownload">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="editor-content">
      <el-input
        v-model="internalValue"
        type="textarea"
        :rows="rows"
        :readonly="readonly"
        :placeholder="placeholder"
        :class="['code-textarea', `language-${language}`]"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 行号显示 -->
      <div v-if="showLineNumbers" class="line-numbers">
        <div
          v-for="n in lineCount"
          :key="n"
          class="line-number"
        >
          {{ n }}
        </div>
      </div>
    </div>
    
    <!-- 状态栏 -->
    <div class="editor-footer">
      <div class="footer-left">
        <span class="line-info">行: {{ currentLine }}, 列: {{ currentColumn }}</span>
        <span class="char-count">字符数: {{ characterCount }}</span>
      </div>
      <div class="footer-right">
        <el-select
          v-model="internalLanguage"
          size="small"
          style="width: 120px"
          @change="handleLanguageChange"
          :disabled="readonly"
        >
          <el-option label="Python" value="python" />
          <el-option label="JavaScript" value="javascript" />
          <el-option label="TypeScript" value="typescript" />
          <el-option label="Go" value="go" />
          <el-option label="Java" value="java" />
          <el-option label="Shell" value="shell" />
          <el-option label="JSON" value="json" />
          <el-option label="YAML" value="yaml" />
          <el-option label="Markdown" value="markdown" />
          <el-option label="Plain Text" value="text" />
        </el-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

interface Props {
  modelValue: string
  language?: string
  title?: string
  readonly?: boolean
  rows?: number
  placeholder?: string
  showLineNumbers?: boolean
  allowDownload?: boolean
  filename?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'update:language', language: string): void
  (e: 'focus'): void
  (e: 'blur'): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  language: 'text',
  title: '代码编辑器',
  readonly: false,
  rows: 20,
  placeholder: '请输入代码...',
  showLineNumbers: true,
  allowDownload: false,
  filename: 'code'
})

const emit = defineEmits<Emits>()

// 响应式数据
const internalValue = ref(props.modelValue)
const internalLanguage = ref(props.language)
const currentLine = ref(1)
const currentColumn = ref(1)
const textareaRef = ref<HTMLTextAreaElement>()

// 计算属性
const lineCount = computed(() => {
  return internalValue.value.split('\n').length
})

const characterCount = computed(() => {
  return internalValue.value.length
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  internalValue.value = newValue
})

watch(() => props.language, (newValue) => {
  internalLanguage.value = newValue || 'text'
})

watch(internalValue, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
})

// 方法定义
const handleInput = (value: string) => {
  internalValue.value = value
  updateCursorPosition()
}

const handleFocus = () => {
  emit('focus')
  updateCursorPosition()
}

const handleBlur = () => {
  emit('blur')
}

const handleLanguageChange = (language: string) => {
  emit('update:language', language)
}

const updateCursorPosition = async () => {
  await nextTick()
  const textarea = document.querySelector('.code-textarea textarea') as HTMLTextAreaElement
  if (!textarea) return
  
  const cursorPosition = textarea.selectionStart
  const textBeforeCursor = internalValue.value.substring(0, cursorPosition)
  const lines = textBeforeCursor.split('\n')
  
  currentLine.value = lines.length
  currentColumn.value = lines[lines.length - 1].length + 1
}

const formatCode = () => {
  try {
    if (internalLanguage.value === 'json') {
      const parsed = JSON.parse(internalValue.value)
      internalValue.value = JSON.stringify(parsed, null, 2)
    } else {
      // 简单的代码格式化（可以集成更专业的格式化工具）
      const lines = internalValue.value.split('\n')
      let indentLevel = 0
      const formatted = lines.map(line => {
        const trimmed = line.trim()
        if (!trimmed) return ''
        
        // 简单的缩进逻辑
        if (trimmed.includes('}') || trimmed.includes(']') || trimmed.includes(')')){
          indentLevel = Math.max(0, indentLevel - 1)
        }
        
        const indented = '  '.repeat(indentLevel) + trimmed
        
        if (trimmed.includes('{') || trimmed.includes('[') || trimmed.includes('(')){
          indentLevel++
        }
        
        return indented
      })
      
      internalValue.value = formatted.join('\n')
    }
    
    ElMessage.success('代码格式化完成')
  } catch (error) {
    ElMessage.error('代码格式化失败')
  }
}

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(internalValue.value)
    ElMessage.success('代码已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textarea = document.createElement('textarea')
    textarea.value = internalValue.value
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
    ElMessage.success('代码已复制到剪贴板')
  }
}

const downloadCode = () => {
  const blob = new Blob([internalValue.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${props.filename}.${getFileExtension(internalLanguage.value)}`
  link.click()
  URL.revokeObjectURL(url)
}

// 工具函数
const getLanguageTagType = (language: string) => {
  const typeMap: Record<string, string> = {
    python: 'warning',
    javascript: 'success',
    typescript: 'primary',
    go: 'info',
    java: 'danger',
    json: 'success',
    yaml: 'warning'
  }
  return typeMap[language] || ''
}

const getLanguageLabel = (language: string) => {
  const labelMap: Record<string, string> = {
    python: 'Python',
    javascript: 'JavaScript',
    typescript: 'TypeScript',
    go: 'Go',
    java: 'Java',
    shell: 'Shell',
    json: 'JSON',
    yaml: 'YAML',
    markdown: 'Markdown',
    text: 'Plain Text'
  }
  return labelMap[language] || language.toUpperCase()
}

const getFileExtension = (language: string) => {
  const extMap: Record<string, string> = {
    python: 'py',
    javascript: 'js',
    typescript: 'ts',
    go: 'go',
    java: 'java',
    shell: 'sh',
    json: 'json',
    yaml: 'yml',
    markdown: 'md',
    text: 'txt'
  }
  return extMap[language] || 'txt'
}
</script>

<style lang="scss" scoped>
.code-editor-container {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
  background: var(--el-bg-color);
  
  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color-light);
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .editor-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .editor-content {
    position: relative;
    
    .code-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
        border: none;
        border-radius: 0;
        resize: vertical;
        padding-left: 50px; // 为行号留出空间
        
        &:focus {
          box-shadow: none;
        }
      }
      
      &.language-python :deep(.el-textarea__inner) {
        background-color: #fefefe;
      }
      
      &.language-javascript :deep(.el-textarea__inner) {
        background-color: #fffef7;
      }
      
      &.language-json :deep(.el-textarea__inner) {
        background-color: #f8fff8;
      }
    }
    
    .line-numbers {
      position: absolute;
      top: 1px;
      left: 1px;
      width: 45px;
      background: var(--el-bg-color-page);
      border-right: 1px solid var(--el-border-color-lighter);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
      color: var(--el-text-color-placeholder);
      user-select: none;
      padding: 5px 0;
      
      .line-number {
        text-align: right;
        padding: 0 8px;
        height: 19.5px; // 匹配textarea行高
      }
    }
  }
  
  .editor-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 12px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color-light);
    font-size: 12px;
    
    .footer-left {
      display: flex;
      gap: 16px;
      color: var(--el-text-color-secondary);
      
      .line-info,
      .char-count {
        font-family: monospace;
      }
    }
  }
}
</style>
