<template>
  <el-tag 
    :type="getRuntimeType(runtime)" 
    :size="size"
    :class="['runtime-badge', `runtime-${runtime}`]"
  >
    <el-icon v-if="showIcon" class="runtime-icon">
      <component :is="getRuntimeIcon(runtime)" />
    </el-icon>
    <span>{{ getRuntimeLabel(runtime) }}</span>
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Cpu,
  Monitor,
  DataAnalysis,
  Coffee
} from '@element-plus/icons-vue'

interface Props {
  runtime: string
  size?: 'large' | 'default' | 'small'
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  showIcon: true
})

const getRuntimeType = (runtime: string) => {
  const typeMap: Record<string, string> = {
    python: 'warning',
    nodejs: 'success',
    go: 'info',
    java: 'danger',
    shell: '',
    docker: 'primary'
  }
  return typeMap[runtime] || ''
}

const getRuntimeLabel = (runtime: string) => {
  const labelMap: Record<string, string> = {
    python: 'Python',
    nodejs: 'Node.js',
    go: 'Go',
    java: 'Java',
    shell: 'Shell',
    docker: 'Docker'
  }
  return labelMap[runtime] || runtime.toUpperCase()
}

const getRuntimeIcon = (runtime: string) => {
  const iconMap: Record<string, any> = {
    python: DataAnalysis,
    nodejs: Monitor,
    go: Cpu,
    java: Coffee,
    shell: Monitor,
    docker: Monitor
  }
  return iconMap[runtime] || Cpu
}
</script>

<style lang="scss" scoped>
.runtime-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  .runtime-icon {
    font-size: 12px;
  }
  
  &.runtime-python {
    --el-tag-bg-color: #fef7e6;
    --el-tag-border-color: #f4d03f;
    --el-tag-text-color: #b7950b;
  }
  
  &.runtime-nodejs {
    --el-tag-bg-color: #e8f5e8;
    --el-tag-border-color: #68d391;
    --el-tag-text-color: #2f855a;
  }
  
  &.runtime-go {
    --el-tag-bg-color: #e6f3ff;
    --el-tag-border-color: #74c0fc;
    --el-tag-text-color: #1971c2;
  }
  
  &.runtime-java {
    --el-tag-bg-color: #ffeaea;
    --el-tag-border-color: #ffa8a8;
    --el-tag-text-color: #c92a2a;
  }
}
</style>
