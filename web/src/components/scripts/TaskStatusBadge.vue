<template>
  <el-tag 
    :type="getStatusType(status)" 
    :size="size"
    :class="['task-status-badge', `status-${status}`]"
  >
    <el-icon v-if="showIcon" class="status-icon">
      <component :is="getStatusIcon(status)" />
    </el-icon>
    <span>{{ getStatusLabel(status) }}</span>
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Clock, 
  Loading, 
  SuccessFilled, 
  CircleCloseFilled, 
  WarningFilled 
} from '@element-plus/icons-vue'

interface Props {
  status: 'pending' | 'running' | 'completed' | 'failed' | 'canceled'
  size?: 'large' | 'default' | 'small'
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  showIcon: true
})

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    canceled: ''
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    canceled: '已取消'
  }
  return labelMap[status] || status
}

const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    pending: Clock,
    running: Loading,
    completed: SuccessFilled,
    failed: CircleCloseFilled,
    canceled: WarningFilled
  }
  return iconMap[status] || Clock
}
</script>

<style lang="scss" scoped>
.task-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  .status-icon {
    font-size: 12px;
  }
  
  &.status-running .status-icon {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
