<template>
  <div class="mobile-navigation">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
      <div class="header-content">
        <!-- 菜单按钮 -->
        <button
          @click="toggleSidebar"
          class="menu-button"
          :class="{ 'menu-active': sidebarOpen }"
        >
          <div class="hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>

        <!-- Logo -->
        <div class="logo-container">
          <img src="/logo.svg" alt="PaaS Platform" class="logo" />
          <span class="logo-text">PaaS</span>
        </div>

        <!-- 右侧操作 -->
        <div class="header-actions">
          <!-- 通知按钮 -->
          <button class="action-button" @click="showNotifications">
            <BellIcon class="action-icon" />
            <span v-if="notificationCount > 0" class="notification-badge">
              {{ notificationCount > 99 ? '99+' : notificationCount }}
            </span>
          </button>

          <!-- 用户头像 -->
          <button class="user-avatar" @click="showUserMenu">
            <img
              :src="userAvatar"
              :alt="userName"
              class="avatar-image"
            />
          </button>
        </div>
      </div>
    </header>

    <!-- 侧边栏遮罩 -->
    <div
      v-if="sidebarOpen"
      class="sidebar-overlay"
      @click="closeSidebar"
    ></div>

    <!-- 侧边栏 -->
    <aside
      class="mobile-sidebar"
      :class="{ 'sidebar-open': sidebarOpen }"
    >
      <div class="sidebar-content">
        <!-- 用户信息 -->
        <div class="user-section">
          <div class="user-info">
            <img
              :src="userAvatar"
              :alt="userName"
              class="user-avatar-large"
            />
            <div class="user-details">
              <h3 class="user-name">{{ userName }}</h3>
              <p class="user-role">{{ userRole }}</p>
            </div>
          </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
          <div class="nav-section">
            <h4 class="section-title">主要功能</h4>
            
            <MobileNavItem
              :to="{ name: 'dashboard' }"
              :icon="HomeIcon"
              title="仪表板"
              @click="closeSidebar"
            />

            <MobileNavGroup
              :icon="CubeIcon"
              title="应用管理"
              :items="[
                { to: { name: 'apps' }, title: '应用列表' },
                { to: { name: 'app-create' }, title: '创建应用' },
                { to: { name: 'app-templates' }, title: '应用模板' }
              ]"
              @item-click="closeSidebar"
            />

            <MobileNavGroup
              :icon="CogIcon"
              title="CI/CD"
              :items="[
                { to: { name: 'pipelines' }, title: '流水线' },
                { to: { name: 'builds' }, title: '构建历史' },
                { to: { name: 'deployments' }, title: '部署记录' }
              ]"
              @item-click="closeSidebar"
            />

            <MobileNavGroup
              :icon="ChartBarIcon"
              title="监控告警"
              :items="[
                { to: { name: 'monitoring' }, title: '系统监控' },
                { to: { name: 'alerts' }, title: '告警管理' },
                { to: { name: 'logs' }, title: '日志查看' }
              ]"
              @item-click="closeSidebar"
            />

            <MobileNavGroup
              :icon="UsersIcon"
              title="用户管理"
              :items="[
                { to: { name: 'users' }, title: '用户列表' },
                { to: { name: 'roles' }, title: '角色权限' },
                { to: { name: 'teams' }, title: '团队管理' }
              ]"
              @item-click="closeSidebar"
            />
          </div>

          <div class="nav-section">
            <h4 class="section-title">系统设置</h4>
            
            <MobileNavItem
              :to="{ name: 'settings' }"
              :icon="Cog6ToothIcon"
              title="系统配置"
              @click="closeSidebar"
            />

            <MobileNavItem
              :to="{ name: 'security' }"
              :icon="ShieldCheckIcon"
              title="安全中心"
              @click="closeSidebar"
            />

            <MobileNavItem
              :to="{ name: 'audit' }"
              :icon="DocumentTextIcon"
              title="审计日志"
              @click="closeSidebar"
            />
          </div>
        </nav>

        <!-- 底部操作 -->
        <div class="sidebar-footer">
          <!-- 主题切换 -->
          <button @click="toggleTheme" class="theme-toggle">
            <SunIcon v-if="isDark" class="theme-icon" />
            <MoonIcon v-else class="theme-icon" />
            <span class="theme-text">
              {{ isDark ? '浅色模式' : '深色模式' }}
            </span>
          </button>

          <!-- 退出登录 -->
          <button @click="logout" class="logout-button">
            <ArrowRightOnRectangleIcon class="logout-icon" />
            <span>退出登录</span>
          </button>
        </div>
      </div>
    </aside>

    <!-- 底部导航栏 -->
    <nav class="bottom-navigation">
      <router-link
        v-for="item in bottomNavItems"
        :key="item.name"
        :to="item.to"
        class="bottom-nav-item"
        :class="{ 'nav-item-active': isActiveRoute(item.to) }"
      >
        <component :is="item.icon" class="nav-item-icon" />
        <span class="nav-item-text">{{ item.title }}</span>
      </router-link>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  HomeIcon,
  CubeIcon,
  CogIcon,
  ChartBarIcon,
  UsersIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  BellIcon,
  SunIcon,
  MoonIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/vue/24/outline'
import MobileNavItem from './MobileNavItem.vue'
import MobileNavGroup from './MobileNavGroup.vue'
import { themeManager } from '@/styles/theme'

// 响应式数据
const sidebarOpen = ref(false)
const isDark = ref(themeManager.getCurrentTheme().name === 'dark')
const notificationCount = ref(5) // 示例数据

// 用户信息（实际项目中应该从状态管理获取）
const userName = ref('管理员')
const userRole = ref('系统管理员')
const userAvatar = ref('/default-avatar.png')

// 路由
const route = useRoute()
const router = useRouter()

// 底部导航项
const bottomNavItems = [
  {
    name: 'dashboard',
    title: '首页',
    icon: HomeIcon,
    to: { name: 'dashboard' },
  },
  {
    name: 'apps',
    title: '应用',
    icon: CubeIcon,
    to: { name: 'apps' },
  },
  {
    name: 'monitoring',
    title: '监控',
    icon: ChartBarIcon,
    to: { name: 'monitoring' },
  },
  {
    name: 'settings',
    title: '设置',
    icon: Cog6ToothIcon,
    to: { name: 'settings' },
  },
]

// 方法
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const toggleTheme = () => {
  themeManager.toggleTheme()
  isDark.value = themeManager.getCurrentTheme().name === 'dark'
}

const showNotifications = () => {
  // 显示通知面板
  console.log('显示通知')
}

const showUserMenu = () => {
  // 显示用户菜单
  console.log('显示用户菜单')
}

const logout = () => {
  // 退出登录逻辑
  console.log('退出登录')
  router.push({ name: 'login' })
}

const isActiveRoute = (to: any) => {
  if (typeof to === 'string') {
    return route.path === to
  } else if (to.name) {
    return route.name === to.name
  }
  return false
}

// 监听主题变化
themeManager.addListener((theme) => {
  isDark.value = theme.name === 'dark'
})
</script>

<style scoped>
/* 移动端导航基础样式 */
.mobile-navigation {
  @apply relative;
}

/* 顶部导航栏 */
.mobile-header {
  @apply fixed top-0 left-0 right-0 z-50;
  @apply bg-white border-b border-gray-200 shadow-sm;
  @apply safe-area-inset-top;
}

.theme-dark .mobile-header {
  @apply bg-gray-900 border-gray-700;
}

.header-content {
  @apply flex items-center justify-between px-4 py-3;
  @apply h-14;
}

/* 菜单按钮 */
.menu-button {
  @apply p-2 rounded-md transition-colors duration-200;
  @apply hover:bg-gray-100 active:bg-gray-200;
}

.theme-dark .menu-button {
  @apply hover:bg-gray-800 active:bg-gray-700;
}

.hamburger {
  @apply w-6 h-6 flex flex-col justify-center items-center;
}

.hamburger span {
  @apply block w-5 h-0.5 bg-gray-600 transition-all duration-300;
  @apply mb-1 last:mb-0;
}

.theme-dark .hamburger span {
  @apply bg-gray-300;
}

.menu-active .hamburger span:nth-child(1) {
  @apply transform rotate-45 translate-y-1.5;
}

.menu-active .hamburger span:nth-child(2) {
  @apply opacity-0;
}

.menu-active .hamburger span:nth-child(3) {
  @apply transform -rotate-45 -translate-y-1.5;
}

/* Logo */
.logo-container {
  @apply flex items-center gap-2;
}

.logo {
  @apply w-8 h-8;
}

.logo-text {
  @apply text-lg font-bold text-gray-900;
}

.theme-dark .logo-text {
  @apply text-gray-100;
}

/* 头部操作 */
.header-actions {
  @apply flex items-center gap-2;
}

.action-button {
  @apply relative p-2 rounded-md transition-colors duration-200;
  @apply hover:bg-gray-100 active:bg-gray-200;
}

.theme-dark .action-button {
  @apply hover:bg-gray-800 active:bg-gray-700;
}

.action-icon {
  @apply w-6 h-6 text-gray-600;
}

.theme-dark .action-icon {
  @apply text-gray-300;
}

.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs;
  @apply rounded-full min-w-5 h-5 flex items-center justify-center;
  @apply px-1;
}

.user-avatar {
  @apply p-1 rounded-full;
}

.avatar-image {
  @apply w-8 h-8 rounded-full object-cover;
}

/* 侧边栏遮罩 */
.sidebar-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  @apply transition-opacity duration-300;
}

/* 侧边栏 */
.mobile-sidebar {
  @apply fixed top-0 left-0 bottom-0 w-80 max-w-[85vw] z-50;
  @apply bg-white shadow-xl transform -translate-x-full transition-transform duration-300;
  @apply safe-area-inset-left;
}

.theme-dark .mobile-sidebar {
  @apply bg-gray-900;
}

.sidebar-open {
  @apply translate-x-0;
}

.sidebar-content {
  @apply flex flex-col h-full overflow-hidden;
}

/* 用户信息区域 */
.user-section {
  @apply p-4 border-b border-gray-200;
}

.theme-dark .user-section {
  @apply border-gray-700;
}

.user-info {
  @apply flex items-center gap-3;
}

.user-avatar-large {
  @apply w-12 h-12 rounded-full object-cover;
}

.user-details {
  @apply flex-1 min-w-0;
}

.user-name {
  @apply text-lg font-semibold text-gray-900 truncate;
}

.theme-dark .user-name {
  @apply text-gray-100;
}

.user-role {
  @apply text-sm text-gray-500 truncate;
}

.theme-dark .user-role {
  @apply text-gray-400;
}

/* 导航菜单 */
.sidebar-nav {
  @apply flex-1 overflow-y-auto py-4;
}

.nav-section {
  @apply mb-6;
}

.section-title {
  @apply px-4 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider;
}

.theme-dark .section-title {
  @apply text-gray-400;
}

/* 侧边栏底部 */
.sidebar-footer {
  @apply p-4 border-t border-gray-200 space-y-2;
}

.theme-dark .sidebar-footer {
  @apply border-gray-700;
}

.theme-toggle,
.logout-button {
  @apply flex items-center gap-3 w-full p-3 text-left rounded-md;
  @apply text-gray-700 hover:bg-gray-100 transition-colors duration-200;
}

.theme-dark .theme-toggle,
.theme-dark .logout-button {
  @apply text-gray-300 hover:bg-gray-800;
}

.theme-icon,
.logout-icon {
  @apply w-5 h-5 flex-shrink-0;
}

.theme-text {
  @apply text-sm font-medium;
}

/* 底部导航栏 */
.bottom-navigation {
  @apply fixed bottom-0 left-0 right-0 z-40;
  @apply bg-white border-t border-gray-200 shadow-lg;
  @apply flex items-center justify-around;
  @apply safe-area-inset-bottom;
  @apply pb-safe;
}

.theme-dark .bottom-navigation {
  @apply bg-gray-900 border-gray-700;
}

.bottom-nav-item {
  @apply flex flex-col items-center justify-center py-2 px-3;
  @apply text-gray-500 hover:text-gray-700 transition-colors duration-200;
  @apply min-w-0 flex-1;
}

.theme-dark .bottom-nav-item {
  @apply text-gray-400 hover:text-gray-200;
}

.nav-item-active {
  @apply text-blue-600;
}

.theme-dark .nav-item-active {
  @apply text-blue-400;
}

.nav-item-icon {
  @apply w-6 h-6 mb-1;
}

.nav-item-text {
  @apply text-xs font-medium truncate;
}

/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.pb-safe {
  padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
}

/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  @apply w-1;
}

.sidebar-nav::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.theme-dark .sidebar-nav::-webkit-scrollbar-thumb {
  @apply bg-gray-600;
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .mobile-sidebar,
  .sidebar-overlay,
  .hamburger span {
    @apply transition-none;
  }
}

/* 触摸优化 */
@media (hover: none) {
  .menu-button:hover,
  .action-button:hover,
  .theme-toggle:hover,
  .logout-button:hover,
  .bottom-nav-item:hover {
    @apply bg-transparent;
  }
}
</style>
