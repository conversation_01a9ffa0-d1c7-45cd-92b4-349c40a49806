<template>
  <component
    :is="linkComponent"
    v-bind="linkProps"
    :class="itemClasses"
    @click="handleClick"
  >
    <!-- 图标 -->
    <component
      v-if="icon"
      :is="icon"
      class="nav-item-icon"
    />
    
    <!-- 标题 -->
    <span class="nav-item-title">{{ title }}</span>
    
    <!-- 徽章 -->
    <span v-if="badge" class="nav-item-badge">
      {{ badge }}
    </span>
    
    <!-- 箭头图标 -->
    <ChevronRightIcon
      v-if="showArrow"
      class="nav-item-arrow"
    />
  </component>
</template>

<script setup lang="ts">
import { computed, type Component } from 'vue'
import { useRoute } from 'vue-router'
import { ChevronRightIcon } from '@heroicons/vue/24/outline'

// 定义属性
interface Props {
  to?: string | object
  href?: string
  icon?: Component | string
  title: string
  badge?: string | number
  disabled?: boolean
  showArrow?: boolean
  exact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showArrow: false,
  exact: false,
})

// 定义事件
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 路由
const route = useRoute()

// 计算属性
const linkComponent = computed(() => {
  if (props.href) {
    return 'a'
  } else if (props.to) {
    return 'router-link'
  } else {
    return 'button'
  }
})

const linkProps = computed(() => {
  if (props.href) {
    return {
      href: props.href,
      target: '_blank',
      rel: 'noopener noreferrer',
    }
  } else if (props.to) {
    return {
      to: props.to,
      exact: props.exact,
    }
  } else {
    return {
      type: 'button',
    }
  }
})

const isActive = computed(() => {
  if (!props.to) return false
  
  if (typeof props.to === 'string') {
    return props.exact 
      ? route.path === props.to
      : route.path.startsWith(props.to)
  } else if (props.to.name) {
    return route.name === props.to.name
  }
  
  return false
})

const itemClasses = computed(() => {
  const classes = [
    'mobile-nav-item',
    {
      'nav-item-active': isActive.value,
      'nav-item-disabled': props.disabled,
    },
  ]
  return classes
})

// 方法
const handleClick = (event: MouseEvent) => {
  if (props.disabled) {
    event.preventDefault()
    return
  }
  
  emit('click', event)
}
</script>

<style scoped>
/* 移动端导航项样式 */
.mobile-nav-item {
  @apply flex items-center gap-3 px-4 py-3 mx-2 rounded-lg;
  @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  @apply transition-all duration-200 ease-in-out;
  @apply w-auto text-left;
  @apply touch-manipulation;
}

.theme-dark .mobile-nav-item {
  @apply text-gray-300 hover:bg-gray-800 active:bg-gray-700;
}

/* 激活状态 */
.nav-item-active {
  @apply bg-blue-50 text-blue-700 font-medium;
}

.theme-dark .nav-item-active {
  @apply bg-blue-900 bg-opacity-20 text-blue-400;
}

/* 禁用状态 */
.nav-item-disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* 图标样式 */
.nav-item-icon {
  @apply w-6 h-6 flex-shrink-0 text-gray-500;
}

.nav-item-active .nav-item-icon {
  @apply text-blue-600;
}

.theme-dark .nav-item-icon {
  @apply text-gray-400;
}

.theme-dark .nav-item-active .nav-item-icon {
  @apply text-blue-400;
}

/* 标题样式 */
.nav-item-title {
  @apply flex-1 text-base font-medium truncate;
}

/* 徽章样式 */
.nav-item-badge {
  @apply px-2 py-0.5 text-xs font-medium rounded-full;
  @apply bg-gray-100 text-gray-600;
}

.nav-item-active .nav-item-badge {
  @apply bg-blue-100 text-blue-800;
}

.theme-dark .nav-item-badge {
  @apply bg-gray-700 text-gray-300;
}

.theme-dark .nav-item-active .nav-item-badge {
  @apply bg-blue-900 bg-opacity-50 text-blue-300;
}

/* 箭头图标样式 */
.nav-item-arrow {
  @apply w-5 h-5 text-gray-400 flex-shrink-0;
}

.nav-item-active .nav-item-arrow {
  @apply text-blue-500;
}

.theme-dark .nav-item-arrow {
  @apply text-gray-500;
}

.theme-dark .nav-item-active .nav-item-arrow {
  @apply text-blue-400;
}

/* 链接样式重置 */
a.mobile-nav-item {
  @apply no-underline;
}

button.mobile-nav-item {
  @apply border-none bg-transparent;
}

/* 触摸优化 */
@media (hover: none) {
  .mobile-nav-item:hover {
    @apply bg-transparent;
  }
  
  .mobile-nav-item:active {
    @apply bg-gray-200;
  }
  
  .theme-dark .mobile-nav-item:active {
    @apply bg-gray-700;
  }
}

/* 焦点状态 */
.mobile-nav-item:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.theme-dark .mobile-nav-item:focus {
  @apply ring-offset-gray-900;
}

/* 按压效果 */
.mobile-nav-item:active {
  @apply transform scale-95;
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .mobile-nav-item {
    @apply transition-none transform-none;
  }
}
</style>
