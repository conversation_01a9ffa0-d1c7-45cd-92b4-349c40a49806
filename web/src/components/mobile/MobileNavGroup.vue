<template>
  <div class="mobile-nav-group">
    <!-- 组标题 -->
    <button
      class="nav-group-header"
      @click="toggleExpanded"
    >
      <!-- 图标 -->
      <component
        v-if="icon"
        :is="icon"
        class="group-icon"
      />
      
      <!-- 标题 -->
      <span class="group-title">{{ title }}</span>
      
      <!-- 展开/收起图标 -->
      <ChevronDownIcon
        class="expand-icon"
        :class="{ 'expand-icon-rotated': isExpanded }"
      />
    </button>

    <!-- 子项列表 -->
    <div
      class="nav-group-items"
      :class="{ 'items-expanded': isExpanded }"
    >
      <div class="items-container">
        <MobileNavItem
          v-for="item in items"
          :key="getItemKey(item)"
          v-bind="item"
          class="group-item"
          @click="handleItemClick(item)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, type Component } from 'vue'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'
import MobileNavItem from './MobileNavItem.vue'

// 定义导航项接口
interface NavItem {
  to?: string | object
  href?: string
  title: string
  badge?: string | number
  disabled?: boolean
}

// 定义属性
interface Props {
  icon?: Component | string
  title: string
  items: NavItem[]
  defaultExpanded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultExpanded: false,
})

// 定义事件
const emit = defineEmits<{
  itemClick: [item: NavItem]
  toggle: [expanded: boolean]
}>()

// 响应式数据
const isExpanded = ref(props.defaultExpanded)

// 计算属性
const hasItems = computed(() => props.items.length > 0)

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  emit('toggle', isExpanded.value)
}

const handleItemClick = (item: NavItem) => {
  emit('itemClick', item)
}

const getItemKey = (item: NavItem) => {
  if (typeof item.to === 'string') {
    return item.to
  } else if (item.to && typeof item.to === 'object' && 'name' in item.to) {
    return item.to.name
  } else if (item.href) {
    return item.href
  } else {
    return item.title
  }
}
</script>

<style scoped>
/* 移动端导航组样式 */
.mobile-nav-group {
  @apply mb-1;
}

/* 组标题 */
.nav-group-header {
  @apply flex items-center gap-3 px-4 py-3 mx-2 rounded-lg;
  @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  @apply transition-all duration-200 ease-in-out;
  @apply w-auto text-left;
  @apply touch-manipulation;
}

.theme-dark .nav-group-header {
  @apply text-gray-300 hover:bg-gray-800 active:bg-gray-700;
}

/* 组图标 */
.group-icon {
  @apply w-6 h-6 flex-shrink-0 text-gray-500;
}

.theme-dark .group-icon {
  @apply text-gray-400;
}

/* 组标题文本 */
.group-title {
  @apply flex-1 text-base font-medium truncate;
}

/* 展开图标 */
.expand-icon {
  @apply w-5 h-5 text-gray-400 transition-transform duration-200 flex-shrink-0;
}

.expand-icon-rotated {
  @apply transform rotate-180;
}

/* 子项容器 */
.nav-group-items {
  @apply overflow-hidden transition-all duration-300 ease-in-out;
  @apply max-h-0 opacity-0;
}

.items-expanded {
  @apply max-h-96 opacity-100;
}

.items-container {
  @apply py-1;
}

/* 组内子项样式 */
.group-item {
  @apply ml-6 mr-2;
  @apply border-l border-gray-200 pl-4;
}

.theme-dark .group-item {
  @apply border-gray-700;
}

/* 子项图标调整 */
.group-item :deep(.nav-item-icon) {
  @apply w-5 h-5;
}

/* 子项标题调整 */
.group-item :deep(.nav-item-title) {
  @apply text-sm;
}

/* 按钮样式重置 */
.nav-group-header {
  @apply border-none bg-transparent;
}

/* 触摸优化 */
@media (hover: none) {
  .nav-group-header:hover {
    @apply bg-transparent;
  }
  
  .nav-group-header:active {
    @apply bg-gray-200;
  }
  
  .theme-dark .nav-group-header:active {
    @apply bg-gray-700;
  }
}

/* 焦点状态 */
.nav-group-header:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.theme-dark .nav-group-header:focus {
  @apply ring-offset-gray-900;
}

/* 按压效果 */
.nav-group-header:active {
  @apply transform scale-95;
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .nav-group-header,
  .nav-group-items,
  .expand-icon {
    @apply transition-none transform-none;
  }
}

/* 展开动画 */
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 384px;
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    max-height: 384px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

.items-expanded {
  animation: slideDown 0.3s ease-in-out;
}

.nav-group-items:not(.items-expanded) {
  animation: slideUp 0.3s ease-in-out;
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .items-expanded,
  .nav-group-items:not(.items-expanded) {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .nav-group-header {
    @apply border border-gray-400;
  }
  
  .group-item {
    @apply border-l-2 border-gray-600;
  }
}
</style>
