<template>
  <div class="realtime-chart" :class="{ 'chart-loading': loading }">
    <!-- 图表头部 -->
    <div v-if="showHeader" class="chart-header">
      <div class="chart-title-section">
        <h3 v-if="title" class="chart-title">{{ title }}</h3>
        <p v-if="description" class="chart-description">{{ description }}</p>
      </div>
      
      <div class="chart-controls">
        <!-- 时间范围选择 -->
        <select
          v-model="timeRange"
          class="time-range-select"
          @change="handleTimeRangeChange"
        >
          <option value="1h">{{ $t('charts.lastHour') }}</option>
          <option value="6h">{{ $t('charts.last6Hours') }}</option>
          <option value="24h">{{ $t('charts.last24Hours') }}</option>
          <option value="7d">{{ $t('charts.last7Days') }}</option>
        </select>
        
        <!-- 刷新控制 -->
        <button
          @click="toggleAutoRefresh"
          class="refresh-button"
          :class="{ 'refresh-active': autoRefresh }"
          :title="autoRefresh ? $t('charts.pauseRefresh') : $t('charts.startRefresh')"
        >
          <PlayIcon v-if="!autoRefresh" class="control-icon" />
          <PauseIcon v-else class="control-icon" />
        </button>
        
        <!-- 手动刷新 -->
        <button
          @click="refreshData"
          class="manual-refresh-button"
          :disabled="loading"
          :title="$t('charts.refreshData')"
        >
          <ArrowPathIcon class="control-icon" :class="{ 'icon-spinning': loading }" />
        </button>
        
        <!-- 全屏切换 -->
        <button
          @click="toggleFullscreen"
          class="fullscreen-button"
          :title="isFullscreen ? $t('charts.exitFullscreen') : $t('charts.enterFullscreen')"
        >
          <ArrowsPointingOutIcon v-if="!isFullscreen" class="control-icon" />
          <ArrowsPointingInIcon v-else class="control-icon" />
        </button>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div
      ref="chartContainer"
      class="chart-container"
      :style="{ height: chartHeight }"
    >
      <!-- 加载状态 -->
      <div v-if="loading && !hasData" class="chart-loading-overlay">
        <div class="loading-spinner">
          <svg class="animate-spin" viewBox="0 0 24 24">
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="2"
              fill="none"
              stroke-dasharray="32"
              stroke-dashoffset="32"
            />
          </svg>
        </div>
        <p class="loading-text">{{ $t('charts.loadingData') }}</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="chart-error">
        <ExclamationTriangleIcon class="error-icon" />
        <p class="error-message">{{ error }}</p>
        <button @click="refreshData" class="retry-button">
          {{ $t('common.retry') }}
        </button>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else-if="!hasData" class="chart-no-data">
        <ChartBarIcon class="no-data-icon" />
        <p class="no-data-message">{{ $t('charts.noData') }}</p>
      </div>
      
      <!-- 图表画布 -->
      <canvas
        v-else
        ref="chartCanvas"
        class="chart-canvas"
        :width="canvasWidth"
        :height="canvasHeight"
      ></canvas>
      
      <!-- 实时指示器 -->
      <div v-if="isRealtime && autoRefresh" class="realtime-indicator">
        <div class="indicator-dot"></div>
        <span class="indicator-text">{{ $t('charts.live') }}</span>
      </div>
      
      <!-- 数据点提示 -->
      <div
        v-if="tooltip.visible"
        ref="tooltipRef"
        class="chart-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-content">
          <div class="tooltip-time">{{ tooltip.time }}</div>
          <div class="tooltip-values">
            <div
              v-for="(value, index) in tooltip.values"
              :key="index"
              class="tooltip-value"
            >
              <span
                class="value-color"
                :style="{ backgroundColor: value.color }"
              ></span>
              <span class="value-label">{{ value.label }}:</span>
              <span class="value-number">{{ value.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图例 -->
    <div v-if="showLegend && series.length > 1" class="chart-legend">
      <div
        v-for="(serie, index) in series"
        :key="index"
        class="legend-item"
        @click="toggleSeries(index)"
      >
        <span
          class="legend-color"
          :style="{ backgroundColor: serie.color }"
        ></span>
        <span class="legend-label" :class="{ 'legend-disabled': !serie.visible }">
          {{ serie.label }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import {
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
} from '@heroicons/vue/24/outline'
import { useRealtimeData } from '@/composables/useWebSocket'
import { useDataCache } from '@/composables/useDataCache'

// 数据点接口
interface DataPoint {
  timestamp: number
  value: number
}

// 数据系列接口
interface DataSeries {
  label: string
  color: string
  data: DataPoint[]
  visible: boolean
}

// 提示框数据
interface TooltipData {
  visible: boolean
  x: number
  y: number
  time: string
  values: Array<{
    label: string
    value: string
    color: string
  }>
}

// 定义属性
interface Props {
  title?: string
  description?: string
  endpoint: string
  series: DataSeries[]
  height?: string
  showHeader?: boolean
  showLegend?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
  maxDataPoints?: number
  isRealtime?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: '300px',
  showHeader: true,
  showLegend: true,
  autoRefresh: true,
  refreshInterval: 5000,
  maxDataPoints: 100,
  isRealtime: true,
})

// 定义事件
const emit = defineEmits<{
  dataUpdate: [data: DataSeries[]]
  error: [error: string]
  fullscreenChange: [isFullscreen: boolean]
}>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chartCanvas = ref<HTMLCanvasElement>()
const tooltipRef = ref<HTMLElement>()
const loading = ref(false)
const error = ref<string | null>(null)
const timeRange = ref('1h')
const autoRefresh = ref(props.autoRefresh)
const isFullscreen = ref(false)
const canvasWidth = ref(800)
const canvasHeight = ref(300)

// 提示框数据
const tooltip = ref<TooltipData>({
  visible: false,
  x: 0,
  y: 0,
  time: '',
  values: [],
})

// WebSocket 连接
const { connect, disconnect, onMessage, isConnected } = useRealtimeData(props.endpoint)

// 数据缓存
const { get: getCachedData, set: setCachedData } = useDataCache()

// 计算属性
const chartHeight = computed(() => props.height)
const hasData = computed(() => props.series.some(s => s.data.length > 0))

const tooltipStyle = computed(() => ({
  left: `${tooltip.value.x}px`,
  top: `${tooltip.value.y}px`,
}))

// 方法
const handleTimeRangeChange = () => {
  refreshData()
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value && props.isRealtime) {
    connect()
  } else {
    disconnect()
  }
}

const refreshData = async () => {
  loading.value = true
  error.value = null
  
  try {
    // 这里应该调用实际的 API 获取数据
    // 示例数据生成
    const now = Date.now()
    const timeRangeMs = getTimeRangeInMs(timeRange.value)
    const interval = timeRangeMs / props.maxDataPoints
    
    const newData = props.series.map(serie => ({
      ...serie,
      data: Array.from({ length: props.maxDataPoints }, (_, i) => ({
        timestamp: now - timeRangeMs + (i * interval),
        value: Math.random() * 100,
      })),
    }))
    
    // 缓存数据
    setCachedData(`chart-${props.endpoint}-${timeRange.value}`, newData, {
      ttl: 30000, // 30秒
    })
    
    emit('dataUpdate', newData)
    
  } catch (err) {
    error.value = err instanceof Error ? err.message : '数据加载失败'
    emit('error', error.value)
  } finally {
    loading.value = false
  }
}

const getTimeRangeInMs = (range: string): number => {
  const ranges: Record<string, number> = {
    '1h': 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
  }
  return ranges[range] || ranges['1h']
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('fullscreenChange', isFullscreen.value)
  
  nextTick(() => {
    updateCanvasSize()
    drawChart()
  })
}

const toggleSeries = (index: number) => {
  const series = [...props.series]
  series[index].visible = !series[index].visible
  emit('dataUpdate', series)
  drawChart()
}

const updateCanvasSize = () => {
  if (!chartContainer.value || !chartCanvas.value) return
  
  const rect = chartContainer.value.getBoundingClientRect()
  canvasWidth.value = rect.width
  canvasHeight.value = rect.height
  
  // 设置高 DPI 支持
  const dpr = window.devicePixelRatio || 1
  chartCanvas.value.width = canvasWidth.value * dpr
  chartCanvas.value.height = canvasHeight.value * dpr
  
  const ctx = chartCanvas.value.getContext('2d')
  if (ctx) {
    ctx.scale(dpr, dpr)
  }
}

const drawChart = () => {
  if (!chartCanvas.value || !hasData.value) return
  
  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return
  
  // 清空画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
  
  // 绘制网格
  drawGrid(ctx)
  
  // 绘制数据线
  props.series.forEach(serie => {
    if (serie.visible && serie.data.length > 0) {
      drawLine(ctx, serie)
    }
  })
  
  // 绘制坐标轴
  drawAxes(ctx)
}

const drawGrid = (ctx: CanvasRenderingContext2D) => {
  const padding = 40
  const gridColor = '#e5e7eb'
  
  ctx.strokeStyle = gridColor
  ctx.lineWidth = 1
  
  // 垂直网格线
  for (let i = 0; i <= 10; i++) {
    const x = padding + (i * (canvasWidth.value - 2 * padding)) / 10
    ctx.beginPath()
    ctx.moveTo(x, padding)
    ctx.lineTo(x, canvasHeight.value - padding)
    ctx.stroke()
  }
  
  // 水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = padding + (i * (canvasHeight.value - 2 * padding)) / 5
    ctx.beginPath()
    ctx.moveTo(padding, y)
    ctx.lineTo(canvasWidth.value - padding, y)
    ctx.stroke()
  }
}

const drawLine = (ctx: CanvasRenderingContext2D, serie: DataSeries) => {
  if (serie.data.length < 2) return
  
  const padding = 40
  const chartWidth = canvasWidth.value - 2 * padding
  const chartHeight = canvasHeight.value - 2 * padding
  
  // 计算数据范围
  const minTime = Math.min(...serie.data.map(d => d.timestamp))
  const maxTime = Math.max(...serie.data.map(d => d.timestamp))
  const minValue = Math.min(...serie.data.map(d => d.value))
  const maxValue = Math.max(...serie.data.map(d => d.value))
  
  const timeRange = maxTime - minTime
  const valueRange = maxValue - minValue
  
  ctx.strokeStyle = serie.color
  ctx.lineWidth = 2
  ctx.beginPath()
  
  serie.data.forEach((point, index) => {
    const x = padding + ((point.timestamp - minTime) / timeRange) * chartWidth
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  
  ctx.stroke()
}

const drawAxes = (ctx: CanvasRenderingContext2D) => {
  const padding = 40
  
  ctx.strokeStyle = '#374151'
  ctx.lineWidth = 2
  
  // X轴
  ctx.beginPath()
  ctx.moveTo(padding, canvasHeight.value - padding)
  ctx.lineTo(canvasWidth.value - padding, canvasHeight.value - padding)
  ctx.stroke()
  
  // Y轴
  ctx.beginPath()
  ctx.moveTo(padding, padding)
  ctx.lineTo(padding, canvasHeight.value - padding)
  ctx.stroke()
}

// 鼠标事件处理
const handleMouseMove = (event: MouseEvent) => {
  if (!chartCanvas.value || !hasData.value) return
  
  const rect = chartCanvas.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 这里可以实现鼠标悬停显示数据点的逻辑
  // 简化实现，实际项目中需要更精确的计算
  tooltip.value = {
    visible: true,
    x: x + 10,
    y: y - 10,
    time: new Date().toLocaleTimeString(),
    values: props.series
      .filter(s => s.visible)
      .map(s => ({
        label: s.label,
        value: '100',
        color: s.color,
      })),
  }
}

const handleMouseLeave = () => {
  tooltip.value.visible = false
}

// 监听器
watch(() => props.series, drawChart, { deep: true })
watch(isConnected, (connected) => {
  if (connected && props.isRealtime) {
    // 监听实时数据
    onMessage('data', (message) => {
      // 处理实时数据更新
      console.log('Received realtime data:', message)
    })
  }
})

// 生命周期
onMounted(() => {
  updateCanvasSize()
  refreshData()
  
  if (autoRefresh.value && props.isRealtime) {
    connect()
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateCanvasSize)
  
  // 监听鼠标事件
  if (chartCanvas.value) {
    chartCanvas.value.addEventListener('mousemove', handleMouseMove)
    chartCanvas.value.addEventListener('mouseleave', handleMouseLeave)
  }
})

onUnmounted(() => {
  disconnect()
  window.removeEventListener('resize', updateCanvasSize)
  
  if (chartCanvas.value) {
    chartCanvas.value.removeEventListener('mousemove', handleMouseMove)
    chartCanvas.value.removeEventListener('mouseleave', handleMouseLeave)
  }
})
</script>

<style scoped>
/* 实时图表样式 */
.realtime-chart {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden;
}

.theme-dark .realtime-chart {
  @apply bg-gray-800 border-gray-700;
}

.chart-loading {
  @apply opacity-75;
}

/* 图表头部 */
.chart-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.theme-dark .chart-header {
  @apply border-gray-700;
}

.chart-title-section {
  @apply flex-1 min-w-0;
}

.chart-title {
  @apply text-lg font-semibold text-gray-900 truncate;
}

.theme-dark .chart-title {
  @apply text-gray-100;
}

.chart-description {
  @apply text-sm text-gray-500 mt-1;
}

.theme-dark .chart-description {
  @apply text-gray-400;
}

.chart-controls {
  @apply flex items-center gap-2;
}

.time-range-select {
  @apply px-3 py-1 text-sm border border-gray-300 rounded;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.theme-dark .time-range-select {
  @apply bg-gray-700 border-gray-600 text-gray-200;
}

.refresh-button,
.manual-refresh-button,
.fullscreen-button {
  @apply p-2 text-gray-500 hover:text-gray-700 rounded;
  @apply transition-colors duration-200;
}

.theme-dark .refresh-button,
.theme-dark .manual-refresh-button,
.theme-dark .fullscreen-button {
  @apply text-gray-400 hover:text-gray-200;
}

.refresh-active {
  @apply text-green-600 bg-green-50;
}

.theme-dark .refresh-active {
  @apply text-green-400 bg-green-900 bg-opacity-20;
}

.control-icon {
  @apply w-5 h-5;
}

.icon-spinning {
  @apply animate-spin;
}

/* 图表容器 */
.chart-container {
  @apply relative;
}

.chart-canvas {
  @apply w-full h-full;
}

/* 加载状态 */
.chart-loading-overlay {
  @apply absolute inset-0 flex flex-col items-center justify-center;
  @apply bg-white bg-opacity-75;
}

.theme-dark .chart-loading-overlay {
  @apply bg-gray-800 bg-opacity-75;
}

.loading-spinner {
  @apply w-8 h-8 text-blue-600 mb-2;
}

.loading-spinner svg {
  @apply w-full h-full;
}

.loading-text {
  @apply text-sm text-gray-600;
}

.theme-dark .loading-text {
  @apply text-gray-400;
}

/* 错误状态 */
.chart-error {
  @apply absolute inset-0 flex flex-col items-center justify-center;
  @apply text-center p-4;
}

.error-icon {
  @apply w-12 h-12 text-red-500 mb-2;
}

.error-message {
  @apply text-sm text-gray-600 mb-4;
}

.theme-dark .error-message {
  @apply text-gray-400;
}

.retry-button {
  @apply px-4 py-2 text-sm text-blue-600 border border-blue-300 rounded;
  @apply hover:bg-blue-50;
}

.theme-dark .retry-button {
  @apply text-blue-400 border-blue-600 hover:bg-blue-900 hover:bg-opacity-20;
}

/* 无数据状态 */
.chart-no-data {
  @apply absolute inset-0 flex flex-col items-center justify-center;
  @apply text-center p-4;
}

.no-data-icon {
  @apply w-12 h-12 text-gray-400 mb-2;
}

.no-data-message {
  @apply text-sm text-gray-500;
}

.theme-dark .no-data-message {
  @apply text-gray-400;
}

/* 实时指示器 */
.realtime-indicator {
  @apply absolute top-2 right-2 flex items-center gap-1;
  @apply px-2 py-1 bg-green-100 text-green-800 text-xs rounded;
}

.theme-dark .realtime-indicator {
  @apply bg-green-900 bg-opacity-20 text-green-400;
}

.indicator-dot {
  @apply w-2 h-2 bg-green-500 rounded-full animate-pulse;
}

/* 提示框 */
.chart-tooltip {
  @apply absolute z-10 pointer-events-none;
}

.tooltip-content {
  @apply bg-gray-900 text-white text-xs rounded px-2 py-1;
  @apply shadow-lg max-w-xs;
}

.tooltip-time {
  @apply font-medium mb-1;
}

.tooltip-values {
  @apply space-y-1;
}

.tooltip-value {
  @apply flex items-center gap-2;
}

.value-color {
  @apply w-2 h-2 rounded-full;
}

.value-label {
  @apply opacity-75;
}

.value-number {
  @apply font-medium;
}

/* 图例 */
.chart-legend {
  @apply flex flex-wrap gap-4 p-4 border-t border-gray-200;
}

.theme-dark .chart-legend {
  @apply border-gray-700;
}

.legend-item {
  @apply flex items-center gap-2 cursor-pointer;
  @apply hover:opacity-75 transition-opacity duration-200;
}

.legend-color {
  @apply w-3 h-3 rounded-full;
}

.legend-label {
  @apply text-sm text-gray-700;
}

.theme-dark .legend-label {
  @apply text-gray-300;
}

.legend-disabled {
  @apply opacity-50 line-through;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .chart-header {
    @apply flex-col items-start gap-3;
  }
  
  .chart-controls {
    @apply w-full justify-between;
  }
  
  .chart-legend {
    @apply gap-2;
  }
}
</style>
