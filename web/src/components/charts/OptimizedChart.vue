<template>
  <div class="optimized-chart" :class="chartClasses">
    <!-- 图表工具栏 -->
    <div v-if="showToolbar" class="chart-toolbar">
      <div class="toolbar-left">
        <h3 v-if="title" class="chart-title">{{ title }}</h3>
        <div class="chart-status">
          <span class="status-indicator" :class="statusClass"></span>
          <span class="status-text">{{ statusText }}</span>
        </div>
      </div>
      
      <div class="toolbar-right">
        <!-- 图表类型切换 -->
        <select v-model="chartType" class="chart-type-select">
          <option value="line">线图</option>
          <option value="area">面积图</option>
          <option value="bar">柱状图</option>
          <option value="scatter">散点图</option>
        </select>
        
        <!-- 时间范围 -->
        <select v-model="timeRange" class="time-range-select">
          <option value="1m">1分钟</option>
          <option value="5m">5分钟</option>
          <option value="15m">15分钟</option>
          <option value="1h">1小时</option>
          <option value="6h">6小时</option>
          <option value="24h">24小时</option>
        </select>
        
        <!-- 控制按钮 -->
        <button
          @click="togglePause"
          class="control-button"
          :class="{ 'button-active': isPaused }"
        >
          <PauseIcon v-if="!isPaused" class="button-icon" />
          <PlayIcon v-else class="button-icon" />
        </button>
        
        <button @click="resetZoom" class="control-button">
          <ArrowsPointingOutIcon class="button-icon" />
        </button>
        
        <button @click="exportData" class="control-button">
          <ArrowDownTrayIcon class="button-icon" />
        </button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div
      ref="chartContainer"
      class="chart-container"
      :style="{ height: chartHeight }"
    >
      <!-- 性能监控覆盖层 -->
      <div v-if="showPerformanceOverlay" class="performance-overlay">
        <div class="performance-metrics">
          <div class="metric">
            <span class="metric-label">FPS:</span>
            <span class="metric-value">{{ Math.round(performanceMetrics.fps) }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">渲染时间:</span>
            <span class="metric-value">{{ Math.round(performanceMetrics.renderTime) }}ms</span>
          </div>
          <div class="metric">
            <span class="metric-label">数据点:</span>
            <span class="metric-value">{{ dataPoints.length }}</span>
          </div>
        </div>
      </div>

      <!-- Canvas 图表 -->
      <canvas
        ref="chartCanvas"
        class="chart-canvas"
        @mousemove="handleMouseMove"
        @mouseleave="handleMouseLeave"
        @wheel="handleWheel"
        @mousedown="handleMouseDown"
        @mouseup="handleMouseUp"
      ></canvas>

      <!-- SVG 覆盖层（用于交互元素） -->
      <svg
        ref="svgOverlay"
        class="svg-overlay"
        :width="canvasWidth"
        :height="canvasHeight"
      >
        <!-- 十字线 -->
        <g v-if="crosshair.visible" class="crosshair">
          <line
            :x1="0"
            :y1="crosshair.y"
            :x2="canvasWidth"
            :y2="crosshair.y"
            class="crosshair-line"
          />
          <line
            :x1="crosshair.x"
            :y1="0"
            :x2="crosshair.x"
            :y2="canvasHeight"
            class="crosshair-line"
          />
        </g>

        <!-- 缩放选择框 -->
        <rect
          v-if="zoomSelection.active"
          :x="Math.min(zoomSelection.startX, zoomSelection.endX)"
          :y="Math.min(zoomSelection.startY, zoomSelection.endY)"
          :width="Math.abs(zoomSelection.endX - zoomSelection.startX)"
          :height="Math.abs(zoomSelection.endY - zoomSelection.startY)"
          class="zoom-selection"
        />
      </svg>

      <!-- 工具提示 -->
      <div
        v-if="tooltip.visible"
        ref="tooltipElement"
        class="chart-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-header">
          <span class="tooltip-time">{{ tooltip.time }}</span>
        </div>
        <div class="tooltip-body">
          <div
            v-for="(item, index) in tooltip.items"
            :key="index"
            class="tooltip-item"
          >
            <span
              class="tooltip-color"
              :style="{ backgroundColor: item.color }"
            ></span>
            <span class="tooltip-label">{{ item.label }}:</span>
            <span class="tooltip-value">{{ item.value }}</span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="chart-loading">
        <div class="loading-spinner"></div>
        <span class="loading-text">加载数据中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="chart-error">
        <ExclamationTriangleIcon class="error-icon" />
        <span class="error-text">{{ error }}</span>
        <button @click="retry" class="retry-button">重试</button>
      </div>
    </div>

    <!-- 图表统计信息 -->
    <div v-if="showStats" class="chart-stats">
      <div class="stat-item">
        <span class="stat-label">最新值:</span>
        <span class="stat-value">{{ formatValue(statistics.latest) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">平均值:</span>
        <span class="stat-value">{{ formatValue(statistics.average) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">最大值:</span>
        <span class="stat-value">{{ formatValue(statistics.max) }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">最小值:</span>
        <span class="stat-value">{{ formatValue(statistics.min) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import {
  PauseIcon,
  PlayIcon,
  ArrowsPointingOutIcon,
  ArrowDownTrayIcon,
  ExclamationTriangleIcon,
} from '@heroicons/vue/24/outline'
import { useRealtimeStream } from '@/composables/useRealtimeStream'
import { useResponsive } from '@/composables/useResponsive'

// 数据点接口
interface ChartDataPoint {
  timestamp: number
  value: number
  label?: string
}

// 工具提示接口
interface TooltipData {
  visible: boolean
  x: number
  y: number
  time: string
  items: Array<{
    label: string
    value: string
    color: string
  }>
}

// 十字线接口
interface CrosshairData {
  visible: boolean
  x: number
  y: number
}

// 缩放选择接口
interface ZoomSelection {
  active: boolean
  startX: number
  startY: number
  endX: number
  endY: number
}

// 性能指标接口
interface PerformanceMetrics {
  fps: number
  renderTime: number
  lastFrameTime: number
}

// 统计数据接口
interface Statistics {
  latest: number
  average: number
  max: number
  min: number
  count: number
}

// 定义属性
interface Props {
  title?: string
  endpoint: string
  height?: string
  chartType?: 'line' | 'area' | 'bar' | 'scatter'
  showToolbar?: boolean
  showStats?: boolean
  showPerformanceOverlay?: boolean
  maxDataPoints?: number
  updateInterval?: number
  enableZoom?: boolean
  enableCrosshair?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  chartType: 'line',
  showToolbar: true,
  showStats: true,
  showPerformanceOverlay: false,
  maxDataPoints: 1000,
  updateInterval: 100,
  enableZoom: true,
  enableCrosshair: true,
})

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chartCanvas = ref<HTMLCanvasElement>()
const svgOverlay = ref<SVGElement>()
const tooltipElement = ref<HTMLElement>()

const chartType = ref(props.chartType)
const timeRange = ref('1h')
const isPaused = ref(false)
const isLoading = ref(false)
const error = ref<string | null>(null)

const canvasWidth = ref(800)
const canvasHeight = ref(400)
const dataPoints = ref<ChartDataPoint[]>([])

// 交互状态
const tooltip = ref<TooltipData>({
  visible: false,
  x: 0,
  y: 0,
  time: '',
  items: [],
})

const crosshair = ref<CrosshairData>({
  visible: false,
  x: 0,
  y: 0,
})

const zoomSelection = ref<ZoomSelection>({
  active: false,
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
})

// 性能监控
const performanceMetrics = ref<PerformanceMetrics>({
  fps: 0,
  renderTime: 0,
  lastFrameTime: 0,
})

// 渲染上下文
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId: number | null = null
let lastRenderTime = 0
let frameCount = 0

// 响应式设计
const { isMobile } = useResponsive()

// 实时数据流
const {
  startStream,
  stopStream,
  dataBuffer,
  streamState,
  getDataStatistics,
} = useRealtimeStream({
  endpoint: props.endpoint,
  bufferSize: props.maxDataPoints,
  updateInterval: props.updateInterval,
})

// 计算属性
const chartClasses = computed(() => ({
  'chart-mobile': isMobile.value,
  'chart-paused': isPaused.value,
  'chart-loading': isLoading.value,
  'chart-error': !!error.value,
}))

const chartHeight = computed(() => props.height)

const statusClass = computed(() => ({
  'status-connected': streamState.value.isConnected && !isPaused.value,
  'status-paused': isPaused.value,
  'status-disconnected': !streamState.value.isConnected,
  'status-error': !!error.value,
}))

const statusText = computed(() => {
  if (error.value) return '错误'
  if (isPaused.value) return '已暂停'
  if (!streamState.value.isConnected) return '未连接'
  return '实时更新'
})

const statistics = computed((): Statistics => {
  const stats = getDataStatistics()
  return {
    latest: stats.latest?.value as number || 0,
    average: stats.average,
    max: stats.max,
    min: stats.min,
    count: stats.count,
  }
})

const tooltipStyle = computed(() => ({
  left: `${tooltip.value.x + 10}px`,
  top: `${tooltip.value.y - 10}px`,
}))

// 方法
const initializeChart = () => {
  if (!chartCanvas.value) return

  ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  updateCanvasSize()
  startRenderLoop()
}

const updateCanvasSize = () => {
  if (!chartContainer.value || !chartCanvas.value) return

  const rect = chartContainer.value.getBoundingClientRect()
  canvasWidth.value = rect.width
  canvasHeight.value = rect.height

  // 设置高 DPI 支持
  const dpr = window.devicePixelRatio || 1
  chartCanvas.value.width = canvasWidth.value * dpr
  chartCanvas.value.height = canvasHeight.value * dpr

  if (ctx) {
    ctx.scale(dpr, dpr)
  }
}

const startRenderLoop = () => {
  const render = (timestamp: number) => {
    if (!ctx || isPaused.value) {
      animationFrameId = requestAnimationFrame(render)
      return
    }

    const startTime = performance.now()

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

    // 渲染图表
    renderChart()

    // 更新性能指标
    const renderTime = performance.now() - startTime
    updatePerformanceMetrics(timestamp, renderTime)

    animationFrameId = requestAnimationFrame(render)
  }

  animationFrameId = requestAnimationFrame(render)
}

const renderChart = () => {
  if (!ctx || dataPoints.value.length === 0) return

  const padding = 40
  const chartWidth = canvasWidth.value - 2 * padding
  const chartHeight = canvasHeight.value - 2 * padding

  // 计算数据范围
  const timeRange = getTimeRangeMs()
  const now = Date.now()
  const startTime = now - timeRange

  const visibleData = dataPoints.value.filter(
    point => point.timestamp >= startTime
  )

  if (visibleData.length === 0) return

  const minValue = Math.min(...visibleData.map(p => p.value))
  const maxValue = Math.max(...visibleData.map(p => p.value))
  const valueRange = maxValue - minValue || 1

  // 根据图表类型渲染
  switch (chartType.value) {
    case 'line':
      renderLineChart(visibleData, padding, chartWidth, chartHeight, startTime, timeRange, minValue, valueRange)
      break
    case 'area':
      renderAreaChart(visibleData, padding, chartWidth, chartHeight, startTime, timeRange, minValue, valueRange)
      break
    case 'bar':
      renderBarChart(visibleData, padding, chartWidth, chartHeight, startTime, timeRange, minValue, valueRange)
      break
    case 'scatter':
      renderScatterChart(visibleData, padding, chartWidth, chartHeight, startTime, timeRange, minValue, valueRange)
      break
  }

  // 渲染坐标轴
  renderAxes(padding, chartWidth, chartHeight, startTime, timeRange, minValue, maxValue)
}

const renderLineChart = (
  data: ChartDataPoint[],
  padding: number,
  chartWidth: number,
  chartHeight: number,
  startTime: number,
  timeRange: number,
  minValue: number,
  valueRange: number
) => {
  if (!ctx || data.length < 2) return

  ctx.strokeStyle = '#3b82f6'
  ctx.lineWidth = 2
  ctx.beginPath()

  data.forEach((point, index) => {
    const x = padding + ((point.timestamp - startTime) / timeRange) * chartWidth
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight

    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })

  ctx.stroke()
}

const renderAreaChart = (
  data: ChartDataPoint[],
  padding: number,
  chartWidth: number,
  chartHeight: number,
  startTime: number,
  timeRange: number,
  minValue: number,
  valueRange: number
) => {
  if (!ctx || data.length < 2) return

  // 创建渐变
  const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight)
  gradient.addColorStop(0, 'rgba(59, 130, 246, 0.3)')
  gradient.addColorStop(1, 'rgba(59, 130, 246, 0.1)')

  ctx.fillStyle = gradient
  ctx.beginPath()

  // 绘制面积
  data.forEach((point, index) => {
    const x = padding + ((point.timestamp - startTime) / timeRange) * chartWidth
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight

    if (index === 0) {
      ctx.moveTo(x, padding + chartHeight)
      ctx.lineTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })

  // 闭合路径
  const lastPoint = data[data.length - 1]
  const lastX = padding + ((lastPoint.timestamp - startTime) / timeRange) * chartWidth
  ctx.lineTo(lastX, padding + chartHeight)
  ctx.closePath()
  ctx.fill()

  // 绘制边线
  renderLineChart(data, padding, chartWidth, chartHeight, startTime, timeRange, minValue, valueRange)
}

const renderBarChart = (
  data: ChartDataPoint[],
  padding: number,
  chartWidth: number,
  chartHeight: number,
  startTime: number,
  timeRange: number,
  minValue: number,
  valueRange: number
) => {
  if (!ctx || data.length === 0) return

  const barWidth = Math.max(1, chartWidth / data.length * 0.8)
  ctx.fillStyle = '#3b82f6'

  data.forEach(point => {
    const x = padding + ((point.timestamp - startTime) / timeRange) * chartWidth - barWidth / 2
    const barHeight = ((point.value - minValue) / valueRange) * chartHeight
    const y = padding + chartHeight - barHeight

    ctx.fillRect(x, y, barWidth, barHeight)
  })
}

const renderScatterChart = (
  data: ChartDataPoint[],
  padding: number,
  chartWidth: number,
  chartHeight: number,
  startTime: number,
  timeRange: number,
  minValue: number,
  valueRange: number
) => {
  if (!ctx || data.length === 0) return

  ctx.fillStyle = '#3b82f6'

  data.forEach(point => {
    const x = padding + ((point.timestamp - startTime) / timeRange) * chartWidth
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight

    ctx.beginPath()
    ctx.arc(x, y, 3, 0, 2 * Math.PI)
    ctx.fill()
  })
}

const renderAxes = (
  padding: number,
  chartWidth: number,
  chartHeight: number,
  startTime: number,
  timeRange: number,
  minValue: number,
  maxValue: number
) => {
  if (!ctx) return

  ctx.strokeStyle = '#6b7280'
  ctx.lineWidth = 1

  // X轴
  ctx.beginPath()
  ctx.moveTo(padding, padding + chartHeight)
  ctx.lineTo(padding + chartWidth, padding + chartHeight)
  ctx.stroke()

  // Y轴
  ctx.beginPath()
  ctx.moveTo(padding, padding)
  ctx.lineTo(padding, padding + chartHeight)
  ctx.stroke()

  // 刻度标签
  ctx.fillStyle = '#6b7280'
  ctx.font = '12px sans-serif'
  ctx.textAlign = 'center'

  // X轴时间标签
  const timeSteps = 5
  for (let i = 0; i <= timeSteps; i++) {
    const x = padding + (i / timeSteps) * chartWidth
    const time = new Date(startTime + (i / timeSteps) * timeRange)
    const label = time.toLocaleTimeString()
    
    ctx.fillText(label, x, padding + chartHeight + 20)
  }

  // Y轴数值标签
  ctx.textAlign = 'right'
  const valueSteps = 5
  for (let i = 0; i <= valueSteps; i++) {
    const y = padding + chartHeight - (i / valueSteps) * chartHeight
    const value = minValue + (i / valueSteps) * (maxValue - minValue)
    
    ctx.fillText(formatValue(value), padding - 10, y + 4)
  }
}

const updatePerformanceMetrics = (timestamp: number, renderTime: number) => {
  frameCount++
  
  if (timestamp - lastRenderTime >= 1000) {
    performanceMetrics.value.fps = frameCount
    frameCount = 0
    lastRenderTime = timestamp
  }
  
  performanceMetrics.value.renderTime = renderTime
}

const getTimeRangeMs = (): number => {
  const ranges: Record<string, number> = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
  }
  return ranges[timeRange.value] || ranges['1h']
}

const formatValue = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  } else {
    return value.toFixed(2)
  }
}

// 事件处理
const handleMouseMove = (event: MouseEvent) => {
  if (!props.enableCrosshair && !tooltip.value.visible) return

  const rect = chartCanvas.value?.getBoundingClientRect()
  if (!rect) return

  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  if (props.enableCrosshair) {
    crosshair.value = { visible: true, x, y }
  }

  // 更新工具提示
  updateTooltip(x, y)
}

const handleMouseLeave = () => {
  crosshair.value.visible = false
  tooltip.value.visible = false
}

const handleWheel = (event: WheelEvent) => {
  if (!props.enableZoom) return
  
  event.preventDefault()
  // 实现缩放逻辑
}

const handleMouseDown = (event: MouseEvent) => {
  if (!props.enableZoom) return
  
  const rect = chartCanvas.value?.getBoundingClientRect()
  if (!rect) return

  zoomSelection.value = {
    active: true,
    startX: event.clientX - rect.left,
    startY: event.clientY - rect.top,
    endX: event.clientX - rect.left,
    endY: event.clientY - rect.top,
  }
}

const handleMouseUp = () => {
  if (zoomSelection.value.active) {
    // 应用缩放
    applyZoom()
    zoomSelection.value.active = false
  }
}

const updateTooltip = (x: number, y: number) => {
  // 查找最近的数据点
  const padding = 40
  const chartWidth = canvasWidth.value - 2 * padding
  const timeRange = getTimeRangeMs()
  const now = Date.now()
  const startTime = now - timeRange

  const relativeX = (x - padding) / chartWidth
  const targetTime = startTime + relativeX * timeRange

  const nearestPoint = dataPoints.value.reduce((nearest, point) => {
    const distance = Math.abs(point.timestamp - targetTime)
    return distance < Math.abs(nearest.timestamp - targetTime) ? point : nearest
  }, dataPoints.value[0])

  if (nearestPoint) {
    tooltip.value = {
      visible: true,
      x,
      y,
      time: new Date(nearestPoint.timestamp).toLocaleTimeString(),
      items: [{
        label: '数值',
        value: formatValue(nearestPoint.value),
        color: '#3b82f6',
      }],
    }
  }
}

const applyZoom = () => {
  // 实现缩放应用逻辑
  console.log('应用缩放:', zoomSelection.value)
}

const togglePause = () => {
  isPaused.value = !isPaused.value
  
  if (isPaused.value) {
    stopStream()
  } else {
    startStream()
  }
}

const resetZoom = () => {
  // 重置缩放
  console.log('重置缩放')
}

const exportData = () => {
  const data = dataPoints.value.map(point => ({
    timestamp: new Date(point.timestamp).toISOString(),
    value: point.value,
  }))
  
  const blob = new Blob([JSON.stringify(data, null, 2)], {
    type: 'application/json',
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chart-data-${Date.now()}.json`
  a.click()
  
  URL.revokeObjectURL(url)
}

const retry = () => {
  error.value = null
  startStream()
}

// 监听数据变化
watch(dataBuffer, (newData) => {
  dataPoints.value = newData.map(point => ({
    timestamp: point.timestamp,
    value: typeof point.value === 'number' ? point.value : 0,
  }))
}, { deep: true })

// 生命周期
onMounted(() => {
  initializeChart()
  startStream()
  
  window.addEventListener('resize', updateCanvasSize)
})

onUnmounted(() => {
  stopStream()
  
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
  
  window.removeEventListener('resize', updateCanvasSize)
})
</script>

<style scoped>
/* 优化图表样式 */
.optimized-chart {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden;
}

.theme-dark .optimized-chart {
  @apply bg-gray-800 border-gray-700;
}

/* 工具栏 */
.chart-toolbar {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.theme-dark .chart-toolbar {
  @apply border-gray-700;
}

.toolbar-left {
  @apply flex items-center gap-4;
}

.chart-title {
  @apply text-lg font-semibold text-gray-900;
}

.theme-dark .chart-title {
  @apply text-gray-100;
}

.chart-status {
  @apply flex items-center gap-2;
}

.status-indicator {
  @apply w-2 h-2 rounded-full;
}

.status-connected {
  @apply bg-green-500;
}

.status-paused {
  @apply bg-yellow-500;
}

.status-disconnected {
  @apply bg-gray-500;
}

.status-error {
  @apply bg-red-500;
}

.status-text {
  @apply text-sm text-gray-600;
}

.theme-dark .status-text {
  @apply text-gray-400;
}

.toolbar-right {
  @apply flex items-center gap-2;
}

.chart-type-select,
.time-range-select {
  @apply px-3 py-1 text-sm border border-gray-300 rounded;
}

.theme-dark .chart-type-select,
.theme-dark .time-range-select {
  @apply bg-gray-700 border-gray-600 text-gray-200;
}

.control-button {
  @apply p-2 text-gray-500 hover:text-gray-700 rounded;
  @apply transition-colors duration-200;
}

.theme-dark .control-button {
  @apply text-gray-400 hover:text-gray-200;
}

.button-active {
  @apply text-blue-600 bg-blue-50;
}

.theme-dark .button-active {
  @apply text-blue-400 bg-blue-900 bg-opacity-20;
}

.button-icon {
  @apply w-5 h-5;
}

/* 图表容器 */
.chart-container {
  @apply relative overflow-hidden;
}

.chart-canvas {
  @apply w-full h-full;
}

.svg-overlay {
  @apply absolute inset-0 pointer-events-none;
}

/* 性能覆盖层 */
.performance-overlay {
  @apply absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs rounded p-2;
}

.performance-metrics {
  @apply space-y-1;
}

.metric {
  @apply flex justify-between gap-2;
}

.metric-label {
  @apply opacity-75;
}

.metric-value {
  @apply font-mono;
}

/* 十字线 */
.crosshair-line {
  @apply stroke-gray-400 stroke-1;
  stroke-dasharray: 2,2;
}

/* 缩放选择 */
.zoom-selection {
  @apply fill-blue-200 fill-opacity-30 stroke-blue-500 stroke-1;
}

/* 工具提示 */
.chart-tooltip {
  @apply absolute z-10 bg-gray-900 text-white text-xs rounded shadow-lg p-2;
  @apply pointer-events-none;
}

.tooltip-header {
  @apply font-medium mb-1;
}

.tooltip-body {
  @apply space-y-1;
}

.tooltip-item {
  @apply flex items-center gap-2;
}

.tooltip-color {
  @apply w-2 h-2 rounded-full;
}

/* 加载和错误状态 */
.chart-loading,
.chart-error {
  @apply absolute inset-0 flex flex-col items-center justify-center;
  @apply bg-white bg-opacity-90;
}

.theme-dark .chart-loading,
.theme-dark .chart-error {
  @apply bg-gray-800 bg-opacity-90;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mb-2;
}

.loading-text {
  @apply text-sm text-gray-600;
}

.theme-dark .loading-text {
  @apply text-gray-400;
}

.error-icon {
  @apply w-8 h-8 text-red-500 mb-2;
}

.error-text {
  @apply text-sm text-gray-600 mb-2;
}

.theme-dark .error-text {
  @apply text-gray-400;
}

.retry-button {
  @apply px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded;
  @apply hover:bg-blue-50;
}

.theme-dark .retry-button {
  @apply text-blue-400 border-blue-600 hover:bg-blue-900 hover:bg-opacity-20;
}

/* 统计信息 */
.chart-stats {
  @apply flex items-center justify-around p-3 border-t border-gray-200;
  @apply bg-gray-50;
}

.theme-dark .chart-stats {
  @apply border-gray-700 bg-gray-700;
}

.stat-item {
  @apply text-center;
}

.stat-label {
  @apply block text-xs text-gray-500;
}

.theme-dark .stat-label {
  @apply text-gray-400;
}

.stat-value {
  @apply block text-sm font-semibold text-gray-900;
}

.theme-dark .stat-value {
  @apply text-gray-100;
}

/* 移动端适配 */
.chart-mobile .chart-toolbar {
  @apply flex-col items-start gap-3;
}

.chart-mobile .toolbar-right {
  @apply w-full justify-between;
}

.chart-mobile .chart-stats {
  @apply grid grid-cols-2 gap-2;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
