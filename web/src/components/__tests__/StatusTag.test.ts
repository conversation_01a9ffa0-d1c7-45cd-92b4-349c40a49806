import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import StatusTag from '@/components/common/StatusTag.vue'

/**
 * StatusTag 组件单元测试
 * 测试状态标签组件的各种状态显示和类型配置
 */
describe('StatusTag', () => {
  it('应该正确渲染应用状态', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'running',
        type: 'app'
      }
    })
    
    expect(wrapper.text()).toBe('运行中')
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--success')
  })
  
  it('应该正确渲染构建状态', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'success',
        type: 'build'
      }
    })
    
    expect(wrapper.text()).toBe('成功')
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--success')
  })
  
  it('应该正确渲染部署状态', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'deploying',
        type: 'deployment'
      }
    })
    
    expect(wrapper.text()).toBe('部署中')
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--warning')
  })
  
  it('应该正确渲染用户状态', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'active',
        type: 'user'
      }
    })
    
    expect(wrapper.text()).toBe('活跃')
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--success')
  })
  
  it('应该正确渲染通用状态', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'enabled',
        type: 'general'
      }
    })
    
    expect(wrapper.text()).toBe('启用')
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--success')
  })
  
  it('应该处理未知状态', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'unknown',
        type: 'app'
      }
    })
    
    expect(wrapper.text()).toBe('unknown')
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--info')
  })
  
  it('应该支持隐藏图标', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'running',
        type: 'app',
        showIcon: false
      }
    })
    
    expect(wrapper.find('.status-icon').exists()).toBe(false)
  })
  
  it('应该支持不同尺寸', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'running',
        type: 'app',
        size: 'small'
      }
    })
    
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--small')
  })
  
  it('应该支持不同效果', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'running',
        type: 'app',
        effect: 'dark'
      }
    })
    
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--dark')
  })
  
  it('应该为警告状态添加动画类', () => {
    const wrapper = mount(StatusTag, {
      props: {
        status: 'building',
        type: 'app'
      }
    })
    
    expect(wrapper.find('.el-tag').classes()).toContain('el-tag--warning')
  })
})
