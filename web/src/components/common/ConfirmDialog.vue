<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="!loading"
    :show-close="!loading"
    class="confirm-dialog"
  >
    <!-- 对话框内容 -->
    <div class="dialog-content">
      <!-- 图标 -->
      <div class="dialog-icon" :class="`icon-${type}`">
        <el-icon>
          <component :is="iconComponent" />
        </el-icon>
      </div>
      
      <!-- 消息内容 -->
      <div class="dialog-message">
        <div class="message-title">{{ message }}</div>
        <div v-if="details" class="message-details">{{ details }}</div>
        
        <!-- 额外的表单内容 -->
        <div v-if="$slots.default" class="message-form">
          <slot />
        </div>
      </div>
    </div>
    
    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button 
          @click="handleCancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </el-button>
        <el-button 
          :type="confirmButtonType"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ loading ? loadingText : confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性定义
interface Props {
  visible: boolean
  title?: string
  message: string
  details?: string
  type?: 'info' | 'success' | 'warning' | 'error'
  width?: string
  confirmText?: string
  cancelText?: string
  loadingText?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认操作',
  type: 'warning',
  width: '420px',
  confirmText: '确定',
  cancelText: '取消',
  loadingText: '处理中...',
  loading: false
})

// 事件定义
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: []
  cancel: []
}>()

// 计算属性
const iconComponent = computed(() => {
  const iconMap = {
    info: 'InfoFilled',
    success: 'SuccessFilled',
    warning: 'WarningFilled',
    error: 'CircleCloseFilled'
  }
  return iconMap[props.type]
})

const confirmButtonType = computed(() => {
  const typeMap = {
    info: 'primary',
    success: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return typeMap[props.type]
})

// 方法定义
const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.confirm-dialog {
  .dialog-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    
    .dialog-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      
      .el-icon {
        font-size: 24px;
      }
      
      &.icon-info {
        background: var(--el-color-info-light-8);
        color: var(--el-color-info);
      }
      
      &.icon-success {
        background: var(--el-color-success-light-8);
        color: var(--el-color-success);
      }
      
      &.icon-warning {
        background: var(--el-color-warning-light-8);
        color: var(--el-color-warning);
      }
      
      &.icon-error {
        background: var(--el-color-danger-light-8);
        color: var(--el-color-danger);
      }
    }
    
    .dialog-message {
      flex: 1;
      
      .message-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
        line-height: 1.4;
      }
      
      .message-details {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        line-height: 1.5;
        margin-bottom: 16px;
      }
      
      .message-form {
        margin-top: 20px;
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .confirm-dialog {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 5vh auto;
    }
    
    .dialog-content {
      flex-direction: column;
      text-align: center;
      
      .dialog-icon {
        align-self: center;
      }
    }
  }
}
</style>
