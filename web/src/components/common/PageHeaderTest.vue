<template>
  <div class="page-header-test">
    <h2>PageHeader 组件测试</h2>
    
    <!-- 测试修复后的 PageHeader 组件 -->
    <PageHeader
      title="测试页面"
      description="这是一个测试页面，用于验证 PageHeader 组件的标签页功能"
      icon="Document"
      :show-back="true"
      :breadcrumbs="breadcrumbs"
      :tabs="tabs"
      :active-tab="currentActiveTab"
      :stats="stats"
      @back="handleBack"
      @tab-change="handleTabChange"
    />
    
    <!-- 显示当前状态 -->
    <div class="test-info">
      <h3>当前状态：</h3>
      <p>激活的标签页：{{ currentActiveTab }}</p>
      <p>标签页切换次数：{{ tabChangeCount }}</p>
      <p>返回按钮点击次数：{{ backClickCount }}</p>
    </div>
    
    <!-- 手动切换标签页的按钮 -->
    <div class="test-controls">
      <h3>测试控制：</h3>
      <el-button 
        v-for="tab in tabs" 
        :key="tab.name"
        :type="currentActiveTab === tab.name ? 'primary' : 'default'"
        @click="setActiveTab(tab.name)"
      >
        切换到 {{ tab.label }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PageHeader from './PageHeader.vue'

// 测试数据
const breadcrumbs = ref([
  { title: '首页', path: '/' },
  { title: '测试页面', path: '/test' }
])

const tabs = ref([
  { name: 'overview', label: '概览' },
  { name: 'details', label: '详情' },
  { name: 'settings', label: '设置' }
])

const stats = ref([
  { key: 'total', label: '总数', value: 100 },
  { key: 'active', label: '活跃', value: 85 },
  { key: 'pending', label: '待处理', value: 15 }
])

// 响应式状态
const currentActiveTab = ref('overview')
const tabChangeCount = ref(0)
const backClickCount = ref(0)

// 事件处理方法
/**
 * 处理标签页切换事件
 * 这个方法验证了修复后的组件能正确触发事件
 */
const handleTabChange = (tabName: string) => {
  console.log('标签页切换到:', tabName)
  currentActiveTab.value = tabName
  tabChangeCount.value++
}

/**
 * 处理返回按钮点击事件
 */
const handleBack = () => {
  console.log('返回按钮被点击')
  backClickCount.value++
}

/**
 * 手动设置激活的标签页
 * 用于测试组件的响应性
 */
const setActiveTab = (tabName: string) => {
  currentActiveTab.value = tabName
}
</script>

<style lang="scss" scoped>
.page-header-test {
  padding: 20px;
  
  .test-info {
    margin: 20px 0;
    padding: 16px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
    
    h3 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 8px 0;
      color: var(--el-text-color-regular);
    }
  }
  
  .test-controls {
    margin: 20px 0;
    padding: 16px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
    
    h3 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
    }
    
    .el-button {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
}
</style>
