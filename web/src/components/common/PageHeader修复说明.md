# PageHeader.vue 组件 v-model 错误修复说明

## 问题描述

在 `PageHeader.vue` 组件的第47行，存在一个 v-model 错误：

```vue
<!-- 错误的代码 -->
<el-tabs v-model="activeTab" @tab-change="handleTabChange">
```

**错误信息**：`v-model cannot be used on a prop, because local prop bindings are not writable`

## 问题原因

1. **根本原因**：`activeTab` 被定义为一个 prop（第102行），而 v-model 试图对 prop 进行双向绑定
2. **Vue 原则违反**：在 Vue 中，prop 是只读的，遵循单向数据流原则
3. **编译错误**：这种用法会导致编译失败，阻止组件正常工作

## 修复方案

### 1. 替换 v-model 为显式绑定

**修复前**：
```vue
<el-tabs v-model="activeTab" @tab-change="handleTabChange">
```

**修复后**：
```vue
<el-tabs 
  :model-value="activeTab" 
  @update:model-value="handleTabChange"
>
```

### 2. 更新事件处理逻辑

```typescript
/**
 * 处理标签页切换事件
 * 当用户点击不同的标签页时，将新的标签页名称传递给父组件
 * 这个方法替代了原来的 v-model 双向绑定，符合 Vue 3 单向数据流原则
 * @param tabName 新选中的标签页名称
 */
const handleTabChange = (tabName: string) => {
  emit('tabChange', tabName)
}
```

### 3. 父组件使用方式

```vue
<template>
  <PageHeader
    :active-tab="currentTab"
    @tab-change="handleTabChange"
    :tabs="tabs"
  />
</template>

<script setup>
const currentTab = ref('overview')

const handleTabChange = (newTab) => {
  currentTab.value = newTab
}
</script>
```

## 修复优势

1. **符合 Vue 3 最佳实践**：遵循单向数据流原则
2. **类型安全**：TypeScript 编译通过，无类型错误
3. **可维护性**：代码逻辑清晰，易于理解和维护
4. **响应性保持**：组件仍然具有完整的响应性功能

## 测试验证

创建了 `PageHeaderTest.vue` 测试组件来验证修复效果：

- ✅ 标签页切换功能正常
- ✅ 事件正确触发
- ✅ 数据绑定响应正常
- ✅ 无编译错误

## 相关文件

- `web/src/components/common/PageHeader.vue` - 主组件文件
- `web/src/components/common/PageHeaderTest.vue` - 测试组件
- `web/src/components/common/PageHeader修复说明.md` - 本说明文档

## 注意事项

1. **向后兼容**：修复后的组件 API 保持不变，现有使用方式无需修改
2. **事件名称**：使用标准的 `@update:model-value` 事件，符合 Vue 3 规范
3. **性能影响**：修复不会影响组件性能，反而更符合框架设计理念
