<template>
  <el-tag 
    :type="tagType" 
    :effect="effect"
    :size="size"
    class="status-tag"
  >
    <el-icon v-if="showIcon" class="status-icon">
      <component :is="statusIcon" />
    </el-icon>
    {{ statusText }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性定义
interface Props {
  status: string
  type?: 'app' | 'build' | 'deployment' | 'user' | 'general'
  size?: 'large' | 'default' | 'small'
  effect?: 'dark' | 'light' | 'plain'
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'general',
  size: 'default',
  effect: 'light',
  showIcon: true
})

// 状态配置映射
const statusConfigs = {
  // 应用状态
  app: {
    running: { text: '运行中', type: 'success', icon: 'VideoPlay' },
    stopped: { text: '已停止', type: 'info', icon: 'VideoPause' },
    building: { text: '构建中', type: 'warning', icon: 'Loading' },
    deploying: { text: '部署中', type: 'warning', icon: 'Upload' },
    failed: { text: '失败', type: 'danger', icon: 'CircleClose' },
    creating: { text: '创建中', type: 'warning', icon: 'Plus' },
    build_failed: { text: '构建失败', type: 'danger', icon: 'CircleClose' },
    build_success: { text: '构建成功', type: 'success', icon: 'CircleCheck' }
  },
  
  // 构建状态
  build: {
    pending: { text: '等待中', type: 'info', icon: 'Clock' },
    running: { text: '构建中', type: 'warning', icon: 'Loading' },
    success: { text: '成功', type: 'success', icon: 'CircleCheck' },
    failed: { text: '失败', type: 'danger', icon: 'CircleClose' },
    cancelled: { text: '已取消', type: 'info', icon: 'CircleClose' }
  },
  
  // 部署状态
  deployment: {
    pending: { text: '等待部署', type: 'info', icon: 'Clock' },
    deploying: { text: '部署中', type: 'warning', icon: 'Upload' },
    success: { text: '部署成功', type: 'success', icon: 'CircleCheck' },
    failed: { text: '部署失败', type: 'danger', icon: 'CircleClose' },
    rollback: { text: '回滚中', type: 'warning', icon: 'RefreshLeft' }
  },
  
  // 用户状态
  user: {
    active: { text: '活跃', type: 'success', icon: 'CircleCheck' },
    inactive: { text: '未激活', type: 'warning', icon: 'Warning' },
    suspended: { text: '已暂停', type: 'danger', icon: 'CircleClose' },
    locked: { text: '已锁定', type: 'danger', icon: 'Lock' }
  },
  
  // 通用状态
  general: {
    enabled: { text: '启用', type: 'success', icon: 'CircleCheck' },
    disabled: { text: '禁用', type: 'info', icon: 'CircleClose' },
    online: { text: '在线', type: 'success', icon: 'Connection' },
    offline: { text: '离线', type: 'info', icon: 'Disconnect' },
    healthy: { text: '健康', type: 'success', icon: 'CircleCheck' },
    unhealthy: { text: '异常', type: 'danger', icon: 'Warning' }
  }
}

// 计算属性
const statusConfig = computed(() => {
  const configs = statusConfigs[props.type]
  return configs[props.status as keyof typeof configs] || {
    text: props.status,
    type: 'info',
    icon: 'QuestionFilled'
  }
})

const tagType = computed(() => statusConfig.value.type)
const statusText = computed(() => statusConfig.value.text)
const statusIcon = computed(() => statusConfig.value.icon)
</script>

<style lang="scss" scoped>
.status-tag {
  .status-icon {
    margin-right: 4px;
    font-size: 12px;
  }
  
  // 动画效果
  &.el-tag--warning {
    .status-icon {
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
