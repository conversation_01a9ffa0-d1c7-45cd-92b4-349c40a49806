<template>
  <div class="keyboard-navigation" :class="{ 'nav-visible': showNavigation }">
    <!-- 键盘导航指示器 -->
    <div
      v-if="showIndicator"
      class="nav-indicator"
      :style="indicatorStyle"
    >
      <div class="indicator-content">
        <span class="indicator-text">{{ currentElement?.label || '未知元素' }}</span>
        <span class="indicator-shortcut">{{ currentElement?.shortcut }}</span>
      </div>
    </div>

    <!-- 键盘快捷键帮助面板 -->
    <Transition name="help-panel">
      <div
        v-if="showHelpPanel"
        class="help-panel"
        role="dialog"
        aria-labelledby="help-panel-title"
        aria-modal="true"
      >
        <div class="help-header">
          <h2 id="help-panel-title" class="help-title">
            {{ $t('accessibility.keyboardShortcuts') }}
          </h2>
          <button
            @click="closeHelpPanel"
            class="help-close"
            :aria-label="$t('common.close')"
          >
            <XMarkIcon class="close-icon" />
          </button>
        </div>

        <div class="help-content">
          <!-- 通用快捷键 -->
          <div class="shortcut-section">
            <h3 class="section-title">{{ $t('accessibility.generalShortcuts') }}</h3>
            <div class="shortcut-list">
              <div
                v-for="shortcut in generalShortcuts"
                :key="shortcut.key"
                class="shortcut-item"
              >
                <kbd class="shortcut-key">{{ shortcut.key }}</kbd>
                <span class="shortcut-description">{{ shortcut.description }}</span>
              </div>
            </div>
          </div>

          <!-- 导航快捷键 -->
          <div class="shortcut-section">
            <h3 class="section-title">{{ $t('accessibility.navigationShortcuts') }}</h3>
            <div class="shortcut-list">
              <div
                v-for="shortcut in navigationShortcuts"
                :key="shortcut.key"
                class="shortcut-item"
              >
                <kbd class="shortcut-key">{{ shortcut.key }}</kbd>
                <span class="shortcut-description">{{ shortcut.description }}</span>
              </div>
            </div>
          </div>

          <!-- 应用特定快捷键 -->
          <div class="shortcut-section">
            <h3 class="section-title">{{ $t('accessibility.applicationShortcuts') }}</h3>
            <div class="shortcut-list">
              <div
                v-for="shortcut in applicationShortcuts"
                :key="shortcut.key"
                class="shortcut-item"
              >
                <kbd class="shortcut-key">{{ shortcut.key }}</kbd>
                <span class="shortcut-description">{{ shortcut.description }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="help-footer">
          <button @click="closeHelpPanel" class="help-ok-button">
            {{ $t('common.ok') }}
          </button>
        </div>
      </div>
    </Transition>

    <!-- 焦点陷阱 -->
    <div
      v-if="focusTrapActive"
      class="focus-trap-overlay"
      @click="releaseFocusTrap"
    ></div>

    <!-- 跳转链接 -->
    <div class="skip-links">
      <a
        v-for="link in skipLinks"
        :key="link.target"
        :href="link.target"
        class="skip-link"
        @click="handleSkipLinkClick"
      >
        {{ link.text }}
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import { useAccessibility } from '@/composables/useAccessibility'

// 导航元素接口
interface NavigationElement {
  element: HTMLElement
  label: string
  shortcut?: string
  type: 'button' | 'link' | 'input' | 'select' | 'textarea' | 'other'
}

// 快捷键接口
interface Shortcut {
  key: string
  description: string
  action: () => void
}

// 跳转链接接口
interface SkipLink {
  target: string
  text: string
}

// 响应式数据
const showNavigation = ref(false)
const showIndicator = ref(false)
const showHelpPanel = ref(false)
const focusTrapActive = ref(false)
const currentElement = ref<NavigationElement | null>(null)
const currentFocusIndex = ref(-1)
const navigableElements = ref<NavigationElement[]>([])

// 无障碍工具
const {
  focusableElements,
  updateFocusableElements,
  focusElementAt,
  focusNext,
  focusPrevious,
  focusFirst,
  focusLast,
  trapFocus,
  announce,
} = useAccessibility()

// 跳转链接配置
const skipLinks: SkipLink[] = [
  { target: '#main-content', text: '跳转到主内容' },
  { target: '#navigation', text: '跳转到导航' },
  { target: '#search', text: '跳转到搜索' },
  { target: '#footer', text: '跳转到页脚' },
]

// 通用快捷键
const generalShortcuts: Shortcut[] = [
  {
    key: '?',
    description: '显示键盘快捷键帮助',
    action: () => toggleHelpPanel(),
  },
  {
    key: 'Escape',
    description: '关闭对话框或取消操作',
    action: () => handleEscape(),
  },
  {
    key: 'Alt + M',
    description: '跳转到主内容',
    action: () => jumpToMain(),
  },
  {
    key: 'Alt + N',
    description: '跳转到导航',
    action: () => jumpToNavigation(),
  },
  {
    key: 'Alt + S',
    description: '跳转到搜索',
    action: () => jumpToSearch(),
  },
]

// 导航快捷键
const navigationShortcuts: Shortcut[] = [
  {
    key: 'Tab',
    description: '移动到下一个可聚焦元素',
    action: () => focusNext(),
  },
  {
    key: 'Shift + Tab',
    description: '移动到上一个可聚焦元素',
    action: () => focusPrevious(),
  },
  {
    key: 'Home',
    description: '移动到第一个元素',
    action: () => focusFirst(),
  },
  {
    key: 'End',
    description: '移动到最后一个元素',
    action: () => focusLast(),
  },
  {
    key: 'Arrow Keys',
    description: '在列表或菜单中导航',
    action: () => {},
  },
]

// 应用特定快捷键
const applicationShortcuts: Shortcut[] = [
  {
    key: 'Ctrl + K',
    description: '打开命令面板',
    action: () => openCommandPalette(),
  },
  {
    key: 'Ctrl + /',
    description: '切换侧边栏',
    action: () => toggleSidebar(),
  },
  {
    key: 'Ctrl + Shift + P',
    description: '打开设置',
    action: () => openSettings(),
  },
  {
    key: 'F1',
    description: '打开帮助',
    action: () => openHelp(),
  },
]

// 计算属性
const indicatorStyle = computed(() => {
  if (!currentElement.value?.element) return {}
  
  const rect = currentElement.value.element.getBoundingClientRect()
  return {
    top: `${rect.top - 40}px`,
    left: `${rect.left}px`,
    width: `${rect.width}px`,
  }
})

// 方法
const updateNavigableElements = () => {
  updateFocusableElements()
  
  navigableElements.value = focusableElements.value.map(element => ({
    element,
    label: getElementLabel(element),
    shortcut: getElementShortcut(element),
    type: getElementType(element),
  }))
}

const getElementLabel = (element: HTMLElement): string => {
  // 优先级：aria-label > aria-labelledby > title > text content > tag name
  if (element.getAttribute('aria-label')) {
    return element.getAttribute('aria-label')!
  }
  
  const labelledBy = element.getAttribute('aria-labelledby')
  if (labelledBy) {
    const labelElement = document.getElementById(labelledBy)
    if (labelElement) {
      return labelElement.textContent?.trim() || ''
    }
  }
  
  if (element.title) {
    return element.title
  }
  
  const textContent = element.textContent?.trim()
  if (textContent && textContent.length < 50) {
    return textContent
  }
  
  if (element.tagName === 'INPUT') {
    const input = element as HTMLInputElement
    return input.placeholder || input.type || '输入框'
  }
  
  return element.tagName.toLowerCase()
}

const getElementShortcut = (element: HTMLElement): string | undefined => {
  const accessKey = element.getAttribute('accesskey')
  if (accessKey) {
    return `Alt + ${accessKey.toUpperCase()}`
  }
  
  // 检查是否有自定义快捷键属性
  const shortcut = element.getAttribute('data-shortcut')
  if (shortcut) {
    return shortcut
  }
  
  return undefined
}

const getElementType = (element: HTMLElement): NavigationElement['type'] => {
  const tagName = element.tagName.toLowerCase()
  
  switch (tagName) {
    case 'button':
      return 'button'
    case 'a':
      return 'link'
    case 'input':
      return 'input'
    case 'select':
      return 'select'
    case 'textarea':
      return 'textarea'
    default:
      return 'other'
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  const { key, altKey, ctrlKey, shiftKey, metaKey } = event
  
  // 检查是否匹配快捷键
  const allShortcuts = [
    ...generalShortcuts,
    ...navigationShortcuts,
    ...applicationShortcuts,
  ]
  
  for (const shortcut of allShortcuts) {
    if (matchesShortcut(event, shortcut.key)) {
      event.preventDefault()
      shortcut.action()
      return
    }
  }
  
  // 特殊键处理
  switch (key) {
    case 'F6':
      event.preventDefault()
      cycleFocusRegions()
      break
      
    case 'F10':
      event.preventDefault()
      toggleNavigationMode()
      break
      
    case 'Insert':
      if (ctrlKey) {
        event.preventDefault()
        toggleIndicator()
      }
      break
  }
}

const matchesShortcut = (event: KeyboardEvent, shortcutKey: string): boolean => {
  const parts = shortcutKey.toLowerCase().split(' + ')
  const key = parts[parts.length - 1]
  
  const hasAlt = parts.includes('alt') === event.altKey
  const hasCtrl = parts.includes('ctrl') === event.ctrlKey
  const hasShift = parts.includes('shift') === event.shiftKey
  const hasMeta = parts.includes('meta') === event.metaKey
  
  const keyMatches = event.key.toLowerCase() === key || 
                    event.code.toLowerCase() === key.replace(' ', '')
  
  return hasAlt && hasCtrl && hasShift && hasMeta && keyMatches
}

const handleFocusChange = (event: FocusEvent) => {
  const target = event.target as HTMLElement
  if (!target) return
  
  const elementIndex = navigableElements.value.findIndex(
    nav => nav.element === target
  )
  
  if (elementIndex !== -1) {
    currentFocusIndex.value = elementIndex
    currentElement.value = navigableElements.value[elementIndex]
    
    if (showIndicator.value) {
      updateIndicatorPosition()
    }
    
    // 宣布当前元素
    announce(`聚焦到 ${currentElement.value.label}`)
  }
}

const updateIndicatorPosition = () => {
  nextTick(() => {
    // 指示器位置会通过计算属性自动更新
  })
}

const toggleNavigationMode = () => {
  showNavigation.value = !showNavigation.value
  
  if (showNavigation.value) {
    updateNavigableElements()
    announce('键盘导航模式已启用')
  } else {
    announce('键盘导航模式已禁用')
  }
}

const toggleIndicator = () => {
  showIndicator.value = !showIndicator.value
  announce(showIndicator.value ? '焦点指示器已启用' : '焦点指示器已禁用')
}

const toggleHelpPanel = () => {
  showHelpPanel.value = !showHelpPanel.value
  
  if (showHelpPanel.value) {
    // 陷阱焦点在帮助面板内
    nextTick(() => {
      const helpPanel = document.querySelector('.help-panel') as HTMLElement
      if (helpPanel) {
        trapFocus(helpPanel)
      }
    })
    announce('键盘快捷键帮助已打开')
  } else {
    announce('键盘快捷键帮助已关闭')
  }
}

const closeHelpPanel = () => {
  showHelpPanel.value = false
  announce('键盘快捷键帮助已关闭')
}

const cycleFocusRegions = () => {
  const regions = [
    '#navigation',
    '#main-content',
    '#sidebar',
    '#footer',
  ]
  
  // 简单的区域循环逻辑
  const currentRegion = document.activeElement?.closest('[role="region"], nav, main, aside, footer')
  let nextRegionIndex = 0
  
  if (currentRegion) {
    const currentId = currentRegion.id
    const currentIndex = regions.findIndex(region => region === `#${currentId}`)
    nextRegionIndex = (currentIndex + 1) % regions.length
  }
  
  const nextRegion = document.querySelector(regions[nextRegionIndex]) as HTMLElement
  if (nextRegion) {
    nextRegion.focus()
    announce(`已切换到 ${nextRegion.getAttribute('aria-label') || '区域'}`)
  }
}

const handleEscape = () => {
  if (showHelpPanel.value) {
    closeHelpPanel()
  } else if (focusTrapActive.value) {
    releaseFocusTrap()
  } else {
    // 关闭任何打开的模态框或下拉菜单
    const openModal = document.querySelector('[role="dialog"][aria-hidden="false"]')
    const openDropdown = document.querySelector('[aria-expanded="true"]')
    
    if (openModal) {
      (openModal as HTMLElement).dispatchEvent(new CustomEvent('close'))
    } else if (openDropdown) {
      (openDropdown as HTMLElement).dispatchEvent(new CustomEvent('close'))
    }
  }
}

const jumpToMain = () => {
  const mainContent = document.getElementById('main-content') || 
                     document.querySelector('main')
  if (mainContent) {
    (mainContent as HTMLElement).focus()
    announce('已跳转到主内容')
  }
}

const jumpToNavigation = () => {
  const navigation = document.getElementById('navigation') || 
                    document.querySelector('nav')
  if (navigation) {
    (navigation as HTMLElement).focus()
    announce('已跳转到导航')
  }
}

const jumpToSearch = () => {
  const search = document.getElementById('search') || 
                document.querySelector('[role="search"]') ||
                document.querySelector('input[type="search"]')
  if (search) {
    (search as HTMLElement).focus()
    announce('已跳转到搜索')
  }
}

const openCommandPalette = () => {
  // 触发命令面板打开事件
  window.dispatchEvent(new CustomEvent('open-command-palette'))
  announce('命令面板已打开')
}

const toggleSidebar = () => {
  // 触发侧边栏切换事件
  window.dispatchEvent(new CustomEvent('toggle-sidebar'))
  announce('侧边栏已切换')
}

const openSettings = () => {
  // 触发设置打开事件
  window.dispatchEvent(new CustomEvent('open-settings'))
  announce('设置已打开')
}

const openHelp = () => {
  toggleHelpPanel()
}

const releaseFocusTrap = () => {
  focusTrapActive.value = false
  announce('焦点陷阱已释放')
}

const handleSkipLinkClick = (event: Event) => {
  const link = event.target as HTMLAnchorElement
  const targetId = link.getAttribute('href')?.substring(1)
  
  if (targetId) {
    const target = document.getElementById(targetId)
    if (target) {
      target.focus()
      announce(`已跳转到 ${target.getAttribute('aria-label') || targetId}`)
    }
  }
}

// 生命周期
onMounted(() => {
  updateNavigableElements()
  
  // 添加事件监听器
  document.addEventListener('keydown', handleKeyDown)
  document.addEventListener('focusin', handleFocusChange)
  
  // 监听窗口大小变化，更新可聚焦元素
  window.addEventListener('resize', updateNavigableElements)
  
  // 监听 DOM 变化
  const observer = new MutationObserver(() => {
    updateNavigableElements()
  })
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['tabindex', 'disabled', 'aria-hidden'],
  })
  
  // 存储观察器以便清理
  ;(window as any).__keyboardNavObserver = observer
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('focusin', handleFocusChange)
  window.removeEventListener('resize', updateNavigableElements)
  
  // 清理 DOM 观察器
  const observer = (window as any).__keyboardNavObserver
  if (observer) {
    observer.disconnect()
    delete (window as any).__keyboardNavObserver
  }
})
</script>

<style scoped>
/* 键盘导航样式 */
.keyboard-navigation {
  @apply relative;
}

/* 导航指示器 */
.nav-indicator {
  @apply fixed z-50 pointer-events-none;
  @apply bg-blue-600 text-white text-xs rounded shadow-lg;
  @apply transition-all duration-200 ease-out;
}

.indicator-content {
  @apply flex items-center justify-between px-2 py-1;
}

.indicator-text {
  @apply font-medium;
}

.indicator-shortcut {
  @apply ml-2 opacity-75;
}

/* 帮助面板 */
.help-panel {
  @apply fixed inset-4 z-50 bg-white rounded-lg shadow-xl;
  @apply max-w-4xl mx-auto max-h-[90vh] overflow-hidden;
  @apply flex flex-col;
}

.theme-dark .help-panel {
  @apply bg-gray-800;
}

.help-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.theme-dark .help-header {
  @apply border-gray-700;
}

.help-title {
  @apply text-xl font-semibold text-gray-900;
}

.theme-dark .help-title {
  @apply text-gray-100;
}

.help-close {
  @apply p-2 text-gray-500 hover:text-gray-700 rounded;
}

.theme-dark .help-close {
  @apply text-gray-400 hover:text-gray-200;
}

.close-icon {
  @apply w-5 h-5;
}

.help-content {
  @apply flex-1 overflow-y-auto p-6 space-y-6;
}

/* 快捷键部分 */
.shortcut-section {
  @apply space-y-3;
}

.section-title {
  @apply text-lg font-medium text-gray-900 border-b border-gray-200 pb-2;
}

.theme-dark .section-title {
  @apply text-gray-100 border-gray-700;
}

.shortcut-list {
  @apply space-y-2;
}

.shortcut-item {
  @apply flex items-center justify-between py-2 px-3 rounded;
  @apply hover:bg-gray-50;
}

.theme-dark .shortcut-item {
  @apply hover:bg-gray-700;
}

.shortcut-key {
  @apply px-2 py-1 text-xs font-mono bg-gray-100 border border-gray-300 rounded;
  @apply text-gray-800;
}

.theme-dark .shortcut-key {
  @apply bg-gray-700 border-gray-600 text-gray-200;
}

.shortcut-description {
  @apply text-sm text-gray-600 ml-4 flex-1;
}

.theme-dark .shortcut-description {
  @apply text-gray-400;
}

/* 帮助面板底部 */
.help-footer {
  @apply flex justify-end p-6 border-t border-gray-200;
}

.theme-dark .help-footer {
  @apply border-gray-700;
}

.help-ok-button {
  @apply px-4 py-2 bg-blue-600 text-white rounded;
  @apply hover:bg-blue-700 transition-colors duration-200;
}

/* 焦点陷阱覆盖层 */
.focus-trap-overlay {
  @apply fixed inset-0 bg-black bg-opacity-25 z-40;
}

/* 跳转链接 */
.skip-links {
  @apply absolute top-0 left-0 z-50;
}

.skip-link {
  @apply absolute top-0 left-0 px-4 py-2 bg-blue-600 text-white;
  @apply transform -translate-y-full focus:translate-y-0;
  @apply transition-transform duration-200;
  @apply text-sm font-medium rounded-b;
}

.skip-link:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* 过渡动画 */
.help-panel-enter-active,
.help-panel-leave-active {
  @apply transition-all duration-300 ease-out;
}

.help-panel-enter-from,
.help-panel-leave-to {
  @apply opacity-0 transform scale-95;
}

/* 键盘导航可见状态 */
.nav-visible .nav-indicator {
  @apply opacity-100;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .nav-indicator {
    @apply border-2 border-white;
  }
  
  .help-panel {
    @apply border-2 border-gray-400;
  }
  
  .shortcut-key {
    @apply border-2;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .nav-indicator,
  .help-panel-enter-active,
  .help-panel-leave-active,
  .skip-link {
    @apply transition-none;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .help-panel {
    @apply inset-2;
  }
  
  .help-content {
    @apply p-4 space-y-4;
  }
  
  .shortcut-item {
    @apply flex-col items-start gap-1;
  }
  
  .shortcut-description {
    @apply ml-0;
  }
}

/* 焦点样式增强 */
.keyboard-navigation :deep(*:focus) {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.theme-dark .keyboard-navigation :deep(*:focus) {
  @apply ring-offset-gray-800;
}

/* 可聚焦元素高亮 */
.nav-visible :deep([tabindex]:not([tabindex="-1"])),
.nav-visible :deep(button:not([disabled])),
.nav-visible :deep(a[href]),
.nav-visible :deep(input:not([disabled])),
.nav-visible :deep(select:not([disabled])),
.nav-visible :deep(textarea:not([disabled])) {
  @apply ring-1 ring-blue-300 ring-opacity-50;
}
</style>
