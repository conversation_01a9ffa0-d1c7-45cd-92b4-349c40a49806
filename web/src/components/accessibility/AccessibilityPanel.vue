<template>
  <div class="accessibility-panel" :class="{ 'panel-open': isOpen }">
    <!-- 触发按钮 -->
    <button
      @click="togglePanel"
      class="accessibility-trigger"
      :aria-label="$t('accessibility.openPanel')"
      :aria-expanded="isOpen"
    >
      <UniversalAccessIcon class="trigger-icon" />
      <span class="sr-only">{{ $t('accessibility.openPanel') }}</span>
    </button>

    <!-- 面板内容 -->
    <Transition name="panel">
      <div
        v-if="isOpen"
        class="panel-content"
        role="dialog"
        :aria-label="$t('accessibility.panelTitle')"
        aria-modal="true"
      >
        <div class="panel-header">
          <h2 class="panel-title">{{ $t('accessibility.panelTitle') }}</h2>
          <button
            @click="closePanel"
            class="close-button"
            :aria-label="$t('common.close')"
          >
            <XMarkIcon class="close-icon" />
          </button>
        </div>

        <div class="panel-body">
          <!-- 字体大小调节 -->
          <div class="setting-group">
            <h3 class="setting-title">{{ $t('accessibility.fontSize') }}</h3>
            <div class="font-size-controls">
              <button
                @click="decreaseFontSize"
                class="font-button"
                :disabled="fontSize <= minFontSize"
                :aria-label="$t('accessibility.decreaseFontSize')"
              >
                <MinusIcon class="button-icon" />
                A-
              </button>
              
              <span class="font-size-display" :aria-live="polite">
                {{ fontSize }}px
              </span>
              
              <button
                @click="increaseFontSize"
                class="font-button"
                :disabled="fontSize >= maxFontSize"
                :aria-label="$t('accessibility.increaseFontSize')"
              >
                <PlusIcon class="button-icon" />
                A+
              </button>
              
              <button
                @click="resetFontSize"
                class="reset-button"
                :aria-label="$t('accessibility.resetFontSize')"
              >
                {{ $t('common.reset') }}
              </button>
            </div>
          </div>

          <!-- 对比度调节 -->
          <div class="setting-group">
            <h3 class="setting-title">{{ $t('accessibility.contrast') }}</h3>
            <div class="contrast-controls">
              <label class="contrast-option">
                <input
                  v-model="contrastMode"
                  type="radio"
                  value="normal"
                  name="contrast"
                  class="sr-only"
                />
                <span class="option-button" :class="{ 'option-active': contrastMode === 'normal' }">
                  {{ $t('accessibility.normalContrast') }}
                </span>
              </label>
              
              <label class="contrast-option">
                <input
                  v-model="contrastMode"
                  type="radio"
                  value="high"
                  name="contrast"
                  class="sr-only"
                />
                <span class="option-button" :class="{ 'option-active': contrastMode === 'high' }">
                  {{ $t('accessibility.highContrast') }}
                </span>
              </label>
              
              <label class="contrast-option">
                <input
                  v-model="contrastMode"
                  type="radio"
                  value="inverted"
                  name="contrast"
                  class="sr-only"
                />
                <span class="option-button" :class="{ 'option-active': contrastMode === 'inverted' }">
                  {{ $t('accessibility.invertedColors') }}
                </span>
              </label>
            </div>
          </div>

          <!-- 动画控制 -->
          <div class="setting-group">
            <h3 class="setting-title">{{ $t('accessibility.animations') }}</h3>
            <label class="toggle-option">
              <input
                v-model="reduceMotion"
                type="checkbox"
                class="sr-only"
                @change="toggleReduceMotion"
              />
              <span class="toggle-button" :class="{ 'toggle-active': reduceMotion }">
                <span class="toggle-slider"></span>
              </span>
              <span class="toggle-label">{{ $t('accessibility.reduceMotion') }}</span>
            </label>
          </div>

          <!-- 焦点指示器 -->
          <div class="setting-group">
            <h3 class="setting-title">{{ $t('accessibility.focusIndicator') }}</h3>
            <label class="toggle-option">
              <input
                v-model="enhancedFocus"
                type="checkbox"
                class="sr-only"
                @change="toggleEnhancedFocus"
              />
              <span class="toggle-button" :class="{ 'toggle-active': enhancedFocus }">
                <span class="toggle-slider"></span>
              </span>
              <span class="toggle-label">{{ $t('accessibility.enhancedFocus') }}</span>
            </label>
          </div>

          <!-- 屏幕阅读器支持 -->
          <div class="setting-group">
            <h3 class="setting-title">{{ $t('accessibility.screenReader') }}</h3>
            <label class="toggle-option">
              <input
                v-model="screenReaderMode"
                type="checkbox"
                class="sr-only"
                @change="toggleScreenReaderMode"
              />
              <span class="toggle-button" :class="{ 'toggle-active': screenReaderMode }">
                <span class="toggle-slider"></span>
              </span>
              <span class="toggle-label">{{ $t('accessibility.screenReaderMode') }}</span>
            </label>
          </div>

          <!-- 键盘导航帮助 -->
          <div class="setting-group">
            <h3 class="setting-title">{{ $t('accessibility.keyboardNavigation') }}</h3>
            <button
              @click="showKeyboardHelp = !showKeyboardHelp"
              class="help-button"
              :aria-expanded="showKeyboardHelp"
            >
              {{ $t('accessibility.showKeyboardShortcuts') }}
              <ChevronDownIcon 
                class="help-icon" 
                :class="{ 'icon-rotated': showKeyboardHelp }"
              />
            </button>
            
            <Transition name="help">
              <div v-if="showKeyboardHelp" class="keyboard-help">
                <dl class="shortcut-list">
                  <div class="shortcut-item">
                    <dt class="shortcut-key">Alt + M</dt>
                    <dd class="shortcut-desc">{{ $t('accessibility.jumpToMain') }}</dd>
                  </div>
                  <div class="shortcut-item">
                    <dt class="shortcut-key">Alt + N</dt>
                    <dd class="shortcut-desc">{{ $t('accessibility.jumpToNav') }}</dd>
                  </div>
                  <div class="shortcut-item">
                    <dt class="shortcut-key">Tab</dt>
                    <dd class="shortcut-desc">{{ $t('accessibility.nextElement') }}</dd>
                  </div>
                  <div class="shortcut-item">
                    <dt class="shortcut-key">Shift + Tab</dt>
                    <dd class="shortcut-desc">{{ $t('accessibility.prevElement') }}</dd>
                  </div>
                  <div class="shortcut-item">
                    <dt class="shortcut-key">Escape</dt>
                    <dd class="shortcut-desc">{{ $t('accessibility.closeDialog') }}</dd>
                  </div>
                </dl>
              </div>
            </Transition>
          </div>

          <!-- 重置所有设置 -->
          <div class="setting-group">
            <button
              @click="resetAllSettings"
              class="reset-all-button"
            >
              {{ $t('accessibility.resetAllSettings') }}
            </button>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 背景遮罩 -->
    <div
      v-if="isOpen"
      class="panel-backdrop"
      @click="closePanel"
      aria-hidden="true"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import {
  UniversalAccessIcon,
  XMarkIcon,
  MinusIcon,
  PlusIcon,
  ChevronDownIcon,
} from '@heroicons/vue/24/outline'
import { useAccessibility } from '@/composables/useAccessibility'

// 响应式数据
const isOpen = ref(false)
const fontSize = ref(16)
const contrastMode = ref<'normal' | 'high' | 'inverted'>('normal')
const reduceMotion = ref(false)
const enhancedFocus = ref(false)
const screenReaderMode = ref(false)
const showKeyboardHelp = ref(false)

// 常量
const minFontSize = 12
const maxFontSize = 24
const defaultFontSize = 16

// 无障碍工具
const { announce, trapFocus } = useAccessibility()

// 方法
const togglePanel = () => {
  isOpen.value = !isOpen.value
  
  if (isOpen.value) {
    announce('无障碍设置面板已打开')
    // 下一帧陷阱焦点
    setTimeout(() => {
      const panel = document.querySelector('.panel-content') as HTMLElement
      if (panel) {
        trapFocus(panel)
      }
    }, 100)
  } else {
    announce('无障碍设置面板已关闭')
  }
}

const closePanel = () => {
  isOpen.value = false
  announce('无障碍设置面板已关闭')
}

const increaseFontSize = () => {
  if (fontSize.value < maxFontSize) {
    fontSize.value += 2
    announce(`字体大小已增加到 ${fontSize.value} 像素`)
  }
}

const decreaseFontSize = () => {
  if (fontSize.value > minFontSize) {
    fontSize.value -= 2
    announce(`字体大小已减少到 ${fontSize.value} 像素`)
  }
}

const resetFontSize = () => {
  fontSize.value = defaultFontSize
  announce(`字体大小已重置为 ${fontSize.value} 像素`)
}

const toggleReduceMotion = () => {
  announce(reduceMotion.value ? '已启用减少动画' : '已禁用减少动画')
}

const toggleEnhancedFocus = () => {
  announce(enhancedFocus.value ? '已启用增强焦点指示器' : '已禁用增强焦点指示器')
}

const toggleScreenReaderMode = () => {
  announce(screenReaderMode.value ? '已启用屏幕阅读器模式' : '已禁用屏幕阅读器模式')
}

const resetAllSettings = () => {
  fontSize.value = defaultFontSize
  contrastMode.value = 'normal'
  reduceMotion.value = false
  enhancedFocus.value = false
  screenReaderMode.value = false
  
  announce('所有无障碍设置已重置')
}

// 应用设置
const applyFontSize = () => {
  document.documentElement.style.setProperty('--base-font-size', `${fontSize.value}px`)
}

const applyContrastMode = () => {
  document.documentElement.classList.remove('high-contrast', 'inverted-colors')
  
  if (contrastMode.value === 'high') {
    document.documentElement.classList.add('high-contrast')
    announce('已启用高对比度模式')
  } else if (contrastMode.value === 'inverted') {
    document.documentElement.classList.add('inverted-colors')
    announce('已启用反色模式')
  } else {
    announce('已恢复正常对比度')
  }
}

const applyReduceMotion = () => {
  if (reduceMotion.value) {
    document.documentElement.classList.add('reduce-motion')
  } else {
    document.documentElement.classList.remove('reduce-motion')
  }
}

const applyEnhancedFocus = () => {
  if (enhancedFocus.value) {
    document.documentElement.classList.add('enhanced-focus')
  } else {
    document.documentElement.classList.remove('enhanced-focus')
  }
}

const applyScreenReaderMode = () => {
  if (screenReaderMode.value) {
    document.documentElement.classList.add('screen-reader-mode')
  } else {
    document.documentElement.classList.remove('screen-reader-mode')
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isOpen.value) {
    closePanel()
  }
}

// 监听设置变化
watch(fontSize, applyFontSize)
watch(contrastMode, applyContrastMode)
watch(reduceMotion, applyReduceMotion)
watch(enhancedFocus, applyEnhancedFocus)
watch(screenReaderMode, applyScreenReaderMode)

// 生命周期
onMounted(() => {
  // 加载保存的设置
  loadSettings()
  
  // 应用初始设置
  applyFontSize()
  applyContrastMode()
  applyReduceMotion()
  applyEnhancedFocus()
  applyScreenReaderMode()
  
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 设置持久化
const saveSettings = () => {
  const settings = {
    fontSize: fontSize.value,
    contrastMode: contrastMode.value,
    reduceMotion: reduceMotion.value,
    enhancedFocus: enhancedFocus.value,
    screenReaderMode: screenReaderMode.value,
  }
  
  try {
    localStorage.setItem('accessibility-settings', JSON.stringify(settings))
  } catch (error) {
    console.warn('Failed to save accessibility settings:', error)
  }
}

const loadSettings = () => {
  try {
    const saved = localStorage.getItem('accessibility-settings')
    if (saved) {
      const settings = JSON.parse(saved)
      fontSize.value = settings.fontSize || defaultFontSize
      contrastMode.value = settings.contrastMode || 'normal'
      reduceMotion.value = settings.reduceMotion || false
      enhancedFocus.value = settings.enhancedFocus || false
      screenReaderMode.value = settings.screenReaderMode || false
    }
  } catch (error) {
    console.warn('Failed to load accessibility settings:', error)
  }
}

// 监听设置变化并保存
watch([fontSize, contrastMode, reduceMotion, enhancedFocus, screenReaderMode], saveSettings)
</script>

<style scoped>
/* 无障碍面板样式 */
.accessibility-panel {
  @apply fixed bottom-4 right-4 z-50;
}

.accessibility-trigger {
  @apply w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg;
  @apply hover:bg-blue-700 focus:bg-blue-700;
  @apply transition-all duration-200;
  @apply flex items-center justify-center;
}

.trigger-icon {
  @apply w-6 h-6;
}

.panel-content {
  @apply absolute bottom-16 right-0 w-80 max-w-[90vw];
  @apply bg-white border border-gray-200 rounded-lg shadow-xl;
  @apply max-h-96 overflow-y-auto;
}

.theme-dark .panel-content {
  @apply bg-gray-800 border-gray-700;
}

.panel-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.theme-dark .panel-header {
  @apply border-gray-700;
}

.panel-title {
  @apply text-lg font-semibold text-gray-900;
}

.theme-dark .panel-title {
  @apply text-gray-100;
}

.close-button {
  @apply p-1 text-gray-500 hover:text-gray-700 rounded;
}

.theme-dark .close-button {
  @apply text-gray-400 hover:text-gray-200;
}

.close-icon {
  @apply w-5 h-5;
}

.panel-body {
  @apply p-4 space-y-6;
}

.setting-group {
  @apply space-y-3;
}

.setting-title {
  @apply text-sm font-medium text-gray-900;
}

.theme-dark .setting-title {
  @apply text-gray-100;
}

/* 字体大小控制 */
.font-size-controls {
  @apply flex items-center gap-2;
}

.font-button {
  @apply px-3 py-1 text-sm border border-gray-300 rounded;
  @apply hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;
}

.theme-dark .font-button {
  @apply border-gray-600 hover:bg-gray-700;
}

.button-icon {
  @apply w-3 h-3 inline mr-1;
}

.font-size-display {
  @apply px-2 py-1 text-sm font-mono bg-gray-100 rounded;
}

.theme-dark .font-size-display {
  @apply bg-gray-700;
}

.reset-button {
  @apply px-3 py-1 text-sm text-blue-600 hover:text-blue-700;
}

.theme-dark .reset-button {
  @apply text-blue-400 hover:text-blue-300;
}

/* 对比度控制 */
.contrast-controls {
  @apply space-y-2;
}

.contrast-option {
  @apply block cursor-pointer;
}

.option-button {
  @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded;
  @apply hover:bg-gray-50 transition-colors duration-200;
}

.theme-dark .option-button {
  @apply border-gray-600 hover:bg-gray-700;
}

.option-active {
  @apply bg-blue-50 border-blue-300 text-blue-700;
}

.theme-dark .option-active {
  @apply bg-blue-900 bg-opacity-20 border-blue-600 text-blue-400;
}

/* 切换开关 */
.toggle-option {
  @apply flex items-center gap-3 cursor-pointer;
}

.toggle-button {
  @apply relative w-10 h-6 bg-gray-300 rounded-full transition-colors duration-200;
}

.toggle-active {
  @apply bg-blue-600;
}

.toggle-slider {
  @apply absolute top-1 left-1 w-4 h-4 bg-white rounded-full;
  @apply transition-transform duration-200;
}

.toggle-active .toggle-slider {
  @apply transform translate-x-4;
}

.toggle-label {
  @apply text-sm text-gray-700;
}

.theme-dark .toggle-label {
  @apply text-gray-300;
}

/* 键盘帮助 */
.help-button {
  @apply flex items-center justify-between w-full px-3 py-2;
  @apply text-sm text-blue-600 hover:text-blue-700;
}

.theme-dark .help-button {
  @apply text-blue-400 hover:text-blue-300;
}

.help-icon {
  @apply w-4 h-4 transition-transform duration-200;
}

.icon-rotated {
  @apply transform rotate-180;
}

.keyboard-help {
  @apply mt-2 p-3 bg-gray-50 rounded;
}

.theme-dark .keyboard-help {
  @apply bg-gray-700;
}

.shortcut-list {
  @apply space-y-2;
}

.shortcut-item {
  @apply flex justify-between items-center;
}

.shortcut-key {
  @apply px-2 py-1 text-xs font-mono bg-gray-200 rounded;
}

.theme-dark .shortcut-key {
  @apply bg-gray-600;
}

.shortcut-desc {
  @apply text-xs text-gray-600;
}

.theme-dark .shortcut-desc {
  @apply text-gray-400;
}

/* 重置按钮 */
.reset-all-button {
  @apply w-full px-4 py-2 text-sm text-red-600 border border-red-300 rounded;
  @apply hover:bg-red-50 hover:text-red-700;
}

.theme-dark .reset-all-button {
  @apply text-red-400 border-red-600 hover:bg-red-900 hover:bg-opacity-20;
}

/* 背景遮罩 */
.panel-backdrop {
  @apply fixed inset-0 bg-black bg-opacity-25 z-40;
}

/* 屏幕阅读器专用 */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 过渡动画 */
.panel-enter-active,
.panel-leave-active {
  @apply transition-all duration-300 ease-out;
}

.panel-enter-from,
.panel-leave-to {
  @apply opacity-0 transform scale-95 translate-y-2;
}

.help-enter-active,
.help-leave-active {
  @apply transition-all duration-200 ease-out;
}

.help-enter-from,
.help-leave-to {
  @apply opacity-0 transform -translate-y-2;
}

/* 无障碍增强样式 */
.enhanced-focus *:focus {
  @apply outline-none ring-4 ring-blue-500 ring-opacity-50;
}

.high-contrast {
  filter: contrast(150%);
}

.inverted-colors {
  filter: invert(1) hue-rotate(180deg);
}

.reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

.screen-reader-mode .sr-only {
  @apply static w-auto h-auto p-1 m-0 overflow-visible;
  clip: auto;
  white-space: normal;
  border: 1px solid;
}
</style>
