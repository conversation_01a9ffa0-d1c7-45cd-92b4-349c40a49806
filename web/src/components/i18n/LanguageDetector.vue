<template>
  <div class="language-detector">
    <!-- 语言建议横幅 -->
    <Transition name="banner">
      <div
        v-if="showLanguageBanner"
        class="language-banner"
        role="banner"
        :aria-label="$t('i18n.languageSuggestionBanner')"
      >
        <div class="banner-content">
          <div class="banner-icon">
            <GlobeAltIcon class="icon" />
          </div>
          
          <div class="banner-text">
            <p class="banner-message">
              {{ $t('i18n.languageSuggestion', { language: suggestedLanguage?.name }) }}
            </p>
            <p class="banner-description">
              {{ $t('i18n.languageSuggestionDescription') }}
            </p>
          </div>
          
          <div class="banner-actions">
            <button
              @click="acceptLanguageSuggestion"
              class="accept-button"
              :aria-label="$t('i18n.switchToLanguage', { language: suggestedLanguage?.name })"
            >
              {{ $t('i18n.switchLanguage') }}
            </button>
            
            <button
              @click="dismissLanguageBanner"
              class="dismiss-button"
              :aria-label="$t('i18n.dismissSuggestion')"
            >
              {{ $t('common.dismiss') }}
            </button>
          </div>
        </div>
        
        <button
          @click="dismissLanguageBanner"
          class="close-button"
          :aria-label="$t('common.close')"
        >
          <XMarkIcon class="close-icon" />
        </button>
      </div>
    </Transition>

    <!-- 语言切换确认对话框 -->
    <Teleport to="body">
      <Transition name="modal">
        <div
          v-if="showConfirmDialog"
          class="modal-overlay"
          @click="cancelLanguageSwitch"
        >
          <div
            class="confirm-dialog"
            role="dialog"
            :aria-labelledby="confirmDialogTitleId"
            :aria-describedby="confirmDialogDescId"
            @click.stop
          >
            <div class="dialog-header">
              <h3 :id="confirmDialogTitleId" class="dialog-title">
                {{ $t('i18n.confirmLanguageSwitch') }}
              </h3>
              <button
                @click="cancelLanguageSwitch"
                class="dialog-close"
                :aria-label="$t('common.close')"
              >
                <XMarkIcon class="close-icon" />
              </button>
            </div>
            
            <div class="dialog-body">
              <p :id="confirmDialogDescId" class="dialog-description">
                {{ $t('i18n.confirmLanguageSwitchDescription', { 
                  from: currentLanguage?.name,
                  to: targetLanguage?.name 
                }) }}
              </p>
              
              <div class="language-preview">
                <div class="preview-item">
                  <span class="preview-label">{{ $t('i18n.currentLanguage') }}:</span>
                  <div class="preview-language">
                    <span class="language-flag">{{ currentLanguage?.flag }}</span>
                    <span class="language-name">{{ currentLanguage?.name }}</span>
                  </div>
                </div>
                
                <div class="preview-arrow">
                  <ArrowRightIcon class="arrow-icon" />
                </div>
                
                <div class="preview-item">
                  <span class="preview-label">{{ $t('i18n.newLanguage') }}:</span>
                  <div class="preview-language">
                    <span class="language-flag">{{ targetLanguage?.flag }}</span>
                    <span class="language-name">{{ targetLanguage?.name }}</span>
                  </div>
                </div>
              </div>
              
              <div class="dialog-options">
                <label class="option-checkbox">
                  <input
                    v-model="rememberChoice"
                    type="checkbox"
                    class="checkbox-input"
                  />
                  <span class="checkbox-label">
                    {{ $t('i18n.rememberLanguageChoice') }}
                  </span>
                </label>
              </div>
            </div>
            
            <div class="dialog-footer">
              <button
                @click="cancelLanguageSwitch"
                class="cancel-button"
              >
                {{ $t('common.cancel') }}
              </button>
              
              <button
                @click="confirmLanguageSwitch"
                class="confirm-button"
              >
                {{ $t('i18n.switchLanguage') }}
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>

    <!-- 语言学习提示 -->
    <div
      v-if="showLanguageLearningTip"
      class="learning-tip"
      role="complementary"
    >
      <div class="tip-content">
        <div class="tip-icon">
          <AcademicCapIcon class="icon" />
        </div>
        
        <div class="tip-text">
          <h4 class="tip-title">{{ $t('i18n.languageLearningTip') }}</h4>
          <p class="tip-description">
            {{ $t('i18n.languageLearningDescription', { language: currentLanguage?.name }) }}
          </p>
        </div>
        
        <button
          @click="dismissLearningTip"
          class="tip-close"
          :aria-label="$t('common.close')"
        >
          <XMarkIcon class="close-icon" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  GlobeAltIcon,
  XMarkIcon,
  ArrowRightIcon,
  AcademicCapIcon,
} from '@heroicons/vue/24/outline'
import { useI18nUtils } from '@/composables/useI18nUtils'
import { supportedLocales, type SupportedLocale } from '@/i18n'

// 生成唯一 ID
const confirmDialogTitleId = `confirm-dialog-title-${Math.random().toString(36).substr(2, 9)}`
const confirmDialogDescId = `confirm-dialog-desc-${Math.random().toString(36).substr(2, 9)}`

// 响应式数据
const showLanguageBanner = ref(false)
const showConfirmDialog = ref(false)
const showLanguageLearningTip = ref(false)
const suggestedLanguage = ref<typeof supportedLocales[number] | null>(null)
const targetLanguage = ref<typeof supportedLocales[number] | null>(null)
const rememberChoice = ref(false)

// 国际化工具
const { currentLocale, currentLocaleInfo, switchLocale } = useI18nUtils()

// 计算属性
const currentLanguage = computed(() => currentLocaleInfo.value)

// 语言检测逻辑
const detectBrowserLanguage = (): SupportedLocale | null => {
  const browserLanguages = navigator.languages || [navigator.language]
  
  for (const browserLang of browserLanguages) {
    // 精确匹配
    const exactMatch = supportedLocales.find(locale => locale.code === browserLang)
    if (exactMatch) {
      return exactMatch.code
    }
    
    // 语言代码匹配（忽略地区）
    const langCode = browserLang.split('-')[0]
    const langMatch = supportedLocales.find(locale => locale.code.startsWith(langCode))
    if (langMatch) {
      return langMatch.code
    }
  }
  
  return null
}

// 检测用户地理位置对应的语言
const detectLocationLanguage = async (): Promise<SupportedLocale | null> => {
  try {
    // 使用时区推断地理位置
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    
    const timezoneLanguageMap: Record<string, SupportedLocale> = {
      'Asia/Shanghai': 'zh-CN',
      'Asia/Beijing': 'zh-CN',
      'Asia/Hong_Kong': 'zh-CN',
      'Asia/Taipei': 'zh-CN',
      'America/New_York': 'en-US',
      'America/Los_Angeles': 'en-US',
      'America/Chicago': 'en-US',
      'Asia/Tokyo': 'ja-JP',
      'Asia/Osaka': 'ja-JP',
    }
    
    return timezoneLanguageMap[timezone] || null
  } catch (error) {
    console.warn('Failed to detect location language:', error)
    return null
  }
}

// 检测用户偏好语言
const detectPreferredLanguage = (): SupportedLocale | null => {
  try {
    const stored = localStorage.getItem('user-preferred-language')
    if (stored && supportedLocales.some(locale => locale.code === stored)) {
      return stored as SupportedLocale
    }
  } catch (error) {
    console.warn('Failed to get stored preferred language:', error)
  }
  return null
}

// 检查是否应该显示语言建议
const shouldShowLanguageSuggestion = (detectedLanguage: SupportedLocale): boolean => {
  // 如果当前语言就是检测到的语言，不显示建议
  if (currentLocale.value === detectedLanguage) {
    return false
  }
  
  // 检查用户是否已经拒绝过这个建议
  try {
    const dismissed = localStorage.getItem(`language-suggestion-dismissed-${detectedLanguage}`)
    if (dismissed) {
      const dismissedTime = parseInt(dismissed)
      const now = Date.now()
      const oneWeek = 7 * 24 * 60 * 60 * 1000
      
      // 如果拒绝时间不到一周，不再显示
      if (now - dismissedTime < oneWeek) {
        return false
      }
    }
  } catch (error) {
    console.warn('Failed to check dismissed suggestions:', error)
  }
  
  return true
}

// 显示语言建议
const showLanguageSuggestion = (language: SupportedLocale) => {
  const languageInfo = supportedLocales.find(locale => locale.code === language)
  if (!languageInfo) return
  
  suggestedLanguage.value = languageInfo
  showLanguageBanner.value = true
}

// 接受语言建议
const acceptLanguageSuggestion = () => {
  if (!suggestedLanguage.value) return
  
  targetLanguage.value = suggestedLanguage.value
  showLanguageBanner.value = false
  showConfirmDialog.value = true
}

// 拒绝语言建议
const dismissLanguageBanner = () => {
  if (suggestedLanguage.value) {
    try {
      localStorage.setItem(
        `language-suggestion-dismissed-${suggestedLanguage.value.code}`,
        Date.now().toString()
      )
    } catch (error) {
      console.warn('Failed to save dismissed suggestion:', error)
    }
  }
  
  showLanguageBanner.value = false
  suggestedLanguage.value = null
}

// 确认语言切换
const confirmLanguageSwitch = () => {
  if (!targetLanguage.value) return
  
  // 切换语言
  switchLocale(targetLanguage.value.code)
  
  // 记住用户选择
  if (rememberChoice.value) {
    try {
      localStorage.setItem('user-preferred-language', targetLanguage.value.code)
    } catch (error) {
      console.warn('Failed to save preferred language:', error)
    }
  }
  
  // 关闭对话框
  showConfirmDialog.value = false
  targetLanguage.value = null
  rememberChoice.value = false
  
  // 显示语言学习提示
  setTimeout(() => {
    showLanguageLearningTip.value = true
  }, 1000)
}

// 取消语言切换
const cancelLanguageSwitch = () => {
  showConfirmDialog.value = false
  targetLanguage.value = null
  rememberChoice.value = false
}

// 关闭语言学习提示
const dismissLearningTip = () => {
  showLanguageLearningTip.value = false
}

// 执行语言检测
const performLanguageDetection = async () => {
  // 检查用户偏好
  const preferredLanguage = detectPreferredLanguage()
  if (preferredLanguage && preferredLanguage !== currentLocale.value) {
    if (shouldShowLanguageSuggestion(preferredLanguage)) {
      showLanguageSuggestion(preferredLanguage)
      return
    }
  }
  
  // 检测浏览器语言
  const browserLanguage = detectBrowserLanguage()
  if (browserLanguage && browserLanguage !== currentLocale.value) {
    if (shouldShowLanguageSuggestion(browserLanguage)) {
      showLanguageSuggestion(browserLanguage)
      return
    }
  }
  
  // 检测地理位置语言
  const locationLanguage = await detectLocationLanguage()
  if (locationLanguage && locationLanguage !== currentLocale.value) {
    if (shouldShowLanguageSuggestion(locationLanguage)) {
      showLanguageSuggestion(locationLanguage)
      return
    }
  }
}

// 监听语言变化
watch(currentLocale, (newLocale) => {
  // 语言切换后隐藏所有提示
  showLanguageBanner.value = false
  showConfirmDialog.value = false
  
  // 更新页面语言属性
  document.documentElement.lang = newLocale
  
  // 更新页面方向
  const localeInfo = supportedLocales.find(locale => locale.code === newLocale)
  if (localeInfo) {
    document.documentElement.dir = localeInfo.rtl ? 'rtl' : 'ltr'
  }
})

// 生命周期
onMounted(() => {
  // 延迟执行语言检测，避免影响页面加载
  setTimeout(performLanguageDetection, 2000)
})
</script>

<style scoped>
/* 语言检测器样式 */
.language-detector {
  @apply relative;
}

/* 语言建议横幅 */
.language-banner {
  @apply fixed top-0 left-0 right-0 z-50;
  @apply bg-blue-600 text-white shadow-lg;
}

.banner-content {
  @apply flex items-center gap-4 px-4 py-3 max-w-7xl mx-auto;
}

.banner-icon {
  @apply flex-shrink-0;
}

.banner-icon .icon {
  @apply w-6 h-6;
}

.banner-text {
  @apply flex-1 min-w-0;
}

.banner-message {
  @apply font-medium text-sm;
}

.banner-description {
  @apply text-xs opacity-90 mt-1;
}

.banner-actions {
  @apply flex items-center gap-2;
}

.accept-button {
  @apply px-4 py-2 bg-white text-blue-600 text-sm font-medium rounded;
  @apply hover:bg-blue-50 transition-colors duration-200;
}

.dismiss-button {
  @apply px-3 py-2 text-sm text-blue-100 hover:text-white;
  @apply transition-colors duration-200;
}

.close-button {
  @apply p-2 text-blue-100 hover:text-white rounded;
  @apply transition-colors duration-200;
}

.close-icon {
  @apply w-5 h-5;
}

/* 确认对话框 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-50;
  @apply flex items-center justify-center p-4;
}

.confirm-dialog {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full;
  @apply max-h-[90vh] overflow-y-auto;
}

.theme-dark .confirm-dialog {
  @apply bg-gray-800;
}

.dialog-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.theme-dark .dialog-header {
  @apply border-gray-700;
}

.dialog-title {
  @apply text-lg font-semibold text-gray-900;
}

.theme-dark .dialog-title {
  @apply text-gray-100;
}

.dialog-close {
  @apply p-1 text-gray-500 hover:text-gray-700 rounded;
}

.theme-dark .dialog-close {
  @apply text-gray-400 hover:text-gray-200;
}

.dialog-body {
  @apply p-4 space-y-4;
}

.dialog-description {
  @apply text-sm text-gray-600;
}

.theme-dark .dialog-description {
  @apply text-gray-400;
}

/* 语言预览 */
.language-preview {
  @apply flex items-center gap-4 p-4 bg-gray-50 rounded-lg;
}

.theme-dark .language-preview {
  @apply bg-gray-700;
}

.preview-item {
  @apply flex-1 text-center;
}

.preview-label {
  @apply block text-xs text-gray-500 mb-2;
}

.theme-dark .preview-label {
  @apply text-gray-400;
}

.preview-language {
  @apply flex items-center justify-center gap-2;
}

.language-flag {
  @apply text-lg;
}

.language-name {
  @apply text-sm font-medium text-gray-900;
}

.theme-dark .language-name {
  @apply text-gray-100;
}

.preview-arrow {
  @apply flex-shrink-0;
}

.arrow-icon {
  @apply w-5 h-5 text-gray-400;
}

/* 对话框选项 */
.dialog-options {
  @apply space-y-2;
}

.option-checkbox {
  @apply flex items-center gap-2 cursor-pointer;
}

.checkbox-input {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded;
  @apply focus:ring-blue-500 focus:ring-2;
}

.checkbox-label {
  @apply text-sm text-gray-700;
}

.theme-dark .checkbox-label {
  @apply text-gray-300;
}

/* 对话框底部 */
.dialog-footer {
  @apply flex items-center justify-end gap-3 p-4 border-t border-gray-200;
}

.theme-dark .dialog-footer {
  @apply border-gray-700;
}

.cancel-button {
  @apply px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded;
  @apply hover:bg-gray-50 transition-colors duration-200;
}

.theme-dark .cancel-button {
  @apply text-gray-300 border-gray-600 hover:bg-gray-700;
}

.confirm-button {
  @apply px-4 py-2 text-sm text-white bg-blue-600 rounded;
  @apply hover:bg-blue-700 transition-colors duration-200;
}

/* 语言学习提示 */
.learning-tip {
  @apply fixed bottom-4 right-4 z-40;
  @apply max-w-sm;
}

.tip-content {
  @apply flex items-start gap-3 p-4 bg-green-50 border border-green-200 rounded-lg shadow-lg;
}

.theme-dark .tip-content {
  @apply bg-green-900 bg-opacity-20 border-green-700;
}

.tip-icon {
  @apply flex-shrink-0;
}

.tip-icon .icon {
  @apply w-6 h-6 text-green-600;
}

.theme-dark .tip-icon .icon {
  @apply text-green-400;
}

.tip-text {
  @apply flex-1 min-w-0;
}

.tip-title {
  @apply text-sm font-medium text-green-800 mb-1;
}

.theme-dark .tip-title {
  @apply text-green-300;
}

.tip-description {
  @apply text-xs text-green-700;
}

.theme-dark .tip-description {
  @apply text-green-400;
}

.tip-close {
  @apply p-1 text-green-600 hover:text-green-800 rounded;
}

.theme-dark .tip-close {
  @apply text-green-400 hover:text-green-200;
}

/* 过渡动画 */
.banner-enter-active,
.banner-leave-active {
  @apply transition-all duration-300 ease-out;
}

.banner-enter-from,
.banner-leave-to {
  @apply opacity-0 transform -translate-y-full;
}

.modal-enter-active,
.modal-leave-active {
  @apply transition-all duration-300 ease-out;
}

.modal-enter-from,
.modal-leave-to {
  @apply opacity-0;
}

.modal-enter-from .confirm-dialog,
.modal-leave-to .confirm-dialog {
  @apply transform scale-95;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .banner-content {
    @apply flex-col items-start gap-3;
  }
  
  .banner-actions {
    @apply w-full justify-between;
  }
  
  .language-preview {
    @apply flex-col gap-3;
  }
  
  .preview-arrow {
    @apply transform rotate-90;
  }
  
  .learning-tip {
    @apply bottom-2 right-2 left-2 max-w-none;
  }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .banner-enter-active,
  .banner-leave-active,
  .modal-enter-active,
  .modal-leave-active {
    @apply transition-none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .language-banner {
    @apply border-b-2 border-blue-300;
  }
  
  .confirm-dialog {
    @apply border-2 border-gray-400;
  }
  
  .tip-content {
    @apply border-2;
  }
}
</style>
