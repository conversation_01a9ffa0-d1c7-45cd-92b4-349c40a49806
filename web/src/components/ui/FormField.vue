<template>
  <div :class="fieldClasses">
    <!-- 标签 -->
    <label
      v-if="label"
      :for="fieldId"
      :class="labelClasses"
    >
      {{ label }}
      <span v-if="required" class="required-mark">*</span>
    </label>

    <!-- 输入控件容器 -->
    <div class="field-input-container">
      <!-- 前缀图标 -->
      <div v-if="prefixIcon" class="field-prefix">
        <component :is="prefixIcon" class="field-icon" />
      </div>

      <!-- 输入控件 -->
      <component
        :is="inputComponent"
        :id="fieldId"
        v-model="modelValue"
        v-bind="inputProps"
        :class="inputClasses"
        :disabled="disabled"
        :readonly="readonly"
        :placeholder="placeholder"
        @blur="handleBlur"
        @focus="handleFocus"
        @input="handleInput"
        @change="handleChange"
      >
        <!-- 选择器选项 -->
        <template v-if="type === 'select'">
          <option
            v-for="option in options"
            :key="getOptionValue(option)"
            :value="getOptionValue(option)"
          >
            {{ getOptionLabel(option) }}
          </option>
        </template>
      </component>

      <!-- 后缀图标 -->
      <div v-if="suffixIcon || showPasswordToggle" class="field-suffix">
        <!-- 密码显示切换 -->
        <button
          v-if="showPasswordToggle"
          type="button"
          @click="togglePasswordVisibility"
          class="password-toggle"
        >
          <EyeIcon v-if="passwordVisible" class="field-icon" />
          <EyeSlashIcon v-else class="field-icon" />
        </button>
        
        <!-- 后缀图标 -->
        <component
          v-if="suffixIcon"
          :is="suffixIcon"
          class="field-icon"
        />
      </div>

      <!-- 清除按钮 -->
      <button
        v-if="clearable && modelValue && !disabled && !readonly"
        type="button"
        @click="clearValue"
        class="clear-button"
      >
        <XMarkIcon class="field-icon" />
      </button>
    </div>

    <!-- 帮助文本 -->
    <div v-if="helpText || errorMessage" class="field-help">
      <p v-if="errorMessage" class="error-message">
        <ExclamationCircleIcon class="error-icon" />
        {{ errorMessage }}
      </p>
      <p v-else-if="helpText" class="help-text">
        {{ helpText }}
      </p>
    </div>

    <!-- 字符计数 -->
    <div v-if="showCharCount && maxLength" class="char-count">
      {{ characterCount }} / {{ maxLength }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type Component } from 'vue'
import {
  EyeIcon,
  EyeSlashIcon,
  XMarkIcon,
  ExclamationCircleIcon,
} from '@heroicons/vue/24/outline'

// 定义选项类型
interface Option {
  label: string
  value: any
  disabled?: boolean
}

// 定义属性
interface Props {
  modelValue?: any
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'textarea' | 'select'
  label?: string
  placeholder?: string
  helpText?: string
  errorMessage?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'filled' | 'outlined'
  prefixIcon?: Component | string
  suffixIcon?: Component | string
  maxLength?: number
  showCharCount?: boolean
  options?: Option[] | string[]
  optionLabel?: string
  optionValue?: string
  rows?: number
  cols?: number
  autoResize?: boolean
  validateOnBlur?: boolean
  validateOnInput?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
  variant: 'default',
  rows: 3,
  validateOnBlur: true,
  validateOnInput: false,
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: any]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
  input: [event: Event]
  change: [event: Event]
  clear: []
}>()

// 响应式数据
const fieldId = ref(`field-${Math.random().toString(36).substr(2, 9)}`)
const isFocused = ref(false)
const passwordVisible = ref(false)

// 计算属性
const inputComponent = computed(() => {
  switch (props.type) {
    case 'textarea':
      return 'textarea'
    case 'select':
      return 'select'
    default:
      return 'input'
  }
})

const inputType = computed(() => {
  if (props.type === 'password') {
    return passwordVisible.value ? 'text' : 'password'
  }
  return props.type
})

const showPasswordToggle = computed(() => {
  return props.type === 'password' && !props.disabled && !props.readonly
})

const characterCount = computed(() => {
  return String(props.modelValue || '').length
})

const fieldClasses = computed(() => {
  const classes = [
    'form-field',
    `field-${props.size}`,
    `field-${props.variant}`,
  ]

  if (props.disabled) {
    classes.push('field-disabled')
  }

  if (props.readonly) {
    classes.push('field-readonly')
  }

  if (props.errorMessage) {
    classes.push('field-error')
  }

  if (isFocused.value) {
    classes.push('field-focused')
  }

  return classes
})

const labelClasses = computed(() => {
  const classes = ['field-label']

  if (props.required) {
    classes.push('field-required')
  }

  return classes
})

const inputClasses = computed(() => {
  const classes = ['field-input']

  if (props.prefixIcon) {
    classes.push('has-prefix')
  }

  if (props.suffixIcon || showPasswordToggle.value || props.clearable) {
    classes.push('has-suffix')
  }

  return classes
})

const inputProps = computed(() => {
  const baseProps: any = {}

  if (props.type !== 'textarea' && props.type !== 'select') {
    baseProps.type = inputType.value
  }

  if (props.maxLength) {
    baseProps.maxlength = props.maxLength
  }

  if (props.type === 'textarea') {
    baseProps.rows = props.rows
    if (props.cols) {
      baseProps.cols = props.cols
    }
  }

  return baseProps
})

// 方法
const getOptionValue = (option: Option | string): any => {
  if (typeof option === 'string') {
    return option
  }
  return props.optionValue ? option[props.optionValue] : option.value
}

const getOptionLabel = (option: Option | string): string => {
  if (typeof option === 'string') {
    return option
  }
  return props.optionLabel ? option[props.optionLabel] : option.label
}

const togglePasswordVisibility = (): void => {
  passwordVisible.value = !passwordVisible.value
}

const clearValue = (): void => {
  emit('update:modelValue', '')
  emit('clear')
}

const handleFocus = (event: FocusEvent): void => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event: FocusEvent): void => {
  isFocused.value = false
  emit('blur', event)
}

const handleInput = (event: Event): void => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  emit('update:modelValue', target.value)
  emit('input', event)
}

const handleChange = (event: Event): void => {
  emit('change', event)
}

// 暴露组件方法
defineExpose({
  focus: () => {
    const input = document.getElementById(fieldId.value)
    input?.focus()
  },
  blur: () => {
    const input = document.getElementById(fieldId.value)
    input?.blur()
  },
  clear: clearValue,
})
</script>

<style scoped>
/* 基础字段样式 */
.form-field {
  @apply space-y-2;
}

/* 标签样式 */
.field-label {
  @apply block text-sm font-medium text-gray-700;
}

.theme-dark .field-label {
  @apply text-gray-300;
}

.required-mark {
  @apply text-red-500 ml-1;
}

/* 输入容器样式 */
.field-input-container {
  @apply relative;
}

/* 输入控件基础样式 */
.field-input {
  @apply block w-full border border-gray-300 rounded-md shadow-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply transition-colors duration-200;
}

.theme-dark .field-input {
  @apply bg-gray-700 border-gray-600 text-gray-100;
  @apply focus:ring-blue-400 focus:border-blue-400;
}

/* 输入控件尺寸 */
.field-sm .field-input {
  @apply px-3 py-1.5 text-sm;
}

.field-md .field-input {
  @apply px-3 py-2 text-base;
}

.field-lg .field-input {
  @apply px-4 py-3 text-lg;
}

/* 输入控件变体 */
.field-filled .field-input {
  @apply bg-gray-50 border-transparent;
}

.theme-dark .field-filled .field-input {
  @apply bg-gray-800 border-transparent;
}

.field-outlined .field-input {
  @apply bg-transparent border-2;
}

/* 前缀和后缀样式 */
.field-prefix,
.field-suffix {
  @apply absolute top-1/2 transform -translate-y-1/2 flex items-center;
}

.field-prefix {
  @apply left-3;
}

.field-suffix {
  @apply right-3;
}

.field-input.has-prefix {
  @apply pl-10;
}

.field-input.has-suffix {
  @apply pr-10;
}

.field-icon {
  @apply w-5 h-5 text-gray-400;
}

.theme-dark .field-icon {
  @apply text-gray-500;
}

/* 密码切换按钮 */
.password-toggle {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded;
  @apply transition-colors duration-200;
}

.theme-dark .password-toggle {
  @apply text-gray-500 hover:text-gray-300;
}

/* 清除按钮 */
.clear-button {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded;
  @apply transition-colors duration-200;
}

.theme-dark .clear-button {
  @apply text-gray-500 hover:text-gray-300;
}

/* 状态样式 */
.field-disabled .field-input {
  @apply bg-gray-100 text-gray-500 cursor-not-allowed;
}

.theme-dark .field-disabled .field-input {
  @apply bg-gray-800 text-gray-500;
}

.field-readonly .field-input {
  @apply bg-gray-50 cursor-default;
}

.theme-dark .field-readonly .field-input {
  @apply bg-gray-800;
}

.field-error .field-input {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

.field-focused .field-input {
  @apply ring-2 ring-blue-500 border-blue-500;
}

.theme-dark .field-focused .field-input {
  @apply ring-blue-400 border-blue-400;
}

/* 帮助文本样式 */
.field-help {
  @apply mt-1;
}

.help-text {
  @apply text-sm text-gray-500;
}

.theme-dark .help-text {
  @apply text-gray-400;
}

.error-message {
  @apply flex items-center text-sm text-red-600;
}

.theme-dark .error-message {
  @apply text-red-400;
}

.error-icon {
  @apply w-4 h-4 mr-1 flex-shrink-0;
}

/* 字符计数样式 */
.char-count {
  @apply text-xs text-gray-500 text-right;
}

.theme-dark .char-count {
  @apply text-gray-400;
}

/* 文本域样式 */
textarea.field-input {
  @apply resize-y min-h-[80px];
}

/* 选择器样式 */
select.field-input {
  @apply cursor-pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* 响应式适配 */
@media (max-width: 640px) {
  .field-lg .field-input {
    @apply px-3 py-2 text-base;
  }
  
  .field-input.has-prefix {
    @apply pl-8;
  }
  
  .field-input.has-suffix {
    @apply pr-8;
  }
  
  .field-prefix {
    @apply left-2;
  }
  
  .field-suffix {
    @apply right-2;
  }
}
</style>
