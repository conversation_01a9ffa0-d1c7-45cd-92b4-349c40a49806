<template>
  <div class="data-table-container">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="data-table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <h3 v-if="title" class="table-title">{{ title }}</h3>
        </slot>
      </div>
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <!-- 搜索框 -->
          <div v-if="searchable" class="search-box">
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="searchPlaceholder"
              class="search-input"
            />
            <SearchIcon class="search-icon" />
          </div>
          
          <!-- 刷新按钮 -->
          <button
            v-if="refreshable"
            @click="handleRefresh"
            class="refresh-btn"
            :disabled="loading"
          >
            <RefreshIcon :class="{ 'animate-spin': loading }" />
          </button>
        </slot>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-wrapper" :class="{ 'table-loading': loading }">
      <table class="data-table">
        <!-- 表头 -->
        <thead class="table-header">
          <tr>
            <!-- 选择列 -->
            <th v-if="selectable" class="select-column">
              <input
                type="checkbox"
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="toggleSelectAll"
                class="select-checkbox"
              />
            </th>
            
            <!-- 数据列 -->
            <th
              v-for="column in columns"
              :key="column.key"
              :class="getColumnClasses(column)"
              @click="handleSort(column)"
            >
              <div class="column-header">
                <span class="column-title">{{ column.title }}</span>
                <div v-if="column.sortable" class="sort-icons">
                  <ChevronUpIcon
                    :class="{
                      'text-blue-600': sortBy === column.key && sortOrder === 'asc',
                      'text-gray-400': sortBy !== column.key || sortOrder !== 'asc'
                    }"
                  />
                  <ChevronDownIcon
                    :class="{
                      'text-blue-600': sortBy === column.key && sortOrder === 'desc',
                      'text-gray-400': sortBy !== column.key || sortOrder !== 'desc'
                    }"
                  />
                </div>
              </div>
            </th>
            
            <!-- 操作列 -->
            <th v-if="$slots.actions" class="actions-column">
              操作
            </th>
          </tr>
        </thead>

        <!-- 表体 -->
        <tbody class="table-body">
          <!-- 数据行 -->
          <tr
            v-for="(item, index) in paginatedData"
            :key="getRowKey(item, index)"
            :class="getRowClasses(item, index)"
            @click="handleRowClick(item, index)"
          >
            <!-- 选择列 -->
            <td v-if="selectable" class="select-column">
              <input
                type="checkbox"
                :checked="isRowSelected(item)"
                @change="toggleRowSelect(item)"
                @click.stop
                class="select-checkbox"
              />
            </td>
            
            <!-- 数据列 -->
            <td
              v-for="column in columns"
              :key="column.key"
              :class="getCellClasses(column)"
            >
              <slot
                :name="`cell-${column.key}`"
                :item="item"
                :value="getColumnValue(item, column.key)"
                :index="index"
              >
                {{ formatCellValue(item, column) }}
              </slot>
            </td>
            
            <!-- 操作列 -->
            <td v-if="$slots.actions" class="actions-column">
              <div class="actions-wrapper">
                <slot name="actions" :item="item" :index="index" />
              </div>
            </td>
          </tr>

          <!-- 空数据提示 -->
          <tr v-if="paginatedData.length === 0 && !loading" class="empty-row">
            <td :colspan="totalColumns" class="empty-cell">
              <div class="empty-content">
                <slot name="empty">
                  <div class="empty-icon">📭</div>
                  <p class="empty-text">{{ emptyText }}</p>
                </slot>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 加载遮罩 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner">
          <svg class="animate-spin" viewBox="0 0 24 24">
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="2"
              fill="none"
              stroke-dasharray="32"
              stroke-dashoffset="32"
            />
          </svg>
        </div>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>

    <!-- 分页器 -->
    <div v-if="paginated && filteredData.length > 0" class="pagination-wrapper">
      <div class="pagination-info">
        显示 {{ paginationStart }} - {{ paginationEnd }} 条，共 {{ filteredData.length }} 条
      </div>
      <div class="pagination-controls">
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          上一页
        </button>
        
        <div class="page-numbers">
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="goToPage(page)"
            :class="['page-btn', { 'page-btn-active': page === currentPage }]"
          >
            {{ page }}
          </button>
        </div>
        
        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { SearchIcon, RefreshIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/vue/24/outline'

// 定义列接口
interface Column {
  key: string
  title: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  formatter?: (value: any, item: any) => string
}

// 定义属性
interface Props {
  data: any[]
  columns: Column[]
  title?: string
  loading?: boolean
  loadingText?: string
  emptyText?: string
  selectable?: boolean
  searchable?: boolean
  searchPlaceholder?: string
  refreshable?: boolean
  paginated?: boolean
  pageSize?: number
  showToolbar?: boolean
  rowKey?: string | ((item: any) => string)
  clickableRows?: boolean
  striped?: boolean
  bordered?: boolean
}

// 暴露组件实例方法
defineExpose({
  getSelectedItems: () => selectedItems.value,
  clearSelection: () => {
    selectedItems.value = []
    emit('selectionChange', selectedItems.value)
  },
  refresh: handleRefresh,
  goToPage,
  getCurrentPage: () => currentPage.value,
  getTotalPages: () => totalPages.value,
})

const props = withDefaults(defineProps<Props>(), {
  loadingText: '加载中...',
  emptyText: '暂无数据',
  searchPlaceholder: '搜索...',
  pageSize: 10,
  showToolbar: true,
  rowKey: 'id',
  clickableRows: false,
  striped: true,
  bordered: true,
})

// 定义事件
const emit = defineEmits<{
  refresh: []
  rowClick: [item: any, index: number]
  selectionChange: [selectedItems: any[]]
  sortChange: [sortBy: string, sortOrder: 'asc' | 'desc']
}>()

// 响应式数据
const searchQuery = ref('')
const sortBy = ref('')
const sortOrder = ref<'asc' | 'desc'>('asc')
const currentPage = ref(1)
const selectedItems = ref<any[]>([])

// 计算属性
const filteredData = computed(() => {
  let result = [...props.data]
  
  // 搜索过滤
  if (searchQuery.value && props.searchable) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item => {
      return props.columns.some(column => {
        const value = getColumnValue(item, column.key)
        return String(value).toLowerCase().includes(query)
      })
    })
  }
  
  // 排序
  if (sortBy.value) {
    result.sort((a, b) => {
      const aValue = getColumnValue(a, sortBy.value)
      const bValue = getColumnValue(b, sortBy.value)
      
      let comparison = 0
      if (aValue < bValue) comparison = -1
      if (aValue > bValue) comparison = 1
      
      return sortOrder.value === 'desc' ? -comparison : comparison
    })
  }
  
  return result
})

const totalPages = computed(() => {
  if (!props.paginated) return 1
  return Math.ceil(filteredData.value.length / props.pageSize)
})

const paginatedData = computed(() => {
  if (!props.paginated) return filteredData.value
  
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredData.value.slice(start, end)
})

const totalColumns = computed(() => {
  let count = props.columns.length
  if (props.selectable) count++
  if (props.$slots?.actions) count++
  return count
})

const isAllSelected = computed(() => {
  return paginatedData.value.length > 0 && 
         paginatedData.value.every(item => isRowSelected(item))
})

const isIndeterminate = computed(() => {
  const selectedCount = paginatedData.value.filter(item => isRowSelected(item)).length
  return selectedCount > 0 && selectedCount < paginatedData.value.length
})

const paginationStart = computed(() => {
  if (filteredData.value.length === 0) return 0
  return (currentPage.value - 1) * props.pageSize + 1
})

const paginationEnd = computed(() => {
  const end = currentPage.value * props.pageSize
  return Math.min(end, filteredData.value.length)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)
  
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const getRowKey = (item: any, index: number): string => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(item)
  }
  return item[props.rowKey] || index.toString()
}

const getColumnValue = (item: any, key: string): any => {
  return key.split('.').reduce((obj, k) => obj?.[k], item)
}

const formatCellValue = (item: any, column: Column): string => {
  const value = getColumnValue(item, column.key)
  if (column.formatter) {
    return column.formatter(value, item)
  }
  return value?.toString() || ''
}

const getColumnClasses = (column: Column): string[] => {
  const classes = ['table-column']
  
  if (column.sortable) {
    classes.push('sortable-column')
  }
  
  if (column.align) {
    classes.push(`text-${column.align}`)
  }
  
  return classes
}

const getCellClasses = (column: Column): string[] => {
  const classes = ['table-cell']
  
  if (column.align) {
    classes.push(`text-${column.align}`)
  }
  
  return classes
}

const getRowClasses = (item: any, index: number): string[] => {
  const classes = ['table-row']
  
  if (props.striped && index % 2 === 1) {
    classes.push('striped-row')
  }
  
  if (props.clickableRows) {
    classes.push('clickable-row')
  }
  
  if (isRowSelected(item)) {
    classes.push('selected-row')
  }
  
  return classes
}

const isRowSelected = (item: any): boolean => {
  const key = getRowKey(item, 0)
  return selectedItems.value.some(selected => getRowKey(selected, 0) === key)
}

const toggleRowSelect = (item: any): void => {
  const key = getRowKey(item, 0)
  const index = selectedItems.value.findIndex(selected => getRowKey(selected, 0) === key)
  
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(item)
  }
  
  emit('selectionChange', selectedItems.value)
}

const toggleSelectAll = (): void => {
  if (isAllSelected.value) {
    // 取消选择当前页所有项
    paginatedData.value.forEach(item => {
      const key = getRowKey(item, 0)
      const index = selectedItems.value.findIndex(selected => getRowKey(selected, 0) === key)
      if (index > -1) {
        selectedItems.value.splice(index, 1)
      }
    })
  } else {
    // 选择当前页所有项
    paginatedData.value.forEach(item => {
      if (!isRowSelected(item)) {
        selectedItems.value.push(item)
      }
    })
  }
  
  emit('selectionChange', selectedItems.value)
}

const handleSort = (column: Column): void => {
  if (!column.sortable) return
  
  if (sortBy.value === column.key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = column.key
    sortOrder.value = 'asc'
  }
  
  emit('sortChange', sortBy.value, sortOrder.value)
}

const handleRowClick = (item: any, index: number): void => {
  if (props.clickableRows) {
    emit('rowClick', item, index)
  }
}

const handleRefresh = (): void => {
  emit('refresh')
}

const goToPage = (page: number): void => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// 监听搜索查询变化，重置到第一页
watch(searchQuery, () => {
  currentPage.value = 1
})

// 监听数据变化，重置选择
watch(() => props.data, () => {
  selectedItems.value = []
  emit('selectionChange', selectedItems.value)
})
</script>

<style scoped>
/* 表格容器样式 */
.data-table-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.theme-dark .data-table-container {
  @apply bg-gray-800 border-gray-700;
}

/* 工具栏样式 */
.data-table-toolbar {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.theme-dark .data-table-toolbar {
  @apply border-gray-700;
}

.toolbar-left {
  @apply flex items-center gap-4;
}

.toolbar-right {
  @apply flex items-center gap-3;
}

.table-title {
  @apply text-lg font-semibold text-gray-900;
}

.theme-dark .table-title {
  @apply text-gray-100;
}

/* 搜索框样式 */
.search-box {
  @apply relative;
}

.search-input {
  @apply pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.theme-dark .search-input {
  @apply bg-gray-700 border-gray-600 text-gray-100;
  @apply focus:ring-blue-400 focus:border-blue-400;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400;
}

/* 刷新按钮样式 */
.refresh-btn {
  @apply p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md;
  @apply transition-colors duration-200;
}

.theme-dark .refresh-btn {
  @apply text-gray-400 hover:text-gray-200 hover:bg-gray-700;
}

/* 表格样式 */
.table-wrapper {
  @apply relative overflow-x-auto;
}

.data-table {
  @apply w-full;
}

.table-header {
  @apply bg-gray-50;
}

.theme-dark .table-header {
  @apply bg-gray-900;
}

.table-header th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.theme-dark .table-header th {
  @apply text-gray-400;
}

.sortable-column {
  @apply cursor-pointer hover:bg-gray-100;
}

.theme-dark .sortable-column {
  @apply hover:bg-gray-800;
}

.column-header {
  @apply flex items-center justify-between;
}

.sort-icons {
  @apply flex flex-col ml-2;
}

.sort-icons svg {
  @apply w-3 h-3;
}

/* 表格行样式 */
.table-row {
  @apply border-b border-gray-200;
}

.theme-dark .table-row {
  @apply border-gray-700;
}

.striped-row {
  @apply bg-gray-50;
}

.theme-dark .striped-row {
  @apply bg-gray-900;
}

.clickable-row {
  @apply cursor-pointer hover:bg-gray-50;
}

.theme-dark .clickable-row {
  @apply hover:bg-gray-900;
}

.selected-row {
  @apply bg-blue-50;
}

.theme-dark .selected-row {
  @apply bg-blue-900 bg-opacity-20;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.theme-dark .table-cell {
  @apply text-gray-100;
}

/* 选择列样式 */
.select-column {
  @apply w-12;
}

.select-checkbox {
  @apply rounded border-gray-300 text-blue-600 focus:ring-blue-500;
}

/* 操作列样式 */
.actions-column {
  @apply w-32;
}

.actions-wrapper {
  @apply flex items-center gap-2;
}

/* 空数据样式 */
.empty-row {
  @apply border-b-0;
}

.empty-cell {
  @apply px-6 py-12 text-center;
}

.empty-content {
  @apply flex flex-col items-center;
}

.empty-icon {
  @apply text-4xl mb-2;
}

.empty-text {
  @apply text-gray-500 text-sm;
}

.theme-dark .empty-text {
  @apply text-gray-400;
}

/* 加载样式 */
.table-loading {
  @apply pointer-events-none;
}

.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 flex flex-col items-center justify-center;
  @apply backdrop-blur-sm;
}

.theme-dark .loading-overlay {
  @apply bg-gray-800 bg-opacity-75;
}

.loading-spinner {
  @apply w-8 h-8 text-blue-600;
}

.loading-text {
  @apply mt-2 text-sm text-gray-600;
}

.theme-dark .loading-text {
  @apply text-gray-400;
}

/* 分页样式 */
.pagination-wrapper {
  @apply flex items-center justify-between px-6 py-4 border-t border-gray-200;
}

.theme-dark .pagination-wrapper {
  @apply border-gray-700;
}

.pagination-info {
  @apply text-sm text-gray-700;
}

.theme-dark .pagination-info {
  @apply text-gray-300;
}

.pagination-controls {
  @apply flex items-center gap-2;
}

.pagination-btn {
  @apply px-3 py-1 text-sm border border-gray-300 rounded-md;
  @apply hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;
}

.theme-dark .pagination-btn {
  @apply border-gray-600 hover:bg-gray-700;
}

.page-numbers {
  @apply flex gap-1;
}

.page-btn {
  @apply w-8 h-8 text-sm border border-gray-300 rounded-md;
  @apply hover:bg-gray-50;
}

.page-btn-active {
  @apply bg-blue-600 text-white border-blue-600;
}

.theme-dark .page-btn {
  @apply border-gray-600 hover:bg-gray-700;
}

/* 动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式适配 */
@media (max-width: 640px) {
  .data-table-toolbar {
    @apply flex-col items-stretch gap-3;
  }
  
  .toolbar-left,
  .toolbar-right {
    @apply justify-between;
  }
  
  .table-cell {
    @apply px-3 py-2;
  }
  
  .pagination-wrapper {
    @apply flex-col gap-3;
  }
}
</style>
