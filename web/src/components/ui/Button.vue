<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="type"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="btn-loading">
      <svg class="animate-spin" viewBox="0 0 24 24">
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
          fill="none"
          stroke-dasharray="32"
          stroke-dashoffset="32"
        />
      </svg>
    </div>

    <!-- 图标 -->
    <component
      v-if="icon && !loading"
      :is="icon"
      :class="iconClasses"
    />

    <!-- 文本内容 -->
    <span v-if="$slots.default" :class="textClasses">
      <slot />
    </span>

    <!-- 右侧图标 -->
    <component
      v-if="iconRight && !loading"
      :is="iconRight"
      :class="iconRightClasses"
    />
  </button>
</template>

<script setup lang="ts">
import { computed, type Component } from 'vue'

// 定义属性
interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'link'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  block?: boolean
  rounded?: boolean
  icon?: Component | string
  iconRight?: Component | string
  type?: 'button' | 'submit' | 'reset'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  block: false,
  rounded: false,
  type: 'button',
})

// 定义事件
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 计算按钮样式类
const buttonClasses = computed(() => {
  const classes = [
    'btn',
    `btn-${props.variant}`,
    `btn-${props.size}`,
  ]

  if (props.disabled || props.loading) {
    classes.push('btn-disabled')
  }

  if (props.block) {
    classes.push('btn-block')
  }

  if (props.rounded) {
    classes.push('btn-rounded')
  }

  if (props.loading) {
    classes.push('btn-loading-state')
  }

  return classes
})

// 计算图标样式类
const iconClasses = computed(() => {
  const classes = ['btn-icon']
  
  if (props.size === 'xs') {
    classes.push('w-3 h-3')
  } else if (props.size === 'sm') {
    classes.push('w-4 h-4')
  } else if (props.size === 'md') {
    classes.push('w-5 h-5')
  } else if (props.size === 'lg') {
    classes.push('w-6 h-6')
  } else if (props.size === 'xl') {
    classes.push('w-7 h-7')
  }

  return classes
})

// 计算右侧图标样式类
const iconRightClasses = computed(() => {
  return [...iconClasses.value, 'btn-icon-right']
})

// 计算文本样式类
const textClasses = computed(() => {
  const classes = ['btn-text']
  
  if (props.loading) {
    classes.push('opacity-0')
  }

  return classes
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
/* 基础按钮样式 */
.btn {
  @apply relative inline-flex items-center justify-center font-medium transition-all duration-200 ease-in-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply border border-transparent;
  @apply cursor-pointer select-none;
}

/* 按钮尺寸 */
.btn-xs {
  @apply px-2 py-1 text-xs rounded;
  @apply gap-1;
}

.btn-sm {
  @apply px-3 py-1.5 text-sm rounded-md;
  @apply gap-1.5;
}

.btn-md {
  @apply px-4 py-2 text-base rounded-md;
  @apply gap-2;
}

.btn-lg {
  @apply px-6 py-3 text-lg rounded-lg;
  @apply gap-2.5;
}

.btn-xl {
  @apply px-8 py-4 text-xl rounded-lg;
  @apply gap-3;
}

/* 按钮变体 */
.btn-primary {
  @apply bg-blue-600 text-white border-blue-600;
  @apply hover:bg-blue-700 hover:border-blue-700;
  @apply focus:ring-blue-500;
  @apply active:bg-blue-800;
}

.btn-secondary {
  @apply bg-gray-600 text-white border-gray-600;
  @apply hover:bg-gray-700 hover:border-gray-700;
  @apply focus:ring-gray-500;
  @apply active:bg-gray-800;
}

.btn-success {
  @apply bg-green-600 text-white border-green-600;
  @apply hover:bg-green-700 hover:border-green-700;
  @apply focus:ring-green-500;
  @apply active:bg-green-800;
}

.btn-warning {
  @apply bg-yellow-600 text-white border-yellow-600;
  @apply hover:bg-yellow-700 hover:border-yellow-700;
  @apply focus:ring-yellow-500;
  @apply active:bg-yellow-800;
}

.btn-error {
  @apply bg-red-600 text-white border-red-600;
  @apply hover:bg-red-700 hover:border-red-700;
  @apply focus:ring-red-500;
  @apply active:bg-red-800;
}

.btn-ghost {
  @apply bg-transparent text-gray-700 border-gray-300;
  @apply hover:bg-gray-50 hover:text-gray-900;
  @apply focus:ring-gray-500;
  @apply active:bg-gray-100;
}

.btn-link {
  @apply bg-transparent text-blue-600 border-transparent;
  @apply hover:text-blue-800 hover:underline;
  @apply focus:ring-blue-500;
  @apply active:text-blue-900;
  @apply p-0;
}

/* 暗色主题适配 */
.theme-dark .btn-ghost {
  @apply text-gray-300 border-gray-600;
  @apply hover:bg-gray-800 hover:text-gray-100;
}

.theme-dark .btn-link {
  @apply text-blue-400;
  @apply hover:text-blue-300;
}

/* 禁用状态 */
.btn-disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply pointer-events-none;
}

/* 块级按钮 */
.btn-block {
  @apply w-full;
}

/* 圆形按钮 */
.btn-rounded {
  @apply rounded-full;
}

/* 加载状态 */
.btn-loading-state {
  @apply cursor-wait;
}

.btn-loading {
  @apply absolute inset-0 flex items-center justify-center;
}

.btn-loading svg {
  @apply w-5 h-5;
}

/* 图标样式 */
.btn-icon {
  @apply flex-shrink-0;
}

.btn-icon-right {
  @apply order-last;
}

.btn-text {
  @apply transition-opacity duration-200;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式适配 */
@media (max-width: 640px) {
  .btn-lg {
    @apply px-4 py-2 text-base;
  }
  
  .btn-xl {
    @apply px-6 py-3 text-lg;
  }
}
</style>
