<template>
  <div :class="cardClasses">
    <!-- 卡片头部 -->
    <div v-if="$slots.header || title || subtitle" class="card-header">
      <slot name="header">
        <div class="card-header-content">
          <div v-if="title || subtitle" class="card-header-text">
            <h3 v-if="title" class="card-title">{{ title }}</h3>
            <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
          </div>
          <div v-if="$slots.actions" class="card-header-actions">
            <slot name="actions" />
          </div>
        </div>
      </slot>
    </div>

    <!-- 卡片内容 -->
    <div v-if="$slots.default" class="card-content">
      <slot />
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer" />
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loading" class="card-loading">
      <div class="card-loading-spinner">
        <svg class="animate-spin" viewBox="0 0 24 24">
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-dasharray="32"
            stroke-dashoffset="32"
          />
        </svg>
      </div>
      <p v-if="loadingText" class="card-loading-text">{{ loadingText }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义属性
interface Props {
  title?: string
  subtitle?: string
  variant?: 'default' | 'outlined' | 'elevated' | 'filled'
  size?: 'sm' | 'md' | 'lg'
  rounded?: boolean
  hoverable?: boolean
  clickable?: boolean
  loading?: boolean
  loadingText?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  rounded: false,
  hoverable: false,
  clickable: false,
  loading: false,
  disabled: false,
})

// 定义事件
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 计算卡片样式类
const cardClasses = computed(() => {
  const classes = [
    'card',
    `card-${props.variant}`,
    `card-${props.size}`,
  ]

  if (props.rounded) {
    classes.push('card-rounded')
  }

  if (props.hoverable) {
    classes.push('card-hoverable')
  }

  if (props.clickable) {
    classes.push('card-clickable')
  }

  if (props.disabled) {
    classes.push('card-disabled')
  }

  if (props.loading) {
    classes.push('card-loading-state')
  }

  return classes
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (props.clickable && !props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
/* 基础卡片样式 */
.card {
  @apply relative bg-white border border-gray-200 transition-all duration-200 ease-in-out;
  @apply overflow-hidden;
}

/* 卡片变体 */
.card-default {
  @apply bg-white border-gray-200;
}

.card-outlined {
  @apply bg-transparent border-2 border-gray-300;
}

.card-elevated {
  @apply bg-white border-gray-200 shadow-lg;
}

.card-filled {
  @apply bg-gray-50 border-gray-200;
}

/* 暗色主题适配 */
.theme-dark .card-default {
  @apply bg-gray-800 border-gray-700;
}

.theme-dark .card-outlined {
  @apply bg-transparent border-gray-600;
}

.theme-dark .card-elevated {
  @apply bg-gray-800 border-gray-700 shadow-xl;
}

.theme-dark .card-filled {
  @apply bg-gray-900 border-gray-700;
}

/* 卡片尺寸 */
.card-sm {
  @apply rounded-md;
}

.card-md {
  @apply rounded-lg;
}

.card-lg {
  @apply rounded-xl;
}

/* 圆角卡片 */
.card-rounded {
  @apply rounded-2xl;
}

/* 可悬停卡片 */
.card-hoverable {
  @apply hover:shadow-md hover:-translate-y-1;
}

.theme-dark .card-hoverable {
  @apply hover:shadow-xl;
}

/* 可点击卡片 */
.card-clickable {
  @apply cursor-pointer;
  @apply hover:shadow-md hover:-translate-y-1;
  @apply active:translate-y-0 active:shadow-sm;
}

.theme-dark .card-clickable {
  @apply hover:shadow-xl;
}

/* 禁用状态 */
.card-disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* 卡片头部 */
.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.theme-dark .card-header {
  @apply border-gray-700;
}

.card-header-content {
  @apply flex items-center justify-between;
}

.card-header-text {
  @apply flex-1 min-w-0;
}

.card-title {
  @apply text-lg font-semibold text-gray-900 truncate;
}

.theme-dark .card-title {
  @apply text-gray-100;
}

.card-subtitle {
  @apply mt-1 text-sm text-gray-500 truncate;
}

.theme-dark .card-subtitle {
  @apply text-gray-400;
}

.card-header-actions {
  @apply flex items-center gap-2 ml-4;
}

/* 卡片内容 */
.card-content {
  @apply px-6 py-4;
}

.card-sm .card-content {
  @apply px-4 py-3;
}

.card-lg .card-content {
  @apply px-8 py-6;
}

/* 卡片底部 */
.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

.theme-dark .card-footer {
  @apply border-gray-700 bg-gray-900;
}

.card-sm .card-footer {
  @apply px-4 py-3;
}

.card-lg .card-footer {
  @apply px-8 py-6;
}

/* 加载状态 */
.card-loading-state {
  @apply pointer-events-none;
}

.card-loading {
  @apply absolute inset-0 bg-white bg-opacity-75 flex flex-col items-center justify-center;
  @apply backdrop-blur-sm;
}

.theme-dark .card-loading {
  @apply bg-gray-800 bg-opacity-75;
}

.card-loading-spinner {
  @apply w-8 h-8 text-blue-600;
}

.card-loading-spinner svg {
  @apply w-full h-full;
}

.card-loading-text {
  @apply mt-2 text-sm text-gray-600;
}

.theme-dark .card-loading-text {
  @apply text-gray-400;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式适配 */
@media (max-width: 640px) {
  .card-content {
    @apply px-4 py-3;
  }
  
  .card-header {
    @apply px-4 py-3;
  }
  
  .card-footer {
    @apply px-4 py-3;
  }
  
  .card-header-content {
    @apply flex-col items-start gap-2;
  }
  
  .card-header-actions {
    @apply ml-0 w-full justify-end;
  }
  
  .card-title {
    @apply text-base;
  }
}

/* 特殊效果 */
.card-hoverable:hover,
.card-clickable:hover {
  @apply transform transition-transform duration-200;
}

/* 焦点状态 */
.card-clickable:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.theme-dark .card-clickable:focus {
  @apply ring-offset-gray-800;
}
</style>
