<template>
  <div class="language-switcher" :class="{ 'switcher-mobile': isMobile }">
    <!-- 桌面端下拉菜单 -->
    <div v-if="!isMobile" class="desktop-switcher">
      <button
        ref="triggerRef"
        @click="toggleDropdown"
        class="language-trigger"
        :class="{ 'trigger-active': isOpen }"
      >
        <span class="current-language">
          <span class="language-flag">{{ currentLocaleInfo?.flag }}</span>
          <span v-if="showText" class="language-name">{{ currentLocaleInfo?.name }}</span>
        </span>
        <ChevronDownIcon
          class="dropdown-icon"
          :class="{ 'icon-rotated': isOpen }"
        />
      </button>

      <!-- 下拉菜单 -->
      <Transition name="dropdown">
        <div
          v-if="isOpen"
          ref="dropdownRef"
          class="language-dropdown"
          :class="dropdownClasses"
        >
          <div class="dropdown-content">
            <button
              v-for="locale in supportedLocales"
              :key="locale.code"
              @click="selectLanguage(locale.code)"
              class="language-option"
              :class="{ 'option-active': locale.code === currentLocale }"
            >
              <span class="option-flag">{{ locale.flag }}</span>
              <span class="option-name">{{ locale.name }}</span>
              <CheckIcon
                v-if="locale.code === currentLocale"
                class="option-check"
              />
            </button>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 移动端模态框 -->
    <div v-else class="mobile-switcher">
      <button
        @click="showMobileModal = true"
        class="mobile-trigger"
      >
        <span class="mobile-language">
          <span class="language-flag">{{ currentLocaleInfo?.flag }}</span>
          <span v-if="showText" class="language-name">{{ currentLocaleInfo?.name }}</span>
        </span>
        <ChevronDownIcon class="mobile-icon" />
      </button>

      <!-- 移动端模态框 -->
      <Teleport to="body">
        <Transition name="modal">
          <div
            v-if="showMobileModal"
            class="mobile-modal-overlay"
            @click="showMobileModal = false"
          >
            <div
              class="mobile-modal"
              @click.stop
            >
              <div class="modal-header">
                <h3 class="modal-title">{{ $t('common.selectLanguage') }}</h3>
                <button
                  @click="showMobileModal = false"
                  class="modal-close"
                >
                  <XMarkIcon class="close-icon" />
                </button>
              </div>

              <div class="modal-content">
                <button
                  v-for="locale in supportedLocales"
                  :key="locale.code"
                  @click="selectLanguage(locale.code, true)"
                  class="mobile-option"
                  :class="{ 'option-active': locale.code === currentLocale }"
                >
                  <span class="option-flag">{{ locale.flag }}</span>
                  <span class="option-name">{{ locale.name }}</span>
                  <CheckIcon
                    v-if="locale.code === currentLocale"
                    class="option-check"
                  />
                </button>
              </div>
            </div>
          </div>
        </Transition>
      </Teleport>
    </div>

    <!-- 点击外部关闭 -->
    <div
      v-if="isOpen && !isMobile"
      class="dropdown-backdrop"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  ChevronDownIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'
import { useResponsive } from '@/composables/useResponsive'
import {
  supportedLocales,
  getCurrentLocaleInfo,
  setLocale,
  type SupportedLocale,
} from '@/i18n'
import { useI18n } from 'vue-i18n'

// 定义属性
interface Props {
  showText?: boolean
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end'
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  showText: true,
  placement: 'bottom-start',
  size: 'md',
})

// 定义事件
const emit = defineEmits<{
  change: [locale: SupportedLocale]
}>()

// 响应式数据
const { isMobile } = useResponsive()
const { locale: currentLocale } = useI18n()
const isOpen = ref(false)
const showMobileModal = ref(false)
const triggerRef = ref<HTMLElement>()
const dropdownRef = ref<HTMLElement>()

// 计算属性
const currentLocaleInfo = computed(() => getCurrentLocaleInfo())

const dropdownClasses = computed(() => {
  const classes = ['dropdown-positioned']
  
  switch (props.placement) {
    case 'bottom-start':
      classes.push('dropdown-bottom-start')
      break
    case 'bottom-end':
      classes.push('dropdown-bottom-end')
      break
    case 'top-start':
      classes.push('dropdown-top-start')
      break
    case 'top-end':
      classes.push('dropdown-top-end')
      break
  }
  
  return classes
})

// 方法
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

const selectLanguage = (locale: SupportedLocale, closeMobile = false) => {
  setLocale(locale)
  emit('change', locale)
  
  if (closeMobile) {
    showMobileModal.value = false
  } else {
    closeDropdown()
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeDropdown()
    showMobileModal.value = false
  }
}

// 点击外部关闭
const handleClickOutside = (event: MouseEvent) => {
  if (
    triggerRef.value &&
    dropdownRef.value &&
    !triggerRef.value.contains(event.target as Node) &&
    !dropdownRef.value.contains(event.target as Node)
  ) {
    closeDropdown()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 语言切换器基础样式 */
.language-switcher {
  @apply relative inline-block;
}

/* 桌面端触发器 */
.language-trigger {
  @apply flex items-center gap-2 px-3 py-2 rounded-md;
  @apply text-gray-700 hover:text-gray-900 hover:bg-gray-100;
  @apply transition-all duration-200 ease-in-out;
  @apply border border-transparent hover:border-gray-300;
}

.theme-dark .language-trigger {
  @apply text-gray-300 hover:text-gray-100 hover:bg-gray-800;
  @apply hover:border-gray-600;
}

.trigger-active {
  @apply bg-gray-100 border-gray-300;
}

.theme-dark .trigger-active {
  @apply bg-gray-800 border-gray-600;
}

.current-language {
  @apply flex items-center gap-2;
}

.language-flag {
  @apply text-lg;
}

.language-name {
  @apply text-sm font-medium;
}

.dropdown-icon {
  @apply w-4 h-4 transition-transform duration-200;
}

.icon-rotated {
  @apply transform rotate-180;
}

/* 下拉菜单 */
.language-dropdown {
  @apply absolute z-50 mt-1 min-w-48;
  @apply bg-white border border-gray-200 rounded-md shadow-lg;
  @apply py-1;
}

.theme-dark .language-dropdown {
  @apply bg-gray-800 border-gray-700;
}

.dropdown-positioned {
  @apply absolute;
}

.dropdown-bottom-start {
  @apply top-full left-0;
}

.dropdown-bottom-end {
  @apply top-full right-0;
}

.dropdown-top-start {
  @apply bottom-full left-0 mb-1;
}

.dropdown-top-end {
  @apply bottom-full right-0 mb-1;
}

.dropdown-content {
  @apply space-y-1;
}

/* 语言选项 */
.language-option {
  @apply flex items-center gap-3 w-full px-3 py-2;
  @apply text-gray-700 hover:text-gray-900 hover:bg-gray-100;
  @apply transition-colors duration-200;
  @apply text-left;
}

.theme-dark .language-option {
  @apply text-gray-300 hover:text-gray-100 hover:bg-gray-700;
}

.option-active {
  @apply bg-blue-50 text-blue-700;
}

.theme-dark .option-active {
  @apply bg-blue-900 bg-opacity-20 text-blue-400;
}

.option-flag {
  @apply text-lg;
}

.option-name {
  @apply flex-1 text-sm font-medium;
}

.option-check {
  @apply w-4 h-4 text-blue-600;
}

.theme-dark .option-check {
  @apply text-blue-400;
}

/* 移动端样式 */
.mobile-trigger {
  @apply flex items-center gap-2 px-3 py-2 rounded-md;
  @apply text-gray-700 hover:text-gray-900 hover:bg-gray-100;
  @apply transition-colors duration-200;
}

.theme-dark .mobile-trigger {
  @apply text-gray-300 hover:text-gray-100 hover:bg-gray-800;
}

.mobile-language {
  @apply flex items-center gap-2;
}

.mobile-icon {
  @apply w-4 h-4;
}

/* 移动端模态框 */
.mobile-modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-50;
  @apply flex items-end justify-center;
  @apply p-4;
}

.mobile-modal {
  @apply bg-white rounded-t-xl w-full max-w-md;
  @apply max-h-96 overflow-hidden;
}

.theme-dark .mobile-modal {
  @apply bg-gray-800;
}

.modal-header {
  @apply flex items-center justify-between px-4 py-3;
  @apply border-b border-gray-200;
}

.theme-dark .modal-header {
  @apply border-gray-700;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.theme-dark .modal-title {
  @apply text-gray-100;
}

.modal-close {
  @apply p-1 text-gray-500 hover:text-gray-700 rounded;
}

.theme-dark .modal-close {
  @apply text-gray-400 hover:text-gray-200;
}

.close-icon {
  @apply w-5 h-5;
}

.modal-content {
  @apply px-4 py-2 max-h-80 overflow-y-auto;
}

.mobile-option {
  @apply flex items-center gap-3 w-full px-3 py-3 rounded-md;
  @apply text-gray-700 hover:bg-gray-100;
  @apply transition-colors duration-200;
  @apply text-left;
}

.theme-dark .mobile-option {
  @apply text-gray-300 hover:bg-gray-700;
}

/* 下拉菜单背景 */
.dropdown-backdrop {
  @apply fixed inset-0 z-40;
}

/* 过渡动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  @apply transition-all duration-200 ease-out;
}

.dropdown-enter-from,
.dropdown-leave-to {
  @apply opacity-0 transform scale-95 -translate-y-1;
}

.dropdown-enter-to,
.dropdown-leave-from {
  @apply opacity-100 transform scale-100 translate-y-0;
}

.modal-enter-active,
.modal-leave-active {
  @apply transition-all duration-300 ease-out;
}

.modal-enter-from,
.modal-leave-to {
  @apply opacity-0;
}

.modal-enter-from .mobile-modal,
.modal-leave-to .mobile-modal {
  @apply transform translate-y-full;
}

.modal-enter-to,
.modal-leave-from {
  @apply opacity-100;
}

.modal-enter-to .mobile-modal,
.modal-leave-from .mobile-modal {
  @apply transform translate-y-0;
}

/* 焦点状态 */
.language-trigger:focus,
.mobile-trigger:focus,
.language-option:focus,
.mobile-option:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.theme-dark .language-trigger:focus,
.theme-dark .mobile-trigger:focus,
.theme-dark .language-option:focus,
.theme-dark .mobile-option:focus {
  @apply ring-offset-gray-800;
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar {
  @apply w-1;
}

.modal-content::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.modal-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.theme-dark .modal-content::-webkit-scrollbar-thumb {
  @apply bg-gray-600;
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .dropdown-enter-active,
  .dropdown-leave-active,
  .modal-enter-active,
  .modal-leave-active,
  .dropdown-icon,
  .language-trigger,
  .mobile-trigger,
  .language-option,
  .mobile-option {
    @apply transition-none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .language-dropdown {
    @apply border-2;
  }
  
  .mobile-modal {
    @apply border-2 border-gray-400;
  }
}
</style>
