<template>
  <div class="sidebar-menu">
    <el-menu
      :default-active="activeMenu"
      :collapse="collapsed"
      :unique-opened="true"
      router
      class="sidebar-menu-el"
    >
      <!-- 遍历菜单项 -->
      <template v-for="item in menuItems" :key="item.path">
        <!-- 有子菜单的项 -->
        <el-sub-menu 
          v-if="item.children && item.children.length > 0" 
          :index="item.path"
        >
          <template #title>
            <el-icon v-if="item.icon">
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.title }}</span>
          </template>
          
          <el-menu-item
            v-for="child in item.children"
            :key="child.path"
            :index="child.path"
          >
            <el-icon v-if="child.icon">
              <component :is="child.icon" />
            </el-icon>
            <span>{{ child.title }}</span>
          </el-menu-item>
        </el-sub-menu>
        
        <!-- 无子菜单的项 -->
        <el-menu-item v-else :index="item.path">
          <el-icon v-if="item.icon">
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.title }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import type { MenuItem } from '@/types'

// 组件属性
interface Props {
  collapsed: boolean
}

defineProps<Props>()

// 响应式数据
const route = useRoute()
const userStore = useUserStore()

// 当前激活的菜单项
const activeMenu = computed(() => {
  const { path } = route
  // 处理子路由的情况，确保父菜单也被激活
  if (path.includes('/apps/')) return '/apps'
  if (path.includes('/cicd/')) return '/cicd'
  if (path.includes('/config/')) return '/config'
  if (path.includes('/monitor/')) return '/monitor'
  if (path.includes('/users/')) return '/users'
  return path
})

// 菜单配置
const allMenuItems: MenuItem[] = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    title: '控制台概览',
    icon: 'Dashboard'
  },
  {
    path: '/apps',
    name: 'Apps',
    title: '应用管理',
    icon: 'Box',
    children: [
      {
        path: '/apps',
        name: 'AppList',
        title: '应用列表',
        icon: 'List'
      },
      {
        path: '/apps/create',
        name: 'AppCreate',
        title: '创建应用',
        icon: 'Plus'
      }
    ]
  },
  {
    path: '/cicd',
    name: 'CICD',
    title: 'CI/CD 管理',
    icon: 'Connection',
    children: [
      {
        path: '/cicd/pipelines',
        name: 'Pipelines',
        title: '流水线管理',
        icon: 'Share'
      },
      {
        path: '/cicd/builds',
        name: 'Builds',
        title: '构建历史',
        icon: 'Tools'
      },
      {
        path: '/cicd/deployments',
        name: 'Deployments',
        title: '部署管理',
        icon: 'Upload'
      }
    ]
  },
  {
    path: '/config',
    name: 'Config',
    title: '配置管理',
    icon: 'Setting',
    children: [
      {
        path: '/config',
        name: 'ConfigList',
        title: '配置列表',
        icon: 'List'
      },
      {
        path: '/config/secrets',
        name: 'Secrets',
        title: '密钥管理',
        icon: 'Key'
      }
    ]
  },
  {
    path: '/monitor',
    name: 'Monitor',
    title: '监控中心',
    icon: 'Monitor',
    children: [
      {
        path: '/monitor',
        name: 'MonitorOverview',
        title: '监控概览',
        icon: 'DataAnalysis'
      },
      {
        path: '/monitor/logs',
        name: 'Logs',
        title: '日志查看',
        icon: 'Document'
      }
    ]
  },
  {
    path: '/users',
    name: 'Users',
    title: '用户管理',
    icon: 'User',
    meta: {
      permission: 'user:manage'
    },
    children: [
      {
        path: '/users',
        name: 'UserList',
        title: '用户列表',
        icon: 'UserFilled'
      },
      {
        path: '/users/roles',
        name: 'Roles',
        title: '角色管理',
        icon: 'Avatar'
      }
    ]
  }
]

// 根据权限过滤菜单项
const menuItems = computed(() => {
  return allMenuItems.filter(item => {
    // 检查权限
    if (item.meta?.permission && !userStore.hasPermission(item.meta.permission)) {
      return false
    }
    
    // 过滤子菜单
    if (item.children) {
      item.children = item.children.filter(child => {
        return !child.meta?.permission || userStore.hasPermission(child.meta.permission)
      })
    }
    
    return true
  })
})
</script>

<style lang="scss" scoped>
.sidebar-menu {
  height: 100%;
  
  .sidebar-menu-el {
    border-right: none;
    height: 100%;
    
    // 菜单项样式定制
    :deep(.el-menu-item) {
      height: 48px;
      line-height: 48px;
      
      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
      
      &.is-active {
        background-color: var(--el-color-primary-light-8);
        color: var(--el-color-primary);
        border-right: 3px solid var(--el-color-primary);
      }
    }
    
    :deep(.el-sub-menu__title) {
      height: 48px;
      line-height: 48px;
      
      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
    }
    
    // 折叠状态下的样式
    &.el-menu--collapse {
      width: 64px;
      
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        padding: 0 20px;
        text-align: center;
      }
    }
  }
}
</style>
