<template>
  <div class="sidebar-item">
    <!-- 菜单项 -->
    <component
      :is="linkComponent"
      v-bind="linkProps"
      :class="itemClasses"
      @click="handleClick"
    >
      <!-- 图标 -->
      <component
        v-if="icon"
        :is="icon"
        :class="iconClasses"
      />
      
      <!-- 标题 -->
      <span v-if="!collapsed" class="item-title">{{ title }}</span>
      
      <!-- 展开/收起图标 -->
      <ChevronDownIcon
        v-if="hasChildren && !collapsed"
        :class="expandIconClasses"
      />
      
      <!-- 徽章 -->
      <span v-if="badge && !collapsed" :class="badgeClasses">
        {{ badge }}
      </span>
    </component>

    <!-- 子菜单 -->
    <div
      v-if="hasChildren && !collapsed"
      :class="submenuClasses"
    >
      <slot />
    </div>

    <!-- 工具提示（折叠状态下显示） -->
    <div
      v-if="collapsed && title"
      class="tooltip"
      :class="tooltipClasses"
    >
      {{ title }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type Component } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'

// 定义属性
interface Props {
  to?: string | object
  href?: string
  icon?: Component | string
  title: string
  badge?: string | number
  collapsed?: boolean
  hasChildren?: boolean
  isChild?: boolean
  disabled?: boolean
  exact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  exact: false,
  hasChildren: false,
  isChild: false,
})

// 响应式数据
const isExpanded = ref(false)
const isHovered = ref(false)

// 路由
const route = useRoute()
const router = useRouter()

// 计算属性
const linkComponent = computed(() => {
  if (props.href) {
    return 'a'
  } else if (props.to) {
    return 'router-link'
  } else {
    return 'button'
  }
})

const linkProps = computed(() => {
  if (props.href) {
    return {
      href: props.href,
      target: '_blank',
      rel: 'noopener noreferrer',
    }
  } else if (props.to) {
    return {
      to: props.to,
      exact: props.exact,
    }
  } else {
    return {
      type: 'button',
    }
  }
})

const isActive = computed(() => {
  if (!props.to) return false
  
  if (typeof props.to === 'string') {
    return props.exact 
      ? route.path === props.to
      : route.path.startsWith(props.to)
  } else if (props.to.name) {
    return route.name === props.to.name
  }
  
  return false
})

const itemClasses = computed(() => {
  const classes = [
    'sidebar-item-link',
    {
      'item-active': isActive.value,
      'item-disabled': props.disabled,
      'item-child': props.isChild,
      'item-parent': props.hasChildren,
      'item-expanded': isExpanded.value,
      'item-collapsed': props.collapsed,
    },
  ]
  return classes
})

const iconClasses = computed(() => {
  const classes = [
    'item-icon',
    {
      'icon-active': isActive.value,
    },
  ]
  return classes
})

const expandIconClasses = computed(() => {
  const classes = [
    'expand-icon',
    {
      'expand-icon-rotated': isExpanded.value,
    },
  ]
  return classes
})

const badgeClasses = computed(() => {
  const classes = [
    'item-badge',
    {
      'badge-primary': !isActive.value,
      'badge-active': isActive.value,
    },
  ]
  return classes
})

const submenuClasses = computed(() => {
  const classes = [
    'submenu',
    {
      'submenu-expanded': isExpanded.value,
      'submenu-collapsed': !isExpanded.value,
    },
  ]
  return classes
})

const tooltipClasses = computed(() => {
  const classes = [
    'tooltip-content',
    {
      'tooltip-visible': isHovered.value,
    },
  ]
  return classes
})

// 方法
const handleClick = (event: Event): void => {
  if (props.disabled) {
    event.preventDefault()
    return
  }

  if (props.hasChildren) {
    event.preventDefault()
    toggleExpanded()
  }
}

const toggleExpanded = (): void => {
  isExpanded.value = !isExpanded.value
}

const handleMouseEnter = (): void => {
  isHovered.value = true
}

const handleMouseLeave = (): void => {
  isHovered.value = false
}
</script>

<style scoped>
/* 菜单项基础样式 */
.sidebar-item {
  @apply relative;
}

.sidebar-item-link {
  @apply flex items-center gap-3 px-4 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md;
  @apply transition-all duration-200 ease-in-out;
  @apply w-full text-left;
}

.theme-dark .sidebar-item-link {
  @apply text-gray-300 hover:text-gray-100 hover:bg-gray-800;
}

/* 子菜单项样式 */
.item-child {
  @apply ml-6 pl-6 border-l border-gray-200;
}

.theme-dark .item-child {
  @apply border-gray-700;
}

/* 激活状态 */
.item-active {
  @apply bg-blue-50 text-blue-700 border-r-2 border-blue-600;
}

.theme-dark .item-active {
  @apply bg-blue-900 bg-opacity-20 text-blue-400 border-blue-400;
}

/* 禁用状态 */
.item-disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* 折叠状态 */
.item-collapsed {
  @apply justify-center px-2;
}

/* 图标样式 */
.item-icon {
  @apply w-5 h-5 flex-shrink-0 text-gray-500;
  @apply transition-colors duration-200;
}

.theme-dark .item-icon {
  @apply text-gray-400;
}

.icon-active {
  @apply text-blue-600;
}

.theme-dark .icon-active {
  @apply text-blue-400;
}

/* 标题样式 */
.item-title {
  @apply flex-1 text-sm font-medium truncate;
}

/* 展开图标样式 */
.expand-icon {
  @apply w-4 h-4 text-gray-400 transition-transform duration-200;
}

.expand-icon-rotated {
  @apply transform rotate-180;
}

/* 徽章样式 */
.item-badge {
  @apply px-2 py-0.5 text-xs font-medium rounded-full;
}

.badge-primary {
  @apply bg-gray-100 text-gray-600;
}

.theme-dark .badge-primary {
  @apply bg-gray-700 text-gray-300;
}

.badge-active {
  @apply bg-blue-100 text-blue-800;
}

.theme-dark .badge-active {
  @apply bg-blue-900 bg-opacity-50 text-blue-300;
}

/* 子菜单样式 */
.submenu {
  @apply overflow-hidden transition-all duration-300 ease-in-out;
}

.submenu-expanded {
  @apply max-h-96 opacity-100;
}

.submenu-collapsed {
  @apply max-h-0 opacity-0;
}

/* 工具提示样式 */
.tooltip {
  @apply absolute left-full top-1/2 transform -translate-y-1/2 ml-2 z-50;
  @apply pointer-events-none;
}

.tooltip-content {
  @apply px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg;
  @apply opacity-0 transition-opacity duration-200;
  @apply whitespace-nowrap;
}

.theme-dark .tooltip-content {
  @apply bg-gray-700;
}

.tooltip-visible {
  @apply opacity-100;
}

/* 悬停效果 */
.sidebar-item:hover .tooltip-content {
  @apply opacity-100;
}

/* 焦点状态 */
.sidebar-item-link:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.theme-dark .sidebar-item-link:focus {
  @apply ring-offset-gray-900;
}

/* 父菜单项特殊样式 */
.item-parent {
  @apply cursor-pointer;
}

.item-parent:hover .expand-icon {
  @apply text-gray-600;
}

.theme-dark .item-parent:hover .expand-icon {
  @apply text-gray-300;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .item-child {
    @apply ml-4 pl-4;
  }
  
  .tooltip {
    @apply hidden;
  }
}

/* 动画效果 */
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 384px;
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    max-height: 384px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

.submenu-expanded {
  animation: slideDown 0.3s ease-in-out;
}

.submenu-collapsed {
  animation: slideUp 0.3s ease-in-out;
}

/* 链接样式重置 */
a.sidebar-item-link {
  @apply no-underline;
}

button.sidebar-item-link {
  @apply border-none bg-transparent;
}

/* 活跃状态的特殊效果 */
.item-active::before {
  content: '';
  @apply absolute left-0 top-0 bottom-0 w-1 bg-blue-600 rounded-r;
}

.theme-dark .item-active::before {
  @apply bg-blue-400;
}

/* 子菜单项的活跃状态 */
.item-child.item-active {
  @apply border-r-0 bg-blue-50;
}

.theme-dark .item-child.item-active {
  @apply bg-blue-900 bg-opacity-20;
}

.item-child.item-active::before {
  @apply hidden;
}
</style>
