<template>
  <aside :class="sidebarClasses">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="logo-container">
        <img
          src="/logo.svg"
          alt="PaaS Platform"
          class="logo-image"
        />
        <span v-if="!collapsed" class="logo-text">PaaS Platform</span>
      </div>
      
      <!-- 折叠按钮 -->
      <button
        v-if="collapsible"
        @click="toggleCollapse"
        class="collapse-btn"
      >
        <ChevronLeftIcon v-if="!collapsed" class="collapse-icon" />
        <ChevronRightIcon v-else class="collapse-icon" />
      </button>
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav">
      <div class="nav-section">
        <h3 v-if="!collapsed" class="section-title">主要功能</h3>
        
        <!-- 仪表板 -->
        <SidebarItem
          :to="{ name: 'dashboard' }"
          :icon="HomeIcon"
          :collapsed="collapsed"
          title="仪表板"
        />

        <!-- 应用管理 -->
        <SidebarItem
          :icon="CubeIcon"
          :collapsed="collapsed"
          title="应用管理"
          :has-children="true"
        >
          <SidebarItem
            :to="{ name: 'apps' }"
            title="应用列表"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'app-create' }"
            title="创建应用"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'app-templates' }"
            title="应用模板"
            :collapsed="collapsed"
            :is-child="true"
          />
        </SidebarItem>

        <!-- CI/CD -->
        <SidebarItem
          :icon="CogIcon"
          :collapsed="collapsed"
          title="CI/CD"
          :has-children="true"
        >
          <SidebarItem
            :to="{ name: 'pipelines' }"
            title="流水线"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'builds' }"
            title="构建历史"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'deployments' }"
            title="部署记录"
            :collapsed="collapsed"
            :is-child="true"
          />
        </SidebarItem>

        <!-- 监控告警 -->
        <SidebarItem
          :icon="ChartBarIcon"
          :collapsed="collapsed"
          title="监控告警"
          :has-children="true"
        >
          <SidebarItem
            :to="{ name: 'monitoring' }"
            title="系统监控"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'alerts' }"
            title="告警管理"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'logs' }"
            title="日志查看"
            :collapsed="collapsed"
            :is-child="true"
          />
        </SidebarItem>

        <!-- 用户管理 -->
        <SidebarItem
          :icon="UsersIcon"
          :collapsed="collapsed"
          title="用户管理"
          :has-children="true"
        >
          <SidebarItem
            :to="{ name: 'users' }"
            title="用户列表"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'roles' }"
            title="角色权限"
            :collapsed="collapsed"
            :is-child="true"
          />
          <SidebarItem
            :to="{ name: 'teams' }"
            title="团队管理"
            :collapsed="collapsed"
            :is-child="true"
          />
        </SidebarItem>
      </div>

      <div class="nav-section">
        <h3 v-if="!collapsed" class="section-title">系统设置</h3>
        
        <!-- 系统配置 -->
        <SidebarItem
          :to="{ name: 'settings' }"
          :icon="Cog6ToothIcon"
          :collapsed="collapsed"
          title="系统配置"
        />

        <!-- 安全中心 -->
        <SidebarItem
          :to="{ name: 'security' }"
          :icon="ShieldCheckIcon"
          :collapsed="collapsed"
          title="安全中心"
        />

        <!-- 审计日志 -->
        <SidebarItem
          :to="{ name: 'audit' }"
          :icon="DocumentTextIcon"
          :collapsed="collapsed"
          title="审计日志"
        />
      </div>
    </nav>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <!-- 主题切换 -->
      <button
        @click="toggleTheme"
        class="theme-toggle"
        :title="collapsed ? '切换主题' : ''"
      >
        <SunIcon v-if="isDark" class="theme-icon" />
        <MoonIcon v-else class="theme-icon" />
        <span v-if="!collapsed" class="theme-text">
          {{ isDark ? '浅色模式' : '深色模式' }}
        </span>
      </button>

      <!-- 用户信息 -->
      <div v-if="!collapsed" class="user-info">
        <div class="user-avatar">
          <img
            :src="userAvatar"
            :alt="userName"
            class="avatar-image"
          />
        </div>
        <div class="user-details">
          <p class="user-name">{{ userName }}</p>
          <p class="user-role">{{ userRole }}</p>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  HomeIcon,
  CubeIcon,
  CogIcon,
  ChartBarIcon,
  UsersIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  SunIcon,
  MoonIcon,
} from '@heroicons/vue/24/outline'
import SidebarItem from './SidebarItem.vue'
import { themeManager } from '@/styles/theme'

// 定义属性
interface Props {
  collapsible?: boolean
  defaultCollapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsible: true,
  defaultCollapsed: false,
})

// 定义事件
const emit = defineEmits<{
  collapse: [collapsed: boolean]
}>()

// 响应式数据
const collapsed = ref(props.defaultCollapsed)
const isDark = ref(themeManager.getCurrentTheme().name === 'dark')

// 用户信息（实际项目中应该从状态管理获取）
const userName = ref('管理员')
const userRole = ref('系统管理员')
const userAvatar = ref('/default-avatar.png')

// 路由
const router = useRouter()

// 计算属性
const sidebarClasses = computed(() => {
  const classes = [
    'sidebar',
    {
      'sidebar-collapsed': collapsed.value,
      'sidebar-expanded': !collapsed.value,
    },
  ]
  return classes
})

// 方法
const toggleCollapse = (): void => {
  collapsed.value = !collapsed.value
  emit('collapse', collapsed.value)
}

const toggleTheme = (): void => {
  themeManager.toggleTheme()
  isDark.value = themeManager.getCurrentTheme().name === 'dark'
}

// 监听主题变化
themeManager.addListener((theme) => {
  isDark.value = theme.name === 'dark'
})
</script>

<style scoped>
/* 侧边栏基础样式 */
.sidebar {
  @apply flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300 ease-in-out;
  @apply shadow-sm;
}

.theme-dark .sidebar {
  @apply bg-gray-900 border-gray-700;
}

.sidebar-collapsed {
  @apply w-16;
}

.sidebar-expanded {
  @apply w-64;
}

/* 侧边栏头部 */
.sidebar-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.theme-dark .sidebar-header {
  @apply border-gray-700;
}

.logo-container {
  @apply flex items-center gap-3;
}

.logo-image {
  @apply w-8 h-8 flex-shrink-0;
}

.logo-text {
  @apply text-lg font-semibold text-gray-900 truncate;
}

.theme-dark .logo-text {
  @apply text-gray-100;
}

.collapse-btn {
  @apply p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md;
  @apply transition-colors duration-200;
}

.theme-dark .collapse-btn {
  @apply text-gray-400 hover:text-gray-200 hover:bg-gray-800;
}

.collapse-icon {
  @apply w-5 h-5;
}

/* 导航菜单 */
.sidebar-nav {
  @apply flex-1 overflow-y-auto py-4 space-y-6;
}

.nav-section {
  @apply space-y-1;
}

.section-title {
  @apply px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2;
}

.theme-dark .section-title {
  @apply text-gray-400;
}

/* 侧边栏底部 */
.sidebar-footer {
  @apply p-4 border-t border-gray-200 space-y-4;
}

.theme-dark .sidebar-footer {
  @apply border-gray-700;
}

/* 主题切换 */
.theme-toggle {
  @apply flex items-center gap-3 w-full p-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md;
  @apply transition-colors duration-200;
}

.theme-dark .theme-toggle {
  @apply text-gray-300 hover:text-gray-100 hover:bg-gray-800;
}

.theme-icon {
  @apply w-5 h-5 flex-shrink-0;
}

.theme-text {
  @apply text-sm font-medium;
}

/* 用户信息 */
.user-info {
  @apply flex items-center gap-3;
}

.user-avatar {
  @apply flex-shrink-0;
}

.avatar-image {
  @apply w-8 h-8 rounded-full object-cover;
}

.user-details {
  @apply flex-1 min-w-0;
}

.user-name {
  @apply text-sm font-medium text-gray-900 truncate;
}

.theme-dark .user-name {
  @apply text-gray-100;
}

.user-role {
  @apply text-xs text-gray-500 truncate;
}

.theme-dark .user-role {
  @apply text-gray-400;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50;
  }
  
  .sidebar-collapsed {
    @apply -translate-x-full;
  }
  
  .sidebar-expanded {
    @apply translate-x-0;
  }
}

/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  @apply w-1;
}

.sidebar-nav::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.theme-dark .sidebar-nav::-webkit-scrollbar-thumb {
  @apply bg-gray-600;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

.theme-dark .sidebar-nav::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}
</style>
