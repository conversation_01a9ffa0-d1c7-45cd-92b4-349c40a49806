<template>
  <div class="responsive-layout" :class="layoutClasses">
    <!-- 桌面端侧边栏 -->
    <Sidebar
      v-if="isDesktop"
      :collapsible="true"
      :default-collapsed="false"
      @collapse="handleSidebarCollapse"
    />

    <!-- 移动端导航 -->
    <MobileNavigation v-if="isMobile" />

    <!-- 主内容区域 -->
    <main class="main-content" :class="mainContentClasses">
      <!-- 页面头部 -->
      <header v-if="showHeader" class="page-header">
        <div class="header-content">
          <!-- 面包屑导航 -->
          <nav v-if="breadcrumbs.length > 0" class="breadcrumb-nav">
            <ol class="breadcrumb-list">
              <li
                v-for="(crumb, index) in breadcrumbs"
                :key="index"
                class="breadcrumb-item"
              >
                <router-link
                  v-if="crumb.to && index < breadcrumbs.length - 1"
                  :to="crumb.to"
                  class="breadcrumb-link"
                >
                  {{ crumb.title }}
                </router-link>
                <span v-else class="breadcrumb-current">
                  {{ crumb.title }}
                </span>
                <ChevronRightIcon
                  v-if="index < breadcrumbs.length - 1"
                  class="breadcrumb-separator"
                />
              </li>
            </ol>
          </nav>

          <!-- 页面标题和操作 -->
          <div class="page-title-section">
            <div class="title-content">
              <h1 v-if="pageTitle" class="page-title">{{ pageTitle }}</h1>
              <p v-if="pageDescription" class="page-description">
                {{ pageDescription }}
              </p>
            </div>
            
            <div v-if="$slots.actions" class="page-actions">
              <slot name="actions" />
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-content" :class="contentClasses">
        <slot />
      </div>

      <!-- 页面底部 -->
      <footer v-if="showFooter" class="page-footer">
        <div class="footer-content">
          <slot name="footer">
            <p class="footer-text">
              © {{ currentYear }} PaaS Platform. All rights reserved.
            </p>
          </slot>
        </div>
      </footer>
    </main>

    <!-- 全局加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <svg class="animate-spin" viewBox="0 0 24 24">
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-dasharray="32"
            stroke-dashoffset="32"
          />
        </svg>
      </div>
      <p v-if="loadingText" class="loading-text">{{ loadingText }}</p>
    </div>

    <!-- 通知容器 -->
    <div class="notification-container">
      <!-- 这里可以放置全局通知组件 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ChevronRightIcon } from '@heroicons/vue/24/outline'
import { useResponsive } from '@/composables/useResponsive'
import Sidebar from './Sidebar.vue'
import MobileNavigation from '../mobile/MobileNavigation.vue'

// 面包屑接口
interface Breadcrumb {
  title: string
  to?: string | object
}

// 定义属性
interface Props {
  pageTitle?: string
  pageDescription?: string
  breadcrumbs?: Breadcrumb[]
  showHeader?: boolean
  showFooter?: boolean
  loading?: boolean
  loadingText?: string
  fluid?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  breadcrumbs: () => [],
  showHeader: true,
  showFooter: false,
  loading: false,
  fluid: false,
  padding: 'md',
})

// 响应式状态
const { isMobile, isTablet, isDesktop } = useResponsive()
const sidebarCollapsed = ref(false)
const currentYear = new Date().getFullYear()

// 计算属性
const layoutClasses = computed(() => {
  const classes = []
  
  if (isMobile.value) {
    classes.push('layout-mobile')
  } else if (isTablet.value) {
    classes.push('layout-tablet')
  } else {
    classes.push('layout-desktop')
  }
  
  if (sidebarCollapsed.value && isDesktop.value) {
    classes.push('sidebar-collapsed')
  }
  
  return classes
})

const mainContentClasses = computed(() => {
  const classes = ['main-content-inner']
  
  if (props.fluid) {
    classes.push('content-fluid')
  }
  
  return classes
})

const contentClasses = computed(() => {
  const classes = []
  
  switch (props.padding) {
    case 'none':
      classes.push('content-padding-none')
      break
    case 'sm':
      classes.push('content-padding-sm')
      break
    case 'md':
      classes.push('content-padding-md')
      break
    case 'lg':
      classes.push('content-padding-lg')
      break
  }
  
  return classes
})

// 方法
const handleSidebarCollapse = (collapsed: boolean) => {
  sidebarCollapsed.value = collapsed
}
</script>

<style scoped>
/* 响应式布局基础样式 */
.responsive-layout {
  @apply min-h-screen bg-gray-50 flex;
}

.theme-dark .responsive-layout {
  @apply bg-gray-900;
}

/* 布局变体 */
.layout-mobile {
  @apply flex-col;
}

.layout-tablet {
  @apply flex-col lg:flex-row;
}

.layout-desktop {
  @apply flex-row;
}

/* 主内容区域 */
.main-content {
  @apply flex-1 flex flex-col min-w-0;
  @apply transition-all duration-300 ease-in-out;
}

.layout-mobile .main-content {
  @apply pt-14 pb-16; /* 为顶部和底部导航留出空间 */
}

.layout-desktop .main-content {
  @apply ml-64; /* 侧边栏宽度 */
}

.layout-desktop.sidebar-collapsed .main-content {
  @apply ml-16; /* 折叠后的侧边栏宽度 */
}

.main-content-inner {
  @apply flex-1 flex flex-col;
}

.content-fluid {
  @apply max-w-none;
}

/* 页面头部 */
.page-header {
  @apply bg-white border-b border-gray-200 shadow-sm;
}

.theme-dark .page-header {
  @apply bg-gray-800 border-gray-700;
}

.header-content {
  @apply px-4 py-4 sm:px-6 lg:px-8;
}

/* 面包屑导航 */
.breadcrumb-nav {
  @apply mb-4;
}

.breadcrumb-list {
  @apply flex items-center space-x-2 text-sm;
}

.breadcrumb-item {
  @apply flex items-center;
}

.breadcrumb-link {
  @apply text-gray-500 hover:text-gray-700 transition-colors duration-200;
}

.theme-dark .breadcrumb-link {
  @apply text-gray-400 hover:text-gray-200;
}

.breadcrumb-current {
  @apply text-gray-900 font-medium;
}

.theme-dark .breadcrumb-current {
  @apply text-gray-100;
}

.breadcrumb-separator {
  @apply w-4 h-4 text-gray-400 mx-2;
}

/* 页面标题区域 */
.page-title-section {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
}

.title-content {
  @apply flex-1 min-w-0;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 truncate;
}

.theme-dark .page-title {
  @apply text-gray-100;
}

.page-description {
  @apply mt-1 text-sm text-gray-500;
}

.theme-dark .page-description {
  @apply text-gray-400;
}

.page-actions {
  @apply flex items-center gap-2 flex-shrink-0;
}

/* 页面内容 */
.page-content {
  @apply flex-1;
}

.content-padding-none {
  @apply p-0;
}

.content-padding-sm {
  @apply p-2 sm:p-4;
}

.content-padding-md {
  @apply p-4 sm:p-6 lg:p-8;
}

.content-padding-lg {
  @apply p-6 sm:p-8 lg:p-12;
}

/* 页面底部 */
.page-footer {
  @apply bg-white border-t border-gray-200 mt-auto;
}

.theme-dark .page-footer {
  @apply bg-gray-800 border-gray-700;
}

.footer-content {
  @apply px-4 py-4 sm:px-6 lg:px-8;
}

.footer-text {
  @apply text-sm text-gray-500 text-center;
}

.theme-dark .footer-text {
  @apply text-gray-400;
}

/* 加载遮罩 */
.loading-overlay {
  @apply fixed inset-0 bg-white bg-opacity-75 flex flex-col items-center justify-center z-50;
  @apply backdrop-blur-sm;
}

.theme-dark .loading-overlay {
  @apply bg-gray-900 bg-opacity-75;
}

.loading-spinner {
  @apply w-12 h-12 text-blue-600;
}

.loading-spinner svg {
  @apply w-full h-full;
}

.loading-text {
  @apply mt-4 text-lg text-gray-600;
}

.theme-dark .loading-text {
  @apply text-gray-400;
}

/* 通知容器 */
.notification-container {
  @apply fixed top-4 right-4 z-50 space-y-2;
  @apply pointer-events-none;
}

.layout-mobile .notification-container {
  @apply top-16; /* 避免与移动端头部重叠 */
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .page-title-section {
    @apply flex-col items-start;
  }
  
  .page-actions {
    @apply w-full justify-end;
  }
  
  .breadcrumb-list {
    @apply flex-wrap;
  }
  
  .page-title {
    @apply text-xl;
  }
}

@media (max-width: 768px) {
  .header-content {
    @apply px-4 py-3;
  }
  
  .content-padding-md {
    @apply p-4;
  }
  
  .content-padding-lg {
    @apply p-6;
  }
}

/* 平板端调整 */
@media (min-width: 768px) and (max-width: 1024px) {
  .layout-tablet .main-content {
    @apply ml-0;
  }
}

/* 安全区域适配 */
@supports (padding: env(safe-area-inset-top)) {
  .layout-mobile .main-content {
    padding-top: calc(3.5rem + env(safe-area-inset-top));
    padding-bottom: calc(4rem + env(safe-area-inset-bottom));
  }
  
  .layout-mobile .notification-container {
    top: calc(1rem + env(safe-area-inset-top) + 3.5rem);
    right: calc(1rem + env(safe-area-inset-right));
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .page-header {
    @apply border-b-2;
  }
  
  .page-footer {
    @apply border-t-2;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .main-content,
  .breadcrumb-link,
  .loading-spinner {
    @apply transition-none;
  }
  
  .animate-spin {
    animation: none;
  }
}
</style>
