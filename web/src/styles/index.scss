// 全局样式文件

// 使用现代 @use 语法导入变量和混合
// 替代已弃用的 @import 语法，符合 Dart Sass 3.0.0 规范
@use './variables' as vars;
@use './mixins' as mixins;
@use 'sass:color';

// Element Plus 主题定制
// 使用现代 Sass 语法和变量引用
:root {
  // 主色调 - 使用 color.scale() 替代已弃用的 lighten() 和 darken()
  --el-color-primary: #{vars.$primary-color};
  --el-color-primary-light-3: #{color.scale(vars.$primary-color, $lightness: 15%)};
  --el-color-primary-light-5: #{color.scale(vars.$primary-color, $lightness: 25%)};
  --el-color-primary-light-7: #{color.scale(vars.$primary-color, $lightness: 35%)};
  --el-color-primary-light-8: #{color.scale(vars.$primary-color, $lightness: 40%)};
  --el-color-primary-light-9: #{color.scale(vars.$primary-color, $lightness: 45%)};
  --el-color-primary-dark-2: #{color.scale(vars.$primary-color, $lightness: -10%)};

  // 功能色彩
  --el-color-success: #{vars.$success-color};
  --el-color-warning: #{vars.$warning-color};
  --el-color-danger: #{vars.$danger-color};
  --el-color-info: #{vars.$info-color};

  // 文字颜色
  --el-text-color-primary: #{vars.$text-color-primary};
  --el-text-color-regular: #{vars.$text-color-regular};
  --el-text-color-secondary: #{vars.$text-color-secondary};
  --el-text-color-placeholder: #{vars.$text-color-placeholder};

  // 边框颜色
  --el-border-color: #{vars.$border-color};
  --el-border-color-light: #{vars.$border-color-light};
  --el-border-color-lighter: #{vars.$border-color-lighter};
  --el-border-color-extra-light: #{vars.$border-color-extra-light};

  // 背景颜色
  --el-bg-color: #{vars.$bg-color};
  --el-bg-color-page: #{vars.$bg-color-page};
  --el-bg-color-overlay: #{vars.$bg-color-overlay};

  // 圆角
  --el-border-radius-base: #{vars.$border-radius-base};
  --el-border-radius-small: #{vars.$border-radius-small};
  --el-border-radius-round: #{vars.$border-radius-round};

  // 阴影
  --el-box-shadow-base: #{vars.$box-shadow-base};
  --el-box-shadow-light: #{vars.$box-shadow-light};
}

// 全局样式类
.text-primary { color: var(--el-color-primary); }
.text-success { color: var(--el-color-success); }
.text-warning { color: var(--el-color-warning); }
.text-danger { color: var(--el-color-danger); }
.text-info { color: var(--el-color-info); }

// 间距工具类
@for $i from 0 through 10 {
  .m-#{$i} { margin: #{$i * 4}px; }
  .mt-#{$i} { margin-top: #{$i * 4}px; }
  .mr-#{$i} { margin-right: #{$i * 4}px; }
  .mb-#{$i} { margin-bottom: #{$i * 4}px; }
  .ml-#{$i} { margin-left: #{$i * 4}px; }
  .mx-#{$i} { margin-left: #{$i * 4}px; margin-right: #{$i * 4}px; }
  .my-#{$i} { margin-top: #{$i * 4}px; margin-bottom: #{$i * 4}px; }
  
  .p-#{$i} { padding: #{$i * 4}px; }
  .pt-#{$i} { padding-top: #{$i * 4}px; }
  .pr-#{$i} { padding-right: #{$i * 4}px; }
  .pb-#{$i} { padding-bottom: #{$i * 4}px; }
  .pl-#{$i} { padding-left: #{$i * 4}px; }
  .px-#{$i} { padding-left: #{$i * 4}px; padding-right: #{$i * 4}px; }
  .py-#{$i} { padding-top: #{$i * 4}px; padding-bottom: #{$i * 4}px; }
}

// 布局工具类
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

// 文本工具类
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }
.font-light { font-weight: 300; }

// 显示工具类
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

// 位置工具类
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// 宽高工具类
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

// 圆角工具类
.rounded { border-radius: var(--el-border-radius-base); }
.rounded-sm { border-radius: var(--el-border-radius-small); }
.rounded-lg { border-radius: 8px; }
.rounded-full { border-radius: 50%; }

// 阴影工具类
.shadow { box-shadow: var(--el-box-shadow-base); }
.shadow-light { box-shadow: var(--el-box-shadow-light); }
.shadow-none { box-shadow: none; }

// 过渡动画
.transition {
  transition: all 0.3s ease;
}

// 自定义组件样式
.page-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 60px);
}

.card-container {
  background: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
  margin-bottom: 20px;
}

// 响应式断点
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .card-container {
    padding: 15px;
    margin-bottom: 15px;
  }
}
