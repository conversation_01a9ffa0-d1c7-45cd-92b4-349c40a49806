// SCSS 混合宏定义
// 使用现代 Sass 语法，兼容 Dart Sass 3.0.0
@use 'sass:color';
@use './variables' as vars;

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 居中对齐
@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 垂直居中
@mixin vertical-center {
  display: flex;
  align-items: center;
}

// 水平居中
@mixin horizontal-center {
  display: flex;
  justify-content: center;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 响应式断点
// 使用现代变量引用语法
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{vars.$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{vars.$breakpoint-xs}) and (max-width: #{vars.$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{vars.$breakpoint-sm}) and (max-width: #{vars.$breakpoint-md - 1px}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{vars.$breakpoint-md}) and (max-width: #{vars.$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{vars.$breakpoint-lg}) {
      @content;
    }
  }
}

// 卡片样式
@mixin card-style {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
}

// 按钮悬停效果
// 使用现代 color.scale() 函数替代已弃用的 lighten()
@mixin button-hover($color) {
  transition: all vars.$transition-duration vars.$transition-function;

  &:hover {
    background-color: color.scale($color, $lightness: 10%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba($color, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// 加载动画
// 修复 mixed-decls 警告：将声明移到嵌套规则之前
@mixin loading-animation {
  animation: loading 1s linear infinite;

  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

// 淡入动画
// 修复 mixed-decls 警告：将声明移到嵌套规则之前
@mixin fade-in($duration: 0.3s) {
  animation: fadeIn $duration ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// 滑入动画
@mixin slide-in($direction: 'left', $duration: 0.3s) {
  @keyframes slideIn {
    from {
      opacity: 0;
      @if $direction == 'left' {
        transform: translateX(-20px);
      } @else if $direction == 'right' {
        transform: translateX(20px);
      } @else if $direction == 'top' {
        transform: translateY(-20px);
      } @else if $direction == 'bottom' {
        transform: translateY(20px);
      }
    }
    to {
      opacity: 1;
      transform: translate(0, 0);
    }
  }
  
  animation: slideIn $duration ease-out;
}

// 状态指示器
@mixin status-indicator($color) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    width: 6px;
    height: 6px;
    background-color: $color;
    border-radius: 50%;
    transform: translateY(-50%);
  }
}
