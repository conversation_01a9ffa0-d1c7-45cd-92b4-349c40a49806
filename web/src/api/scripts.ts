import { request } from '@/utils/request'
import type { ApiResponse, PaginatedResponse } from '@/types'

// 脚本执行请求参数
export interface ExecuteScriptRequest {
  app_id: string
  script_path: string
  runtime_type: 'python' | 'nodejs' | 'go' | 'java'
  parameters?: Record<string, any>
  environment?: Record<string, string>
  timeout?: number
  priority?: number
  callback_url?: string
}

// 任务查询参数
export interface TaskQueryParams {
  page?: number
  pageSize?: number
  status?: TaskStatus
  app_id?: string
  user_id?: string
  start_time?: string
  end_time?: string
}

// 任务状态类型
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'canceled'

// 任务数据结构
export interface ScriptTask {
  id: string
  app_id: string
  user_id: string
  tenant_id: string
  script_path: string
  runtime_type: string
  runtime_config: Record<string, any>
  parameters: Record<string, any>
  environment: Record<string, string>
  status: TaskStatus
  priority: number
  timeout: number
  callback_url?: string
  started_at?: string
  finished_at?: string
  duration?: number
  exit_code?: number
  error_message?: string
  created_at: string
  updated_at: string
}

// 任务结果数据结构
export interface TaskResult {
  task_id: string
  status: TaskStatus
  exit_code: number
  stdout: string
  stderr: string
  artifacts: TaskArtifact[]
  resource_usage: ResourceUsage
  created_at: string
}

// 任务产物
export interface TaskArtifact {
  name: string
  path: string
  size: number
  mime_type: string
  url: string
}

// 资源使用情况
export interface ResourceUsage {
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  network_io: number
}

// 日志条目
export interface LogEntry {
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  source: string
}

// 脚本模板数据结构
export interface ScriptTemplate {
  id: string
  name: string
  description: string
  category: string
  language: string
  version: string
  content: string
  parameters: Record<string, any>
  environment: Record<string, string>
  resources: Record<string, any>
  tags: string[]
  status: 'draft' | 'active' | 'deprecated'
  tenant_id: string
  created_by: string
  created_at: string
  updated_at: string
}

// 创建模板请求
export interface CreateTemplateRequest {
  name: string
  description: string
  category: string
  language: string
  version: string
  content: string
  parameters?: Record<string, any>
  environment?: Record<string, string>
  resources?: Record<string, any>
  tags?: string[]
}

// 更新模板请求
export interface UpdateTemplateRequest {
  name?: string
  description?: string
  content?: string
  parameters?: Record<string, any>
  environment?: Record<string, string>
  resources?: Record<string, any>
  tags?: string[]
  status?: 'draft' | 'active' | 'deprecated'
}

// 模板查询参数
export interface TemplateQueryParams {
  page?: number
  pageSize?: number
  category?: string
  language?: string
  status?: 'draft' | 'active' | 'deprecated'
}

// 任务调度数据结构
export interface TaskSchedule {
  id: string
  name: string
  description: string
  app_id: string
  script_path: string
  parameters: Record<string, any>
  environment: Record<string, string>
  cron_expr: string
  timezone: string
  status: 'active' | 'inactive' | 'paused'
  next_run?: string
  last_run?: string
  run_count: number
  tenant_id: string
  created_by: string
  created_at: string
  updated_at: string
}

// 创建调度请求
export interface CreateScheduleRequest {
  name: string
  description: string
  app_id: string
  script_path: string
  parameters?: Record<string, any>
  environment?: Record<string, string>
  cron_expr: string
  timezone?: string
}

// 更新调度请求
export interface UpdateScheduleRequest {
  name?: string
  description?: string
  parameters?: Record<string, any>
  environment?: Record<string, string>
  cron_expr?: string
  timezone?: string
  status?: 'active' | 'inactive' | 'paused'
}

// 调度查询参数
export interface ScheduleQueryParams {
  page?: number
  pageSize?: number
  app_id?: string
  status?: 'active' | 'inactive' | 'paused'
}

// 手动触发调度请求
export interface TriggerScheduleRequest {
  parameters?: Record<string, any>
  environment?: Record<string, string>
}

// 统计概览数据
export interface StatsOverview {
  today: TaskStatsData
  yesterday: TaskStatsData
  this_week: TaskStatsData
  this_month: TaskStatsData
}

// 任务统计数据
export interface TaskStatsData {
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  canceled_tasks: number
  success_rate: number
  avg_duration: number
}

// 指标数据点
export interface MetricPoint {
  timestamp: string
  task_count: number
  success_rate: number
  avg_duration: number
  cpu_usage: number
  memory_usage: number
}

// 指标查询参数
export interface MetricsQueryParams {
  time_range?: '1h' | '6h' | '24h' | '7d' | '30d'
  interval?: '5m' | '15m' | '1h' | '6h' | '1d'
}

/**
 * 脚本执行相关API
 */
export const scriptsApi = {
  /**
   * 执行脚本
   */
  executeScript(data: ExecuteScriptRequest): Promise<ApiResponse<{ task_id: string }>> {
    return request.post('/v1/scripts/execute', data)
  },

  /**
   * 获取任务列表
   */
  getTasks(params?: TaskQueryParams): Promise<ApiResponse<PaginatedResponse<ScriptTask>>> {
    return request.get('/v1/scripts/tasks', { params })
  },

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): Promise<ApiResponse<ScriptTask>> {
    return request.get(`/v1/scripts/tasks/${taskId}`)
  },

  /**
   * 获取任务结果
   */
  getTaskResult(taskId: string): Promise<ApiResponse<TaskResult>> {
    return request.get(`/v1/scripts/tasks/${taskId}/result`)
  },

  /**
   * 获取任务日志
   */
  getTaskLogs(taskId: string): Promise<ApiResponse<LogEntry[]>> {
    return request.get(`/v1/scripts/tasks/${taskId}/logs`)
  },

  /**
   * 取消任务
   */
  cancelTask(taskId: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/scripts/tasks/${taskId}`)
  },

  /**
   * 下载任务产物
   */
  downloadArtifact(taskId: string, artifactName: string): Promise<Blob> {
    return request.get(`/v1/scripts/tasks/${taskId}/artifacts/${artifactName}`, {
      responseType: 'blob'
    })
  },

  /**
   * 获取模板列表
   */
  getTemplates(params?: TemplateQueryParams): Promise<ApiResponse<PaginatedResponse<ScriptTemplate>>> {
    return request.get('/v1/script-templates', { params })
  },

  /**
   * 获取模板详情
   */
  getTemplate(templateId: string): Promise<ApiResponse<ScriptTemplate>> {
    return request.get(`/v1/script-templates/${templateId}`)
  },

  /**
   * 创建模板
   */
  createTemplate(data: CreateTemplateRequest): Promise<ApiResponse<ScriptTemplate>> {
    return request.post('/v1/script-templates', data)
  },

  /**
   * 更新模板
   */
  updateTemplate(templateId: string, data: UpdateTemplateRequest): Promise<ApiResponse<ScriptTemplate>> {
    return request.put(`/v1/script-templates/${templateId}`, data)
  },

  /**
   * 删除模板
   */
  deleteTemplate(templateId: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/script-templates/${templateId}`)
  },

  /**
   * 获取调度列表
   */
  getSchedules(params?: ScheduleQueryParams): Promise<ApiResponse<PaginatedResponse<TaskSchedule>>> {
    return request.get('/v1/script-schedules', { params })
  },

  /**
   * 获取调度详情
   */
  getSchedule(scheduleId: string): Promise<ApiResponse<TaskSchedule>> {
    return request.get(`/v1/script-schedules/${scheduleId}`)
  },

  /**
   * 创建调度
   */
  createSchedule(data: CreateScheduleRequest): Promise<ApiResponse<TaskSchedule>> {
    return request.post('/v1/script-schedules', data)
  },

  /**
   * 更新调度
   */
  updateSchedule(scheduleId: string, data: UpdateScheduleRequest): Promise<ApiResponse<TaskSchedule>> {
    return request.put(`/v1/script-schedules/${scheduleId}`, data)
  },

  /**
   * 删除调度
   */
  deleteSchedule(scheduleId: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/script-schedules/${scheduleId}`)
  },

  /**
   * 手动触发调度
   */
  triggerSchedule(scheduleId: string, data?: TriggerScheduleRequest): Promise<ApiResponse<{ task_id: string }>> {
    return request.post(`/v1/script-schedules/${scheduleId}/trigger`, data)
  },

  /**
   * 获取统计概览
   */
  getStatsOverview(): Promise<ApiResponse<StatsOverview>> {
    return request.get('/v1/script-stats/overview')
  },

  /**
   * 获取指标数据
   */
  getMetrics(params?: MetricsQueryParams): Promise<ApiResponse<MetricPoint[]>> {
    return request.get('/v1/script-stats/metrics', { params })
  }
}
