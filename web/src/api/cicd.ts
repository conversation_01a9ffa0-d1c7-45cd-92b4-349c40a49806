import { request } from '@/utils/request'
import type { ApiResponse, Pipeline, Build, PaginatedResponse } from '@/types'

// 流水线创建请求参数
export interface CreatePipelineRequest {
  name: string
  description: string
  repository: {
    url: string
    branch: string
    type: 'gitea' | 'github' | 'gitlab'
  }
  stages: Array<{
    name: string
    type: 'build' | 'test' | 'deploy'
    commands: string[]
    environment?: Record<string, string>
    timeout?: number
  }>
  triggers: Array<{
    type: 'webhook' | 'schedule' | 'manual'
    config: Record<string, any>
  }>
}

// 构建创建请求参数
export interface CreateBuildRequest {
  pipelineId: string
  branch?: string
  commit?: string
  environment?: Record<string, string>
}

// 查询参数
export interface PipelineQueryParams {
  page?: number
  pageSize?: number
  status?: string
  search?: string
}

export interface BuildQueryParams {
  page?: number
  pageSize?: number
  pipelineId?: string
  status?: string
  branch?: string
}

/**
 * CI/CD相关API
 */
export const cicdApi = {
  // 流水线管理
  /**
   * 获取流水线列表
   */
  getPipelines(params?: PipelineQueryParams): Promise<ApiResponse<PaginatedResponse<Pipeline>>> {
    return request.get('/v1/pipelines', { params })
  },

  /**
   * 获取流水线详情
   */
  getPipeline(id: string): Promise<ApiResponse<Pipeline>> {
    return request.get(`/v1/pipelines/${id}`)
  },

  /**
   * 创建流水线
   */
  createPipeline(data: CreatePipelineRequest): Promise<ApiResponse<Pipeline>> {
    return request.post('/v1/pipelines', data)
  },

  /**
   * 更新流水线
   */
  updatePipeline(id: string, data: Partial<CreatePipelineRequest>): Promise<ApiResponse<Pipeline>> {
    return request.put(`/v1/pipelines/${id}`, data)
  },

  /**
   * 删除流水线
   */
  deletePipeline(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/pipelines/${id}`)
  },

  // 构建管理
  /**
   * 获取构建列表
   */
  getBuilds(params?: BuildQueryParams): Promise<ApiResponse<PaginatedResponse<Build>>> {
    return request.get('/v1/builds', { params })
  },

  /**
   * 获取构建详情
   */
  getBuild(id: string): Promise<ApiResponse<Build>> {
    return request.get(`/v1/builds/${id}`)
  },

  /**
   * 创建构建
   */
  createBuild(data: CreateBuildRequest): Promise<ApiResponse<Build>> {
    return request.post('/v1/builds', data)
  },

  /**
   * 取消构建
   */
  cancelBuild(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/builds/${id}/cancel`)
  },

  /**
   * 重试构建
   */
  retryBuild(id: string): Promise<ApiResponse<Build>> {
    return request.post(`/v1/builds/${id}/retry`)
  },

  /**
   * 获取构建日志
   */
  getBuildLogs(id: string, params?: {
    follow?: boolean
    lines?: number
  }): Promise<ApiResponse<{ logs: string }>> {
    return request.get(`/v1/builds/${id}/logs`, { params })
  },

  /**
   * 获取构建产物
   */
  getBuildArtifacts(id: string): Promise<ApiResponse<any[]>> {
    return request.get(`/v1/builds/${id}/artifacts`)
  },

  // 部署管理
  /**
   * 获取部署列表
   */
  getDeployments(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    return request.get('/v1/deployments', { params })
  },

  /**
   * 获取部署详情
   */
  getDeployment(id: string): Promise<ApiResponse<any>> {
    return request.get(`/v1/deployments/${id}`)
  },

  /**
   * 创建部署
   */
  createDeployment(data: any): Promise<ApiResponse<any>> {
    return request.post('/v1/deployments', data)
  },

  /**
   * 回滚部署
   */
  rollbackDeployment(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/deployments/${id}/rollback`)
  },

  // 环境管理
  /**
   * 获取环境列表
   */
  getEnvironments(): Promise<ApiResponse<any[]>> {
    return request.get('/v1/environments')
  },

  /**
   * 创建环境
   */
  createEnvironment(data: any): Promise<ApiResponse<any>> {
    return request.post('/v1/environments', data)
  },

  // 构建节点管理
  /**
   * 获取构建节点列表
   */
  getBuildNodes(): Promise<ApiResponse<any[]>> {
    return request.get('/v1/nodes')
  },

  /**
   * 注册构建节点
   */
  registerBuildNode(data: any): Promise<ApiResponse<any>> {
    return request.post('/v1/nodes', data)
  }
}
