import { request } from '@/utils/request'
import type { ApiResponse, Application, ApplicationInstance, PaginatedResponse } from '@/types'

// 应用创建请求参数
export interface CreateAppRequest {
  name: string
  displayName: string
  description: string
  runtime: {
    language: string
    version: string
    framework?: string
  }
  repository?: {
    url: string
    branch: string
    type: 'gitea' | 'github' | 'gitlab'
  }
  buildConfig?: {
    dockerfile?: string
    buildArgs?: Record<string, string>
    excludes?: string[]
  }
  deployConfig: {
    port: number
    instances: number
    command?: string[]
    workingDir?: string
    resources: {
      cpu: string
      memory: string
      storage?: string
    }
    environment?: Record<string, string>
    healthCheck?: {
      path: string
      port: number
      initialDelaySeconds: number
      periodSeconds: number
    }
  }
}

// 应用更新请求参数
export interface UpdateAppRequest {
  displayName?: string
  description?: string
  deployConfig?: Partial<CreateAppRequest['deployConfig']>
}

// 应用查询参数
export interface AppQueryParams {
  page?: number
  pageSize?: number
  status?: string
  language?: string
  search?: string
  tenantId?: string
}

// 应用实例操作参数
export interface InstanceActionRequest {
  action: 'start' | 'stop' | 'restart'
  instanceIds?: string[]
}

// 应用扩缩容参数
export interface ScaleAppRequest {
  instances: number
}

/**
 * 应用管理相关API
 */
export const appsApi = {
  /**
   * 获取应用列表
   */
  getApps(params?: AppQueryParams): Promise<ApiResponse<PaginatedResponse<Application>>> {
    return request.get('/v1/apps', { params })
  },

  /**
   * 获取应用详情
   */
  getApp(id: string): Promise<ApiResponse<Application>> {
    return request.get(`/v1/apps/${id}`)
  },

  /**
   * 创建应用
   */
  createApp(data: CreateAppRequest): Promise<ApiResponse<Application>> {
    return request.post('/v1/apps', data)
  },

  /**
   * 更新应用
   */
  updateApp(id: string, data: UpdateAppRequest): Promise<ApiResponse<Application>> {
    return request.put(`/v1/apps/${id}`, data)
  },

  /**
   * 删除应用
   */
  deleteApp(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/v1/apps/${id}`)
  },

  /**
   * 部署应用
   */
  deployApp(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/apps/${id}/deploy`)
  },

  /**
   * 停止应用
   */
  stopApp(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/apps/${id}/stop`)
  },

  /**
   * 启动应用
   */
  startApp(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/apps/${id}/start`)
  },

  /**
   * 重启应用
   */
  restartApp(id: string): Promise<ApiResponse<void>> {
    return request.post(`/v1/apps/${id}/restart`)
  },

  /**
   * 应用扩缩容
   */
  scaleApp(id: string, data: ScaleAppRequest): Promise<ApiResponse<void>> {
    return request.post(`/v1/apps/${id}/scale`, data)
  },

  /**
   * 获取应用实例列表
   */
  getAppInstances(id: string): Promise<ApiResponse<ApplicationInstance[]>> {
    return request.get(`/v1/apps/${id}/instances`)
  },

  /**
   * 应用实例操作
   */
  instanceAction(id: string, data: InstanceActionRequest): Promise<ApiResponse<void>> {
    return request.post(`/v1/apps/${id}/instances/action`, data)
  },

  /**
   * 获取应用日志
   */
  getAppLogs(id: string, params?: { 
    lines?: number
    since?: string
    follow?: boolean
  }): Promise<ApiResponse<{ logs: string }>> {
    return request.get(`/v1/apps/${id}/logs`, { params })
  },

  /**
   * 获取应用事件
   */
  getAppEvents(id: string): Promise<ApiResponse<any[]>> {
    return request.get(`/v1/apps/${id}/events`)
  },

  /**
   * 获取应用指标
   */
  getAppMetrics(id: string, params?: {
    start?: string
    end?: string
    step?: string
  }): Promise<ApiResponse<any>> {
    return request.get(`/v1/apps/${id}/metrics`, { params })
  },

  /**
   * 获取支持的运行时
   */
  getSupportedRuntimes(): Promise<ApiResponse<any>> {
    return request.get('/v1/apps/runtimes')
  }
}
