{"common": {"ok": "OK", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "save": "Save", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "refresh": "Refresh", "loading": "Loading...", "noData": "No Data", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "export": "Export", "import": "Import", "copy": "Copy", "paste": "Paste", "cut": "Cut", "select": "Select", "selectAll": "Select All", "deselect": "Deselect", "enable": "Enable", "disable": "Disable", "online": "Online", "offline": "Offline", "active": "Active", "inactive": "Inactive", "public": "Public", "private": "Private", "draft": "Draft", "published": "Published"}, "navigation": {"dashboard": "Dashboard", "applications": "Applications", "appList": "App List", "createApp": "Create App", "appTemplates": "App Templates", "cicd": "CI/CD", "pipelines": "Pipelines", "builds": "Build History", "deployments": "Deployments", "monitoring": "Monitoring", "systemMonitoring": "System Monitoring", "alertManagement": "Alert Management", "logViewer": "Log Viewer", "userManagement": "User Management", "userList": "User List", "rolePermissions": "Role Permissions", "teamManagement": "Team Management", "systemSettings": "System Settings", "securityCenter": "Security Center", "auditLogs": "<PERSON><PERSON>", "profile": "Profile", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "username": "Username", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "logoutSuccess": "Logout successful", "invalidCredentials": "Invalid username or password", "accountLocked": "Account is locked", "sessionExpired": "Session expired, please login again", "passwordTooWeak": "Password is too weak", "passwordMismatch": "Passwords do not match", "emailInvalid": "Invalid email format", "usernameRequired": "Username is required", "passwordRequired": "Password is required", "emailRequired": "Email is required"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back", "overview": "Overview", "statistics": "Statistics", "recentActivities": "Recent Activities", "quickActions": "Quick Actions", "systemStatus": "System Status", "resourceUsage": "Resource Usage", "alerts": "<PERSON><PERSON><PERSON>", "notifications": "Notifications", "totalApplications": "Total Applications", "runningApplications": "Running Applications", "totalUsers": "Total Users", "activeUsers": "Active Users", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "diskUsage": "Disk Usage", "networkTraffic": "Network Traffic"}, "applications": {"title": "Application Management", "createApplication": "Create Application", "applicationName": "Application Name", "applicationDescription": "Application Description", "applicationStatus": "Application Status", "applicationVersion": "Application Version", "applicationUrl": "Application URL", "applicationPort": "Application Port", "applicationEnvironment": "Application Environment", "applicationLanguage": "Programming Language", "applicationFramework": "Application Framework", "applicationDatabase": "Database", "applicationCache": "<PERSON><PERSON>", "applicationStorage": "Storage", "applicationLogs": "Application Logs", "applicationMetrics": "Application Metrics", "applicationHealth": "Health Status", "applicationInstances": "Instance Count", "applicationResources": "Resource Configuration", "applicationSecurity": "Security Configuration", "applicationBackup": "Backup Configuration", "applicationRestore": "Restore Configuration", "start": "Start", "stop": "Stop", "restart": "<PERSON><PERSON>", "deploy": "Deploy", "rollback": "Rollback", "scale": "Scale", "clone": "<PERSON><PERSON>", "archive": "Archive", "running": "Running", "stopped": "Stopped", "deploying": "Deploying", "failed": "Failed", "pending": "Pending", "healthy": "Healthy", "unhealthy": "Unhealthy", "unknown": "Unknown"}, "cicd": {"title": "CI/CD", "pipeline": "Pipeline", "createPipeline": "Create Pipeline", "pipelineName": "Pipeline Name", "pipelineDescription": "Pipeline Description", "pipelineStatus": "Pipeline Status", "pipelineStages": "Pipeline Stages", "pipelineHistory": "Pipeline History", "buildNumber": "Build Number", "buildStatus": "Build Status", "buildTime": "Build Time", "buildDuration": "Build Duration", "buildLogs": "Build Logs", "buildArtifacts": "Build Artifacts", "deploymentTarget": "Deployment Target", "deploymentStrategy": "Deployment Strategy", "deploymentHistory": "Deployment History", "rollbackDeployment": "Rollback Deployment", "triggerBuild": "Trigger Build", "cancelBuild": "Cancel Build", "retryBuild": "Retry Build", "viewLogs": "View Logs", "downloadArtifacts": "Download Artifacts", "success": "Success", "building": "Building", "cancelled": "Cancelled", "timeout": "Timeout", "skipped": "Skipped"}, "monitoring": {"title": "Monitoring & Alerts", "systemMonitoring": "System Monitoring", "applicationMonitoring": "Application Monitoring", "alertRules": "Alert <PERSON>", "alertHistory": "<PERSON><PERSON>", "metrics": "Metrics", "logs": "Logs", "traces": "Traces", "dashboards": "Dashboards", "createAlert": "Create <PERSON><PERSON>", "alertName": "Alert <PERSON>", "alertDescription": "<PERSON><PERSON>", "alertCondition": "<PERSON><PERSON>", "alertThreshold": "<PERSON><PERSON>", "alertSeverity": "<PERSON><PERSON>", "alertChannel": "Alert <PERSON>", "alertStatus": "Alert <PERSON>", "alertTime": "<PERSON><PERSON>", "resolveAlert": "Resolve <PERSON>", "acknowledgeAlert": "Acknowledge <PERSON>", "silenceAlert": "<PERSON>", "critical": "Critical", "high": "High", "medium": "Medium", "low": "Low", "resolved": "Resolved", "acknowledged": "Acknowledged", "silenced": "Silenced", "firing": "Firing"}, "users": {"title": "User Management", "createUser": "Create User", "editUser": "Edit User", "deleteUser": "Delete User", "userName": "Username", "userEmail": "User Email", "userRole": "User Role", "userStatus": "User Status", "userGroups": "User Groups", "userPermissions": "User Permissions", "lastLogin": "Last Login", "createdAt": "Created At", "updatedAt": "Updated At", "activateUser": "Activate User", "deactivateUser": "Deactivate User", "resetUserPassword": "Reset Password", "admin": "Administrator", "developer": "Developer", "operator": "Operator", "viewer": "Viewer", "guest": "Guest"}, "settings": {"title": "System Settings", "generalSettings": "General Settings", "securitySettings": "Security Settings", "notificationSettings": "Notification Settings", "integrationSettings": "Integration Settings", "backupSettings": "Backup Settings", "systemInfo": "System Information", "systemVersion": "System Version", "systemUptime": "System Uptime", "systemResources": "System Resources", "databaseInfo": "Database Information", "cacheInfo": "<PERSON><PERSON>", "storageInfo": "Storage Information", "networkInfo": "Network Information", "securityPolicy": "Security Policy", "accessControl": "Access Control", "auditSettings": "<PERSON>t Settings", "backupSchedule": "Backup Schedule", "restorePoint": "Restore Point", "maintenanceMode": "Maintenance Mode", "systemMaintenance": "System Maintenance", "performanceOptimization": "Performance Optimization", "resourceLimits": "Resource Limits", "quotaManagement": "Quota Management"}, "forms": {"required": "This field is required", "invalid": "Invalid format", "tooShort": "Input is too short", "tooLong": "Input is too long", "emailInvalid": "Invalid email format", "urlInvalid": "Invalid URL format", "numberInvalid": "Invalid number format", "dateInvalid": "Invalid date format", "phoneInvalid": "Invalid phone number format", "passwordWeak": "Password is too weak", "confirmPasswordMismatch": "Confirm password does not match", "fileTypeInvalid": "File type not supported", "fileSizeExceeded": "File size exceeds limit", "uploadFailed": "File upload failed", "uploadSuccess": "File upload successful"}, "messages": {"saveSuccess": "Save successful", "saveFailed": "Save failed", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "createSuccess": "Create successful", "createFailed": "Create failed", "updateSuccess": "Update successful", "updateFailed": "Update failed", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "networkError": "Network error", "serverError": "Server error", "permissionDenied": "Permission denied", "resourceNotFound": "Resource not found", "confirmDelete": "Are you sure you want to delete?", "confirmAction": "Are you sure you want to perform this action?", "unsavedChanges": "You have unsaved changes, are you sure you want to leave?", "sessionTimeout": "Session timeout, please login again", "maintenanceMode": "System is under maintenance, please try again later"}, "time": {"now": "Just now", "minutesAgo": "{count} minutes ago", "hoursAgo": "{count} hours ago", "daysAgo": "{count} days ago", "weeksAgo": "{count} weeks ago", "monthsAgo": "{count} months ago", "yearsAgo": "{count} years ago", "inMinutes": "in {count} minutes", "inHours": "in {count} hours", "inDays": "in {count} days", "inWeeks": "in {count} weeks", "inMonths": "in {count} months", "inYears": "in {count} years", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "nextWeek": "Next week", "thisMonth": "This month", "lastMonth": "Last month", "nextMonth": "Next month", "thisYear": "This year", "lastYear": "Last year", "nextYear": "Next year"}, "units": {"bytes": "Bytes", "kb": "KB", "mb": "MB", "gb": "GB", "tb": "TB", "seconds": "Seconds", "minutes": "Minutes", "hours": "Hours", "days": "Days", "weeks": "Weeks", "months": "Months", "years": "Years", "percent": "%", "count": "Count", "items": "Items", "records": "Records", "requests": "Requests", "responses": "Responses", "errors": "Errors", "warnings": "Warnings"}}