// 国际化配置
// 支持多语言切换和本地化

import { createI18n } from 'vue-i18n'
import type { App } from 'vue'

// 导入语言包
import zhCN from './locales/zh-CN.json'
import enUS from './locales/en-US.json'
import jaJP from './locales/ja-JP.json'

// 支持的语言列表
export const supportedLocales = [
  {
    code: 'zh-CN',
    name: '简体中文',
    flag: '🇨🇳',
    rtl: false,
  },
  {
    code: 'en-US',
    name: 'English',
    flag: '🇺🇸',
    rtl: false,
  },
  {
    code: 'ja-<PERSON>',
    name: '日本語',
    flag: '🇯🇵',
    rtl: false,
  },
] as const

export type SupportedLocale = typeof supportedLocales[number]['code']

// 默认语言
export const defaultLocale: SupportedLocale = 'zh-CN'

// 回退语言
export const fallbackLocale: SupportedLocale = 'en-US'

// 语言包
const messages = {
  'zh-CN': zhCN,
  'en-US': enUS,
  'ja-JP': jaJP,
}

// 获取浏览器语言
export function getBrowserLocale(): SupportedLocale {
  const browserLocale = navigator.language || navigator.languages[0]
  
  // 精确匹配
  for (const locale of supportedLocales) {
    if (browserLocale === locale.code) {
      return locale.code
    }
  }
  
  // 语言代码匹配（忽略地区）
  const languageCode = browserLocale.split('-')[0]
  for (const locale of supportedLocales) {
    if (locale.code.startsWith(languageCode)) {
      return locale.code
    }
  }
  
  return defaultLocale
}

// 获取存储的语言
export function getStoredLocale(): SupportedLocale | null {
  try {
    const stored = localStorage.getItem('paas-locale')
    if (stored && supportedLocales.some(locale => locale.code === stored)) {
      return stored as SupportedLocale
    }
  } catch (error) {
    console.warn('Failed to get stored locale:', error)
  }
  return null
}

// 存储语言设置
export function setStoredLocale(locale: SupportedLocale): void {
  try {
    localStorage.setItem('paas-locale', locale)
  } catch (error) {
    console.warn('Failed to store locale:', error)
  }
}

// 获取初始语言
export function getInitialLocale(): SupportedLocale {
  return getStoredLocale() || getBrowserLocale()
}

// 创建 i18n 实例
export const i18n = createI18n({
  legacy: false,
  locale: getInitialLocale(),
  fallbackLocale,
  messages,
  globalInjection: true,
  silentTranslationWarn: true,
  silentFallbackWarn: true,
})

// 语言切换函数
export function setLocale(locale: SupportedLocale): void {
  if (!supportedLocales.some(l => l.code === locale)) {
    console.warn(`Unsupported locale: ${locale}`)
    return
  }
  
  i18n.global.locale.value = locale
  setStoredLocale(locale)
  
  // 更新 HTML lang 属性
  document.documentElement.lang = locale
  
  // 更新页面方向（RTL/LTR）
  const localeInfo = supportedLocales.find(l => l.code === locale)
  if (localeInfo) {
    document.documentElement.dir = localeInfo.rtl ? 'rtl' : 'ltr'
  }
  
  // 触发自定义事件
  window.dispatchEvent(new CustomEvent('locale-changed', {
    detail: { locale, localeInfo }
  }))
}

// 获取当前语言信息
export function getCurrentLocaleInfo() {
  const currentLocale = i18n.global.locale.value as SupportedLocale
  return supportedLocales.find(locale => locale.code === currentLocale)
}

// 格式化数字
export function formatNumber(
  value: number,
  locale?: SupportedLocale,
  options?: Intl.NumberFormatOptions
): string {
  const currentLocale = locale || i18n.global.locale.value as SupportedLocale
  return new Intl.NumberFormat(currentLocale, options).format(value)
}

// 格式化货币
export function formatCurrency(
  value: number,
  currency: string = 'CNY',
  locale?: SupportedLocale
): string {
  const currentLocale = locale || i18n.global.locale.value as SupportedLocale
  return new Intl.NumberFormat(currentLocale, {
    style: 'currency',
    currency,
  }).format(value)
}

// 格式化日期
export function formatDate(
  date: Date | string | number,
  locale?: SupportedLocale,
  options?: Intl.DateTimeFormatOptions
): string {
  const currentLocale = locale || i18n.global.locale.value as SupportedLocale
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }
  
  return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(dateObj)
}

// 格式化相对时间
export function formatRelativeTime(
  date: Date | string | number,
  locale?: SupportedLocale
): string {
  const currentLocale = locale || i18n.global.locale.value as SupportedLocale
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
  
  const rtf = new Intl.RelativeTimeFormat(currentLocale, { numeric: 'auto' })
  
  if (Math.abs(diffInSeconds) < 60) {
    return rtf.format(-diffInSeconds, 'second')
  } else if (Math.abs(diffInSeconds) < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
  } else if (Math.abs(diffInSeconds) < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
  } else if (Math.abs(diffInSeconds) < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
  } else if (Math.abs(diffInSeconds) < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')
  }
}

// 复数形式处理
export function pluralize(
  count: number,
  singular: string,
  plural?: string,
  locale?: SupportedLocale
): string {
  const currentLocale = locale || i18n.global.locale.value as SupportedLocale
  const pr = new Intl.PluralRules(currentLocale)
  const rule = pr.select(count)
  
  if (rule === 'one') {
    return singular
  } else {
    return plural || `${singular}s`
  }
}

// 语言检测中间件
export function detectLocaleMiddleware() {
  const detectedLocale = getInitialLocale()
  if (detectedLocale !== i18n.global.locale.value) {
    setLocale(detectedLocale)
  }
}

// Vue 插件安装
export function setupI18n(app: App): void {
  app.use(i18n)
  
  // 设置初始语言
  const initialLocale = getInitialLocale()
  setLocale(initialLocale)
  
  // 全局属性
  app.config.globalProperties.$formatNumber = formatNumber
  app.config.globalProperties.$formatCurrency = formatCurrency
  app.config.globalProperties.$formatDate = formatDate
  app.config.globalProperties.$formatRelativeTime = formatRelativeTime
  app.config.globalProperties.$pluralize = pluralize
}

// 导出 composable
export { useI18n } from 'vue-i18n'

// 类型定义
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $formatNumber: typeof formatNumber
    $formatCurrency: typeof formatCurrency
    $formatDate: typeof formatDate
    $formatRelativeTime: typeof formatRelativeTime
    $pluralize: typeof pluralize
  }
}

export default i18n
