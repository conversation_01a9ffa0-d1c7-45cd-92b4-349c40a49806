// 通用类型定义

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  displayName: string
  avatar?: string
  status: 'active' | 'inactive' | 'suspended'
  roles: Role[]
  tenantId: string
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
}

export interface Role {
  id: string
  name: string
  displayName: string
  description: string
  permissions: Permission[]
  tenantId: string
  isSystem: boolean
  createdAt: string
  updatedAt: string
}

export interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description: string
}

// 租户类型
export interface Tenant {
  id: string
  name: string
  displayName: string
  description: string
  status: 'active' | 'inactive' | 'suspended'
  plan: string
  settings: Record<string, any>
  createdAt: string
  updatedAt: string
}

// 应用相关类型
export interface Application {
  id: string
  name: string
  displayName: string
  description: string
  status: 'creating' | 'building' | 'build_failed' | 'build_success' | 'deploying' | 'running' | 'stopped' | 'failed'
  runtime: {
    language: string
    version: string
    framework?: string
  }
  instances: ApplicationInstance[]
  resources: {
    cpu: string
    memory: string
    storage: string
  }
  environment: Record<string, string>
  healthCheck: {
    path: string
    port: number
    enabled: boolean
  }
  tenantId: string
  createdBy: string
  createdAt: string
  updatedAt: string
  lastDeployedAt?: string
}

export interface ApplicationInstance {
  id: string
  applicationId: string
  status: 'pending' | 'running' | 'stopped' | 'failed'
  containerId?: string
  port: number
  cpu: number
  memory: number
  startedAt?: string
  stoppedAt?: string
}

// CI/CD相关类型
export interface Pipeline {
  id: string
  name: string
  description: string
  repository: {
    url: string
    branch: string
    type: 'gitea' | 'github' | 'gitlab'
  }
  stages: PipelineStage[]
  triggers: PipelineTrigger[]
  status: 'active' | 'inactive'
  tenantId: string
  createdAt: string
  updatedAt: string
}

export interface PipelineStage {
  name: string
  type: 'build' | 'test' | 'deploy'
  commands: string[]
  environment: Record<string, string>
  timeout: number
}

export interface PipelineTrigger {
  type: 'webhook' | 'schedule' | 'manual'
  config: Record<string, any>
}

export interface Build {
  id: string
  pipelineId: string
  number: number
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled'
  branch: string
  commit: {
    hash: string
    message: string
    author: string
  }
  startedAt?: string
  finishedAt?: string
  duration?: number
  logs?: string
  artifacts?: BuildArtifact[]
}

export interface BuildArtifact {
  name: string
  path: string
  size: number
  type: string
}

// 配置管理类型
export interface Config {
  id: string
  key: string
  value: string
  type: 'string' | 'number' | 'boolean' | 'json' | 'secret'
  scope: 'global' | 'tenant' | 'application'
  scopeId: string
  environment: string
  description: string
  isSecret: boolean
  version: number
  createdAt: string
  updatedAt: string
}

// 监控相关类型
export interface MetricData {
  timestamp: number
  value: number
  labels?: Record<string, string>
}

export interface MonitoringMetrics {
  cpu: MetricData[]
  memory: MetricData[]
  network: MetricData[]
  disk: MetricData[]
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: 'blur' | 'change'
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 菜单项类型
export interface MenuItem {
  path: string
  name: string
  title: string
  icon?: string
  children?: MenuItem[]
  meta?: {
    requiresAuth?: boolean
    permission?: string
    hideInMenu?: boolean
  }
}

// 脚本执行相关类型
export interface ScriptTask {
  id: string
  app_id: string
  user_id: string
  tenant_id: string
  script_path: string
  runtime_type: string
  runtime_config: Record<string, any>
  parameters: Record<string, any>
  environment: Record<string, string>
  status: 'pending' | 'running' | 'completed' | 'failed' | 'canceled'
  priority: number
  timeout: number
  callback_url?: string
  started_at?: string
  finished_at?: string
  duration?: number
  exit_code?: number
  error_message?: string
  created_at: string
  updated_at: string
}

export interface ScriptTemplate {
  id: string
  name: string
  description: string
  category: string
  language: string
  version: string
  content: string
  parameters: Record<string, any>
  environment: Record<string, string>
  resources: Record<string, any>
  tags: string[]
  status: 'draft' | 'active' | 'deprecated'
  tenant_id: string
  created_by: string
  created_at: string
  updated_at: string
}

export interface TaskSchedule {
  id: string
  name: string
  description: string
  app_id: string
  script_path: string
  parameters: Record<string, any>
  environment: Record<string, string>
  cron_expr: string
  timezone: string
  status: 'active' | 'inactive' | 'paused'
  next_run?: string
  last_run?: string
  run_count: number
  tenant_id: string
  created_by: string
  created_at: string
  updated_at: string
}
