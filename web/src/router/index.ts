import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置进度条
NProgress.configure({ showSpinner: false })

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/auth/callback/:provider',
    name: 'AuthCallback',
    component: () => import('@/views/auth/CallbackView.vue'),
    meta: {
      title: '登录处理中',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/debug/login-test',
    name: 'LoginTest',
    component: () => import('@/views/debug/LoginTestView.vue'),
    meta: {
      title: '登录功能测试',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '控制台',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'DashboardHome',
        component: () => import('@/views/dashboard/DashboardView.vue'),
        meta: {
          title: '概览',
          icon: 'Dashboard'
        }
      }
    ]
  },
  {
    path: '/apps',
    name: 'Apps',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '应用管理',
      requiresAuth: true,
      icon: 'Box'
    },
    children: [
      {
        path: '',
        name: 'AppList',
        component: () => import('@/views/apps/AppListView.vue'),
        meta: {
          title: '应用列表',
          icon: 'List'
        }
      },
      {
        path: 'create',
        name: 'AppCreate',
        component: () => import('@/views/apps/AppCreateView.vue'),
        meta: {
          title: '创建应用',
          icon: 'Plus'
        }
      },
      {
        path: ':id',
        name: 'AppDetail',
        component: () => import('@/views/apps/AppDetailView.vue'),
        meta: {
          title: '应用详情',
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/cicd',
    name: 'CICD',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: 'CI/CD',
      requiresAuth: true,
      icon: 'Connection'
    },
    children: [
      {
        path: 'pipelines',
        name: 'Pipelines',
        component: () => import('@/views/cicd/PipelineListView.vue'),
        meta: {
          title: '流水线',
          icon: 'Share'
        }
      },
      {
        path: 'builds',
        name: 'Builds',
        component: () => import('@/views/cicd/BuildListView.vue'),
        meta: {
          title: '构建历史',
          icon: 'Tools'
        }
      },
      {
        path: 'deployments',
        name: 'Deployments',
        component: () => import('@/views/cicd/DeploymentListView.vue'),
        meta: {
          title: '部署管理',
          icon: 'Upload'
        }
      }
    ]
  },
  {
    path: '/config',
    name: 'Config',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '配置管理',
      requiresAuth: true,
      icon: 'Setting'
    },
    children: [
      {
        path: '',
        name: 'ConfigList',
        component: () => import('@/views/config/ConfigListView.vue'),
        meta: {
          title: '配置列表',
          icon: 'List'
        }
      },
      {
        path: 'secrets',
        name: 'Secrets',
        component: () => import('@/views/config/SecretListView.vue'),
        meta: {
          title: '密钥管理',
          icon: 'Key'
        }
      }
    ]
  },
  {
    path: '/monitor',
    name: 'Monitor',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '监控中心',
      requiresAuth: true,
      icon: 'Monitor'
    },
    children: [
      {
        path: '',
        name: 'MonitorOverview',
        component: () => import('@/views/monitor/MonitorOverview.vue'),
        meta: {
          title: '监控概览',
          icon: 'DataAnalysis'
        }
      },
      {
        path: 'logs',
        name: 'Logs',
        component: () => import('@/views/monitor/LogsView.vue'),
        meta: {
          title: '日志查看',
          icon: 'Document'
        }
      }
    ]
  },
  {
    path: '/scripts',
    name: 'Scripts',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '脚本执行',
      requiresAuth: true,
      icon: 'Cpu'
    },
    children: [
      {
        path: '',
        name: 'ScriptTasks',
        component: () => import('@/views/scripts/TaskListView.vue'),
        meta: {
          title: '任务管理',
          icon: 'List'
        }
      },
      {
        path: 'execute',
        name: 'ScriptExecute',
        component: () => import('@/views/scripts/ExecuteScriptView.vue'),
        meta: {
          title: '执行脚本',
          icon: 'VideoPlay'
        }
      },
      {
        path: 'tasks/:id',
        name: 'TaskDetail',
        component: () => import('@/views/scripts/TaskDetailView.vue'),
        meta: {
          title: '任务详情',
          hideInMenu: true
        }
      },
      {
        path: 'templates',
        name: 'ScriptTemplates',
        component: () => import('@/views/scripts/TemplateListView.vue'),
        meta: {
          title: '脚本模板',
          icon: 'Document'
        }
      },
      {
        path: 'templates/create',
        name: 'CreateTemplate',
        component: () => import('@/views/scripts/CreateTemplateView.vue'),
        meta: {
          title: '创建模板',
          hideInMenu: true
        }
      },
      {
        path: 'templates/:id',
        name: 'TemplateDetail',
        component: () => import('@/views/scripts/TemplateDetailView.vue'),
        meta: {
          title: '模板详情',
          hideInMenu: true
        }
      },
      {
        path: 'templates/:id/edit',
        name: 'EditTemplate',
        component: () => import('@/views/scripts/EditTemplateView.vue'),
        meta: {
          title: '编辑模板',
          hideInMenu: true
        }
      },
      {
        path: 'schedules',
        name: 'TaskSchedules',
        component: () => import('@/views/scripts/ScheduleListView.vue'),
        meta: {
          title: '任务调度',
          icon: 'Timer'
        }
      },
      {
        path: 'schedules/create',
        name: 'CreateSchedule',
        component: () => import('@/views/scripts/CreateScheduleView.vue'),
        meta: {
          title: '创建调度',
          hideInMenu: true
        }
      },
      {
        path: 'schedules/:id',
        name: 'ScheduleDetail',
        component: () => import('@/views/scripts/ScheduleDetailView.vue'),
        meta: {
          title: '调度详情',
          hideInMenu: true
        }
      },
      {
        path: 'schedules/:id/edit',
        name: 'EditSchedule',
        component: () => import('@/views/scripts/EditScheduleView.vue'),
        meta: {
          title: '编辑调度',
          hideInMenu: true
        }
      },
      {
        path: 'stats',
        name: 'ScriptStats',
        component: () => import('@/views/scripts/StatsView.vue'),
        meta: {
          title: '统计监控',
          icon: 'DataAnalysis'
        }
      }
    ]
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      icon: 'User',
      permission: 'user:manage'
    },
    children: [
      {
        path: '',
        name: 'UserList',
        component: () => import('@/views/users/UserListView.vue'),
        meta: {
          title: '用户列表',
          icon: 'UserFilled'
        }
      },
      {
        path: 'roles',
        name: 'Roles',
        component: () => import('@/views/users/RoleListView.vue'),
        meta: {
          title: '角色管理',
          icon: 'Avatar'
        }
      }
    ]
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      hideInMenu: true
    },
    children: [
      {
        path: '',
        name: 'UserProfile',
        component: () => import('@/views/profile/ProfileView.vue'),
        meta: {
          title: '个人信息'
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - PaaS 平台管理控制台`
  }
  
  // 检查认证状态
  if (requiresAuth && !userStore.isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }
  
  // 检查权限
  if (to.meta.permission && !userStore.hasPermission(to.meta.permission as string)) {
    // 没有权限访问该页面
    ElMessage.error('您没有权限访问该页面')
    next('/dashboard')
    return
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isAuthenticated) {
    next('/dashboard')
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
