// 国际化工具函数
// 提供高级的国际化功能和本地化支持

import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { 
  setLocale, 
  getCurrentLocaleInfo, 
  supportedLocales,
  formatNumber,
  formatCurrency,
  formatDate,
  formatRelativeTime,
  type SupportedLocale 
} from '@/i18n'

// 数字格式化选项
export interface NumberFormatOptions extends Intl.NumberFormatOptions {
  locale?: SupportedLocale
}

// 日期格式化选项
export interface DateFormatOptions extends Intl.DateTimeFormatOptions {
  locale?: SupportedLocale
  relative?: boolean
}

// 货币格式化选项
export interface CurrencyFormatOptions {
  locale?: SupportedLocale
  currency?: string
  display?: 'symbol' | 'code' | 'name'
}

// 复数规则
export interface PluralRules {
  zero?: string
  one?: string
  two?: string
  few?: string
  many?: string
  other: string
}

// 国际化工具 composable
export function useI18nUtils() {
  const { t, locale } = useI18n()
  
  // 当前语言信息
  const currentLocale = computed(() => locale.value as SupportedLocale)
  const currentLocaleInfo = computed(() => getCurrentLocaleInfo())
  const isRTL = computed(() => currentLocaleInfo.value?.rtl || false)
  
  // 语言切换
  const switchLocale = (newLocale: SupportedLocale) => {
    setLocale(newLocale)
  }
  
  // 获取支持的语言列表
  const getSupportedLocales = () => supportedLocales
  
  // 高级数字格式化
  const formatNumberAdvanced = (
    value: number,
    options: NumberFormatOptions = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    return formatNumber(value, locale, options)
  }
  
  // 百分比格式化
  const formatPercentage = (
    value: number,
    options: NumberFormatOptions = {}
  ): string => {
    return formatNumberAdvanced(value, {
      ...options,
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    })
  }
  
  // 文件大小格式化
  const formatFileSize = (
    bytes: number,
    options: NumberFormatOptions = {}
  ): string => {
    const units = ['bytes', 'kb', 'mb', 'gb', 'tb']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    const formattedSize = formatNumberAdvanced(size, {
      ...options,
      minimumFractionDigits: unitIndex > 0 ? 1 : 0,
      maximumFractionDigits: unitIndex > 0 ? 2 : 0,
    })
    
    return `${formattedSize} ${t(`units.${units[unitIndex]}`)}`
  }
  
  // 高级日期格式化
  const formatDateAdvanced = (
    date: Date | string | number,
    options: DateFormatOptions = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    
    if (options.relative) {
      return formatRelativeTime(date, locale)
    }
    
    return formatDate(date, locale, options)
  }
  
  // 智能日期格式化（根据时间距离选择格式）
  const formatDateSmart = (
    date: Date | string | number,
    options: DateFormatOptions = {}
  ): string => {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
    const now = new Date()
    const diffInHours = Math.abs(now.getTime() - dateObj.getTime()) / (1000 * 60 * 60)
    
    // 24小时内显示相对时间
    if (diffInHours < 24) {
      return formatDateAdvanced(date, { ...options, relative: true })
    }
    
    // 一周内显示星期几
    if (diffInHours < 24 * 7) {
      return formatDateAdvanced(date, {
        ...options,
        weekday: 'long',
        hour: '2-digit',
        minute: '2-digit',
      })
    }
    
    // 一年内显示月日
    if (diffInHours < 24 * 365) {
      return formatDateAdvanced(date, {
        ...options,
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    }
    
    // 超过一年显示完整日期
    return formatDateAdvanced(date, {
      ...options,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }
  
  // 高级货币格式化
  const formatCurrencyAdvanced = (
    value: number,
    options: CurrencyFormatOptions = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    const currency = options.currency || getCurrencyForLocale(locale)
    
    return formatCurrency(value, currency, locale)
  }
  
  // 根据语言环境获取默认货币
  const getCurrencyForLocale = (locale: SupportedLocale): string => {
    const currencyMap: Record<SupportedLocale, string> = {
      'zh-CN': 'CNY',
      'en-US': 'USD',
      'ja-JP': 'JPY',
    }
    return currencyMap[locale] || 'USD'
  }
  
  // 复数形式处理
  const formatPlural = (
    count: number,
    rules: PluralRules,
    options: { locale?: SupportedLocale } = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    const pr = new Intl.PluralRules(locale)
    const rule = pr.select(count)
    
    return rules[rule as keyof PluralRules] || rules.other
  }
  
  // 列表格式化
  const formatList = (
    items: string[],
    options: {
      style?: 'long' | 'short' | 'narrow'
      type?: 'conjunction' | 'disjunction' | 'unit'
      locale?: SupportedLocale
    } = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    
    try {
      const formatter = new Intl.ListFormat(locale, {
        style: options.style || 'long',
        type: options.type || 'conjunction',
      })
      return formatter.format(items)
    } catch (error) {
      // 降级处理
      return items.join(', ')
    }
  }
  
  // 时间段格式化
  const formatDuration = (
    milliseconds: number,
    options: {
      style?: 'long' | 'short' | 'narrow'
      units?: ('year' | 'month' | 'day' | 'hour' | 'minute' | 'second')[]
      locale?: SupportedLocale
    } = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    const style = options.style || 'long'
    const units = options.units || ['hour', 'minute', 'second']
    
    const durations = {
      year: 365 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000,
      hour: 60 * 60 * 1000,
      minute: 60 * 1000,
      second: 1000,
    }
    
    const parts: string[] = []
    let remaining = milliseconds
    
    for (const unit of units) {
      const duration = durations[unit]
      if (remaining >= duration) {
        const value = Math.floor(remaining / duration)
        remaining %= duration
        
        try {
          const formatter = new Intl.RelativeTimeFormat(locale, { style })
          // 注意：这里使用相对时间格式化器来获取单位名称
          // 实际项目中可能需要更复杂的实现
          parts.push(`${value} ${t(`units.${unit}${value !== 1 ? 's' : ''}`)}`)
        } catch (error) {
          parts.push(`${value} ${unit}${value !== 1 ? 's' : ''}`)
        }
      }
    }
    
    return parts.length > 0 ? formatList(parts, { locale }) : '0 ' + t('units.seconds')
  }
  
  // 地址格式化
  const formatAddress = (
    address: {
      street?: string
      city?: string
      state?: string
      country?: string
      postalCode?: string
    },
    options: { locale?: SupportedLocale } = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    
    // 根据不同地区的地址格式进行格式化
    const formatters: Record<string, (addr: typeof address) => string> = {
      'zh-CN': (addr) => [
        addr.country,
        addr.state,
        addr.city,
        addr.street,
        addr.postalCode,
      ].filter(Boolean).join(' '),
      
      'en-US': (addr) => [
        addr.street,
        addr.city,
        addr.state,
        addr.postalCode,
        addr.country,
      ].filter(Boolean).join(', '),
      
      'ja-JP': (addr) => [
        addr.country,
        addr.postalCode,
        addr.state,
        addr.city,
        addr.street,
      ].filter(Boolean).join(' '),
    }
    
    const formatter = formatters[locale] || formatters['en-US']
    return formatter(address)
  }
  
  // 电话号码格式化
  const formatPhoneNumber = (
    phoneNumber: string,
    options: { 
      locale?: SupportedLocale
      international?: boolean 
    } = {}
  ): string => {
    const locale = options.locale || currentLocale.value
    
    // 简单的电话号码格式化
    // 实际项目中可能需要使用专门的库如 libphonenumber
    const cleaned = phoneNumber.replace(/\D/g, '')
    
    const formatters: Record<string, (num: string) => string> = {
      'zh-CN': (num) => {
        if (num.length === 11) {
          return `${num.slice(0, 3)} ${num.slice(3, 7)} ${num.slice(7)}`
        }
        return num
      },
      'en-US': (num) => {
        if (num.length === 10) {
          return `(${num.slice(0, 3)}) ${num.slice(3, 6)}-${num.slice(6)}`
        } else if (num.length === 11 && num.startsWith('1')) {
          return `+1 (${num.slice(1, 4)}) ${num.slice(4, 7)}-${num.slice(7)}`
        }
        return num
      },
      'ja-JP': (num) => {
        if (num.length === 11) {
          return `${num.slice(0, 3)}-${num.slice(3, 7)}-${num.slice(7)}`
        }
        return num
      },
    }
    
    const formatter = formatters[locale] || formatters['en-US']
    return formatter(cleaned)
  }
  
  // 获取本地化的排序规则
  const getCollator = (options: Intl.CollatorOptions = {}) => {
    return new Intl.Collator(currentLocale.value, options)
  }
  
  // 本地化排序
  const sortLocalized = <T>(
    array: T[],
    keyExtractor: (item: T) => string,
    options: Intl.CollatorOptions = {}
  ): T[] => {
    const collator = getCollator(options)
    return [...array].sort((a, b) => 
      collator.compare(keyExtractor(a), keyExtractor(b))
    )
  }
  
  // 文本方向性检测
  const getTextDirection = (text: string): 'ltr' | 'rtl' => {
    // 简单的 RTL 检测
    const rtlChars = /[\u0590-\u083F]|[\u08A0-\u08FF]|[\uFB1D-\uFDFF]|[\uFE70-\uFEFF]/
    return rtlChars.test(text) ? 'rtl' : 'ltr'
  }
  
  // 获取本地化的搜索建议
  const getSearchSuggestions = (
    query: string,
    suggestions: string[],
    options: { maxSuggestions?: number } = {}
  ): string[] => {
    const maxSuggestions = options.maxSuggestions || 5
    const collator = getCollator({ sensitivity: 'base' })
    
    return suggestions
      .filter(suggestion => 
        collator.compare(suggestion.toLowerCase(), query.toLowerCase()) === 0 ||
        suggestion.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, maxSuggestions)
  }
  
  return {
    // 状态
    currentLocale,
    currentLocaleInfo,
    isRTL,
    
    // 语言切换
    switchLocale,
    getSupportedLocales,
    
    // 格式化方法
    formatNumberAdvanced,
    formatPercentage,
    formatFileSize,
    formatDateAdvanced,
    formatDateSmart,
    formatCurrencyAdvanced,
    formatPlural,
    formatList,
    formatDuration,
    formatAddress,
    formatPhoneNumber,
    
    // 工具方法
    getCollator,
    sortLocalized,
    getTextDirection,
    getSearchSuggestions,
    getCurrencyForLocale,
  }
}
