// 实时数据流管理
// 提供高性能的实时数据处理和展示优化

import { ref, computed, watch, onUnmounted } from 'vue'
import { useWebSocket } from './useWebSocket'
import { useDataCache } from './useDataCache'

// 数据流配置
export interface StreamConfig {
  endpoint: string
  bufferSize?: number
  updateInterval?: number
  enableCompression?: boolean
  enableBatching?: boolean
  batchSize?: number
  retryAttempts?: number
}

// 数据点接口
export interface DataPoint {
  timestamp: number
  value: number | string | object
  metadata?: Record<string, any>
}

// 数据流状态
export interface StreamState {
  isConnected: boolean
  isBuffering: boolean
  bufferSize: number
  dataRate: number
  lastUpdate: number
  errorCount: number
}

// 实时数据流管理器
export function useRealtimeStream(config: StreamConfig) {
  // 默认配置
  const defaultConfig: Required<StreamConfig> = {
    endpoint: config.endpoint,
    bufferSize: config.bufferSize || 1000,
    updateInterval: config.updateInterval || 100,
    enableCompression: config.enableCompression || false,
    enableBatching: config.enableBatching || true,
    batchSize: config.batchSize || 10,
    retryAttempts: config.retryAttempts || 3,
  }

  // 响应式状态
  const dataBuffer = ref<DataPoint[]>([])
  const latestData = ref<DataPoint | null>(null)
  const streamState = ref<StreamState>({
    isConnected: false,
    isBuffering: false,
    bufferSize: 0,
    dataRate: 0,
    lastUpdate: 0,
    errorCount: 0,
  })

  // WebSocket 连接
  const { connect, disconnect, onMessage, send, status } = useWebSocket({
    url: `ws://localhost:8080/ws/${config.endpoint}`,
    reconnectInterval: 3000,
    maxReconnectAttempts: defaultConfig.retryAttempts,
  })

  // 数据缓存
  const { set: cacheSet, get: cacheGet } = useDataCache()

  // 性能监控
  const performanceMetrics = ref({
    messagesReceived: 0,
    messagesProcessed: 0,
    averageProcessingTime: 0,
    maxProcessingTime: 0,
    minProcessingTime: Infinity,
  })

  // 数据处理队列
  const processingQueue = ref<DataPoint[]>([])
  let processingTimer: number | null = null
  let rateCalculationTimer: number | null = null

  // 计算属性
  const isConnected = computed(() => status.value === 'connected')
  const bufferUtilization = computed(() => 
    (dataBuffer.value.length / defaultConfig.bufferSize) * 100
  )
  const isBufferFull = computed(() => 
    dataBuffer.value.length >= defaultConfig.bufferSize
  )

  // 数据处理函数
  const processDataPoint = (data: any): DataPoint => {
    const startTime = performance.now()
    
    let processedData: DataPoint
    
    try {
      // 数据解压缩（如果启用）
      if (defaultConfig.enableCompression && data.compressed) {
        data = decompressData(data.data)
      }
      
      // 数据验证和转换
      processedData = {
        timestamp: data.timestamp || Date.now(),
        value: data.value,
        metadata: data.metadata || {},
      }
      
      // 更新性能指标
      const processingTime = performance.now() - startTime
      updatePerformanceMetrics(processingTime)
      
    } catch (error) {
      console.error('数据处理错误:', error)
      streamState.value.errorCount++
      throw error
    }
    
    return processedData
  }

  // 批量处理数据
  const processBatch = () => {
    if (processingQueue.value.length === 0) return

    const batchSize = Math.min(
      defaultConfig.batchSize,
      processingQueue.value.length
    )
    
    const batch = processingQueue.value.splice(0, batchSize)
    
    batch.forEach(dataPoint => {
      // 添加到缓冲区
      addToBuffer(dataPoint)
      
      // 更新最新数据
      latestData.value = dataPoint
      
      // 缓存数据
      cacheSet(`stream-${config.endpoint}-latest`, dataPoint, {
        ttl: 60000, // 1分钟
      })
    })

    // 更新流状态
    streamState.value.lastUpdate = Date.now()
    streamState.value.bufferSize = dataBuffer.value.length
    
    performanceMetrics.value.messagesProcessed += batch.length
  }

  // 添加数据到缓冲区
  const addToBuffer = (dataPoint: DataPoint) => {
    dataBuffer.value.push(dataPoint)
    
    // 如果缓冲区满了，移除最旧的数据
    if (dataBuffer.value.length > defaultConfig.bufferSize) {
      dataBuffer.value.shift()
    }
  }

  // 更新性能指标
  const updatePerformanceMetrics = (processingTime: number) => {
    const metrics = performanceMetrics.value
    
    metrics.averageProcessingTime = 
      (metrics.averageProcessingTime * metrics.messagesProcessed + processingTime) / 
      (metrics.messagesProcessed + 1)
    
    metrics.maxProcessingTime = Math.max(metrics.maxProcessingTime, processingTime)
    metrics.minProcessingTime = Math.min(metrics.minProcessingTime, processingTime)
  }

  // 数据解压缩
  const decompressData = (compressedData: string): any => {
    try {
      // 简单的 base64 解码（实际项目中可以使用更复杂的压缩算法）
      return JSON.parse(atob(compressedData))
    } catch (error) {
      console.error('数据解压缩失败:', error)
      throw error
    }
  }

  // 计算数据速率
  const calculateDataRate = () => {
    const now = Date.now()
    const timeWindow = 5000 // 5秒窗口
    
    const recentData = dataBuffer.value.filter(
      point => now - point.timestamp <= timeWindow
    )
    
    streamState.value.dataRate = recentData.length / (timeWindow / 1000)
  }

  // 启动数据流
  const startStream = async () => {
    try {
      await connect()
      
      // 监听数据消息
      onMessage('data', (message) => {
        performanceMetrics.value.messagesReceived++
        
        try {
          const dataPoint = processDataPoint(message.data)
          
          if (defaultConfig.enableBatching) {
            processingQueue.value.push(dataPoint)
          } else {
            addToBuffer(dataPoint)
            latestData.value = dataPoint
          }
        } catch (error) {
          console.error('数据处理失败:', error)
        }
      })

      // 启动批处理定时器
      if (defaultConfig.enableBatching) {
        processingTimer = window.setInterval(
          processBatch,
          defaultConfig.updateInterval
        )
      }

      // 启动速率计算定时器
      rateCalculationTimer = window.setInterval(
        calculateDataRate,
        1000
      )

      streamState.value.isConnected = true
      
    } catch (error) {
      console.error('启动数据流失败:', error)
      streamState.value.errorCount++
    }
  }

  // 停止数据流
  const stopStream = () => {
    disconnect()
    
    if (processingTimer) {
      clearInterval(processingTimer)
      processingTimer = null
    }
    
    if (rateCalculationTimer) {
      clearInterval(rateCalculationTimer)
      rateCalculationTimer = null
    }
    
    streamState.value.isConnected = false
  }

  // 清空缓冲区
  const clearBuffer = () => {
    dataBuffer.value = []
    processingQueue.value = []
    streamState.value.bufferSize = 0
  }

  // 获取历史数据
  const getHistoricalData = (timeRange: number): DataPoint[] => {
    const now = Date.now()
    return dataBuffer.value.filter(
      point => now - point.timestamp <= timeRange
    )
  }

  // 获取数据统计
  const getDataStatistics = () => {
    if (dataBuffer.value.length === 0) {
      return {
        count: 0,
        min: 0,
        max: 0,
        average: 0,
        latest: null,
      }
    }

    const numericValues = dataBuffer.value
      .map(point => point.value)
      .filter(value => typeof value === 'number') as number[]

    if (numericValues.length === 0) {
      return {
        count: dataBuffer.value.length,
        min: 0,
        max: 0,
        average: 0,
        latest: latestData.value,
      }
    }

    const min = Math.min(...numericValues)
    const max = Math.max(...numericValues)
    const average = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length

    return {
      count: dataBuffer.value.length,
      min,
      max,
      average,
      latest: latestData.value,
    }
  }

  // 发送控制命令
  const sendCommand = (command: string, params?: any) => {
    if (!isConnected.value) {
      throw new Error('数据流未连接')
    }

    send({
      type: 'command',
      data: {
        command,
        params,
        timestamp: Date.now(),
      },
    })
  }

  // 监听连接状态变化
  watch(status, (newStatus) => {
    streamState.value.isConnected = newStatus === 'connected'
    
    if (newStatus === 'error') {
      streamState.value.errorCount++
    }
  })

  // 组件卸载时清理
  onUnmounted(() => {
    stopStream()
  })

  return {
    // 状态
    dataBuffer: computed(() => dataBuffer.value),
    latestData: computed(() => latestData.value),
    streamState: computed(() => streamState.value),
    performanceMetrics: computed(() => performanceMetrics.value),
    isConnected,
    bufferUtilization,
    isBufferFull,

    // 方法
    startStream,
    stopStream,
    clearBuffer,
    getHistoricalData,
    getDataStatistics,
    sendCommand,

    // 工具方法
    processBatch,
    calculateDataRate,
  }
}

// 多流管理器
export class StreamManager {
  private streams = new Map<string, ReturnType<typeof useRealtimeStream>>()

  // 创建数据流
  createStream(id: string, config: StreamConfig) {
    if (this.streams.has(id)) {
      throw new Error(`数据流 ${id} 已存在`)
    }

    const stream = useRealtimeStream(config)
    this.streams.set(id, stream)
    return stream
  }

  // 获取数据流
  getStream(id: string) {
    return this.streams.get(id)
  }

  // 删除数据流
  removeStream(id: string) {
    const stream = this.streams.get(id)
    if (stream) {
      stream.stopStream()
      this.streams.delete(id)
    }
  }

  // 获取所有流的状态
  getAllStreamStates() {
    const states: Record<string, any> = {}
    this.streams.forEach((stream, id) => {
      states[id] = stream.streamState.value
    })
    return states
  }

  // 停止所有流
  stopAllStreams() {
    this.streams.forEach(stream => {
      stream.stopStream()
    })
  }

  // 清理所有流
  cleanup() {
    this.stopAllStreams()
    this.streams.clear()
  }
}

// 导出全局流管理器
export const globalStreamManager = new StreamManager()
