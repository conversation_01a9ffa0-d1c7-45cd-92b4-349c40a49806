// WebSocket 连接管理
// 提供实时数据通信、自动重连和消息处理

import { ref, computed, onUnmounted, nextTick } from 'vue'

// WebSocket 连接状态
export type WebSocketStatus = 'connecting' | 'connected' | 'disconnected' | 'error'

// 消息类型
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
  id?: string
}

// 连接配置
export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  timeout?: number
}

// 事件处理器
export type MessageHandler = (message: WebSocketMessage) => void
export type StatusHandler = (status: WebSocketStatus) => void

// WebSocket 管理器
export function useWebSocket(config: WebSocketConfig) {
  // 响应式状态
  const status = ref<WebSocketStatus>('disconnected')
  const isConnected = computed(() => status.value === 'connected')
  const lastMessage = ref<WebSocketMessage | null>(null)
  const messageHistory = ref<WebSocketMessage[]>([])
  const reconnectAttempts = ref(0)
  const latency = ref(0)
  
  // 内部状态
  let ws: WebSocket | null = null
  let reconnectTimer: number | null = null
  let heartbeatTimer: number | null = null
  let pingStartTime = 0
  
  // 事件处理器
  const messageHandlers = new Map<string, Set<MessageHandler>>()
  const statusHandlers = new Set<StatusHandler>()
  
  // 默认配置
  const defaultConfig: Required<WebSocketConfig> = {
    url: config.url,
    protocols: config.protocols || [],
    reconnectInterval: config.reconnectInterval || 3000,
    maxReconnectAttempts: config.maxReconnectAttempts || 10,
    heartbeatInterval: config.heartbeatInterval || 30000,
    timeout: config.timeout || 10000,
  }
  
  // 连接 WebSocket
  const connect = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        resolve()
        return
      }
      
      status.value = 'connecting'
      notifyStatusHandlers('connecting')
      
      try {
        ws = new WebSocket(defaultConfig.url, defaultConfig.protocols)
        
        // 连接超时处理
        const timeoutId = setTimeout(() => {
          if (ws && ws.readyState === WebSocket.CONNECTING) {
            ws.close()
            status.value = 'error'
            notifyStatusHandlers('error')
            reject(new Error('Connection timeout'))
          }
        }, defaultConfig.timeout)
        
        ws.onopen = () => {
          clearTimeout(timeoutId)
          status.value = 'connected'
          reconnectAttempts.value = 0
          notifyStatusHandlers('connected')
          startHeartbeat()
          resolve()
        }
        
        ws.onmessage = (event) => {
          handleMessage(event.data)
        }
        
        ws.onclose = (event) => {
          clearTimeout(timeoutId)
          stopHeartbeat()
          
          if (event.wasClean) {
            status.value = 'disconnected'
            notifyStatusHandlers('disconnected')
          } else {
            status.value = 'error'
            notifyStatusHandlers('error')
            scheduleReconnect()
          }
        }
        
        ws.onerror = () => {
          clearTimeout(timeoutId)
          status.value = 'error'
          notifyStatusHandlers('error')
        }
        
      } catch (error) {
        status.value = 'error'
        notifyStatusHandlers('error')
        reject(error)
      }
    })
  }
  
  // 断开连接
  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    stopHeartbeat()
    
    if (ws) {
      ws.close(1000, 'Manual disconnect')
      ws = null
    }
    
    status.value = 'disconnected'
    notifyStatusHandlers('disconnected')
  }
  
  // 发送消息
  const send = (message: Omit<WebSocketMessage, 'timestamp'>) => {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected')
    }
    
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: Date.now(),
    }
    
    ws.send(JSON.stringify(fullMessage))
  }
  
  // 处理接收到的消息
  const handleMessage = (data: string) => {
    try {
      const message: WebSocketMessage = JSON.parse(data)
      
      // 处理心跳响应
      if (message.type === 'pong') {
        latency.value = Date.now() - pingStartTime
        return
      }
      
      lastMessage.value = message
      
      // 添加到历史记录
      messageHistory.value.push(message)
      
      // 限制历史记录长度
      if (messageHistory.value.length > 1000) {
        messageHistory.value = messageHistory.value.slice(-500)
      }
      
      // 通知消息处理器
      const handlers = messageHandlers.get(message.type)
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message)
          } catch (error) {
            console.error('Error in message handler:', error)
          }
        })
      }
      
      // 通知通用处理器
      const allHandlers = messageHandlers.get('*')
      if (allHandlers) {
        allHandlers.forEach(handler => {
          try {
            handler(message)
          } catch (error) {
            console.error('Error in universal message handler:', error)
          }
        })
      }
      
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }
  
  // 注册消息处理器
  const onMessage = (type: string, handler: MessageHandler) => {
    if (!messageHandlers.has(type)) {
      messageHandlers.set(type, new Set())
    }
    messageHandlers.get(type)!.add(handler)
    
    // 返回取消注册函数
    return () => {
      const handlers = messageHandlers.get(type)
      if (handlers) {
        handlers.delete(handler)
        if (handlers.size === 0) {
          messageHandlers.delete(type)
        }
      }
    }
  }
  
  // 注册状态处理器
  const onStatusChange = (handler: StatusHandler) => {
    statusHandlers.add(handler)
    
    // 返回取消注册函数
    return () => {
      statusHandlers.delete(handler)
    }
  }
  
  // 通知状态处理器
  const notifyStatusHandlers = (newStatus: WebSocketStatus) => {
    statusHandlers.forEach(handler => {
      try {
        handler(newStatus)
      } catch (error) {
        console.error('Error in status handler:', error)
      }
    })
  }
  
  // 自动重连
  const scheduleReconnect = () => {
    if (reconnectAttempts.value >= defaultConfig.maxReconnectAttempts) {
      console.warn('Max reconnect attempts reached')
      return
    }
    
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
    }
    
    const delay = Math.min(
      defaultConfig.reconnectInterval * Math.pow(2, reconnectAttempts.value),
      30000
    )
    
    reconnectTimer = window.setTimeout(() => {
      reconnectAttempts.value++
      connect().catch(() => {
        // 重连失败，会自动再次调度重连
      })
    }, delay)
  }
  
  // 心跳检测
  const startHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
    }
    
    heartbeatTimer = window.setInterval(() => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        pingStartTime = Date.now()
        send({ type: 'ping', data: null })
      }
    }, defaultConfig.heartbeatInterval)
  }
  
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }
  
  // 获取连接信息
  const getConnectionInfo = () => {
    return {
      status: status.value,
      url: defaultConfig.url,
      protocols: defaultConfig.protocols,
      reconnectAttempts: reconnectAttempts.value,
      latency: latency.value,
      messageCount: messageHistory.value.length,
    }
  }
  
  // 清理历史记录
  const clearHistory = () => {
    messageHistory.value = []
    lastMessage.value = null
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    // 状态
    status: computed(() => status.value),
    isConnected,
    lastMessage: computed(() => lastMessage.value),
    messageHistory: computed(() => messageHistory.value),
    reconnectAttempts: computed(() => reconnectAttempts.value),
    latency: computed(() => latency.value),
    
    // 方法
    connect,
    disconnect,
    send,
    onMessage,
    onStatusChange,
    getConnectionInfo,
    clearHistory,
  }
}

// 全局 WebSocket 管理器
class WebSocketManager {
  private connections = new Map<string, ReturnType<typeof useWebSocket>>()
  
  // 创建或获取连接
  getConnection(name: string, config: WebSocketConfig) {
    if (!this.connections.has(name)) {
      this.connections.set(name, useWebSocket(config))
    }
    return this.connections.get(name)!
  }
  
  // 关闭连接
  closeConnection(name: string) {
    const connection = this.connections.get(name)
    if (connection) {
      connection.disconnect()
      this.connections.delete(name)
    }
  }
  
  // 关闭所有连接
  closeAllConnections() {
    this.connections.forEach(connection => {
      connection.disconnect()
    })
    this.connections.clear()
  }
  
  // 获取所有连接状态
  getAllConnectionStatus() {
    const status: Record<string, any> = {}
    this.connections.forEach((connection, name) => {
      status[name] = connection.getConnectionInfo()
    })
    return status
  }
}

// 导出全局管理器实例
export const wsManager = new WebSocketManager()

// 便捷的 composable
export function useRealtimeData(endpoint: string) {
  const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/${endpoint}`
  
  return useWebSocket({
    url: wsUrl,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 30000,
  })
}
