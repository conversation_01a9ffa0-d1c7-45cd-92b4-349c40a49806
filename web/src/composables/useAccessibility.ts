// 无障碍支持工具
// 提供键盘导航、屏幕阅读器支持和无障碍功能

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// 焦点管理
export function useFocusManagement() {
  const focusableElements = ref<HTMLElement[]>([])
  const currentFocusIndex = ref(-1)
  
  // 获取可聚焦元素的选择器
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
  ].join(', ')
  
  // 更新可聚焦元素列表
  const updateFocusableElements = (container?: HTMLElement) => {
    const root = container || document.body
    const elements = Array.from(root.querySelectorAll(focusableSelectors)) as HTMLElement[]
    
    // 过滤掉不可见的元素
    focusableElements.value = elements.filter(el => {
      const style = window.getComputedStyle(el)
      return (
        style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        style.opacity !== '0' &&
        el.offsetWidth > 0 &&
        el.offsetHeight > 0
      )
    })
  }
  
  // 聚焦到指定索引的元素
  const focusElementAt = (index: number) => {
    if (index >= 0 && index < focusableElements.value.length) {
      currentFocusIndex.value = index
      focusableElements.value[index].focus()
    }
  }
  
  // 聚焦到下一个元素
  const focusNext = () => {
    const nextIndex = (currentFocusIndex.value + 1) % focusableElements.value.length
    focusElementAt(nextIndex)
  }
  
  // 聚焦到上一个元素
  const focusPrevious = () => {
    const prevIndex = currentFocusIndex.value <= 0 
      ? focusableElements.value.length - 1 
      : currentFocusIndex.value - 1
    focusElementAt(prevIndex)
  }
  
  // 聚焦到第一个元素
  const focusFirst = () => {
    focusElementAt(0)
  }
  
  // 聚焦到最后一个元素
  const focusLast = () => {
    focusElementAt(focusableElements.value.length - 1)
  }
  
  // 陷阱焦点在容器内
  const trapFocus = (container: HTMLElement) => {
    updateFocusableElements(container)
    
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        event.preventDefault()
        
        if (event.shiftKey) {
          focusPrevious()
        } else {
          focusNext()
        }
      }
    }
    
    container.addEventListener('keydown', handleKeydown)
    
    // 聚焦到第一个元素
    if (focusableElements.value.length > 0) {
      focusFirst()
    }
    
    return () => {
      container.removeEventListener('keydown', handleKeydown)
    }
  }
  
  return {
    focusableElements: computed(() => focusableElements.value),
    currentFocusIndex: computed(() => currentFocusIndex.value),
    updateFocusableElements,
    focusElementAt,
    focusNext,
    focusPrevious,
    focusFirst,
    focusLast,
    trapFocus,
  }
}

// 键盘导航
export function useKeyboardNavigation() {
  const keyHandlers = ref<Map<string, (event: KeyboardEvent) => void>>(new Map())
  
  // 注册键盘快捷键
  const registerKey = (key: string, handler: (event: KeyboardEvent) => void) => {
    keyHandlers.value.set(key.toLowerCase(), handler)
  }
  
  // 注销键盘快捷键
  const unregisterKey = (key: string) => {
    keyHandlers.value.delete(key.toLowerCase())
  }
  
  // 处理键盘事件
  const handleKeydown = (event: KeyboardEvent) => {
    const key = event.key.toLowerCase()
    const handler = keyHandlers.value.get(key)
    
    if (handler) {
      handler(event)
    }
  }
  
  onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
  })
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
  
  return {
    registerKey,
    unregisterKey,
  }
}

// 屏幕阅读器支持
export function useScreenReader() {
  const announcements = ref<string[]>([])
  
  // 创建屏幕阅读器公告区域
  const createAnnouncementRegion = () => {
    const existing = document.getElementById('sr-announcements')
    if (existing) return existing
    
    const region = document.createElement('div')
    region.id = 'sr-announcements'
    region.setAttribute('aria-live', 'polite')
    region.setAttribute('aria-atomic', 'true')
    region.style.position = 'absolute'
    region.style.left = '-10000px'
    region.style.width = '1px'
    region.style.height = '1px'
    region.style.overflow = 'hidden'
    
    document.body.appendChild(region)
    return region
  }
  
  // 向屏幕阅读器宣布消息
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const region = createAnnouncementRegion()
    region.setAttribute('aria-live', priority)
    
    // 清空然后设置新消息，确保屏幕阅读器能够读取
    region.textContent = ''
    
    nextTick(() => {
      region.textContent = message
      announcements.value.push(message)
      
      // 限制公告历史记录
      if (announcements.value.length > 10) {
        announcements.value.shift()
      }
    })
  }
  
  // 宣布页面变化
  const announcePageChange = (title: string) => {
    announce(`页面已切换到 ${title}`, 'polite')
  }
  
  // 宣布操作结果
  const announceAction = (action: string, result: 'success' | 'error' | 'info') => {
    const resultText = {
      success: '成功',
      error: '失败',
      info: '完成',
    }[result]
    
    announce(`${action} ${resultText}`, result === 'error' ? 'assertive' : 'polite')
  }
  
  return {
    announcements: computed(() => announcements.value),
    announce,
    announcePageChange,
    announceAction,
  }
}

// ARIA 属性管理
export function useAria() {
  // 设置 ARIA 标签
  const setAriaLabel = (element: HTMLElement, label: string) => {
    element.setAttribute('aria-label', label)
  }
  
  // 设置 ARIA 描述
  const setAriaDescription = (element: HTMLElement, description: string) => {
    const descId = `desc-${Math.random().toString(36).substr(2, 9)}`
    
    // 创建描述元素
    const descElement = document.createElement('div')
    descElement.id = descId
    descElement.textContent = description
    descElement.style.display = 'none'
    
    document.body.appendChild(descElement)
    element.setAttribute('aria-describedby', descId)
    
    return () => {
      document.body.removeChild(descElement)
      element.removeAttribute('aria-describedby')
    }
  }
  
  // 设置 ARIA 展开状态
  const setAriaExpanded = (element: HTMLElement, expanded: boolean) => {
    element.setAttribute('aria-expanded', expanded.toString())
  }
  
  // 设置 ARIA 选中状态
  const setAriaSelected = (element: HTMLElement, selected: boolean) => {
    element.setAttribute('aria-selected', selected.toString())
  }
  
  // 设置 ARIA 禁用状态
  const setAriaDisabled = (element: HTMLElement, disabled: boolean) => {
    element.setAttribute('aria-disabled', disabled.toString())
  }
  
  // 设置 ARIA 隐藏状态
  const setAriaHidden = (element: HTMLElement, hidden: boolean) => {
    element.setAttribute('aria-hidden', hidden.toString())
  }
  
  // 设置 ARIA 实时区域
  const setAriaLive = (element: HTMLElement, live: 'off' | 'polite' | 'assertive') => {
    element.setAttribute('aria-live', live)
  }
  
  return {
    setAriaLabel,
    setAriaDescription,
    setAriaExpanded,
    setAriaSelected,
    setAriaDisabled,
    setAriaHidden,
    setAriaLive,
  }
}

// 颜色对比度检查
export function useColorContrast() {
  // 计算相对亮度
  const getRelativeLuminance = (color: string): number => {
    const rgb = hexToRgb(color)
    if (!rgb) return 0
    
    const { r, g, b } = rgb
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
  }
  
  // 十六进制颜色转 RGB
  const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : null
  }
  
  // 计算对比度
  const getContrastRatio = (color1: string, color2: string): number => {
    const l1 = getRelativeLuminance(color1)
    const l2 = getRelativeLuminance(color2)
    
    const lighter = Math.max(l1, l2)
    const darker = Math.min(l1, l2)
    
    return (lighter + 0.05) / (darker + 0.05)
  }
  
  // 检查对比度是否符合 WCAG 标准
  const checkContrastCompliance = (
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA',
    size: 'normal' | 'large' = 'normal'
  ): { ratio: number; compliant: boolean } => {
    const ratio = getContrastRatio(foreground, background)
    
    const thresholds = {
      AA: { normal: 4.5, large: 3 },
      AAA: { normal: 7, large: 4.5 },
    }
    
    const threshold = thresholds[level][size]
    
    return {
      ratio,
      compliant: ratio >= threshold,
    }
  }
  
  return {
    getContrastRatio,
    checkContrastCompliance,
  }
}

// 减少动画偏好检测
export function useReducedMotion() {
  const prefersReducedMotion = ref(false)
  
  const updatePreference = () => {
    prefersReducedMotion.value = window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }
  
  onMounted(() => {
    updatePreference()
    
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    mediaQuery.addEventListener('change', updatePreference)
    
    onUnmounted(() => {
      mediaQuery.removeEventListener('change', updatePreference)
    })
  })
  
  return {
    prefersReducedMotion: computed(() => prefersReducedMotion.value),
  }
}

// 高对比度偏好检测
export function useHighContrast() {
  const prefersHighContrast = ref(false)
  
  const updatePreference = () => {
    prefersHighContrast.value = window.matchMedia('(prefers-contrast: high)').matches
  }
  
  onMounted(() => {
    updatePreference()
    
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    mediaQuery.addEventListener('change', updatePreference)
    
    onUnmounted(() => {
      mediaQuery.removeEventListener('change', updatePreference)
    })
  })
  
  return {
    prefersHighContrast: computed(() => prefersHighContrast.value),
  }
}

// 综合无障碍工具
export function useAccessibility() {
  const focusManagement = useFocusManagement()
  const keyboardNavigation = useKeyboardNavigation()
  const screenReader = useScreenReader()
  const aria = useAria()
  const colorContrast = useColorContrast()
  const reducedMotion = useReducedMotion()
  const highContrast = useHighContrast()
  
  // 初始化无障碍功能
  const initAccessibility = () => {
    // 设置页面语言
    if (!document.documentElement.lang) {
      document.documentElement.lang = 'zh-CN'
    }
    
    // 添加跳转到主内容的链接
    addSkipLink()
    
    // 注册常用键盘快捷键
    registerCommonShortcuts()
  }
  
  // 添加跳转到主内容的链接
  const addSkipLink = () => {
    const existing = document.getElementById('skip-to-main')
    if (existing) return
    
    const skipLink = document.createElement('a')
    skipLink.id = 'skip-to-main'
    skipLink.href = '#main-content'
    skipLink.textContent = '跳转到主内容'
    skipLink.className = 'skip-link'
    
    // 样式
    const style = document.createElement('style')
    style.textContent = `
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        z-index: 9999;
        border-radius: 4px;
      }
      .skip-link:focus {
        top: 6px;
      }
    `
    
    document.head.appendChild(style)
    document.body.insertBefore(skipLink, document.body.firstChild)
  }
  
  // 注册常用键盘快捷键
  const registerCommonShortcuts = () => {
    // Alt + M: 跳转到主内容
    keyboardNavigation.registerKey('m', (event) => {
      if (event.altKey) {
        event.preventDefault()
        const mainContent = document.getElementById('main-content')
        if (mainContent) {
          mainContent.focus()
          screenReader.announce('已跳转到主内容')
        }
      }
    })
    
    // Alt + N: 跳转到导航
    keyboardNavigation.registerKey('n', (event) => {
      if (event.altKey) {
        event.preventDefault()
        const navigation = document.querySelector('nav') || document.querySelector('[role="navigation"]')
        if (navigation) {
          (navigation as HTMLElement).focus()
          screenReader.announce('已跳转到导航')
        }
      }
    })
    
    // Escape: 关闭模态框或下拉菜单
    keyboardNavigation.registerKey('escape', (event) => {
      const activeModal = document.querySelector('[role="dialog"][aria-hidden="false"]')
      const activeDropdown = document.querySelector('[aria-expanded="true"]')
      
      if (activeModal) {
        event.preventDefault()
        // 触发关闭事件
        activeModal.dispatchEvent(new CustomEvent('close'))
        screenReader.announce('已关闭对话框')
      } else if (activeDropdown) {
        event.preventDefault()
        // 触发关闭事件
        activeDropdown.dispatchEvent(new CustomEvent('close'))
        screenReader.announce('已关闭下拉菜单')
      }
    })
  }
  
  return {
    ...focusManagement,
    ...keyboardNavigation,
    ...screenReader,
    ...aria,
    ...colorContrast,
    ...reducedMotion,
    ...highContrast,
    initAccessibility,
  }
}
