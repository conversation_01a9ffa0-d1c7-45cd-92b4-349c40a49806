// 数据缓存管理
// 提供智能缓存、数据同步和性能优化

import { ref, computed, watch, onUnmounted } from 'vue'

// 缓存项接口
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number
  key: string
  version: number
  tags: string[]
}

// 缓存配置
export interface CacheConfig {
  defaultTTL?: number
  maxSize?: number
  enablePersistence?: boolean
  storageKey?: string
  enableCompression?: boolean
}

// 缓存统计
export interface CacheStats {
  hits: number
  misses: number
  size: number
  hitRate: number
}

// 数据缓存管理器
export function useDataCache(config: CacheConfig = {}) {
  // 默认配置
  const defaultConfig: Required<CacheConfig> = {
    defaultTTL: 5 * 60 * 1000, // 5分钟
    maxSize: 1000,
    enablePersistence: true,
    storageKey: 'paas-data-cache',
    enableCompression: false,
  }
  
  const finalConfig = { ...defaultConfig, ...config }
  
  // 缓存存储
  const cache = ref<Map<string, CacheItem>>(new Map())
  const stats = ref<CacheStats>({
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0,
  })
  
  // 计算属性
  const cacheSize = computed(() => cache.value.size)
  const hitRate = computed(() => {
    const total = stats.value.hits + stats.value.misses
    return total > 0 ? (stats.value.hits / total) * 100 : 0
  })
  
  // 更新统计信息
  const updateStats = () => {
    stats.value.size = cache.value.size
    stats.value.hitRate = hitRate.value
  }
  
  // 生成缓存键
  const generateKey = (key: string, params?: Record<string, any>): string => {
    if (!params) return key
    
    const paramString = Object.keys(params)
      .sort()
      .map(k => `${k}=${JSON.stringify(params[k])}`)
      .join('&')
    
    return `${key}?${paramString}`
  }
  
  // 检查缓存项是否过期
  const isExpired = (item: CacheItem): boolean => {
    return Date.now() - item.timestamp > item.ttl
  }
  
  // 清理过期项
  const cleanupExpired = () => {
    const now = Date.now()
    const toDelete: string[] = []
    
    cache.value.forEach((item, key) => {
      if (now - item.timestamp > item.ttl) {
        toDelete.push(key)
      }
    })
    
    toDelete.forEach(key => {
      cache.value.delete(key)
    })
    
    updateStats()
  }
  
  // 强制清理最旧的项
  const evictOldest = () => {
    if (cache.value.size === 0) return
    
    let oldestKey = ''
    let oldestTime = Date.now()
    
    cache.value.forEach((item, key) => {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp
        oldestKey = key
      }
    })
    
    if (oldestKey) {
      cache.value.delete(oldestKey)
    }
  }
  
  // 设置缓存
  const set = <T>(
    key: string,
    data: T,
    options: {
      ttl?: number
      tags?: string[]
      version?: number
      params?: Record<string, any>
    } = {}
  ): void => {
    const finalKey = generateKey(key, options.params)
    
    // 检查缓存大小限制
    if (cache.value.size >= finalConfig.maxSize && !cache.value.has(finalKey)) {
      cleanupExpired()
      
      // 如果清理后仍然超出限制，强制删除最旧的项
      if (cache.value.size >= finalConfig.maxSize) {
        evictOldest()
      }
    }
    
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: options.ttl || finalConfig.defaultTTL,
      key: finalKey,
      version: options.version || 1,
      tags: options.tags || [],
    }
    
    cache.value.set(finalKey, item)
    updateStats()
    
    // 持久化到本地存储
    if (finalConfig.enablePersistence) {
      persistCache()
    }
  }
  
  // 获取缓存
  const get = <T>(
    key: string,
    params?: Record<string, any>
  ): T | null => {
    const finalKey = generateKey(key, params)
    const item = cache.value.get(finalKey) as CacheItem<T> | undefined
    
    if (!item) {
      stats.value.misses++
      updateStats()
      return null
    }
    
    if (isExpired(item)) {
      cache.value.delete(finalKey)
      stats.value.misses++
      updateStats()
      return null
    }
    
    stats.value.hits++
    updateStats()
    return item.data
  }
  
  // 检查缓存是否存在且有效
  const has = (key: string, params?: Record<string, any>): boolean => {
    const finalKey = generateKey(key, params)
    const item = cache.value.get(finalKey)
    
    if (!item) return false
    
    if (isExpired(item)) {
      cache.value.delete(finalKey)
      updateStats()
      return false
    }
    
    return true
  }
  
  // 删除缓存
  const remove = (key: string, params?: Record<string, any>): boolean => {
    const finalKey = generateKey(key, params)
    const deleted = cache.value.delete(finalKey)
    
    if (deleted) {
      updateStats()
      
      if (finalConfig.enablePersistence) {
        persistCache()
      }
    }
    
    return deleted
  }
  
  // 根据标签删除缓存
  const removeByTag = (tag: string): number => {
    let removed = 0
    const toDelete: string[] = []
    
    cache.value.forEach((item, key) => {
      if (item.tags.includes(tag)) {
        toDelete.push(key)
      }
    })
    
    toDelete.forEach(key => {
      cache.value.delete(key)
      removed++
    })
    
    if (removed > 0) {
      updateStats()
      
      if (finalConfig.enablePersistence) {
        persistCache()
      }
    }
    
    return removed
  }
  
  // 根据模式删除缓存
  const removeByPattern = (pattern: RegExp): number => {
    let removed = 0
    const toDelete: string[] = []
    
    cache.value.forEach((item, key) => {
      if (pattern.test(key)) {
        toDelete.push(key)
      }
    })
    
    toDelete.forEach(key => {
      cache.value.delete(key)
      removed++
    })
    
    if (removed > 0) {
      updateStats()
      
      if (finalConfig.enablePersistence) {
        persistCache()
      }
    }
    
    return removed
  }
  
  // 清空缓存
  const clear = (): void => {
    cache.value.clear()
    stats.value = {
      hits: 0,
      misses: 0,
      size: 0,
      hitRate: 0,
    }
    
    if (finalConfig.enablePersistence) {
      try {
        localStorage.removeItem(finalConfig.storageKey)
      } catch (error) {
        console.warn('Failed to clear persisted cache:', error)
      }
    }
  }
  
  // 获取所有键
  const keys = (): string[] => {
    return Array.from(cache.value.keys())
  }
  
  // 获取缓存统计
  const getStats = (): CacheStats => {
    return { ...stats.value }
  }
  
  // 持久化缓存到本地存储
  const persistCache = (): void => {
    if (!finalConfig.enablePersistence) return
    
    try {
      const data = Array.from(cache.value.entries())
      const serialized = JSON.stringify(data)
      
      if (finalConfig.enableCompression) {
        // 简单的压缩（实际项目中可以使用更好的压缩算法）
        localStorage.setItem(finalConfig.storageKey, btoa(serialized))
      } else {
        localStorage.setItem(finalConfig.storageKey, serialized)
      }
    } catch (error) {
      console.warn('Failed to persist cache:', error)
    }
  }
  
  // 从本地存储恢复缓存
  const restoreCache = (): void => {
    if (!finalConfig.enablePersistence) return
    
    try {
      const stored = localStorage.getItem(finalConfig.storageKey)
      if (!stored) return
      
      let serialized = stored
      if (finalConfig.enableCompression) {
        serialized = atob(stored)
      }
      
      const data = JSON.parse(serialized) as [string, CacheItem][]
      const now = Date.now()
      
      // 只恢复未过期的项
      data.forEach(([key, item]) => {
        if (now - item.timestamp <= item.ttl) {
          cache.value.set(key, item)
        }
      })
      
      updateStats()
    } catch (error) {
      console.warn('Failed to restore cache:', error)
    }
  }
  
  // 缓存装饰器
  const cached = <T extends (...args: any[]) => any>(
    fn: T,
    options: {
      key?: string
      ttl?: number
      tags?: string[]
      keyGenerator?: (...args: Parameters<T>) => string
    } = {}
  ) => {
    return ((...args: Parameters<T>): ReturnType<T> => {
      const cacheKey = options.keyGenerator 
        ? options.keyGenerator(...args)
        : options.key || `${fn.name}_${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cached = get<ReturnType<T>>(cacheKey)
      if (cached !== null) {
        return cached
      }
      
      // 执行函数并缓存结果
      const result = fn(...args)
      
      // 如果是 Promise，等待结果后缓存
      if (result instanceof Promise) {
        return result.then(data => {
          set(cacheKey, data, {
            ttl: options.ttl,
            tags: options.tags,
          })
          return data
        }) as ReturnType<T>
      } else {
        set(cacheKey, result, {
          ttl: options.ttl,
          tags: options.tags,
        })
        return result
      }
    }) as T
  }
  
  // 定期清理过期项
  const cleanupInterval = setInterval(cleanupExpired, 60000) // 每分钟清理一次
  
  // 初始化时恢复缓存
  restoreCache()
  
  // 组件卸载时清理
  onUnmounted(() => {
    clearInterval(cleanupInterval)
    
    if (finalConfig.enablePersistence) {
      persistCache()
    }
  })
  
  return {
    // 状态
    cacheSize,
    hitRate,
    stats: computed(() => stats.value),
    
    // 方法
    set,
    get,
    has,
    remove,
    removeByTag,
    removeByPattern,
    clear,
    keys,
    getStats,
    cached,
    
    // 工具方法
    cleanupExpired,
    persistCache,
    restoreCache,
  }
}

// 全局缓存实例
export const globalCache = useDataCache({
  defaultTTL: 10 * 60 * 1000, // 10分钟
  maxSize: 2000,
  enablePersistence: true,
  storageKey: 'paas-global-cache',
})
