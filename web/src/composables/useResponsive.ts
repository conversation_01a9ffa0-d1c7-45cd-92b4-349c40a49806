// 响应式设计工具
// 提供断点检测、设备类型判断和响应式状态管理

import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点定义
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof breakpoints

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// 屏幕方向
export type Orientation = 'portrait' | 'landscape'

// 响应式状态
interface ResponsiveState {
  width: number
  height: number
  currentBreakpoint: Breakpoint
  deviceType: DeviceType
  orientation: Orientation
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouch: boolean
}

// 全局响应式状态
const state = ref<ResponsiveState>({
  width: 0,
  height: 0,
  currentBreakpoint: 'xs',
  deviceType: 'desktop',
  orientation: 'landscape',
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  isTouch: false,
})

// 更新状态
const updateState = () => {
  const width = window.innerWidth
  const height = window.innerHeight
  
  // 确定当前断点
  let currentBreakpoint: Breakpoint = 'xs'
  for (const [bp, minWidth] of Object.entries(breakpoints).reverse()) {
    if (width >= minWidth) {
      currentBreakpoint = bp as Breakpoint
      break
    }
  }
  
  // 确定设备类型
  let deviceType: DeviceType = 'desktop'
  if (width < breakpoints.md) {
    deviceType = 'mobile'
  } else if (width < breakpoints.lg) {
    deviceType = 'tablet'
  }
  
  // 确定屏幕方向
  const orientation: Orientation = width > height ? 'landscape' : 'portrait'
  
  // 检测触摸设备
  const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  state.value = {
    width,
    height,
    currentBreakpoint,
    deviceType,
    orientation,
    isMobile: deviceType === 'mobile',
    isTablet: deviceType === 'tablet',
    isDesktop: deviceType === 'desktop',
    isTouch,
  }
}

// 监听器管理
let resizeListener: (() => void) | null = null
let orientationListener: (() => void) | null = null

// 初始化响应式监听
const initResponsive = () => {
  if (typeof window === 'undefined') return
  
  updateState()
  
  // 防抖处理
  let timeoutId: number | null = null
  resizeListener = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = window.setTimeout(updateState, 100)
  }
  
  orientationListener = () => {
    // 延迟更新，等待屏幕旋转完成
    setTimeout(updateState, 200)
  }
  
  window.addEventListener('resize', resizeListener)
  window.addEventListener('orientationchange', orientationListener)
}

// 清理监听器
const cleanupResponsive = () => {
  if (resizeListener) {
    window.removeEventListener('resize', resizeListener)
    resizeListener = null
  }
  
  if (orientationListener) {
    window.removeEventListener('orientationchange', orientationListener)
    orientationListener = null
  }
}

// 主要的响应式 composable
export function useResponsive() {
  onMounted(() => {
    if (!resizeListener) {
      initResponsive()
    }
  })
  
  onUnmounted(() => {
    // 注意：不要在这里清理全局监听器，因为可能有其他组件在使用
  })
  
  // 断点检测方法
  const isBreakpoint = (bp: Breakpoint) => {
    return computed(() => state.value.currentBreakpoint === bp)
  }
  
  const isBreakpointOrLarger = (bp: Breakpoint) => {
    return computed(() => state.value.width >= breakpoints[bp])
  }
  
  const isBreakpointOrSmaller = (bp: Breakpoint) => {
    return computed(() => state.value.width <= breakpoints[bp])
  }
  
  const isBetweenBreakpoints = (min: Breakpoint, max: Breakpoint) => {
    return computed(() => 
      state.value.width >= breakpoints[min] && 
      state.value.width < breakpoints[max]
    )
  }
  
  // 设备类型检测
  const isMobile = computed(() => state.value.isMobile)
  const isTablet = computed(() => state.value.isTablet)
  const isDesktop = computed(() => state.value.isDesktop)
  const isTouch = computed(() => state.value.isTouch)
  
  // 屏幕方向检测
  const isPortrait = computed(() => state.value.orientation === 'portrait')
  const isLandscape = computed(() => state.value.orientation === 'landscape')
  
  // 屏幕尺寸
  const screenWidth = computed(() => state.value.width)
  const screenHeight = computed(() => state.value.height)
  const currentBreakpoint = computed(() => state.value.currentBreakpoint)
  const deviceType = computed(() => state.value.deviceType)
  
  return {
    // 状态
    screenWidth,
    screenHeight,
    currentBreakpoint,
    deviceType,
    
    // 设备类型
    isMobile,
    isTablet,
    isDesktop,
    isTouch,
    
    // 屏幕方向
    isPortrait,
    isLandscape,
    
    // 断点检测方法
    isBreakpoint,
    isBreakpointOrLarger,
    isBreakpointOrSmaller,
    isBetweenBreakpoints,
    
    // 便捷方法
    isXs: isBreakpoint('xs'),
    isSm: isBreakpoint('sm'),
    isMd: isBreakpoint('md'),
    isLg: isBreakpoint('lg'),
    isXl: isBreakpoint('xl'),
    is2xl: isBreakpoint('2xl'),
    
    isSmOrLarger: isBreakpointOrLarger('sm'),
    isMdOrLarger: isBreakpointOrLarger('md'),
    isLgOrLarger: isBreakpointOrLarger('lg'),
    isXlOrLarger: isBreakpointOrLarger('xl'),
    
    isSmOrSmaller: isBreakpointOrSmaller('sm'),
    isMdOrSmaller: isBreakpointOrSmaller('md'),
    isLgOrSmaller: isBreakpointOrSmaller('lg'),
    isXlOrSmaller: isBreakpointOrSmaller('xl'),
  }
}

// 媒体查询 composable
export function useMediaQuery(query: string) {
  const matches = ref(false)
  
  onMounted(() => {
    if (typeof window === 'undefined') return
    
    const mediaQuery = window.matchMedia(query)
    matches.value = mediaQuery.matches
    
    const handler = (e: MediaQueryListEvent) => {
      matches.value = e.matches
    }
    
    mediaQuery.addEventListener('change', handler)
    
    onUnmounted(() => {
      mediaQuery.removeEventListener('change', handler)
    })
  })
  
  return matches
}

// 视口尺寸 composable
export function useViewport() {
  const { screenWidth, screenHeight } = useResponsive()
  
  // 视口单位计算
  const vw = computed(() => screenWidth.value / 100)
  const vh = computed(() => screenHeight.value / 100)
  const vmin = computed(() => Math.min(vw.value, vh.value))
  const vmax = computed(() => Math.max(vw.value, vh.value))
  
  return {
    width: screenWidth,
    height: screenHeight,
    vw,
    vh,
    vmin,
    vmax,
  }
}

// 安全区域 composable (用于处理刘海屏等)
export function useSafeArea() {
  const safeAreaInsets = ref({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  })
  
  onMounted(() => {
    if (typeof window === 'undefined') return
    
    const updateSafeArea = () => {
      const computedStyle = getComputedStyle(document.documentElement)
      
      safeAreaInsets.value = {
        top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
        right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0'),
        bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0'),
      }
    }
    
    updateSafeArea()
    
    // 监听方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(updateSafeArea, 200)
    })
  })
  
  return {
    safeAreaInsets,
    hasSafeArea: computed(() => 
      Object.values(safeAreaInsets.value).some(inset => inset > 0)
    ),
  }
}

// 初始化全局响应式系统
if (typeof window !== 'undefined') {
  initResponsive()
}

// 清理函数（在应用卸载时调用）
export const cleanupResponsiveSystem = cleanupResponsive
