import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import * as authUtils from '@/utils/auth'

// Mock API 和工具函数
vi.mock('@/api/auth')
vi.mock('@/utils/auth')

const mockAuthApi = vi.mocked(authApi)
const mockAuthUtils = vi.mocked(authUtils)

/**
 * 用户状态管理 Store 单元测试
 * 测试用户登录、登出、权限检查等核心功能
 */
describe('useUserStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('登录功能', () => {
    it('应该成功登录并保存用户信息', async () => {
      const mockUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        displayName: '测试用户',
        roles: [
          {
            id: '1',
            name: 'user',
            displayName: '普通用户',
            description: '普通用户角色',
            permissions: [],
            tenantId: '1',
            isSystem: false,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }
        ],
        tenantId: '1',
        status: 'active' as const,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      const mockLoginResponse = {
        code: 0,
        message: 'success',
        data: {
          access_token: 'mock-token',
          refresh_token: 'mock-refresh-token',
          token_type: 'Bearer',
          expires_in: 3600,
          user: mockUser
        }
      }

      const mockPermissionsResponse = {
        code: 0,
        message: 'success',
        data: [
          { id: '1', name: 'app:read', resource: 'app', action: 'read', description: '读取应用' }
        ]
      }

      mockAuthApi.login.mockResolvedValue(mockLoginResponse)
      mockAuthApi.getUserPermissions.mockResolvedValue(mockPermissionsResponse)

      const userStore = useUserStore()
      
      await userStore.login({
        username: 'testuser',
        password: 'password123'
      })

      expect(userStore.user).toEqual(mockUser)
      expect(userStore.token).toBe('mock-token')
      expect(userStore.isAuthenticated).toBe(true)
      expect(userStore.permissions).toEqual(['app:read'])
      expect(mockAuthUtils.setToken).toHaveBeenCalledWith('mock-token')
    })

    it('应该处理登录失败', async () => {
      const mockError = new Error('登录失败')
      mockAuthApi.login.mockRejectedValue(mockError)

      const userStore = useUserStore()
      
      await expect(userStore.login({
        username: 'testuser',
        password: 'wrongpassword'
      })).rejects.toThrow('登录失败')

      expect(userStore.user).toBeNull()
      expect(userStore.token).toBe('')
      expect(userStore.isAuthenticated).toBe(false)
    })
  })

  describe('登出功能', () => {
    it('应该成功登出并清除用户数据', async () => {
      mockAuthApi.logout.mockResolvedValue({ code: 0, message: 'success', data: undefined })

      const userStore = useUserStore()
      
      // 先设置一些用户数据
      userStore.user = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        displayName: '测试用户',
        roles: [],
        tenantId: '1',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
      userStore.token = 'mock-token'

      await userStore.logout()

      expect(userStore.user).toBeNull()
      expect(userStore.token).toBe('')
      expect(userStore.isAuthenticated).toBe(false)
      expect(mockAuthUtils.removeToken).toHaveBeenCalled()
    })

    it('应该在登出API失败时仍然清除本地数据', async () => {
      mockAuthApi.logout.mockRejectedValue(new Error('网络错误'))

      const userStore = useUserStore()
      
      // 先设置一些用户数据
      userStore.user = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        displayName: '测试用户',
        roles: [],
        tenantId: '1',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
      userStore.token = 'mock-token'

      await userStore.logout()

      expect(userStore.user).toBeNull()
      expect(userStore.token).toBe('')
      expect(userStore.isAuthenticated).toBe(false)
      expect(mockAuthUtils.removeToken).toHaveBeenCalled()
    })
  })

  describe('权限检查', () => {
    it('应该正确检查用户权限', () => {
      const userStore = useUserStore()
      
      userStore.permissions = ['app:read', 'app:write', 'user:read']

      expect(userStore.hasPermission('app:read')).toBe(true)
      expect(userStore.hasPermission('app:write')).toBe(true)
      expect(userStore.hasPermission('app:delete')).toBe(false)
      expect(userStore.hasPermission('user:read')).toBe(true)
    })

    it('管理员应该拥有所有权限', () => {
      const userStore = useUserStore()
      
      userStore.user = {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        displayName: '管理员',
        roles: [
          {
            id: '1',
            name: 'admin',
            displayName: '管理员',
            description: '系统管理员',
            permissions: [],
            tenantId: '1',
            isSystem: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }
        ],
        tenantId: '1',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      expect(userStore.hasPermission('any:permission')).toBe(true)
      expect(userStore.isAdmin).toBe(true)
    })

    it('应该正确检查用户角色', () => {
      const userStore = useUserStore()
      
      userStore.user = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        displayName: '测试用户',
        roles: [
          {
            id: '1',
            name: 'user',
            displayName: '普通用户',
            description: '普通用户角色',
            permissions: [],
            tenantId: '1',
            isSystem: false,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          },
          {
            id: '2',
            name: 'developer',
            displayName: '开发者',
            description: '开发者角色',
            permissions: [],
            tenantId: '1',
            isSystem: false,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }
        ],
        tenantId: '1',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      expect(userStore.hasRole('user')).toBe(true)
      expect(userStore.hasRole('developer')).toBe(true)
      expect(userStore.hasRole('admin')).toBe(false)
    })
  })

  describe('token刷新', () => {
    it('应该成功刷新token', async () => {
      const mockRefreshResponse = {
        code: 0,
        message: 'success',
        data: {
          access_token: 'new-mock-token'
        }
      }

      mockAuthApi.refreshToken.mockResolvedValue(mockRefreshResponse)

      const userStore = useUserStore()
      userStore.token = 'old-token'

      await userStore.refreshToken()

      expect(userStore.token).toBe('new-mock-token')
      expect(mockAuthUtils.setToken).toHaveBeenCalledWith('new-mock-token')
    })

    it('应该在刷新失败时清除用户数据', async () => {
      mockAuthApi.refreshToken.mockRejectedValue(new Error('刷新失败'))

      const userStore = useUserStore()
      userStore.token = 'old-token'
      userStore.user = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        displayName: '测试用户',
        roles: [],
        tenantId: '1',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      await expect(userStore.refreshToken()).rejects.toThrow('刷新失败')

      expect(userStore.user).toBeNull()
      expect(userStore.token).toBe('')
      expect(mockAuthUtils.removeToken).toHaveBeenCalled()
    })
  })

  describe('初始化认证状态', () => {
    it('应该从本地存储恢复用户状态', async () => {
      const mockUser = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        displayName: '测试用户',
        roles: [],
        tenantId: '1',
        status: 'active' as const,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }

      const mockUserResponse = {
        code: 0,
        message: 'success',
        data: mockUser
      }

      const mockPermissionsResponse = {
        code: 0,
        message: 'success',
        data: []
      }

      mockAuthUtils.getToken.mockReturnValue('saved-token')
      mockAuthApi.getCurrentUser.mockResolvedValue(mockUserResponse)
      mockAuthApi.getUserPermissions.mockResolvedValue(mockPermissionsResponse)

      const userStore = useUserStore()
      
      await userStore.initializeAuth()

      expect(userStore.token).toBe('saved-token')
      expect(userStore.user).toEqual(mockUser)
      expect(userStore.isAuthenticated).toBe(true)
    })

    it('应该在没有保存的token时不执行任何操作', async () => {
      mockAuthUtils.getToken.mockReturnValue(undefined)

      const userStore = useUserStore()
      
      await userStore.initializeAuth()

      expect(userStore.token).toBe('')
      expect(userStore.user).toBeNull()
      expect(mockAuthApi.getCurrentUser).not.toHaveBeenCalled()
    })

    it('应该在获取用户信息失败时清除数据', async () => {
      mockAuthUtils.getToken.mockReturnValue('invalid-token')
      mockAuthApi.getCurrentUser.mockRejectedValue(new Error('无效token'))

      const userStore = useUserStore()
      
      await userStore.initializeAuth()

      expect(userStore.token).toBe('')
      expect(userStore.user).toBeNull()
      expect(mockAuthUtils.removeToken).toHaveBeenCalled()
    })
  })
})
