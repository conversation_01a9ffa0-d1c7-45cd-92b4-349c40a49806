import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, Role, Permission } from '@/types'
import { authApi } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { devModeUtils } from '@/utils/dev-mode'

/**
 * 用户状态管理Store
 * 管理用户登录状态、用户信息、权限等
 */
export const useUserStore = defineStore('user', () => {
  // 状态定义
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRoles = computed(() => user.value?.roles || [])
  const isAdmin = computed(() => 
    userRoles.value.some(role => role.name === 'admin' || role.name === 'super_admin')
  )
  
  /**
   * 用户登录
   * @param credentials 登录凭据
   */
  const login = async (credentials: { username: string; password: string }) => {
    try {
      // 🔧 检测开发模式
      const devConfig = await devModeUtils.detect()

      // 🔓 开发模式处理：如果后端禁用了认证，使用开发模式登录
      if (devConfig.devMode && !devConfig.authEnabled) {
        console.log('🔧 使用开发模式登录')
        devModeUtils.showDevModeWarning()

        // 创建开发模式登录响应
        const devResponse = devModeUtils.createDevModeLoginResponse(credentials.username)

        // 保存开发模式token和用户信息
        token.value = devResponse.access_token
        user.value = devResponse.user
        setToken(devResponse.access_token)

        // 开发模式拥有所有权限
        permissions.value = ['*']

        console.log('✅ 开发模式登录成功:', devResponse.user)
        return { data: devResponse }
      }

      // 🔒 正常登录流程
      const response = await authApi.login(credentials)
      const { access_token, user: userInfo } = response.data

      // 保存token和用户信息
      token.value = access_token
      user.value = userInfo
      setToken(access_token)

      // 获取用户权限
      await fetchUserPermissions()

      return response
    } catch (error) {
      console.error('登录失败:', error)

      // 🔧 开发模式错误处理：如果是网络错误且在开发环境，尝试开发模式登录
      if (import.meta.env.DEV && isNetworkError(error)) {
        console.warn('🔧 检测到网络错误，尝试开发模式登录')

        try {
          const devResponse = devModeUtils.createDevModeLoginResponse(credentials.username)

          token.value = devResponse.access_token
          user.value = devResponse.user
          setToken(devResponse.access_token)
          permissions.value = ['*']

          console.log('✅ 开发模式备用登录成功')
          devModeUtils.showDevModeWarning()

          return { data: devResponse }
        } catch (devError) {
          console.error('开发模式登录也失败:', devError)
        }
      }

      throw error
    }
  }
  
  /**
   * 用户登出
   */
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      clearUserData()
    }
  }
  
  /**
   * 检查是否为开发模式token
   */
  const isDevModeToken = (token: string): boolean => {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) return false

      const payload = JSON.parse(atob(parts[1]))
      return payload.dev_mode === true
    } catch {
      return false
    }
  }

  /**
   * 刷新token
   */
  const refreshToken = async () => {
    try {
      // 🔧 开发模式处理：如果当前是开发模式token，直接返回成功
      const currentToken = token.value
      if (currentToken && isDevModeToken(currentToken)) {
        console.log('🔧 开发模式token无需刷新')
        return { data: { access_token: currentToken } }
      }

      console.log('🔄 开始刷新token...')
      const response = await authApi.refreshToken()
      const { access_token } = response.data

      token.value = access_token
      setToken(access_token)

      console.log('✅ Token刷新成功')
      return response
    } catch (error) {
      console.error('刷新token失败:', error)

      // 🔧 开发模式错误处理：如果是网络错误且在开发环境，生成新的开发模式token
      if (import.meta.env.DEV && isNetworkError(error)) {
        console.warn('🔧 开发环境网络错误，生成新的开发模式token')
        const devResponse = devModeUtils.createDevModeLoginResponse(user.value?.username || '开发者')
        const devToken = devResponse.access_token

        token.value = devToken
        setToken(devToken)
        return { data: { access_token: devToken } }
      }

      // 刷新失败，清除用户数据
      clearUserData()
      throw error
    }
  }
  
  /**
   * 获取用户权限列表
   */
  const fetchUserPermissions = async () => {
    if (!user.value) return
    
    try {
      const response = await authApi.getUserPermissions(user.value.id)
      permissions.value = response.data.map((perm: Permission) => 
        `${perm.resource}:${perm.action}`
      )
    } catch (error) {
      console.error('获取用户权限失败:', error)
    }
  }
  
  /**
   * 检查用户是否有指定权限
   * @param permission 权限字符串，格式：resource:action
   */
  const hasPermission = (permission: string): boolean => {
    if (isAdmin.value) return true
    return permissions.value.includes(permission)
  }
  
  /**
   * 检查用户是否有指定角色
   * @param roleName 角色名称
   */
  const hasRole = (roleName: string): boolean => {
    return userRoles.value.some(role => role.name === roleName)
  }
  
  /**
   * 更新用户信息
   * @param userInfo 用户信息
   */
  const updateUserInfo = (userInfo: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userInfo }
    }
  }
  
  /**
   * 清除用户数据
   */
  const clearUserData = () => {
    user.value = null
    token.value = ''
    permissions.value = []
    removeToken()
  }
  
  /**
   * 初始化认证状态
   * 从本地存储恢复用户登录状态
   */
  const initializeAuth = async () => {
    const savedToken = getToken()
    if (!savedToken) return
    
    try {
      token.value = savedToken
      // 获取当前用户信息
      const response = await authApi.getCurrentUser()
      user.value = response.data
      
      // 获取用户权限
      await fetchUserPermissions()
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      clearUserData()
    }
  }
  
  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    permissions: readonly(permissions),

    // 计算属性
    isAuthenticated,
    userRoles,
    isAdmin,

    // 方法
    login,
    logout,
    refreshToken,
    fetchUserPermissions,
    hasPermission,
    hasRole,
    updateUserInfo,
    clearUserData,
    initializeAuth
  }
})

/**
 * 检查是否为网络错误
 * @param error 错误对象
 */
function isNetworkError(error: any): boolean {
  // 检查常见的网络错误
  if (!error) return false

  // Axios网络错误
  if (error.code === 'NETWORK_ERROR' || error.code === 'ERR_NETWORK') {
    return true
  }

  // 连接被拒绝
  if (error.message && error.message.includes('ECONNREFUSED')) {
    return true
  }

  // 超时错误
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return true
  }

  // 无响应
  if (!error.response) {
    return true
  }

  return false
}
