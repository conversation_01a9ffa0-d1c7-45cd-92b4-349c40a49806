import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { scriptsApi } from '@/api/scripts'
import type { 
  ScriptTask, 
  ScriptTemplate, 
  TaskSchedule, 
  StatsOverview,
  MetricPoint,
  TaskQueryParams,
  TemplateQueryParams,
  ScheduleQueryParams,
  MetricsQueryParams
} from '@/api/scripts'

export const useScriptsStore = defineStore('scripts', () => {
  // 任务相关状态
  const tasks = ref<ScriptTask[]>([])
  const currentTask = ref<ScriptTask | null>(null)
  const taskLoading = ref(false)
  const taskPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 模板相关状态
  const templates = ref<ScriptTemplate[]>([])
  const currentTemplate = ref<ScriptTemplate | null>(null)
  const templateLoading = ref(false)
  const templatePagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 调度相关状态
  const schedules = ref<TaskSchedule[]>([])
  const currentSchedule = ref<TaskSchedule | null>(null)
  const scheduleLoading = ref(false)
  const schedulePagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 统计相关状态
  const statsOverview = ref<StatsOverview | null>(null)
  const metrics = ref<MetricPoint[]>([])
  const statsLoading = ref(false)

  // 计算属性
  const runningTasks = computed(() => 
    tasks.value.filter(task => task.status === 'running')
  )

  const completedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'completed')
  )

  const failedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'failed')
  )

  const activeTemplates = computed(() => 
    templates.value.filter(template => template.status === 'active')
  )

  const activeSchedules = computed(() => 
    schedules.value.filter(schedule => schedule.status === 'active')
  )

  // 任务相关操作
  const fetchTasks = async (params?: TaskQueryParams) => {
    try {
      taskLoading.value = true
      const response = await scriptsApi.getTasks(params)
      tasks.value = response.data.items
      taskPagination.value = {
        page: response.data.pagination.page,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.total
      }
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw error
    } finally {
      taskLoading.value = false
    }
  }

  const fetchTaskById = async (taskId: string) => {
    try {
      const response = await scriptsApi.getTaskStatus(taskId)
      currentTask.value = response.data
      
      // 更新任务列表中的对应项
      const index = tasks.value.findIndex(task => task.id === taskId)
      if (index !== -1) {
        tasks.value[index] = response.data
      }
      
      return response.data
    } catch (error) {
      console.error('获取任务详情失败:', error)
      throw error
    }
  }

  const executeScript = async (data: any) => {
    try {
      const response = await scriptsApi.executeScript(data)
      // 刷新任务列表
      await fetchTasks()
      return response.data
    } catch (error) {
      console.error('执行脚本失败:', error)
      throw error
    }
  }

  const cancelTask = async (taskId: string) => {
    try {
      await scriptsApi.cancelTask(taskId)
      // 更新任务状态
      await fetchTaskById(taskId)
    } catch (error) {
      console.error('取消任务失败:', error)
      throw error
    }
  }

  // 模板相关操作
  const fetchTemplates = async (params?: TemplateQueryParams) => {
    try {
      templateLoading.value = true
      const response = await scriptsApi.getTemplates(params)
      templates.value = response.data.items
      templatePagination.value = {
        page: response.data.pagination.page,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.total
      }
    } catch (error) {
      console.error('获取模板列表失败:', error)
      throw error
    } finally {
      templateLoading.value = false
    }
  }

  const fetchTemplateById = async (templateId: string) => {
    try {
      const response = await scriptsApi.getTemplate(templateId)
      currentTemplate.value = response.data
      return response.data
    } catch (error) {
      console.error('获取模板详情失败:', error)
      throw error
    }
  }

  const createTemplate = async (data: any) => {
    try {
      const response = await scriptsApi.createTemplate(data)
      // 刷新模板列表
      await fetchTemplates()
      return response.data
    } catch (error) {
      console.error('创建模板失败:', error)
      throw error
    }
  }

  const updateTemplate = async (templateId: string, data: any) => {
    try {
      const response = await scriptsApi.updateTemplate(templateId, data)
      // 更新模板列表中的对应项
      const index = templates.value.findIndex(template => template.id === templateId)
      if (index !== -1) {
        templates.value[index] = response.data
      }
      return response.data
    } catch (error) {
      console.error('更新模板失败:', error)
      throw error
    }
  }

  const deleteTemplate = async (templateId: string) => {
    try {
      await scriptsApi.deleteTemplate(templateId)
      // 从列表中移除
      templates.value = templates.value.filter(template => template.id !== templateId)
    } catch (error) {
      console.error('删除模板失败:', error)
      throw error
    }
  }

  // 调度相关操作
  const fetchSchedules = async (params?: ScheduleQueryParams) => {
    try {
      scheduleLoading.value = true
      const response = await scriptsApi.getSchedules(params)
      schedules.value = response.data.items
      schedulePagination.value = {
        page: response.data.pagination.page,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.total
      }
    } catch (error) {
      console.error('获取调度列表失败:', error)
      throw error
    } finally {
      scheduleLoading.value = false
    }
  }

  const fetchScheduleById = async (scheduleId: string) => {
    try {
      const response = await scriptsApi.getSchedule(scheduleId)
      currentSchedule.value = response.data
      return response.data
    } catch (error) {
      console.error('获取调度详情失败:', error)
      throw error
    }
  }

  const createSchedule = async (data: any) => {
    try {
      const response = await scriptsApi.createSchedule(data)
      // 刷新调度列表
      await fetchSchedules()
      return response.data
    } catch (error) {
      console.error('创建调度失败:', error)
      throw error
    }
  }

  const updateSchedule = async (scheduleId: string, data: any) => {
    try {
      const response = await scriptsApi.updateSchedule(scheduleId, data)
      // 更新调度列表中的对应项
      const index = schedules.value.findIndex(schedule => schedule.id === scheduleId)
      if (index !== -1) {
        schedules.value[index] = response.data
      }
      return response.data
    } catch (error) {
      console.error('更新调度失败:', error)
      throw error
    }
  }

  const deleteSchedule = async (scheduleId: string) => {
    try {
      await scriptsApi.deleteSchedule(scheduleId)
      // 从列表中移除
      schedules.value = schedules.value.filter(schedule => schedule.id !== scheduleId)
    } catch (error) {
      console.error('删除调度失败:', error)
      throw error
    }
  }

  const triggerSchedule = async (scheduleId: string, data?: any) => {
    try {
      const response = await scriptsApi.triggerSchedule(scheduleId, data)
      // 刷新任务列表
      await fetchTasks()
      return response.data
    } catch (error) {
      console.error('触发调度失败:', error)
      throw error
    }
  }

  // 统计相关操作
  const fetchStatsOverview = async () => {
    try {
      statsLoading.value = true
      const response = await scriptsApi.getStatsOverview()
      statsOverview.value = response.data
    } catch (error) {
      console.error('获取统计概览失败:', error)
      throw error
    } finally {
      statsLoading.value = false
    }
  }

  const fetchMetrics = async (params?: MetricsQueryParams) => {
    try {
      const response = await scriptsApi.getMetrics(params)
      metrics.value = response.data
    } catch (error) {
      console.error('获取指标数据失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetTaskState = () => {
    tasks.value = []
    currentTask.value = null
    taskPagination.value = { page: 1, pageSize: 20, total: 0 }
  }

  const resetTemplateState = () => {
    templates.value = []
    currentTemplate.value = null
    templatePagination.value = { page: 1, pageSize: 20, total: 0 }
  }

  const resetScheduleState = () => {
    schedules.value = []
    currentSchedule.value = null
    schedulePagination.value = { page: 1, pageSize: 20, total: 0 }
  }

  const resetStatsState = () => {
    statsOverview.value = null
    metrics.value = []
  }

  return {
    // 状态
    tasks,
    currentTask,
    taskLoading,
    taskPagination,
    templates,
    currentTemplate,
    templateLoading,
    templatePagination,
    schedules,
    currentSchedule,
    scheduleLoading,
    schedulePagination,
    statsOverview,
    metrics,
    statsLoading,

    // 计算属性
    runningTasks,
    completedTasks,
    failedTasks,
    activeTemplates,
    activeSchedules,

    // 方法
    fetchTasks,
    fetchTaskById,
    executeScript,
    cancelTask,
    fetchTemplates,
    fetchTemplateById,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    fetchSchedules,
    fetchScheduleById,
    createSchedule,
    updateSchedule,
    deleteSchedule,
    triggerSchedule,
    fetchStatsOverview,
    fetchMetrics,
    resetTaskState,
    resetTemplateState,
    resetScheduleState,
    resetStatsState
  }
})
