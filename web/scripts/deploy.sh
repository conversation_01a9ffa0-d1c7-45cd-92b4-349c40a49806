#!/bin/bash

# PaaS 前端部署脚本
# 用于自动化部署前端应用到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="paas-web"
DOCKER_REGISTRY="your-registry.com"
IMAGE_TAG=${1:-latest}
ENVIRONMENT=${2:-production}
NAMESPACE=${3:-default}

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装或不在 PATH 中"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建 Docker 镜像
build_image() {
    log_info "开始构建 Docker 镜像..."
    
    # 检查 Dockerfile 是否存在
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile 不存在"
        exit 1
    fi
    
    # 构建镜像
    docker build -t ${PROJECT_NAME}:${IMAGE_TAG} .
    
    if [ $? -eq 0 ]; then
        log_success "Docker 镜像构建成功"
    else
        log_error "Docker 镜像构建失败"
        exit 1
    fi
}

# 推送镜像到仓库
push_image() {
    log_info "推送镜像到仓库..."
    
    # 标记镜像
    docker tag ${PROJECT_NAME}:${IMAGE_TAG} ${DOCKER_REGISTRY}/${PROJECT_NAME}:${IMAGE_TAG}
    
    # 推送镜像
    docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}:${IMAGE_TAG}
    
    if [ $? -eq 0 ]; then
        log_success "镜像推送成功"
    else
        log_error "镜像推送失败"
        exit 1
    fi
}

# 部署到 Kubernetes
deploy_to_k8s() {
    log_info "部署到 Kubernetes 集群..."
    
    # 检查 k8s 配置文件
    if [ ! -d "k8s" ]; then
        log_error "k8s 配置目录不存在"
        exit 1
    fi
    
    # 替换配置文件中的变量
    sed -i.bak "s|{{IMAGE_TAG}}|${IMAGE_TAG}|g" k8s/deployment.yaml
    sed -i.bak "s|{{ENVIRONMENT}}|${ENVIRONMENT}|g" k8s/deployment.yaml
    sed -i.bak "s|{{NAMESPACE}}|${NAMESPACE}|g" k8s/deployment.yaml
    
    # 应用配置
    kubectl apply -f k8s/ -n ${NAMESPACE}
    
    # 等待部署完成
    kubectl rollout status deployment/${PROJECT_NAME} -n ${NAMESPACE} --timeout=300s
    
    if [ $? -eq 0 ]; then
        log_success "Kubernetes 部署成功"
    else
        log_error "Kubernetes 部署失败"
        exit 1
    fi
    
    # 恢复配置文件
    mv k8s/deployment.yaml.bak k8s/deployment.yaml
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 获取服务地址
    SERVICE_URL=$(kubectl get service ${PROJECT_NAME} -n ${NAMESPACE} -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    
    if [ -z "$SERVICE_URL" ]; then
        SERVICE_URL=$(kubectl get service ${PROJECT_NAME} -n ${NAMESPACE} -o jsonpath='{.spec.clusterIP}')
    fi
    
    # 健康检查
    for i in {1..10}; do
        if curl -f http://${SERVICE_URL}/health &> /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        log_warning "健康检查失败，重试中... ($i/10)"
        sleep 10
    done
    
    log_error "健康检查失败"
    return 1
}

# 回滚部署
rollback() {
    log_warning "开始回滚部署..."
    
    kubectl rollout undo deployment/${PROJECT_NAME} -n ${NAMESPACE}
    kubectl rollout status deployment/${PROJECT_NAME} -n ${NAMESPACE} --timeout=300s
    
    if [ $? -eq 0 ]; then
        log_success "回滚成功"
    else
        log_error "回滚失败"
        exit 1
    fi
}

# 清理旧镜像
cleanup() {
    log_info "清理旧镜像..."
    
    # 保留最近的 5 个镜像版本
    docker images ${PROJECT_NAME} --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
    tail -n +2 | sort -k2 -r | tail -n +6 | awk '{print $1}' | xargs -r docker rmi
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [IMAGE_TAG] [ENVIRONMENT] [NAMESPACE]"
    echo ""
    echo "参数:"
    echo "  IMAGE_TAG    Docker 镜像标签 (默认: latest)"
    echo "  ENVIRONMENT  部署环境 (默认: production)"
    echo "  NAMESPACE    Kubernetes 命名空间 (默认: default)"
    echo ""
    echo "示例:"
    echo "  $0 v1.0.0 production paas-prod"
    echo "  $0 latest staging paas-staging"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY  Docker 镜像仓库地址"
    echo "  SKIP_BUILD       跳过镜像构建 (true/false)"
    echo "  SKIP_PUSH        跳过镜像推送 (true/false)"
    echo "  SKIP_DEPLOY      跳过部署 (true/false)"
}

# 主函数
main() {
    log_info "开始部署 ${PROJECT_NAME} (${IMAGE_TAG}) 到 ${ENVIRONMENT} 环境"
    
    # 检查参数
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    # 检查依赖
    check_dependencies
    
    # 构建镜像
    if [ "${SKIP_BUILD}" != "true" ]; then
        build_image
    else
        log_warning "跳过镜像构建"
    fi
    
    # 推送镜像
    if [ "${SKIP_PUSH}" != "true" ]; then
        push_image
    else
        log_warning "跳过镜像推送"
    fi
    
    # 部署应用
    if [ "${SKIP_DEPLOY}" != "true" ]; then
        deploy_to_k8s
        
        # 健康检查
        if ! health_check; then
            log_error "健康检查失败，开始回滚"
            rollback
            exit 1
        fi
    else
        log_warning "跳过部署"
    fi
    
    # 清理旧镜像
    cleanup
    
    log_success "部署完成！"
    log_info "应用版本: ${IMAGE_TAG}"
    log_info "部署环境: ${ENVIRONMENT}"
    log_info "命名空间: ${NAMESPACE}"
}

# 捕获中断信号
trap 'log_error "部署被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
