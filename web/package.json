{"name": "paas-platform-web", "version": "1.0.0", "description": "PaaS平台前端管理界面", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.4.4", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "prettier": "^3.1.1", "sass-embedded": "^1.90.0", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8", "vitest": "^1.0.4", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}