# 环境变量配置示例文件
# 复制此文件为 .env.local 并根据实际情况修改配置

# 应用基本配置
VITE_APP_TITLE=PaaS 管理平台
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=企业级PaaS平台管理控制台

# API 配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=30000

# 功能开关
VITE_USE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_ANALYTICS=false

# 第三方服务配置
VITE_OAUTH_GITHUB_CLIENT_ID=your_github_client_id
VITE_OAUTH_GOOGLE_CLIENT_ID=your_google_client_id

# 监控配置
VITE_SENTRY_DSN=your_sentry_dsn
VITE_ENABLE_ERROR_REPORTING=false

# 主题配置
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true

# 缓存配置
VITE_CACHE_PREFIX=paas_
VITE_TOKEN_EXPIRE_DAYS=1
VITE_REFRESH_TOKEN_EXPIRE_DAYS=7

# 上传配置
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx

# 分页配置
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# WebSocket 配置
VITE_WS_URL=ws://localhost:8080/ws
VITE_WS_RECONNECT_INTERVAL=5000

# 地图配置（如果需要）
VITE_MAP_API_KEY=your_map_api_key

# 日志配置
VITE_LOG_LEVEL=info
VITE_ENABLE_CONSOLE_LOG=true
