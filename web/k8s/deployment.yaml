# Kubernetes 部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paas-web
  namespace: {{NAMESPACE}}
  labels:
    app: paas-web
    version: "{{IMAGE_TAG}}"
    environment: "{{ENVIRONMENT}}"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: paas-web
  template:
    metadata:
      labels:
        app: paas-web
        version: "{{IMAGE_TAG}}"
        environment: "{{ENVIRONMENT}}"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: paas-web
        image: your-registry.com/paas-web:{{IMAGE_TAG}}
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: ENVIRONMENT
          value: "{{ENVIRONMENT}}"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-cache-nginx
          mountPath: /var/cache/nginx
        - name: var-run
          mountPath: /var/run
      volumes:
      - name: tmp
        emptyDir: {}
      - name: var-cache-nginx
        emptyDir: {}
      - name: var-run
        emptyDir: {}
      securityContext:
        fsGroup: 1001
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
# Service 配置
apiVersion: v1
kind: Service
metadata:
  name: paas-web
  namespace: {{NAMESPACE}}
  labels:
    app: paas-web
    environment: "{{ENVIRONMENT}}"
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: paas-web

---
# Ingress 配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: paas-web
  namespace: {{NAMESPACE}}
  labels:
    app: paas-web
    environment: "{{ENVIRONMENT}}"
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - paas.yourdomain.com
    secretName: paas-web-tls
  rules:
  - host: paas.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: paas-web
            port:
              number: 80

---
# HorizontalPodAutoscaler 配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: paas-web
  namespace: {{NAMESPACE}}
  labels:
    app: paas-web
    environment: "{{ENVIRONMENT}}"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: paas-web
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60

---
# PodDisruptionBudget 配置
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: paas-web
  namespace: {{NAMESPACE}}
  labels:
    app: paas-web
    environment: "{{ENVIRONMENT}}"
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: paas-web
