# 开发环境认证跳过 - 快速开始

本文档提供在开发环境中跳过用户认证的快速配置方法。

## ⚠️ 安全警告

**🚨 此配置仅适用于开发环境，绝不能在生产环境使用！**

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
# 1. 启动开发环境（自动跳过认证）
./scripts/start-dev-mode.sh

# 2. 测试认证配置
./scripts/test-dev-auth.sh

# 3. 停止开发环境
./scripts/stop-dev-mode.sh
```

### 方法二：环境变量配置

```bash
# 1. 设置环境变量
export PAAS_AUTH_ENABLED=false
export PAAS_DEV_MODE=true

# 2. 启动服务
go run ./cmd/app-manager --config=configs/app-manager.dev.yaml
```

### 方法三：使用开发配置文件

```bash
# 直接使用开发环境配置文件启动
./bin/app-manager --config=configs/app-manager.dev.yaml
```

## 📋 可用的开发配置文件

- `configs/app-manager.dev.yaml` - 应用管理服务
- `configs/script-service.dev.yaml` - 脚本执行服务  
- `configs/cicd-service.dev.yaml` - CI/CD服务
- `configs/config-service.dev.yaml` - 配置服务

## 🧪 验证配置

启动服务后，测试无认证API访问：

```bash
# 健康检查（无需认证）
curl http://localhost:8081/health

# API接口（无需认证）
curl http://localhost:8081/api/v1/apps

# 使用开发令牌（可选）
curl -H "Authorization: Bearer dev-token-2024" \
     http://localhost:8081/api/v1/apps

# 访问API文档
open http://localhost:8081/swagger/index.html
```

## 🔧 环境变量说明

| 环境变量 | 说明 | 开发环境值 |
|---------|------|-----------|
| `PAAS_AUTH_ENABLED` | 是否启用认证 | `false` |
| `PAAS_DEV_MODE` | 开发模式 | `true` |
| `PAAS_DEV_TOKEN` | 开发令牌 | `dev-token-2024` |
| `PAAS_DEV_USER_ROLES` | 开发用户角色 | `admin,developer` |

## 🎯 服务端口

| 服务 | 端口 | 健康检查 | API文档 |
|------|------|----------|---------|
| 应用管理 | 8081 | `/health` | `/swagger` |
| 脚本执行 | 8084 | `/health` | `/swagger` |
| CI/CD | 8082 | `/health` | `/swagger` |
| 配置服务 | 8083 | `/health` | `/swagger` |

## 🔍 故障排除

### API返回401错误

```bash
# 检查环境变量
env | grep PAAS_

# 确认服务使用开发配置
ps aux | grep -E "(app-manager|script-service)"

# 查看服务日志
tail -f logs/app-manager.log
```

### 服务启动失败

```bash
# 检查端口占用
lsof -i :8081

# 清理并重启
./scripts/stop-dev-mode.sh --clean-workspace
./scripts/start-dev-mode.sh
```

## 📖 详细文档

完整的配置说明请参考：[开发环境认证配置指南](docs/development-auth-guide.md)

## 🛠️ 开发工具

### 自动化脚本

- `scripts/start-dev-mode.sh` - 启动开发环境
- `scripts/stop-dev-mode.sh` - 停止开发环境  
- `scripts/test-dev-auth.sh` - 测试认证配置

### 配置文件

- `.env.development` - 开发环境变量模板
- `configs/*.dev.yaml` - 各服务开发配置

### 日志和监控

```bash
# 查看所有服务日志
ls -la logs/

# 实时监控日志
tail -f logs/*.log

# 检查服务状态
curl http://localhost:8081/health
```

## 💡 最佳实践

1. **使用专门的开发配置文件**
2. **通过环境变量控制认证开关**
3. **定期测试认证配置**
4. **不要在生产环境使用开发配置**
5. **保持开发环境与生产环境的配置同步**

## 🚨 重要提醒

- ✅ 开发环境：可以跳过认证
- ❌ 测试环境：建议启用认证
- ❌ 生产环境：必须启用认证

---

**快速开始完成！现在您可以在开发环境中无需认证地访问所有API接口。**
