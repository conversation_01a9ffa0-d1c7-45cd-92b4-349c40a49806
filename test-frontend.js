#!/usr/bin/env node

/**
 * 前端功能测试脚本
 * 测试前端登录页面和健康检查功能
 */

const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3002';
const BACKEND_URL = 'http://localhost:8081';

async function testHealthCheck() {
  console.log('🔍 测试后端健康检查...');
  try {
    const response = await axios.get(`${BACKEND_URL}/health`);
    console.log('✅ 后端健康检查成功:', response.data);
    
    if (response.data.dev_mode) {
      console.log('🔧 开发模式已启用');
      console.log('👤 开发用户:', response.data.dev_user);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 后端健康检查失败:', error.message);
    return false;
  }
}

async function testFrontendHealthCheck() {
  console.log('🔍 测试前端健康检查代理...');
  try {
    // 测试前端代理到后端的健康检查
    const response = await axios.get(`${FRONTEND_URL}/health`);
    console.log('✅ 前端健康检查代理成功:', response.data);
    return true;
  } catch (error) {
    console.error('❌ 前端健康检查代理失败:', error.message);
    return false;
  }
}

async function testFrontendPage() {
  console.log('🔍 测试前端页面加载...');
  try {
    const response = await axios.get(FRONTEND_URL);
    if (response.status === 200) {
      console.log('✅ 前端页面加载成功');
      return true;
    }
  } catch (error) {
    console.error('❌ 前端页面加载失败:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 开始前端功能测试...\n');
  
  const results = {
    backendHealth: await testHealthCheck(),
    frontendHealth: await testFrontendHealthCheck(),
    frontendPage: await testFrontendPage()
  };
  
  console.log('\n📊 测试结果汇总:');
  console.log(`后端健康检查: ${results.backendHealth ? '✅ 通过' : '❌ 失败'}`);
  console.log(`前端健康检查代理: ${results.frontendHealth ? '✅ 通过' : '❌ 失败'}`);
  console.log(`前端页面加载: ${results.frontendPage ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 所有测试通过！前端登录页面应该能够正常工作。');
    console.log(`\n🌐 请在浏览器中访问: ${FRONTEND_URL}`);
    console.log('📝 测试登录功能：');
    console.log('   - 用户名: 任意用户名（开发模式）');
    console.log('   - 密码: 任意密码（开发模式）');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查相关服务状态。');
  }
  
  return allPassed;
}

// 运行测试
runTests().catch(console.error);
