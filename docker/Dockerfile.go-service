# 优化的 Go 服务 Dockerfile 模板
# 使用多阶段构建减小镜像大小并提高安全性

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具和依赖
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    upx

# 设置 Go 环境变量
ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64

# 复制 go.mod 和 go.sum 文件
COPY go.mod go.sum ./

# 下载依赖（利用 Docker 缓存层）
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用
RUN go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main ./cmd/main.go

# 使用 UPX 压缩二进制文件（可选）
RUN upx --best --lzma main

# 运行阶段 - 使用 distroless 镜像
FROM gcr.io/distroless/static:nonroot

# 从构建阶段复制必要文件
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /app/main /main

# 设置时区
ENV TZ=Asia/Shanghai

# 使用非 root 用户
USER nonroot:nonroot

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/main", "health"] || exit 1

# 运行应用
ENTRYPOINT ["/main"]

# 标签信息
LABEL maintainer="PaaS Team <<EMAIL>>" \
      version="1.0" \
      description="Optimized Go service container" \
      org.opencontainers.image.source="https://github.com/paas/platform" \
      org.opencontainers.image.documentation="https://docs.paas.com" \
      org.opencontainers.image.licenses="MIT"
