# PaaS 平台监控系统 Docker Compose 配置
# 包含 Prometheus, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 等完整监控栈

version: '3.8'

services:
  # 📊 Prometheus - 指标收集和存储
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: paas-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./configs/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./configs/alert-rules.yml:/etc/prometheus/alert-rules.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--log.level=info'
    networks:
      - paas-monitoring
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.paas.local`)"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"

  # 📈 Grafana - 可视化仪表板
  grafana:
    image: grafana/grafana:10.0.0
    container_name: paas-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./configs/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./configs/grafana/dashboards:/var/lib/grafana/dashboards:ro
    environment:
      # 基础配置
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      
      # 数据源配置
      - GF_DATASOURCES_DEFAULT_NAME=Prometheus
      
      # 插件配置
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel
      
      # 日志配置
      - GF_LOG_LEVEL=info
      - GF_LOG_MODE=console,file
      
      # 服务器配置
      - GF_SERVER_ROOT_URL=http://localhost:3001
      - GF_SERVER_SERVE_FROM_SUB_PATH=false
    networks:
      - paas-monitoring
    depends_on:
      - prometheus
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.paas.local`)"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  # 🚨 AlertManager - 告警管理
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: paas-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./configs/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager-data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
      - '--log.level=info'
    networks:
      - paas-monitoring
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.alertmanager.rule=Host(`alertmanager.paas.local`)"
      - "traefik.http.services.alertmanager.loadbalancer.server.port=9093"

  # 🔍 Jaeger - 分布式链路追踪
  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: paas-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
      - "6831:6831/udp"  # UDP agent
      - "6832:6832/udp"  # UDP agent
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - SPAN_STORAGE_TYPE=memory
      - MEMORY_MAX_TRACES=50000
    networks:
      - paas-monitoring
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.jaeger.rule=Host(`jaeger.paas.local`)"
      - "traefik.http.services.jaeger.loadbalancer.server.port=16686"

  # 📊 Node Exporter - 系统指标收集
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: paas-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
      - '--collector.systemd'
      - '--collector.processes'
    networks:
      - paas-monitoring

  # 🐘 PostgreSQL Exporter - 数据库指标
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.13.2
    container_name: paas-postgres-exporter
    restart: unless-stopped
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=postgresql://paas_user:<EMAIL>:5432/paas_db?sslmode=disable
      - PG_EXPORTER_EXTEND_QUERY_PATH=/etc/postgres_exporter/queries.yaml
    volumes:
      - ./configs/postgres-queries.yaml:/etc/postgres_exporter/queries.yaml:ro
    networks:
      - paas-monitoring
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 🔴 Redis Exporter - Redis 指标
  redis-exporter:
    image: oliver006/redis_exporter:v1.52.0
    container_name: paas-redis-exporter
    restart: unless-stopped
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://host.docker.internal:6379
      - REDIS_PASSWORD=
    networks:
      - paas-monitoring
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 📋 cAdvisor - 容器指标
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: paas-cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - paas-monitoring

  # 🔔 Webhook 告警接收器
  webhook-receiver:
    image: alpine/curl:latest
    container_name: paas-webhook-receiver
    restart: unless-stopped
    ports:
      - "8090:8080"
    command: |
      sh -c "
        apk add --no-cache python3 py3-pip &&
        pip3 install flask &&
        cat > /app.py << 'EOF'
      from flask import Flask, request, jsonify
      import json
      import datetime
      
      app = Flask(__name__)
      
      @app.route('/alerts', methods=['POST'])
      def receive_alert():
          data = request.get_json()
          timestamp = datetime.datetime.now().isoformat()
          
          print(f'[{timestamp}] 收到告警:')
          print(json.dumps(data, indent=2, ensure_ascii=False))
          
          # 这里可以添加自定义的告警处理逻辑
          # 例如：发送到企业微信、钉钉等
          
          return jsonify({'status': 'ok', 'message': '告警已接收'})
      
      @app.route('/health', methods=['GET'])
      def health():
          return jsonify({'status': 'healthy'})
      
      if __name__ == '__main__':
          app.run(host='0.0.0.0', port=8080, debug=True)
      EOF
      python3 /app.py
      "
    networks:
      - paas-monitoring

  # 🌐 Traefik - 反向代理和负载均衡
  traefik:
    image: traefik:v3.0
    container_name: paas-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "8081:8080"  # Traefik Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./configs/traefik.yml:/etc/traefik/traefik.yml:ro
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --log.level=INFO
    networks:
      - paas-monitoring
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.paas.local`)"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

# 📊 数据卷
volumes:
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  alertmanager-data:
    driver: local

# 🌐 网络
networks:
  paas-monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

---
# 📝 使用说明
# 
# 1. 启动监控栈：
#    docker-compose -f docker-compose.monitoring.yml up -d
# 
# 2. 访问地址：
#    - Prometheus: http://localhost:9090
#    - Grafana: http://localhost:3001 (admin/admin123)
#    - AlertManager: http://localhost:9093
#    - Jaeger: http://localhost:16686
#    - Traefik Dashboard: http://localhost:8081
# 
# 3. 停止监控栈：
#    docker-compose -f docker-compose.monitoring.yml down
# 
# 4. 清理数据：
#    docker-compose -f docker-compose.monitoring.yml down -v
# 
# 注意：首次启动需要配置 Grafana 数据源和仪表板
