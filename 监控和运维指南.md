# PaaS 平台监控和运维指南

## 📋 监控运维概览

本文档提供了 PaaS 平台的全面监控和运维指南，包括监控体系设计、告警策略、故障处理流程和运维最佳实践。

### 监控目标
- **可观测性**: 全面了解系统运行状态
- **可靠性**: 确保系统高可用和稳定运行
- **性能优化**: 及时发现和解决性能瓶颈
- **故障预防**: 主动发现和预防潜在问题

## 🔍 监控体系架构

### 1. 监控层次结构

```mermaid
graph TB
    subgraph "业务监控层"
        A1[用户体验监控]
        A2[业务指标监控]
        A3[SLA监控]
    end
    
    subgraph "应用监控层"
        B1[APM性能监控]
        B2[错误率监控]
        B3[响应时间监控]
        B4[吞吐量监控]
    end
    
    subgraph "服务监控层"
        C1[微服务健康检查]
        C2[API接口监控]
        C3[数据库监控]
        C4[缓存监控]
    end
    
    subgraph "基础设施监控层"
        D1[服务器资源监控]
        D2[网络监控]
        D3[存储监控]
        D4[容器监控]
    end
    
    A1 --> B1
    A2 --> B2
    B1 --> C1
    B2 --> C2
    C1 --> D1
    C2 --> D2
```

### 2. 监控技术栈

#### 2.1 指标收集
```yaml
metrics_collection:
  prometheus:
    version: "2.45+"
    retention: "30d"
    scrape_interval: "15s"
    
  node_exporter:
    version: "1.6+"
    metrics: [cpu, memory, disk, network]
    
  application_metrics:
    go: prometheus/client_golang
    javascript: prom-client
    custom_metrics: business_kpis
```

#### 2.2 日志聚合
```yaml
logging_stack:
  elasticsearch:
    version: "8.8+"
    cluster_size: 3
    retention: "90d"
    
  logstash:
    version: "8.8+"
    pipelines: [app_logs, access_logs, error_logs]
    
  kibana:
    version: "8.8+"
    dashboards: [application, infrastructure, security]
    
  filebeat:
    version: "8.8+"
    inputs: [docker, kubernetes, files]
```

#### 2.3 链路追踪
```yaml
tracing:
  jaeger:
    version: "1.47+"
    sampling_rate: 0.1
    retention: "7d"
    
  opentelemetry:
    version: "1.19+"
    exporters: [jaeger, prometheus]
    
  instrumentation:
    go: opentelemetry-go
    javascript: "@opentelemetry/api"
```

## 📊 关键监控指标

### 1. 业务指标 (Business KPIs)

#### 1.1 用户体验指标
```yaml
user_experience:
  login_success_rate:
    target: "> 99.5%"
    alert_threshold: "< 99%"
    measurement: "successful_logins / total_login_attempts"
    
  page_load_time:
    target: "< 3s"
    alert_threshold: "> 5s"
    measurement: "p95_page_load_time"
    
  api_response_time:
    target: "< 200ms"
    alert_threshold: "> 500ms"
    measurement: "p95_api_response_time"
```

#### 1.2 业务功能指标
```yaml
business_functions:
  application_deployment_success_rate:
    target: "> 95%"
    alert_threshold: "< 90%"
    
  cicd_pipeline_success_rate:
    target: "> 90%"
    alert_threshold: "< 85%"
    
  script_execution_success_rate:
    target: "> 95%"
    alert_threshold: "< 90%"
```

### 2. 技术指标 (Technical KPIs)

#### 2.1 系统可用性
```yaml
availability:
  system_uptime:
    target: "> 99.9%"
    alert_threshold: "< 99.5%"
    calculation: "uptime / total_time"
    
  service_health:
    target: "all_services_healthy"
    alert_threshold: "any_service_unhealthy"
    check_interval: "30s"
```

#### 2.2 性能指标
```yaml
performance:
  cpu_utilization:
    target: "< 70%"
    alert_threshold: "> 85%"
    measurement: "avg_cpu_usage_5min"
    
  memory_utilization:
    target: "< 80%"
    alert_threshold: "> 90%"
    measurement: "memory_used / memory_total"
    
  disk_utilization:
    target: "< 80%"
    alert_threshold: "> 90%"
    measurement: "disk_used / disk_total"
```

#### 2.3 错误率指标
```yaml
error_rates:
  http_error_rate:
    target: "< 0.1%"
    alert_threshold: "> 1%"
    measurement: "4xx_5xx_responses / total_responses"
    
  database_error_rate:
    target: "< 0.01%"
    alert_threshold: "> 0.1%"
    measurement: "failed_queries / total_queries"
```

## 🚨 告警策略

### 1. 告警级别定义

#### 1.1 告警级别
```yaml
alert_levels:
  critical:
    description: "系统不可用或严重影响业务"
    response_time: "5分钟内"
    notification: ["pagerduty", "phone", "email"]
    escalation: "15分钟后升级"
    
  warning:
    description: "系统性能下降或潜在问题"
    response_time: "30分钟内"
    notification: ["slack", "email"]
    escalation: "2小时后升级"
    
  info:
    description: "系统状态变化或维护通知"
    response_time: "工作时间内"
    notification: ["email"]
    escalation: "无"
```

#### 1.2 告警规则示例
```yaml
# Prometheus 告警规则
groups:
  - name: paas_platform_alerts
    rules:
      # 系统可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.instance }} 不可用"
          description: "服务已经下线超过1分钟"
          
      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.01
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.service }} 错误率过高"
          description: "5分钟内错误率超过1%"
          
      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.service }} 响应时间过长"
          description: "P95响应时间超过500ms"
```

### 2. 告警通知配置

#### 2.1 通知渠道
```yaml
notification_channels:
  pagerduty:
    integration_key: "${PAGERDUTY_INTEGRATION_KEY}"
    severity_mapping:
      critical: "critical"
      warning: "warning"
      
  slack:
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#paas-alerts"
    username: "PaaS Monitor"
    
  email:
    smtp_server: "smtp.company.com"
    from: "<EMAIL>"
    to: ["<EMAIL>", "<EMAIL>"]
```

#### 2.2 告警抑制规则
```yaml
# 告警抑制配置
inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['service', 'instance']
    
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '(HighResponseTime|HighErrorRate)'
    equal: ['service']
```

## 🔧 运维操作指南

### 1. 日常运维检查

#### 1.1 每日检查清单
```bash
#!/bin/bash
# 每日健康检查脚本

echo "=== PaaS 平台每日健康检查 ==="
echo "检查时间: $(date)"

# 1. 检查所有服务状态
echo "1. 检查服务状态..."
kubectl get pods -n paas-system
docker-compose ps

# 2. 检查系统资源使用情况
echo "2. 检查系统资源..."
df -h
free -h
top -bn1 | head -20

# 3. 检查关键业务指标
echo "3. 检查业务指标..."
curl -s "http://prometheus:9090/api/v1/query?query=up" | jq '.data.result'

# 4. 检查错误日志
echo "4. 检查错误日志..."
docker logs --since=24h paas_app_manager 2>&1 | grep -i error | tail -10

# 5. 检查数据库连接
echo "5. 检查数据库..."
psql -h postgres -U paas_user -d paas_db -c "SELECT 1;" > /dev/null && echo "数据库连接正常" || echo "数据库连接异常"

echo "=== 检查完成 ==="
```

#### 1.2 每周维护任务
```bash
#!/bin/bash
# 每周维护脚本

echo "=== PaaS 平台每周维护 ==="

# 1. 清理旧日志
echo "1. 清理旧日志..."
find /var/log/paas -name "*.log" -mtime +7 -delete
docker system prune -f

# 2. 数据库维护
echo "2. 数据库维护..."
psql -h postgres -U paas_user -d paas_db -c "VACUUM ANALYZE;"

# 3. 备份检查
echo "3. 检查备份..."
ls -la /backup/paas/ | tail -10

# 4. 安全更新检查
echo "4. 检查安全更新..."
apt list --upgradable | grep -i security

# 5. 证书有效期检查
echo "5. 检查SSL证书..."
openssl x509 -in /etc/ssl/certs/paas.crt -noout -dates

echo "=== 维护完成 ==="
```

### 2. 故障处理流程

#### 2.1 故障响应流程
```mermaid
graph TD
    A[收到告警] --> B{告警级别}
    B -->|Critical| C[立即响应<br/>5分钟内]
    B -->|Warning| D[30分钟内响应]
    B -->|Info| E[工作时间响应]
    
    C --> F[确认故障]
    D --> F
    E --> F
    
    F --> G{是否已知问题}
    G -->|是| H[执行标准处理流程]
    G -->|否| I[故障分析和诊断]
    
    H --> J[问题解决]
    I --> K[制定解决方案]
    K --> J
    
    J --> L[验证修复效果]
    L --> M{是否解决}
    M -->|否| I
    M -->|是| N[更新文档]
    N --> O[故障总结]
```

#### 2.2 常见故障处理手册

**服务不可用故障**:
```bash
# 1. 检查服务状态
kubectl get pods -n paas-system
docker-compose ps

# 2. 查看服务日志
kubectl logs -f deployment/app-manager -n paas-system
docker logs -f paas_app_manager

# 3. 检查资源使用情况
kubectl top pods -n paas-system
docker stats

# 4. 重启服务
kubectl rollout restart deployment/app-manager -n paas-system
docker-compose restart app-manager

# 5. 验证服务恢复
curl -f http://app-manager:8081/health
```

**数据库连接问题**:
```bash
# 1. 检查数据库状态
kubectl get pods -l app=postgresql -n paas-system
docker logs paas_postgres

# 2. 检查连接配置
kubectl get configmap db-config -n paas-system -o yaml
cat configs/database.yaml

# 3. 测试数据库连接
psql -h postgres -U paas_user -d paas_db -c "SELECT 1;"

# 4. 检查连接池状态
kubectl exec -it deployment/app-manager -n paas-system -- \
  curl localhost:8081/debug/db/stats

# 5. 重启数据库连接
kubectl rollout restart deployment/app-manager -n paas-system
```

### 3. 性能优化指南

#### 3.1 数据库优化
```sql
-- 查询慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;

-- 检查索引使用情况
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE tablename = 'users';

-- 分析表统计信息
ANALYZE users;

-- 重建索引
REINDEX INDEX idx_users_email;
```

#### 3.2 应用性能优化
```go
// Go 应用性能优化示例
func (s *Service) OptimizedGetUsers(ctx context.Context, params *QueryParams) ([]User, error) {
    // 1. 使用连接池
    db := s.db.WithContext(ctx)
    
    // 2. 限制查询字段
    query := db.Select("id, name, email, created_at")
    
    // 3. 添加索引条件
    if params.TenantID != "" {
        query = query.Where("tenant_id = ?", params.TenantID)
    }
    
    // 4. 分页查询
    query = query.Limit(params.Limit).Offset(params.Offset)
    
    // 5. 预加载关联数据
    if params.IncludeRoles {
        query = query.Preload("Roles")
    }
    
    var users []User
    if err := query.Find(&users).Error; err != nil {
        return nil, fmt.Errorf("查询用户失败: %w", err)
    }
    
    return users, nil
}
```

## 📈 监控仪表板

### 1. 系统概览仪表板
```yaml
dashboard_panels:
  - title: "系统状态概览"
    panels:
      - name: "服务可用性"
        query: "up"
        visualization: "stat"
        
      - name: "请求量"
        query: "rate(http_requests_total[5m])"
        visualization: "graph"
        
      - name: "错误率"
        query: "rate(http_requests_total{status=~'5..'}[5m])"
        visualization: "graph"
        
      - name: "响应时间"
        query: "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
        visualization: "graph"
```

### 2. 业务监控仪表板
```yaml
business_dashboard:
  - title: "业务指标监控"
    panels:
      - name: "用户登录成功率"
        query: "login_success_rate"
        target: "> 99.5%"
        
      - name: "应用部署成功率"
        query: "deployment_success_rate"
        target: "> 95%"
        
      - name: "活跃用户数"
        query: "active_users_count"
        
      - name: "资源使用情况"
        query: "resource_utilization"
```

## 🔄 持续改进

### 1. 监控优化策略
- **指标精简**: 定期清理无用指标
- **告警优化**: 减少误报和告警疲劳
- **自动化**: 增加自动化运维能力
- **预测性**: 引入预测性监控

### 2. 运维能力提升
- **知识库**: 建立运维知识库
- **培训**: 定期运维培训
- **演练**: 故障演练和应急响应
- **工具**: 持续改进运维工具

---

*本文档将根据系统运行情况和运维经验持续更新*
*最后更新时间: 2025-08-15*
