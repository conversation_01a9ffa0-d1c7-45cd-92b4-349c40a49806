package metrics

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// MetricsStorage 指标存储接口
type MetricsStorage interface {
	Store(metrics *SystemMetrics) error
	Query(query *MetricsQuery) (*MetricsResult, error)
	GetLatest() (*SystemMetrics, error)
	GetTimeRange(start, end time.Time) ([]*SystemMetrics, error)
	GetAggregated(start, end time.Time, interval time.Duration) (*AggregatedMetrics, error)
	Cleanup(retentionPeriod time.Duration) error
	Close() error
}

// MetricsQuery 指标查询
type MetricsQuery struct {
	StartTime time.Time         `json:"start_time"`
	EndTime   time.Time         `json:"end_time"`
	Metrics   []string          `json:"metrics"`
	Filters   map[string]string `json:"filters"`
	Limit     int               `json:"limit"`
	Offset    int               `json:"offset"`
	OrderBy   string            `json:"order_by"`
	OrderDesc bool              `json:"order_desc"`
}

// MetricsResult 指标查询结果
type MetricsResult struct {
	Metrics    []*SystemMetrics `json:"metrics"`
	Total      int              `json:"total"`
	StartTime  time.Time        `json:"start_time"`
	EndTime    time.Time        `json:"end_time"`
	QueryTime  time.Duration    `json:"query_time"`
}

// AggregatedMetrics 聚合指标
type AggregatedMetrics struct {
	Interval   time.Duration           `json:"interval"`
	StartTime  time.Time               `json:"start_time"`
	EndTime    time.Time               `json:"end_time"`
	DataPoints []AggregatedDataPoint   `json:"data_points"`
	Summary    map[string]MetricSummary `json:"summary"`
}

// AggregatedDataPoint 聚合数据点
type AggregatedDataPoint struct {
	Timestamp time.Time              `json:"timestamp"`
	Values    map[string]float64     `json:"values"`
	Counts    map[string]int         `json:"counts"`
}

// MetricSummary 指标摘要
type MetricSummary struct {
	Min    float64 `json:"min"`
	Max    float64 `json:"max"`
	Avg    float64 `json:"avg"`
	Sum    float64 `json:"sum"`
	Count  int     `json:"count"`
	Latest float64 `json:"latest"`
}

// MemoryMetricsStorage 内存指标存储实现
type MemoryMetricsStorage struct {
	metrics         []*SystemMetrics
	mutex           sync.RWMutex
	maxSize         int
	retentionPeriod time.Duration
	logger          logger.Logger
}

// NewMemoryMetricsStorage 创建内存指标存储
func NewMemoryMetricsStorage(maxSize int, retentionPeriod time.Duration, logger logger.Logger) *MemoryMetricsStorage {
	return &MemoryMetricsStorage{
		metrics:         make([]*SystemMetrics, 0, maxSize),
		maxSize:         maxSize,
		retentionPeriod: retentionPeriod,
		logger:          logger,
	}
}

// Store 存储指标
func (ms *MemoryMetricsStorage) Store(metrics *SystemMetrics) error {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	// 添加新指标
	ms.metrics = append(ms.metrics, metrics)

	// 如果超过最大大小，删除最旧的指标
	if len(ms.metrics) > ms.maxSize {
		ms.metrics = ms.metrics[1:]
	}

	// 清理过期指标
	cutoff := time.Now().Add(-ms.retentionPeriod)
	var validMetrics []*SystemMetrics
	for _, metric := range ms.metrics {
		if metric.Timestamp.After(cutoff) {
			validMetrics = append(validMetrics, metric)
		}
	}
	ms.metrics = validMetrics

	ms.logger.Debug("存储指标", "count", len(ms.metrics), "timestamp", metrics.Timestamp)
	return nil
}

// Query 查询指标
func (ms *MemoryMetricsStorage) Query(query *MetricsQuery) (*MetricsResult, error) {
	start := time.Now()
	
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	var filteredMetrics []*SystemMetrics

	// 时间范围过滤
	for _, metric := range ms.metrics {
		if (query.StartTime.IsZero() || metric.Timestamp.After(query.StartTime) || metric.Timestamp.Equal(query.StartTime)) &&
			(query.EndTime.IsZero() || metric.Timestamp.Before(query.EndTime) || metric.Timestamp.Equal(query.EndTime)) {
			filteredMetrics = append(filteredMetrics, metric)
		}
	}

	// 排序
	if query.OrderBy == "timestamp" || query.OrderBy == "" {
		sort.Slice(filteredMetrics, func(i, j int) bool {
			if query.OrderDesc {
				return filteredMetrics[i].Timestamp.After(filteredMetrics[j].Timestamp)
			}
			return filteredMetrics[i].Timestamp.Before(filteredMetrics[j].Timestamp)
		})
	}

	total := len(filteredMetrics)

	// 分页
	if query.Offset > 0 {
		if query.Offset >= len(filteredMetrics) {
			filteredMetrics = []*SystemMetrics{}
		} else {
			filteredMetrics = filteredMetrics[query.Offset:]
		}
	}

	if query.Limit > 0 && query.Limit < len(filteredMetrics) {
		filteredMetrics = filteredMetrics[:query.Limit]
	}

	result := &MetricsResult{
		Metrics:   filteredMetrics,
		Total:     total,
		StartTime: query.StartTime,
		EndTime:   query.EndTime,
		QueryTime: time.Since(start),
	}

	return result, nil
}

// GetLatest 获取最新指标
func (ms *MemoryMetricsStorage) GetLatest() (*SystemMetrics, error) {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	if len(ms.metrics) == 0 {
		return nil, fmt.Errorf("没有可用的指标数据")
	}

	// 返回最后一个指标
	latest := ms.metrics[len(ms.metrics)-1]
	
	// 返回副本以避免并发修改
	data, _ := json.Marshal(latest)
	var metrics SystemMetrics
	json.Unmarshal(data, &metrics)
	
	return &metrics, nil
}

// GetTimeRange 获取时间范围内的指标
func (ms *MemoryMetricsStorage) GetTimeRange(start, end time.Time) ([]*SystemMetrics, error) {
	query := &MetricsQuery{
		StartTime: start,
		EndTime:   end,
		OrderBy:   "timestamp",
		OrderDesc: false,
	}

	result, err := ms.Query(query)
	if err != nil {
		return nil, err
	}

	return result.Metrics, nil
}

// GetAggregated 获取聚合指标
func (ms *MemoryMetricsStorage) GetAggregated(start, end time.Time, interval time.Duration) (*AggregatedMetrics, error) {
	metrics, err := ms.GetTimeRange(start, end)
	if err != nil {
		return nil, err
	}

	if len(metrics) == 0 {
		return &AggregatedMetrics{
			Interval:   interval,
			StartTime:  start,
			EndTime:    end,
			DataPoints: []AggregatedDataPoint{},
			Summary:    make(map[string]MetricSummary),
		}, nil
	}

	// 创建时间桶
	buckets := make(map[time.Time][]*SystemMetrics)
	
	for _, metric := range metrics {
		// 计算该指标属于哪个时间桶
		bucketTime := start.Add(time.Duration(metric.Timestamp.Sub(start)/interval) * interval)
		buckets[bucketTime] = append(buckets[bucketTime], metric)
	}

	// 生成聚合数据点
	var dataPoints []AggregatedDataPoint
	var allValues = make(map[string][]float64)

	// 按时间排序桶
	var bucketTimes []time.Time
	for bucketTime := range buckets {
		bucketTimes = append(bucketTimes, bucketTime)
	}
	sort.Slice(bucketTimes, func(i, j int) bool {
		return bucketTimes[i].Before(bucketTimes[j])
	})

	for _, bucketTime := range bucketTimes {
		bucketMetrics := buckets[bucketTime]
		
		dataPoint := AggregatedDataPoint{
			Timestamp: bucketTime,
			Values:    make(map[string]float64),
			Counts:    make(map[string]int),
		}

		// 聚合各种指标
		var cpuSum, memorySum, diskSum float64
		count := len(bucketMetrics)

		for _, metric := range bucketMetrics {
			cpuSum += metric.CPU.UsagePercent
			memorySum += metric.Memory.UsedPercent
			diskSum += metric.Disk.UsedPercent
		}

		if count > 0 {
			dataPoint.Values["cpu_usage"] = cpuSum / float64(count)
			dataPoint.Values["memory_usage"] = memorySum / float64(count)
			dataPoint.Values["disk_usage"] = diskSum / float64(count)
			dataPoint.Counts["samples"] = count

			// 收集所有值用于计算摘要
			allValues["cpu_usage"] = append(allValues["cpu_usage"], dataPoint.Values["cpu_usage"])
			allValues["memory_usage"] = append(allValues["memory_usage"], dataPoint.Values["memory_usage"])
			allValues["disk_usage"] = append(allValues["disk_usage"], dataPoint.Values["disk_usage"])
		}

		dataPoints = append(dataPoints, dataPoint)
	}

	// 计算摘要统计
	summary := make(map[string]MetricSummary)
	for metricName, values := range allValues {
		if len(values) > 0 {
			summary[metricName] = calculateSummary(values)
		}
	}

	return &AggregatedMetrics{
		Interval:   interval,
		StartTime:  start,
		EndTime:    end,
		DataPoints: dataPoints,
		Summary:    summary,
	}, nil
}

// calculateSummary 计算指标摘要
func calculateSummary(values []float64) MetricSummary {
	if len(values) == 0 {
		return MetricSummary{}
	}

	min := values[0]
	max := values[0]
	sum := 0.0

	for _, value := range values {
		if value < min {
			min = value
		}
		if value > max {
			max = value
		}
		sum += value
	}

	avg := sum / float64(len(values))
	latest := values[len(values)-1]

	return MetricSummary{
		Min:    min,
		Max:    max,
		Avg:    avg,
		Sum:    sum,
		Count:  len(values),
		Latest: latest,
	}
}

// Cleanup 清理过期指标
func (ms *MemoryMetricsStorage) Cleanup(retentionPeriod time.Duration) error {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	cutoff := time.Now().Add(-retentionPeriod)
	var validMetrics []*SystemMetrics

	for _, metric := range ms.metrics {
		if metric.Timestamp.After(cutoff) {
			validMetrics = append(validMetrics, metric)
		}
	}

	removed := len(ms.metrics) - len(validMetrics)
	ms.metrics = validMetrics

	ms.logger.Info("清理过期指标", "removed", removed, "remaining", len(ms.metrics))
	return nil
}

// Close 关闭存储
func (ms *MemoryMetricsStorage) Close() error {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.metrics = nil
	ms.logger.Info("指标存储已关闭")
	return nil
}

// GetMetricsCount 获取指标数量
func (ms *MemoryMetricsStorage) GetMetricsCount() int {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()
	return len(ms.metrics)
}

// GetStorageInfo 获取存储信息
func (ms *MemoryMetricsStorage) GetStorageInfo() map[string]interface{} {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	var oldestTime, newestTime time.Time
	if len(ms.metrics) > 0 {
		oldestTime = ms.metrics[0].Timestamp
		newestTime = ms.metrics[len(ms.metrics)-1].Timestamp
	}

	return map[string]interface{}{
		"type":             "memory",
		"count":            len(ms.metrics),
		"max_size":         ms.maxSize,
		"retention_period": ms.retentionPeriod.String(),
		"oldest_timestamp": oldestTime,
		"newest_timestamp": newestTime,
		"usage_percent":    float64(len(ms.metrics)) / float64(ms.maxSize) * 100,
	}
}
