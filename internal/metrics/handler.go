package metrics

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// Handler 指标处理器
type Handler struct {
	collector *MetricsCollector
	storage   MetricsStorage
	logger    logger.Logger
}

// NewHandler 创建指标处理器
func NewHandler(collector *MetricsCollector, storage MetricsStorage, logger logger.Logger) *Handler {
	return &Handler{
		collector: collector,
		storage:   storage,
		logger:    logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 指标查询路由
	router.GET("/current", h.GetCurrentMetrics)
	router.GET("/latest", h.GetLatestMetrics)
	router.GET("/history", h.GetHistoryMetrics)
	router.GET("/aggregated", h.GetAggregatedMetrics)
	router.GET("/summary", h.GetMetricsSummary)
	
	// 指标管理路由
	router.POST("/custom", h.AddCustomMetric)
	router.GET("/storage/info", h.GetStorageInfo)
	router.POST("/storage/cleanup", h.CleanupStorage)
	
	// 导出路由
	router.GET("/export", h.ExportMetrics)
	router.GET("/prometheus", h.PrometheusMetrics)
}

// GetCurrentMetrics 获取当前指标
func (h *Handler) GetCurrentMetrics(c *gin.Context) {
	metrics := h.collector.GetLastMetrics()
	
	c.JSON(http.StatusOK, gin.H{
		"message":   "获取当前指标成功",
		"data":      metrics,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetLatestMetrics 获取最新指标
func (h *Handler) GetLatestMetrics(c *gin.Context) {
	metrics, err := h.storage.GetLatest()
	if err != nil {
		h.logger.Error("获取最新指标失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取最新指标失败",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message":   "获取最新指标成功",
		"data":      metrics,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetHistoryMetrics 获取历史指标
func (h *Handler) GetHistoryMetrics(c *gin.Context) {
	// 解析查询参数
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")
	orderBy := c.DefaultQuery("order_by", "timestamp")
	orderDesc := c.DefaultQuery("order_desc", "false") == "true"

	// 解析时间参数
	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "开始时间格式错误",
				"details": "请使用 RFC3339 格式，例如: 2023-01-01T00:00:00Z",
			})
			return
		}
	} else {
		// 默认查询最近1小时
		startTime = time.Now().Add(-time.Hour)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "结束时间格式错误",
				"details": "请使用 RFC3339 格式，例如: 2023-01-01T00:00:00Z",
			})
			return
		}
	} else {
		endTime = time.Now()
	}

	// 解析分页参数
	var limit, offset int
	if limitStr != "" {
		limit, err = strconv.Atoi(limitStr)
		if err != nil || limit < 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "limit 参数必须是非负整数",
			})
			return
		}
	}

	if offsetStr != "" {
		offset, err = strconv.Atoi(offsetStr)
		if err != nil || offset < 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "offset 参数必须是非负整数",
			})
			return
		}
	}

	// 构建查询
	query := &MetricsQuery{
		StartTime: startTime,
		EndTime:   endTime,
		Limit:     limit,
		Offset:    offset,
		OrderBy:   orderBy,
		OrderDesc: orderDesc,
	}

	// 执行查询
	result, err := h.storage.Query(query)
	if err != nil {
		h.logger.Error("查询历史指标失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查询历史指标失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取历史指标成功",
		"data":    result,
		"query": map[string]interface{}{
			"start_time": startTime.Format(time.RFC3339),
			"end_time":   endTime.Format(time.RFC3339),
			"limit":      limit,
			"offset":     offset,
			"order_by":   orderBy,
			"order_desc": orderDesc,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetAggregatedMetrics 获取聚合指标
func (h *Handler) GetAggregatedMetrics(c *gin.Context) {
	// 解析查询参数
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")
	intervalStr := c.DefaultQuery("interval", "5m")

	// 解析时间参数
	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "开始时间格式错误",
				"details": "请使用 RFC3339 格式",
			})
			return
		}
	} else {
		// 默认查询最近24小时
		startTime = time.Now().Add(-24 * time.Hour)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "结束时间格式错误",
				"details": "请使用 RFC3339 格式",
			})
			return
		}
	} else {
		endTime = time.Now()
	}

	// 解析间隔参数
	interval, err := time.ParseDuration(intervalStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "间隔格式错误",
			"details": "请使用 Go duration 格式，例如: 5m, 1h, 30s",
		})
		return
	}

	// 获取聚合指标
	aggregated, err := h.storage.GetAggregated(startTime, endTime, interval)
	if err != nil {
		h.logger.Error("获取聚合指标失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取聚合指标失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取聚合指标成功",
		"data":    aggregated,
		"query": map[string]interface{}{
			"start_time": startTime.Format(time.RFC3339),
			"end_time":   endTime.Format(time.RFC3339),
			"interval":   intervalStr,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetMetricsSummary 获取指标摘要
func (h *Handler) GetMetricsSummary(c *gin.Context) {
	// 获取最新指标
	latest, err := h.storage.GetLatest()
	if err != nil {
		h.logger.Error("获取最新指标失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取指标摘要失败",
			"details": err.Error(),
		})
		return
	}

	// 获取最近1小时的聚合数据
	endTime := time.Now()
	startTime := endTime.Add(-time.Hour)
	aggregated, err := h.storage.GetAggregated(startTime, endTime, 5*time.Minute)
	if err != nil {
		h.logger.Error("获取聚合指标失败", "error", err)
		aggregated = &AggregatedMetrics{
			Summary: make(map[string]MetricSummary),
		}
	}

	// 构建摘要
	summary := map[string]interface{}{
		"current": map[string]interface{}{
			"cpu_usage":    latest.CPU.UsagePercent,
			"memory_usage": latest.Memory.UsedPercent,
			"disk_usage":   latest.Disk.UsedPercent,
			"goroutines":   latest.Application.Goroutines,
			"heap_alloc":   latest.Application.HeapAlloc,
		},
		"hourly_stats": aggregated.Summary,
		"system_info": map[string]interface{}{
			"cpu_cores":    latest.CPU.CoreCount,
			"memory_total": latest.Memory.Total,
			"disk_total":   latest.Disk.Total,
		},
		"timestamp": latest.Timestamp.Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "获取指标摘要成功",
		"data":      summary,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// AddCustomMetricRequest 添加自定义指标请求
type AddCustomMetricRequest struct {
	Name  string  `json:"name" binding:"required"`
	Value float64 `json:"value" binding:"required"`
}

// AddCustomMetric 添加自定义指标
func (h *Handler) AddCustomMetric(c *gin.Context) {
	var req AddCustomMetricRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 添加自定义指标
	h.collector.AddCustomMetric(req.Name, req.Value)

	c.JSON(http.StatusOK, gin.H{
		"message": "添加自定义指标成功",
		"data": map[string]interface{}{
			"name":  req.Name,
			"value": req.Value,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetStorageInfo 获取存储信息
func (h *Handler) GetStorageInfo(c *gin.Context) {
	var info map[string]interface{}

	// 尝试获取存储信息（如果存储实现了相应方法）
	if memStorage, ok := h.storage.(*MemoryMetricsStorage); ok {
		info = memStorage.GetStorageInfo()
	} else {
		info = map[string]interface{}{
			"type": "unknown",
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "获取存储信息成功",
		"data":      info,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// CleanupStorage 清理存储
func (h *Handler) CleanupStorage(c *gin.Context) {
	retentionStr := c.DefaultQuery("retention", "24h")
	
	retention, err := time.ParseDuration(retentionStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "保留期格式错误",
			"details": "请使用 Go duration 格式，例如: 24h, 7d",
		})
		return
	}

	if err := h.storage.Cleanup(retention); err != nil {
		h.logger.Error("清理存储失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "清理存储失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "存储清理成功",
		"data": map[string]interface{}{
			"retention": retentionStr,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ExportMetrics 导出指标
func (h *Handler) ExportMetrics(c *gin.Context) {
	format := c.DefaultQuery("format", "json")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	// 解析时间参数
	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "开始时间格式错误",
			})
			return
		}
	} else {
		startTime = time.Now().Add(-24 * time.Hour)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "结束时间格式错误",
			})
			return
		}
	} else {
		endTime = time.Now()
	}

	// 获取指标数据
	metrics, err := h.storage.GetTimeRange(startTime, endTime)
	if err != nil {
		h.logger.Error("导出指标失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "导出指标失败",
			"details": err.Error(),
		})
		return
	}

	switch format {
	case "json":
		c.Header("Content-Disposition", "attachment; filename=metrics.json")
		c.JSON(http.StatusOK, map[string]interface{}{
			"export_time": time.Now().Format(time.RFC3339),
			"start_time":  startTime.Format(time.RFC3339),
			"end_time":    endTime.Format(time.RFC3339),
			"count":       len(metrics),
			"metrics":     metrics,
		})
	case "csv":
		c.Header("Content-Type", "text/csv")
		c.Header("Content-Disposition", "attachment; filename=metrics.csv")
		
		// 简化的 CSV 导出
		csvData := "timestamp,cpu_usage,memory_usage,disk_usage,goroutines\n"
		for _, metric := range metrics {
			csvData += fmt.Sprintf("%s,%.2f,%.2f,%.2f,%d\n",
				metric.Timestamp.Format(time.RFC3339),
				metric.CPU.UsagePercent,
				metric.Memory.UsedPercent,
				metric.Disk.UsedPercent,
				metric.Application.Goroutines,
			)
		}
		c.String(http.StatusOK, csvData)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "不支持的导出格式，支持: json, csv",
		})
	}
}

// PrometheusMetrics 导出 Prometheus 格式指标
func (h *Handler) PrometheusMetrics(c *gin.Context) {
	latest := h.collector.GetLastMetrics()
	
	// 生成 Prometheus 格式的指标
	prometheusData := fmt.Sprintf(`# HELP cpu_usage_percent CPU usage percentage
# TYPE cpu_usage_percent gauge
cpu_usage_percent %.2f

# HELP memory_usage_percent Memory usage percentage
# TYPE memory_usage_percent gauge
memory_usage_percent %.2f

# HELP disk_usage_percent Disk usage percentage
# TYPE disk_usage_percent gauge
disk_usage_percent %.2f

# HELP goroutines_count Number of goroutines
# TYPE goroutines_count gauge
goroutines_count %d

# HELP heap_alloc_bytes Heap allocated bytes
# TYPE heap_alloc_bytes gauge
heap_alloc_bytes %d
`,
		latest.CPU.UsagePercent,
		latest.Memory.UsedPercent,
		latest.Disk.UsedPercent,
		latest.Application.Goroutines,
		latest.Application.HeapAlloc,
	)

	c.Header("Content-Type", "text/plain; version=0.0.4; charset=utf-8")
	c.String(http.StatusOK, prometheusData)
}
