package metrics

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/net"
	"github.com/shirou/gopsutil/v3/process"

	"paas-platform/pkg/logger"
)

// MetricsCollector 指标收集器
type MetricsCollector struct {
	logger           logger.Logger
	storage          MetricsStorage
	collectionInterval time.Duration
	ctx              context.Context
	cancel           context.CancelFunc
	mutex            sync.RWMutex
	lastMetrics      *SystemMetrics
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	Timestamp    time.Time          `json:"timestamp"`
	CPU          CPUMetrics         `json:"cpu"`
	Memory       MemoryMetrics      `json:"memory"`
	Disk         DiskMetrics        `json:"disk"`
	Network      NetworkMetrics     `json:"network"`
	Process      ProcessMetrics     `json:"process"`
	Application  ApplicationMetrics `json:"application"`
	Custom       map[string]float64 `json:"custom"`
}

// CPUMetrics CPU 指标
type CPUMetrics struct {
	UsagePercent float64   `json:"usage_percent"`
	LoadAvg1     float64   `json:"load_avg_1"`
	LoadAvg5     float64   `json:"load_avg_5"`
	LoadAvg15    float64   `json:"load_avg_15"`
	CoreCount    int       `json:"core_count"`
	PerCore      []float64 `json:"per_core"`
}

// MemoryMetrics 内存指标
type MemoryMetrics struct {
	Total       uint64  `json:"total"`
	Available   uint64  `json:"available"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"used_percent"`
	Free        uint64  `json:"free"`
	Cached      uint64  `json:"cached"`
	Buffers     uint64  `json:"buffers"`
	SwapTotal   uint64  `json:"swap_total"`
	SwapUsed    uint64  `json:"swap_used"`
	SwapFree    uint64  `json:"swap_free"`
}

// DiskMetrics 磁盘指标
type DiskMetrics struct {
	Total       uint64             `json:"total"`
	Used        uint64             `json:"used"`
	Free        uint64             `json:"free"`
	UsedPercent float64            `json:"used_percent"`
	Partitions  []PartitionMetrics `json:"partitions"`
	IOStats     DiskIOMetrics      `json:"io_stats"`
}

// PartitionMetrics 分区指标
type PartitionMetrics struct {
	Device      string  `json:"device"`
	Mountpoint  string  `json:"mountpoint"`
	Fstype      string  `json:"fstype"`
	Total       uint64  `json:"total"`
	Used        uint64  `json:"used"`
	Free        uint64  `json:"free"`
	UsedPercent float64 `json:"used_percent"`
}

// DiskIOMetrics 磁盘 IO 指标
type DiskIOMetrics struct {
	ReadCount  uint64 `json:"read_count"`
	WriteCount uint64 `json:"write_count"`
	ReadBytes  uint64 `json:"read_bytes"`
	WriteBytes uint64 `json:"write_bytes"`
	ReadTime   uint64 `json:"read_time"`
	WriteTime  uint64 `json:"write_time"`
}

// NetworkMetrics 网络指标
type NetworkMetrics struct {
	BytesSent   uint64                    `json:"bytes_sent"`
	BytesRecv   uint64                    `json:"bytes_recv"`
	PacketsSent uint64                    `json:"packets_sent"`
	PacketsRecv uint64                    `json:"packets_recv"`
	Errin       uint64                    `json:"errin"`
	Errout      uint64                    `json:"errout"`
	Dropin      uint64                    `json:"dropin"`
	Dropout     uint64                    `json:"dropout"`
	Interfaces  []NetworkInterfaceMetrics `json:"interfaces"`
}

// NetworkInterfaceMetrics 网络接口指标
type NetworkInterfaceMetrics struct {
	Name        string `json:"name"`
	BytesSent   uint64 `json:"bytes_sent"`
	BytesRecv   uint64 `json:"bytes_recv"`
	PacketsSent uint64 `json:"packets_sent"`
	PacketsRecv uint64 `json:"packets_recv"`
	Errin       uint64 `json:"errin"`
	Errout      uint64 `json:"errout"`
	Dropin      uint64 `json:"dropin"`
	Dropout     uint64 `json:"dropout"`
}

// ProcessMetrics 进程指标
type ProcessMetrics struct {
	Count     int                `json:"count"`
	Running   int                `json:"running"`
	Sleeping  int                `json:"sleeping"`
	Stopped   int                `json:"stopped"`
	Zombie    int                `json:"zombie"`
	Processes []ProcessInfo      `json:"processes"`
}

// ProcessInfo 进程信息
type ProcessInfo struct {
	PID         int32   `json:"pid"`
	Name        string  `json:"name"`
	Status      string  `json:"status"`
	CPUPercent  float64 `json:"cpu_percent"`
	MemoryRSS   uint64  `json:"memory_rss"`
	MemoryVMS   uint64  `json:"memory_vms"`
	MemoryPercent float32 `json:"memory_percent"`
	CreateTime  int64   `json:"create_time"`
}

// ApplicationMetrics 应用指标
type ApplicationMetrics struct {
	Goroutines   int           `json:"goroutines"`
	GCPauseTotal uint64        `json:"gc_pause_total"`
	GCPauseCount uint32        `json:"gc_pause_count"`
	HeapAlloc    uint64        `json:"heap_alloc"`
	HeapSys      uint64        `json:"heap_sys"`
	HeapIdle     uint64        `json:"heap_idle"`
	HeapInuse    uint64        `json:"heap_inuse"`
	HeapReleased uint64        `json:"heap_released"`
	HeapObjects  uint64        `json:"heap_objects"`
	StackInuse   uint64        `json:"stack_inuse"`
	StackSys     uint64        `json:"stack_sys"`
	MSpanInuse   uint64        `json:"mspan_inuse"`
	MSpanSys     uint64        `json:"mspan_sys"`
	MCacheInuse  uint64        `json:"mcache_inuse"`
	MCacheSys    uint64        `json:"mcache_sys"`
	NextGC       uint64        `json:"next_gc"`
	LastGC       uint64        `json:"last_gc"`
	Uptime       time.Duration `json:"uptime"`
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(storage MetricsStorage, logger logger.Logger) *MetricsCollector {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &MetricsCollector{
		logger:             logger,
		storage:            storage,
		collectionInterval: 30 * time.Second,
		ctx:                ctx,
		cancel:             cancel,
		lastMetrics:        &SystemMetrics{},
	}
}

// Start 启动指标收集
func (mc *MetricsCollector) Start() {
	mc.logger.Info("启动指标收集器")
	
	// 立即收集一次指标
	mc.collectMetrics()
	
	ticker := time.NewTicker(mc.collectionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-mc.ctx.Done():
			mc.logger.Info("指标收集器已停止")
			return
		case <-ticker.C:
			mc.collectMetrics()
		}
	}
}

// Stop 停止指标收集
func (mc *MetricsCollector) Stop() {
	mc.logger.Info("停止指标收集器")
	mc.cancel()
}

// collectMetrics 收集系统指标
func (mc *MetricsCollector) collectMetrics() {
	start := time.Now()
	
	metrics := &SystemMetrics{
		Timestamp: start,
		Custom:    make(map[string]float64),
	}

	// 并发收集各类指标
	var wg sync.WaitGroup
	
	wg.Add(6)
	
	// 收集 CPU 指标
	go func() {
		defer wg.Done()
		mc.collectCPUMetrics(metrics)
	}()
	
	// 收集内存指标
	go func() {
		defer wg.Done()
		mc.collectMemoryMetrics(metrics)
	}()
	
	// 收集磁盘指标
	go func() {
		defer wg.Done()
		mc.collectDiskMetrics(metrics)
	}()
	
	// 收集网络指标
	go func() {
		defer wg.Done()
		mc.collectNetworkMetrics(metrics)
	}()
	
	// 收集进程指标
	go func() {
		defer wg.Done()
		mc.collectProcessMetrics(metrics)
	}()
	
	// 收集应用指标
	go func() {
		defer wg.Done()
		mc.collectApplicationMetrics(metrics)
	}()
	
	wg.Wait()

	// 存储指标
	if err := mc.storage.Store(metrics); err != nil {
		mc.logger.Error("存储指标失败", "error", err)
	}

	// 更新最后收集的指标
	mc.mutex.Lock()
	mc.lastMetrics = metrics
	mc.mutex.Unlock()

	duration := time.Since(start)
	mc.logger.Debug("指标收集完成", "duration", duration)
}

// collectCPUMetrics 收集 CPU 指标
func (mc *MetricsCollector) collectCPUMetrics(metrics *SystemMetrics) {
	// CPU 使用率
	if cpuPercent, err := cpu.Percent(time.Second, false); err == nil && len(cpuPercent) > 0 {
		metrics.CPU.UsagePercent = cpuPercent[0]
	}

	// 每核心 CPU 使用率
	if perCorePercent, err := cpu.Percent(time.Second, true); err == nil {
		metrics.CPU.PerCore = perCorePercent
	}

	// CPU 核心数
	if coreCount, err := cpu.Counts(true); err == nil {
		metrics.CPU.CoreCount = coreCount
	}

	// 负载平均值 (Linux/Unix)
	if loadAvg, err := cpu.LoadAvg(); err == nil {
		metrics.CPU.LoadAvg1 = loadAvg.Load1
		metrics.CPU.LoadAvg5 = loadAvg.Load5
		metrics.CPU.LoadAvg15 = loadAvg.Load15
	}
}

// collectMemoryMetrics 收集内存指标
func (mc *MetricsCollector) collectMemoryMetrics(metrics *SystemMetrics) {
	// 虚拟内存
	if vmStat, err := mem.VirtualMemory(); err == nil {
		metrics.Memory.Total = vmStat.Total
		metrics.Memory.Available = vmStat.Available
		metrics.Memory.Used = vmStat.Used
		metrics.Memory.UsedPercent = vmStat.UsedPercent
		metrics.Memory.Free = vmStat.Free
		metrics.Memory.Cached = vmStat.Cached
		metrics.Memory.Buffers = vmStat.Buffers
	}

	// 交换内存
	if swapStat, err := mem.SwapMemory(); err == nil {
		metrics.Memory.SwapTotal = swapStat.Total
		metrics.Memory.SwapUsed = swapStat.Used
		metrics.Memory.SwapFree = swapStat.Free
	}
}

// collectDiskMetrics 收集磁盘指标
func (mc *MetricsCollector) collectDiskMetrics(metrics *SystemMetrics) {
	// 根分区使用情况
	if usage, err := disk.Usage("/"); err == nil {
		metrics.Disk.Total = usage.Total
		metrics.Disk.Used = usage.Used
		metrics.Disk.Free = usage.Free
		metrics.Disk.UsedPercent = usage.UsedPercent
	}

	// 所有分区信息
	if partitions, err := disk.Partitions(false); err == nil {
		for _, partition := range partitions {
			if usage, err := disk.Usage(partition.Mountpoint); err == nil {
				partitionMetric := PartitionMetrics{
					Device:      partition.Device,
					Mountpoint:  partition.Mountpoint,
					Fstype:      partition.Fstype,
					Total:       usage.Total,
					Used:        usage.Used,
					Free:        usage.Free,
					UsedPercent: usage.UsedPercent,
				}
				metrics.Disk.Partitions = append(metrics.Disk.Partitions, partitionMetric)
			}
		}
	}

	// 磁盘 IO 统计
	if ioStats, err := disk.IOCounters(); err == nil {
		var totalReadCount, totalWriteCount uint64
		var totalReadBytes, totalWriteBytes uint64
		var totalReadTime, totalWriteTime uint64

		for _, stat := range ioStats {
			totalReadCount += stat.ReadCount
			totalWriteCount += stat.WriteCount
			totalReadBytes += stat.ReadBytes
			totalWriteBytes += stat.WriteBytes
			totalReadTime += stat.ReadTime
			totalWriteTime += stat.WriteTime
		}

		metrics.Disk.IOStats = DiskIOMetrics{
			ReadCount:  totalReadCount,
			WriteCount: totalWriteCount,
			ReadBytes:  totalReadBytes,
			WriteBytes: totalWriteBytes,
			ReadTime:   totalReadTime,
			WriteTime:  totalWriteTime,
		}
	}
}

// collectNetworkMetrics 收集网络指标
func (mc *MetricsCollector) collectNetworkMetrics(metrics *SystemMetrics) {
	// 网络 IO 统计
	if netStats, err := net.IOCounters(false); err == nil && len(netStats) > 0 {
		stat := netStats[0]
		metrics.Network.BytesSent = stat.BytesSent
		metrics.Network.BytesRecv = stat.BytesRecv
		metrics.Network.PacketsSent = stat.PacketsSent
		metrics.Network.PacketsRecv = stat.PacketsRecv
		metrics.Network.Errin = stat.Errin
		metrics.Network.Errout = stat.Errout
		metrics.Network.Dropin = stat.Dropin
		metrics.Network.Dropout = stat.Dropout
	}

	// 各网络接口统计
	if interfaceStats, err := net.IOCounters(true); err == nil {
		for _, stat := range interfaceStats {
			interfaceMetric := NetworkInterfaceMetrics{
				Name:        stat.Name,
				BytesSent:   stat.BytesSent,
				BytesRecv:   stat.BytesRecv,
				PacketsSent: stat.PacketsSent,
				PacketsRecv: stat.PacketsRecv,
				Errin:       stat.Errin,
				Errout:      stat.Errout,
				Dropin:      stat.Dropin,
				Dropout:     stat.Dropout,
			}
			metrics.Network.Interfaces = append(metrics.Network.Interfaces, interfaceMetric)
		}
	}
}

// collectProcessMetrics 收集进程指标
func (mc *MetricsCollector) collectProcessMetrics(metrics *SystemMetrics) {
	// 获取所有进程
	if pids, err := process.Pids(); err == nil {
		metrics.Process.Count = len(pids)

		// 统计进程状态
		statusCount := make(map[string]int)
		var processes []ProcessInfo

		for _, pid := range pids {
			if proc, err := process.NewProcess(pid); err == nil {
				if status, err := proc.Status(); err == nil {
					statusCount[status]++

					// 收集详细进程信息（限制数量）
					if len(processes) < 10 {
						if name, err := proc.Name(); err == nil {
							if cpuPercent, err := proc.CPUPercent(); err == nil {
								if memInfo, err := proc.MemoryInfo(); err == nil {
									if memPercent, err := proc.MemoryPercent(); err == nil {
										if createTime, err := proc.CreateTime(); err == nil {
											processInfo := ProcessInfo{
												PID:           pid,
												Name:          name,
												Status:        status,
												CPUPercent:    cpuPercent,
												MemoryRSS:     memInfo.RSS,
												MemoryVMS:     memInfo.VMS,
												MemoryPercent: memPercent,
												CreateTime:    createTime,
											}
											processes = append(processes, processInfo)
										}
									}
								}
							}
						}
					}
				}
			}
		}

		metrics.Process.Running = statusCount["R"] + statusCount["S"]
		metrics.Process.Sleeping = statusCount["S"]
		metrics.Process.Stopped = statusCount["T"]
		metrics.Process.Zombie = statusCount["Z"]
		metrics.Process.Processes = processes
	}
}

// collectApplicationMetrics 收集应用指标
func (mc *MetricsCollector) collectApplicationMetrics(metrics *SystemMetrics) {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	metrics.Application.Goroutines = runtime.NumGoroutine()
	metrics.Application.GCPauseTotal = memStats.PauseTotalNs
	metrics.Application.GCPauseCount = memStats.NumGC
	metrics.Application.HeapAlloc = memStats.HeapAlloc
	metrics.Application.HeapSys = memStats.HeapSys
	metrics.Application.HeapIdle = memStats.HeapIdle
	metrics.Application.HeapInuse = memStats.HeapInuse
	metrics.Application.HeapReleased = memStats.HeapReleased
	metrics.Application.HeapObjects = memStats.HeapObjects
	metrics.Application.StackInuse = memStats.StackInuse
	metrics.Application.StackSys = memStats.StackSys
	metrics.Application.MSpanInuse = memStats.MSpanInuse
	metrics.Application.MSpanSys = memStats.MSpanSys
	metrics.Application.MCacheInuse = memStats.MCacheInuse
	metrics.Application.MCacheSys = memStats.MCacheSys
	metrics.Application.NextGC = memStats.NextGC
	metrics.Application.LastGC = memStats.LastGC

	// 计算运行时间（这里需要在应用启动时记录启动时间）
	// 暂时使用一个固定值，实际应用中应该从配置或全局变量获取
	metrics.Application.Uptime = time.Since(time.Now().Add(-time.Hour))
}

// GetLastMetrics 获取最后收集的指标
func (mc *MetricsCollector) GetLastMetrics() *SystemMetrics {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	// 返回副本以避免并发修改
	data, _ := json.Marshal(mc.lastMetrics)
	var metrics SystemMetrics
	json.Unmarshal(data, &metrics)
	
	return &metrics
}

// AddCustomMetric 添加自定义指标
func (mc *MetricsCollector) AddCustomMetric(name string, value float64) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	if mc.lastMetrics.Custom == nil {
		mc.lastMetrics.Custom = make(map[string]float64)
	}
	
	mc.lastMetrics.Custom[name] = value
}
