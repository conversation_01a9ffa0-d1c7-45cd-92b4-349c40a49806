package container

import (
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"

	"paas-platform/pkg/logger"
)

// ContainerManager 容器管理器接口
type ContainerManager interface {
	// 创建容器
	CreateContainer(ctx context.Context, config *ContainerConfig) (*Container, error)
	
	// 启动容器
	StartContainer(ctx context.Context, containerID string) error
	
	// 停止容器
	StopContainer(ctx context.Context, containerID string, timeout time.Duration) error
	
	// 删除容器
	RemoveContainer(ctx context.Context, containerID string, force bool) error
	
	// 获取容器日志
	GetContainerLogs(ctx context.Context, containerID string) (io.ReadCloser, error)
	
	// 获取容器状态
	GetContainerStatus(ctx context.Context, containerID string) (*ContainerStatus, error)
	
	// 执行命令
	ExecCommand(ctx context.Context, containerID string, cmd []string) (*ExecResult, error)
	
	// 拉取镜像
	PullImage(ctx context.Context, image string) error
	
	// 检查镜像是否存在
	ImageExists(ctx context.Context, image string) (bool, error)
	
	// 获取容器统计信息
	GetContainerStats(ctx context.Context, containerID string) (*ContainerStats, error)
}

// DockerManager Docker容器管理器实现
type DockerManager struct {
	logger logger.Logger
}

// ContainerConfig 容器配置
type ContainerConfig struct {
	Image       string            `json:"image"`
	Name        string            `json:"name"`
	Command     []string          `json:"command"`
	Env         []string          `json:"env"`
	WorkingDir  string            `json:"working_dir"`
	Volumes     []VolumeMount     `json:"volumes"`
	Ports       []PortMapping     `json:"ports"`
	Resources   *ResourceConfig   `json:"resources"`
	NetworkMode string            `json:"network_mode"`
	AutoRemove  bool              `json:"auto_remove"`
	Privileged  bool              `json:"privileged"`
	Labels      map[string]string `json:"labels"`
}

// VolumeMount 卷挂载配置
type VolumeMount struct {
	Source      string `json:"source"`
	Target      string `json:"target"`
	Type        string `json:"type"` // bind, volume, tmpfs
	ReadOnly    bool   `json:"read_only"`
}

// PortMapping 端口映射配置
type PortMapping struct {
	HostPort      string `json:"host_port"`
	ContainerPort string `json:"container_port"`
	Protocol      string `json:"protocol"` // tcp, udp
}

// ResourceConfig 资源限制配置
type ResourceConfig struct {
	CPULimit    string `json:"cpu_limit"`     // 例如: "0.5" 表示0.5个CPU核心
	MemoryLimit string `json:"memory_limit"`  // 例如: "512m", "1g"
	DiskLimit   string `json:"disk_limit"`    // 例如: "1g"
}

// Container 容器信息
type Container struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Image    string            `json:"image"`
	Status   string            `json:"status"`
	Created  time.Time         `json:"created"`
	Labels   map[string]string `json:"labels"`
}

// ContainerStatus 容器状态
type ContainerStatus struct {
	ID         string    `json:"id"`
	Status     string    `json:"status"`     // created, running, paused, restarting, removing, exited, dead
	State      string    `json:"state"`      // running, exited, etc.
	ExitCode   int       `json:"exit_code"`
	StartedAt  time.Time `json:"started_at"`
	FinishedAt time.Time `json:"finished_at"`
	Error      string    `json:"error"`
}

// ExecResult 命令执行结果
type ExecResult struct {
	ExitCode int    `json:"exit_code"`
	Stdout   string `json:"stdout"`
	Stderr   string `json:"stderr"`
}

// ContainerStats 容器统计信息
type ContainerStats struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU使用率百分比
	MemoryUsage int64   `json:"memory_usage"` // 内存使用量(字节)
	MemoryLimit int64   `json:"memory_limit"` // 内存限制(字节)
	NetworkRx   int64   `json:"network_rx"`   // 网络接收字节数
	NetworkTx   int64   `json:"network_tx"`   // 网络发送字节数
	DiskRead    int64   `json:"disk_read"`    // 磁盘读取字节数
	DiskWrite   int64   `json:"disk_write"`   // 磁盘写入字节数
}

// ContainerSpec 容器规格
type ContainerSpec struct {
	Image       string            `json:"image"`
	Name        string            `json:"name"`
	Command     []string          `json:"command"`
	Args        []string          `json:"args"`
	Env         []string          `json:"env"`
	WorkingDir  string            `json:"working_dir"`
	Volumes     []VolumeMount     `json:"volumes"`
	Ports       []PortMapping     `json:"ports"`
	Resources   *ResourceLimits   `json:"resources"`
	NetworkMode string            `json:"network_mode"`
	AutoRemove  bool              `json:"auto_remove"`
	Privileged  bool              `json:"privileged"`
	Labels      map[string]string `json:"labels"`
	RestartPolicy string          `json:"restart_policy"`
}

// ResourceLimits 资源限制（容器级别）
type ResourceLimits struct {
	CPULimit    int64 `json:"cpu_limit"`    // CPU限制（纳秒）
	MemoryLimit int64 `json:"memory_limit"` // 内存限制（字节）
	PidsLimit   int64 `json:"pids_limit"`   // 进程数限制
}

// DockerConfig Docker配置
type DockerConfig struct {
	Host    string `yaml:"host"`
	Timeout string `yaml:"timeout"`
	TLS     struct {
		Enabled            bool   `yaml:"enabled"`
		Verify             bool   `yaml:"verify"`
		CertPath           string `yaml:"cert_path"`
		CAFile             string `yaml:"ca_file"`
		CertFile           string `yaml:"cert_file"`
		KeyFile            string `yaml:"key_file"`
		InsecureSkipVerify bool   `yaml:"insecure_skip_verify"`
	} `yaml:"tls"`
	Fallback struct {
		Enabled bool   `yaml:"enabled"`
		Host    string `yaml:"host"`
	} `yaml:"fallback"`
}

// NewDockerManager 创建Docker管理器
func NewDockerManager(logger logger.Logger) (ContainerManager, error) {
	return NewDockerManagerWithConfig(logger, nil)
}

// NewDockerManagerWithConfig 使用配置创建Docker管理器
func NewDockerManagerWithConfig(logger logger.Logger, config *DockerConfig) (ContainerManager, error) {
	dm := &DockerManager{
		logger: logger,
	}

	// 尝试连接Docker
	if err := dm.testConnection(config); err != nil {
		logger.Warn("Docker连接失败，将使用模拟模式", "error", err)
	} else {
		logger.Info("Docker连接成功")
	}

	return dm, nil
}

// testConnection 测试Docker连接
func (dm *DockerManager) testConnection(config *DockerConfig) error {
	if config == nil {
		return checkDockerAvailable()
	}

	// 设置环境变量
	if config.Host != "" {
		if err := setDockerHost(config.Host); err != nil {
			return fmt.Errorf("设置Docker主机失败: %w", err)
		}
	}

	// 设置TLS配置
	if config.TLS.Enabled {
		if err := setDockerTLS(config); err != nil {
			return fmt.Errorf("设置Docker TLS失败: %w", err)
		}
	}

	// 测试连接
	return checkDockerAvailable()
}

// setDockerHost 设置Docker主机
func setDockerHost(host string) error {
	if err := os.Setenv("DOCKER_HOST", host); err != nil {
		return fmt.Errorf("设置DOCKER_HOST环境变量失败: %w", err)
	}
	return nil
}

// setDockerTLS 设置Docker TLS配置
func setDockerTLS(config *DockerConfig) error {
	if config.TLS.Verify {
		if err := os.Setenv("DOCKER_TLS_VERIFY", "1"); err != nil {
			return fmt.Errorf("设置DOCKER_TLS_VERIFY失败: %w", err)
		}
	}

	if config.TLS.CertPath != "" {
		if err := os.Setenv("DOCKER_CERT_PATH", config.TLS.CertPath); err != nil {
			return fmt.Errorf("设置DOCKER_CERT_PATH失败: %w", err)
		}
	}

	return nil
}

// checkDockerAvailable 检查Docker是否可用
func checkDockerAvailable() error {
	cmd := exec.Command("docker", "version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("Docker命令不可用: %w", err)
	}
	return nil
}

// CreateContainer 创建容器
func (dm *DockerManager) CreateContainer(ctx context.Context, config *ContainerConfig) (*Container, error) {
	// 检查Docker是否可用
	if err := checkDockerAvailable(); err != nil {
		return dm.createMockContainer(config)
	}

	// 构建Docker命令
	args := []string{"run", "-d"}

	// 添加名称
	if config.Name != "" {
		args = append(args, "--name", config.Name)
	}

	// 添加环境变量
	for _, env := range config.Env {
		args = append(args, "-e", env)
	}

	// 添加工作目录
	if config.WorkingDir != "" {
		args = append(args, "-w", config.WorkingDir)
	}

	// 添加卷挂载
	for _, vol := range config.Volumes {
		args = append(args, "-v", fmt.Sprintf("%s:%s", vol.Source, vol.Target))
	}

	// 添加端口映射
	for _, port := range config.Ports {
		args = append(args, "-p", fmt.Sprintf("%s:%s", port.HostPort, port.ContainerPort))
	}

	// 添加标签
	for k, v := range config.Labels {
		args = append(args, "--label", fmt.Sprintf("%s=%s", k, v))
	}

	// 自动删除
	if config.AutoRemove {
		args = append(args, "--rm")
	}

	// 特权模式
	if config.Privileged {
		args = append(args, "--privileged")
	}

	// 添加镜像和命令
	args = append(args, config.Image)
	args = append(args, config.Command...)

	// 执行Docker命令
	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("创建容器失败: %w", err)
	}

	containerID := strings.TrimSpace(string(output))

	container := &Container{
		ID:      containerID,
		Name:    config.Name,
		Image:   config.Image,
		Status:  "created",
		Created: time.Now(),
		Labels:  config.Labels,
	}

	dm.logger.Info("容器创建成功", "container_id", containerID, "name", config.Name)
	return container, nil
}

// createMockContainer 创建模拟容器（用于测试）
func (dm *DockerManager) createMockContainer(config *ContainerConfig) (*Container, error) {
	containerID := fmt.Sprintf("mock-%d", time.Now().Unix())

	container := &Container{
		ID:      containerID,
		Name:    config.Name,
		Image:   config.Image,
		Status:  "created",
		Created: time.Now(),
		Labels:  config.Labels,
	}

	dm.logger.Info("创建模拟容器", "container_id", containerID, "name", config.Name)
	return container, nil
}

// StartContainer 启动容器
func (dm *DockerManager) StartContainer(ctx context.Context, containerID string) error {
	if err := checkDockerAvailable(); err != nil {
		dm.logger.Info("模拟启动容器", "container_id", containerID)
		return nil
	}

	cmd := exec.CommandContext(ctx, "docker", "start", containerID)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("启动容器失败: %w", err)
	}

	dm.logger.Info("容器启动成功", "container_id", containerID)
	return nil
}

// StopContainer 停止容器
func (dm *DockerManager) StopContainer(ctx context.Context, containerID string, timeout time.Duration) error {
	if err := checkDockerAvailable(); err != nil {
		dm.logger.Info("模拟停止容器", "container_id", containerID)
		return nil
	}

	timeoutSeconds := int(timeout.Seconds())
	cmd := exec.CommandContext(ctx, "docker", "stop", "-t", fmt.Sprintf("%d", timeoutSeconds), containerID)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("停止容器失败: %w", err)
	}

	dm.logger.Info("容器停止成功", "container_id", containerID)
	return nil
}

// RemoveContainer 删除容器
func (dm *DockerManager) RemoveContainer(ctx context.Context, containerID string, force bool) error {
	if err := checkDockerAvailable(); err != nil {
		dm.logger.Info("模拟删除容器", "container_id", containerID)
		return nil
	}

	args := []string{"rm"}
	if force {
		args = append(args, "-f")
	}
	args = append(args, containerID)

	cmd := exec.CommandContext(ctx, "docker", args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("删除容器失败: %w", err)
	}

	dm.logger.Info("容器删除成功", "container_id", containerID)
	return nil
}

// GetContainerLogs 获取容器日志
func (dm *DockerManager) GetContainerLogs(ctx context.Context, containerID string) (io.ReadCloser, error) {
	if err := checkDockerAvailable(); err != nil {
		// 返回模拟日志
		return io.NopCloser(strings.NewReader("Mock container logs\n")), nil
	}

	cmd := exec.CommandContext(ctx, "docker", "logs", containerID)
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return nil, fmt.Errorf("获取容器日志失败: %w", err)
	}

	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("启动日志命令失败: %w", err)
	}

	return stdout, nil
}

// GetContainerStatus 获取容器状态
func (dm *DockerManager) GetContainerStatus(ctx context.Context, containerID string) (*ContainerStatus, error) {
	if err := checkDockerAvailable(); err != nil {
		// 返回模拟状态
		return &ContainerStatus{
			ID:         containerID,
			Status:     "running",
			State:      "running",
			ExitCode:   0,
			StartedAt:  time.Now(),
			FinishedAt: time.Time{},
			Error:      "",
		}, nil
	}

	cmd := exec.CommandContext(ctx, "docker", "inspect", "--format", "{{.State.Status}}", containerID)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取容器状态失败: %w", err)
	}

	status := strings.TrimSpace(string(output))

	return &ContainerStatus{
		ID:         containerID,
		Status:     status,
		State:      status,
		ExitCode:   0,
		StartedAt:  time.Now(),
		FinishedAt: time.Time{},
		Error:      "",
	}, nil
}

// ExecCommand 执行命令
func (dm *DockerManager) ExecCommand(ctx context.Context, containerID string, cmd []string) (*ExecResult, error) {
	if err := checkDockerAvailable(); err != nil {
		// 返回模拟执行结果
		return &ExecResult{
			ExitCode: 0,
			Stdout:   "Mock command execution output\n",
			Stderr:   "",
		}, nil
	}

	args := []string{"exec", containerID}
	args = append(args, cmd...)

	execCmd := exec.CommandContext(ctx, "docker", args...)
	output, err := execCmd.CombinedOutput()

	exitCode := 0
	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			exitCode = exitError.ExitCode()
		} else {
			return nil, fmt.Errorf("执行命令失败: %w", err)
		}
	}

	result := &ExecResult{
		ExitCode: exitCode,
		Stdout:   string(output),
		Stderr:   "",
	}

	return result, nil
}

// PullImage 拉取镜像
func (dm *DockerManager) PullImage(ctx context.Context, image string) error {
	if err := checkDockerAvailable(); err != nil {
		dm.logger.Info("模拟拉取镜像", "image", image)
		return nil
	}

	cmd := exec.CommandContext(ctx, "docker", "pull", image)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("拉取镜像失败: %w", err)
	}

	dm.logger.Info("镜像拉取成功", "image", image)
	return nil
}

// ImageExists 检查镜像是否存在
func (dm *DockerManager) ImageExists(ctx context.Context, image string) (bool, error) {
	if err := checkDockerAvailable(); err != nil {
		// 模拟镜像存在
		return true, nil
	}

	cmd := exec.CommandContext(ctx, "docker", "image", "inspect", image)
	err := cmd.Run()
	if err != nil {
		// 如果命令失败，假设镜像不存在
		return false, nil
	}

	return true, nil
}

// GetContainerStats 获取容器统计信息
func (dm *DockerManager) GetContainerStats(ctx context.Context, containerID string) (*ContainerStats, error) {
	if err := checkDockerAvailable(); err != nil {
		// 返回模拟统计信息
		return &ContainerStats{
			CPUUsage:    25.5,
			MemoryUsage: 128 * 1024 * 1024, // 128MB
			MemoryLimit: 512 * 1024 * 1024, // 512MB
			NetworkRx:   1024,
			NetworkTx:   2048,
			DiskRead:    4096,
			DiskWrite:   8192,
		}, nil
	}

	// 这里应该解析Docker stats的JSON输出，简化处理
	containerStats := &ContainerStats{
		CPUUsage:    0.0,
		MemoryUsage: 0,
		MemoryLimit: 0,
		NetworkRx:   0,
		NetworkTx:   0,
		DiskRead:    0,
		DiskWrite:   0,
	}

	dm.logger.Debug("获取容器统计信息", "container_id", containerID)
	return containerStats, nil
}
