package config

import (
	"fmt"
	"net/http"
	"time"
)

// 错误码定义
const (
	// 通用错误
	ErrCodeInvalidRequest     = "INVALID_REQUEST"
	ErrCodeUnauthorized       = "UNAUTHORIZED"
	ErrCodeForbidden          = "FORBIDDEN"
	ErrCodeInternalError      = "INTERNAL_ERROR"
	ErrCodeServiceUnavailable = "SERVICE_UNAVAILABLE"

	// 配置相关错误
	ErrCodeConfigNotFound     = "CONFIG_NOT_FOUND"
	ErrCodeConfigExists       = "CONFIG_EXISTS"
	ErrCodeConfigInvalid      = "CONFIG_INVALID"
	ErrCodeConfigLocked       = "CONFIG_LOCKED"
	ErrCodeConfigEncrypted    = "CONFIG_ENCRYPTED"
	ErrCodeConfigDecryptFailed = "CONFIG_DECRYPT_FAILED"

	// 版本相关错误
	ErrCodeVersionNotFound    = "VERSION_NOT_FOUND"
	ErrCodeVersionConflict    = "VERSION_CONFLICT"
	ErrCodeRollbackFailed     = "ROLLBACK_FAILED"

	// 权限相关错误
	ErrCodePermissionDenied   = "PERMISSION_DENIED"
	ErrCodeInvalidScope       = "INVALID_SCOPE"
	ErrCodeScopeNotFound      = "SCOPE_NOT_FOUND"

	// 模板相关错误
	ErrCodeTemplateNotFound   = "TEMPLATE_NOT_FOUND"
	ErrCodeTemplateInvalid    = "TEMPLATE_INVALID"
	ErrCodeRenderFailed       = "RENDER_FAILED"

	// 密钥相关错误
	ErrCodeSecretNotFound     = "SECRET_NOT_FOUND"
	ErrCodeSecretExists       = "SECRET_EXISTS"
	ErrCodeSecretExpired      = "SECRET_EXPIRED"
	ErrCodeRotationFailed     = "ROTATION_FAILED"

	// 分发相关错误
	ErrCodeDistributionFailed = "DISTRIBUTION_FAILED"
	ErrCodeTargetUnreachable  = "TARGET_UNREACHABLE"

	// 验证相关错误
	ErrCodeValidationFailed   = "VALIDATION_FAILED"
	ErrCodeSchemaInvalid      = "SCHEMA_INVALID"

	// 导入导出错误
	ErrCodeExportFailed       = "EXPORT_FAILED"
	ErrCodeImportFailed       = "IMPORT_FAILED"
	ErrCodeFormatUnsupported  = "FORMAT_UNSUPPORTED"
)

// ConfigError 配置服务错误
type ConfigError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Field   string `json:"field,omitempty"`
}

// Error 实现 error 接口
func (e *ConfigError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// HTTPStatus 获取 HTTP 状态码
func (e *ConfigError) HTTPStatus() int {
	switch e.Code {
	case ErrCodeInvalidRequest, ErrCodeConfigInvalid, ErrCodeTemplateInvalid,
		 ErrCodeSchemaInvalid, ErrCodeValidationFailed, ErrCodeFormatUnsupported:
		return http.StatusBadRequest
	case ErrCodeUnauthorized:
		return http.StatusUnauthorized
	case ErrCodeForbidden, ErrCodePermissionDenied:
		return http.StatusForbidden
	case ErrCodeConfigNotFound, ErrCodeVersionNotFound, ErrCodeTemplateNotFound,
		 ErrCodeSecretNotFound, ErrCodeScopeNotFound:
		return http.StatusNotFound
	case ErrCodeConfigExists, ErrCodeSecretExists, ErrCodeVersionConflict:
		return http.StatusConflict
	case ErrCodeConfigLocked:
		return http.StatusLocked
	case ErrCodeServiceUnavailable:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}



// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Value   string `json:"value"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// ValidationErrors 验证错误集合
type ValidationErrors struct {
	Errors []*ValidationError `json:"errors"`
}

// Error 实现 error 接口
func (ve *ValidationErrors) Error() string {
	if len(ve.Errors) == 0 {
		return "validation failed"
	}
	return fmt.Sprintf("validation failed: %s", ve.Errors[0].Message)
}

// Add 添加验证错误
func (ve *ValidationErrors) Add(field, value, message, code string) {
	ve.Errors = append(ve.Errors, &ValidationError{
		Field:   field,
		Value:   value,
		Message: message,
		Code:    code,
	})
}

// HasErrors 是否有错误
func (ve *ValidationErrors) HasErrors() bool {
	return len(ve.Errors) > 0
}

// 预定义错误
var (
	ErrConfigNotFound     = &ConfigError{Code: ErrCodeConfigNotFound, Message: "配置不存在"}
	ErrConfigExists       = &ConfigError{Code: ErrCodeConfigExists, Message: "配置已存在"}
	ErrConfigInvalid      = &ConfigError{Code: ErrCodeConfigInvalid, Message: "配置无效"}
	ErrConfigLocked       = &ConfigError{Code: ErrCodeConfigLocked, Message: "配置已锁定"}
	ErrUnauthorized       = &ConfigError{Code: ErrCodeUnauthorized, Message: "用户未认证"}
	ErrPermissionDenied   = &ConfigError{Code: ErrCodePermissionDenied, Message: "权限不足"}
	ErrInvalidScope       = &ConfigError{Code: ErrCodeInvalidScope, Message: "无效的作用域"}
	ErrTemplateNotFound   = &ConfigError{Code: ErrCodeTemplateNotFound, Message: "模板不存在"}
	ErrSecretNotFound     = &ConfigError{Code: ErrCodeSecretNotFound, Message: "密钥不存在"}
	ErrSecretExists       = &ConfigError{Code: ErrCodeSecretExists, Message: "密钥已存在"}
	ErrVersionNotFound    = &ConfigError{Code: ErrCodeVersionNotFound, Message: "版本不存在"}
	ErrValidationFailed   = &ConfigError{Code: ErrCodeValidationFailed, Message: "验证失败"}
	ErrExportFailed       = &ConfigError{Code: ErrCodeExportFailed, Message: "导出失败"}
	ErrImportFailed       = &ConfigError{Code: ErrCodeImportFailed, Message: "导入失败"}
	ErrDistributionFailed = &ConfigError{Code: ErrCodeDistributionFailed, Message: "分发失败"}
)

// NewConfigError 创建配置错误
func NewConfigError(code, message string) *ConfigError {
	return &ConfigError{
		Code:    code,
		Message: message,
	}
}

// NewConfigErrorWithDetails 创建带详情的配置错误
func NewConfigErrorWithDetails(code, message, details string) *ConfigError {
	return &ConfigError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewConfigErrorWithField 创建带字段的配置错误
func NewConfigErrorWithField(code, message, field string) *ConfigError {
	return &ConfigError{
		Code:    code,
		Message: message,
		Field:   field,
	}
}

// WrapError 包装错误
func WrapError(err error, code, message string) *ConfigError {
	return &ConfigError{
		Code:    code,
		Message: message,
		Details: err.Error(),
	}
}

// IsConfigError 检查是否为配置错误
func IsConfigError(err error) bool {
	_, ok := err.(*ConfigError)
	return ok
}

// GetConfigError 获取配置错误
func GetConfigError(err error) *ConfigError {
	if configErr, ok := err.(*ConfigError); ok {
		return configErr
	}
	return nil
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
	logger Logger
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(logger Logger) *ErrorHandler {
	return &ErrorHandler{
		logger: logger,
	}
}

// HandleError 处理错误
func (h *ErrorHandler) HandleError(err error, requestID string) *ErrorResponse {
	if configErr := GetConfigError(err); configErr != nil {
		h.logger.Error("配置服务错误", "code", configErr.Code, "message", configErr.Message, "details", configErr.Details)
		return &ErrorResponse{
			Code:      configErr.Code,
			Message:   configErr.Message,
			Details:   configErr.Details,
			Timestamp: time.Now().Format(time.RFC3339),
		}
	}

	// 处理验证错误
	if validationErr, ok := err.(*ValidationErrors); ok {
		h.logger.Error("验证错误", "errors", validationErr.Errors)
		return &ErrorResponse{
			Code:      ErrCodeValidationFailed,
			Message:   "请求参数验证失败",
			Details:   validationErr.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		}
	}

	// 处理其他错误
	h.logger.Error("未知错误", "error", err)
	return &ErrorResponse{
		Code:      ErrCodeInternalError,
		Message:   "服务器内部错误",
		Details:   err.Error(),
		Timestamp: time.Now().Format(time.RFC3339),
	}
}



// ErrorCode 错误码映射
var ErrorCodeMap = map[string]int{
	ErrCodeInvalidRequest:     http.StatusBadRequest,
	ErrCodeUnauthorized:       http.StatusUnauthorized,
	ErrCodeForbidden:          http.StatusForbidden,
	ErrCodeConfigNotFound:     http.StatusNotFound,
	ErrCodeConfigExists:       http.StatusConflict,
	ErrCodeConfigInvalid:      http.StatusBadRequest,
	ErrCodeConfigLocked:       http.StatusLocked,
	ErrCodePermissionDenied:   http.StatusForbidden,
	ErrCodeInvalidScope:       http.StatusBadRequest,
	ErrCodeTemplateNotFound:   http.StatusNotFound,
	ErrCodeSecretNotFound:     http.StatusNotFound,
	ErrCodeSecretExists:       http.StatusConflict,
	ErrCodeVersionNotFound:    http.StatusNotFound,
	ErrCodeValidationFailed:   http.StatusBadRequest,
	ErrCodeExportFailed:       http.StatusInternalServerError,
	ErrCodeImportFailed:       http.StatusInternalServerError,
	ErrCodeDistributionFailed: http.StatusInternalServerError,
	ErrCodeInternalError:      http.StatusInternalServerError,
	ErrCodeServiceUnavailable: http.StatusServiceUnavailable,
}
