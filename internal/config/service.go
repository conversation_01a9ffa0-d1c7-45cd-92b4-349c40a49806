package config

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"text/template"
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Service 配置服务接口
type Service interface {
	// 基础 CRUD
	CreateConfig(ctx context.Context, req *CreateConfigRequest) (*Config, error)
	GetConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*Config, error)
	UpdateConfig(ctx context.Context, configID string, req *UpdateConfigRequest) (*Config, error)
	DeleteConfig(ctx context.Context, configID string) error
	
	// 批量操作
	BatchGetConfigs(ctx context.Context, keys []string, scope ConfigScope, scopeID string, env string) (map[string]*Config, error)
	BatchUpdateConfigs(ctx context.Context, configs []*UpdateConfigRequest) error
	
	// 配置查询
	ListConfigs(ctx context.Context, filter *ConfigFilter) ([]*Config, int64, error)
	SearchConfigs(ctx context.Context, query string, scope ConfigScope, scopeID string) ([]*Config, error)
	
	// 配置渲染
	RenderConfig(ctx context.Context, template string, variables map[string]interface{}) (string, error)
	GetEffectiveConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (interface{}, error)
	
	// 版本管理
	GetConfigVersions(ctx context.Context, configID string) ([]*ConfigVersion, error)
	RollbackConfig(ctx context.Context, configID string, version int64, reason string) (*Config, error)
	
	// 密钥管理
	CreateSecret(ctx context.Context, req *CreateSecretRequest) (*Secret, error)
	GetSecret(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*Secret, error)
	UpdateSecret(ctx context.Context, secretID string, req *UpdateSecretRequest) (*Secret, error)
	DeleteSecret(ctx context.Context, secretID string) error
	RotateSecret(ctx context.Context, secretID string) error
	
	// 功能开关
	CreateFeatureFlag(ctx context.Context, req *CreateFeatureFlagRequest) (*FeatureFlag, error)
	GetFeatureFlag(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*FeatureFlag, error)
	EvaluateFeatureFlag(ctx context.Context, key string, context map[string]interface{}) (interface{}, error)
	
	// 配置模板
	CreateTemplate(ctx context.Context, req *CreateTemplateRequest) (*ConfigTemplate, error)
	RenderTemplate(ctx context.Context, templateID string, variables map[string]interface{}) (string, error)
	
	// 配置分发
	DistributeConfig(ctx context.Context, configID string, targets []string) error
	GetDistributionStatus(ctx context.Context, configID string) ([]*ConfigDistribution, error)

	// 通过ID获取配置
	GetConfigByID(ctx context.Context, configID string) (*Config, error)


}

// ConfigService 配置服务实现
type ConfigService struct {
	db         *gorm.DB
	encryptor  Encryptor
	logger     Logger
	config     ServiceConfig
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
}



// ServiceConfig 服务配置
type ServiceConfig struct {
	EncryptionKey     string `json:"encryption_key"`
	CacheEnabled      bool   `json:"cache_enabled"`
	VersionLimit      int    `json:"version_limit"`
	AuditEnabled      bool   `json:"audit_enabled"`
	ValidationEnabled bool   `json:"validation_enabled"`
}

// NewConfigService 创建配置服务
func NewConfigService(db *gorm.DB, encryptor Encryptor, logger Logger, config ServiceConfig) Service {
	return &ConfigService{
		db:        db,
		encryptor: encryptor,
		logger:    logger,
		config:    config,
	}
}

// CreateConfig 创建配置
func (s *ConfigService) CreateConfig(ctx context.Context, req *CreateConfigRequest) (*Config, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查配置是否已存在
	existing, err := s.GetConfig(ctx, req.Key, req.Scope, req.ScopeID, req.Environment)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("配置已存在: %s", req.Key)
	}
	
	// 处理敏感配置加密
	value := fmt.Sprintf("%v", req.Value)
	encrypted := false
	if req.Type == ConfigTypeSecret || req.Encrypted {
		if s.encryptor != nil {
			encryptedValue, err := s.encryptor.Encrypt(value)
			if err != nil {
				return nil, fmt.Errorf("配置加密失败: %w", err)
			}
			value = encryptedValue
			encrypted = true
		}
	}
	
	// 创建配置
	// 处理 Metadata
	var metadata datatypes.JSON
	if req.Metadata != nil {
		metadataBytes, err := json.Marshal(req.Metadata)
		if err != nil {
			return nil, fmt.Errorf("序列化元数据失败: %w", err)
		}
		metadata = datatypes.JSON(metadataBytes)
	}

	config := &Config{
		ID:          uuid.New().String(),
		Key:         req.Key,
		Value:       value,
		Type:        req.Type,
		Scope:       req.Scope,
		ScopeID:     req.ScopeID,
		Environment: req.Environment,
		Encrypted:   encrypted,
		Description: req.Description,
		Tags:        req.Tags,
		Metadata:    metadata,
		Schema:      req.Schema,
		Version:     1,
		Status:      ConfigStatusActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   req.CreatedBy,
		UpdatedBy:   req.CreatedBy,
	}
	
	// 在事务中创建配置和版本记录
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 创建配置
		if err := tx.Create(config).Error; err != nil {
			return fmt.Errorf("创建配置失败: %w", err)
		}
		
		// 创建版本记录
		version := &ConfigVersion{
			ID:           uuid.New().String(),
			ConfigID:     config.ID,
			Version:      1,
			Value:        value,
			ChangeType:   ChangeTypeCreate,
			ChangeReason: "初始创建",
			ChangedBy:    req.CreatedBy,
			CreatedAt:    time.Now(),
		}
		
		if err := tx.Create(version).Error; err != nil {
			return fmt.Errorf("创建配置版本失败: %w", err)
		}
		
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	// 记录审计日志
	s.logConfigAudit(ctx, &ConfigAuditEvent{
		Action:       AuditActionCreate,
		ResourceType: "config",
		ResourceID:   config.ID,
		ResourceKey:  config.Key,
		Scope:        config.Scope,
		ScopeID:      config.ScopeID,
		Environment:  config.Environment,
		UserID:       req.CreatedBy,
		Username:     req.CreatedBy,
		NewValue:     req.Value,
		Success:      true,
		Message:      "创建配置",
	})
	
	s.logger.Info("配置创建成功", "config_id", config.ID, "key", config.Key, "scope", config.Scope)
	return config, nil
}

// DeleteConfig 删除配置
func (s *ConfigService) DeleteConfig(ctx context.Context, configID string) error {
	// 获取配置信息用于审计
	var config Config
	if err := s.db.First(&config, "id = ?", configID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("配置不存在")
		}
		return fmt.Errorf("获取配置失败: %w", err)
	}

	// 软删除：更新状态为已删除
	if err := s.db.Model(&config).Update("status", ConfigStatusDeleted).Error; err != nil {
		return fmt.Errorf("删除配置失败: %w", err)
	}

	s.logger.Info("配置删除成功", "config_id", configID, "key", config.Key)
	return nil
}

// GetConfig 获取配置
func (s *ConfigService) GetConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*Config, error) {
	var config Config
	query := s.db.Where("key = ? AND scope = ? AND environment = ? AND status = ?", 
		key, scope, env, ConfigStatusActive)
	
	if scopeID != "" {
		query = query.Where("scope_id = ?", scopeID)
	} else {
		query = query.Where("scope_id IS NULL OR scope_id = ''")
	}
	
	err := query.First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("配置不存在: %s", key)
		}
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}
	
	// 解密敏感配置
	if config.Encrypted && s.encryptor != nil {
		decryptedValue, err := s.encryptor.Decrypt(config.Value)
		if err != nil {
			s.logger.Error("配置解密失败", "error", err, "config_id", config.ID)
			return nil, fmt.Errorf("配置解密失败")
		}
		config.Value = decryptedValue
	}
	
	return &config, nil
}

// GetEffectiveConfig 获取有效配置 (考虑继承和优先级)
func (s *ConfigService) GetEffectiveConfig(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (interface{}, error) {
	// 配置查找优先级: 应用 > 租户 > 平台 > 全局
	scopes := []ConfigScope{scope}
	
	// 根据当前作用域添加上级作用域
	switch scope {
	case ConfigScopeApplication:
		scopes = append(scopes, ConfigScopeTenant, ConfigScopePlatform, ConfigScopeGlobal)
	case ConfigScopeTenant:
		scopes = append(scopes, ConfigScopePlatform, ConfigScopeGlobal)
	case ConfigScopePlatform:
		scopes = append(scopes, ConfigScopeGlobal)
	}
	
	// 按优先级查找配置
	for _, currentScope := range scopes {
		currentScopeID := scopeID
		if currentScope != scope {
			// 上级作用域使用对应的 ID
			currentScopeID = s.getParentScopeID(ctx, scope, scopeID, currentScope)
		}
		
		config, err := s.GetConfig(ctx, key, currentScope, currentScopeID, env)
		if err == nil {
			return config.Value, nil
		}
	}
	
	return nil, fmt.Errorf("配置不存在: %s", key)
}

// UpdateConfig 更新配置
func (s *ConfigService) UpdateConfig(ctx context.Context, configID string, req *UpdateConfigRequest) (*Config, error) {
	// 获取现有配置
	var config Config
	err := s.db.First(&config, "id = ?", configID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("配置不存在: %s", configID)
		}
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}
	
	// 保存旧值用于审计
	oldValue := config.Value
	if config.Encrypted && s.encryptor != nil {
		if decrypted, err := s.encryptor.Decrypt(config.Value); err == nil {
			oldValue = decrypted
		}
	}
	
	// 处理新值加密
	var newValue string
	encrypted := config.Encrypted
	if req.Value != nil {
		newValue = fmt.Sprintf("%v", req.Value)
		if config.Type == ConfigTypeSecret || config.Encrypted {
			if s.encryptor != nil {
				encryptedValue, err := s.encryptor.Encrypt(newValue)
				if err != nil {
					return nil, fmt.Errorf("配置加密失败: %w", err)
				}
				newValue = encryptedValue
				encrypted = true
			}
		}
	}
	
	// 在事务中更新配置和创建版本记录
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 更新配置
		updates := map[string]interface{}{
			"updated_at": time.Now(),
			"updated_by": req.UpdatedBy,
			"version":    gorm.Expr("version + 1"),
		}
		
		if req.Value != nil {
			updates["value"] = newValue
			updates["encrypted"] = encrypted
		}
		if req.Description != nil {
			updates["description"] = *req.Description
		}
		if req.Tags != nil {
			updates["tags"] = req.Tags
		}
		if req.Metadata != nil {
			metadataBytes, err := json.Marshal(req.Metadata)
			if err != nil {
				return fmt.Errorf("序列化元数据失败: %w", err)
			}
			updates["metadata"] = datatypes.JSON(metadataBytes)
		}
		
		if err := tx.Model(&config).Updates(updates).Error; err != nil {
			return fmt.Errorf("更新配置失败: %w", err)
		}
		
		// 创建版本记录
		version := &ConfigVersion{
			ID:           uuid.New().String(),
			ConfigID:     configID,
			Version:      config.Version + 1,
			Value:        newValue,
			ChangeType:   ChangeTypeUpdate,
			ChangeReason: req.ChangeReason,
			ChangedBy:    req.UpdatedBy,
			CreatedAt:    time.Now(),
		}
		
		if err := tx.Create(version).Error; err != nil {
			return fmt.Errorf("创建配置版本失败: %w", err)
		}
		
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	// 记录审计日志
	s.logConfigAudit(ctx, &ConfigAuditEvent{
		Action:       AuditActionUpdate,
		ResourceType: "config",
		ResourceID:   configID,
		ResourceKey:  config.Key,
		Scope:        config.Scope,
		ScopeID:      config.ScopeID,
		Environment:  config.Environment,
		UserID:       req.UpdatedBy,
		Username:     req.UpdatedBy,
		OldValue:     oldValue,
		NewValue:     req.Value,
		Success:      true,
		Message:      "更新配置",
	})
	
	// 重新获取更新后的配置
	updatedConfig, _ := s.GetConfig(ctx, config.Key, config.Scope, config.ScopeID, config.Environment)
	
	s.logger.Info("配置更新成功", "config_id", configID, "key", config.Key)
	return updatedConfig, nil
}

// CreateSecret 创建密钥
func (s *ConfigService) CreateSecret(ctx context.Context, req *CreateSecretRequest) (*Secret, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查密钥是否已存在
	existing, err := s.GetSecret(ctx, req.Key, req.Scope, req.ScopeID, req.Environment)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("密钥已存在: %s", req.Key)
	}
	
	// 加密密钥值
	encryptedValue, err := s.encryptor.Encrypt(req.Value)
	if err != nil {
		return nil, fmt.Errorf("密钥加密失败: %w", err)
	}
	
	// 创建密钥
	secret := &Secret{
		ID:             uuid.New().String(),
		Name:           req.Name,
		Key:            req.Key,
		Value:          encryptedValue,
		Type:           req.Type,
		Scope:          req.Scope,
		ScopeID:        req.ScopeID,
		Environment:    req.Environment,
		Description:    req.Description,
		Tags:           req.Tags,
		Metadata:       req.Metadata,
		ExpiresAt:      req.ExpiresAt,
		RotationPolicy: req.RotationPolicy,
		Version:        1,
		Status:         ConfigStatusActive,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		CreatedBy:      req.CreatedBy,
		UpdatedBy:      req.CreatedBy,
	}
	
	// 在事务中创建密钥和版本记录
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 创建密钥
		if err := tx.Create(secret).Error; err != nil {
			return fmt.Errorf("创建密钥失败: %w", err)
		}
		
		// 创建版本记录
		version := &SecretVersion{
			ID:        uuid.New().String(),
			SecretID:  secret.ID,
			Version:   1,
			Value:     encryptedValue,
			CreatedAt: time.Now(),
			CreatedBy: req.CreatedBy,
		}
		
		if err := tx.Create(version).Error; err != nil {
			return fmt.Errorf("创建密钥版本失败: %w", err)
		}
		
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	s.logger.Info("密钥创建成功", "secret_id", secret.ID, "key", secret.Key, "scope", secret.Scope)
	return secret, nil
}

// GetSecret 获取密钥
func (s *ConfigService) GetSecret(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*Secret, error) {
	var secret Secret
	query := s.db.Where("key = ? AND scope = ? AND environment = ? AND status = ?", 
		key, scope, env, ConfigStatusActive)
	
	if scopeID != "" {
		query = query.Where("scope_id = ?", scopeID)
	} else {
		query = query.Where("scope_id IS NULL OR scope_id = ''")
	}
	
	err := query.First(&secret).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("密钥不存在: %s", key)
		}
		return nil, fmt.Errorf("获取密钥失败: %w", err)
	}
	
	// 解密密钥值
	if s.encryptor != nil {
		decryptedValue, err := s.encryptor.Decrypt(secret.Value)
		if err != nil {
			s.logger.Error("密钥解密失败", "error", err, "secret_id", secret.ID)
			return nil, fmt.Errorf("密钥解密失败")
		}
		secret.Value = decryptedValue
	}
	
	return &secret, nil
}

// RenderConfig 渲染配置模板
func (s *ConfigService) RenderConfig(ctx context.Context, templateStr string, variables map[string]interface{}) (string, error) {
	// 创建模板
	tmpl, err := template.New("config").Funcs(s.getTemplateFuncs()).Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("模板解析失败: %w", err)
	}
	
	// 渲染模板
	var result strings.Builder
	if err := tmpl.Execute(&result, variables); err != nil {
		return "", fmt.Errorf("模板渲染失败: %w", err)
	}
	
	return result.String(), nil
}

// EvaluateFeatureFlag 评估功能开关
func (s *ConfigService) EvaluateFeatureFlag(ctx context.Context, key string, evalContext map[string]interface{}) (interface{}, error) {
	// 获取功能开关
	flag, err := s.GetFeatureFlag(ctx, key, ConfigScopeGlobal, "", "default")
	if err != nil {
		return nil, err
	}
	
	if !flag.Enabled {
		return s.getDefaultValue(flag.Type), nil
	}
	
	// 评估规则
	for _, rule := range flag.Rules {
		if !rule.Enabled {
			continue
		}
		
		// 检查规则条件
		if s.evaluateConditions(rule.Conditions, evalContext) {
			return rule.Value, nil
		}
	}
	
	// 检查灰度发布
	if flag.Rollout != nil {
		if s.evaluateRollout(flag.Rollout, evalContext) {
			return s.getDefaultValue(flag.Type), nil
		}
	}
	
	return s.getDefaultValue(flag.Type), nil
}

// 辅助方法

// getTemplateFuncs 获取模板函数
func (s *ConfigService) getTemplateFuncs() template.FuncMap {
	return template.FuncMap{
		"secret": func(key string) string {
			// 从密钥存储获取值
			parts := strings.Split(key, ".")
			if len(parts) >= 2 {
				scope := ConfigScopeGlobal
				scopeID := ""
				env := "default"
				
				secret, err := s.GetSecret(context.Background(), key, scope, scopeID, env)
				if err != nil {
					s.logger.Error("模板函数获取密钥失败", "error", err, "key", key)
					return ""
				}
				return secret.Value
			}
			return ""
		},
		"default": func(value, defaultValue interface{}) interface{} {
			if value == nil || value == "" {
				return defaultValue
			}
			return value
		},
		"env": func(key string) string {
			// 从环境变量获取值
			return ""
		},
	}
}

// getParentScopeID 获取父级作用域ID
func (s *ConfigService) getParentScopeID(ctx context.Context, currentScope ConfigScope, currentScopeID string, targetScope ConfigScope) string {
	// TODO: 实现作用域层次关系查找
	// 例如: 应用 -> 租户 -> 平台 -> 全局
	switch targetScope {
	case ConfigScopeGlobal:
		return ""
	case ConfigScopePlatform:
		return ""
	case ConfigScopeTenant:
		// 从应用获取租户ID
		if currentScope == ConfigScopeApplication {
			// TODO: 查询应用的租户ID
			return "tenant-123"
		}
		return currentScopeID
	default:
		return currentScopeID
	}
}

// GetConfigByID 通过ID获取配置
func (s *ConfigService) GetConfigByID(ctx context.Context, configID string) (*Config, error) {
	var config Config
	err := s.db.First(&config, "id = ? AND status = ?", configID, ConfigStatusActive).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("配置不存在: %s", configID)
		}
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}

	// 解密敏感配置
	if config.Encrypted && s.encryptor != nil {
		decryptedValue, err := s.encryptor.Decrypt(config.Value)
		if err != nil {
			s.logger.Error("配置解密失败", "error", err, "config_id", configID)
			// 不返回错误，但记录日志
		} else {
			config.Value = decryptedValue
		}
	}

	return &config, nil
}

// ListConfigs 获取配置列表
func (s *ConfigService) ListConfigs(ctx context.Context, filter *ConfigFilter) ([]*Config, int64, error) {
	query := s.db.Model(&Config{}).Where("status = ?", ConfigStatusActive)

	// 应用过滤条件
	if filter.Scope != "" {
		query = query.Where("scope = ?", filter.Scope)
	}
	if filter.ScopeID != "" {
		query = query.Where("scope_id = ?", filter.ScopeID)
	}
	if filter.Environment != "" {
		query = query.Where("environment = ?", filter.Environment)
	}
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if len(filter.Tags) > 0 {
		// PostgreSQL JSON 查询
		for _, tag := range filter.Tags {
			query = query.Where("tags @> ?", fmt.Sprintf(`["%s"]`, tag))
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取配置总数失败: %w", err)
	}

	// 分页查询
	var configs []*Config
	offset := (filter.Page - 1) * filter.PageSize
	err := query.Offset(offset).Limit(filter.PageSize).Order("created_at DESC").Find(&configs).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取配置列表失败: %w", err)
	}

	// 解密敏感配置
	for _, config := range configs {
		if config.Encrypted && s.encryptor != nil {
			if decryptedValue, err := s.encryptor.Decrypt(config.Value); err == nil {
				config.Value = decryptedValue
			}
		}
	}

	return configs, total, nil
}

// SearchConfigs 搜索配置
func (s *ConfigService) SearchConfigs(ctx context.Context, query string, scope ConfigScope, scopeID string) ([]*Config, error) {
	dbQuery := s.db.Where("status = ?", ConfigStatusActive)

	// 搜索条件
	searchPattern := "%" + query + "%"
	dbQuery = dbQuery.Where("key ILIKE ? OR description ILIKE ?", searchPattern, searchPattern)

	// 作用域过滤
	if scope != "" {
		dbQuery = dbQuery.Where("scope = ?", scope)
	}
	if scopeID != "" {
		dbQuery = dbQuery.Where("scope_id = ?", scopeID)
	}

	var configs []*Config
	err := dbQuery.Order("key ASC").Find(&configs).Error
	if err != nil {
		return nil, fmt.Errorf("搜索配置失败: %w", err)
	}

	// 解密敏感配置
	for _, config := range configs {
		if config.Encrypted && s.encryptor != nil {
			if decryptedValue, err := s.encryptor.Decrypt(config.Value); err == nil {
				config.Value = decryptedValue
			}
		}
	}

	return configs, nil
}

// BatchGetConfigs 批量获取配置
func (s *ConfigService) BatchGetConfigs(ctx context.Context, keys []string, scope ConfigScope, scopeID string, env string) (map[string]*Config, error) {
	if len(keys) == 0 {
		return make(map[string]*Config), nil
	}

	query := s.db.Where("key IN ? AND scope = ? AND environment = ? AND status = ?",
		keys, scope, env, ConfigStatusActive)

	if scopeID != "" {
		query = query.Where("scope_id = ?", scopeID)
	} else {
		query = query.Where("scope_id IS NULL OR scope_id = ''")
	}

	var configs []*Config
	err := query.Find(&configs).Error
	if err != nil {
		return nil, fmt.Errorf("批量获取配置失败: %w", err)
	}

	// 转换为 map
	result := make(map[string]*Config)
	for _, config := range configs {
		// 解密敏感配置
		if config.Encrypted && s.encryptor != nil {
			if decryptedValue, err := s.encryptor.Decrypt(config.Value); err == nil {
				config.Value = decryptedValue
			}
		}
		result[config.Key] = config
	}

	return result, nil
}

// BatchUpdateConfigs 批量更新配置
func (s *ConfigService) BatchUpdateConfigs(ctx context.Context, configs []*UpdateConfigRequest) error {
	if len(configs) == 0 {
		return nil
	}

	// 在事务中执行批量更新
	return s.db.Transaction(func(tx *gorm.DB) error {
		for _, req := range configs {
			// 获取现有配置
			var config Config
			err := tx.First(&config, "id = ?", req.ConfigID).Error
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					s.logger.Warn("批量更新中配置不存在", "config_id", req.ConfigID)
					continue
				}
				return fmt.Errorf("获取配置失败: %w", err)
			}

			// 更新配置
			updates := make(map[string]interface{})
			if req.Value != nil {
				// 处理加密
				value := req.Value
				if config.Encrypted && s.encryptor != nil {
					encryptedValue, err := s.encryptor.Encrypt(fmt.Sprintf("%v", req.Value))
					if err != nil {
						return fmt.Errorf("配置加密失败: %w", err)
					}
					value = encryptedValue
				}
				updates["value"] = value
			}
			if req.Description != nil {
				updates["description"] = *req.Description
			}
			if req.Tags != nil {
				updates["tags"] = req.Tags
			}
			if req.Metadata != nil {
				updates["metadata"] = req.Metadata
			}
			updates["updated_at"] = time.Now()
			updates["updated_by"] = req.UpdatedBy
			updates["version"] = gorm.Expr("version + 1")

			// 执行更新
			if err := tx.Model(&config).Updates(updates).Error; err != nil {
				return fmt.Errorf("更新配置失败: %w", err)
			}
		}
		return nil
	})
}

// CreateFeatureFlag 创建功能开关
func (s *ConfigService) CreateFeatureFlag(ctx context.Context, req *CreateFeatureFlagRequest) (*FeatureFlag, error) {
	// 简化实现
	flag := &FeatureFlag{
		ID:          generateID(),
		Key:         req.Key,
		Enabled:     req.Enabled,
		Scope:       req.Scope,
		ScopeID:     req.ScopeID,
		Environment: req.Environment,
		Description: req.Description,
		CreatedAt:   time.Now(),
		CreatedBy:   req.CreatedBy,
	}

	if err := s.db.Create(flag).Error; err != nil {
		return nil, fmt.Errorf("创建功能开关失败: %w", err)
	}

	return flag, nil
}

// GetFeatureFlag 获取功能开关
func (s *ConfigService) GetFeatureFlag(ctx context.Context, key string, scope ConfigScope, scopeID string, env string) (*FeatureFlag, error) {
	var flag FeatureFlag
	query := s.db.Where("key = ? AND scope = ? AND environment = ?", key, scope, env)

	if scopeID != "" {
		query = query.Where("scope_id = ?", scopeID)
	} else {
		query = query.Where("scope_id IS NULL OR scope_id = ''")
	}

	err := query.First(&flag).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("功能开关不存在")
		}
		return nil, fmt.Errorf("获取功能开关失败: %w", err)
	}

	return &flag, nil
}

// CreateTemplate 创建配置模板
func (s *ConfigService) CreateTemplate(ctx context.Context, req *CreateTemplateRequest) (*ConfigTemplate, error) {
	// 简化实现
	template := &ConfigTemplate{
		ID:          generateID(),
		Name:        req.Name,
		Description: req.Description,
		Template:    req.Template,
		Variables:   req.Variables,
		CreatedAt:   time.Now(),
		CreatedBy:   req.CreatedBy,
	}

	if err := s.db.Create(template).Error; err != nil {
		return nil, fmt.Errorf("创建配置模板失败: %w", err)
	}

	return template, nil
}

// RenderTemplate 渲染模板
func (s *ConfigService) RenderTemplate(ctx context.Context, templateID string, variables map[string]interface{}) (string, error) {
	// 简化实现
	return "rendered template", nil
}



// UpdateSecret 更新密钥
func (s *ConfigService) UpdateSecret(ctx context.Context, secretID string, req *UpdateSecretRequest) (*Secret, error) {
	// 简化实现
	var secret Secret
	if err := s.db.First(&secret, "id = ?", secretID).Error; err != nil {
		return nil, fmt.Errorf("密钥不存在")
	}

	if req.Value != nil {
		secret.Value = *req.Value
	}
	if req.Description != nil {
		secret.Description = *req.Description
	}
	secret.UpdatedAt = time.Now()
	secret.UpdatedBy = req.UpdatedBy

	if err := s.db.Save(&secret).Error; err != nil {
		return nil, fmt.Errorf("更新密钥失败: %w", err)
	}

	return &secret, nil
}

// DeleteSecret 删除密钥
func (s *ConfigService) DeleteSecret(ctx context.Context, secretID string) error {
	if err := s.db.Delete(&Secret{}, "id = ?", secretID).Error; err != nil {
		return fmt.Errorf("删除密钥失败: %w", err)
	}
	return nil
}

// RotateSecret 轮换密钥
func (s *ConfigService) RotateSecret(ctx context.Context, secretID string) error {
	// 简化实现
	return nil
}

// GetConfigVersions 获取配置版本历史
func (s *ConfigService) GetConfigVersions(ctx context.Context, configID string) ([]*ConfigVersion, error) {
	var versions []*ConfigVersion
	err := s.db.Where("config_id = ?", configID).Order("version DESC").Find(&versions).Error
	if err != nil {
		return nil, fmt.Errorf("获取配置版本失败: %w", err)
	}
	return versions, nil
}

// RollbackConfig 回滚配置
func (s *ConfigService) RollbackConfig(ctx context.Context, configID string, version int64, reason string) (*Config, error) {
	// 获取指定版本的配置
	var configVersion ConfigVersion
	err := s.db.Where("config_id = ? AND version = ?", configID, version).First(&configVersion).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("配置版本不存在")
		}
		return nil, fmt.Errorf("获取配置版本失败: %w", err)
	}

	// 获取当前配置
	var config Config
	err = s.db.First(&config, "id = ?", configID).Error
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}

	// 保存当前值到版本历史
	currentVersion := &ConfigVersion{
		ID:           generateID(),
		ConfigID:     configID,
		Version:      config.Version,
		Value:        config.Value,
		ChangeType:   ChangeTypeRollback,
		ChangeReason: reason,
		ChangedBy:    "system", // 简化处理
		CreatedAt:    time.Now(),
	}

	if err := s.db.Create(currentVersion).Error; err != nil {
		return nil, fmt.Errorf("保存版本历史失败: %w", err)
	}

	// 更新配置为指定版本的值
	config.Value = fmt.Sprintf("%v", configVersion.Value)
	config.Version = config.Version + 1
	config.UpdatedAt = time.Now()
	config.UpdatedBy = "system" // 简化处理

	if err := s.db.Save(&config).Error; err != nil {
		return nil, fmt.Errorf("回滚配置失败: %w", err)
	}

	s.logger.Info("配置回滚成功", "config_id", configID, "version", version)
	return &config, nil
}

// ValidateConfig 验证配置
func (s *ConfigService) ValidateConfig(ctx context.Context, config *Config) error {
	// 简化实现
	if config.Key == "" {
		return fmt.Errorf("配置键不能为空")
	}
	if config.Value == "" {
		return fmt.Errorf("配置值不能为空")
	}
	return nil
}

// logConfigAudit 记录配置审计日志
func (s *ConfigService) logConfigAudit(ctx context.Context, event *ConfigAuditEvent) {
	if !s.config.AuditEnabled {
		return
	}

	audit := &ConfigAudit{
		ID:           generateID(),
		Action:       event.Action,
		Level:        AuditLevelInfo,
		ResourceType: event.ResourceType,
		ResourceID:   event.ResourceID,
		ResourceKey:  event.ResourceKey,
		Scope:        event.Scope,
		ScopeID:      event.ScopeID,
		Environment:  event.Environment,
		UserID:       event.UserID,
		Username:     event.Username,
		UserIP:       event.UserIP,
		UserAgent:    event.UserAgent,
		OldValue:     event.OldValue,
		NewValue:     event.NewValue,
		Changes:      event.Changes,
		Message:      event.Message,
		Success:      event.Success,
		ErrorCode:    event.ErrorCode,
		ErrorMessage: event.ErrorMessage,
		CreatedAt:    time.Now(),
	}

	if err := s.db.Create(audit).Error; err != nil {
		s.logger.Error("记录配置审计日志失败", "error", err)
	}
}

// generateID 生成唯一ID
func generateID() string {
	return uuid.New().String()
}

// evaluateConditions 评估功能开关条件
func (s *ConfigService) evaluateConditions(conditions []FeatureFlagCondition, context map[string]interface{}) bool {
	for _, condition := range conditions {
		if !s.evaluateCondition(condition, context) {
			return false
		}
	}
	return true
}

// evaluateCondition 评估单个条件
func (s *ConfigService) evaluateCondition(condition FeatureFlagCondition, context map[string]interface{}) bool {
	value, exists := context[condition.Attribute]
	if !exists {
		return false
	}
	
	switch condition.Operator {
	case "eq":
		return value == condition.Value
	case "ne":
		return value != condition.Value
	case "in":
		if list, ok := condition.Value.([]interface{}); ok {
			for _, item := range list {
				if value == item {
					return true
				}
			}
		}
		return false
	case "not_in":
		if list, ok := condition.Value.([]interface{}); ok {
			for _, item := range list {
				if value == item {
					return false
				}
			}
		}
		return true
	case "contains":
		if str, ok := value.(string); ok {
			if substr, ok := condition.Value.(string); ok {
				return strings.Contains(str, substr)
			}
		}
		return false
	default:
		return false
	}
}

// evaluateRollout 评估灰度发布
func (s *ConfigService) evaluateRollout(rollout *RolloutConfig, context map[string]interface{}) bool {
	switch rollout.Strategy {
	case "percentage":
		// TODO: 实现基于百分比的灰度逻辑
		return false
	case "user_list":
		if userID, ok := context["user_id"].(string); ok {
			for _, id := range rollout.UserList {
				if id == userID {
					return true
				}
			}
		}
		return false
	case "tenant_list":
		if tenantID, ok := context["tenant_id"].(string); ok {
			for _, id := range rollout.TenantList {
				if id == tenantID {
					return true
				}
			}
		}
		return false
	default:
		return false
	}
}

// getDefaultValue 获取默认值
func (s *ConfigService) getDefaultValue(flagType FeatureFlagType) interface{} {
	switch flagType {
	case FeatureFlagTypeBool:
		return false
	case FeatureFlagTypeString:
		return ""
	case FeatureFlagTypeNumber:
		return 0
	case FeatureFlagTypeJSON:
		return map[string]interface{}{}
	default:
		return nil
	}
}

// DistributeConfig 分发配置
func (s *ConfigService) DistributeConfig(ctx context.Context, configID string, targets []string) error {
	// 简化实现
	return nil
}

// GetDistributionStatus 获取分发状态
func (s *ConfigService) GetDistributionStatus(ctx context.Context, configID string) ([]*ConfigDistribution, error) {
	// 简化实现
	return []*ConfigDistribution{}, nil
}

