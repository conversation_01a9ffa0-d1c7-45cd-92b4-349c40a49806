package config

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

// ConfigWatcher 配置监听器
type ConfigWatcher struct {
	redis     *redis.Client
	wsManager *WebSocketManager
	logger    Logger
	
	subscribers map[string][]chan *ConfigChangeEvent
	mutex       sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
}



// NewConfigWatcher 创建配置监听器
func NewConfigWatcher(redis *redis.Client, wsManager *WebSocketManager, logger Logger) *ConfigWatcher {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &ConfigWatcher{
		redis:       redis,
		wsManager:   wsManager,
		logger:      logger,
		subscribers: make(map[string][]chan *ConfigChangeEvent),
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start 启动监听器
func (w *ConfigWatcher) Start() {
	w.logger.Info("配置监听器启动")
	
	// 订阅 Redis 配置变更通道
	pubsub := w.redis.Subscribe(w.ctx, "config:changes")
	defer pubsub.Close()
	
	// 处理消息
	for {
		select {
		case <-w.ctx.Done():
			w.logger.Info("配置监听器停止")
			return
		default:
			msg, err := pubsub.ReceiveMessage(w.ctx)
			if err != nil {
				if err == context.Canceled {
					return
				}
				w.logger.Error("接收配置变更消息失败", "error", err)
				time.Sleep(time.Second)
				continue
			}
			
			w.handleConfigChange(msg.Payload)
		}
	}
}

// Stop 停止监听器
func (w *ConfigWatcher) Stop() {
	w.cancel()
	
	// 关闭所有订阅通道
	w.mutex.Lock()
	defer w.mutex.Unlock()
	
	for key, channels := range w.subscribers {
		for _, ch := range channels {
			close(ch)
		}
		delete(w.subscribers, key)
	}
}

// Subscribe 订阅配置变更
func (w *ConfigWatcher) Subscribe(key string, scope ConfigScope, scopeID string) (<-chan *ConfigChangeEvent, error) {
	subscriptionKey := w.buildSubscriptionKey(key, scope, scopeID)
	
	ch := make(chan *ConfigChangeEvent, 100) // 缓冲通道
	
	w.mutex.Lock()
	defer w.mutex.Unlock()
	
	w.subscribers[subscriptionKey] = append(w.subscribers[subscriptionKey], ch)
	
	w.logger.Info("添加配置订阅", "key", key, "scope", scope, "scope_id", scopeID)
	
	return ch, nil
}

// Unsubscribe 取消订阅
func (w *ConfigWatcher) Unsubscribe(key string, scope ConfigScope, scopeID string, ch <-chan *ConfigChangeEvent) {
	subscriptionKey := w.buildSubscriptionKey(key, scope, scopeID)
	
	w.mutex.Lock()
	defer w.mutex.Unlock()
	
	channels := w.subscribers[subscriptionKey]
	for i, subscriber := range channels {
		if subscriber == ch {
			// 移除订阅者
			w.subscribers[subscriptionKey] = append(channels[:i], channels[i+1:]...)
			close(subscriber)
			break
		}
	}
	
	// 如果没有订阅者了，删除键
	if len(w.subscribers[subscriptionKey]) == 0 {
		delete(w.subscribers, subscriptionKey)
	}
	
	w.logger.Info("移除配置订阅", "key", key, "scope", scope, "scope_id", scopeID)
}

// PublishChange 发布配置变更
func (w *ConfigWatcher) PublishChange(event *ConfigChangeEvent) error {
	// 序列化事件
	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("序列化配置变更事件失败: %w", err)
	}
	
	// 发布到 Redis
	err = w.redis.Publish(w.ctx, "config:changes", data).Err()
	if err != nil {
		return fmt.Errorf("发布配置变更事件失败: %w", err)
	}
	
	w.logger.Info("发布配置变更事件", "type", event.Type, "config_id", event.Config.ID)
	
	return nil
}

// handleConfigChange 处理配置变更
func (w *ConfigWatcher) handleConfigChange(payload string) {
	var event ConfigChangeEvent
	if err := json.Unmarshal([]byte(payload), &event); err != nil {
		w.logger.Error("反序列化配置变更事件失败", "error", err, "payload", payload)
		return
	}
	
	w.logger.Info("处理配置变更事件", "type", event.Type, "config_id", event.Config.ID)
	
	// 通知本地订阅者
	w.notifySubscribers(&event)
	
	// 通过 WebSocket 通知客户端
	w.wsManager.BroadcastConfigChange(&event)
}

// notifySubscribers 通知订阅者
func (w *ConfigWatcher) notifySubscribers(event *ConfigChangeEvent) {
	w.mutex.RLock()
	defer w.mutex.RUnlock()
	
	// 构建可能的订阅键
	keys := []string{
		w.buildSubscriptionKey(event.Config.Key, event.Config.Scope, event.Config.ScopeID),
		w.buildSubscriptionKey("*", event.Config.Scope, event.Config.ScopeID),
		w.buildSubscriptionKey(event.Config.Key, "*", "*"),
		w.buildSubscriptionKey("*", "*", "*"),
	}
	
	// 通知所有匹配的订阅者
	for _, key := range keys {
		if channels, exists := w.subscribers[key]; exists {
			for _, ch := range channels {
				select {
				case ch <- event:
				default:
					// 通道已满，跳过
					w.logger.Warn("配置变更通知通道已满", "subscription_key", key)
				}
			}
		}
	}
}

// buildSubscriptionKey 构建订阅键
func (w *ConfigWatcher) buildSubscriptionKey(key string, scope ConfigScope, scopeID string) string {
	return fmt.Sprintf("%s:%s:%s", key, scope, scopeID)
}

// ConfigChangeNotifier 配置变更通知器
type ConfigChangeNotifier struct {
	watcher *ConfigWatcher
	logger  Logger
}

// NewConfigChangeNotifier 创建配置变更通知器
func NewConfigChangeNotifier(watcher *ConfigWatcher, logger Logger) *ConfigChangeNotifier {
	return &ConfigChangeNotifier{
		watcher: watcher,
		logger:  logger,
	}
}

// NotifyConfigCreated 通知配置创建
func (n *ConfigChangeNotifier) NotifyConfigCreated(config *Config, createdBy string) error {
	event := &ConfigChangeEvent{
		Type:         ChangeTypeCreate,
		Config:       config,
		NewValue:     config.Value,
		Timestamp:    time.Now(),
		ChangedBy:    createdBy,
		ChangeReason: "配置创建",
	}
	
	return n.watcher.PublishChange(event)
}

// NotifyConfigUpdated 通知配置更新
func (n *ConfigChangeNotifier) NotifyConfigUpdated(config *Config, oldValue interface{}, updatedBy string, reason string) error {
	event := &ConfigChangeEvent{
		Type:         ChangeTypeUpdate,
		Config:       config,
		OldValue:     oldValue,
		NewValue:     config.Value,
		Timestamp:    time.Now(),
		ChangedBy:    updatedBy,
		ChangeReason: reason,
	}
	
	return n.watcher.PublishChange(event)
}

// NotifyConfigDeleted 通知配置删除
func (n *ConfigChangeNotifier) NotifyConfigDeleted(config *Config, deletedBy string) error {
	event := &ConfigChangeEvent{
		Type:         ChangeTypeDelete,
		Config:       config,
		OldValue:     config.Value,
		Timestamp:    time.Now(),
		ChangedBy:    deletedBy,
		ChangeReason: "配置删除",
	}
	
	return n.watcher.PublishChange(event)
}

// ConfigSubscriptionManager 配置订阅管理器
type ConfigSubscriptionManager struct {
	watcher       *ConfigWatcher
	subscriptions map[string]map[string]<-chan *ConfigChangeEvent // clientID -> subscriptionKey -> channel
	mutex         sync.RWMutex
	logger        Logger
}

// NewConfigSubscriptionManager 创建配置订阅管理器
func NewConfigSubscriptionManager(watcher *ConfigWatcher, logger Logger) *ConfigSubscriptionManager {
	return &ConfigSubscriptionManager{
		watcher:       watcher,
		subscriptions: make(map[string]map[string]<-chan *ConfigChangeEvent),
		logger:        logger,
	}
}

// AddSubscription 添加订阅
func (m *ConfigSubscriptionManager) AddSubscription(clientID, key string, scope ConfigScope, scopeID string) error {
	ch, err := m.watcher.Subscribe(key, scope, scopeID)
	if err != nil {
		return err
	}
	
	subscriptionKey := fmt.Sprintf("%s:%s:%s", key, scope, scopeID)
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if m.subscriptions[clientID] == nil {
		m.subscriptions[clientID] = make(map[string]<-chan *ConfigChangeEvent)
	}
	
	m.subscriptions[clientID][subscriptionKey] = ch
	
	m.logger.Info("添加客户端配置订阅", "client_id", clientID, "subscription_key", subscriptionKey)
	
	return nil
}

// RemoveSubscription 移除订阅
func (m *ConfigSubscriptionManager) RemoveSubscription(clientID, key string, scope ConfigScope, scopeID string) {
	subscriptionKey := fmt.Sprintf("%s:%s:%s", key, scope, scopeID)
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if clientSubs, exists := m.subscriptions[clientID]; exists {
		if ch, exists := clientSubs[subscriptionKey]; exists {
			m.watcher.Unsubscribe(key, scope, scopeID, ch)
			delete(clientSubs, subscriptionKey)
			
			// 如果客户端没有其他订阅，删除客户端记录
			if len(clientSubs) == 0 {
				delete(m.subscriptions, clientID)
			}
		}
	}
	
	m.logger.Info("移除客户端配置订阅", "client_id", clientID, "subscription_key", subscriptionKey)
}

// RemoveAllSubscriptions 移除客户端的所有订阅
func (m *ConfigSubscriptionManager) RemoveAllSubscriptions(clientID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if clientSubs, exists := m.subscriptions[clientID]; exists {
		for subscriptionKey, ch := range clientSubs {
			// 解析订阅键
			parts := splitSubscriptionKey(subscriptionKey)
			if len(parts) == 3 {
				m.watcher.Unsubscribe(parts[0], ConfigScope(parts[1]), parts[2], ch)
			}
		}
		delete(m.subscriptions, clientID)
	}
	
	m.logger.Info("移除客户端所有配置订阅", "client_id", clientID)
}

// splitSubscriptionKey 分割订阅键
func splitSubscriptionKey(key string) []string {
	// 简单实现，实际应该更健壮
	parts := make([]string, 0, 3)
	current := ""
	colonCount := 0
	
	for _, char := range key {
		if char == ':' && colonCount < 2 {
			parts = append(parts, current)
			current = ""
			colonCount++
		} else {
			current += string(char)
		}
	}
	
	if current != "" {
		parts = append(parts, current)
	}
	
	return parts
}
