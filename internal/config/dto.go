package config

import (
	"fmt"
	"strings"
	"time"
)

// CreateConfigRequest 创建配置请求
type CreateConfigRequest struct {
	Key         string         `json:"key" validate:"required"`
	Value       interface{}    `json:"value" validate:"required"`
	Type        ConfigType     `json:"type" validate:"required"`
	Scope       ConfigScope    `json:"scope" validate:"required"`
	ScopeID     string         `json:"scope_id"`
	Environment string         `json:"environment" validate:"required"`
	Encrypted   bool           `json:"encrypted"`
	Description string         `json:"description"`
	Tags        []string       `json:"tags"`
	Metadata    map[string]string `json:"metadata"`
	Schema      interface{}    `json:"schema"`
	CreatedBy   string         `json:"created_by" validate:"required"`
}

// Validate 验证创建配置请求
func (req *CreateConfigRequest) Validate() error {
	if strings.TrimSpace(req.Key) == "" {
		return fmt.Errorf("配置键不能为空")
	}
	
	if len(req.Key) > 200 {
		return fmt.Errorf("配置键长度不能超过200个字符")
	}
	
	if req.Value == nil {
		return fmt.Errorf("配置值不能为空")
	}
	
	if req.Type == "" {
		return fmt.Errorf("配置类型不能为空")
	}
	
	if req.Scope == "" {
		return fmt.Errorf("配置作用域不能为空")
	}
	
	if strings.TrimSpace(req.Environment) == "" {
		return fmt.Errorf("环境不能为空")
	}
	
	if strings.TrimSpace(req.CreatedBy) == "" {
		return fmt.Errorf("创建者不能为空")
	}
	
	// 验证配置类型
	validTypes := []ConfigType{
		ConfigTypeString, ConfigTypeNumber, ConfigTypeBool,
		ConfigTypeJSON, ConfigTypeYAML, ConfigTypeSecret,
		ConfigTypeFile, ConfigTypeList, ConfigTypeMap,
	}
	if !containsConfigType(validTypes, req.Type) {
		return fmt.Errorf("无效的配置类型: %s", req.Type)
	}
	
	// 验证配置作用域
	validScopes := []ConfigScope{
		ConfigScopeGlobal, ConfigScopePlatform, ConfigScopeTenant,
		ConfigScopeApplication, ConfigScopeService, ConfigScopeUser,
	}
	if !containsConfigScope(validScopes, req.Scope) {
		return fmt.Errorf("无效的配置作用域: %s", req.Scope)
	}
	
	return nil
}

// UpdateConfigRequest 更新配置请求
type UpdateConfigRequest struct {
	ConfigID     string          `json:"config_id"`
	Value        interface{}     `json:"value"`
	Description  *string         `json:"description"`
	Tags         []string        `json:"tags"`
	Metadata     map[string]string `json:"metadata"`
	Schema       interface{}     `json:"schema"`
	ChangeReason string          `json:"change_reason"`
	UpdatedBy    string          `json:"updated_by" validate:"required"`
}

// ConfigFilter 配置过滤器
type ConfigFilter struct {
	Key         string      `json:"key"`
	Scope       ConfigScope `json:"scope"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment"`
	Type        ConfigType  `json:"type"`
	Tags        []string    `json:"tags"`
	Status      ConfigStatus `json:"status"`
	CreatedBy   string      `json:"created_by"`
	Page        int         `json:"page"`
	PageSize    int         `json:"page_size"`
}

// CreateSecretRequest 创建密钥请求
type CreateSecretRequest struct {
	Name           string          `json:"name" validate:"required"`
	Key            string          `json:"key" validate:"required"`
	Value          string          `json:"value" validate:"required"`
	Type           SecretType      `json:"type" validate:"required"`
	Scope          ConfigScope     `json:"scope" validate:"required"`
	ScopeID        string          `json:"scope_id"`
	Environment    string          `json:"environment" validate:"required"`
	Description    string          `json:"description"`
	Tags           []string        `json:"tags"`
	Metadata       map[string]string `json:"metadata"`
	ExpiresAt      *time.Time      `json:"expires_at"`
	RotationPolicy *RotationPolicy `json:"rotation_policy"`
	CreatedBy      string          `json:"created_by" validate:"required"`
}

// Validate 验证创建密钥请求
func (req *CreateSecretRequest) Validate() error {
	if strings.TrimSpace(req.Name) == "" {
		return fmt.Errorf("密钥名称不能为空")
	}
	
	if strings.TrimSpace(req.Key) == "" {
		return fmt.Errorf("密钥键不能为空")
	}
	
	if strings.TrimSpace(req.Value) == "" {
		return fmt.Errorf("密钥值不能为空")
	}
	
	if req.Type == "" {
		return fmt.Errorf("密钥类型不能为空")
	}
	
	if req.Scope == "" {
		return fmt.Errorf("密钥作用域不能为空")
	}
	
	if strings.TrimSpace(req.Environment) == "" {
		return fmt.Errorf("环境不能为空")
	}
	
	if strings.TrimSpace(req.CreatedBy) == "" {
		return fmt.Errorf("创建者不能为空")
	}
	
	return nil
}

// UpdateSecretRequest 更新密钥请求
type UpdateSecretRequest struct {
	Value          *string         `json:"value"`
	Description    *string         `json:"description"`
	Tags           []string        `json:"tags"`
	Metadata       map[string]string `json:"metadata"`
	ExpiresAt      *time.Time      `json:"expires_at"`
	RotationPolicy *RotationPolicy `json:"rotation_policy"`
	UpdatedBy      string          `json:"updated_by" validate:"required"`
}

// CreateFeatureFlagRequest 创建功能开关请求
type CreateFeatureFlagRequest struct {
	Key         string            `json:"key" validate:"required"`
	Name        string            `json:"name" validate:"required"`
	Description string            `json:"description"`
	Type        FeatureFlagType   `json:"type" validate:"required"`
	Enabled     bool              `json:"enabled"`
	Scope       ConfigScope        `json:"scope" binding:"required"`
	ScopeID     string             `json:"scope_id"`
	Environment string             `json:"environment" binding:"required"`
	Rules       []FeatureFlagRule `json:"rules"`
	Rollout     *RolloutConfig    `json:"rollout"`
	Tags        []string          `json:"tags"`
	Metadata    map[string]string `json:"metadata"`
	CreatedBy   string            `json:"created_by" validate:"required"`
}

// UpdateFeatureFlagRequest 更新功能开关请求
type UpdateFeatureFlagRequest struct {
	Name        *string           `json:"name"`
	Description *string           `json:"description"`
	Enabled     *bool             `json:"enabled"`
	Rules       []FeatureFlagRule `json:"rules"`
	Rollout     *RolloutConfig    `json:"rollout"`
	Tags        []string          `json:"tags"`
	Metadata    map[string]string `json:"metadata"`
	UpdatedBy   string            `json:"updated_by" validate:"required"`
}

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Name        string                 `json:"name" validate:"required"`
	Description string                 `json:"description"`
	Category    string                 `json:"category" validate:"required"`
	Template    string                 `json:"template" validate:"required"`
	Variables   []TemplateVariable     `json:"variables"`
	Schema      interface{}            `json:"schema"`
	Tags        []string               `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
	Version     string                 `json:"version" validate:"required"`
	CreatedBy   string                 `json:"created_by" validate:"required"`
}

// RenderTemplateRequest 渲染模板请求
type RenderTemplateRequest struct {
	TemplateID string                 `json:"template_id" validate:"required"`
	Variables  map[string]interface{} `json:"variables" validate:"required"`
}

// ConfigResponse 配置响应
type ConfigResponse struct {
	*Config
	DecryptedValue interface{} `json:"decrypted_value,omitempty"` // 仅在有权限时返回
}

// ConfigListResponse 配置列表响应
type ConfigListResponse struct {
	Configs []*ConfigResponse `json:"configs"`
	Total   int64             `json:"total"`
	Page    int               `json:"page"`
	Size    int               `json:"size"`
}

// SecretResponse 密钥响应
type SecretResponse struct {
	*Secret
	DecryptedValue string `json:"decrypted_value,omitempty"` // 仅在有权限时返回
}

// SecretListResponse 密钥列表响应
type SecretListResponse struct {
	Secrets []*SecretResponse `json:"secrets"`
	Total   int64             `json:"total"`
	Page    int               `json:"page"`
	Size    int               `json:"size"`
}

// FeatureFlagResponse 功能开关响应
type FeatureFlagResponse struct {
	*FeatureFlag
	EvaluationResult interface{} `json:"evaluation_result,omitempty"`
}

// FeatureFlagListResponse 功能开关列表响应
type FeatureFlagListResponse struct {
	Flags []*FeatureFlagResponse `json:"flags"`
	Total int64                  `json:"total"`
	Page  int                    `json:"page"`
	Size  int                    `json:"size"`
}

// TemplateResponse 模板响应
type TemplateResponse struct {
	*ConfigTemplate
	RenderedContent string `json:"rendered_content,omitempty"`
}

// TemplateListResponse 模板列表响应
type TemplateListResponse struct {
	Templates []*TemplateResponse `json:"templates"`
	Total     int64               `json:"total"`
	Page      int                 `json:"page"`
	Size      int                 `json:"size"`
}

// ConfigVersionResponse 配置版本响应
type ConfigVersionResponse struct {
	*ConfigVersion
	ConfigKey string `json:"config_key"`
}

// ConfigVersionListResponse 配置版本列表响应
type ConfigVersionListResponse struct {
	Versions []*ConfigVersionResponse `json:"versions"`
	Total    int64                    `json:"total"`
}

// ConfigSetResponse 配置集响应
type ConfigSetResponse struct {
	*ConfigSet
	ConfigDetails []*ConfigResponse `json:"config_details,omitempty"`
}

// ConfigSnapshotResponse 配置快照响应
type ConfigSnapshotResponse struct {
	*ConfigSnapshot
	ConfigCount int `json:"config_count"`
}

// ConfigAuditLogResponse 配置审计日志响应
type ConfigAuditLogResponse struct {
	*ConfigAuditLog
	ConfigKey string `json:"config_key"`
}

// ConfigAuditLogListResponse 配置审计日志列表响应
type ConfigAuditLogListResponse struct {
	Logs  []*ConfigAuditLogResponse `json:"logs"`
	Total int64                     `json:"total"`
	Page  int                       `json:"page"`
	Size  int                       `json:"size"`
}

// ConfigDistributionResponse 配置分发响应
type ConfigDistributionResponse struct {
	*ConfigDistribution
	ConfigKey string `json:"config_key"`
}

// ConfigDistributionListResponse 配置分发列表响应
type ConfigDistributionListResponse struct {
	Distributions []*ConfigDistributionResponse `json:"distributions"`
	Total         int64                         `json:"total"`
	Page          int                           `json:"page"`
	Size          int                           `json:"size"`
}

// ConfigExportRequest 配置导出请求
type ConfigExportRequest struct {
	Scope       ConfigScope `json:"scope" validate:"required"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment"`
	Format      string      `json:"format" validate:"required"` // json, yaml, env
	IncludeSecrets bool     `json:"include_secrets"`
	Tags        []string    `json:"tags"`
}

// ConfigImportRequest 配置导入请求
type ConfigImportRequest struct {
	Data        string      `json:"data" validate:"required"`
	Format      string      `json:"format" validate:"required"`
	Scope       ConfigScope `json:"scope" validate:"required"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment" validate:"required"`
	Overwrite   bool        `json:"overwrite"`
	DryRun      bool        `json:"dry_run"`
	ImportedBy  string      `json:"imported_by" validate:"required"`
}

// ConfigImportResult 配置导入结果
type ConfigImportResult struct {
	Success     bool     `json:"success"`
	Total       int      `json:"total"`
	Created     int      `json:"created"`
	Updated     int      `json:"updated"`
	Skipped     int      `json:"skipped"`
	Failed      int      `json:"failed"`
	Errors      []string `json:"errors"`
	Warnings    []string `json:"warnings"`
}

// ConfigValidationRequest 配置验证请求
type ConfigValidationRequest struct {
	Key    string      `json:"key" validate:"required"`
	Value  interface{} `json:"value" validate:"required"`
	Type   ConfigType  `json:"type" validate:"required"`
	Schema interface{} `json:"schema"`
}

// ConfigValidationResult 配置验证结果
type ConfigValidationResult struct {
	Valid   bool     `json:"valid"`
	Errors  []string `json:"errors"`
	Warnings []string `json:"warnings"`
}

// ConfigWatchRequest 配置监听请求
type ConfigWatchRequest struct {
	Key         string      `json:"key" validate:"required"`
	Scope       ConfigScope `json:"scope" validate:"required"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment" validate:"required"`
	WebhookURL  string      `json:"webhook_url"`
	Events      []string    `json:"events"`
	Filters     interface{} `json:"filters"`
}

// ConfigChangeEvent 配置变更事件
type ConfigChangeEvent struct {
	Type        ChangeType  `json:"type"`
	Config      *Config     `json:"config"`
	OldValue    interface{} `json:"old_value"`
	NewValue    interface{} `json:"new_value"`
	Timestamp   time.Time   `json:"timestamp"`
	ChangedBy   string      `json:"changed_by"`
	ChangeReason string     `json:"change_reason"`
}

// ConfigStatistics 配置统计
type ConfigStatistics struct {
	TotalConfigs    int64                    `json:"total_configs"`
	ConfigsByScope  map[string]int64         `json:"configs_by_scope"`
	ConfigsByType   map[string]int64         `json:"configs_by_type"`
	ConfigsByEnv    map[string]int64         `json:"configs_by_env"`
	TotalSecrets    int64                    `json:"total_secrets"`
	ExpiredSecrets  int64                    `json:"expired_secrets"`
	TotalTemplates  int64                    `json:"total_templates"`
	TotalFlags      int64                    `json:"total_flags"`
	EnabledFlags    int64                    `json:"enabled_flags"`
}

// ConfigHealthCheck 配置健康检查
type ConfigHealthCheck struct {
	Status           string            `json:"status"`
	DatabaseStatus   string            `json:"database_status"`
	EncryptionStatus string            `json:"encryption_status"`
	CacheStatus      string            `json:"cache_status"`
	Checks           map[string]string `json:"checks"`
	Timestamp        time.Time         `json:"timestamp"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Details   string `json:"details,omitempty"`
	Timestamp string `json:"timestamp"`
}

// SuccessResponse 成功响应
type SuccessResponse struct {
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp string      `json:"timestamp"`
}

// 辅助函数

// containsConfigType 检查配置类型是否在列表中
func containsConfigType(slice []ConfigType, item ConfigType) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// containsConfigScope 检查配置作用域是否在列表中
func containsConfigScope(slice []ConfigScope, item ConfigScope) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetDefaultConfigMetadata 获取默认配置元数据
func GetDefaultConfigMetadata() ConfigMetadata {
	return ConfigMetadata{
		Source:      "manual",
		Priority:    0,
		Sensitive:   false,
		ReadOnly:    false,
		Required:    false,
		Deprecated:  false,
		Labels:      make(map[string]string),
		Annotations: make(map[string]string),
	}
}

// GetDefaultRotationPolicy 获取默认轮换策略
func GetDefaultRotationPolicy() *RotationPolicy {
	return &RotationPolicy{
		Enabled:        false,
		Interval:       30 * 24 * time.Hour, // 30天
		MaxAge:         90 * 24 * time.Hour, // 90天
		NotifyBefore:   7 * 24 * time.Hour,  // 7天
		AutoRotate:     false,
		BackupVersions: 5,
	}
}

// ValidateConfigKey 验证配置键格式
func ValidateConfigKey(key string) error {
	if strings.TrimSpace(key) == "" {
		return fmt.Errorf("配置键不能为空")
	}
	
	if len(key) > 200 {
		return fmt.Errorf("配置键长度不能超过200个字符")
	}
	
	// 配置键只能包含字母、数字、点、下划线和连字符
	for _, char := range key {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '.' || char == '_' || char == '-') {
			return fmt.Errorf("配置键包含无效字符: %c", char)
		}
	}
	
	return nil
}

// ValidateEnvironment 验证环境名称
func ValidateEnvironment(env string) error {
	if strings.TrimSpace(env) == "" {
		return fmt.Errorf("环境名称不能为空")
	}
	
	if len(env) > 50 {
		return fmt.Errorf("环境名称长度不能超过50个字符")
	}
	
	// 环境名称只能包含字母、数字、下划线和连字符
	for _, char := range env {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return fmt.Errorf("环境名称包含无效字符: %c", char)
		}
	}
	
	return nil
}

// BatchGetConfigsRequest 批量获取配置请求
type BatchGetConfigsRequest struct {
	Keys        []string    `json:"keys" binding:"required"`
	Scope       ConfigScope `json:"scope" binding:"required"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment" binding:"required"`
}

// BatchUpdateConfigsRequest 批量更新配置请求
type BatchUpdateConfigsRequest struct {
	Configs []*UpdateConfigRequest `json:"configs" binding:"required"`
}

// RollbackConfigRequest 回滚配置请求
type RollbackConfigRequest struct {
	Version int64  `json:"version" binding:"required"` // 目标版本号
	Reason  string `json:"reason"`                     // 回滚原因
}

// ExportConfigsRequest 导出配置请求
type ExportConfigsRequest struct {
	Scope       ConfigScope `json:"scope"`       // 作用域
	ScopeID     string      `json:"scope_id"`    // 作用域ID
	Environment string      `json:"environment"` // 环境
	Keys        []string    `json:"keys"`        // 指定导出的配置键（为空则导出所有）
	Format      string      `json:"format"`      // 导出格式（json, yaml, env）
}

// ImportConfigsRequest 导入配置请求
type ImportConfigsRequest struct {
	Data        interface{} `json:"data" binding:"required"`        // 配置数据
	Format      string      `json:"format"`                         // 数据格式（json, yaml, env）
	Scope       ConfigScope `json:"scope" binding:"required"`       // 目标作用域
	ScopeID     string      `json:"scope_id"`                       // 目标作用域ID
	Environment string      `json:"environment" binding:"required"` // 目标环境
	Overwrite   bool        `json:"overwrite"`                      // 是否覆盖已存在的配置
	CreatedBy   string      `json:"created_by" binding:"required"`  // 创建者
}

// ConfigVersionsResponse 配置版本历史响应
type ConfigVersionsResponse struct {
	Code    string           `json:"code"`
	Message string           `json:"message"`
	Data    []*ConfigVersion `json:"data"`
}

// ExportConfigsResponse 导出配置响应
type ExportConfigsResponse struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// ImportConfigsResponse 导入配置响应
type ImportConfigsResponse struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// ValidateConfigRequest 验证配置请求
type ValidateConfigRequest struct {
	Key         string      `json:"key" binding:"required"`
	Value       interface{} `json:"value" binding:"required"`
	Type        ConfigType  `json:"type" binding:"required"`
	Scope       ConfigScope `json:"scope" binding:"required"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment" binding:"required"`
	Schema      interface{} `json:"schema"`
}

// ValidateConfigResponse 验证配置响应
type ValidateConfigResponse struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}






