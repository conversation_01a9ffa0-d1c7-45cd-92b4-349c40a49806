package config

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// MetricsCollector 指标收集器
type MetricsCollector struct {
	metrics map[string]*Metric
	mutex   sync.RWMutex
	logger  Logger
}

// Metric 指标
type Metric struct {
	Name        string            `json:"name"`
	Type        MetricType        `json:"type"`
	Value       float64           `json:"value"`
	Labels      map[string]string `json:"labels"`
	Description string            `json:"description"`
	Timestamp   time.Time         `json:"timestamp"`
}

// MetricType 指标类型
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"   // 计数器
	MetricTypeGauge     MetricType = "gauge"     // 仪表盘
	MetricTypeHistogram MetricType = "histogram" // 直方图
	MetricTypeSummary   MetricType = "summary"   // 摘要
)

// ConfigMetrics 配置服务指标
type ConfigMetrics struct {
	// 请求指标
	RequestsTotal       *Metric `json:"requests_total"`
	RequestDuration     *Metric `json:"request_duration"`
	RequestErrors       *Metric `json:"request_errors"`
	
	// 配置指标
	ConfigsTotal        *Metric `json:"configs_total"`
	ConfigReads         *Metric `json:"config_reads"`
	ConfigWrites        *Metric `json:"config_writes"`
	ConfigDeletes       *Metric `json:"config_deletes"`
	ConfigCacheHits     *Metric `json:"config_cache_hits"`
	ConfigCacheMisses   *Metric `json:"config_cache_misses"`
	
	// 密钥指标
	SecretsTotal        *Metric `json:"secrets_total"`
	SecretAccesses      *Metric `json:"secret_accesses"`
	SecretRotations     *Metric `json:"secret_rotations"`
	
	// 模板指标
	TemplatesTotal      *Metric `json:"templates_total"`
	TemplateRenders     *Metric `json:"template_renders"`
	TemplateErrors      *Metric `json:"template_errors"`
	
	// WebSocket 指标
	WebSocketConnections *Metric `json:"websocket_connections"`
	WebSocketMessages    *Metric `json:"websocket_messages"`
	
	// 系统指标
	DatabaseConnections  *Metric `json:"database_connections"`
	RedisConnections     *Metric `json:"redis_connections"`
	MemoryUsage         *Metric `json:"memory_usage"`
	CPUUsage            *Metric `json:"cpu_usage"`
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(logger Logger) *MetricsCollector {
	return &MetricsCollector{
		metrics: make(map[string]*Metric),
		logger:  logger,
	}
}

// RegisterMetric 注册指标
func (mc *MetricsCollector) RegisterMetric(name string, metricType MetricType, description string, labels map[string]string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	mc.metrics[name] = &Metric{
		Name:        name,
		Type:        metricType,
		Value:       0,
		Labels:      labels,
		Description: description,
		Timestamp:   time.Now(),
	}
	
	mc.logger.Debug("注册指标", "name", name, "type", metricType)
}

// IncrementCounter 增加计数器
func (mc *MetricsCollector) IncrementCounter(name string, value float64, labels map[string]string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	key := mc.buildMetricKey(name, labels)
	if metric, exists := mc.metrics[key]; exists {
		metric.Value += value
		metric.Timestamp = time.Now()
	} else {
		mc.metrics[key] = &Metric{
			Name:      name,
			Type:      MetricTypeCounter,
			Value:     value,
			Labels:    labels,
			Timestamp: time.Now(),
		}
	}
}

// SetGauge 设置仪表盘值
func (mc *MetricsCollector) SetGauge(name string, value float64, labels map[string]string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	key := mc.buildMetricKey(name, labels)
	if metric, exists := mc.metrics[key]; exists {
		metric.Value = value
		metric.Timestamp = time.Now()
	} else {
		mc.metrics[key] = &Metric{
			Name:      name,
			Type:      MetricTypeGauge,
			Value:     value,
			Labels:    labels,
			Timestamp: time.Now(),
		}
	}
}

// RecordHistogram 记录直方图
func (mc *MetricsCollector) RecordHistogram(name string, value float64, labels map[string]string) {
	// 简化实现，实际应该维护桶统计
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	key := mc.buildMetricKey(name, labels)
	if metric, exists := mc.metrics[key]; exists {
		// 简单平均值计算
		metric.Value = (metric.Value + value) / 2
		metric.Timestamp = time.Now()
	} else {
		mc.metrics[key] = &Metric{
			Name:      name,
			Type:      MetricTypeHistogram,
			Value:     value,
			Labels:    labels,
			Timestamp: time.Now(),
		}
	}
}

// GetMetric 获取指标
func (mc *MetricsCollector) GetMetric(name string, labels map[string]string) (*Metric, bool) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	key := mc.buildMetricKey(name, labels)
	metric, exists := mc.metrics[key]
	return metric, exists
}

// GetAllMetrics 获取所有指标
func (mc *MetricsCollector) GetAllMetrics() map[string]*Metric {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	result := make(map[string]*Metric)
	for key, metric := range mc.metrics {
		result[key] = metric
	}
	
	return result
}

// buildMetricKey 构建指标键
func (mc *MetricsCollector) buildMetricKey(name string, labels map[string]string) string {
	key := name
	if len(labels) > 0 {
		for k, v := range labels {
			key += fmt.Sprintf("_%s_%s", k, v)
		}
	}
	return key
}

// ConfigMetricsCollector 配置服务指标收集器
type ConfigMetricsCollector struct {
	collector *MetricsCollector
	logger    Logger
}

// NewConfigMetricsCollector 创建配置服务指标收集器
func NewConfigMetricsCollector(logger Logger) *ConfigMetricsCollector {
	collector := NewMetricsCollector(logger)
	
	cmc := &ConfigMetricsCollector{
		collector: collector,
		logger:    logger,
	}
	
	// 注册默认指标
	cmc.registerDefaultMetrics()
	
	return cmc
}

// registerDefaultMetrics 注册默认指标
func (cmc *ConfigMetricsCollector) registerDefaultMetrics() {
	// 请求指标
	cmc.collector.RegisterMetric("config_requests_total", MetricTypeCounter, "配置服务请求总数", nil)
	cmc.collector.RegisterMetric("config_request_duration_seconds", MetricTypeHistogram, "配置服务请求耗时", nil)
	cmc.collector.RegisterMetric("config_request_errors_total", MetricTypeCounter, "配置服务请求错误总数", nil)
	
	// 配置指标
	cmc.collector.RegisterMetric("configs_total", MetricTypeGauge, "配置总数", nil)
	cmc.collector.RegisterMetric("config_reads_total", MetricTypeCounter, "配置读取总数", nil)
	cmc.collector.RegisterMetric("config_writes_total", MetricTypeCounter, "配置写入总数", nil)
	cmc.collector.RegisterMetric("config_deletes_total", MetricTypeCounter, "配置删除总数", nil)
	cmc.collector.RegisterMetric("config_cache_hits_total", MetricTypeCounter, "配置缓存命中总数", nil)
	cmc.collector.RegisterMetric("config_cache_misses_total", MetricTypeCounter, "配置缓存未命中总数", nil)
	
	// 密钥指标
	cmc.collector.RegisterMetric("secrets_total", MetricTypeGauge, "密钥总数", nil)
	cmc.collector.RegisterMetric("secret_accesses_total", MetricTypeCounter, "密钥访问总数", nil)
	cmc.collector.RegisterMetric("secret_rotations_total", MetricTypeCounter, "密钥轮换总数", nil)
	
	// 模板指标
	cmc.collector.RegisterMetric("templates_total", MetricTypeGauge, "模板总数", nil)
	cmc.collector.RegisterMetric("template_renders_total", MetricTypeCounter, "模板渲染总数", nil)
	cmc.collector.RegisterMetric("template_errors_total", MetricTypeCounter, "模板错误总数", nil)
	
	// WebSocket 指标
	cmc.collector.RegisterMetric("websocket_connections", MetricTypeGauge, "WebSocket 连接数", nil)
	cmc.collector.RegisterMetric("websocket_messages_total", MetricTypeCounter, "WebSocket 消息总数", nil)
	
	// 系统指标
	cmc.collector.RegisterMetric("database_connections", MetricTypeGauge, "数据库连接数", nil)
	cmc.collector.RegisterMetric("redis_connections", MetricTypeGauge, "Redis 连接数", nil)
	cmc.collector.RegisterMetric("memory_usage_bytes", MetricTypeGauge, "内存使用量", nil)
	cmc.collector.RegisterMetric("cpu_usage_percent", MetricTypeGauge, "CPU 使用率", nil)
}

// RecordRequest 记录请求指标
func (cmc *ConfigMetricsCollector) RecordRequest(method, endpoint string, statusCode int, duration time.Duration) {
	labels := map[string]string{
		"method":   method,
		"endpoint": endpoint,
		"status":   fmt.Sprintf("%d", statusCode),
	}
	
	cmc.collector.IncrementCounter("config_requests_total", 1, labels)
	cmc.collector.RecordHistogram("config_request_duration_seconds", duration.Seconds(), labels)
	
	if statusCode >= 400 {
		cmc.collector.IncrementCounter("config_request_errors_total", 1, labels)
	}
}

// RecordConfigOperation 记录配置操作
func (cmc *ConfigMetricsCollector) RecordConfigOperation(operation string, scope ConfigScope, environment string) {
	labels := map[string]string{
		"operation":   operation,
		"scope":       string(scope),
		"environment": environment,
	}
	
	switch operation {
	case "read":
		cmc.collector.IncrementCounter("config_reads_total", 1, labels)
	case "write":
		cmc.collector.IncrementCounter("config_writes_total", 1, labels)
	case "delete":
		cmc.collector.IncrementCounter("config_deletes_total", 1, labels)
	}
}

// RecordCacheHit 记录缓存命中
func (cmc *ConfigMetricsCollector) RecordCacheHit(hit bool) {
	if hit {
		cmc.collector.IncrementCounter("config_cache_hits_total", 1, nil)
	} else {
		cmc.collector.IncrementCounter("config_cache_misses_total", 1, nil)
	}
}

// RecordSecretAccess 记录密钥访问
func (cmc *ConfigMetricsCollector) RecordSecretAccess(operation string, scope ConfigScope) {
	labels := map[string]string{
		"operation": operation,
		"scope":     string(scope),
	}
	
	cmc.collector.IncrementCounter("secret_accesses_total", 1, labels)
	
	if operation == "rotate" {
		cmc.collector.IncrementCounter("secret_rotations_total", 1, labels)
	}
}

// RecordTemplateRender 记录模板渲染
func (cmc *ConfigMetricsCollector) RecordTemplateRender(success bool, duration time.Duration) {
	labels := map[string]string{
		"success": fmt.Sprintf("%t", success),
	}
	
	cmc.collector.IncrementCounter("template_renders_total", 1, labels)
	cmc.collector.RecordHistogram("template_render_duration_seconds", duration.Seconds(), labels)
	
	if !success {
		cmc.collector.IncrementCounter("template_errors_total", 1, nil)
	}
}

// UpdateWebSocketConnections 更新 WebSocket 连接数
func (cmc *ConfigMetricsCollector) UpdateWebSocketConnections(count int) {
	cmc.collector.SetGauge("websocket_connections", float64(count), nil)
}

// RecordWebSocketMessage 记录 WebSocket 消息
func (cmc *ConfigMetricsCollector) RecordWebSocketMessage(messageType string) {
	labels := map[string]string{
		"type": messageType,
	}
	
	cmc.collector.IncrementCounter("websocket_messages_total", 1, labels)
}

// UpdateSystemMetrics 更新系统指标
func (cmc *ConfigMetricsCollector) UpdateSystemMetrics(dbConns, redisConns int, memoryUsage, cpuUsage float64) {
	cmc.collector.SetGauge("database_connections", float64(dbConns), nil)
	cmc.collector.SetGauge("redis_connections", float64(redisConns), nil)
	cmc.collector.SetGauge("memory_usage_bytes", memoryUsage, nil)
	cmc.collector.SetGauge("cpu_usage_percent", cpuUsage, nil)
}

// GetMetrics 获取指标
func (cmc *ConfigMetricsCollector) GetMetrics() map[string]*Metric {
	return cmc.collector.GetAllMetrics()
}

// HealthChecker 健康检查器
type HealthChecker struct {
	checks map[string]HealthCheck
	logger Logger
}

// HealthCheck 健康检查
type HealthCheck struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	CheckFunc   func() HealthStatus    `json:"-"`
	Interval    time.Duration          `json:"interval"`
	Timeout     time.Duration          `json:"timeout"`
	LastCheck   time.Time              `json:"last_check"`
	Status      HealthStatus           `json:"status"`
	Message     string                 `json:"message"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// HealthStatus 健康状态
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusDegraded  HealthStatus = "degraded"
	HealthStatusUnknown   HealthStatus = "unknown"
)

// OverallHealth 整体健康状态
type OverallHealth struct {
	Status    HealthStatus             `json:"status"`
	Timestamp time.Time                `json:"timestamp"`
	Checks    map[string]*HealthCheck  `json:"checks"`
	Metadata  map[string]interface{}   `json:"metadata"`
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(logger Logger) *HealthChecker {
	return &HealthChecker{
		checks: make(map[string]HealthCheck),
		logger: logger,
	}
}

// RegisterCheck 注册健康检查
func (hc *HealthChecker) RegisterCheck(name, description string, checkFunc func() HealthStatus, interval, timeout time.Duration) {
	hc.checks[name] = HealthCheck{
		Name:        name,
		Description: description,
		CheckFunc:   checkFunc,
		Interval:    interval,
		Timeout:     timeout,
		Status:      HealthStatusUnknown,
	}
	
	hc.logger.Info("注册健康检查", "name", name, "interval", interval)
}

// RunCheck 运行健康检查
func (hc *HealthChecker) RunCheck(name string) *HealthCheck {
	check, exists := hc.checks[name]
	if !exists {
		return nil
	}
	
	start := time.Now()
	
	// 使用超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), check.Timeout)
	defer cancel()
	
	done := make(chan HealthStatus, 1)
	go func() {
		done <- check.CheckFunc()
	}()
	
	select {
	case status := <-done:
		check.Status = status
		check.Message = "检查完成"
	case <-ctx.Done():
		check.Status = HealthStatusUnhealthy
		check.Message = "检查超时"
	}
	
	check.LastCheck = time.Now()
	check.Metadata = map[string]interface{}{
		"duration": time.Since(start).String(),
	}
	
	hc.checks[name] = check
	
	return &check
}

// RunAllChecks 运行所有健康检查
func (hc *HealthChecker) RunAllChecks() *OverallHealth {
	overall := &OverallHealth{
		Status:    HealthStatusHealthy,
		Timestamp: time.Now(),
		Checks:    make(map[string]*HealthCheck),
		Metadata:  make(map[string]interface{}),
	}
	
	healthyCount := 0
	totalCount := len(hc.checks)
	
	for name := range hc.checks {
		check := hc.RunCheck(name)
		if check != nil {
			overall.Checks[name] = check
			
			if check.Status == HealthStatusHealthy {
				healthyCount++
			} else if check.Status == HealthStatusUnhealthy {
				overall.Status = HealthStatusUnhealthy
			}
		}
	}
	
	// 如果有部分检查失败，状态为降级
	if overall.Status == HealthStatusHealthy && healthyCount < totalCount {
		overall.Status = HealthStatusDegraded
	}
	
	overall.Metadata["healthy_checks"] = healthyCount
	overall.Metadata["total_checks"] = totalCount
	overall.Metadata["health_ratio"] = float64(healthyCount) / float64(totalCount)
	
	return overall
}

// GetCheck 获取健康检查
func (hc *HealthChecker) GetCheck(name string) (*HealthCheck, bool) {
	check, exists := hc.checks[name]
	return &check, exists
}

// StartPeriodicChecks 启动定期检查
func (hc *HealthChecker) StartPeriodicChecks(ctx context.Context) {
	for name, check := range hc.checks {
		go func(checkName string, checkInterval time.Duration) {
			ticker := time.NewTicker(checkInterval)
			defer ticker.Stop()
			
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					hc.RunCheck(checkName)
				}
			}
		}(name, check.Interval)
	}
	
	hc.logger.Info("启动定期健康检查", "checks", len(hc.checks))
}
