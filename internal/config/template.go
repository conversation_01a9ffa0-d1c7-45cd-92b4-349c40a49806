package config

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"text/template"
	"time"

	"gopkg.in/yaml.v3"
)

// TemplateRenderer 模板渲染器
type TemplateRenderer struct {
	service   Service
	logger    Logger
	funcMap   template.FuncMap
	templates map[string]*template.Template
}

// NewTemplateRenderer 创建模板渲染器
func NewTemplateRenderer(service Service, logger Logger) *TemplateRenderer {
	renderer := &TemplateRenderer{
		service:   service,
		logger:    logger,
		templates: make(map[string]*template.Template),
	}
	
	// 初始化模板函数
	renderer.initTemplateFuncs()
	
	return renderer
}

// initTemplateFuncs 初始化模板函数
func (r *TemplateRenderer) initTemplateFuncs() {
	r.funcMap = template.FuncMap{
		// 默认值函数
		"default": func(value, defaultValue interface{}) interface{} {
			if value == nil || value == "" {
				return defaultValue
			}
			return value
		},
		
		// 密钥获取函数
		"secret": func(key string) string {
			// 从密钥存储获取值
			parts := strings.Split(key, ".")
			if len(parts) >= 2 {
				scope := ConfigScopeGlobal
				scopeID := ""
				env := "default"
				
				// 解析作用域信息
				if len(parts) >= 3 {
					scope = ConfigScope(parts[1])
					if len(parts) >= 4 {
						scopeID = parts[2]
						env = parts[3]
					}
				}
				
				secret, err := r.service.GetSecret(context.Background(), key, scope, scopeID, env)
				if err != nil {
					r.logger.Error("模板函数获取密钥失败", "error", err, "key", key)
					return ""
				}
				return secret.Value
			}
			return ""
		},
		
		// 环境变量函数
		"env": func(key string, defaultValue ...string) string {
			value := os.Getenv(key)
			if value == "" && len(defaultValue) > 0 {
				return defaultValue[0]
			}
			return value
		},
		
		// Base64 编码
		"base64": func(value string) string {
			return base64.StdEncoding.EncodeToString([]byte(value))
		},
		
		// Base64 解码
		"base64decode": func(value string) string {
			decoded, err := base64.StdEncoding.DecodeString(value)
			if err != nil {
				r.logger.Error("Base64 解码失败", "error", err, "value", value)
				return ""
			}
			return string(decoded)
		},
		
		// SHA256 哈希
		"sha256": func(value string) string {
			hash := sha256.Sum256([]byte(value))
			return fmt.Sprintf("%x", hash)
		},
		
		// 随机字符串
		"random": func(length int) string {
			const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
			b := make([]byte, length)
			for i := range b {
				b[i] = charset[rand.Intn(len(charset))]
			}
			return string(b)
		},
		
		// 当前时间
		"now": func() time.Time {
			return time.Now()
		},
		
		// 格式化时间
		"formatTime": func(format string, t time.Time) string {
			return t.Format(format)
		},
		
		// JSON 序列化
		"toJson": func(value interface{}) string {
			bytes, err := json.Marshal(value)
			if err != nil {
				r.logger.Error("JSON 序列化失败", "error", err)
				return ""
			}
			return string(bytes)
		},
		
		// JSON 反序列化
		"fromJson": func(jsonStr string) interface{} {
			var result interface{}
			if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
				r.logger.Error("JSON 反序列化失败", "error", err, "json", jsonStr)
				return nil
			}
			return result
		},
		
		// YAML 序列化
		"toYaml": func(value interface{}) string {
			bytes, err := yaml.Marshal(value)
			if err != nil {
				r.logger.Error("YAML 序列化失败", "error", err)
				return ""
			}
			return string(bytes)
		},
		
		// YAML 反序列化
		"fromYaml": func(yamlStr string) interface{} {
			var result interface{}
			if err := yaml.Unmarshal([]byte(yamlStr), &result); err != nil {
				r.logger.Error("YAML 反序列化失败", "error", err, "yaml", yamlStr)
				return nil
			}
			return result
		},
		
		// 字符串操作
		"upper":     strings.ToUpper,
		"lower":     strings.ToLower,
		"title":     strings.Title,
		"trim":      strings.TrimSpace,
		"replace":   strings.ReplaceAll,
		"contains":  strings.Contains,
		"hasPrefix": strings.HasPrefix,
		"hasSuffix": strings.HasSuffix,
		
		// 字符串分割和连接
		"split": func(sep, str string) []string {
			return strings.Split(str, sep)
		},
		"join": func(sep string, strs []string) string {
			return strings.Join(strs, sep)
		},
		
		// 条件函数
		"eq": func(a, b interface{}) bool {
			return a == b
		},
		"ne": func(a, b interface{}) bool {
			return a != b
		},
		"lt": func(a, b interface{}) bool {
			return fmt.Sprintf("%v", a) < fmt.Sprintf("%v", b)
		},
		"le": func(a, b interface{}) bool {
			return fmt.Sprintf("%v", a) <= fmt.Sprintf("%v", b)
		},
		"gt": func(a, b interface{}) bool {
			return fmt.Sprintf("%v", a) > fmt.Sprintf("%v", b)
		},
		"ge": func(a, b interface{}) bool {
			return fmt.Sprintf("%v", a) >= fmt.Sprintf("%v", b)
		},
		
		// 逻辑函数
		"and": func(a, b bool) bool {
			return a && b
		},
		"or": func(a, b bool) bool {
			return a || b
		},
		"not": func(a bool) bool {
			return !a
		},
		
		// 配置获取函数
		"config": func(key string, scope ...string) interface{} {
			configScope := ConfigScopeGlobal
			scopeID := ""
			env := "default"
			
			if len(scope) > 0 {
				configScope = ConfigScope(scope[0])
			}
			if len(scope) > 1 {
				scopeID = scope[1]
			}
			if len(scope) > 2 {
				env = scope[2]
			}
			
			config, err := r.service.GetConfig(context.Background(), key, configScope, scopeID, env)
			if err != nil {
				r.logger.Error("模板函数获取配置失败", "error", err, "key", key)
				return nil
			}
			return config.Value
		},
		
		// 有效配置获取函数
		"effectiveConfig": func(key string, scope ...string) interface{} {
			configScope := ConfigScopeGlobal
			scopeID := ""
			env := "default"
			
			if len(scope) > 0 {
				configScope = ConfigScope(scope[0])
			}
			if len(scope) > 1 {
				scopeID = scope[1]
			}
			if len(scope) > 2 {
				env = scope[2]
			}
			
			value, err := r.service.GetEffectiveConfig(context.Background(), key, configScope, scopeID, env)
			if err != nil {
				r.logger.Error("模板函数获取有效配置失败", "error", err, "key", key)
				return nil
			}
			return value
		},
	}
}

// RenderTemplate 渲染模板
func (r *TemplateRenderer) RenderTemplate(ctx context.Context, templateContent string, variables map[string]interface{}) (string, error) {
	// 创建模板
	tmpl, err := template.New("config").Funcs(r.funcMap).Parse(templateContent)
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %w", err)
	}
	
	// 渲染模板
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, variables); err != nil {
		return "", fmt.Errorf("渲染模板失败: %w", err)
	}
	
	return buf.String(), nil
}

// RenderConfigTemplate 渲染配置模板
func (r *TemplateRenderer) RenderConfigTemplate(ctx context.Context, template *ConfigTemplate, variables map[string]interface{}) (string, error) {
	// 验证模板变量
	if err := r.validateTemplateVariables(template, variables); err != nil {
		return "", fmt.Errorf("模板变量验证失败: %w", err)
	}
	
	// 渲染模板
	result, err := r.RenderTemplate(ctx, template.Template, variables)
	if err != nil {
		return "", err
	}
	
	r.logger.Info("配置模板渲染成功", "template_id", template.ID, "template_name", template.Name)
	return result, nil
}

// validateTemplateVariables 验证模板变量
func (r *TemplateRenderer) validateTemplateVariables(template *ConfigTemplate, variables map[string]interface{}) error {
	// 检查必需变量
	for _, variable := range template.Variables {
		if variable.Required {
			if _, exists := variables[variable.Name]; !exists {
				return fmt.Errorf("缺少必需变量: %s", variable.Name)
			}
		}
		
		// 验证变量类型
		if value, exists := variables[variable.Name]; exists {
			if err := r.validateVariableType(variable, value); err != nil {
				return fmt.Errorf("变量 %s 验证失败: %w", variable.Name, err)
			}
		}
	}
	
	return nil
}

// validateVariableType 验证变量类型
func (r *TemplateRenderer) validateVariableType(variable TemplateVariable, value interface{}) error {
	switch variable.Type {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("变量类型必须是字符串")
		}
	case "number":
		switch value.(type) {
		case int, int64, float32, float64:
			// OK
		default:
			return fmt.Errorf("变量类型必须是数字")
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("变量类型必须是布尔值")
		}
	case "array":
		if _, ok := value.([]interface{}); !ok {
			return fmt.Errorf("变量类型必须是数组")
		}
	case "object":
		if _, ok := value.(map[string]interface{}); !ok {
			return fmt.Errorf("变量类型必须是对象")
		}
	}
	
	// 验证枚举值
	if len(variable.Enum) > 0 {
		found := false
		for _, enum := range variable.Enum {
			if value == enum {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("值不在允许的枚举中: %v", variable.Enum)
		}
	}
	
	return nil
}

// GetTemplateVariables 获取模板变量
func (r *TemplateRenderer) GetTemplateVariables(templateContent string) ([]string, error) {
	// 解析模板以提取变量
	_, err := template.New("temp").Funcs(r.funcMap).Parse(templateContent)
	if err != nil {
		return nil, fmt.Errorf("解析模板失败: %w", err)
	}
	
	// 这里简化处理，实际应该分析模板 AST 来提取变量
	variables := make([]string, 0)
	
	// 简单的正则匹配提取变量（实际应该更复杂）
	content := templateContent
	start := 0
	for {
		startIdx := strings.Index(content[start:], "{{")
		if startIdx == -1 {
			break
		}
		startIdx += start
		
		endIdx := strings.Index(content[startIdx:], "}}")
		if endIdx == -1 {
			break
		}
		endIdx += startIdx
		
		// 提取变量表达式
		expr := strings.TrimSpace(content[startIdx+2 : endIdx])
		if strings.HasPrefix(expr, ".") {
			// 提取变量名
			parts := strings.Fields(expr)
			if len(parts) > 0 {
				varName := strings.TrimPrefix(parts[0], ".")
				if varName != "" && !contains(variables, varName) {
					variables = append(variables, varName)
				}
			}
		}
		
		start = endIdx + 2
	}
	
	return variables, nil
}

// contains 检查字符串数组是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// CacheTemplate 缓存模板
func (r *TemplateRenderer) CacheTemplate(templateID string, templateContent string) error {
	tmpl, err := template.New(templateID).Funcs(r.funcMap).Parse(templateContent)
	if err != nil {
		return fmt.Errorf("解析模板失败: %w", err)
	}
	
	r.templates[templateID] = tmpl
	r.logger.Debug("模板缓存成功", "template_id", templateID)
	
	return nil
}

// GetCachedTemplate 获取缓存的模板
func (r *TemplateRenderer) GetCachedTemplate(templateID string) (*template.Template, bool) {
	tmpl, exists := r.templates[templateID]
	return tmpl, exists
}

// ClearTemplateCache 清除模板缓存
func (r *TemplateRenderer) ClearTemplateCache() {
	r.templates = make(map[string]*template.Template)
	r.logger.Info("模板缓存已清除")
}

// RenderWithCache 使用缓存渲染模板
func (r *TemplateRenderer) RenderWithCache(ctx context.Context, templateID string, templateContent string, variables map[string]interface{}) (string, error) {
	// 尝试从缓存获取模板
	tmpl, exists := r.GetCachedTemplate(templateID)
	if !exists {
		// 缓存模板
		if err := r.CacheTemplate(templateID, templateContent); err != nil {
			return "", err
		}
		tmpl = r.templates[templateID]
	}
	
	// 渲染模板
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, variables); err != nil {
		return "", fmt.Errorf("渲染模板失败: %w", err)
	}
	
	return buf.String(), nil
}

// AddTemplateFunc 添加自定义模板函数
func (r *TemplateRenderer) AddTemplateFunc(name string, fn interface{}) {
	r.funcMap[name] = fn
}

// RemoveTemplateFunc 移除模板函数
func (r *TemplateRenderer) RemoveTemplateFunc(name string) {
	delete(r.funcMap, name)
}

// GetTemplateFuncs 获取所有模板函数
func (r *TemplateRenderer) GetTemplateFuncs() template.FuncMap {
	return r.funcMap
}
