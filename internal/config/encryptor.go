package config

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"

	"golang.org/x/crypto/pbkdf2"
)

// Encryptor 加密器接口
type Encryptor interface {
	// Encrypt 加密数据
	Encrypt(plaintext string) (string, error)
	
	// Decrypt 解密数据
	Decrypt(ciphertext string) (string, error)
	
	// GenerateKey 生成新的加密密钥
	GenerateKey() (string, error)
}

// AESEncryptor AES 加密器实现
type AESEncryptor struct {
	key []byte
}

// NewAESEncryptor 创建新的 AES 加密器
func NewAESEncryptor(masterKey string) *AESEncryptor {
	// 使用 PBKDF2 从主密钥派生加密密钥
	salt := []byte("paas-config-salt") // 在生产环境中应该使用随机盐
	key := pbkdf2.Key([]byte(masterKey), salt, 10000, 32, sha256.New)
	
	return &AESEncryptor{
		key: key,
	}
}

// Encrypt 加密数据
func (e *AESEncryptor) Encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	// 创建 AES 密码块
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", fmt.Errorf("创建 AES 密码块失败: %w", err)
	}

	// 创建 GCM 模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 模式失败: %w", err)
	}

	// 生成随机 nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("生成 nonce 失败: %w", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// 返回 Base64 编码的密文
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt 解密数据
func (e *AESEncryptor) Decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// Base64 解码
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("Base64 解码失败: %w", err)
	}

	// 创建 AES 密码块
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", fmt.Errorf("创建 AES 密码块失败: %w", err)
	}

	// 创建 GCM 模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 模式失败: %w", err)
	}

	// 检查数据长度
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("密文数据长度不足")
	}

	// 提取 nonce 和密文
	nonce, cipherData := data[:nonceSize], data[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %w", err)
	}

	return string(plaintext), nil
}

// GenerateKey 生成新的加密密钥
func (e *AESEncryptor) GenerateKey() (string, error) {
	key := make([]byte, 32) // 256-bit key
	if _, err := rand.Read(key); err != nil {
		return "", fmt.Errorf("生成密钥失败: %w", err)
	}
	
	return base64.StdEncoding.EncodeToString(key), nil
}

// NoOpEncryptor 无操作加密器（用于测试或不需要加密的场景）
type NoOpEncryptor struct{}

// NewNoOpEncryptor 创建无操作加密器
func NewNoOpEncryptor() *NoOpEncryptor {
	return &NoOpEncryptor{}
}

// Encrypt 不进行加密，直接返回原文
func (e *NoOpEncryptor) Encrypt(plaintext string) (string, error) {
	return plaintext, nil
}

// Decrypt 不进行解密，直接返回原文
func (e *NoOpEncryptor) Decrypt(ciphertext string) (string, error) {
	return ciphertext, nil
}

// GenerateKey 生成随机字符串作为密钥
func (e *NoOpEncryptor) GenerateKey() (string, error) {
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

// EncryptionManager 加密管理器
type EncryptionManager struct {
	encryptors map[string]Encryptor
	default_   string
}

// NewEncryptionManager 创建加密管理器
func NewEncryptionManager() *EncryptionManager {
	return &EncryptionManager{
		encryptors: make(map[string]Encryptor),
		default_:   "aes",
	}
}

// RegisterEncryptor 注册加密器
func (m *EncryptionManager) RegisterEncryptor(name string, encryptor Encryptor) {
	m.encryptors[name] = encryptor
}

// GetEncryptor 获取加密器
func (m *EncryptionManager) GetEncryptor(name string) (Encryptor, error) {
	if name == "" {
		name = m.default_
	}
	
	encryptor, exists := m.encryptors[name]
	if !exists {
		return nil, fmt.Errorf("加密器不存在: %s", name)
	}
	
	return encryptor, nil
}

// SetDefault 设置默认加密器
func (m *EncryptionManager) SetDefault(name string) error {
	if _, exists := m.encryptors[name]; !exists {
		return fmt.Errorf("加密器不存在: %s", name)
	}
	
	m.default_ = name
	return nil
}

// EncryptWithAlgorithm 使用指定算法加密
func (m *EncryptionManager) EncryptWithAlgorithm(algorithm, plaintext string) (string, error) {
	encryptor, err := m.GetEncryptor(algorithm)
	if err != nil {
		return "", err
	}
	
	ciphertext, err := encryptor.Encrypt(plaintext)
	if err != nil {
		return "", err
	}
	
	// 在密文前添加算法标识
	return fmt.Sprintf("%s:%s", algorithm, ciphertext), nil
}

// DecryptWithAlgorithm 自动识别算法并解密
func (m *EncryptionManager) DecryptWithAlgorithm(ciphertext string) (string, error) {
	// 解析算法标识
	parts := splitOnce(ciphertext, ":")
	if len(parts) != 2 {
		// 没有算法标识，使用默认加密器
		encryptor, err := m.GetEncryptor("")
		if err != nil {
			return "", err
		}
		return encryptor.Decrypt(ciphertext)
	}
	
	algorithm, data := parts[0], parts[1]
	encryptor, err := m.GetEncryptor(algorithm)
	if err != nil {
		return "", err
	}
	
	return encryptor.Decrypt(data)
}

// splitOnce 分割字符串（只分割一次）
func splitOnce(s, sep string) []string {
	parts := make([]string, 0, 2)
	if i := len(s); i >= 0 {
		for j := 0; j < len(s); j++ {
			if s[j:j+len(sep)] == sep {
				parts = append(parts, s[:j])
				parts = append(parts, s[j+len(sep):])
				return parts
			}
		}
	}
	return []string{s}
}

// KeyRotationManager 密钥轮换管理器
type KeyRotationManager struct {
	encryptionManager *EncryptionManager
	logger            Logger
}

// NewKeyRotationManager 创建密钥轮换管理器
func NewKeyRotationManager(encryptionManager *EncryptionManager, logger Logger) *KeyRotationManager {
	return &KeyRotationManager{
		encryptionManager: encryptionManager,
		logger:            logger,
	}
}

// RotateKey 轮换密钥
func (m *KeyRotationManager) RotateKey(algorithm string) error {
	encryptor, err := m.encryptionManager.GetEncryptor(algorithm)
	if err != nil {
		return fmt.Errorf("获取加密器失败: %w", err)
	}
	
	// 生成新密钥
	newKey, err := encryptor.GenerateKey()
	if err != nil {
		return fmt.Errorf("生成新密钥失败: %w", err)
	}
	
	m.logger.Info("密钥轮换成功", "algorithm", algorithm, "new_key_length", len(newKey))
	
	// TODO: 实现密钥轮换逻辑
	// 1. 使用新密钥重新加密所有数据
	// 2. 更新密钥存储
	// 3. 清理旧密钥
	
	return nil
}
