package config

import (
	"context"
	"strings"
)

// Permission 权限定义
type Permission string

const (
	// 配置权限
	PermissionConfigRead   Permission = "config:read"
	PermissionConfigWrite  Permission = "config:write"
	PermissionConfigDelete Permission = "config:delete"
	
	// 密钥权限
	PermissionSecretRead   Permission = "secret:read"
	PermissionSecretWrite  Permission = "secret:write"
	PermissionSecretDelete Permission = "secret:delete"
	PermissionSecretRotate Permission = "secret:rotate"
	
	// 模板权限
	PermissionTemplateRead   Permission = "template:read"
	PermissionTemplateWrite  Permission = "template:write"
	PermissionTemplateDelete Permission = "template:delete"
	PermissionTemplateRender Permission = "template:render"
	
	// 功能开关权限
	PermissionFeatureFlagRead   Permission = "feature_flag:read"
	PermissionFeatureFlagWrite  Permission = "feature_flag:write"
	PermissionFeatureFlagDelete Permission = "feature_flag:delete"
	
	// 管理权限
	PermissionConfigAdmin    Permission = "config:admin"
	PermissionSecretAdmin    Permission = "secret:admin"
	PermissionTemplateAdmin  Permission = "template:admin"
	PermissionSystemAdmin    Permission = "system:admin"
	
	// 审计权限
	PermissionAuditRead Permission = "audit:read"
	
	// 分发权限
	PermissionConfigDistribute Permission = "config:distribute"
)

// Role 角色定义
type Role string

const (
	RoleViewer    Role = "viewer"     // 查看者
	RoleDeveloper Role = "developer"  // 开发者
	RoleOperator  Role = "operator"   // 运维者
	RoleAdmin     Role = "admin"      // 管理员
	RoleSystem    Role = "system"     // 系统角色
)

// User 用户信息
type User struct {
	ID       string   `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []Role   `json:"roles"`
	TenantID string   `json:"tenant_id"`
	Scopes   []string `json:"scopes"` // 用户可访问的作用域
}

// PermissionManager 权限管理器
type PermissionManager struct {
	rolePermissions map[Role][]Permission
	logger          Logger
}

// NewPermissionManager 创建权限管理器
func NewPermissionManager(logger Logger) *PermissionManager {
	pm := &PermissionManager{
		rolePermissions: make(map[Role][]Permission),
		logger:          logger,
	}
	
	// 初始化默认角色权限
	pm.initDefaultRolePermissions()
	
	return pm
}

// initDefaultRolePermissions 初始化默认角色权限
func (pm *PermissionManager) initDefaultRolePermissions() {
	// 查看者权限
	pm.rolePermissions[RoleViewer] = []Permission{
		PermissionConfigRead,
		PermissionTemplateRead,
		PermissionFeatureFlagRead,
	}
	
	// 开发者权限
	pm.rolePermissions[RoleDeveloper] = []Permission{
		PermissionConfigRead,
		PermissionConfigWrite,
		PermissionSecretRead,
		PermissionTemplateRead,
		PermissionTemplateWrite,
		PermissionTemplateRender,
		PermissionFeatureFlagRead,
		PermissionFeatureFlagWrite,
	}
	
	// 运维者权限
	pm.rolePermissions[RoleOperator] = []Permission{
		PermissionConfigRead,
		PermissionConfigWrite,
		PermissionConfigDelete,
		PermissionSecretRead,
		PermissionSecretWrite,
		PermissionSecretRotate,
		PermissionTemplateRead,
		PermissionTemplateWrite,
		PermissionTemplateRender,
		PermissionFeatureFlagRead,
		PermissionFeatureFlagWrite,
		PermissionFeatureFlagDelete,
		PermissionConfigDistribute,
		PermissionAuditRead,
	}
	
	// 管理员权限
	pm.rolePermissions[RoleAdmin] = []Permission{
		PermissionConfigRead,
		PermissionConfigWrite,
		PermissionConfigDelete,
		PermissionConfigAdmin,
		PermissionSecretRead,
		PermissionSecretWrite,
		PermissionSecretDelete,
		PermissionSecretRotate,
		PermissionSecretAdmin,
		PermissionTemplateRead,
		PermissionTemplateWrite,
		PermissionTemplateDelete,
		PermissionTemplateRender,
		PermissionTemplateAdmin,
		PermissionFeatureFlagRead,
		PermissionFeatureFlagWrite,
		PermissionFeatureFlagDelete,
		PermissionConfigDistribute,
		PermissionAuditRead,
	}
	
	// 系统角色权限
	pm.rolePermissions[RoleSystem] = []Permission{
		PermissionSystemAdmin,
		PermissionConfigAdmin,
		PermissionSecretAdmin,
		PermissionTemplateAdmin,
		PermissionConfigRead,
		PermissionConfigWrite,
		PermissionConfigDelete,
		PermissionSecretRead,
		PermissionSecretWrite,
		PermissionSecretDelete,
		PermissionSecretRotate,
		PermissionTemplateRead,
		PermissionTemplateWrite,
		PermissionTemplateDelete,
		PermissionTemplateRender,
		PermissionFeatureFlagRead,
		PermissionFeatureFlagWrite,
		PermissionFeatureFlagDelete,
		PermissionConfigDistribute,
		PermissionAuditRead,
	}
}

// HasPermission 检查用户是否有指定权限
func (pm *PermissionManager) HasPermission(user *User, permission Permission) bool {
	// 系统管理员拥有所有权限
	if pm.hasRole(user, RoleSystem) {
		return true
	}
	
	// 检查用户角色权限
	for _, role := range user.Roles {
		if permissions, exists := pm.rolePermissions[role]; exists {
			for _, p := range permissions {
				if p == permission {
					return true
				}
			}
		}
	}
	
	return false
}

// HasAnyPermission 检查用户是否有任意一个权限
func (pm *PermissionManager) HasAnyPermission(user *User, permissions ...Permission) bool {
	for _, permission := range permissions {
		if pm.HasPermission(user, permission) {
			return true
		}
	}
	return false
}

// HasAllPermissions 检查用户是否有所有权限
func (pm *PermissionManager) HasAllPermissions(user *User, permissions ...Permission) bool {
	for _, permission := range permissions {
		if !pm.HasPermission(user, permission) {
			return false
		}
	}
	return true
}

// CanAccessScope 检查用户是否可以访问指定作用域
func (pm *PermissionManager) CanAccessScope(user *User, scope ConfigScope, scopeID string) bool {
	// 系统管理员可以访问所有作用域
	if pm.hasRole(user, RoleSystem) {
		return true
	}
	
	switch scope {
	case ConfigScopeGlobal:
		// 只有管理员可以访问全局配置
		return pm.hasRole(user, RoleAdmin)
		
	case ConfigScopePlatform:
		// 管理员和运维者可以访问平台配置
		return pm.hasRole(user, RoleAdmin) || pm.hasRole(user, RoleOperator)
		
	case ConfigScopeTenant:
		// 检查用户是否属于该租户
		if user.TenantID == scopeID {
			return true
		}
		// 管理员可以访问所有租户配置
		return pm.hasRole(user, RoleAdmin)
		
	case ConfigScopeApplication:
		// 检查用户是否有权访问该应用
		return pm.canAccessApplication(user, scopeID)
		
	case ConfigScopeService:
		// 检查用户是否有权访问该服务
		return pm.canAccessService(user, scopeID)
		
	default:
		return false
	}
}

// CanAccessEnvironment 检查用户是否可以访问指定环境
func (pm *PermissionManager) CanAccessEnvironment(user *User, environment string) bool {
	// 系统管理员可以访问所有环境
	if pm.hasRole(user, RoleSystem) {
		return true
	}
	
	switch environment {
	case "production":
		// 只有运维者和管理员可以访问生产环境
		return pm.hasRole(user, RoleOperator) || pm.hasRole(user, RoleAdmin)
		
	case "staging":
		// 开发者、运维者和管理员可以访问预发布环境
		return pm.hasRole(user, RoleDeveloper) || pm.hasRole(user, RoleOperator) || pm.hasRole(user, RoleAdmin)
		
	case "development", "testing":
		// 所有角色都可以访问开发和测试环境
		return true
		
	default:
		// 其他环境需要开发者以上权限
		return pm.hasRole(user, RoleDeveloper) || pm.hasRole(user, RoleOperator) || pm.hasRole(user, RoleAdmin)
	}
}

// CanViewDecryptedValue 检查用户是否可以查看解密值
func (pm *PermissionManager) CanViewDecryptedValue(user *User, config *Config) bool {
	// 如果配置未加密，所有有读权限的用户都可以查看
	if !config.Encrypted {
		return pm.HasPermission(user, PermissionConfigRead)
	}
	
	// 加密配置需要密钥读权限
	if !pm.HasPermission(user, PermissionSecretRead) {
		return false
	}
	
	// 检查作用域和环境权限
	if !pm.CanAccessScope(user, config.Scope, config.ScopeID) {
		return false
	}
	
	if !pm.CanAccessEnvironment(user, config.Environment) {
		return false
	}
	
	return true
}

// hasRole 检查用户是否有指定角色
func (pm *PermissionManager) hasRole(user *User, role Role) bool {
	for _, r := range user.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// canAccessApplication 检查用户是否可以访问应用
func (pm *PermissionManager) canAccessApplication(user *User, appID string) bool {
	// 简化实现，实际应该查询应用权限表
	// 这里假设用户的 Scopes 包含可访问的应用ID
	for _, scope := range user.Scopes {
		if scope == appID || scope == "*" {
			return true
		}
	}
	
	// 管理员可以访问所有应用
	return pm.hasRole(user, RoleAdmin)
}

// canAccessService 检查用户是否可以访问服务
func (pm *PermissionManager) canAccessService(user *User, serviceID string) bool {
	// 简化实现，实际应该查询服务权限表
	for _, scope := range user.Scopes {
		if scope == serviceID || scope == "*" {
			return true
		}
	}
	
	// 管理员和运维者可以访问所有服务
	return pm.hasRole(user, RoleAdmin) || pm.hasRole(user, RoleOperator)
}

// AddRolePermission 添加角色权限
func (pm *PermissionManager) AddRolePermission(role Role, permission Permission) {
	if permissions, exists := pm.rolePermissions[role]; exists {
		// 检查权限是否已存在
		for _, p := range permissions {
			if p == permission {
				return
			}
		}
		pm.rolePermissions[role] = append(permissions, permission)
	} else {
		pm.rolePermissions[role] = []Permission{permission}
	}
	
	pm.logger.Info("添加角色权限", "role", role, "permission", permission)
}

// RemoveRolePermission 移除角色权限
func (pm *PermissionManager) RemoveRolePermission(role Role, permission Permission) {
	if permissions, exists := pm.rolePermissions[role]; exists {
		for i, p := range permissions {
			if p == permission {
				pm.rolePermissions[role] = append(permissions[:i], permissions[i+1:]...)
				break
			}
		}
	}
	
	pm.logger.Info("移除角色权限", "role", role, "permission", permission)
}

// GetRolePermissions 获取角色权限
func (pm *PermissionManager) GetRolePermissions(role Role) []Permission {
	if permissions, exists := pm.rolePermissions[role]; exists {
		return permissions
	}
	return []Permission{}
}

// GetUserPermissions 获取用户权限
func (pm *PermissionManager) GetUserPermissions(user *User) []Permission {
	permissionSet := make(map[Permission]bool)
	
	for _, role := range user.Roles {
		if permissions, exists := pm.rolePermissions[role]; exists {
			for _, permission := range permissions {
				permissionSet[permission] = true
			}
		}
	}
	
	permissions := make([]Permission, 0, len(permissionSet))
	for permission := range permissionSet {
		permissions = append(permissions, permission)
	}
	
	return permissions
}

// AuthorizationMiddleware 权限中间件
type AuthorizationMiddleware struct {
	permissionManager *PermissionManager
	logger            Logger
}

// NewAuthorizationMiddleware 创建权限中间件
func NewAuthorizationMiddleware(permissionManager *PermissionManager, logger Logger) *AuthorizationMiddleware {
	return &AuthorizationMiddleware{
		permissionManager: permissionManager,
		logger:            logger,
	}
}

// RequirePermission 要求权限中间件
func (am *AuthorizationMiddleware) RequirePermission(permission Permission) func(context.Context, *User) error {
	return func(ctx context.Context, user *User) error {
		if !am.permissionManager.HasPermission(user, permission) {
			am.logger.Warn("权限不足", "user_id", user.ID, "permission", permission)
			return ErrPermissionDenied
		}
		return nil
	}
}

// RequireAnyPermission 要求任意权限中间件
func (am *AuthorizationMiddleware) RequireAnyPermission(permissions ...Permission) func(context.Context, *User) error {
	return func(ctx context.Context, user *User) error {
		if !am.permissionManager.HasAnyPermission(user, permissions...) {
			am.logger.Warn("权限不足", "user_id", user.ID, "permissions", permissions)
			return ErrPermissionDenied
		}
		return nil
	}
}

// RequireScope 要求作用域权限中间件
func (am *AuthorizationMiddleware) RequireScope(scope ConfigScope, scopeID string) func(context.Context, *User) error {
	return func(ctx context.Context, user *User) error {
		if !am.permissionManager.CanAccessScope(user, scope, scopeID) {
			am.logger.Warn("作用域权限不足", "user_id", user.ID, "scope", scope, "scope_id", scopeID)
			return ErrPermissionDenied
		}
		return nil
	}
}

// RequireEnvironment 要求环境权限中间件
func (am *AuthorizationMiddleware) RequireEnvironment(environment string) func(context.Context, *User) error {
	return func(ctx context.Context, user *User) error {
		if !am.permissionManager.CanAccessEnvironment(user, environment) {
			am.logger.Warn("环境权限不足", "user_id", user.ID, "environment", environment)
			return ErrPermissionDenied
		}
		return nil
	}
}

// ParsePermission 解析权限字符串
func ParsePermission(permissionStr string) Permission {
	return Permission(strings.ToLower(permissionStr))
}

// ParseRole 解析角色字符串
func ParseRole(roleStr string) Role {
	return Role(strings.ToLower(roleStr))
}
