package config

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AuditAction 审计动作
type AuditAction string

const (
	AuditActionCreate   AuditAction = "create"
	AuditActionRead     AuditAction = "read"
	AuditActionUpdate   AuditAction = "update"
	AuditActionDelete   AuditAction = "delete"
	AuditActionRollback AuditAction = "rollback"
	AuditActionExport   AuditAction = "export"
	AuditActionImport   AuditAction = "import"
	AuditActionRotate   AuditAction = "rotate"
	AuditActionRender   AuditAction = "render"
)

// AuditLevel 审计级别
type AuditLevel string

const (
	AuditLevelInfo    AuditLevel = "info"
	AuditLevelWarning AuditLevel = "warning"
	AuditLevelError   AuditLevel = "error"
	AuditLevelCritical AuditLevel = "critical"
)

// ConfigAudit 配置审计日志
type ConfigAudit struct {
	ID          string                 `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Action      AuditAction            `json:"action" gorm:"type:varchar(20);not null;index"`
	Level       AuditLevel             `json:"level" gorm:"type:varchar(20);not null;default:info"`
	ResourceType string                `json:"resource_type" gorm:"type:varchar(50);not null;index"`
	ResourceID  string                 `json:"resource_id" gorm:"type:varchar(36);index"`
	ResourceKey string                 `json:"resource_key" gorm:"type:varchar(200);index"`
	Scope       ConfigScope            `json:"scope" gorm:"type:varchar(20);index"`
	ScopeID     string                 `json:"scope_id" gorm:"type:varchar(36);index"`
	Environment string                 `json:"environment" gorm:"type:varchar(50);index"`
	UserID      string                 `json:"user_id" gorm:"type:varchar(36);not null;index"`
	Username    string                 `json:"username" gorm:"type:varchar(100);index"`
	UserIP      string                 `json:"user_ip" gorm:"type:varchar(45)"`
	UserAgent   string                 `json:"user_agent" gorm:"type:text"`
	SessionID   string                 `json:"session_id" gorm:"type:varchar(100);index"`
	RequestID   string                 `json:"request_id" gorm:"type:varchar(100);index"`
	OldValue    interface{}            `json:"old_value" gorm:"type:jsonb"`
	NewValue    interface{}            `json:"new_value" gorm:"type:jsonb"`
	Changes     map[string]interface{} `json:"changes" gorm:"type:jsonb"`
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	Message     string                 `json:"message" gorm:"type:text"`
	Success     bool                   `json:"success" gorm:"not null;default:true;index"`
	ErrorCode   string                 `json:"error_code" gorm:"type:varchar(50)"`
	ErrorMessage string                `json:"error_message" gorm:"type:text"`
	Duration    int64                  `json:"duration"` // 操作耗时（毫秒）
	CreatedAt   time.Time              `json:"created_at" gorm:"autoCreateTime;index"`
}

// ConfigAuditEvent 配置审计事件
type ConfigAuditEvent struct {
	Action       AuditAction            `json:"action"`
	Level        AuditLevel             `json:"level"`
	ResourceType string                 `json:"resource_type"`
	ResourceID   string                 `json:"resource_id"`
	ResourceKey  string                 `json:"resource_key"`
	Scope        ConfigScope            `json:"scope"`
	ScopeID      string                 `json:"scope_id"`
	Environment  string                 `json:"environment"`
	UserID       string                 `json:"user_id"`
	Username     string                 `json:"username"`
	UserIP       string                 `json:"user_ip"`
	UserAgent    string                 `json:"user_agent"`
	SessionID    string                 `json:"session_id"`
	RequestID    string                 `json:"request_id"`
	OldValue     interface{}            `json:"old_value"`
	NewValue     interface{}            `json:"new_value"`
	Changes      map[string]interface{} `json:"changes"`
	Metadata     map[string]interface{} `json:"metadata"`
	Message      string                 `json:"message"`
	Success      bool                   `json:"success"`
	ErrorCode    string                 `json:"error_code"`
	ErrorMessage string                 `json:"error_message"`
	Duration     time.Duration          `json:"duration"`
}

// AuditLogger 审计日志记录器
type AuditLogger struct {
	db     *gorm.DB
	logger Logger
	config AuditConfig
}

// AuditConfig 审计配置
type AuditConfig struct {
	Enabled         bool          `json:"enabled"`
	Level           AuditLevel    `json:"level"`
	RetentionPeriod time.Duration `json:"retention_period"`
	BatchSize       int           `json:"batch_size"`
	FlushInterval   time.Duration `json:"flush_interval"`
	AsyncWrite      bool          `json:"async_write"`
}

// NewAuditLogger 创建审计日志记录器
func NewAuditLogger(db *gorm.DB, logger Logger, config AuditConfig) *AuditLogger {
	return &AuditLogger{
		db:     db,
		logger: logger,
		config: config,
	}
}

// LogEvent 记录审计事件
func (al *AuditLogger) LogEvent(ctx context.Context, event *ConfigAuditEvent) error {
	if !al.config.Enabled {
		return nil
	}
	
	// 检查审计级别
	if !al.shouldLog(event.Level) {
		return nil
	}
	
	audit := &ConfigAudit{
		ID:           uuid.New().String(),
		Action:       event.Action,
		Level:        event.Level,
		ResourceType: event.ResourceType,
		ResourceID:   event.ResourceID,
		ResourceKey:  event.ResourceKey,
		Scope:        event.Scope,
		ScopeID:      event.ScopeID,
		Environment:  event.Environment,
		UserID:       event.UserID,
		Username:     event.Username,
		UserIP:       event.UserIP,
		UserAgent:    event.UserAgent,
		SessionID:    event.SessionID,
		RequestID:    event.RequestID,
		OldValue:     event.OldValue,
		NewValue:     event.NewValue,
		Changes:      event.Changes,
		Metadata:     event.Metadata,
		Message:      event.Message,
		Success:      event.Success,
		ErrorCode:    event.ErrorCode,
		ErrorMessage: event.ErrorMessage,
		Duration:     event.Duration.Milliseconds(),
		CreatedAt:    time.Now(),
	}
	
	if al.config.AsyncWrite {
		// 异步写入
		go func() {
			if err := al.writeAuditLog(audit); err != nil {
				al.logger.Error("写入审计日志失败", "error", err, "audit_id", audit.ID)
			}
		}()
		return nil
	} else {
		// 同步写入
		return al.writeAuditLog(audit)
	}
}

// LogConfigCreate 记录配置创建
func (al *AuditLogger) LogConfigCreate(ctx context.Context, config *Config, user *User, requestID string) error {
	return al.LogEvent(ctx, &ConfigAuditEvent{
		Action:       AuditActionCreate,
		Level:        AuditLevelInfo,
		ResourceType: "config",
		ResourceID:   config.ID,
		ResourceKey:  config.Key,
		Scope:        config.Scope,
		ScopeID:      config.ScopeID,
		Environment:  config.Environment,
		UserID:       user.ID,
		Username:     user.Username,
		RequestID:    requestID,
		NewValue:     config.Value,
		Message:      fmt.Sprintf("创建配置: %s", config.Key),
		Success:      true,
	})
}

// LogConfigUpdate 记录配置更新
func (al *AuditLogger) LogConfigUpdate(ctx context.Context, config *Config, oldValue interface{}, user *User, requestID string, changes map[string]interface{}) error {
	return al.LogEvent(ctx, &ConfigAuditEvent{
		Action:       AuditActionUpdate,
		Level:        AuditLevelInfo,
		ResourceType: "config",
		ResourceID:   config.ID,
		ResourceKey:  config.Key,
		Scope:        config.Scope,
		ScopeID:      config.ScopeID,
		Environment:  config.Environment,
		UserID:       user.ID,
		Username:     user.Username,
		RequestID:    requestID,
		OldValue:     oldValue,
		NewValue:     config.Value,
		Changes:      changes,
		Message:      fmt.Sprintf("更新配置: %s", config.Key),
		Success:      true,
	})
}

// LogConfigDelete 记录配置删除
func (al *AuditLogger) LogConfigDelete(ctx context.Context, config *Config, user *User, requestID string) error {
	return al.LogEvent(ctx, &ConfigAuditEvent{
		Action:       AuditActionDelete,
		Level:        AuditLevelWarning,
		ResourceType: "config",
		ResourceID:   config.ID,
		ResourceKey:  config.Key,
		Scope:        config.Scope,
		ScopeID:      config.ScopeID,
		Environment:  config.Environment,
		UserID:       user.ID,
		Username:     user.Username,
		RequestID:    requestID,
		OldValue:     config.Value,
		Message:      fmt.Sprintf("删除配置: %s", config.Key),
		Success:      true,
	})
}

// LogSecretAccess 记录密钥访问
func (al *AuditLogger) LogSecretAccess(ctx context.Context, secret *Secret, user *User, requestID string, action AuditAction) error {
	level := AuditLevelInfo
	if action == AuditActionRead {
		level = AuditLevelWarning // 密钥读取使用警告级别
	}
	
	return al.LogEvent(ctx, &ConfigAuditEvent{
		Action:       action,
		Level:        level,
		ResourceType: "secret",
		ResourceID:   secret.ID,
		ResourceKey:  secret.Key,
		Scope:        secret.Scope,
		ScopeID:      secret.ScopeID,
		Environment:  secret.Environment,
		UserID:       user.ID,
		Username:     user.Username,
		RequestID:    requestID,
		Message:      fmt.Sprintf("密钥%s: %s", action, secret.Key),
		Success:      true,
	})
}

// LogError 记录错误事件
func (al *AuditLogger) LogError(ctx context.Context, action AuditAction, resourceType, resourceID string, user *User, requestID string, err error) error {
	var errorCode, errorMessage string
	if configErr := GetConfigError(err); configErr != nil {
		errorCode = configErr.Code
		errorMessage = configErr.Message
	} else {
		errorCode = "UNKNOWN_ERROR"
		errorMessage = err.Error()
	}
	
	return al.LogEvent(ctx, &ConfigAuditEvent{
		Action:       action,
		Level:        AuditLevelError,
		ResourceType: resourceType,
		ResourceID:   resourceID,
		UserID:       user.ID,
		Username:     user.Username,
		RequestID:    requestID,
		Message:      fmt.Sprintf("操作失败: %s", action),
		Success:      false,
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
	})
}

// QueryAuditLogs 查询审计日志
func (al *AuditLogger) QueryAuditLogs(ctx context.Context, filter *AuditFilter) ([]*ConfigAudit, int64, error) {
	query := al.db.Model(&ConfigAudit{})
	
	// 应用过滤条件
	if filter.Action != "" {
		query = query.Where("action = ?", filter.Action)
	}
	if filter.Level != "" {
		query = query.Where("level = ?", filter.Level)
	}
	if filter.ResourceType != "" {
		query = query.Where("resource_type = ?", filter.ResourceType)
	}
	if filter.ResourceID != "" {
		query = query.Where("resource_id = ?", filter.ResourceID)
	}
	if filter.UserID != "" {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.Scope != "" {
		query = query.Where("scope = ?", filter.Scope)
	}
	if filter.Environment != "" {
		query = query.Where("environment = ?", filter.Environment)
	}
	if !filter.StartTime.IsZero() {
		query = query.Where("created_at >= ?", filter.StartTime)
	}
	if !filter.EndTime.IsZero() {
		query = query.Where("created_at <= ?", filter.EndTime)
	}
	if filter.Success != nil {
		query = query.Where("success = ?", *filter.Success)
	}
	
	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取审计日志总数失败: %w", err)
	}
	
	// 分页查询
	var audits []*ConfigAudit
	offset := (filter.Page - 1) * filter.Size
	err := query.Offset(offset).Limit(filter.Size).Order("created_at DESC").Find(&audits).Error
	if err != nil {
		return nil, 0, fmt.Errorf("查询审计日志失败: %w", err)
	}
	
	return audits, total, nil
}

// CleanupOldLogs 清理旧的审计日志
func (al *AuditLogger) CleanupOldLogs(ctx context.Context) error {
	if al.config.RetentionPeriod <= 0 {
		return nil
	}
	
	cutoffTime := time.Now().Add(-al.config.RetentionPeriod)
	
	result := al.db.Where("created_at < ?", cutoffTime).Delete(&ConfigAudit{})
	if result.Error != nil {
		return fmt.Errorf("清理旧审计日志失败: %w", result.Error)
	}
	
	if result.RowsAffected > 0 {
		al.logger.Info("清理旧审计日志", "deleted_count", result.RowsAffected, "cutoff_time", cutoffTime)
	}
	
	return nil
}

// GetAuditStats 获取审计统计
func (al *AuditLogger) GetAuditStats(ctx context.Context, startTime, endTime time.Time) (*AuditStats, error) {
	stats := &AuditStats{
		StartTime: startTime,
		EndTime:   endTime,
	}
	
	query := al.db.Model(&ConfigAudit{}).Where("created_at BETWEEN ? AND ?", startTime, endTime)
	
	// 总数统计
	if err := query.Count(&stats.TotalEvents).Error; err != nil {
		return nil, fmt.Errorf("获取审计事件总数失败: %w", err)
	}
	
	// 成功/失败统计
	if err := query.Where("success = ?", true).Count(&stats.SuccessEvents).Error; err != nil {
		return nil, fmt.Errorf("获取成功事件数失败: %w", err)
	}
	stats.FailedEvents = stats.TotalEvents - stats.SuccessEvents
	
	// 按动作统计
	var actionStats []struct {
		Action AuditAction `json:"action"`
		Count  int64       `json:"count"`
	}
	if err := query.Select("action, COUNT(*) as count").Group("action").Find(&actionStats).Error; err != nil {
		return nil, fmt.Errorf("获取动作统计失败: %w", err)
	}
	
	stats.ActionStats = make(map[AuditAction]int64)
	for _, stat := range actionStats {
		stats.ActionStats[stat.Action] = stat.Count
	}
	
	// 按用户统计
	var userStats []struct {
		UserID string `json:"user_id"`
		Count  int64  `json:"count"`
	}
	if err := query.Select("user_id, COUNT(*) as count").Group("user_id").Order("count DESC").Limit(10).Find(&userStats).Error; err != nil {
		return nil, fmt.Errorf("获取用户统计失败: %w", err)
	}
	
	stats.TopUsers = make(map[string]int64)
	for _, stat := range userStats {
		stats.TopUsers[stat.UserID] = stat.Count
	}
	
	return stats, nil
}

// shouldLog 检查是否应该记录日志
func (al *AuditLogger) shouldLog(level AuditLevel) bool {
	levelOrder := map[AuditLevel]int{
		AuditLevelInfo:     1,
		AuditLevelWarning:  2,
		AuditLevelError:    3,
		AuditLevelCritical: 4,
	}
	
	configLevel := levelOrder[al.config.Level]
	eventLevel := levelOrder[level]
	
	return eventLevel >= configLevel
}

// writeAuditLog 写入审计日志
func (al *AuditLogger) writeAuditLog(audit *ConfigAudit) error {
	if err := al.db.Create(audit).Error; err != nil {
		return fmt.Errorf("写入审计日志失败: %w", err)
	}
	return nil
}

// AuditFilter 审计日志过滤器
type AuditFilter struct {
	Action       AuditAction `json:"action"`
	Level        AuditLevel  `json:"level"`
	ResourceType string      `json:"resource_type"`
	ResourceID   string      `json:"resource_id"`
	UserID       string      `json:"user_id"`
	Scope        ConfigScope `json:"scope"`
	Environment  string      `json:"environment"`
	StartTime    time.Time   `json:"start_time"`
	EndTime      time.Time   `json:"end_time"`
	Success      *bool       `json:"success"`
	Page         int         `json:"page"`
	Size         int         `json:"size"`
}

// AuditStats 审计统计
type AuditStats struct {
	StartTime     time.Time                `json:"start_time"`
	EndTime       time.Time                `json:"end_time"`
	TotalEvents   int64                    `json:"total_events"`
	SuccessEvents int64                    `json:"success_events"`
	FailedEvents  int64                    `json:"failed_events"`
	ActionStats   map[AuditAction]int64    `json:"action_stats"`
	TopUsers      map[string]int64         `json:"top_users"`
}
