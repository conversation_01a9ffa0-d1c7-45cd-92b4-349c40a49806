package config

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockLogger 模拟日志记录器
type MockLogger struct {
	mock.Mock
}

func (m *<PERSON>ckLogger) Debug(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Info(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Warn(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Error(msg string, args ...interface{}) {
	m.Called(msg, args)
}

// MockEncryptor 模拟加密器
type MockEncryptor struct {
	mock.Mock
}

func (m *MockEncryptor) Encrypt(plaintext string) (string, error) {
	args := m.Called(plaintext)
	return args.String(0), args.Error(1)
}

func (m *MockEncryptor) Decrypt(ciphertext string) (string, error) {
	args := m.Called(ciphertext)
	return args.String(0), args.Error(1)
}

func (m *MockEncryptor) GenerateKey() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 自动迁移
	err = db.AutoMigrate(
		&Config{},
		&ConfigVersion{},
		&Secret{},
		&ConfigTemplate{},
		&FeatureFlag{},
		&ConfigSet{},
		&ConfigDistribution{},
		&ConfigAudit{},
	)
	assert.NoError(t, err)

	return db
}

// TestConfigService_CreateConfig 测试创建配置
func TestConfigService_CreateConfig(t *testing.T) {
	db := setupTestDB(t)
	mockLogger := &MockLogger{}
	mockEncryptor := &MockEncryptor{}

	// 设置 mock 期望
	mockLogger.On("Info", mock.Anything, mock.Anything).Return()
	mockEncryptor.On("Encrypt", "test_value").Return("encrypted_value", nil)

	service := NewConfigService(db, mockEncryptor, mockLogger, ServiceConfig{
		CacheEnabled:      false, // 测试时禁用缓存
		VersionLimit:      10,
		AuditEnabled:      false, // 测试时禁用审计
		ValidationEnabled: true,
	})

	ctx := context.Background()
	req := &CreateConfigRequest{
		Key:         "test.key",
		Value:       "test_value",
		Type:        ConfigTypeString,
		Scope:       ConfigScopeApplication,
		ScopeID:     "app-123",
		Environment: "development",
		Description: "测试配置",
		Encrypted:   true,
		CreatedBy:   "user-123",
	}

	// 执行测试
	config, err := service.CreateConfig(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, config)
	assert.Equal(t, req.Key, config.Key)
	assert.Equal(t, "encrypted_value", config.Value)
	assert.Equal(t, req.Type, config.Type)
	assert.Equal(t, req.Scope, config.Scope)
	assert.Equal(t, req.ScopeID, config.ScopeID)
	assert.Equal(t, req.Environment, config.Environment)
	assert.Equal(t, req.Description, config.Description)
	assert.True(t, config.Encrypted)
	assert.Equal(t, int64(1), int64(config.Version))
	assert.Equal(t, ConfigStatusActive, config.Status)

	// 验证 mock 调用
	mockLogger.AssertExpectations(t)
	mockEncryptor.AssertExpectations(t)
}

// TestConfigService_GetConfig 测试获取配置
func TestConfigService_GetConfig(t *testing.T) {
	db := setupTestDB(t)
	mockLogger := &MockLogger{}
	mockEncryptor := &MockEncryptor{}

	// 设置 mock 期望
	mockLogger.On("Debug", mock.Anything, mock.Anything).Return()

	service := NewConfigService(db, mockEncryptor, mockLogger, ServiceConfig{
		CacheEnabled:      false,
		VersionLimit:      10,
		AuditEnabled:      false,
		ValidationEnabled: true,
	})

	// 先创建一个配置
	config := &Config{
		ID:          "config-123",
		Key:         "test.key",
		Value:       "encrypted_value",
		Type:        ConfigTypeString,
		Scope:       ConfigScopeApplication,
		ScopeID:     "app-123",
		Environment: "development",
		Encrypted:   false, // 先设置为不加密，避免类型转换问题
		Status:      ConfigStatusActive,
		Version:     1,
		CreatedAt:   time.Now(),
		CreatedBy:   "user-123",
	}
	err := db.Create(config).Error
	assert.NoError(t, err)

	ctx := context.Background()

	// 执行测试
	result, err := service.GetConfig(ctx, "test.key", ConfigScopeApplication, "app-123", "development")

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, config.Key, result.Key)
	assert.Equal(t, "encrypted_value", result.Value) // 应该是解密后的值
	assert.Equal(t, config.Type, result.Type)
	assert.Equal(t, config.Scope, result.Scope)

	// 验证 mock 调用
	mockEncryptor.AssertExpectations(t)
}

// TestConfigService_UpdateConfig 测试更新配置
func TestConfigService_UpdateConfig(t *testing.T) {
	db := setupTestDB(t)
	mockLogger := &MockLogger{}
	mockEncryptor := &MockEncryptor{}

	// 设置 mock 期望
	mockLogger.On("Info", mock.Anything, mock.Anything).Return()
	mockEncryptor.On("Encrypt", "new_value").Return("new_encrypted_value", nil)
	mockEncryptor.On("Decrypt", "old_value").Return("decrypted_old_value", nil)
	mockEncryptor.On("Decrypt", "new_encrypted_value").Return("new_value", nil)

	service := NewConfigService(db, mockEncryptor, mockLogger, ServiceConfig{
		CacheEnabled:      false,
		VersionLimit:      10,
		AuditEnabled:      false,
		ValidationEnabled: true,
	})

	// 先创建一个配置
	config := &Config{
		ID:          "config-123",
		Key:         "test.key",
		Value:       "old_value",
		Type:        ConfigTypeString,
		Scope:       ConfigScopeApplication,
		ScopeID:     "app-123",
		Environment: "development",
		Encrypted:   true,
		Status:      ConfigStatusActive,
		Version:     1,
		CreatedAt:   time.Now(),
		CreatedBy:   "user-123",
	}
	err := db.Create(config).Error
	assert.NoError(t, err)

	ctx := context.Background()
	newValue := "new_value"
	newDescription := "更新后的描述"
	req := &UpdateConfigRequest{
		Value:       newValue,
		Description: &newDescription,
		UpdatedBy:   "user-456",
	}

	// 执行测试
	result, err := service.UpdateConfig(ctx, config.ID, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "new_value", result.Value) // 应该是解密后的值
	assert.Equal(t, newDescription, result.Description)
	assert.Equal(t, int64(2), int64(result.Version)) // 版本应该增加
	assert.Equal(t, req.UpdatedBy, result.UpdatedBy)

	// 验证版本历史是否创建
	var versions []ConfigVersion
	err = db.Where("config_id = ?", config.ID).Find(&versions).Error
	assert.NoError(t, err)
	assert.Len(t, versions, 1) // 应该有一个版本记录

	// 验证 mock 调用
	mockLogger.AssertExpectations(t)
	mockEncryptor.AssertExpectations(t)
}

// TestConfigService_DeleteConfig 测试删除配置
func TestConfigService_DeleteConfig(t *testing.T) {
	db := setupTestDB(t)
	mockLogger := &MockLogger{}
	mockEncryptor := &MockEncryptor{}

	// 设置 mock 期望
	mockLogger.On("Info", mock.Anything, mock.Anything).Return()

	service := NewConfigService(db, mockEncryptor, mockLogger, ServiceConfig{
		CacheEnabled:      false,
		VersionLimit:      10,
		AuditEnabled:      false,
		ValidationEnabled: true,
	})

	// 先创建一个配置
	config := &Config{
		ID:          "config-123",
		Key:         "test.key",
		Value:       "test_value",
		Type:        ConfigTypeString,
		Scope:       ConfigScopeApplication,
		ScopeID:     "app-123",
		Environment: "development",
		Status:      ConfigStatusActive,
		Version:     1,
		CreatedAt:   time.Now(),
		CreatedBy:   "user-123",
	}
	err := db.Create(config).Error
	assert.NoError(t, err)

	ctx := context.Background()

	// 执行测试
	err = service.DeleteConfig(ctx, config.ID)

	// 验证结果
	assert.NoError(t, err)

	// 验证配置状态是否更新为已删除
	var deletedConfig Config
	err = db.First(&deletedConfig, "id = ?", config.ID).Error
	assert.NoError(t, err)
	assert.Equal(t, ConfigStatusDeleted, deletedConfig.Status)

	// 验证 mock 调用
	mockLogger.AssertExpectations(t)
}

// TestConfigService_GetEffectiveConfig 测试获取有效配置
func TestConfigService_GetEffectiveConfig(t *testing.T) {
	db := setupTestDB(t)
	mockLogger := &MockLogger{}
	mockEncryptor := &MockEncryptor{}

	// 设置 mock 期望
	mockLogger.On("Debug", mock.Anything, mock.Anything).Return()

	service := NewConfigService(db, mockEncryptor, mockLogger, ServiceConfig{
		CacheEnabled:      false,
		VersionLimit:      10,
		AuditEnabled:      false,
		ValidationEnabled: true,
	})

	// 创建多个层级的配置
	configs := []*Config{
		{
			ID:          "global-config",
			Key:         "test.key",
			Value:       "global_value",
			Type:        ConfigTypeString,
			Scope:       ConfigScopeGlobal,
			ScopeID:     "",
			Environment: "development",
			Status:      ConfigStatusActive,
			Version:     1,
			CreatedAt:   time.Now(),
			CreatedBy:   "user-123",
		},
		{
			ID:          "app-config",
			Key:         "test.key",
			Value:       "app_value",
			Type:        ConfigTypeString,
			Scope:       ConfigScopeApplication,
			ScopeID:     "app-123",
			Environment: "development",
			Status:      ConfigStatusActive,
			Version:     1,
			CreatedAt:   time.Now(),
			CreatedBy:   "user-123",
		},
	}

	for _, config := range configs {
		err := db.Create(config).Error
		assert.NoError(t, err)
	}

	ctx := context.Background()

	// 执行测试 - 应该返回应用级别的配置（优先级更高）
	result, err := service.GetEffectiveConfig(ctx, "test.key", ConfigScopeApplication, "app-123", "development")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, "app_value", result) // 应该返回应用级别的值

	// 测试不存在应用级别配置时，返回全局配置
	result, err = service.GetEffectiveConfig(ctx, "test.key", ConfigScopeApplication, "app-456", "development")
	assert.NoError(t, err)
	assert.Equal(t, "global_value", result) // 应该返回全局级别的值
}

// BenchmarkConfigService_GetConfig 性能测试
func BenchmarkConfigService_GetConfig(b *testing.B) {
	db := setupTestDB(&testing.T{})
	mockLogger := &MockLogger{}
	mockEncryptor := &MockEncryptor{}

	// 设置 mock 期望
	mockLogger.On("Debug", mock.Anything, mock.Anything).Return()

	service := NewConfigService(db, mockEncryptor, mockLogger, ServiceConfig{
		CacheEnabled:      false,
		VersionLimit:      10,
		AuditEnabled:      false,
		ValidationEnabled: false, // 性能测试时禁用验证
	})

	// 创建测试配置
	config := &Config{
		ID:          "config-123",
		Key:         "test.key",
		Value:       "test_value",
		Type:        ConfigTypeString,
		Scope:       ConfigScopeApplication,
		ScopeID:     "app-123",
		Environment: "development",
		Status:      ConfigStatusActive,
		Version:     1,
		CreatedAt:   time.Now(),
		CreatedBy:   "user-123",
	}
	db.Create(config)

	ctx := context.Background()

	// 重置计时器
	b.ResetTimer()

	// 运行基准测试
	for i := 0; i < b.N; i++ {
		_, err := service.GetConfig(ctx, "test.key", ConfigScopeApplication, "app-123", "development")
		if err != nil {
			b.Fatal(err)
		}
	}
}
