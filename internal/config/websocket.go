package config

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// WebSocketManager WebSocket 管理器
type WebSocketManager struct {
	clients    map[string]*WebSocketClient
	mutex      sync.RWMutex
	logger     Logger
	upgrader   websocket.Upgrader
	subManager *ConfigSubscriptionManager
}

// WebSocketClient WebSocket 客户端
type WebSocketClient struct {
	ID         string
	Conn       *websocket.Conn
	Send       chan []byte
	Manager    *WebSocketManager
	LastPing   time.Time
	Subscriptions map[string]bool // 订阅的配置键
}

// WebSocketMessage WebSocket 消息
type WebSocketMessage struct {
	Type    string      `json:"type"`    // subscribe, unsubscribe, ping, pong
	Data    interface{} `json:"data"`
	ID      string      `json:"id"`      // 消息ID
	Timestamp int64     `json:"timestamp"`
}

// SubscribeRequest 订阅请求
type SubscribeRequest struct {
	Key         string      `json:"key"`
	Scope       ConfigScope `json:"scope"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment"`
}

// UnsubscribeRequest 取消订阅请求
type UnsubscribeRequest struct {
	Key         string      `json:"key"`
	Scope       ConfigScope `json:"scope"`
	ScopeID     string      `json:"scope_id"`
	Environment string      `json:"environment"`
}

// NewWebSocketManager 创建 WebSocket 管理器
func NewWebSocketManager(logger Logger) *WebSocketManager {
	manager := &WebSocketManager{
		clients: make(map[string]*WebSocketClient),
		logger:  logger,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查来源
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}
	
	// 启动清理协程
	go manager.cleanup()
	
	return manager
}

// SetSubscriptionManager 设置订阅管理器
func (m *WebSocketManager) SetSubscriptionManager(subManager *ConfigSubscriptionManager) {
	m.subManager = subManager
}

// HandleWebSocket 处理 WebSocket 连接
func (m *WebSocketManager) HandleWebSocket(c *gin.Context) {
	conn, err := m.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		m.logger.Error("WebSocket 升级失败", "error", err)
		return
	}

	clientID := uuid.New().String()
	client := &WebSocketClient{
		ID:            clientID,
		Conn:          conn,
		Send:          make(chan []byte, 256),
		Manager:       m,
		LastPing:      time.Now(),
		Subscriptions: make(map[string]bool),
	}

	m.mutex.Lock()
	m.clients[clientID] = client
	m.mutex.Unlock()

	m.logger.Info("WebSocket 客户端连接", "client_id", clientID)

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}

// BroadcastConfigChange 广播配置变更
func (m *WebSocketManager) BroadcastConfigChange(event *ConfigChangeEvent) {
	message := WebSocketMessage{
		Type:      "config_change",
		Data:      event,
		ID:        uuid.New().String(),
		Timestamp: time.Now().Unix(),
	}

	data, err := json.Marshal(message)
	if err != nil {
		m.logger.Error("序列化配置变更消息失败", "error", err)
		return
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 广播给所有客户端
	for _, client := range m.clients {
		select {
		case client.Send <- data:
		default:
			// 客户端发送缓冲区已满，关闭连接
			close(client.Send)
			delete(m.clients, client.ID)
		}
	}

	m.logger.Info("广播配置变更消息", "clients", len(m.clients))
}

// SendToClient 发送消息给指定客户端
func (m *WebSocketManager) SendToClient(clientID string, message WebSocketMessage) error {
	m.mutex.RLock()
	client, exists := m.clients[clientID]
	m.mutex.RUnlock()

	if !exists {
		return ErrClientNotFound
	}

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	select {
	case client.Send <- data:
		return nil
	default:
		return ErrClientSendBufferFull
	}
}

// RemoveClient 移除客户端
func (m *WebSocketManager) RemoveClient(clientID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if client, exists := m.clients[clientID]; exists {
		// 移除所有订阅
		if m.subManager != nil {
			m.subManager.RemoveAllSubscriptions(clientID)
		}
		
		close(client.Send)
		client.Conn.Close()
		delete(m.clients, clientID)
		
		m.logger.Info("移除 WebSocket 客户端", "client_id", clientID)
	}
}

// GetClientCount 获取客户端数量
func (m *WebSocketManager) GetClientCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return len(m.clients)
}

// cleanup 清理断开的连接
func (m *WebSocketManager) cleanup() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		m.mutex.Lock()
		for clientID, client := range m.clients {
			// 检查客户端是否超时
			if time.Since(client.LastPing) > 60*time.Second {
				m.logger.Info("清理超时的 WebSocket 客户端", "client_id", clientID)
				
				// 移除所有订阅
				if m.subManager != nil {
					m.subManager.RemoveAllSubscriptions(clientID)
				}
				
				close(client.Send)
				client.Conn.Close()
				delete(m.clients, clientID)
			}
		}
		m.mutex.Unlock()
	}
}

// readPump 读取消息
func (c *WebSocketClient) readPump() {
	defer func() {
		c.Manager.RemoveClient(c.ID)
	}()

	// 设置读取超时
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.LastPing = time.Now()
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, messageData, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.Manager.logger.Error("WebSocket 读取错误", "error", err, "client_id", c.ID)
			}
			break
		}

		var message WebSocketMessage
		if err := json.Unmarshal(messageData, &message); err != nil {
			c.Manager.logger.Error("WebSocket 消息解析失败", "error", err, "client_id", c.ID)
			continue
		}

		c.handleMessage(&message)
	}
}

// writePump 写入消息
func (c *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				c.Manager.logger.Error("WebSocket 写入失败", "error", err, "client_id", c.ID)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理消息
func (c *WebSocketClient) handleMessage(message *WebSocketMessage) {
	switch message.Type {
	case "subscribe":
		c.handleSubscribe(message)
	case "unsubscribe":
		c.handleUnsubscribe(message)
	case "ping":
		c.handlePing(message)
	default:
		c.Manager.logger.Warn("未知的 WebSocket 消息类型", "type", message.Type, "client_id", c.ID)
	}
}

// handleSubscribe 处理订阅
func (c *WebSocketClient) handleSubscribe(message *WebSocketMessage) {
	var req SubscribeRequest
	data, _ := json.Marshal(message.Data)
	if err := json.Unmarshal(data, &req); err != nil {
		c.Manager.logger.Error("解析订阅请求失败", "error", err, "client_id", c.ID)
		return
	}

	// 添加订阅
	if c.Manager.subManager != nil {
		if err := c.Manager.subManager.AddSubscription(c.ID, req.Key, req.Scope, req.ScopeID); err != nil {
			c.Manager.logger.Error("添加订阅失败", "error", err, "client_id", c.ID)
			return
		}
	}

	// 记录订阅
	subscriptionKey := fmt.Sprintf("%s:%s:%s", req.Key, req.Scope, req.ScopeID)
	c.Subscriptions[subscriptionKey] = true

	// 发送确认消息
	response := WebSocketMessage{
		Type:      "subscribe_ack",
		Data:      req,
		ID:        message.ID,
		Timestamp: time.Now().Unix(),
	}

	data, _ = json.Marshal(response)
	select {
	case c.Send <- data:
	default:
		c.Manager.logger.Warn("客户端发送缓冲区已满", "client_id", c.ID)
	}

	c.Manager.logger.Info("客户端订阅配置", "client_id", c.ID, "key", req.Key, "scope", req.Scope)
}

// handleUnsubscribe 处理取消订阅
func (c *WebSocketClient) handleUnsubscribe(message *WebSocketMessage) {
	var req UnsubscribeRequest
	data, _ := json.Marshal(message.Data)
	if err := json.Unmarshal(data, &req); err != nil {
		c.Manager.logger.Error("解析取消订阅请求失败", "error", err, "client_id", c.ID)
		return
	}

	// 移除订阅
	if c.Manager.subManager != nil {
		c.Manager.subManager.RemoveSubscription(c.ID, req.Key, req.Scope, req.ScopeID)
	}

	// 移除记录
	subscriptionKey := fmt.Sprintf("%s:%s:%s", req.Key, req.Scope, req.ScopeID)
	delete(c.Subscriptions, subscriptionKey)

	// 发送确认消息
	response := WebSocketMessage{
		Type:      "unsubscribe_ack",
		Data:      req,
		ID:        message.ID,
		Timestamp: time.Now().Unix(),
	}

	data, _ = json.Marshal(response)
	select {
	case c.Send <- data:
	default:
		c.Manager.logger.Warn("客户端发送缓冲区已满", "client_id", c.ID)
	}

	c.Manager.logger.Info("客户端取消订阅配置", "client_id", c.ID, "key", req.Key, "scope", req.Scope)
}

// handlePing 处理 ping
func (c *WebSocketClient) handlePing(message *WebSocketMessage) {
	c.LastPing = time.Now()

	// 发送 pong 响应
	response := WebSocketMessage{
		Type:      "pong",
		Data:      message.Data,
		ID:        message.ID,
		Timestamp: time.Now().Unix(),
	}

	data, _ := json.Marshal(response)
	select {
	case c.Send <- data:
	default:
		c.Manager.logger.Warn("客户端发送缓冲区已满", "client_id", c.ID)
	}
}

// 错误定义
var (
	ErrClientNotFound       = fmt.Errorf("客户端不存在")
	ErrClientSendBufferFull = fmt.Errorf("客户端发送缓冲区已满")
)
