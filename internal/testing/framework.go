package testing

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	appLogger "paas-platform/pkg/logger"
)

// TestFramework 测试框架
type TestFramework struct {
	suite.Suite
	DB     *gorm.DB
	Logger appLogger.Logger
	ctx    context.Context
}

// SetupSuite 测试套件初始化
func (tf *TestFramework) SetupSuite() {
	// 设置测试环境变量
	os.Setenv("ENV", "test")
	os.Setenv("LOG_LEVEL", "error")
	
	// 初始化测试数据库
	tf.setupTestDB()
	
	// 初始化测试日志
	tf.setupTestLogger()
	
	// 设置测试上下文
	tf.ctx = context.Background()
}

// TearDownSuite 测试套件清理
func (tf *TestFramework) TearDownSuite() {
	if tf.DB != nil {
		sqlDB, _ := tf.DB.DB()
		sqlDB.Close()
	}
}

// SetupTest 每个测试前的初始化
func (tf *TestFramework) SetupTest() {
	// 清理测试数据
	tf.cleanupTestData()
}

// TearDownTest 每个测试后的清理
func (tf *TestFramework) TearDownTest() {
	// 清理测试数据
	tf.cleanupTestData()
}

// setupTestDB 设置测试数据库
func (tf *TestFramework) setupTestDB() {
	// 使用内存数据库进行测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	tf.Require().NoError(err)
	
	tf.DB = db
}

// setupTestLogger 设置测试日志
func (tf *TestFramework) setupTestLogger() {
	tf.Logger = &MockLogger{}
}

// cleanupTestData 清理测试数据
func (tf *TestFramework) cleanupTestData() {
	if tf.DB == nil {
		return
	}
	
	// 获取所有表名
	var tables []string
	tf.DB.Raw("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").Scan(&tables)
	
	// 清空所有表
	for _, table := range tables {
		tf.DB.Exec(fmt.Sprintf("DELETE FROM %s", table))
	}
}

// GetTestDB 获取测试数据库
func (tf *TestFramework) GetTestDB() *gorm.DB {
	return tf.DB
}

// GetTestLogger 获取测试日志
func (tf *TestFramework) GetTestLogger() appLogger.Logger {
	return tf.Logger
}

// GetTestContext 获取测试上下文
func (tf *TestFramework) GetTestContext() context.Context {
	return tf.ctx
}

// MockLogger 模拟日志器
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(msg string, fields ...interface{}) {
	args := []interface{}{msg}
	args = append(args, fields...)
	m.Called(args...)
}

func (m *MockLogger) Info(msg string, fields ...interface{}) {
	args := []interface{}{msg}
	args = append(args, fields...)
	m.Called(args...)
}

func (m *MockLogger) Warn(msg string, fields ...interface{}) {
	args := []interface{}{msg}
	args = append(args, fields...)
	m.Called(args...)
}

func (m *MockLogger) Error(msg string, fields ...interface{}) {
	args := []interface{}{msg}
	args = append(args, fields...)
	m.Called(args...)
}

func (m *MockLogger) Fatal(msg string, fields ...interface{}) {
	args := []interface{}{msg}
	args = append(args, fields...)
	m.Called(args...)
}

func (m *MockLogger) With(fields ...interface{}) appLogger.Logger {
	return m
}

// TestHelper 测试辅助工具
type TestHelper struct {
	t *testing.T
}

// NewTestHelper 创建测试辅助工具
func NewTestHelper(t *testing.T) *TestHelper {
	return &TestHelper{t: t}
}

// AssertNoError 断言无错误
func (h *TestHelper) AssertNoError(err error, msgAndArgs ...interface{}) {
	assert.NoError(h.t, err, msgAndArgs...)
}

// AssertError 断言有错误
func (h *TestHelper) AssertError(err error, msgAndArgs ...interface{}) {
	assert.Error(h.t, err, msgAndArgs...)
}

// AssertEqual 断言相等
func (h *TestHelper) AssertEqual(expected, actual interface{}, msgAndArgs ...interface{}) {
	assert.Equal(h.t, expected, actual, msgAndArgs...)
}

// AssertNotEqual 断言不相等
func (h *TestHelper) AssertNotEqual(expected, actual interface{}, msgAndArgs ...interface{}) {
	assert.NotEqual(h.t, expected, actual, msgAndArgs...)
}

// AssertNil 断言为空
func (h *TestHelper) AssertNil(object interface{}, msgAndArgs ...interface{}) {
	assert.Nil(h.t, object, msgAndArgs...)
}

// AssertNotNil 断言不为空
func (h *TestHelper) AssertNotNil(object interface{}, msgAndArgs ...interface{}) {
	assert.NotNil(h.t, object, msgAndArgs...)
}

// AssertTrue 断言为真
func (h *TestHelper) AssertTrue(value bool, msgAndArgs ...interface{}) {
	assert.True(h.t, value, msgAndArgs...)
}

// AssertFalse 断言为假
func (h *TestHelper) AssertFalse(value bool, msgAndArgs ...interface{}) {
	assert.False(h.t, value, msgAndArgs...)
}

// AssertContains 断言包含
func (h *TestHelper) AssertContains(s, contains interface{}, msgAndArgs ...interface{}) {
	assert.Contains(h.t, s, contains, msgAndArgs...)
}

// AssertNotContains 断言不包含
func (h *TestHelper) AssertNotContains(s, contains interface{}, msgAndArgs ...interface{}) {
	assert.NotContains(h.t, s, contains, msgAndArgs...)
}

// AssertLen 断言长度
func (h *TestHelper) AssertLen(object interface{}, length int, msgAndArgs ...interface{}) {
	assert.Len(h.t, object, length, msgAndArgs...)
}

// AssertEmpty 断言为空
func (h *TestHelper) AssertEmpty(object interface{}, msgAndArgs ...interface{}) {
	assert.Empty(h.t, object, msgAndArgs...)
}

// AssertNotEmpty 断言不为空
func (h *TestHelper) AssertNotEmpty(object interface{}, msgAndArgs ...interface{}) {
	assert.NotEmpty(h.t, object, msgAndArgs...)
}

// AssertGreater 断言大于
func (h *TestHelper) AssertGreater(e1, e2 interface{}, msgAndArgs ...interface{}) {
	assert.Greater(h.t, e1, e2, msgAndArgs...)
}

// AssertLess 断言小于
func (h *TestHelper) AssertLess(e1, e2 interface{}, msgAndArgs ...interface{}) {
	assert.Less(h.t, e1, e2, msgAndArgs...)
}

// AssertWithinDuration 断言时间差在范围内
func (h *TestHelper) AssertWithinDuration(expected, actual time.Time, delta time.Duration, msgAndArgs ...interface{}) {
	assert.WithinDuration(h.t, expected, actual, delta, msgAndArgs...)
}

// TestData 测试数据生成器
type TestData struct{}

// NewTestData 创建测试数据生成器
func NewTestData() *TestData {
	return &TestData{}
}

// GenerateUUID 生成测试用UUID
func (td *TestData) GenerateUUID() string {
	return fmt.Sprintf("test-uuid-%d", time.Now().UnixNano())
}

// GenerateEmail 生成测试邮箱
func (td *TestData) GenerateEmail() string {
	return fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
}

// GenerateUsername 生成测试用户名
func (td *TestData) GenerateUsername() string {
	return fmt.Sprintf("testuser-%d", time.Now().UnixNano())
}

// GenerateAppName 生成测试应用名
func (td *TestData) GenerateAppName() string {
	return fmt.Sprintf("test-app-%d", time.Now().UnixNano())
}

// GeneratePipelineName 生成测试流水线名
func (td *TestData) GeneratePipelineName() string {
	return fmt.Sprintf("test-pipeline-%d", time.Now().UnixNano())
}

// DatabaseHelper 数据库测试辅助工具
type DatabaseHelper struct {
	db *gorm.DB
}

// NewDatabaseHelper 创建数据库测试辅助工具
func NewDatabaseHelper(db *gorm.DB) *DatabaseHelper {
	return &DatabaseHelper{db: db}
}

// CreateTestUser 创建测试用户
func (dh *DatabaseHelper) CreateTestUser(data map[string]interface{}) error {
	user := map[string]interface{}{
		"id":         NewTestData().GenerateUUID(),
		"name":       "测试用户",
		"email":      NewTestData().GenerateEmail(),
		"created_at": time.Now(),
		"updated_at": time.Now(),
	}
	
	// 合并自定义数据
	for k, v := range data {
		user[k] = v
	}
	
	return dh.db.Table("users").Create(user).Error
}

// CreateTestApp 创建测试应用
func (dh *DatabaseHelper) CreateTestApp(data map[string]interface{}) error {
	app := map[string]interface{}{
		"id":          NewTestData().GenerateUUID(),
		"name":        NewTestData().GenerateAppName(),
		"description": "测试应用",
		"status":      "active",
		"created_at":  time.Now(),
		"updated_at":  time.Now(),
	}
	
	// 合并自定义数据
	for k, v := range data {
		app[k] = v
	}
	
	return dh.db.Table("applications").Create(app).Error
}

// CreateTestPipeline 创建测试流水线
func (dh *DatabaseHelper) CreateTestPipeline(data map[string]interface{}) error {
	pipeline := map[string]interface{}{
		"id":          NewTestData().GenerateUUID(),
		"name":        NewTestData().GeneratePipelineName(),
		"description": "测试流水线",
		"status":      "active",
		"created_at":  time.Now(),
		"updated_at":  time.Now(),
	}
	
	// 合并自定义数据
	for k, v := range data {
		pipeline[k] = v
	}
	
	return dh.db.Table("pipelines").Create(pipeline).Error
}

// CountRecords 统计记录数
func (dh *DatabaseHelper) CountRecords(table string) (int64, error) {
	var count int64
	err := dh.db.Table(table).Count(&count).Error
	return count, err
}

// RecordExists 检查记录是否存在
func (dh *DatabaseHelper) RecordExists(table string, conditions map[string]interface{}) (bool, error) {
	var count int64
	query := dh.db.Table(table)
	
	for k, v := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", k), v)
	}
	
	err := query.Count(&count).Error
	return count > 0, err
}

// GetRecord 获取记录
func (dh *DatabaseHelper) GetRecord(table string, conditions map[string]interface{}) (map[string]interface{}, error) {
	var result map[string]interface{}
	query := dh.db.Table(table)
	
	for k, v := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", k), v)
	}
	
	err := query.First(&result).Error
	return result, err
}

// HTTPTestHelper HTTP测试辅助工具
type HTTPTestHelper struct {
	baseURL string
}

// NewHTTPTestHelper 创建HTTP测试辅助工具
func NewHTTPTestHelper(baseURL string) *HTTPTestHelper {
	return &HTTPTestHelper{baseURL: baseURL}
}

// BuildURL 构建测试URL
func (h *HTTPTestHelper) BuildURL(path string) string {
	return fmt.Sprintf("%s%s", h.baseURL, path)
}

// TestConfig 测试配置
type TestConfig struct {
	DatabaseURL    string
	LogLevel       string
	TestTimeout    time.Duration
	CleanupEnabled bool
}

// DefaultTestConfig 默认测试配置
func DefaultTestConfig() *TestConfig {
	return &TestConfig{
		DatabaseURL:    ":memory:",
		LogLevel:       "error",
		TestTimeout:    30 * time.Second,
		CleanupEnabled: true,
	}
}

// TestRunner 测试运行器
type TestRunner struct {
	config *TestConfig
	suites []suite.TestingSuite
}

// NewTestRunner 创建测试运行器
func NewTestRunner(config *TestConfig) *TestRunner {
	if config == nil {
		config = DefaultTestConfig()
	}
	
	return &TestRunner{
		config: config,
		suites: make([]suite.TestingSuite, 0),
	}
}

// AddSuite 添加测试套件
func (tr *TestRunner) AddSuite(s suite.TestingSuite) {
	tr.suites = append(tr.suites, s)
}

// Run 运行所有测试套件
func (tr *TestRunner) Run(t *testing.T) {
	for _, s := range tr.suites {
		suite.Run(t, s)
	}
}
