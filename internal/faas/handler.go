package faas

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// Handler FaaS HTTP处理器
type Handler struct {
	executor *FunctionExecutor
	logger   logger.Logger
}

// NewHandler 创建FaaS处理器
func NewHandler(executor *FunctionExecutor, logger logger.Logger) *Handler {
	return &Handler{
		executor: executor,
		logger:   logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 函数执行相关路由
	router.POST("/functions/execute", h.ExecuteFunction)
	router.POST("/functions/async-execute", h.AsyncExecuteFunction)
	
	// 函数管理路由
	router.GET("/functions/metrics", h.GetExecutorMetrics)
	router.GET("/functions/pool/stats", h.GetPoolStats)
	
	// 健康检查
	router.GET("/functions/health", h.HealthCheck)
}

// ExecuteFunction 同步执行函数
// @Summary 同步执行函数
// @Description 同步执行FaaS函数并返回结果
// @Tags FaaS
// @Accept json
// @Produce json
// @Param request body FunctionRequest true "函数执行请求"
// @Success 200 {object} FunctionResponse "执行成功"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/faas/functions/execute [post]
func (h *Handler) ExecuteFunction(c *gin.Context) {
	var request FunctionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 生成请求ID
	if request.RequestID == "" {
		request.RequestID = generateRequestID()
	}

	h.logger.Info("收到函数执行请求", "function_id", request.FunctionID, 
		"request_id", request.RequestID, "runtime", request.Runtime)

	// 执行函数
	response, err := h.executor.ExecuteFunction(c.Request.Context(), &request)
	if err != nil {
		h.logger.Error("函数执行失败", "function_id", request.FunctionID, 
			"request_id", request.RequestID, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":      "函数执行失败",
			"details":    err.Error(),
			"request_id": request.RequestID,
		})
		return
	}

	h.logger.Info("函数执行完成", "function_id", request.FunctionID, 
		"request_id", request.RequestID, "status", response.Status, 
		"execution_time", response.ExecutionTime)

	c.JSON(http.StatusOK, response)
}

// AsyncExecuteFunction 异步执行函数
// @Summary 异步执行函数
// @Description 异步执行FaaS函数，立即返回请求ID
// @Tags FaaS
// @Accept json
// @Produce json
// @Param request body FunctionRequest true "函数执行请求"
// @Success 202 {object} map[string]interface{} "请求已接受"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/faas/functions/async-execute [post]
func (h *Handler) AsyncExecuteFunction(c *gin.Context) {
	var request FunctionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 生成请求ID
	if request.RequestID == "" {
		request.RequestID = generateRequestID()
	}

	h.logger.Info("收到异步函数执行请求", "function_id", request.FunctionID, 
		"request_id", request.RequestID, "runtime", request.Runtime)

	// 异步执行函数
	go func() {
		response, err := h.executor.ExecuteFunction(c.Request.Context(), &request)
		if err != nil {
			h.logger.Error("异步函数执行失败", "function_id", request.FunctionID, 
				"request_id", request.RequestID, "error", err)
		} else {
			h.logger.Info("异步函数执行完成", "function_id", request.FunctionID, 
				"request_id", request.RequestID, "status", response.Status, 
				"execution_time", response.ExecutionTime)
		}
	}()

	// 立即返回请求ID
	c.JSON(http.StatusAccepted, gin.H{
		"message":    "函数执行请求已接受",
		"request_id": request.RequestID,
		"status":     "accepted",
		"timestamp":  time.Now(),
	})
}

// GetExecutorMetrics 获取执行器指标
// @Summary 获取执行器指标
// @Description 获取FaaS执行器的性能指标
// @Tags FaaS
// @Produce json
// @Success 200 {object} ExecutorMetrics "执行器指标"
// @Router /api/v1/faas/functions/metrics [get]
func (h *Handler) GetExecutorMetrics(c *gin.Context) {
	metrics := h.executor.GetMetrics()
	
	h.logger.Debug("获取执行器指标", "total_executions", metrics.TotalExecutions, 
		"active_executions", metrics.ActiveExecutions)

	c.JSON(http.StatusOK, gin.H{
		"metrics":   metrics,
		"timestamp": time.Now(),
	})
}

// GetPoolStats 获取容器池统计
// @Summary 获取容器池统计
// @Description 获取FaaS容器池的统计信息
// @Tags FaaS
// @Produce json
// @Success 200 {object} map[string]interface{} "容器池统计"
// @Router /api/v1/faas/functions/pool/stats [get]
func (h *Handler) GetPoolStats(c *gin.Context) {
	stats := h.executor.containerPool.GetPoolStats()
	
	h.logger.Debug("获取容器池统计", "total_containers", stats["total_containers"])

	c.JSON(http.StatusOK, gin.H{
		"pool_stats": stats,
		"timestamp":  time.Now(),
	})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description FaaS服务健康检查
// @Tags FaaS
// @Produce json
// @Success 200 {object} map[string]interface{} "健康状态"
// @Router /api/v1/faas/functions/health [get]
func (h *Handler) HealthCheck(c *gin.Context) {
	metrics := h.executor.GetMetrics()
	poolStats := h.executor.containerPool.GetPoolStats()

	health := gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"service":   "faas-executor",
		"metrics": gin.H{
			"active_executions":  metrics.ActiveExecutions,
			"total_executions":   metrics.TotalExecutions,
			"success_rate":       calculateSuccessRate(metrics),
			"average_exec_time":  metrics.AverageExecutionTime.String(),
		},
		"container_pool": gin.H{
			"total_containers": poolStats["total_containers"],
			"max_size":        poolStats["max_size"],
			"utilization":     calculatePoolUtilization(poolStats),
		},
	}

	// 检查服务健康状态
	if metrics.ActiveExecutions > int64(h.executor.config.MaxConcurrentExecutions*0.9) {
		health["status"] = "warning"
		health["warning"] = "接近最大并发执行数限制"
	}

	c.JSON(http.StatusOK, health)
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return "req-" + strconv.FormatInt(time.Now().UnixNano(), 36)
}

// calculateSuccessRate 计算成功率
func calculateSuccessRate(metrics *ExecutorMetrics) float64 {
	if metrics.TotalExecutions == 0 {
		return 0.0
	}
	return float64(metrics.SuccessfulExecutions) / float64(metrics.TotalExecutions) * 100
}

// calculatePoolUtilization 计算容器池利用率
func calculatePoolUtilization(stats map[string]interface{}) float64 {
	totalContainers, ok1 := stats["total_containers"].(int)
	maxSize, ok2 := stats["max_size"].(int)
	
	if !ok1 || !ok2 || maxSize == 0 {
		return 0.0
	}
	
	return float64(totalContainers) / float64(maxSize) * 100
}

// FunctionExecutionResult 函数执行结果（用于异步查询）
type FunctionExecutionResult struct {
	RequestID string           `json:"request_id"`
	Status    string           `json:"status"` // pending, running, completed, failed
	Response  *FunctionResponse `json:"response,omitempty"`
	Error     string           `json:"error,omitempty"`
	CreatedAt time.Time        `json:"created_at"`
	UpdatedAt time.Time        `json:"updated_at"`
}

// ExecutionSummary 执行摘要
type ExecutionSummary struct {
	TotalRequests      int64         `json:"total_requests"`
	SuccessfulRequests int64         `json:"successful_requests"`
	FailedRequests     int64         `json:"failed_requests"`
	AverageLatency     time.Duration `json:"average_latency"`
	P95Latency         time.Duration `json:"p95_latency"`
	P99Latency         time.Duration `json:"p99_latency"`
	ThroughputPerSecond float64      `json:"throughput_per_second"`
	ErrorRate          float64       `json:"error_rate"`
	LastUpdated        time.Time     `json:"last_updated"`
}

// RuntimeStats 运行时统计
type RuntimeStats struct {
	Runtime           string        `json:"runtime"`
	TotalExecutions   int64         `json:"total_executions"`
	AverageLatency    time.Duration `json:"average_latency"`
	ContainerCount    int           `json:"container_count"`
	MemoryUsage       int64         `json:"memory_usage"`
	CPUUsage          float64       `json:"cpu_usage"`
}
