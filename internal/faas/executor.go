package faas

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// FunctionExecutor FaaS函数执行器
type FunctionExecutor struct {
	containerManager container.ContainerManager
	containerPool    *ContainerPool
	logger           logger.Logger
	config           *ExecutorConfig
	metrics          *ExecutorMetrics
	mutex            sync.RWMutex
}

// ExecutorConfig 执行器配置
type ExecutorConfig struct {
	MaxConcurrentExecutions int           `yaml:"max_concurrent_executions"` // 最大并发执行数
	DefaultTimeout          time.Duration `yaml:"default_timeout"`           // 默认超时时间
	MaxExecutionTime        time.Duration `yaml:"max_execution_time"`        // 最大执行时间
	ContainerPoolSize       int           `yaml:"container_pool_size"`       // 容器池大小
	PrewarmContainers       int           `yaml:"prewarm_containers"`        // 预热容器数量
	CleanupInterval         time.Duration `yaml:"cleanup_interval"`          // 清理间隔
	ResourceLimits          *ResourceLimits `yaml:"resource_limits"`         // 资源限制
}

// ResourceLimits 资源限制配置
type ResourceLimits struct {
	CPULimit    string `yaml:"cpu_limit"`    // CPU限制，如 "0.5"
	MemoryLimit string `yaml:"memory_limit"` // 内存限制，如 "512m"
	DiskLimit   string `yaml:"disk_limit"`   // 磁盘限制，如 "1g"
}

// ExecutorMetrics 执行器指标
type ExecutorMetrics struct {
	TotalExecutions     int64         `json:"total_executions"`
	SuccessfulExecutions int64        `json:"successful_executions"`
	FailedExecutions    int64         `json:"failed_executions"`
	AverageExecutionTime time.Duration `json:"average_execution_time"`
	ActiveExecutions    int64         `json:"active_executions"`
	ContainerPoolUsage  int           `json:"container_pool_usage"`
	mutex               sync.RWMutex
}

// FunctionRequest 函数执行请求
type FunctionRequest struct {
	FunctionID   string                 `json:"function_id" binding:"required"`
	FunctionName string                 `json:"function_name" binding:"required"`
	Runtime      string                 `json:"runtime" binding:"required"` // nodejs, python, go
	Code         string                 `json:"code" binding:"required"`
	Handler      string                 `json:"handler"`                    // 入口函数
	Environment  map[string]string      `json:"environment"`                // 环境变量
	Parameters   map[string]interface{} `json:"parameters"`                 // 函数参数
	Timeout      time.Duration          `json:"timeout"`                    // 执行超时
	MemoryLimit  string                 `json:"memory_limit"`               // 内存限制
	RequestID    string                 `json:"request_id"`                 // 请求ID
}

// FunctionResponse 函数执行响应
type FunctionResponse struct {
	RequestID     string        `json:"request_id"`
	FunctionID    string        `json:"function_id"`
	Status        string        `json:"status"`        // success, error, timeout
	Result        interface{}   `json:"result"`        // 执行结果
	Error         string        `json:"error"`         // 错误信息
	ExecutionTime time.Duration `json:"execution_time"` // 执行时间
	MemoryUsage   int64         `json:"memory_usage"`   // 内存使用量
	Logs          []string      `json:"logs"`           // 执行日志
	ContainerID   string        `json:"container_id"`   // 容器ID
	StartTime     time.Time     `json:"start_time"`     // 开始时间
	EndTime       time.Time     `json:"end_time"`       // 结束时间
}

// ExecutionContext 执行上下文
type ExecutionContext struct {
	Request     *FunctionRequest
	Container   *container.Container
	StartTime   time.Time
	Timeout     time.Duration
	CancelFunc  context.CancelFunc
	Logs        []string
	mutex       sync.Mutex
}

// NewFunctionExecutor 创建函数执行器
func NewFunctionExecutor(containerManager container.ContainerManager, config *ExecutorConfig, logger logger.Logger) *FunctionExecutor {
	if config == nil {
		config = getDefaultExecutorConfig()
	}

	executor := &FunctionExecutor{
		containerManager: containerManager,
		logger:           logger,
		config:           config,
		metrics: &ExecutorMetrics{
			TotalExecutions:      0,
			SuccessfulExecutions: 0,
			FailedExecutions:     0,
			AverageExecutionTime: 0,
			ActiveExecutions:     0,
			ContainerPoolUsage:   0,
		},
	}

	// 初始化容器池
	executor.containerPool = NewContainerPool(containerManager, config.ContainerPoolSize, logger)

	// 启动预热容器
	go executor.prewarmContainers()

	// 启动清理任务
	go executor.startCleanupTask()

	return executor
}

// getDefaultExecutorConfig 获取默认配置
func getDefaultExecutorConfig() *ExecutorConfig {
	return &ExecutorConfig{
		MaxConcurrentExecutions: 100,
		DefaultTimeout:          30 * time.Second,
		MaxExecutionTime:        5 * time.Minute,
		ContainerPoolSize:       10,
		PrewarmContainers:       3,
		CleanupInterval:         5 * time.Minute,
		ResourceLimits: &ResourceLimits{
			CPULimit:    "0.5",
			MemoryLimit: "512m",
			DiskLimit:   "1g",
		},
	}
}

// ExecuteFunction 执行函数
func (fe *FunctionExecutor) ExecuteFunction(ctx context.Context, request *FunctionRequest) (*FunctionResponse, error) {
	startTime := time.Now()
	
	// 验证请求
	if err := fe.validateRequest(request); err != nil {
		return nil, fmt.Errorf("请求验证失败: %w", err)
	}

	// 检查并发限制
	if !fe.checkConcurrencyLimit() {
		return nil, fmt.Errorf("超过最大并发执行数限制: %d", fe.config.MaxConcurrentExecutions)
	}

	// 增加活跃执行计数
	fe.incrementActiveExecutions()
	defer fe.decrementActiveExecutions()

	// 设置超时
	timeout := request.Timeout
	if timeout == 0 {
		timeout = fe.config.DefaultTimeout
	}
	if timeout > fe.config.MaxExecutionTime {
		timeout = fe.config.MaxExecutionTime
	}

	execCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 创建执行上下文
	executionContext := &ExecutionContext{
		Request:    request,
		StartTime:  startTime,
		Timeout:    timeout,
		CancelFunc: cancel,
		Logs:       make([]string, 0),
	}

	// 执行函数
	response, err := fe.doExecuteFunction(execCtx, executionContext)
	
	// 更新指标
	fe.updateMetrics(response, err, time.Since(startTime))

	if err != nil {
		fe.logger.Error("函数执行失败", "function_id", request.FunctionID, "error", err)
		return nil, err
	}

	fe.logger.Info("函数执行成功", "function_id", request.FunctionID, 
		"execution_time", response.ExecutionTime, "status", response.Status)

	return response, nil
}

// validateRequest 验证请求
func (fe *FunctionExecutor) validateRequest(request *FunctionRequest) error {
	if request.FunctionID == "" {
		return fmt.Errorf("函数ID不能为空")
	}
	if request.FunctionName == "" {
		return fmt.Errorf("函数名称不能为空")
	}
	if request.Runtime == "" {
		return fmt.Errorf("运行时不能为空")
	}
	if request.Code == "" {
		return fmt.Errorf("函数代码不能为空")
	}
	
	// 验证运行时支持
	supportedRuntimes := []string{"nodejs", "python", "go", "java"}
	runtimeSupported := false
	for _, runtime := range supportedRuntimes {
		if request.Runtime == runtime {
			runtimeSupported = true
			break
		}
	}
	if !runtimeSupported {
		return fmt.Errorf("不支持的运行时: %s", request.Runtime)
	}

	return nil
}

// checkConcurrencyLimit 检查并发限制
func (fe *FunctionExecutor) checkConcurrencyLimit() bool {
	fe.metrics.mutex.RLock()
	defer fe.metrics.mutex.RUnlock()
	return fe.metrics.ActiveExecutions < int64(fe.config.MaxConcurrentExecutions)
}

// incrementActiveExecutions 增加活跃执行计数
func (fe *FunctionExecutor) incrementActiveExecutions() {
	fe.metrics.mutex.Lock()
	defer fe.metrics.mutex.Unlock()
	fe.metrics.ActiveExecutions++
}

// decrementActiveExecutions 减少活跃执行计数
func (fe *FunctionExecutor) decrementActiveExecutions() {
	fe.metrics.mutex.Lock()
	defer fe.metrics.mutex.Unlock()
	fe.metrics.ActiveExecutions--
}

// updateMetrics 更新指标
func (fe *FunctionExecutor) updateMetrics(response *FunctionResponse, err error, duration time.Duration) {
	fe.metrics.mutex.Lock()
	defer fe.metrics.mutex.Unlock()
	
	fe.metrics.TotalExecutions++
	
	if err != nil || (response != nil && response.Status == "error") {
		fe.metrics.FailedExecutions++
	} else {
		fe.metrics.SuccessfulExecutions++
	}
	
	// 更新平均执行时间
	if fe.metrics.TotalExecutions > 0 {
		totalTime := fe.metrics.AverageExecutionTime * time.Duration(fe.metrics.TotalExecutions-1)
		fe.metrics.AverageExecutionTime = (totalTime + duration) / time.Duration(fe.metrics.TotalExecutions)
	}
}

// doExecuteFunction 执行函数的核心逻辑
func (fe *FunctionExecutor) doExecuteFunction(ctx context.Context, execCtx *ExecutionContext) (*FunctionResponse, error) {
	// 获取或创建容器
	containerInfo, err := fe.getOrCreateContainer(ctx, execCtx.Request)
	if err != nil {
		return nil, fmt.Errorf("获取容器失败: %w", err)
	}

	execCtx.Container = containerInfo
	defer fe.cleanupContainer(containerInfo)

	// 准备函数代码和参数
	if err := fe.prepareFunction(ctx, execCtx); err != nil {
		return nil, fmt.Errorf("准备函数失败: %w", err)
	}

	// 执行函数
	result, err := fe.runFunction(ctx, execCtx)
	if err != nil {
		return &FunctionResponse{
			RequestID:     execCtx.Request.RequestID,
			FunctionID:    execCtx.Request.FunctionID,
			Status:        "error",
			Error:         err.Error(),
			ExecutionTime: time.Since(execCtx.StartTime),
			ContainerID:   containerInfo.ID,
			StartTime:     execCtx.StartTime,
			EndTime:       time.Now(),
			Logs:          execCtx.Logs,
		}, nil
	}

	// 构建响应
	response := &FunctionResponse{
		RequestID:     execCtx.Request.RequestID,
		FunctionID:    execCtx.Request.FunctionID,
		Status:        "success",
		Result:        result,
		ExecutionTime: time.Since(execCtx.StartTime),
		ContainerID:   containerInfo.ID,
		StartTime:     execCtx.StartTime,
		EndTime:       time.Now(),
		Logs:          execCtx.Logs,
	}

	// 获取内存使用情况
	if stats, err := fe.containerManager.GetContainerStats(ctx, containerInfo.ID); err == nil {
		response.MemoryUsage = stats.MemoryUsage
	}

	return response, nil
}

// getOrCreateContainer 获取或创建容器
func (fe *FunctionExecutor) getOrCreateContainer(ctx context.Context, request *FunctionRequest) (*container.Container, error) {
	// 尝试从容器池获取
	if poolContainer := fe.containerPool.GetContainer(request.Runtime); poolContainer != nil {
		fe.logger.Debug("从容器池获取容器", "container_id", poolContainer.ID, "runtime", request.Runtime)
		return poolContainer, nil
	}

	// 创建新容器
	containerConfig := fe.buildContainerConfig(request)
	containerInfo, err := fe.containerManager.CreateContainer(ctx, containerConfig)
	if err != nil {
		return nil, fmt.Errorf("创建容器失败: %w", err)
	}

	// 启动容器
	if err := fe.containerManager.StartContainer(ctx, containerInfo.ID); err != nil {
		fe.containerManager.RemoveContainer(ctx, containerInfo.ID, true)
		return nil, fmt.Errorf("启动容器失败: %w", err)
	}

	fe.logger.Info("创建新容器", "container_id", containerInfo.ID, "runtime", request.Runtime)
	return containerInfo, nil
}

// buildContainerConfig 构建容器配置
func (fe *FunctionExecutor) buildContainerConfig(request *FunctionRequest) *container.ContainerConfig {
	// 根据运行时选择镜像
	image := fe.getRuntimeImage(request.Runtime)

	// 构建环境变量
	env := make([]string, 0)
	for k, v := range request.Environment {
		env = append(env, fmt.Sprintf("%s=%s", k, v))
	}

	// 添加函数相关环境变量
	env = append(env, fmt.Sprintf("FUNCTION_ID=%s", request.FunctionID))
	env = append(env, fmt.Sprintf("FUNCTION_NAME=%s", request.FunctionName))
	env = append(env, fmt.Sprintf("HANDLER=%s", request.Handler))

	// 内存限制
	memoryLimit := request.MemoryLimit
	if memoryLimit == "" {
		memoryLimit = fe.config.ResourceLimits.MemoryLimit
	}

	return &container.ContainerConfig{
		Image:      image,
		Name:       fmt.Sprintf("faas-%s-%d", request.FunctionID, time.Now().Unix()),
		Command:    []string{},
		Env:        env,
		WorkingDir: "/app",
		Resources: &container.ResourceConfig{
			CPULimit:    fe.config.ResourceLimits.CPULimit,
			MemoryLimit: memoryLimit,
			DiskLimit:   fe.config.ResourceLimits.DiskLimit,
		},
		AutoRemove: false, // 手动管理容器生命周期
		Labels: map[string]string{
			"paas.function.id":   request.FunctionID,
			"paas.function.name": request.FunctionName,
			"paas.runtime":       request.Runtime,
			"paas.type":          "faas",
		},
	}
}

// getRuntimeImage 获取运行时镜像
func (fe *FunctionExecutor) getRuntimeImage(runtime string) string {
	images := map[string]string{
		"nodejs": "node:18-alpine",
		"python": "python:3.9-alpine",
		"go":     "golang:1.21-alpine",
		"java":   "openjdk:11-jre-slim",
	}

	if image, exists := images[runtime]; exists {
		return image
	}

	return "alpine:latest" // 默认镜像
}

// prepareFunction 准备函数代码和参数
func (fe *FunctionExecutor) prepareFunction(ctx context.Context, execCtx *ExecutionContext) error {
	request := execCtx.Request

	// 将函数代码写入容器
	codeFile := "/tmp/function." + fe.getFileExtension(request.Runtime)
	writeCodeCmd := []string{"sh", "-c", fmt.Sprintf("echo '%s' > %s", request.Code, codeFile)}

	if _, err := fe.containerManager.ExecCommand(ctx, execCtx.Container.ID, writeCodeCmd); err != nil {
		return fmt.Errorf("写入函数代码失败: %w", err)
	}

	// 将参数写入容器
	if len(request.Parameters) > 0 {
		paramsJSON, _ := json.Marshal(request.Parameters)
		writeParamsCmd := []string{"sh", "-c", fmt.Sprintf("echo '%s' > /tmp/params.json", string(paramsJSON))}

		if _, err := fe.containerManager.ExecCommand(ctx, execCtx.Container.ID, writeParamsCmd); err != nil {
			return fmt.Errorf("写入函数参数失败: %w", err)
		}
	}

	execCtx.addLog("函数代码和参数准备完成")
	return nil
}

// getFileExtension 获取文件扩展名
func (fe *FunctionExecutor) getFileExtension(runtime string) string {
	extensions := map[string]string{
		"nodejs": "js",
		"python": "py",
		"go":     "go",
		"java":   "java",
	}

	if ext, exists := extensions[runtime]; exists {
		return ext
	}

	return "txt"
}

// GetMetrics 获取执行器指标
func (fe *FunctionExecutor) GetMetrics() *ExecutorMetrics {
	fe.metrics.mutex.RLock()
	defer fe.metrics.mutex.RUnlock()

	// 返回副本
	return &ExecutorMetrics{
		TotalExecutions:      fe.metrics.TotalExecutions,
		SuccessfulExecutions: fe.metrics.SuccessfulExecutions,
		FailedExecutions:     fe.metrics.FailedExecutions,
		AverageExecutionTime: fe.metrics.AverageExecutionTime,
		ActiveExecutions:     fe.metrics.ActiveExecutions,
		ContainerPoolUsage:   fe.metrics.ContainerPoolUsage,
	}
}

// addLog 添加日志到执行上下文
func (ec *ExecutionContext) addLog(message string) {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()
	ec.Logs = append(ec.Logs, fmt.Sprintf("[%s] %s", time.Now().Format("15:04:05"), message))
}
