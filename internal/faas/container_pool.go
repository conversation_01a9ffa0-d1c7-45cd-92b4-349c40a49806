package faas

import (
	"context"
	"sync"
	"time"

	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// ContainerPool 容器池管理器
type ContainerPool struct {
	containers       map[string][]*PooledContainer // 按运行时分组的容器
	maxSize          int                           // 最大容器数
	containerManager container.ContainerManager
	logger           logger.Logger
	mutex            sync.RWMutex
	cleanupInterval  time.Duration
	maxIdleTime      time.Duration
}

// PooledContainer 池化容器
type PooledContainer struct {
	Container    *container.Container
	Runtime      string
	CreatedAt    time.Time
	LastUsedAt   time.Time
	UsageCount   int
	IsAvailable  bool
	mutex        sync.Mutex
}

// NewContainerPool 创建容器池
func NewContainerPool(containerManager container.ContainerManager, maxSize int, logger logger.Logger) *ContainerPool {
	pool := &ContainerPool{
		containers:       make(map[string][]*PooledContainer),
		maxSize:          maxSize,
		containerManager: containerManager,
		logger:           logger,
		cleanupInterval:  5 * time.Minute,
		maxIdleTime:      10 * time.Minute,
	}

	// 启动清理任务
	go pool.startCleanupTask()

	return pool
}

// GetContainer 从池中获取容器
func (cp *ContainerPool) GetContainer(runtime string) *container.Container {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	containers, exists := cp.containers[runtime]
	if !exists || len(containers) == 0 {
		return nil
	}

	// 查找可用容器
	for i, pooledContainer := range containers {
		pooledContainer.mutex.Lock()
		if pooledContainer.IsAvailable {
			pooledContainer.IsAvailable = false
			pooledContainer.LastUsedAt = time.Now()
			pooledContainer.UsageCount++
			pooledContainer.mutex.Unlock()

			cp.logger.Debug("从容器池获取容器", "container_id", pooledContainer.Container.ID, 
				"runtime", runtime, "usage_count", pooledContainer.UsageCount)

			// 从池中移除
			cp.containers[runtime] = append(containers[:i], containers[i+1:]...)
			return pooledContainer.Container
		}
		pooledContainer.mutex.Unlock()
	}

	return nil
}

// ReturnContainer 将容器返回到池中
func (cp *ContainerPool) ReturnContainer(cont *container.Container, runtime string) {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	// 检查池是否已满
	totalContainers := cp.getTotalContainerCount()
	if totalContainers >= cp.maxSize {
		// 池已满，直接销毁容器
		go cp.destroyContainer(cont)
		return
	}

	// 创建池化容器
	pooledContainer := &PooledContainer{
		Container:   cont,
		Runtime:     runtime,
		CreatedAt:   time.Now(),
		LastUsedAt:  time.Now(),
		UsageCount:  0,
		IsAvailable: true,
	}

	// 添加到池中
	if _, exists := cp.containers[runtime]; !exists {
		cp.containers[runtime] = make([]*PooledContainer, 0)
	}
	cp.containers[runtime] = append(cp.containers[runtime], pooledContainer)

	cp.logger.Debug("容器返回到池中", "container_id", cont.ID, "runtime", runtime)
}

// PrewarmContainers 预热容器
func (cp *ContainerPool) PrewarmContainers(runtime string, count int) error {
	cp.logger.Info("开始预热容器", "runtime", runtime, "count", count)

	for i := 0; i < count; i++ {
		// 创建容器配置
		config := &container.ContainerConfig{
			Image:      cp.getRuntimeImage(runtime),
			Name:       "",
			Command:    []string{"sleep", "3600"}, // 保持容器运行
			Env:        []string{},
			WorkingDir: "/app",
			Resources: &container.ResourceConfig{
				CPULimit:    "0.1",
				MemoryLimit: "128m",
			},
			AutoRemove: false,
			Labels: map[string]string{
				"paas.type":    "faas-pool",
				"paas.runtime": runtime,
			},
		}

		// 创建容器
		ctx := context.Background()
		cont, err := cp.containerManager.CreateContainer(ctx, config)
		if err != nil {
			cp.logger.Error("预热容器创建失败", "runtime", runtime, "error", err)
			continue
		}

		// 启动容器
		if err := cp.containerManager.StartContainer(ctx, cont.ID); err != nil {
			cp.logger.Error("预热容器启动失败", "container_id", cont.ID, "error", err)
			cp.containerManager.RemoveContainer(ctx, cont.ID, true)
			continue
		}

		// 添加到池中
		cp.ReturnContainer(cont, runtime)
	}

	cp.logger.Info("容器预热完成", "runtime", runtime)
	return nil
}

// startCleanupTask 启动清理任务
func (cp *ContainerPool) startCleanupTask() {
	ticker := time.NewTicker(cp.cleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		cp.cleanupIdleContainers()
	}
}

// cleanupIdleContainers 清理空闲容器
func (cp *ContainerPool) cleanupIdleContainers() {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	now := time.Now()
	totalCleaned := 0

	for runtime, containers := range cp.containers {
		remainingContainers := make([]*PooledContainer, 0)

		for _, pooledContainer := range containers {
			pooledContainer.mutex.Lock()
			
			// 检查是否空闲时间过长
			if pooledContainer.IsAvailable && now.Sub(pooledContainer.LastUsedAt) > cp.maxIdleTime {
				// 销毁空闲容器
				go cp.destroyContainer(pooledContainer.Container)
				totalCleaned++
				cp.logger.Debug("清理空闲容器", "container_id", pooledContainer.Container.ID, 
					"runtime", runtime, "idle_time", now.Sub(pooledContainer.LastUsedAt))
			} else {
				remainingContainers = append(remainingContainers, pooledContainer)
			}
			
			pooledContainer.mutex.Unlock()
		}

		cp.containers[runtime] = remainingContainers
	}

	if totalCleaned > 0 {
		cp.logger.Info("清理空闲容器完成", "cleaned_count", totalCleaned)
	}
}

// destroyContainer 销毁容器
func (cp *ContainerPool) destroyContainer(cont *container.Container) {
	ctx := context.Background()
	
	// 停止容器
	if err := cp.containerManager.StopContainer(ctx, cont.ID, 10*time.Second); err != nil {
		cp.logger.Warn("停止容器失败", "container_id", cont.ID, "error", err)
	}

	// 删除容器
	if err := cp.containerManager.RemoveContainer(ctx, cont.ID, true); err != nil {
		cp.logger.Warn("删除容器失败", "container_id", cont.ID, "error", err)
	} else {
		cp.logger.Debug("容器销毁成功", "container_id", cont.ID)
	}
}

// getTotalContainerCount 获取总容器数
func (cp *ContainerPool) getTotalContainerCount() int {
	total := 0
	for _, containers := range cp.containers {
		total += len(containers)
	}
	return total
}

// getRuntimeImage 获取运行时镜像
func (cp *ContainerPool) getRuntimeImage(runtime string) string {
	images := map[string]string{
		"nodejs": "node:18-alpine",
		"python": "python:3.9-alpine",
		"go":     "golang:1.21-alpine",
		"java":   "openjdk:11-jre-slim",
	}
	
	if image, exists := images[runtime]; exists {
		return image
	}
	
	return "alpine:latest"
}

// GetPoolStats 获取容器池统计信息
func (cp *ContainerPool) GetPoolStats() map[string]interface{} {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["max_size"] = cp.maxSize
	stats["total_containers"] = cp.getTotalContainerCount()

	runtimeStats := make(map[string]int)
	for runtime, containers := range cp.containers {
		runtimeStats[runtime] = len(containers)
	}
	stats["runtime_distribution"] = runtimeStats

	return stats
}

// Shutdown 关闭容器池
func (cp *ContainerPool) Shutdown() {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	cp.logger.Info("关闭容器池")

	// 销毁所有容器
	for runtime, containers := range cp.containers {
		for _, pooledContainer := range containers {
			go cp.destroyContainer(pooledContainer.Container)
		}
		cp.logger.Info("销毁运行时容器", "runtime", runtime, "count", len(containers))
	}

	// 清空容器池
	cp.containers = make(map[string][]*PooledContainer)
}
