package faas

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// TestNewContainerPool 测试创建容器池
func TestNewContainerPool(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	maxSize := 10

	pool := NewContainerPool(mockContainerManager, maxSize, mockLogger)

	assert.NotNil(t, pool)
	assert.Equal(t, maxSize, pool.maxSize)
	assert.Equal(t, mockContainerManager, pool.containerManager)
	assert.NotNil(t, pool.containers)
	assert.Equal(t, 5*time.Minute, pool.cleanupInterval)
	assert.Equal(t, 10*time.Minute, pool.maxIdleTime)
}

// TestGetContainer 测试从池中获取容器
func TestGetContainer(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	pool := NewContainerPool(mockContainerManager, 10, mockLogger)

	runtime := "nodejs"

	// 池为空时应该返回nil
	container := pool.GetContainer(runtime)
	assert.Nil(t, container)

	// 添加容器到池中
	testContainer := &container.Container{
		ID:     "test-container-1",
		Name:   "test-container",
		Image:  "node:18-alpine",
		Status: "running",
	}

	pooledContainer := &PooledContainer{
		Container:   testContainer,
		Runtime:     runtime,
		CreatedAt:   time.Now(),
		LastUsedAt:  time.Now(),
		UsageCount:  0,
		IsAvailable: true,
	}

	pool.containers[runtime] = []*PooledContainer{pooledContainer}

	// 现在应该能获取到容器
	retrievedContainer := pool.GetContainer(runtime)
	assert.NotNil(t, retrievedContainer)
	assert.Equal(t, testContainer.ID, retrievedContainer.ID)

	// 容器应该从池中移除
	assert.Len(t, pool.containers[runtime], 0)

	// 再次获取应该返回nil
	container = pool.GetContainer(runtime)
	assert.Nil(t, container)
}

// TestReturnContainer 测试将容器返回到池中
func TestReturnContainer(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	pool := NewContainerPool(mockContainerManager, 10, mockLogger)

	runtime := "nodejs"
	testContainer := &container.Container{
		ID:     "test-container-1",
		Name:   "test-container",
		Image:  "node:18-alpine",
		Status: "running",
	}

	// 返回容器到池中
	pool.ReturnContainer(testContainer, runtime)

	// 检查容器是否在池中
	assert.Len(t, pool.containers[runtime], 1)
	assert.Equal(t, testContainer.ID, pool.containers[runtime][0].Container.ID)
	assert.True(t, pool.containers[runtime][0].IsAvailable)
}

// TestReturnContainerWhenPoolFull 测试池满时返回容器
func TestReturnContainerWhenPoolFull(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	maxSize := 2
	pool := NewContainerPool(mockContainerManager, maxSize, mockLogger)

	runtime := "nodejs"

	// 填满容器池
	for i := 0; i < maxSize; i++ {
		testContainer := &container.Container{
			ID:     fmt.Sprintf("test-container-%d", i),
			Name:   fmt.Sprintf("test-container-%d", i),
			Image:  "node:18-alpine",
			Status: "running",
		}
		pool.ReturnContainer(testContainer, runtime)
	}

	assert.Equal(t, maxSize, pool.getTotalContainerCount())

	// 设置mock期望：当池满时，应该销毁容器
	extraContainer := &container.Container{
		ID:     "extra-container",
		Name:   "extra-container",
		Image:  "node:18-alpine",
		Status: "running",
	}

	mockContainerManager.On("StopContainer", mock.Anything, "extra-container", 10*time.Second).Return(nil)
	mockContainerManager.On("RemoveContainer", mock.Anything, "extra-container", true).Return(nil)

	// 尝试返回额外的容器
	pool.ReturnContainer(extraContainer, runtime)

	// 池大小应该保持不变
	assert.Equal(t, maxSize, pool.getTotalContainerCount())

	// 等待一段时间让goroutine执行
	time.Sleep(100 * time.Millisecond)

	// 验证mock调用
	mockContainerManager.AssertExpectations(t)
}

// TestPrewarmContainers 测试预热容器
func TestPrewarmContainers(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	pool := NewContainerPool(mockContainerManager, 10, mockLogger)

	runtime := "nodejs"
	count := 3

	// 设置mock期望
	for i := 0; i < count; i++ {
		containerID := fmt.Sprintf("prewarm-container-%d", i)
		testContainer := &container.Container{
			ID:     containerID,
			Name:   "",
			Image:  "node:18-alpine",
			Status: "running",
		}

		mockContainerManager.On("CreateContainer", mock.Anything, mock.AnythingOfType("*container.ContainerConfig")).Return(testContainer, nil).Once()
		mockContainerManager.On("StartContainer", mock.Anything, containerID).Return(nil).Once()
	}

	// 预热容器
	err := pool.PrewarmContainers(runtime, count)
	assert.NoError(t, err)

	// 检查容器是否在池中
	assert.Len(t, pool.containers[runtime], count)

	// 验证mock调用
	mockContainerManager.AssertExpectations(t)
}

// TestGetRuntimeImage 测试获取运行时镜像
func TestGetRuntimeImage(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	pool := NewContainerPool(mockContainerManager, 10, mockLogger)

	tests := []struct {
		runtime string
		want    string
	}{
		{"nodejs", "node:18-alpine"},
		{"python", "python:3.9-alpine"},
		{"go", "golang:1.21-alpine"},
		{"java", "openjdk:11-jre-slim"},
		{"unknown", "alpine:latest"},
	}

	for _, tt := range tests {
		t.Run(tt.runtime, func(t *testing.T) {
			got := pool.getRuntimeImage(tt.runtime)
			assert.Equal(t, tt.want, got)
		})
	}
}

// TestGetPoolStats 测试获取容器池统计
func TestGetPoolStats(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	maxSize := 10
	pool := NewContainerPool(mockContainerManager, maxSize, mockLogger)

	// 添加一些容器
	runtimes := []string{"nodejs", "python", "go"}
	for i, runtime := range runtimes {
		for j := 0; j < i+1; j++ {
			testContainer := &container.Container{
				ID:     fmt.Sprintf("%s-container-%d", runtime, j),
				Name:   fmt.Sprintf("%s-container-%d", runtime, j),
				Image:  pool.getRuntimeImage(runtime),
				Status: "running",
			}
			pool.ReturnContainer(testContainer, runtime)
		}
	}

	stats := pool.GetPoolStats()

	assert.Equal(t, maxSize, stats["max_size"])
	assert.Equal(t, 6, stats["total_containers"]) // 1+2+3 = 6

	runtimeStats, ok := stats["runtime_distribution"].(map[string]int)
	assert.True(t, ok)
	assert.Equal(t, 1, runtimeStats["nodejs"])
	assert.Equal(t, 2, runtimeStats["python"])
	assert.Equal(t, 3, runtimeStats["go"])
}

// TestCleanupIdleContainers 测试清理空闲容器
func TestCleanupIdleContainers(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	pool := NewContainerPool(mockContainerManager, 10, mockLogger)
	
	// 设置较短的空闲时间用于测试
	pool.maxIdleTime = 100 * time.Millisecond

	runtime := "nodejs"
	
	// 添加一个容器
	testContainer := &container.Container{
		ID:     "idle-container",
		Name:   "idle-container",
		Image:  "node:18-alpine",
		Status: "running",
	}

	pooledContainer := &PooledContainer{
		Container:   testContainer,
		Runtime:     runtime,
		CreatedAt:   time.Now(),
		LastUsedAt:  time.Now().Add(-200 * time.Millisecond), // 已经空闲200ms
		UsageCount:  0,
		IsAvailable: true,
	}

	pool.containers[runtime] = []*PooledContainer{pooledContainer}

	// 设置mock期望
	mockContainerManager.On("StopContainer", mock.Anything, "idle-container", 10*time.Second).Return(nil)
	mockContainerManager.On("RemoveContainer", mock.Anything, "idle-container", true).Return(nil)

	// 执行清理
	pool.cleanupIdleContainers()

	// 容器应该被清理
	assert.Len(t, pool.containers[runtime], 0)

	// 等待一段时间让goroutine执行
	time.Sleep(100 * time.Millisecond)

	// 验证mock调用
	mockContainerManager.AssertExpectations(t)
}

// TestShutdown 测试关闭容器池
func TestShutdown(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	pool := NewContainerPool(mockContainerManager, 10, mockLogger)

	// 添加一些容器
	runtimes := []string{"nodejs", "python"}
	containerIDs := []string{}
	
	for _, runtime := range runtimes {
		containerID := fmt.Sprintf("%s-container", runtime)
		containerIDs = append(containerIDs, containerID)
		
		testContainer := &container.Container{
			ID:     containerID,
			Name:   containerID,
			Image:  pool.getRuntimeImage(runtime),
			Status: "running",
		}
		pool.ReturnContainer(testContainer, runtime)

		// 设置mock期望
		mockContainerManager.On("StopContainer", mock.Anything, containerID, 10*time.Second).Return(nil)
		mockContainerManager.On("RemoveContainer", mock.Anything, containerID, true).Return(nil)
	}

	// 关闭容器池
	pool.Shutdown()

	// 容器池应该被清空
	assert.Len(t, pool.containers, 0)

	// 等待一段时间让goroutine执行
	time.Sleep(100 * time.Millisecond)

	// 验证mock调用
	mockContainerManager.AssertExpectations(t)
}

// TestPooledContainerConcurrency 测试池化容器的并发安全性
func TestPooledContainerConcurrency(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLogger := logger.NewConsoleLogger("test")
	pool := NewContainerPool(mockContainerManager, 10, mockLogger)

	runtime := "nodejs"
	testContainer := &container.Container{
		ID:     "concurrent-test-container",
		Name:   "concurrent-test-container",
		Image:  "node:18-alpine",
		Status: "running",
	}

	// 并发返回和获取容器
	done := make(chan bool, 2)

	go func() {
		for i := 0; i < 100; i++ {
			pool.ReturnContainer(testContainer, runtime)
			time.Sleep(1 * time.Millisecond)
		}
		done <- true
	}()

	go func() {
		for i := 0; i < 100; i++ {
			pool.GetContainer(runtime)
			time.Sleep(1 * time.Millisecond)
		}
		done <- true
	}()

	// 等待两个goroutine完成
	<-done
	<-done

	// 测试应该没有panic或死锁
	assert.True(t, true)
}
