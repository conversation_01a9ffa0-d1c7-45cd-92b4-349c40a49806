package monitoring

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"paas-platform/pkg/logger"
)

// APMCollector 应用性能监控收集器
type APMCollector struct {
	logger logger.Logger
	mutex  sync.RWMutex

	// Prometheus 指标
	httpRequestsTotal    *prometheus.CounterVec
	httpRequestDuration  *prometheus.HistogramVec
	httpRequestSize      *prometheus.HistogramVec
	httpResponseSize     *prometheus.HistogramVec
	activeConnections    prometheus.Gauge
	systemMemoryUsage    prometheus.Gauge
	systemCPUUsage       prometheus.Gauge
	goroutineCount       prometheus.Gauge
	gcDuration           prometheus.Gauge
	
	// 业务指标
	loginAttempts        *prometheus.CounterVec
	loginSuccess         *prometheus.CounterVec
	activeSessions       prometheus.Gauge
	apiErrors            *prometheus.CounterVec
	databaseConnections  prometheus.Gauge
	cacheHitRate         *prometheus.GaugeVec
	
	// 自定义指标存储
	customMetrics map[string]prometheus.Collector
}

// NewAPMCollector 创建新的 APM 收集器
func NewAPMCollector(serviceName string, logger logger.Logger) *APMCollector {
	apm := &APMCollector{
		logger:        logger,
		customMetrics: make(map[string]prometheus.Collector),
	}

	// 初始化 HTTP 相关指标
	apm.httpRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "http_requests_total",
			Help:      "HTTP 请求总数",
		},
		[]string{"method", "endpoint", "status_code"},
	)

	apm.httpRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "http_request_duration_seconds",
			Help:      "HTTP 请求持续时间（秒）",
			Buckets:   prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	apm.httpRequestSize = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "http_request_size_bytes",
			Help:      "HTTP 请求大小（字节）",
			Buckets:   prometheus.ExponentialBuckets(100, 10, 8),
		},
		[]string{"method", "endpoint"},
	)

	apm.httpResponseSize = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "http_response_size_bytes",
			Help:      "HTTP 响应大小（字节）",
			Buckets:   prometheus.ExponentialBuckets(100, 10, 8),
		},
		[]string{"method", "endpoint"},
	)

	// 初始化系统指标
	apm.activeConnections = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "active_connections",
			Help:      "当前活跃连接数",
		},
	)

	apm.systemMemoryUsage = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "system_memory_usage_bytes",
			Help:      "系统内存使用量（字节）",
		},
	)

	apm.systemCPUUsage = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "system_cpu_usage_percent",
			Help:      "系统 CPU 使用率（百分比）",
		},
	)

	apm.goroutineCount = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "goroutine_count",
			Help:      "当前 Goroutine 数量",
		},
	)

	apm.gcDuration = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "gc_duration_seconds",
			Help:      "垃圾回收持续时间（秒）",
		},
	)

	// 初始化业务指标
	apm.loginAttempts = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "login_attempts_total",
			Help:      "登录尝试总数",
		},
		[]string{"result", "method"},
	)

	apm.loginSuccess = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "login_success_total",
			Help:      "登录成功总数",
		},
		[]string{"method"},
	)

	apm.activeSessions = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "active_sessions",
			Help:      "当前活跃会话数",
		},
	)

	apm.apiErrors = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "api_errors_total",
			Help:      "API 错误总数",
		},
		[]string{"endpoint", "error_type"},
	)

	apm.databaseConnections = promauto.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "database_connections",
			Help:      "数据库连接数",
		},
	)

	apm.cacheHitRate = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "paas",
			Subsystem: serviceName,
			Name:      "cache_hit_rate",
			Help:      "缓存命中率",
		},
		[]string{"cache_type"},
	)

	logger.Info("APM 收集器初始化完成", "service", serviceName)
	return apm
}

// HTTPMiddleware 返回 HTTP 监控中间件
func (apm *APMCollector) HTTPMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// 记录请求大小
		if c.Request.ContentLength > 0 {
			apm.httpRequestSize.WithLabelValues(
				c.Request.Method,
				c.FullPath(),
			).Observe(float64(c.Request.ContentLength))
		}

		// 处理请求
		c.Next()

		// 计算持续时间
		duration := time.Since(start)
		
		// 记录指标
		apm.httpRequestsTotal.WithLabelValues(
			c.Request.Method,
			c.FullPath(),
			fmt.Sprintf("%d", c.Writer.Status()),
		).Inc()

		apm.httpRequestDuration.WithLabelValues(
			c.Request.Method,
			c.FullPath(),
		).Observe(duration.Seconds())

		// 记录响应大小
		apm.httpResponseSize.WithLabelValues(
			c.Request.Method,
			c.FullPath(),
		).Observe(float64(c.Writer.Size()))

		// 记录错误
		if c.Writer.Status() >= 400 {
			errorType := "client_error"
			if c.Writer.Status() >= 500 {
				errorType = "server_error"
			}
			
			apm.apiErrors.WithLabelValues(
				c.FullPath(),
				errorType,
			).Inc()
		}
	}
}

// RecordLoginAttempt 记录登录尝试
func (apm *APMCollector) RecordLoginAttempt(success bool, method string) {
	result := "failure"
	if success {
		result = "success"
		apm.loginSuccess.WithLabelValues(method).Inc()
	}
	
	apm.loginAttempts.WithLabelValues(result, method).Inc()
}

// UpdateActiveSessions 更新活跃会话数
func (apm *APMCollector) UpdateActiveSessions(count int) {
	apm.activeSessions.Set(float64(count))
}

// UpdateDatabaseConnections 更新数据库连接数
func (apm *APMCollector) UpdateDatabaseConnections(count int) {
	apm.databaseConnections.Set(float64(count))
}

// UpdateCacheHitRate 更新缓存命中率
func (apm *APMCollector) UpdateCacheHitRate(cacheType string, hitRate float64) {
	apm.cacheHitRate.WithLabelValues(cacheType).Set(hitRate)
}

// UpdateSystemMetrics 更新系统指标
func (apm *APMCollector) UpdateSystemMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 更新内存使用量
	apm.systemMemoryUsage.Set(float64(m.Alloc))
	
	// 更新 Goroutine 数量
	apm.goroutineCount.Set(float64(runtime.NumGoroutine()))
	
	// 更新 GC 持续时间
	apm.gcDuration.Set(float64(m.PauseTotalNs) / 1e9)
}

// StartSystemMetricsCollection 启动系统指标收集
func (apm *APMCollector) StartSystemMetricsCollection(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	go func() {
		for {
			select {
			case <-ctx.Done():
				apm.logger.Info("停止系统指标收集")
				return
			case <-ticker.C:
				apm.UpdateSystemMetrics()
			}
		}
	}()

	apm.logger.Info("启动系统指标收集", "interval", interval)
}

// RegisterCustomMetric 注册自定义指标
func (apm *APMCollector) RegisterCustomMetric(name string, collector prometheus.Collector) error {
	apm.mutex.Lock()
	defer apm.mutex.Unlock()

	if _, exists := apm.customMetrics[name]; exists {
		return fmt.Errorf("指标 %s 已存在", name)
	}

	if err := prometheus.Register(collector); err != nil {
		return fmt.Errorf("注册指标失败: %w", err)
	}

	apm.customMetrics[name] = collector
	apm.logger.Info("注册自定义指标", "name", name)
	return nil
}

// GetMetricsHandler 获取指标处理器
func (apm *APMCollector) GetMetricsHandler() http.Handler {
	return promhttp.Handler()
}

// GetHealthHandler 获取健康检查处理器
func (apm *APMCollector) GetHealthHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 基本健康检查
		health := map[string]interface{}{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
			"service":   "apm-collector",
			"version":   "1.0.0",
		}

		// 添加系统信息
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		
		health["system"] = map[string]interface{}{
			"memory_usage_mb": float64(m.Alloc) / 1024 / 1024,
			"goroutines":      runtime.NumGoroutine(),
			"gc_cycles":       m.NumGC,
		}

		c.JSON(http.StatusOK, health)
	}
}

// Shutdown 关闭 APM 收集器
func (apm *APMCollector) Shutdown() {
	apm.mutex.Lock()
	defer apm.mutex.Unlock()

	// 注销自定义指标
	for name, collector := range apm.customMetrics {
		prometheus.Unregister(collector)
		apm.logger.Debug("注销自定义指标", "name", name)
	}

	apm.logger.Info("APM 收集器已关闭")
}
