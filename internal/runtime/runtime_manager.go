package runtime

import (
	"context"
	"fmt"
	"strings"
	"time"

	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// RuntimeManager 运行时管理器接口
type RuntimeManager interface {
	// 获取支持的运行时列表
	GetSupportedRuntimes() []RuntimeInfo
	
	// 获取运行时适配器
	GetRuntimeAdapter(runtimeType RuntimeType) (RuntimeAdapter, error)
	
	// 注册运行时适配器
	RegisterRuntimeAdapter(runtimeType RuntimeType, adapter RuntimeAdapter) error
	
	// 检测脚本运行时类型
	DetectRuntimeType(scriptPath string, content []byte) (RuntimeType, error)
	
	// 验证运行时配置
	ValidateRuntimeConfig(runtimeType RuntimeType, config *RuntimeConfig) error
}

// RuntimeAdapter 运行时适配器接口
type RuntimeAdapter interface {
	// 获取运行时信息
	GetRuntimeInfo() RuntimeInfo
	
	// 构建容器规格
	BuildContainerSpec(ctx context.Context, req *ExecutionRequest) (*container.ContainerSpec, error)
	
	// 构建执行命令
	BuildExecutionCommand(req *ExecutionRequest) ([]string, error)
	
	// 处理依赖安装
	HandleDependencies(ctx context.Context, containerID string, deps *Dependencies) error
	
	// 解析执行结果
	ParseExecutionResult(output string, exitCode int) (*ExecutionResult, error)
	
	// 获取健康检查命令
	GetHealthCheckCommand() []string
	
	// 获取默认环境变量
	GetDefaultEnvironment() map[string]string
}

// RuntimeType 运行时类型
type RuntimeType string

const (
	RuntimeTypePython RuntimeType = "python"
	RuntimeTypeNodeJS RuntimeType = "nodejs"
	RuntimeTypeGo     RuntimeType = "go"
	RuntimeTypeJava   RuntimeType = "java"
	RuntimeTypePHP    RuntimeType = "php"
	RuntimeTypeRuby   RuntimeType = "ruby"
)

// RuntimeInfo 运行时信息
type RuntimeInfo struct {
	Type        RuntimeType `json:"type"`
	Name        string      `json:"name"`
	Version     string      `json:"version"`
	Description string      `json:"description"`
	ImageTag    string      `json:"image_tag"`
	Extensions  []string    `json:"extensions"`
	Commands    []string    `json:"commands"`
	Supported   bool        `json:"supported"`
}

// RuntimeConfig 运行时配置
type RuntimeConfig struct {
	Type         RuntimeType       `json:"type"`
	Version      string            `json:"version"`
	ImageTag     string            `json:"image_tag"`
	Environment  map[string]string `json:"environment"`
	Dependencies *Dependencies     `json:"dependencies"`
	Resources    *ResourceLimits   `json:"resources"`
	Options      map[string]interface{} `json:"options"`
}

// Dependencies 依赖配置
type Dependencies struct {
	Type     DependencyType `json:"type"`
	File     string         `json:"file"`     // 依赖文件路径 (package.json, requirements.txt等)
	Packages []string       `json:"packages"` // 直接指定的包列表
	Registry string         `json:"registry"` // 包注册表URL
	Options  map[string]interface{} `json:"options"`
}

// DependencyType 依赖类型
type DependencyType string

const (
	DependencyTypeNPM    DependencyType = "npm"
	DependencyTypeYarn   DependencyType = "yarn"
	DependencyTypePip    DependencyType = "pip"
	DependencyTypeConda  DependencyType = "conda"
	DependencyTypeGo     DependencyType = "go"
	DependencyTypeMaven  DependencyType = "maven"
	DependencyTypeGradle DependencyType = "gradle"
)

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	ScriptPath   string                 `json:"script_path"`
	Parameters   map[string]interface{} `json:"parameters"`
	Environment  map[string]string      `json:"environment"`
	RuntimeConfig *RuntimeConfig        `json:"runtime_config"`
	WorkingDir   string                 `json:"working_dir"`
	Timeout      time.Duration          `json:"timeout"`
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ExitCode    int                    `json:"exit_code"`
	Output      string                 `json:"output"`
	Error       string                 `json:"error"`
	Duration    time.Duration          `json:"duration"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	CPULimit    string `json:"cpu_limit"`    // 如 "500m", "1"
	MemoryLimit string `json:"memory_limit"` // 如 "512Mi", "1Gi"
	DiskLimit   string `json:"disk_limit"`   // 如 "1Gi", "10Gi"
}

// runtimeManager 运行时管理器实现
type runtimeManager struct {
	adapters map[RuntimeType]RuntimeAdapter
	logger   logger.Logger
}

// NewRuntimeManager 创建运行时管理器
func NewRuntimeManager(logger logger.Logger) RuntimeManager {
	manager := &runtimeManager{
		adapters: make(map[RuntimeType]RuntimeAdapter),
		logger:   logger,
	}

	// 注册默认的运行时适配器
	manager.registerDefaultAdapters()

	return manager
}

// GetSupportedRuntimes 获取支持的运行时列表
func (rm *runtimeManager) GetSupportedRuntimes() []RuntimeInfo {
	var runtimes []RuntimeInfo
	for _, adapter := range rm.adapters {
		runtimes = append(runtimes, adapter.GetRuntimeInfo())
	}
	return runtimes
}

// GetRuntimeAdapter 获取运行时适配器
func (rm *runtimeManager) GetRuntimeAdapter(runtimeType RuntimeType) (RuntimeAdapter, error) {
	adapter, exists := rm.adapters[runtimeType]
	if !exists {
		return nil, fmt.Errorf("不支持的运行时类型: %s", runtimeType)
	}
	return adapter, nil
}

// RegisterRuntimeAdapter 注册运行时适配器
func (rm *runtimeManager) RegisterRuntimeAdapter(runtimeType RuntimeType, adapter RuntimeAdapter) error {
	if adapter == nil {
		return fmt.Errorf("适配器不能为空")
	}
	
	rm.adapters[runtimeType] = adapter
	rm.logger.Info("运行时适配器已注册", "type", runtimeType)
	return nil
}

// DetectRuntimeType 检测脚本运行时类型
func (rm *runtimeManager) DetectRuntimeType(scriptPath string, content []byte) (RuntimeType, error) {
	// 基于文件扩展名检测
	if strings.HasSuffix(scriptPath, ".py") {
		return RuntimeTypePython, nil
	}
	if strings.HasSuffix(scriptPath, ".js") || strings.HasSuffix(scriptPath, ".mjs") {
		return RuntimeTypeNodeJS, nil
	}
	if strings.HasSuffix(scriptPath, ".go") {
		return RuntimeTypeGo, nil
	}
	if strings.HasSuffix(scriptPath, ".java") {
		return RuntimeTypeJava, nil
	}
	if strings.HasSuffix(scriptPath, ".php") {
		return RuntimeTypePHP, nil
	}
	if strings.HasSuffix(scriptPath, ".rb") {
		return RuntimeTypeRuby, nil
	}

	// 基于文件内容检测
	contentStr := string(content)
	
	// 检测shebang
	if strings.HasPrefix(contentStr, "#!/usr/bin/env python") || 
	   strings.HasPrefix(contentStr, "#!/usr/bin/python") {
		return RuntimeTypePython, nil
	}
	if strings.HasPrefix(contentStr, "#!/usr/bin/env node") || 
	   strings.HasPrefix(contentStr, "#!/usr/bin/node") {
		return RuntimeTypeNodeJS, nil
	}

	// 检测特定语法特征
	if strings.Contains(contentStr, "import ") && strings.Contains(contentStr, "def ") {
		return RuntimeTypePython, nil
	}
	if strings.Contains(contentStr, "require(") || strings.Contains(contentStr, "import ") && strings.Contains(contentStr, "from ") {
		return RuntimeTypeNodeJS, nil
	}

	return "", fmt.Errorf("无法检测脚本运行时类型: %s", scriptPath)
}

// ValidateRuntimeConfig 验证运行时配置
func (rm *runtimeManager) ValidateRuntimeConfig(runtimeType RuntimeType, config *RuntimeConfig) error {
	if config == nil {
		return fmt.Errorf("运行时配置不能为空")
	}

	if config.Type != runtimeType {
		return fmt.Errorf("运行时类型不匹配: 期望 %s, 实际 %s", runtimeType, config.Type)
	}

	adapter, err := rm.GetRuntimeAdapter(runtimeType)
	if err != nil {
		return err
	}

	// 验证镜像标签
	if config.ImageTag == "" {
		config.ImageTag = adapter.GetRuntimeInfo().ImageTag
	}

	// 验证版本
	if config.Version == "" {
		config.Version = adapter.GetRuntimeInfo().Version
	}

	return nil
}

// registerDefaultAdapters 注册默认的运行时适配器
func (rm *runtimeManager) registerDefaultAdapters() {
	// 注册Python适配器
	pythonAdapter := NewPythonAdapter(rm.logger)
	rm.RegisterRuntimeAdapter(RuntimeTypePython, pythonAdapter)

	// 注册Node.js适配器
	nodejsAdapter := NewNodeJSAdapter(rm.logger)
	rm.RegisterRuntimeAdapter(RuntimeTypeNodeJS, nodejsAdapter)

	rm.logger.Info("默认运行时适配器注册完成")
}

// BaseRuntimeAdapter 基础运行时适配器
type BaseRuntimeAdapter struct {
	logger logger.Logger
	info   RuntimeInfo
}

// NewBaseRuntimeAdapter 创建基础运行时适配器
func NewBaseRuntimeAdapter(logger logger.Logger, info RuntimeInfo) *BaseRuntimeAdapter {
	return &BaseRuntimeAdapter{
		logger: logger,
		info:   info,
	}
}

// GetRuntimeInfo 获取运行时信息
func (bra *BaseRuntimeAdapter) GetRuntimeInfo() RuntimeInfo {
	return bra.info
}

// GetDefaultEnvironment 获取默认环境变量
func (bra *BaseRuntimeAdapter) GetDefaultEnvironment() map[string]string {
	return map[string]string{
		"LANG":     "C.UTF-8",
		"LC_ALL":   "C.UTF-8",
		"TZ":       "UTC",
	}
}

// ParseExecutionResult 解析执行结果
func (bra *BaseRuntimeAdapter) ParseExecutionResult(output string, exitCode int) (*ExecutionResult, error) {
	result := &ExecutionResult{
		ExitCode: exitCode,
		Output:   output,
		Metadata: make(map[string]interface{}),
	}

	if exitCode != 0 {
		result.Error = output
	}

	return result, nil
}

// 工具函数

// ParseResourceLimits 解析资源限制
func ParseResourceLimits(limits *ResourceLimits) (*container.ResourceLimits, error) {
	if limits == nil {
		return &container.ResourceLimits{
			CPULimit:    1000000000, // 1 CPU
			MemoryLimit: 512 * 1024 * 1024, // 512MB
			PidsLimit:   100,
		}, nil
	}

	containerLimits := &container.ResourceLimits{}

	// 解析CPU限制
	if limits.CPULimit != "" {
		cpuLimit, err := parseResourceValue(limits.CPULimit, "cpu")
		if err != nil {
			return containerLimits, fmt.Errorf("解析CPU限制失败: %w", err)
		}
		containerLimits.CPULimit = cpuLimit
	}

	// 解析内存限制
	if limits.MemoryLimit != "" {
		memoryLimit, err := parseResourceValue(limits.MemoryLimit, "memory")
		if err != nil {
			return containerLimits, fmt.Errorf("解析内存限制失败: %w", err)
		}
		containerLimits.MemoryLimit = memoryLimit
	}

	return containerLimits, nil
}

// parseResourceValue 解析资源值
func parseResourceValue(value string, resourceType string) (int64, error) {
	// TODO: 实现资源值解析逻辑
	// 支持 "500m", "1", "512Mi", "1Gi" 等格式
	return 0, fmt.Errorf("资源值解析功能开发中")
}

// MergeEnvironment 合并环境变量
func MergeEnvironment(base, override map[string]string) map[string]string {
	result := make(map[string]string)
	
	// 复制基础环境变量
	for k, v := range base {
		result[k] = v
	}
	
	// 覆盖指定的环境变量
	for k, v := range override {
		result[k] = v
	}
	
	return result
}

// BuildParameterArgs 构建参数参数
func BuildParameterArgs(parameters map[string]interface{}, style ParameterStyle) []string {
	var args []string
	
	for key, value := range parameters {
		switch style {
		case ParameterStyleDoubleDash:
			args = append(args, fmt.Sprintf("--%s", key))
			args = append(args, fmt.Sprintf("%v", value))
		case ParameterStyleSingleDash:
			args = append(args, fmt.Sprintf("-%s", key))
			args = append(args, fmt.Sprintf("%v", value))
		case ParameterStyleEquals:
			args = append(args, fmt.Sprintf("--%s=%v", key, value))
		case ParameterStyleEnvironment:
			// 通过环境变量传递，不添加到命令行参数
		}
	}
	
	return args
}

// ParameterStyle 参数传递样式
type ParameterStyle string

const (
	ParameterStyleDoubleDash  ParameterStyle = "double-dash"  // --key value
	ParameterStyleSingleDash  ParameterStyle = "single-dash"  // -key value
	ParameterStyleEquals      ParameterStyle = "equals"       // --key=value
	ParameterStyleEnvironment ParameterStyle = "environment"  // 环境变量
)
