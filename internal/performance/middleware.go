package performance

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// PerformanceMiddleware 性能监控中间件
func PerformanceMiddleware(analyzer *PerformanceAnalyzer, logger logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()
		
		// 处理请求
		c.Next()
		
		// 计算处理时间
		duration := time.Since(startTime)
		
		// 判断是否为错误
		isError := c.Writer.Status() >= 400
		
		// 记录性能指标
		analyzer.RecordAPIMetrics(c.Request.Method, c.Request.URL.Path, duration, isError)
		
		// 记录详细日志
		logger.Info("HTTP请求完成",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"status", c.Writer.Status(),
			"duration", duration,
			"client_ip", c.ClientIP(),
			"user_agent", c.Request.UserAgent(),
			"request_size", c.Request.ContentLength,
			"response_size", c.<PERSON>.<PERSON>ze(),
		)
		
		// 如果响应时间过长，记录警告
		if duration > 1*time.Second {
			logger.Warn("检测到慢请求",
				"method", c.Request.Method,
				"path", c.Request.URL.Path,
				"duration", duration,
				"status", c.Writer.Status(),
			)
		}
		
		// 添加性能相关的响应头
		c.Header("X-Response-Time", duration.String())
		c.Header("X-Request-ID", getRequestID(c))
	}
}

// CacheMiddleware 缓存中间件
func CacheMiddleware(cacheTTL time.Duration) gin.HandlerFunc {
	// 简单的内存缓存实现
	cache := NewMemoryCache(cacheTTL)
	
	return func(c *gin.Context) {
		// 只对 GET 请求启用缓存
		if c.Request.Method != "GET" {
			c.Next()
			return
		}
		
		// 生成缓存键
		cacheKey := generateCacheKey(c)
		
		// 尝试从缓存获取
		if cachedResponse, found := cache.Get(cacheKey); found {
			// 设置缓存命中头
			c.Header("X-Cache", "HIT")
			c.Header("X-Cache-Key", cacheKey)
			
			// 返回缓存的响应
			response := cachedResponse.(*CachedResponse)
			for key, value := range response.Headers {
				c.Header(key, value)
			}
			c.Data(response.StatusCode, response.ContentType, response.Body)
			c.Abort()
			return
		}
		
		// 缓存未命中，继续处理请求
		c.Header("X-Cache", "MISS")
		c.Header("X-Cache-Key", cacheKey)
		
		// 创建响应写入器来捕获响应
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:          make([]byte, 0),
			headers:       make(map[string]string),
		}
		c.Writer = writer
		
		c.Next()
		
		// 如果响应成功，缓存结果
		if writer.status >= 200 && writer.status < 300 {
			cachedResponse := &CachedResponse{
				StatusCode:  writer.status,
				ContentType: writer.Header().Get("Content-Type"),
				Headers:     writer.headers,
				Body:        writer.body,
				CachedAt:    time.Now(),
			}
			cache.Set(cacheKey, cachedResponse)
		}
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(requestsPerMinute int) gin.HandlerFunc {
	limiter := NewRateLimiter(requestsPerMinute)
	
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		
		if !limiter.Allow(clientIP) {
			c.JSON(429, gin.H{
				"error":   "请求过于频繁",
				"message": "请稍后再试",
				"retry_after": 60,
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// CompressionMiddleware 压缩中间件
func CompressionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查客户端是否支持压缩
		acceptEncoding := c.Request.Header.Get("Accept-Encoding")
		if acceptEncoding == "" {
			c.Next()
			return
		}
		
		// 检查响应大小是否值得压缩
		c.Next()
		
		// 如果响应体较小，不进行压缩
		if c.Writer.Size() < 1024 {
			return
		}
		
		// 设置压缩头
		c.Header("Content-Encoding", "gzip")
		c.Header("Vary", "Accept-Encoding")
	}
}

// 辅助结构和函数

// CachedResponse 缓存的响应
type CachedResponse struct {
	StatusCode  int               `json:"status_code"`
	ContentType string            `json:"content_type"`
	Headers     map[string]string `json:"headers"`
	Body        []byte            `json:"body"`
	CachedAt    time.Time         `json:"cached_at"`
}

// responseWriter 响应写入器
type responseWriter struct {
	gin.ResponseWriter
	body    []byte
	headers map[string]string
	status  int
}

func (w *responseWriter) Write(data []byte) (int, error) {
	w.body = append(w.body, data...)
	return w.ResponseWriter.Write(data)
}

func (w *responseWriter) WriteHeader(statusCode int) {
	w.status = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

func (w *responseWriter) Header() gin.Header {
	return w.ResponseWriter.Header()
}

// MemoryCache 内存缓存
type MemoryCache struct {
	data map[string]*cacheItem
	ttl  time.Duration
}

type cacheItem struct {
	value     interface{}
	expiresAt time.Time
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache(ttl time.Duration) *MemoryCache {
	cache := &MemoryCache{
		data: make(map[string]*cacheItem),
		ttl:  ttl,
	}
	
	// 启动清理协程
	go cache.cleanup()
	
	return cache
}

// Get 获取缓存
func (mc *MemoryCache) Get(key string) (interface{}, bool) {
	item, exists := mc.data[key]
	if !exists {
		return nil, false
	}
	
	if time.Now().After(item.expiresAt) {
		delete(mc.data, key)
		return nil, false
	}
	
	return item.value, true
}

// Set 设置缓存
func (mc *MemoryCache) Set(key string, value interface{}) {
	mc.data[key] = &cacheItem{
		value:     value,
		expiresAt: time.Now().Add(mc.ttl),
	}
}

// cleanup 清理过期缓存
func (mc *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		now := time.Now()
		for key, item := range mc.data {
			if now.After(item.expiresAt) {
				delete(mc.data, key)
			}
		}
	}
}

// RateLimiter 限流器
type RateLimiter struct {
	requests map[string]*clientRequests
	limit    int
}

type clientRequests struct {
	count     int
	resetTime time.Time
}

// NewRateLimiter 创建限流器
func NewRateLimiter(requestsPerMinute int) *RateLimiter {
	limiter := &RateLimiter{
		requests: make(map[string]*clientRequests),
		limit:    requestsPerMinute,
	}
	
	// 启动清理协程
	go limiter.cleanup()
	
	return limiter
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow(clientIP string) bool {
	now := time.Now()
	
	client, exists := rl.requests[clientIP]
	if !exists {
		rl.requests[clientIP] = &clientRequests{
			count:     1,
			resetTime: now.Add(time.Minute),
		}
		return true
	}
	
	// 检查是否需要重置计数
	if now.After(client.resetTime) {
		client.count = 1
		client.resetTime = now.Add(time.Minute)
		return true
	}
	
	// 检查是否超过限制
	if client.count >= rl.limit {
		return false
	}
	
	client.count++
	return true
}

// cleanup 清理过期的客户端记录
func (rl *RateLimiter) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		now := time.Now()
		for clientIP, client := range rl.requests {
			if now.After(client.resetTime) {
				delete(rl.requests, clientIP)
			}
		}
	}
}

// 辅助函数

// generateCacheKey 生成缓存键
func generateCacheKey(c *gin.Context) string {
	return c.Request.Method + ":" + c.Request.URL.Path + ":" + c.Request.URL.RawQuery
}

// getRequestID 获取请求ID
func getRequestID(c *gin.Context) string {
	// 尝试从头部获取
	if requestID := c.Request.Header.Get("X-Request-ID"); requestID != "" {
		return requestID
	}
	
	// 生成新的请求ID
	return generateRequestID()
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	// 缓存配置
	CacheEnabled bool          `yaml:"cache_enabled"`
	CacheTTL     time.Duration `yaml:"cache_ttl"`
	
	// 限流配置
	RateLimitEnabled      bool `yaml:"rate_limit_enabled"`
	RequestsPerMinute     int  `yaml:"requests_per_minute"`
	
	// 压缩配置
	CompressionEnabled    bool `yaml:"compression_enabled"`
	CompressionThreshold  int  `yaml:"compression_threshold"`
	
	// 监控配置
	MonitoringEnabled     bool          `yaml:"monitoring_enabled"`
	SlowRequestThreshold  time.Duration `yaml:"slow_request_threshold"`
	
	// 数据库配置
	DBMaxConnections      int           `yaml:"db_max_connections"`
	DBMaxIdleConnections  int           `yaml:"db_max_idle_connections"`
	DBConnectionLifetime  time.Duration `yaml:"db_connection_lifetime"`
}

// DefaultPerformanceConfig 默认性能配置
func DefaultPerformanceConfig() *PerformanceConfig {
	return &PerformanceConfig{
		CacheEnabled:          true,
		CacheTTL:              5 * time.Minute,
		RateLimitEnabled:      true,
		RequestsPerMinute:     100,
		CompressionEnabled:    true,
		CompressionThreshold:  1024,
		MonitoringEnabled:     true,
		SlowRequestThreshold:  1 * time.Second,
		DBMaxConnections:      25,
		DBMaxIdleConnections:  5,
		DBConnectionLifetime:  5 * time.Minute,
	}
}

// ApplyPerformanceMiddleware 应用性能中间件
func ApplyPerformanceMiddleware(router *gin.Engine, analyzer *PerformanceAnalyzer, config *PerformanceConfig, logger logger.Logger) {
	// 性能监控中间件
	if config.MonitoringEnabled {
		router.Use(PerformanceMiddleware(analyzer, logger))
	}
	
	// 限流中间件
	if config.RateLimitEnabled {
		router.Use(RateLimitMiddleware(config.RequestsPerMinute))
	}
	
	// 缓存中间件
	if config.CacheEnabled {
		router.Use(CacheMiddleware(config.CacheTTL))
	}
	
	// 压缩中间件
	if config.CompressionEnabled {
		router.Use(CompressionMiddleware())
	}
	
	logger.Info("性能中间件已应用",
		"cache_enabled", config.CacheEnabled,
		"rate_limit_enabled", config.RateLimitEnabled,
		"compression_enabled", config.CompressionEnabled,
		"monitoring_enabled", config.MonitoringEnabled)
}
