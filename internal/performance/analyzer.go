package performance

import (
	"context"
	"database/sql"
	"fmt"
	"runtime"
	"sync"
	"time"

	"gorm.io/gorm"
	"paas-platform/pkg/logger"
)

// PerformanceAnalyzer 性能分析器
type PerformanceAnalyzer struct {
	db     *gorm.DB
	logger logger.Logger
	mutex  sync.RWMutex
	
	// 性能指标收集
	metrics *PerformanceMetrics
	
	// 慢查询收集
	slowQueries []SlowQuery
	
	// API 性能收集
	apiMetrics map[string]*APIMetrics
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	// 系统指标
	CPUUsage    float64   `json:"cpu_usage"`
	MemoryUsage int64     `json:"memory_usage"`
	GoroutineCount int    `json:"goroutine_count"`
	GCPauseTime time.Duration `json:"gc_pause_time"`
	
	// 数据库指标
	DBConnections     int           `json:"db_connections"`
	DBMaxConnections  int           `json:"db_max_connections"`
	DBIdleConnections int           `json:"db_idle_connections"`
	AvgQueryTime      time.Duration `json:"avg_query_time"`
	SlowQueryCount    int           `json:"slow_query_count"`
	
	// HTTP 指标
	TotalRequests     int64         `json:"total_requests"`
	AvgResponseTime   time.Duration `json:"avg_response_time"`
	ErrorRate         float64       `json:"error_rate"`
	
	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// SlowQuery 慢查询记录
type SlowQuery struct {
	SQL       string        `json:"sql"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
	Args      []interface{} `json:"args,omitempty"`
	Error     string        `json:"error,omitempty"`
}

// APIMetrics API 性能指标
type APIMetrics struct {
	Path            string        `json:"path"`
	Method          string        `json:"method"`
	TotalRequests   int64         `json:"total_requests"`
	TotalDuration   time.Duration `json:"total_duration"`
	MinDuration     time.Duration `json:"min_duration"`
	MaxDuration     time.Duration `json:"max_duration"`
	ErrorCount      int64         `json:"error_count"`
	LastRequestTime time.Time     `json:"last_request_time"`
}

// PerformanceReport 性能报告
type PerformanceReport struct {
	Summary     *PerformanceMetrics    `json:"summary"`
	SlowQueries []SlowQuery           `json:"slow_queries"`
	APIMetrics  map[string]*APIMetrics `json:"api_metrics"`
	Bottlenecks []PerformanceBottleneck `json:"bottlenecks"`
	Recommendations []string          `json:"recommendations"`
	GeneratedAt time.Time             `json:"generated_at"`
}

// PerformanceBottleneck 性能瓶颈
type PerformanceBottleneck struct {
	Type        string  `json:"type"`        // database, api, memory, cpu
	Severity    string  `json:"severity"`    // low, medium, high, critical
	Description string  `json:"description"`
	Impact      string  `json:"impact"`
	Solution    string  `json:"solution"`
	Priority    int     `json:"priority"`
}

// NewPerformanceAnalyzer 创建性能分析器
func NewPerformanceAnalyzer(db *gorm.DB, logger logger.Logger) *PerformanceAnalyzer {
	analyzer := &PerformanceAnalyzer{
		db:          db,
		logger:      logger,
		metrics:     &PerformanceMetrics{},
		slowQueries: make([]SlowQuery, 0),
		apiMetrics:  make(map[string]*APIMetrics),
	}
	
	// 启用数据库性能监控
	analyzer.enableDBPerformanceMonitoring()
	
	return analyzer
}

// enableDBPerformanceMonitoring 启用数据库性能监控
func (pa *PerformanceAnalyzer) enableDBPerformanceMonitoring() {
	// 注册慢查询回调
	pa.db.Callback().Query().Before("gorm:query").Register("performance:before_query", pa.beforeQuery)
	pa.db.Callback().Query().After("gorm:query").Register("performance:after_query", pa.afterQuery)
	
	pa.db.Callback().Create().Before("gorm:create").Register("performance:before_create", pa.beforeQuery)
	pa.db.Callback().Create().After("gorm:create").Register("performance:after_create", pa.afterQuery)
	
	pa.db.Callback().Update().Before("gorm:update").Register("performance:before_update", pa.beforeQuery)
	pa.db.Callback().Update().After("gorm:update").Register("performance:after_update", pa.afterQuery)
	
	pa.db.Callback().Delete().Before("gorm:delete").Register("performance:before_delete", pa.beforeQuery)
	pa.db.Callback().Delete().After("gorm:delete").Register("performance:after_delete", pa.afterQuery)
}

// beforeQuery 查询前回调
func (pa *PerformanceAnalyzer) beforeQuery(db *gorm.DB) {
	db.Set("performance:start_time", time.Now())
}

// afterQuery 查询后回调
func (pa *PerformanceAnalyzer) afterQuery(db *gorm.DB) {
	startTime, exists := db.Get("performance:start_time")
	if !exists {
		return
	}
	
	duration := time.Since(startTime.(time.Time))
	
	// 记录慢查询（超过100ms）
	if duration > 100*time.Millisecond {
		pa.recordSlowQuery(db.Statement.SQL.String(), duration, db.Statement.Vars, db.Error)
	}
}

// recordSlowQuery 记录慢查询
func (pa *PerformanceAnalyzer) recordSlowQuery(sql string, duration time.Duration, args []interface{}, err error) {
	pa.mutex.Lock()
	defer pa.mutex.Unlock()
	
	slowQuery := SlowQuery{
		SQL:       sql,
		Duration:  duration,
		Timestamp: time.Now(),
		Args:      args,
	}
	
	if err != nil {
		slowQuery.Error = err.Error()
	}
	
	pa.slowQueries = append(pa.slowQueries, slowQuery)
	
	// 保持最近的1000条慢查询记录
	if len(pa.slowQueries) > 1000 {
		pa.slowQueries = pa.slowQueries[len(pa.slowQueries)-1000:]
	}
	
	pa.logger.Warn("检测到慢查询",
		"sql", sql,
		"duration", duration,
		"args", args)
}

// RecordAPIMetrics 记录 API 性能指标
func (pa *PerformanceAnalyzer) RecordAPIMetrics(method, path string, duration time.Duration, isError bool) {
	pa.mutex.Lock()
	defer pa.mutex.Unlock()
	
	key := fmt.Sprintf("%s %s", method, path)
	
	metrics, exists := pa.apiMetrics[key]
	if !exists {
		metrics = &APIMetrics{
			Path:          path,
			Method:        method,
			MinDuration:   duration,
			MaxDuration:   duration,
		}
		pa.apiMetrics[key] = metrics
	}
	
	// 更新指标
	metrics.TotalRequests++
	metrics.TotalDuration += duration
	metrics.LastRequestTime = time.Now()
	
	if duration < metrics.MinDuration {
		metrics.MinDuration = duration
	}
	if duration > metrics.MaxDuration {
		metrics.MaxDuration = duration
	}
	
	if isError {
		metrics.ErrorCount++
	}
}

// CollectSystemMetrics 收集系统指标
func (pa *PerformanceAnalyzer) CollectSystemMetrics() {
	pa.mutex.Lock()
	defer pa.mutex.Unlock()
	
	// 收集内存指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	pa.metrics.MemoryUsage = int64(m.Alloc)
	pa.metrics.GoroutineCount = runtime.NumGoroutine()
	pa.metrics.GCPauseTime = time.Duration(m.PauseTotalNs)
	
	// 收集数据库连接指标
	if sqlDB, err := pa.db.DB(); err == nil {
		stats := sqlDB.Stats()
		pa.metrics.DBConnections = stats.OpenConnections
		pa.metrics.DBMaxConnections = stats.MaxOpenConnections
		pa.metrics.DBIdleConnections = stats.Idle
	}
	
	// 计算慢查询数量
	pa.metrics.SlowQueryCount = len(pa.slowQueries)
	
	// 计算平均查询时间
	if len(pa.slowQueries) > 0 {
		var totalDuration time.Duration
		for _, query := range pa.slowQueries {
			totalDuration += query.Duration
		}
		pa.metrics.AvgQueryTime = totalDuration / time.Duration(len(pa.slowQueries))
	}
	
	// 计算 HTTP 指标
	var totalRequests int64
	var totalDuration time.Duration
	var errorCount int64
	
	for _, metrics := range pa.apiMetrics {
		totalRequests += metrics.TotalRequests
		totalDuration += metrics.TotalDuration
		errorCount += metrics.ErrorCount
	}
	
	pa.metrics.TotalRequests = totalRequests
	if totalRequests > 0 {
		pa.metrics.AvgResponseTime = totalDuration / time.Duration(totalRequests)
		pa.metrics.ErrorRate = float64(errorCount) / float64(totalRequests) * 100
	}
	
	pa.metrics.Timestamp = time.Now()
}

// AnalyzeBottlenecks 分析性能瓶颈
func (pa *PerformanceAnalyzer) AnalyzeBottlenecks() []PerformanceBottleneck {
	pa.mutex.RLock()
	defer pa.mutex.RUnlock()
	
	var bottlenecks []PerformanceBottleneck
	
	// 分析内存使用
	if pa.metrics.MemoryUsage > 1024*1024*1024 { // 1GB
		bottlenecks = append(bottlenecks, PerformanceBottleneck{
			Type:        "memory",
			Severity:    "high",
			Description: fmt.Sprintf("内存使用过高: %d MB", pa.metrics.MemoryUsage/1024/1024),
			Impact:      "可能导致系统响应缓慢或内存溢出",
			Solution:    "优化内存使用，检查内存泄漏，增加内存限制",
			Priority:    1,
		})
	}
	
	// 分析 Goroutine 数量
	if pa.metrics.GoroutineCount > 1000 {
		bottlenecks = append(bottlenecks, PerformanceBottleneck{
			Type:        "goroutine",
			Severity:    "medium",
			Description: fmt.Sprintf("Goroutine 数量过多: %d", pa.metrics.GoroutineCount),
			Impact:      "可能存在 Goroutine 泄漏，影响系统性能",
			Solution:    "检查 Goroutine 泄漏，优化并发控制",
			Priority:    2,
		})
	}
	
	// 分析数据库连接
	if pa.metrics.DBConnections > pa.metrics.DBMaxConnections*8/10 {
		bottlenecks = append(bottlenecks, PerformanceBottleneck{
			Type:        "database",
			Severity:    "high",
			Description: fmt.Sprintf("数据库连接使用率过高: %d/%d", pa.metrics.DBConnections, pa.metrics.DBMaxConnections),
			Impact:      "可能导致数据库连接耗尽，影响应用可用性",
			Solution:    "优化数据库连接池配置，减少长连接，使用连接复用",
			Priority:    1,
		})
	}
	
	// 分析慢查询
	if pa.metrics.SlowQueryCount > 10 {
		bottlenecks = append(bottlenecks, PerformanceBottleneck{
			Type:        "database",
			Severity:    "medium",
			Description: fmt.Sprintf("慢查询数量过多: %d", pa.metrics.SlowQueryCount),
			Impact:      "影响数据库性能和用户体验",
			Solution:    "优化 SQL 查询，添加索引，使用查询缓存",
			Priority:    2,
		})
	}
	
	// 分析 API 响应时间
	if pa.metrics.AvgResponseTime > 500*time.Millisecond {
		bottlenecks = append(bottlenecks, PerformanceBottleneck{
			Type:        "api",
			Severity:    "medium",
			Description: fmt.Sprintf("API 平均响应时间过长: %v", pa.metrics.AvgResponseTime),
			Impact:      "影响用户体验和系统吞吐量",
			Solution:    "优化业务逻辑，使用缓存，异步处理",
			Priority:    2,
		})
	}
	
	// 分析错误率
	if pa.metrics.ErrorRate > 5.0 {
		bottlenecks = append(bottlenecks, PerformanceBottleneck{
			Type:        "api",
			Severity:    "high",
			Description: fmt.Sprintf("API 错误率过高: %.2f%%", pa.metrics.ErrorRate),
			Impact:      "影响系统可靠性和用户体验",
			Solution:    "修复错误处理逻辑，增强系统稳定性",
			Priority:    1,
		})
	}
	
	return bottlenecks
}

// GenerateRecommendations 生成优化建议
func (pa *PerformanceAnalyzer) GenerateRecommendations(bottlenecks []PerformanceBottleneck) []string {
	var recommendations []string
	
	// 基于瓶颈生成建议
	for _, bottleneck := range bottlenecks {
		recommendations = append(recommendations, bottleneck.Solution)
	}
	
	// 通用优化建议
	recommendations = append(recommendations,
		"定期监控系统性能指标",
		"建立性能基线和告警机制",
		"使用缓存减少数据库查询",
		"优化数据库索引和查询",
		"实施代码性能分析和优化",
		"考虑使用 CDN 加速静态资源",
		"实施负载均衡和水平扩展",
	)
	
	return recommendations
}

// GenerateReport 生成性能报告
func (pa *PerformanceAnalyzer) GenerateReport() *PerformanceReport {
	// 收集最新指标
	pa.CollectSystemMetrics()
	
	// 分析瓶颈
	bottlenecks := pa.AnalyzeBottlenecks()
	
	// 生成建议
	recommendations := pa.GenerateRecommendations(bottlenecks)
	
	pa.mutex.RLock()
	defer pa.mutex.RUnlock()
	
	// 复制慢查询（最近50条）
	slowQueries := make([]SlowQuery, 0)
	if len(pa.slowQueries) > 0 {
		start := 0
		if len(pa.slowQueries) > 50 {
			start = len(pa.slowQueries) - 50
		}
		slowQueries = append(slowQueries, pa.slowQueries[start:]...)
	}
	
	// 复制 API 指标
	apiMetrics := make(map[string]*APIMetrics)
	for k, v := range pa.apiMetrics {
		apiMetrics[k] = &APIMetrics{
			Path:            v.Path,
			Method:          v.Method,
			TotalRequests:   v.TotalRequests,
			TotalDuration:   v.TotalDuration,
			MinDuration:     v.MinDuration,
			MaxDuration:     v.MaxDuration,
			ErrorCount:      v.ErrorCount,
			LastRequestTime: v.LastRequestTime,
		}
	}
	
	return &PerformanceReport{
		Summary:         pa.metrics,
		SlowQueries:     slowQueries,
		APIMetrics:      apiMetrics,
		Bottlenecks:     bottlenecks,
		Recommendations: recommendations,
		GeneratedAt:     time.Now(),
	}
}

// StartPeriodicCollection 启动定期收集
func (pa *PerformanceAnalyzer) StartPeriodicCollection(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	
	go func() {
		for {
			select {
			case <-ctx.Done():
				pa.logger.Info("停止性能指标收集")
				return
			case <-ticker.C:
				pa.CollectSystemMetrics()
			}
		}
	}()
	
	pa.logger.Info("启动性能指标定期收集", "interval", interval)
}

// GetTopSlowQueries 获取最慢的查询
func (pa *PerformanceAnalyzer) GetTopSlowQueries(limit int) []SlowQuery {
	pa.mutex.RLock()
	defer pa.mutex.RUnlock()
	
	if len(pa.slowQueries) == 0 {
		return []SlowQuery{}
	}
	
	// 复制并排序
	queries := make([]SlowQuery, len(pa.slowQueries))
	copy(queries, pa.slowQueries)
	
	// 按持续时间排序
	for i := 0; i < len(queries)-1; i++ {
		for j := i + 1; j < len(queries); j++ {
			if queries[i].Duration < queries[j].Duration {
				queries[i], queries[j] = queries[j], queries[i]
			}
		}
	}
	
	if limit > len(queries) {
		limit = len(queries)
	}
	
	return queries[:limit]
}

// GetTopSlowAPIs 获取最慢的 API
func (pa *PerformanceAnalyzer) GetTopSlowAPIs(limit int) []*APIMetrics {
	pa.mutex.RLock()
	defer pa.mutex.RUnlock()
	
	if len(pa.apiMetrics) == 0 {
		return []*APIMetrics{}
	}
	
	// 转换为切片并排序
	apis := make([]*APIMetrics, 0, len(pa.apiMetrics))
	for _, metrics := range pa.apiMetrics {
		if metrics.TotalRequests > 0 {
			apis = append(apis, metrics)
		}
	}
	
	// 按平均响应时间排序
	for i := 0; i < len(apis)-1; i++ {
		for j := i + 1; j < len(apis); j++ {
			avgI := apis[i].TotalDuration / time.Duration(apis[i].TotalRequests)
			avgJ := apis[j].TotalDuration / time.Duration(apis[j].TotalRequests)
			if avgI < avgJ {
				apis[i], apis[j] = apis[j], apis[i]
			}
		}
	}
	
	if limit > len(apis) {
		limit = len(apis)
	}
	
	return apis[:limit]
}

// Reset 重置性能数据
func (pa *PerformanceAnalyzer) Reset() {
	pa.mutex.Lock()
	defer pa.mutex.Unlock()
	
	pa.slowQueries = make([]SlowQuery, 0)
	pa.apiMetrics = make(map[string]*APIMetrics)
	pa.metrics = &PerformanceMetrics{}
	
	pa.logger.Info("性能分析器数据已重置")
}
