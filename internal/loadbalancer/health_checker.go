package loadbalancer

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// HealthChecker 健康检查器
type HealthChecker struct {
	registry *ServiceRegistry
	logger   logger.Logger
	client   *http.Client
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	ServiceName string        `json:"service_name"`
	InstanceID  string        `json:"instance_id"`
	Status      string        `json:"status"`
	ResponseTime time.Duration `json:"response_time"`
	Error       string        `json:"error,omitempty"`
	Timestamp   time.Time     `json:"timestamp"`
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(registry *ServiceRegistry, logger logger.Logger) *HealthChecker {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &HealthChecker{
		registry: registry,
		logger:   logger,
		client: &http.Client{
			Timeout: 10 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				IdleConnTimeout:     30 * time.Second,
			},
		},
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start 启动健康检查
func (hc *HealthChecker) Start() {
	hc.logger.Info("启动健康检查器")
	
	// 启动主检查循环
	go hc.startHealthCheckLoop()
}

// Stop 停止健康检查
func (hc *HealthChecker) Stop() {
	hc.logger.Info("停止健康检查器")
	hc.cancel()
	hc.wg.Wait()
}

// startHealthCheckLoop 启动健康检查循环
func (hc *HealthChecker) startHealthCheckLoop() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-hc.ctx.Done():
			return
		case <-ticker.C:
			hc.performHealthChecks()
		}
	}
}

// performHealthChecks 执行健康检查
func (hc *HealthChecker) performHealthChecks() {
	services := hc.registry.ListServices()
	
	for serviceName, serviceGroup := range services {
		for _, instance := range serviceGroup.Instances {
			// 检查是否需要进行健康检查
			if hc.shouldPerformHealthCheck(instance) {
				hc.wg.Add(1)
				go hc.checkInstanceHealth(serviceName, instance)
			}
		}
	}
}

// shouldPerformHealthCheck 判断是否需要进行健康检查
func (hc *HealthChecker) shouldPerformHealthCheck(instance *ServiceInstance) bool {
	// 如果从未检查过，需要检查
	if instance.LastHealthCheck.IsZero() {
		return true
	}
	
	// 根据配置的间隔判断是否需要检查
	interval := instance.HealthCheckInterval
	if interval == 0 {
		interval = 30 * time.Second // 默认30秒
	}
	
	return time.Since(instance.LastHealthCheck) >= interval
}

// checkInstanceHealth 检查单个实例的健康状态
func (hc *HealthChecker) checkInstanceHealth(serviceName string, instance *ServiceInstance) {
	defer hc.wg.Done()
	
	start := time.Now()
	result := &HealthCheckResult{
		ServiceName: serviceName,
		InstanceID:  instance.ID,
		Timestamp:   start,
	}
	
	// 执行健康检查
	status, err := hc.performHealthCheck(instance)
	result.ResponseTime = time.Since(start)
	
	if err != nil {
		result.Status = "unhealthy"
		result.Error = err.Error()
		hc.logger.Debug("健康检查失败", "service", serviceName, "instance", instance.ID, 
			"error", err, "response_time", result.ResponseTime)
	} else {
		result.Status = status
		hc.logger.Debug("健康检查成功", "service", serviceName, "instance", instance.ID, 
			"status", status, "response_time", result.ResponseTime)
	}
	
	// 更新实例状态
	hc.registry.UpdateInstanceStatus(serviceName, instance.ID, result.Status)
	
	// 记录健康检查历史（可选）
	hc.recordHealthCheckResult(result)
}

// performHealthCheck 执行具体的健康检查
func (hc *HealthChecker) performHealthCheck(instance *ServiceInstance) (string, error) {
	// 创建带超时的上下文
	timeout := instance.HealthCheckTimeout
	if timeout == 0 {
		timeout = 5 * time.Second // 默认5秒超时
	}
	
	ctx, cancel := context.WithTimeout(hc.ctx, timeout)
	defer cancel()
	
	// 构建健康检查请求
	req, err := http.NewRequestWithContext(ctx, "GET", instance.HealthCheckURL, nil)
	if err != nil {
		return "unhealthy", fmt.Errorf("创建健康检查请求失败: %w", err)
	}
	
	// 设置请求头
	req.Header.Set("User-Agent", "PaaS-LoadBalancer-HealthChecker/1.0")
	req.Header.Set("Accept", "application/json")
	
	// 发送请求
	resp, err := hc.client.Do(req)
	if err != nil {
		return "unhealthy", fmt.Errorf("健康检查请求失败: %w", err)
	}
	defer resp.Body.Close()
	
	// 检查响应状态码
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		return "healthy", nil
	} else if resp.StatusCode >= 500 {
		return "unhealthy", fmt.Errorf("服务器错误: HTTP %d", resp.StatusCode)
	} else {
		return "degraded", fmt.Errorf("服务降级: HTTP %d", resp.StatusCode)
	}
}

// recordHealthCheckResult 记录健康检查结果
func (hc *HealthChecker) recordHealthCheckResult(result *HealthCheckResult) {
	// 这里可以将健康检查结果存储到数据库或发送到监控系统
	// 目前只是记录日志
	if result.Status != "healthy" {
		hc.logger.Warn("实例健康检查异常", 
			"service", result.ServiceName,
			"instance", result.InstanceID,
			"status", result.Status,
			"response_time", result.ResponseTime,
			"error", result.Error)
	}
}

// CheckInstanceNow 立即检查指定实例的健康状态
func (hc *HealthChecker) CheckInstanceNow(serviceName, instanceID string) (*HealthCheckResult, error) {
	serviceGroup, err := hc.registry.GetService(serviceName)
	if err != nil {
		return nil, err
	}
	
	var targetInstance *ServiceInstance
	for _, instance := range serviceGroup.Instances {
		if instance.ID == instanceID {
			targetInstance = instance
			break
		}
	}
	
	if targetInstance == nil {
		return nil, fmt.Errorf("实例不存在: %s/%s", serviceName, instanceID)
	}
	
	start := time.Now()
	result := &HealthCheckResult{
		ServiceName: serviceName,
		InstanceID:  instanceID,
		Timestamp:   start,
	}
	
	status, err := hc.performHealthCheck(targetInstance)
	result.ResponseTime = time.Since(start)
	
	if err != nil {
		result.Status = "unhealthy"
		result.Error = err.Error()
	} else {
		result.Status = status
	}
	
	// 更新实例状态
	hc.registry.UpdateInstanceStatus(serviceName, instanceID, result.Status)
	
	return result, nil
}

// CheckServiceHealth 检查整个服务的健康状态
func (hc *HealthChecker) CheckServiceHealth(serviceName string) (map[string]*HealthCheckResult, error) {
	serviceGroup, err := hc.registry.GetService(serviceName)
	if err != nil {
		return nil, err
	}
	
	results := make(map[string]*HealthCheckResult)
	var wg sync.WaitGroup
	var mutex sync.Mutex
	
	for _, instance := range serviceGroup.Instances {
		wg.Add(1)
		go func(inst *ServiceInstance) {
			defer wg.Done()
			
			start := time.Now()
			result := &HealthCheckResult{
				ServiceName: serviceName,
				InstanceID:  inst.ID,
				Timestamp:   start,
			}
			
			status, err := hc.performHealthCheck(inst)
			result.ResponseTime = time.Since(start)
			
			if err != nil {
				result.Status = "unhealthy"
				result.Error = err.Error()
			} else {
				result.Status = status
			}
			
			mutex.Lock()
			results[inst.ID] = result
			mutex.Unlock()
			
			// 更新实例状态
			hc.registry.UpdateInstanceStatus(serviceName, inst.ID, result.Status)
		}(instance)
	}
	
	wg.Wait()
	return results, nil
}

// GetHealthStats 获取健康检查统计信息
func (hc *HealthChecker) GetHealthStats() map[string]interface{} {
	services := hc.registry.ListServices()
	
	stats := map[string]interface{}{
		"total_services":     len(services),
		"total_instances":    0,
		"healthy_instances":  0,
		"unhealthy_instances": 0,
		"degraded_instances": 0,
		"unknown_instances":  0,
		"services":          make(map[string]interface{}),
	}
	
	totalInstances := 0
	healthyInstances := 0
	unhealthyInstances := 0
	degradedInstances := 0
	unknownInstances := 0
	
	for serviceName, serviceGroup := range services {
		serviceStats := map[string]interface{}{
			"instance_count":     len(serviceGroup.Instances),
			"healthy_count":      0,
			"unhealthy_count":    0,
			"degraded_count":     0,
			"unknown_count":      0,
			"last_check_time":    time.Time{},
		}
		
		serviceHealthy := 0
		serviceUnhealthy := 0
		serviceDegraded := 0
		serviceUnknown := 0
		var lastCheckTime time.Time
		
		for _, instance := range serviceGroup.Instances {
			totalInstances++
			
			switch instance.Status {
			case "healthy":
				serviceHealthy++
				healthyInstances++
			case "unhealthy":
				serviceUnhealthy++
				unhealthyInstances++
			case "degraded":
				serviceDegraded++
				degradedInstances++
			default:
				serviceUnknown++
				unknownInstances++
			}
			
			if instance.LastHealthCheck.After(lastCheckTime) {
				lastCheckTime = instance.LastHealthCheck
			}
		}
		
		serviceStats["healthy_count"] = serviceHealthy
		serviceStats["unhealthy_count"] = serviceUnhealthy
		serviceStats["degraded_count"] = serviceDegraded
		serviceStats["unknown_count"] = serviceUnknown
		serviceStats["last_check_time"] = lastCheckTime
		
		stats["services"].(map[string]interface{})[serviceName] = serviceStats
	}
	
	stats["total_instances"] = totalInstances
	stats["healthy_instances"] = healthyInstances
	stats["unhealthy_instances"] = unhealthyInstances
	stats["degraded_instances"] = degradedInstances
	stats["unknown_instances"] = unknownInstances
	
	return stats
}

// SetHealthCheckConfig 设置健康检查配置
func (hc *HealthChecker) SetHealthCheckConfig(serviceName string, config *LoadBalancerConfig) error {
	hc.registry.mutex.Lock()
	defer hc.registry.mutex.Unlock()
	
	serviceGroup, exists := hc.registry.services[serviceName]
	if !exists {
		return fmt.Errorf("服务不存在: %s", serviceName)
	}
	
	serviceGroup.mutex.Lock()
	defer serviceGroup.mutex.Unlock()
	
	serviceGroup.Config = config
	
	hc.logger.Info("更新健康检查配置", "service", serviceName, 
		"interval", config.HealthCheckInterval, "timeout", config.HealthCheckTimeout)
	
	return nil
}
