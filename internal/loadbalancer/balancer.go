package loadbalancer

import (
	"fmt"
	"hash/fnv"
	"math/rand"
	"net/http"
	"net/http/httputil"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"paas-platform/pkg/logger"
)

// LoadBalancer 负载均衡器
type LoadBalancer struct {
	registry    *ServiceRegistry
	logger      logger.Logger
	algorithms  map[string]Algorithm
	proxies     map[string]*httputil.ReverseProxy
	proxyMutex  sync.RWMutex
	stats       *LoadBalancerStats
}

// Algorithm 负载均衡算法接口
type Algorithm interface {
	SelectInstance(instances []*ServiceInstance, request *http.Request) (*ServiceInstance, error)
	GetName() string
}

// LoadBalancerStats 负载均衡统计信息
type LoadBalancerStats struct {
	TotalRequests    int64            `json:"total_requests"`
	SuccessRequests  int64            `json:"success_requests"`
	FailedRequests   int64            `json:"failed_requests"`
	ServiceStats     map[string]*ServiceStats `json:"service_stats"`
	mutex            sync.RWMutex
}

// ServiceStats 服务统计信息
type ServiceStats struct {
	TotalRequests   int64 `json:"total_requests"`
	SuccessRequests int64 `json:"success_requests"`
	FailedRequests  int64 `json:"failed_requests"`
	AvgResponseTime int64 `json:"avg_response_time"` // 毫秒
	LastRequestTime time.Time `json:"last_request_time"`
}

// NewLoadBalancer 创建负载均衡器
func NewLoadBalancer(registry *ServiceRegistry, logger logger.Logger) *LoadBalancer {
	lb := &LoadBalancer{
		registry:   registry,
		logger:     logger,
		algorithms: make(map[string]Algorithm),
		proxies:    make(map[string]*httputil.ReverseProxy),
		stats: &LoadBalancerStats{
			ServiceStats: make(map[string]*ServiceStats),
		},
	}

	// 注册负载均衡算法
	lb.registerAlgorithms()

	return lb
}

// registerAlgorithms 注册负载均衡算法
func (lb *LoadBalancer) registerAlgorithms() {
	lb.algorithms["round_robin"] = NewRoundRobinAlgorithm()
	lb.algorithms["weighted_round_robin"] = NewWeightedRoundRobinAlgorithm()
	lb.algorithms["least_connections"] = NewLeastConnectionsAlgorithm()
	lb.algorithms["ip_hash"] = NewIPHashAlgorithm()
	lb.algorithms["random"] = NewRandomAlgorithm()
}

// ProxyRequest 代理请求
func (lb *LoadBalancer) ProxyRequest(serviceName string, w http.ResponseWriter, r *http.Request) error {
	start := time.Now()
	
	// 获取健康的服务实例
	instances, err := lb.registry.GetHealthyInstances(serviceName)
	if err != nil {
		lb.recordFailedRequest(serviceName)
		return fmt.Errorf("获取服务实例失败: %w", err)
	}

	if len(instances) == 0 {
		lb.recordFailedRequest(serviceName)
		return fmt.Errorf("没有可用的健康实例: %s", serviceName)
	}

	// 获取服务配置
	serviceGroup, err := lb.registry.GetService(serviceName)
	if err != nil {
		lb.recordFailedRequest(serviceName)
		return fmt.Errorf("获取服务配置失败: %w", err)
	}

	// 选择负载均衡算法
	algorithm, exists := lb.algorithms[serviceGroup.Config.Algorithm]
	if !exists {
		algorithm = lb.algorithms["round_robin"] // 默认使用轮询
	}

	// 选择目标实例
	targetInstance, err := algorithm.SelectInstance(instances, r)
	if err != nil {
		lb.recordFailedRequest(serviceName)
		return fmt.Errorf("选择目标实例失败: %w", err)
	}

	// 获取或创建反向代理
	proxy, err := lb.getOrCreateProxy(targetInstance)
	if err != nil {
		lb.recordFailedRequest(serviceName)
		return fmt.Errorf("创建代理失败: %w", err)
	}

	// 设置请求头
	r.Header.Set("X-Forwarded-For", r.RemoteAddr)
	r.Header.Set("X-Forwarded-Proto", "http")
	r.Header.Set("X-Forwarded-Host", r.Host)

	// 代理请求
	proxy.ServeHTTP(w, r)

	// 记录统计信息
	duration := time.Since(start)
	lb.recordSuccessRequest(serviceName, duration)
	lb.registry.UpdateInstanceStats(serviceName, targetInstance.ID, 1, 0)

	lb.logger.Debug("代理请求成功", "service", serviceName, "instance", targetInstance.ID, 
		"target", fmt.Sprintf("%s:%d", targetInstance.Host, targetInstance.Port), 
		"duration", duration)

	return nil
}

// getOrCreateProxy 获取或创建反向代理
func (lb *LoadBalancer) getOrCreateProxy(instance *ServiceInstance) (*httputil.ReverseProxy, error) {
	proxyKey := fmt.Sprintf("%s:%d", instance.Host, instance.Port)
	
	lb.proxyMutex.RLock()
	proxy, exists := lb.proxies[proxyKey]
	lb.proxyMutex.RUnlock()

	if exists {
		return proxy, nil
	}

	// 创建新的反向代理
	targetURL, err := url.Parse(fmt.Sprintf("%s://%s:%d", instance.Protocol, instance.Host, instance.Port))
	if err != nil {
		return nil, fmt.Errorf("解析目标URL失败: %w", err)
	}

	proxy = httputil.NewSingleHostReverseProxy(targetURL)
	
	// 自定义错误处理
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		lb.logger.Error("代理请求失败", "target", targetURL.String(), "error", err)
		w.WriteHeader(http.StatusBadGateway)
		w.Write([]byte("Bad Gateway"))
	}

	// 缓存代理
	lb.proxyMutex.Lock()
	lb.proxies[proxyKey] = proxy
	lb.proxyMutex.Unlock()

	return proxy, nil
}

// recordSuccessRequest 记录成功请求
func (lb *LoadBalancer) recordSuccessRequest(serviceName string, duration time.Duration) {
	atomic.AddInt64(&lb.stats.TotalRequests, 1)
	atomic.AddInt64(&lb.stats.SuccessRequests, 1)

	lb.stats.mutex.Lock()
	defer lb.stats.mutex.Unlock()

	serviceStats, exists := lb.stats.ServiceStats[serviceName]
	if !exists {
		serviceStats = &ServiceStats{}
		lb.stats.ServiceStats[serviceName] = serviceStats
	}

	serviceStats.TotalRequests++
	serviceStats.SuccessRequests++
	serviceStats.LastRequestTime = time.Now()
	
	// 计算平均响应时间
	if serviceStats.TotalRequests == 1 {
		serviceStats.AvgResponseTime = duration.Milliseconds()
	} else {
		serviceStats.AvgResponseTime = (serviceStats.AvgResponseTime + duration.Milliseconds()) / 2
	}
}

// recordFailedRequest 记录失败请求
func (lb *LoadBalancer) recordFailedRequest(serviceName string) {
	atomic.AddInt64(&lb.stats.TotalRequests, 1)
	atomic.AddInt64(&lb.stats.FailedRequests, 1)

	lb.stats.mutex.Lock()
	defer lb.stats.mutex.Unlock()

	serviceStats, exists := lb.stats.ServiceStats[serviceName]
	if !exists {
		serviceStats = &ServiceStats{}
		lb.stats.ServiceStats[serviceName] = serviceStats
	}

	serviceStats.TotalRequests++
	serviceStats.FailedRequests++
	serviceStats.LastRequestTime = time.Now()
}

// GetStats 获取统计信息
func (lb *LoadBalancer) GetStats() *LoadBalancerStats {
	lb.stats.mutex.RLock()
	defer lb.stats.mutex.RUnlock()

	// 返回副本
	statsCopy := &LoadBalancerStats{
		TotalRequests:   atomic.LoadInt64(&lb.stats.TotalRequests),
		SuccessRequests: atomic.LoadInt64(&lb.stats.SuccessRequests),
		FailedRequests:  atomic.LoadInt64(&lb.stats.FailedRequests),
		ServiceStats:    make(map[string]*ServiceStats),
	}

	for serviceName, serviceStats := range lb.stats.ServiceStats {
		statsCopy.ServiceStats[serviceName] = &ServiceStats{
			TotalRequests:   serviceStats.TotalRequests,
			SuccessRequests: serviceStats.SuccessRequests,
			FailedRequests:  serviceStats.FailedRequests,
			AvgResponseTime: serviceStats.AvgResponseTime,
			LastRequestTime: serviceStats.LastRequestTime,
		}
	}

	return statsCopy
}

// 轮询算法
type RoundRobinAlgorithm struct {
	counters map[string]*int64
	mutex    sync.RWMutex
}

func NewRoundRobinAlgorithm() *RoundRobinAlgorithm {
	return &RoundRobinAlgorithm{
		counters: make(map[string]*int64),
	}
}

func (rr *RoundRobinAlgorithm) SelectInstance(instances []*ServiceInstance, request *http.Request) (*ServiceInstance, error) {
	if len(instances) == 0 {
		return nil, fmt.Errorf("没有可用实例")
	}

	serviceName := instances[0].Name
	
	rr.mutex.Lock()
	counter, exists := rr.counters[serviceName]
	if !exists {
		counter = new(int64)
		rr.counters[serviceName] = counter
	}
	rr.mutex.Unlock()

	index := atomic.AddInt64(counter, 1) % int64(len(instances))
	return instances[index], nil
}

func (rr *RoundRobinAlgorithm) GetName() string {
	return "round_robin"
}

// 加权轮询算法
type WeightedRoundRobinAlgorithm struct {
	counters map[string]*int64
	mutex    sync.RWMutex
}

func NewWeightedRoundRobinAlgorithm() *WeightedRoundRobinAlgorithm {
	return &WeightedRoundRobinAlgorithm{
		counters: make(map[string]*int64),
	}
}

func (wrr *WeightedRoundRobinAlgorithm) SelectInstance(instances []*ServiceInstance, request *http.Request) (*ServiceInstance, error) {
	if len(instances) == 0 {
		return nil, fmt.Errorf("没有可用实例")
	}

	// 构建加权实例列表
	var weightedInstances []*ServiceInstance
	for _, instance := range instances {
		weight := instance.Weight
		if weight <= 0 {
			weight = 1
		}
		for i := 0; i < weight; i++ {
			weightedInstances = append(weightedInstances, instance)
		}
	}

	serviceName := instances[0].Name
	
	wrr.mutex.Lock()
	counter, exists := wrr.counters[serviceName]
	if !exists {
		counter = new(int64)
		wrr.counters[serviceName] = counter
	}
	wrr.mutex.Unlock()

	index := atomic.AddInt64(counter, 1) % int64(len(weightedInstances))
	return weightedInstances[index], nil
}

func (wrr *WeightedRoundRobinAlgorithm) GetName() string {
	return "weighted_round_robin"
}

// 最少连接算法
type LeastConnectionsAlgorithm struct{}

func NewLeastConnectionsAlgorithm() *LeastConnectionsAlgorithm {
	return &LeastConnectionsAlgorithm{}
}

func (lc *LeastConnectionsAlgorithm) SelectInstance(instances []*ServiceInstance, request *http.Request) (*ServiceInstance, error) {
	if len(instances) == 0 {
		return nil, fmt.Errorf("没有可用实例")
	}

	// 简化实现：选择请求数最少的实例
	var selectedInstance *ServiceInstance
	minRequests := int64(-1)

	for _, instance := range instances {
		if minRequests == -1 || instance.RequestCount < minRequests {
			minRequests = instance.RequestCount
			selectedInstance = instance
		}
	}

	return selectedInstance, nil
}

func (lc *LeastConnectionsAlgorithm) GetName() string {
	return "least_connections"
}

// IP 哈希算法
type IPHashAlgorithm struct{}

func NewIPHashAlgorithm() *IPHashAlgorithm {
	return &IPHashAlgorithm{}
}

func (ih *IPHashAlgorithm) SelectInstance(instances []*ServiceInstance, request *http.Request) (*ServiceInstance, error) {
	if len(instances) == 0 {
		return nil, fmt.Errorf("没有可用实例")
	}

	// 获取客户端IP
	clientIP := request.RemoteAddr
	if forwarded := request.Header.Get("X-Forwarded-For"); forwarded != "" {
		clientIP = forwarded
	}

	// 计算哈希值
	hash := fnv.New32a()
	hash.Write([]byte(clientIP))
	index := hash.Sum32() % uint32(len(instances))

	return instances[index], nil
}

func (ih *IPHashAlgorithm) GetName() string {
	return "ip_hash"
}

// 随机算法
type RandomAlgorithm struct{}

func NewRandomAlgorithm() *RandomAlgorithm {
	return &RandomAlgorithm{}
}

func (ra *RandomAlgorithm) SelectInstance(instances []*ServiceInstance, request *http.Request) (*ServiceInstance, error) {
	if len(instances) == 0 {
		return nil, fmt.Errorf("没有可用实例")
	}

	index := rand.Intn(len(instances))
	return instances[index], nil
}

func (ra *RandomAlgorithm) GetName() string {
	return "random"
}
