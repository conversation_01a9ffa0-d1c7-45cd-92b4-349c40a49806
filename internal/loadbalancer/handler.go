package loadbalancer

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// Handler 负载均衡处理器
type Handler struct {
	registry      *ServiceRegistry
	loadBalancer  *LoadBalancer
	healthChecker *HealthChecker
	logger        logger.Logger
}

// NewHandler 创建负载均衡处理器
func NewHandler(registry *ServiceRegistry, loadBalancer *LoadBalancer, healthChecker *HealthChecker, logger logger.Logger) *Handler {
	return &Handler{
		registry:      registry,
		loadBalancer:  loadBalancer,
		healthChecker: healthChecker,
		logger:        logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 服务注册和发现路由
	router.POST("/services/register", h.RegisterService)
	router.DELETE("/services/:service/:instance", h.DeregisterService)
	router.GET("/services", h.ListServices)
	router.GET("/services/:service", h.GetService)
	router.GET("/services/:service/instances", h.GetServiceInstances)
	
	// 健康检查路由
	router.GET("/health/services", h.GetHealthStats)
	router.GET("/health/services/:service", h.CheckServiceHealth)
	router.GET("/health/services/:service/instances/:instance", h.CheckInstanceHealth)
	router.POST("/health/services/:service/config", h.SetHealthCheckConfig)
	
	// 负载均衡路由
	router.GET("/loadbalancer/stats", h.GetLoadBalancerStats)
	router.GET("/loadbalancer/algorithms", h.GetAlgorithms)
	router.POST("/loadbalancer/services/:service/config", h.SetLoadBalancerConfig)
	
	// 代理路由 (通配符路由，放在最后)
	router.Any("/proxy/:service/*path", h.ProxyRequest)
}

// RegisterService 注册服务
func (h *Handler) RegisterService(c *gin.Context) {
	var req RegistrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析服务注册请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.registry.RegisterService(&req); err != nil {
		h.logger.Error("注册服务失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "注册服务失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "服务注册成功",
		"data": map[string]interface{}{
			"service_name": req.ServiceName,
			"instance_id":  req.InstanceID,
			"endpoint":     req.Host + ":" + strconv.Itoa(req.Port),
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// DeregisterService 注销服务
func (h *Handler) DeregisterService(c *gin.Context) {
	serviceName := c.Param("service")
	instanceID := c.Param("instance")

	if err := h.registry.DeregisterService(serviceName, instanceID); err != nil {
		h.logger.Error("注销服务失败", "service", serviceName, "instance", instanceID, "error", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "注销服务失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "服务注销成功",
		"data": map[string]interface{}{
			"service_name": serviceName,
			"instance_id":  instanceID,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ListServices 列出所有服务
func (h *Handler) ListServices(c *gin.Context) {
	services := h.registry.ListServices()
	
	c.JSON(http.StatusOK, gin.H{
		"message":   "获取服务列表成功",
		"data":      services,
		"count":     len(services),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetService 获取服务详情
func (h *Handler) GetService(c *gin.Context) {
	serviceName := c.Param("service")

	service, err := h.registry.GetService(serviceName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "服务不存在",
			"service": serviceName,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "获取服务详情成功",
		"data":      service,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetServiceInstances 获取服务实例
func (h *Handler) GetServiceInstances(c *gin.Context) {
	serviceName := c.Param("service")
	healthyOnly := c.DefaultQuery("healthy_only", "false") == "true"

	var instances []*ServiceInstance
	var err error

	if healthyOnly {
		instances, err = h.registry.GetHealthyInstances(serviceName)
	} else {
		service, serviceErr := h.registry.GetService(serviceName)
		if serviceErr != nil {
			err = serviceErr
		} else {
			instances = service.Instances
		}
	}

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取服务实例失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":     "获取服务实例成功",
		"data":        instances,
		"count":       len(instances),
		"healthy_only": healthyOnly,
		"timestamp":   time.Now().Format(time.RFC3339),
	})
}

// GetHealthStats 获取健康检查统计
func (h *Handler) GetHealthStats(c *gin.Context) {
	stats := h.healthChecker.GetHealthStats()
	
	c.JSON(http.StatusOK, gin.H{
		"message":   "获取健康检查统计成功",
		"data":      stats,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// CheckServiceHealth 检查服务健康状态
func (h *Handler) CheckServiceHealth(c *gin.Context) {
	serviceName := c.Param("service")

	results, err := h.healthChecker.CheckServiceHealth(serviceName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "检查服务健康状态失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "检查服务健康状态成功",
		"data":      results,
		"service":   serviceName,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// CheckInstanceHealth 检查实例健康状态
func (h *Handler) CheckInstanceHealth(c *gin.Context) {
	serviceName := c.Param("service")
	instanceID := c.Param("instance")

	result, err := h.healthChecker.CheckInstanceNow(serviceName, instanceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "检查实例健康状态失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "检查实例健康状态成功",
		"data":      result,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// SetHealthCheckConfigRequest 设置健康检查配置请求
type SetHealthCheckConfigRequest struct {
	Enabled  bool   `json:"enabled"`
	Interval string `json:"interval"`
	Timeout  string `json:"timeout"`
	Path     string `json:"path"`
}

// SetHealthCheckConfig 设置健康检查配置
func (h *Handler) SetHealthCheckConfig(c *gin.Context) {
	serviceName := c.Param("service")

	var req SetHealthCheckConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 解析时间间隔
	interval, err := time.ParseDuration(req.Interval)
	if err != nil {
		interval = 30 * time.Second
	}

	timeout, err := time.ParseDuration(req.Timeout)
	if err != nil {
		timeout = 5 * time.Second
	}

	config := &LoadBalancerConfig{
		HealthCheckEnabled:  req.Enabled,
		HealthCheckInterval: interval,
		HealthCheckTimeout:  timeout,
		HealthCheckPath:     req.Path,
	}

	if err := h.healthChecker.SetHealthCheckConfig(serviceName, config); err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "设置健康检查配置失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "设置健康检查配置成功",
		"data":    config,
		"service": serviceName,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetLoadBalancerStats 获取负载均衡统计
func (h *Handler) GetLoadBalancerStats(c *gin.Context) {
	stats := h.loadBalancer.GetStats()
	
	c.JSON(http.StatusOK, gin.H{
		"message":   "获取负载均衡统计成功",
		"data":      stats,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetAlgorithms 获取可用的负载均衡算法
func (h *Handler) GetAlgorithms(c *gin.Context) {
	algorithms := []string{
		"round_robin",
		"weighted_round_robin",
		"least_connections",
		"ip_hash",
		"random",
	}
	
	c.JSON(http.StatusOK, gin.H{
		"message":    "获取负载均衡算法成功",
		"data":       algorithms,
		"count":      len(algorithms),
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// SetLoadBalancerConfigRequest 设置负载均衡配置请求
type SetLoadBalancerConfigRequest struct {
	Algorithm      string `json:"algorithm"`
	MaxRetries     int    `json:"max_retries"`
	RetryTimeout   string `json:"retry_timeout"`
	SessionSticky  bool   `json:"session_sticky"`
	CircuitBreaker bool   `json:"circuit_breaker"`
}

// SetLoadBalancerConfig 设置负载均衡配置
func (h *Handler) SetLoadBalancerConfig(c *gin.Context) {
	serviceName := c.Param("service")

	var req SetLoadBalancerConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 解析重试超时
	retryTimeout, err := time.ParseDuration(req.RetryTimeout)
	if err != nil {
		retryTimeout = 1 * time.Second
	}

	// 获取现有服务配置
	service, err := h.registry.GetService(serviceName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "服务不存在",
			"service": serviceName,
		})
		return
	}

	// 更新配置
	config := service.Config
	config.Algorithm = req.Algorithm
	config.MaxRetries = req.MaxRetries
	config.RetryTimeout = retryTimeout
	config.SessionSticky = req.SessionSticky
	config.CircuitBreaker = req.CircuitBreaker

	c.JSON(http.StatusOK, gin.H{
		"message":   "设置负载均衡配置成功",
		"data":      config,
		"service":   serviceName,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ProxyRequest 代理请求
func (h *Handler) ProxyRequest(c *gin.Context) {
	serviceName := c.Param("service")
	
	// 记录请求开始时间
	start := time.Now()
	
	// 代理请求
	if err := h.loadBalancer.ProxyRequest(serviceName, c.Writer, c.Request); err != nil {
		h.logger.Error("代理请求失败", "service", serviceName, "error", err, 
			"duration", time.Since(start))
		
		c.JSON(http.StatusBadGateway, gin.H{
			"error":   "代理请求失败",
			"service": serviceName,
			"details": err.Error(),
		})
		return
	}
	
	h.logger.Debug("代理请求成功", "service", serviceName, "duration", time.Since(start))
}
