package auth

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockLogger 模拟日志记录器
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Info(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Error(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Warn(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Debug(msg string, args ...interface{}) {
	m.Called(msg, args)
}

// setupTestDB 设置测试数据库
func setupTestDB() (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// 自动迁移测试表
	err = db.AutoMigrate(
		&Tenant{},
		&User{},
		&IDPProvider{},
		&IDPAccount{},
		&IDPSession{},
		&IDPAuditLog{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}

// createTestTenant 创建测试租户
func createTestTenant(db *gorm.DB) *Tenant {
	tenant := &Tenant{
		ID:     "test-tenant-001",
		Name:   "测试租户",
		Status: TenantStatusActive,
	}
	db.Create(tenant)
	return tenant
}

// createTestUser 创建测试用户
func createTestUser(db *gorm.DB, tenantID string) *User {
	user := &User{
		ID:       "test-user-001",
		TenantID: tenantID,
		Username: "testuser",
		Email:    "<EMAIL>",
		Status:   UserStatusActive,
	}
	db.Create(user)
	return user
}

// TestIDPService_CreateIDPProvider 测试创建 IDP 提供商
func TestIDPService_CreateIDPProvider(t *testing.T) {
	db, err := setupTestDB()
	assert.NoError(t, err)

	logger := &MockLogger{}
	service := NewIDPService(db, logger)

	tenant := createTestTenant(db)

	tests := []struct {
		name    string
		request *CreateIDPProviderRequest
		wantErr bool
	}{
		{
			name: "创建 OIDC 提供商成功",
			request: &CreateIDPProviderRequest{
				Name:     "test-oidc",
				Type:     IDPProviderOIDC,
				Enabled:  true,
				TenantID: tenant.ID,
				Config: IDPConfig{
					ClientID:     "test-client-id",
					ClientSecret: "test-client-secret",
					AuthURL:      "https://example.com/auth",
					TokenURL:     "https://example.com/token",
				},
				Mapping: IDPAttributeMapping{
					Email: "email",
				},
				Priority:       1,
				AutoCreateUser: true,
				AutoLinkUser:   false,
			},
			wantErr: false,
		},
		{
			name: "缺少必要配置失败",
			request: &CreateIDPProviderRequest{
				Name:     "test-invalid",
				Type:     IDPProviderOIDC,
				TenantID: tenant.ID,
				Config: IDPConfig{
					// 缺少 ClientID
					ClientSecret: "test-client-secret",
				},
				Mapping: IDPAttributeMapping{
					Email: "email",
				},
			},
			wantErr: true,
		},
		{
			name: "重复名称失败",
			request: &CreateIDPProviderRequest{
				Name:     "test-oidc", // 与第一个测试用例重复
				Type:     IDPProviderOIDC,
				TenantID: tenant.ID,
				Config: IDPConfig{
					ClientID:     "test-client-id-2",
					ClientSecret: "test-client-secret-2",
					AuthURL:      "https://example2.com/auth",
					TokenURL:     "https://example2.com/token",
				},
				Mapping: IDPAttributeMapping{
					Email: "email",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			provider, err := service.CreateIDPProvider(context.Background(), tt.request)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, provider)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, provider)
				assert.Equal(t, tt.request.Name, provider.Name)
				assert.Equal(t, tt.request.Type, provider.Type)
				assert.Equal(t, tt.request.TenantID, provider.TenantID)
			}
		})
	}
}

// TestIDPService_LinkIDPAccount 测试关联 IDP 账号
func TestIDPService_LinkIDPAccount(t *testing.T) {
	db, err := setupTestDB()
	assert.NoError(t, err)

	logger := &MockLogger{}
	service := NewIDPService(db, logger)

	tenant := createTestTenant(db)
	user := createTestUser(db, tenant.ID)

	// 创建测试 IDP 提供商
	provider := &IDPProvider{
		ID:       "test-provider-001",
		Name:     "test-provider",
		Type:     IDPProviderOIDC,
		Enabled:  true,
		TenantID: tenant.ID,
	}
	db.Create(provider)

	tests := []struct {
		name        string
		userID      string
		providerID  string
		idpUserID   string
		wantErr     bool
		expectedErr string
	}{
		{
			name:       "关联账号成功",
			userID:     user.ID,
			providerID: provider.ID,
			idpUserID:  "idp-user-001",
			wantErr:    false,
		},
		{
			name:        "用户不存在",
			userID:      "non-existent-user",
			providerID:  provider.ID,
			idpUserID:   "idp-user-002",
			wantErr:     true,
			expectedErr: "用户不存在",
		},
		{
			name:        "IDP 提供商不存在",
			userID:      user.ID,
			providerID:  "non-existent-provider",
			idpUserID:   "idp-user-003",
			wantErr:     true,
			expectedErr: "IDP 提供商不存在",
		},
		{
			name:        "重复关联同一提供商",
			userID:      user.ID,
			providerID:  provider.ID,
			idpUserID:   "idp-user-004",
			wantErr:     true,
			expectedErr: "用户已关联此 IDP 提供商",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			account, err := service.LinkIDPAccount(context.Background(), tt.userID, tt.providerID, tt.idpUserID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, account)
				if tt.expectedErr != "" {
					assert.Contains(t, err.Error(), tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, account)
				assert.Equal(t, tt.userID, account.UserID)
				assert.Equal(t, tt.providerID, account.IDPProviderID)
				assert.Equal(t, tt.idpUserID, account.IDPUserID)
				assert.Equal(t, IDPAccountStatusActive, account.Status)
			}
		})
	}
}

// TestIDPService_GetIDPProvider 测试获取 IDP 提供商
func TestIDPService_GetIDPProvider(t *testing.T) {
	db, err := setupTestDB()
	assert.NoError(t, err)

	logger := &MockLogger{}
	service := NewIDPService(db, logger)

	tenant := createTestTenant(db)

	// 创建测试 IDP 提供商
	provider := &IDPProvider{
		ID:       "test-provider-001",
		Name:     "test-provider",
		Type:     IDPProviderOIDC,
		Enabled:  true,
		TenantID: tenant.ID,
	}
	db.Create(provider)

	tests := []struct {
		name       string
		providerID string
		wantErr    bool
	}{
		{
			name:       "获取存在的提供商",
			providerID: provider.ID,
			wantErr:    false,
		},
		{
			name:       "获取不存在的提供商",
			providerID: "non-existent-provider",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.GetIDPProvider(context.Background(), tt.providerID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, provider.ID, result.ID)
				assert.Equal(t, provider.Name, result.Name)
				assert.Equal(t, provider.Type, result.Type)
			}
		})
	}
}

// TestIDPService_ListIDPProviders 测试获取 IDP 提供商列表
func TestIDPService_ListIDPProviders(t *testing.T) {
	db, err := setupTestDB()
	assert.NoError(t, err)

	logger := &MockLogger{}
	service := NewIDPService(db, logger)

	tenant := createTestTenant(db)

	// 创建多个测试 IDP 提供商
	providers := []*IDPProvider{
		{
			ID:       "provider-001",
			Name:     "provider-1",
			Type:     IDPProviderOIDC,
			Enabled:  true,
			TenantID: tenant.ID,
			Priority: 1,
		},
		{
			ID:       "provider-002",
			Name:     "provider-2",
			Type:     IDPProviderOAuth2,
			Enabled:  true,
			TenantID: tenant.ID,
			Priority: 2,
		},
		{
			ID:       "provider-003",
			Name:     "provider-3",
			Type:     IDPProviderSAML,
			Enabled:  false,
			TenantID: tenant.ID,
			Priority: 3,
		},
	}

	for _, provider := range providers {
		db.Create(provider)
	}

	result, err := service.ListIDPProviders(context.Background(), tenant.ID)

	assert.NoError(t, err)
	assert.Len(t, result, 3)

	// 验证按优先级排序
	assert.Equal(t, "provider-1", result[0].Name)
	assert.Equal(t, "provider-2", result[1].Name)
	assert.Equal(t, "provider-3", result[2].Name)
}

// TestIDPService_LogIDPEvent 测试记录 IDP 事件
func TestIDPService_LogIDPEvent(t *testing.T) {
	db, err := setupTestDB()
	assert.NoError(t, err)

	logger := &MockLogger{}
	service := NewIDPService(db, logger)

	event := &IDPAuditLog{
		EventType:     "test_event",
		IDPProviderID: "test-provider-001",
		Result:        "success",
		IPAddress:     "127.0.0.1",
		UserAgent:     "test-agent",
		Details: map[string]interface{}{
			"test_key": "test_value",
		},
	}

	err = service.LogIDPEvent(context.Background(), event)
	assert.NoError(t, err)

	// 验证事件已保存
	var savedEvent IDPAuditLog
	err = db.First(&savedEvent, "event_type = ?", "test_event").Error
	assert.NoError(t, err)
	assert.Equal(t, event.EventType, savedEvent.EventType)
	assert.Equal(t, event.IDPProviderID, savedEvent.IDPProviderID)
	assert.Equal(t, event.Result, savedEvent.Result)
}
