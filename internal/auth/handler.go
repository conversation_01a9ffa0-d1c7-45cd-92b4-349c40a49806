package auth

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

// Handler 认证 HTTP 处理器
type Handler struct {
	service Service
	logger  Logger
}

// NewHandler 创建认证处理器
func NewHandler(service Service, logger Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 认证相关路由 (无需认证)
	auth := router.Group("/auth")
	{
		auth.POST("/login", h.<PERSON><PERSON>)                    // 用户登录
		auth.POST("/logout", h.<PERSON>gout)                  // 用户登出
		auth.POST("/refresh", h.RefreshToken)           // 刷新令牌
		auth.POST("/reset-password", h.ResetPassword)   // 重置密码
		auth.POST("/confirm-reset", h.ConfirmResetPassword) // 确认重置密码
	}
	
	// 用户管理路由 (需要认证)
	users := router.Group("/users")
	{
		users.POST("", h.CreateUser)                    // 创建用户
		users.GET("", h.ListUsers)                      // 获取用户列表
		users.GET("/:id", h.GetUser)                    // 获取用户详情
		users.PUT("/:id", h.UpdateUser)                 // 更新用户
		users.DELETE("/:id", h.DeleteUser)              // 删除用户
		users.POST("/:id/change-password", h.ChangePassword) // 修改密码
		users.GET("/:id/roles", h.GetUserRoles)         // 获取用户角色
		users.POST("/:id/roles", h.AssignRole)          // 分配角色
		users.DELETE("/:id/roles/:role_id", h.UnassignRole) // 取消角色
		users.GET("/:id/permissions", h.GetUserPermissions) // 获取用户权限
		users.GET("/:id/sessions", h.GetUserSessions)   // 获取用户会话
		users.DELETE("/:id/sessions", h.DeleteUserSessions) // 删除用户会话
	}
	
	// 租户管理路由
	tenants := router.Group("/tenants")
	{
		tenants.POST("", h.CreateTenant)                // 创建租户
		tenants.GET("", h.ListTenants)                  // 获取租户列表
		tenants.GET("/:id", h.GetTenant)                // 获取租户详情
		tenants.PUT("/:id", h.UpdateTenant)             // 更新租户
		tenants.GET("/:id/users", h.GetTenantUsers)     // 获取租户用户
		tenants.GET("/:id/stats", h.GetTenantStats)     // 获取租户统计
	}
	
	// 角色管理路由
	roles := router.Group("/roles")
	{
		roles.POST("", h.CreateRole)                    // 创建角色
		roles.GET("", h.ListRoles)                      // 获取角色列表
		roles.GET("/:id", h.GetRole)                    // 获取角色详情
		roles.PUT("/:id", h.UpdateRole)                 // 更新角色
		roles.DELETE("/:id", h.DeleteRole)              // 删除角色
		roles.GET("/:id/users", h.GetRoleUsers)         // 获取角色用户
	}
	
	// 权限管理路由
	permissions := router.Group("/permissions")
	{
		permissions.GET("", h.ListPermissions)          // 获取权限列表
		permissions.POST("/check", h.CheckPermission)   // 检查权限
	}
	
	// 会话管理路由
	sessions := router.Group("/sessions")
	{
		sessions.GET("", h.ListSessions)                // 获取会话列表
		sessions.GET("/:id", h.GetSession)              // 获取会话详情
		sessions.DELETE("/:id", h.DeleteSession)        // 删除会话
	}
	
	// 审计日志路由
	audit := router.Group("/audit")
	{
		audit.GET("/logs", h.GetAuditLogs)              // 获取审计日志
		audit.GET("/stats", h.GetAuditStats)           // 获取审计统计
		audit.GET("/security-report", h.GetSecurityReport) // 获取安全报告
	}
	
	// MFA 管理路由
	mfa := router.Group("/mfa")
	{
		mfa.POST("/setup", h.SetupMFA)                  // 设置 MFA
		mfa.POST("/verify", h.VerifyMFA)                // 验证 MFA
		mfa.GET("/devices", h.ListMFADevices)           // 获取 MFA 设备
		mfa.DELETE("/devices/:id", h.DeleteMFADevice)   // 删除 MFA 设备
	}
	
	// API 密钥管理路由
	apiKeys := router.Group("/api-keys")
	{
		apiKeys.POST("", h.CreateAPIKey)                // 创建 API 密钥
		apiKeys.GET("", h.ListAPIKeys)                  // 获取 API 密钥列表
		apiKeys.DELETE("/:id", h.DeleteAPIKey)          // 删除 API 密钥
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户使用用户名和密码登录系统
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录请求"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定登录请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理：如果启用了开发模式，提供简化的登录流程
	if h.isDevMode() {
		h.logger.Info("开发模式登录", "username", req.Username)
		response := h.createDevModeLoginResponse(&req)
		c.JSON(http.StatusOK, response)
		return
	}
	
	// 设置客户端信息
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")
	
	response, err := h.service.Login(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("用户登录失败", "error", err, "username", req.Username, "tenant_id", req.TenantID)
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "LOGIN_FAILED",
			Message:   "登录失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 设置刷新令牌到 Cookie (HttpOnly, Secure)
	c.SetCookie(
		"refresh_token",
		response.TokenPair.RefreshToken,
		int(time.Hour*24*7/time.Second), // 7天
		"/",
		"",
		true,  // Secure
		true,  // HttpOnly
	)
	
	h.logger.Info("用户登录成功", "user_id", response.User.ID, "username", response.User.Username)
	c.JSON(http.StatusOK, response)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出系统，撤销令牌和会话
// @Tags 认证
// @Produce json
// @Success 200 {object} SuccessResponse "登出成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/logout [post]
func (h *Handler) Logout(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	sessionID, _ := c.Get("session_id")
	sessionIDStr := ""
	if sessionID != nil {
		sessionIDStr = sessionID.(string)
	}
	
	err := h.service.Logout(c.Request.Context(), userID.(string), sessionIDStr)
	if err != nil {
		h.logger.Error("用户登出失败", "error", err, "user_id", userID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "LOGOUT_FAILED",
			Message:   "登出失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 清除刷新令牌 Cookie
	c.SetCookie("refresh_token", "", -1, "/", "", true, true)
	
	h.logger.Info("用户登出成功", "user_id", userID)
	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "登出成功",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户账户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body CreateUserRequest true "创建用户请求"
// @Success 201 {object} User "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 409 {object} ErrorResponse "用户已存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users [post]
func (h *Handler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定创建用户请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 设置创建者
	createdBy, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	req.CreatedBy = createdBy.(string)
	
	// 设置租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	req.TenantID = tenantID.(string)
	
	user, err := h.service.CreateUser(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建用户失败", "error", err, "username", req.Username)
		
		statusCode := http.StatusInternalServerError
		errorCode := "CREATE_USER_FAILED"
		
		// 根据错误类型设置不同的状态码
		if contains([]string{"用户名", "邮箱"}, err.Error()) {
			statusCode = http.StatusConflict
			errorCode = "USER_ALREADY_EXISTS"
		}
		
		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "创建用户失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	h.logger.Info("用户创建成功", "user_id", user.ID, "username", user.Username)
	c.JSON(http.StatusCreated, user)
}

// GetUser 获取用户详情
// @Summary 获取用户详情
// @Description 根据用户ID获取用户详细信息
// @Tags 用户管理
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} UserResponse "获取成功"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id} [get]
func (h *Handler) GetUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	user, err := h.service.GetUser(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取用户失败", "error", err, "user_id", userID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:      "USER_NOT_FOUND",
			Message:   "用户不存在",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 获取用户角色和权限
	roles, _ := h.service.GetUserRoles(c.Request.Context(), userID)
	permissions, _ := h.service.GetUserPermissions(c.Request.Context(), userID)
	
	response := &UserResponse{
		User:        user,
		Roles:       roles,
		Permissions: permissions,
		LastLogin:   user.LastLoginAt,
	}
	
	c.JSON(http.StatusOK, response)
}

// ListUsers 获取用户列表
// @Summary 获取用户列表
// @Description 获取租户内的用户列表
// @Tags 用户管理
// @Produce json
// @Param username query string false "用户名过滤"
// @Param email query string false "邮箱过滤"
// @Param status query string false "状态过滤"
// @Param role_id query string false "角色过滤"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} UserListResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users [get]
func (h *Handler) ListUsers(c *gin.Context) {
	// 获取租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 解析查询参数
	filter := &UserFilter{
		Username: c.Query("username"),
		Email:    c.Query("email"),
		RoleID:   c.Query("role_id"),
		Page:     1,
		PageSize: 20,
	}
	
	if status := c.Query("status"); status != "" {
		filter.Status = UserStatus(status)
	}
	
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}
	
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}
	
	users, total, err := h.service.ListUsers(c.Request.Context(), tenantID.(string), filter)
	if err != nil {
		h.logger.Error("获取用户列表失败", "error", err, "tenant_id", tenantID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "LIST_USERS_FAILED",
			Message:   "获取用户列表失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 转换为响应格式
	userResponses := make([]*UserResponse, len(users))
	for i, user := range users {
		roles, _ := h.service.GetUserRoles(c.Request.Context(), user.ID)
		permissions, _ := h.service.GetUserPermissions(c.Request.Context(), user.ID)
		
		userResponses[i] = &UserResponse{
			User:        user,
			Roles:       roles,
			Permissions: permissions,
			LastLogin:   user.LastLoginAt,
		}
	}
	
	response := &UserListResponse{
		Users: userResponses,
		Total: total,
		Page:  filter.Page,
		Size:  len(userResponses),
	}
	
	c.JSON(http.StatusOK, response)
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 用户修改自己的密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Param request body ChangePasswordRequest true "修改密码请求"
// @Success 200 {object} SuccessResponse "修改成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "当前密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/change-password [post]
func (h *Handler) ChangePassword(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 检查权限 (只能修改自己的密码或管理员权限)
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	if currentUserID.(string) != userID {
		// TODO: 检查是否有管理员权限
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:      "FORBIDDEN",
			Message:   "权限不足",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定修改密码请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	err := h.service.ChangePassword(c.Request.Context(), userID, &req)
	if err != nil {
		h.logger.Error("修改密码失败", "error", err, "user_id", userID)
		
		statusCode := http.StatusInternalServerError
		errorCode := "CHANGE_PASSWORD_FAILED"
		
		// 根据错误类型设置不同的状态码
		if strings.Contains(err.Error(), "当前密码错误") {
			statusCode = http.StatusUnauthorized
			errorCode = "INVALID_CURRENT_PASSWORD"
		} else if strings.Contains(err.Error(), "密码强度") {
			statusCode = http.StatusBadRequest
			errorCode = "WEAK_PASSWORD"
		}
		
		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "修改密码失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	h.logger.Info("密码修改成功", "user_id", userID)
	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "密码修改成功",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags 认证
// @Produce json
// @Success 200 {object} TokenPair "刷新成功"
// @Failure 401 {object} ErrorResponse "刷新令牌无效"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/refresh [post]
func (h *Handler) RefreshToken(c *gin.Context) {
	// 从 Cookie 获取刷新令牌
	refreshToken, err := c.Cookie("refresh_token")
	if err != nil {
		// 尝试从请求体获取
		var req struct {
			RefreshToken string `json:"refresh_token"`
		}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Code:      "MISSING_REFRESH_TOKEN",
				Message:   "缺少刷新令牌",
				Timestamp: time.Now().Format(time.RFC3339),
			})
			return
		}
		refreshToken = req.RefreshToken
	}
	
	tokenPair, err := h.service.RefreshToken(c.Request.Context(), refreshToken)
	if err != nil {
		h.logger.Error("刷新令牌失败", "error", err)
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "REFRESH_TOKEN_FAILED",
			Message:   "刷新令牌失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 更新刷新令牌 Cookie
	c.SetCookie(
		"refresh_token",
		tokenPair.RefreshToken,
		int(time.Hour*24*7/time.Second),
		"/",
		"",
		true,
		true,
	)
	
	h.logger.Info("令牌刷新成功")
	c.JSON(http.StatusOK, tokenPair)
}

// CheckPermission 检查权限
// @Summary 检查权限
// @Description 检查用户是否具有指定权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param request body PermissionCheckRequest true "权限检查请求"
// @Success 200 {object} PermissionCheckResponse "检查成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/permissions/check [post]
func (h *Handler) CheckPermission(c *gin.Context) {
	var req PermissionCheckRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定权限检查请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 构建权限字符串
	permission := fmt.Sprintf("%s:%s", req.Resource, req.Action)
	if req.Scope != "" {
		permission += ":" + req.Scope
	}
	
	allowed, err := h.service.HasPermission(c.Request.Context(), req.UserID, permission)
	if err != nil {
		h.logger.Error("权限检查失败", "error", err, "user_id", req.UserID, "permission", permission)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "PERMISSION_CHECK_FAILED",
			Message:   "权限检查失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	response := &PermissionCheckResponse{
		Allowed: allowed,
	}
	
	if !allowed {
		response.Reason = "权限不足"
	}
	
	c.JSON(http.StatusOK, response)
}

// 🔧 开发模式辅助方法

// isDevMode 检查是否为开发模式
func (h *Handler) isDevMode() bool {
	// 优先检查环境变量
	if envDevMode := os.Getenv("PAAS_DEV_MODE"); envDevMode != "" {
		return strings.ToLower(envDevMode) == "true"
	}

	// 检查配置文件
	if viper.IsSet("security.jwt.dev_mode") {
		return viper.GetBool("security.jwt.dev_mode")
	}

	// 检查认证是否禁用
	if envAuthEnabled := os.Getenv("PAAS_AUTH_ENABLED"); envAuthEnabled != "" {
		return strings.ToLower(envAuthEnabled) == "false"
	}

	if viper.IsSet("security.jwt.enabled") {
		return !viper.GetBool("security.jwt.enabled")
	}

	// 检查环境标识
	env := strings.ToLower(os.Getenv("ENV"))
	nodeEnv := strings.ToLower(os.Getenv("NODE_ENV"))
	goEnv := strings.ToLower(os.Getenv("GO_ENV"))

	devEnvs := []string{"development", "dev", "debug", "local"}
	for _, devEnv := range devEnvs {
		if env == devEnv || nodeEnv == devEnv || goEnv == devEnv {
			return true
		}
	}

	return false
}

// createDevModeLoginResponse 创建开发模式登录响应
func (h *Handler) createDevModeLoginResponse(req *LoginRequest) *LoginResponse {
	// 🧑‍💻 创建开发模式用户
	devUser := &User{
		ID:        getEnvOrDefault("PAAS_DEV_USER_ID", "dev-user-001"),
		TenantID:  getEnvOrDefault("PAAS_DEV_USER_TENANT_ID", "dev-tenant-001"),
		Username:  getEnvOrDefault("PAAS_DEV_USER_USERNAME", "开发者"),
		Email:     getEnvOrDefault("PAAS_DEV_USER_EMAIL", "developer@localhost"),
		FirstName: "开发",
		LastName:  "用户",
		Status:    UserStatusActive,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 🔑 创建开发模式令牌
	devToken := getEnvOrDefault("PAAS_DEV_TOKEN", "dev-token-2024-secure")

	// 🎭 创建开发模式角色
	roles := []string{"admin", "developer"}
	if envRoles := os.Getenv("PAAS_DEV_USER_ROLES"); envRoles != "" {
		roles = strings.Split(envRoles, ",")
	}

	userRoles := make([]*Role, len(roles))
	for i, roleName := range roles {
		userRoles[i] = &Role{
			ID:          fmt.Sprintf("dev-role-%d", i+1),
			Name:        strings.TrimSpace(roleName),
			Description: fmt.Sprintf("开发环境%s角色", strings.TrimSpace(roleName)),
			Permissions: []string{"*:*"},
		}
	}
	// 注意：User 结构体没有直接的 Roles 字段，这里只是为了开发模式的演示
	// 实际应该通过 UserRoles 关联来处理

	// 🔐 创建令牌对
	tokenPair := &TokenPair{
		AccessToken:  devToken,
		RefreshToken: devToken + "-refresh",
		TokenType:    "Bearer",
		ExpiresIn:    86400, // 24小时
	}

	// 📝 记录开发模式登录
	h.logger.Info("开发模式登录成功",
		"username", req.Username,
		"dev_user_id", devUser.ID,
		"dev_user_roles", roles,
		"client_ip", req.IPAddress)

	return &LoginResponse{
		User:        devUser,
		TokenPair:   tokenPair,
		SessionID:   "dev-session-" + devUser.ID,
		ExpiresIn:   86400,
		Permissions: []string{"*"}, // 开发模式拥有所有权限
	}
}

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(envKey, defaultValue string) string {
	if value := os.Getenv(envKey); value != "" {
		return value
	}
	return defaultValue
}

// ResetPassword 重置密码请求
// @Summary 重置密码
// @Description 用户请求重置密码，系统将发送重置链接到用户邮箱
// @Tags 认证管理
// @Accept json
// @Produce json
// @Param request body ResetPasswordRequest true "重置密码请求"
// @Success 200 {object} SuccessResponse "请求成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/reset-password [post]
func (h *Handler) ResetPassword(c *gin.Context) {
	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定重置密码请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：密码重置请求", "email", req.Email, "tenant_id", req.TenantID)
		c.JSON(http.StatusOK, SuccessResponse{
			Message:   "开发模式：密码重置邮件已发送（模拟）",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层处理重置密码请求
	if err := h.service.ResetPassword(c.Request.Context(), &req); err != nil {
		h.logger.Error("重置密码请求失败", "error", err, "email", req.Email)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "RESET_PASSWORD_FAILED",
			Message:   "重置密码请求失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "密码重置邮件已发送，请检查您的邮箱",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// ConfirmResetPassword 确认重置密码
// @Summary 确认重置密码
// @Description 使用重置令牌确认并设置新密码
// @Tags 认证管理
// @Accept json
// @Produce json
// @Param request body ConfirmResetPasswordRequest true "确认重置密码请求"
// @Success 200 {object} SuccessResponse "重置成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "令牌无效"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/confirm-reset [post]
func (h *Handler) ConfirmResetPassword(c *gin.Context) {
	var req ConfirmResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定确认重置密码请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：确认重置密码", "token", req.Token)
		c.JSON(http.StatusOK, SuccessResponse{
			Message:   "开发模式：密码重置成功",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层处理确认重置密码
	if err := h.service.ConfirmResetPassword(c.Request.Context(), &req); err != nil {
		h.logger.Error("确认重置密码失败", "error", err, "token", req.Token)

		// 根据错误类型返回不同的状态码
		statusCode := http.StatusInternalServerError
		errorCode := "CONFIRM_RESET_FAILED"
		if strings.Contains(err.Error(), "令牌无效") || strings.Contains(err.Error(), "已过期") {
			statusCode = http.StatusUnauthorized
			errorCode = "INVALID_TOKEN"
		} else if strings.Contains(err.Error(), "验证失败") || strings.Contains(err.Error(), "强度不足") {
			statusCode = http.StatusBadRequest
			errorCode = "INVALID_PASSWORD"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "确认重置密码失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "密码重置成功，请使用新密码登录",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// UpdateUser 更新用户信息
// @Summary 更新用户信息
// @Description 更新指定用户的基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Param request body UpdateUserRequest true "更新用户请求"
// @Success 200 {object} UserResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id} [put]
func (h *Handler) UpdateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定更新用户请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：更新用户", "user_id", userID)
		c.JSON(http.StatusOK, &UserResponse{
			User: &User{
				ID:        userID,
				Username:  "开发用户",
				Email:     "dev@localhost",
				FirstName: "开发",
				LastName:  "用户",
				Status:    UserStatusActive,
				UpdatedAt: time.Now(),
			},
			Roles:       []*Role{},
			Permissions: []string{},
		})
		return
	}

	// 调用服务层更新用户
	user, err := h.service.UpdateUser(c.Request.Context(), userID, &req)
	if err != nil {
		h.logger.Error("更新用户失败", "error", err, "user_id", userID)

		statusCode := http.StatusInternalServerError
		errorCode := "UPDATE_USER_FAILED"
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
			errorCode = "USER_NOT_FOUND"
		} else if strings.Contains(err.Error(), "已被使用") {
			statusCode = http.StatusConflict
			errorCode = "EMAIL_ALREADY_EXISTS"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "更新用户失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 获取用户角色和权限
	roles, _ := h.service.GetUserRoles(c.Request.Context(), user.ID)
	permissions, _ := h.service.GetUserPermissions(c.Request.Context(), user.ID)

	response := &UserResponse{
		User:        user,
		Roles:       roles,
		Permissions: permissions,
		LastLogin:   user.LastLoginAt,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除指定用户及其相关数据
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id} [delete]
func (h *Handler) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：删除用户", "user_id", userID)
		c.JSON(http.StatusOK, SuccessResponse{
			Message:   "开发模式：用户删除成功",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层删除用户
	if err := h.service.DeleteUser(c.Request.Context(), userID); err != nil {
		h.logger.Error("删除用户失败", "error", err, "user_id", userID)

		statusCode := http.StatusInternalServerError
		errorCode := "DELETE_USER_FAILED"
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
			errorCode = "USER_NOT_FOUND"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "删除用户失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "用户删除成功",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetUserRoles 获取用户角色
// @Summary 获取用户角色
// @Description 获取指定用户的角色列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} UserRolesResponse "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/roles [get]
func (h *Handler) GetUserRoles(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：获取用户角色", "user_id", userID)
		c.JSON(http.StatusOK, &UserRolesResponse{
			UserID: userID,
			Roles: []*Role{
				{
					ID:          "dev-role-001",
					Name:        "开发者角色",
					Description: "开发模式默认角色",
					Permissions: []string{"*:*"},
				},
			},
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层获取用户角色
	roles, err := h.service.GetUserRoles(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取用户角色失败", "error", err, "user_id", userID)

		statusCode := http.StatusInternalServerError
		errorCode := "GET_USER_ROLES_FAILED"
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
			errorCode = "USER_NOT_FOUND"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "获取用户角色失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	response := &UserRolesResponse{
		UserID:    userID,
		Roles:     roles,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// AssignRole 分配角色
// @Summary 分配角色给用户
// @Description 为指定用户分配角色
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Param request body AssignRoleRequest true "分配角色请求"
// @Success 200 {object} SuccessResponse "分配成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户或角色不存在"
// @Failure 409 {object} ErrorResponse "角色已分配"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/roles [post]
func (h *Handler) AssignRole(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	var req AssignRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定分配角色请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：分配角色", "user_id", userID, "role_id", req.RoleID)
		c.JSON(http.StatusOK, SuccessResponse{
			Message:   "开发模式：角色分配成功",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层分配角色
	if err := h.service.AssignRole(c.Request.Context(), userID, req.RoleID); err != nil {
		h.logger.Error("分配角色失败", "error", err, "user_id", userID, "role_id", req.RoleID)

		statusCode := http.StatusInternalServerError
		errorCode := "ASSIGN_ROLE_FAILED"
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
			errorCode = "USER_OR_ROLE_NOT_FOUND"
		} else if strings.Contains(err.Error(), "已拥有") {
			statusCode = http.StatusConflict
			errorCode = "ROLE_ALREADY_ASSIGNED"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "分配角色失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "角色分配成功",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// UnassignRole 取消角色分配
// @Summary 取消用户角色
// @Description 取消指定用户的角色分配
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Param role_id path string true "角色ID"
// @Success 200 {object} SuccessResponse "取消成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户或角色不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/roles/{role_id} [delete]
func (h *Handler) UnassignRole(c *gin.Context) {
	userID := c.Param("id")
	roleID := c.Param("role_id")

	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	if roleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_ROLE_ID",
			Message:   "角色ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：取消角色分配", "user_id", userID, "role_id", roleID)
		c.JSON(http.StatusOK, SuccessResponse{
			Message:   "开发模式：角色取消成功",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层取消角色分配
	if err := h.service.UnassignRole(c.Request.Context(), userID, roleID); err != nil {
		h.logger.Error("取消角色分配失败", "error", err, "user_id", userID, "role_id", roleID)

		statusCode := http.StatusInternalServerError
		errorCode := "UNASSIGN_ROLE_FAILED"
		if strings.Contains(err.Error(), "不存在") || strings.Contains(err.Error(), "未拥有") {
			statusCode = http.StatusNotFound
			errorCode = "USER_ROLE_NOT_FOUND"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "取消角色分配失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "角色取消成功",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetUserPermissions 获取用户权限
// @Summary 获取用户权限
// @Description 获取指定用户的权限列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} UserPermissionsResponse "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/permissions [get]
func (h *Handler) GetUserPermissions(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：获取用户权限", "user_id", userID)
		c.JSON(http.StatusOK, &UserPermissionsResponse{
			UserID:      userID,
			Permissions: []string{"*:*", "app:*", "user:*", "system:*"},
			Timestamp:   time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层获取用户权限
	permissions, err := h.service.GetUserPermissions(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取用户权限失败", "error", err, "user_id", userID)

		statusCode := http.StatusInternalServerError
		errorCode := "GET_USER_PERMISSIONS_FAILED"
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
			errorCode = "USER_NOT_FOUND"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "获取用户权限失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	response := &UserPermissionsResponse{
		UserID:      userID,
		Permissions: permissions,
		Timestamp:   time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// GetUserSessions 获取用户会话
// @Summary 获取用户会话
// @Description 获取指定用户的活跃会话列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} UserSessionsResponse "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/sessions [get]
func (h *Handler) GetUserSessions(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：获取用户会话", "user_id", userID)
		devSessions := []*Session{
			{
				ID:           "dev-session-001",
				UserID:       userID,
				IPAddress:    "127.0.0.1",
				UserAgent:    "开发模式浏览器",
				CreatedAt:    time.Now().Add(-time.Hour),
				LastActivity: time.Now(),
				ExpiresAt:    time.Now().Add(time.Hour * 8),
			},
		}
		c.JSON(http.StatusOK, &UserSessionsResponse{
			UserID:    userID,
			Sessions:  devSessions,
			Total:     len(devSessions),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层获取用户会话
	sessions, err := h.service.GetUserSessions(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取用户会话失败", "error", err, "user_id", userID)

		statusCode := http.StatusInternalServerError
		errorCode := "GET_USER_SESSIONS_FAILED"
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
			errorCode = "USER_NOT_FOUND"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "获取用户会话失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	response := &UserSessionsResponse{
		UserID:    userID,
		Sessions:  sessions,
		Total:     len(sessions),
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// DeleteUserSessions 删除用户会话
// @Summary 删除用户会话
// @Description 删除指定用户的所有会话（强制下线）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/sessions [delete]
func (h *Handler) DeleteUserSessions(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 🔧 开发模式处理
	if h.isDevMode() {
		h.logger.Info("开发模式：删除用户会话", "user_id", userID)
		c.JSON(http.StatusOK, SuccessResponse{
			Message:   "开发模式：用户会话删除成功",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	// 调用服务层删除用户会话
	if err := h.service.DeleteUserSessions(c.Request.Context(), userID); err != nil {
		h.logger.Error("删除用户会话失败", "error", err, "user_id", userID)

		statusCode := http.StatusInternalServerError
		errorCode := "DELETE_USER_SESSIONS_FAILED"
		if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
			errorCode = "USER_NOT_FOUND"
		}

		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "删除用户会话失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "用户会话删除成功，用户已被强制下线",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// 以下是占位符方法，用于满足路由注册的编译要求
// TODO: 根据业务需求实现具体功能

// CreateTenant 创建租户（占位符实现）
func (h *Handler) CreateTenant(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "租户创建功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// ListTenants 获取租户列表（占位符实现）
func (h *Handler) ListTenants(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "租户列表功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetTenant 获取租户详情（占位符实现）
func (h *Handler) GetTenant(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "获取租户详情功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// UpdateTenant 更新租户（占位符实现）
func (h *Handler) UpdateTenant(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "更新租户功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetTenantUsers 获取租户用户（占位符实现）
func (h *Handler) GetTenantUsers(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "获取租户用户功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetTenantStats 获取租户统计（占位符实现）
func (h *Handler) GetTenantStats(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "获取租户统计功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// CreateRole 创建角色（占位符实现）
func (h *Handler) CreateRole(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "创建角色功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// ListRoles 获取角色列表（占位符实现）
func (h *Handler) ListRoles(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "角色列表功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetRole 获取角色详情（占位符实现）
func (h *Handler) GetRole(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "获取角色详情功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// UpdateRole 更新角色（占位符实现）
func (h *Handler) UpdateRole(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "更新角色功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// DeleteRole 删除角色（占位符实现）
func (h *Handler) DeleteRole(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "删除角色功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetRoleUsers 获取角色用户（占位符实现）
func (h *Handler) GetRoleUsers(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "获取角色用户功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// ListPermissions 获取权限列表（占位符实现）
func (h *Handler) ListPermissions(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "权限列表功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// ListSessions 获取会话列表（占位符实现）
func (h *Handler) ListSessions(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "会话列表功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetSession 获取会话详情（占位符实现）
func (h *Handler) GetSession(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "获取会话详情功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// DeleteSession 删除会话（占位符实现）
func (h *Handler) DeleteSession(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "删除会话功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetAuditLogs 获取审计日志（占位符实现）
func (h *Handler) GetAuditLogs(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "审计日志功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetAuditStats 获取审计统计（占位符实现）
func (h *Handler) GetAuditStats(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "审计统计功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// GetSecurityReport 获取安全报告（占位符实现）
func (h *Handler) GetSecurityReport(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "安全报告功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// SetupMFA 设置多因子认证（占位符实现）
func (h *Handler) SetupMFA(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "多因子认证设置功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// VerifyMFA 验证多因子认证（占位符实现）
func (h *Handler) VerifyMFA(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "多因子认证验证功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// ListMFADevices 获取多因子认证设备列表（占位符实现）
func (h *Handler) ListMFADevices(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "多因子认证设备列表功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// DeleteMFADevice 删除多因子认证设备（占位符实现）
func (h *Handler) DeleteMFADevice(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "删除多因子认证设备功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// CreateAPIKey 创建API密钥（占位符实现）
func (h *Handler) CreateAPIKey(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "创建API密钥功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// ListAPIKeys 获取API密钥列表（占位符实现）
func (h *Handler) ListAPIKeys(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "API密钥列表功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// DeleteAPIKey 删除API密钥（占位符实现）
func (h *Handler) DeleteAPIKey(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:      "NOT_IMPLEMENTED",
		Message:   "删除API密钥功能暂未实现",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

