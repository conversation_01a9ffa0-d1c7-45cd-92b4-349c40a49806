package auth

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// IDPHandler IDP HTTP 处理器
type IDPHandler struct {
	idpService  IDPService
	authService Service
	logger      Logger
}

// NewIDPHandler 创建 IDP 处理器
func NewIDPHandler(idpService IDPService, authService Service, logger Logger) *IDPHandler {
	return &IDPHandler{
		idpService:  idpService,
		authService: authService,
		logger:      logger,
	}
}

// RegisterRoutes 注册 IDP 路由
func (h *IDPHandler) RegisterRoutes(router *gin.RouterGroup) {
	// IDP 认证路由
	idp := router.Group("/idp")
	{
		// 认证流程
		idp.POST("/login/:provider", h.InitiateIDPLogin)      // 发起 IDP 登录
		idp.POST("/callback/:provider", h.HandleIDPCallback) // 处理 IDP 回调
		idp.POST("/logout", h.IDPLogout)                     // IDP 登出
		
		// 账号关联
		idp.POST("/link", h.LinkIDPAccount)                  // 关联 IDP 账号
		idp.DELETE("/unlink/:account_id", h.UnlinkIDPAccount) // 取消关联 IDP 账号
		idp.GET("/accounts", h.GetUserIDPAccounts)           // 获取用户的 IDP 账号
		
		// 用户信息同步
		idp.POST("/sync/:account_id", h.SyncUserFromIDP)     // 同步单个用户
		idp.POST("/sync-all/:provider", h.SyncAllUsersFromIDP) // 同步所有用户
	}
	
	// IDP 管理路由（需要管理员权限）
	admin := router.Group("/admin/idp")
	{
		// IDP 提供商管理
		admin.POST("/providers", h.CreateIDPProvider)           // 创建 IDP 提供商
		admin.GET("/providers", h.ListIDPProviders)             // 获取 IDP 提供商列表
		admin.GET("/providers/:id", h.GetIDPProvider)           // 获取 IDP 提供商详情
		admin.PUT("/providers/:id", h.UpdateIDPProvider)        // 更新 IDP 提供商
		admin.DELETE("/providers/:id", h.DeleteIDPProvider)     // 删除 IDP 提供商
		
		// IDP 统计和监控
		admin.GET("/stats", h.GetIDPStats)                      // 获取 IDP 统计信息
		admin.GET("/audit-logs", h.GetIDPAuditLogs)             // 获取 IDP 审计日志
	}
}

// InitiateIDPLogin 发起 IDP 登录
// @Summary 发起 IDP 登录
// @Description 发起通过指定 IDP 提供商的登录流程
// @Tags IDP认证
// @Accept json
// @Produce json
// @Param provider path string true "IDP 提供商名称"
// @Param request body IDPLoginRequest true "IDP 登录请求"
// @Success 200 {object} IDPAuthInitResponse "登录初始化成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "IDP 提供商不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/idp/login/{provider} [post]
func (h *IDPHandler) InitiateIDPLogin(c *gin.Context) {
	providerName := c.Param("provider")
	
	var req IDPLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定 IDP 登录请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 设置提供商名称
	req.ProviderName = providerName
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")
	
	// 验证请求参数
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数验证失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 获取 IDP 提供商
	provider, err := h.idpService.GetIDPProviderByName(c.Request.Context(), req.ProviderName, req.TenantID)
	if err != nil {
		h.logger.Error("获取 IDP 提供商失败", "error", err, "provider", req.ProviderName)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:      "PROVIDER_NOT_FOUND",
			Message:   "IDP 提供商不存在",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 检查提供商是否启用
	if !provider.Enabled {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "PROVIDER_DISABLED",
			Message:   "IDP 提供商已禁用",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 发起认证流程
	response, err := h.idpService.InitiateIDPAuth(c.Request.Context(), provider.ID, req.RedirectURL)
	if err != nil {
		h.logger.Error("发起 IDP 认证失败", "error", err, "provider_id", provider.ID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "AUTH_INIT_FAILED",
			Message:   "发起认证失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 记录审计日志
	h.logIDPEvent(c.Request.Context(), &IDPAuditLog{
		EventType:     "auth_init",
		IDPProviderID: provider.ID,
		Result:        "success",
		IPAddress:     req.IPAddress,
		UserAgent:     req.UserAgent,
		Details: map[string]interface{}{
			"provider_name": req.ProviderName,
			"redirect_url":  req.RedirectURL,
		},
	})
	
	h.logger.Info("IDP 认证初始化成功", "provider", req.ProviderName, "auth_url", response.AuthURL)
	c.JSON(http.StatusOK, response)
}

// HandleIDPCallback 处理 IDP 回调
// @Summary 处理 IDP 回调
// @Description 处理 IDP 认证回调，完成用户登录
// @Tags IDP认证
// @Accept json
// @Produce json
// @Param provider path string true "IDP 提供商名称"
// @Param request body IDPCallbackRequest true "IDP 回调请求"
// @Success 200 {object} IDPAuthResult "认证成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/idp/callback/{provider} [post]
func (h *IDPHandler) HandleIDPCallback(c *gin.Context) {
	providerName := c.Param("provider")
	
	var req IDPCallbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定 IDP 回调请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 设置提供商名称
	req.ProviderName = providerName
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")
	
	// 验证请求参数
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数验证失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 获取 IDP 提供商
	provider, err := h.idpService.GetIDPProviderByName(c.Request.Context(), req.ProviderName, req.TenantID)
	if err != nil {
		h.logger.Error("获取 IDP 提供商失败", "error", err, "provider", req.ProviderName)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:      "PROVIDER_NOT_FOUND",
			Message:   "IDP 提供商不存在",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 处理认证回调
	result, err := h.idpService.HandleIDPCallback(c.Request.Context(), provider.ID, req.Code, req.State)
	if err != nil {
		h.logger.Error("处理 IDP 回调失败", "error", err, "provider_id", provider.ID)
		
		// 记录失败的审计日志
		h.logIDPEvent(c.Request.Context(), &IDPAuditLog{
			EventType:     "auth_callback",
			IDPProviderID: provider.ID,
			Result:        "failure",
			ErrorMessage:  err.Error(),
			IPAddress:     req.IPAddress,
			UserAgent:     req.UserAgent,
			Details: map[string]interface{}{
				"provider_name": req.ProviderName,
				"code":          req.Code,
				"state":         req.State,
			},
		})
		
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "AUTH_CALLBACK_FAILED",
			Message:   "认证回调处理失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 记录成功的审计日志
	h.logIDPEvent(c.Request.Context(), &IDPAuditLog{
		EventType:     "auth_callback",
		UserID:        result.User.ID,
		IDPProviderID: provider.ID,
		IDPUserID:     result.IDPAccount.IDPUserID,
		Result:        "success",
		IPAddress:     req.IPAddress,
		UserAgent:     req.UserAgent,
		Details: map[string]interface{}{
			"provider_name": req.ProviderName,
			"is_new_user":   result.IsNewUser,
			"is_linked":     result.IsLinked,
		},
	})
	
	// 设置刷新令牌到 Cookie（如果有）
	if result.TokenPair != nil && result.TokenPair.RefreshToken != "" {
		c.SetCookie(
			"refresh_token",
			result.TokenPair.RefreshToken,
			int(time.Hour*24*7/time.Second), // 7天
			"/",
			"",
			true,  // Secure
			true,  // HttpOnly
		)
	}
	
	h.logger.Info("IDP 认证成功", 
		"provider", req.ProviderName, 
		"user_id", result.User.ID,
		"is_new_user", result.IsNewUser,
		"is_linked", result.IsLinked)
	
	c.JSON(http.StatusOK, result)
}

// LinkIDPAccount 关联 IDP 账号
// @Summary 关联 IDP 账号
// @Description 将当前用户与 IDP 账号进行关联
// @Tags IDP账号管理
// @Accept json
// @Produce json
// @Param request body LinkIDPAccountRequest true "关联请求"
// @Success 200 {object} IDPAccount "关联成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 409 {object} ErrorResponse "账号已关联"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/idp/link [post]
func (h *IDPHandler) LinkIDPAccount(c *gin.Context) {
	var req LinkIDPAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定关联 IDP 账号请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 验证请求参数
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数验证失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 关联账号
	account, err := h.idpService.LinkIDPAccount(c.Request.Context(), req.UserID, req.IDPProviderID, req.IDPUserID)
	if err != nil {
		h.logger.Error("关联 IDP 账号失败", "error", err, "user_id", req.UserID)
		
		statusCode := http.StatusInternalServerError
		errorCode := "LINK_FAILED"
		
		if err.Error() == "用户已关联此 IDP 提供商" || err.Error() == "此 IDP 账号已被其他用户关联" {
			statusCode = http.StatusConflict
			errorCode = "ALREADY_LINKED"
		}
		
		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "关联 IDP 账号失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 记录审计日志
	h.logIDPEvent(c.Request.Context(), &IDPAuditLog{
		EventType:     "account_link",
		UserID:        req.UserID,
		IDPProviderID: req.IDPProviderID,
		IDPUserID:     req.IDPUserID,
		Result:        "success",
		IPAddress:     c.ClientIP(),
		UserAgent:     c.GetHeader("User-Agent"),
	})
	
	h.logger.Info("IDP 账号关联成功", "user_id", req.UserID, "account_id", account.ID)
	c.JSON(http.StatusOK, account)
}

// logIDPEvent 记录 IDP 事件
func (h *IDPHandler) logIDPEvent(ctx gin.Context, event *IDPAuditLog) {
	if err := h.idpService.LogIDPEvent(ctx, event); err != nil {
		h.logger.Error("记录 IDP 审计日志失败", "error", err)
	}
}
