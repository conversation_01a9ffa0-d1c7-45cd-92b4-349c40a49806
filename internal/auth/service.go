package auth

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Service 认证服务接口
type Service interface {
	// 用户认证
	Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error)
	Logout(ctx context.Context, userID string, sessionID string) error
	RefreshToken(ctx context.Context, refreshToken string) (*TokenPair, error)
	
	// 用户管理
	CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
	GetUser(ctx context.Context, userID string) (*User, error)
	GetUserByUsername(ctx context.Context, username string, tenantID string) (*User, error)
	ListUsers(ctx context.Context, tenantID string, filter *UserFilter) ([]*User, int64, error)
	UpdateUser(ctx context.Context, userID string, req *UpdateUserRequest) (*User, error)
	DeleteUser(ctx context.Context, userID string) error
	ChangePassword(ctx context.Context, userID string, req *ChangePasswordRequest) error

	// 密码重置
	ResetPassword(ctx context.Context, req *ResetPasswordRequest) error
	ConfirmResetPassword(ctx context.Context, req *ConfirmResetPasswordRequest) error
	
	// 租户管理
	CreateTenant(ctx context.Context, req *CreateTenantRequest) (*Tenant, error)
	GetTenant(ctx context.Context, tenantID string) (*Tenant, error)
	ListTenants(ctx context.Context, filter *TenantFilter) ([]*Tenant, int64, error)
	UpdateTenant(ctx context.Context, tenantID string, req *UpdateTenantRequest) (*Tenant, error)
	
	// 角色管理
	CreateRole(ctx context.Context, req *CreateRoleRequest) (*Role, error)
	GetRole(ctx context.Context, roleID string) (*Role, error)
	ListRoles(ctx context.Context, tenantID string) ([]*Role, error)
	UpdateRole(ctx context.Context, roleID string, req *UpdateRoleRequest) (*Role, error)
	DeleteRole(ctx context.Context, roleID string) error
	
	// 用户角色管理
	AssignRole(ctx context.Context, userID string, roleID string) error
	UnassignRole(ctx context.Context, userID string, roleID string) error
	GetUserRoles(ctx context.Context, userID string) ([]*Role, error)
	GetUserPermissions(ctx context.Context, userID string) ([]string, error)
	
	// 权限检查
	HasPermission(ctx context.Context, userID string, permission string) (bool, error)
	CheckPermission(ctx context.Context, userID string, resource string, action string, scope string) (bool, error)
	
	// 会话管理
	CreateSession(ctx context.Context, userID string, ipAddress string, userAgent string) (*Session, error)
	GetSession(ctx context.Context, sessionID string) (*Session, error)
	GetUserSessions(ctx context.Context, userID string) ([]*Session, error)
	UpdateSessionActivity(ctx context.Context, sessionID string) error
	DeleteSession(ctx context.Context, sessionID string) error
	DeleteUserSessions(ctx context.Context, userID string) error
	
	// 审计日志
	LogAuditEvent(ctx context.Context, event *AuditEvent) error
	GetAuditLogs(ctx context.Context, filter *AuditFilter) ([]*AuditLog, int64, error)
}

// AuthService 认证服务实现
type AuthService struct {
	db          *gorm.DB
	jwtService  JWTService
	logger      Logger
	config      AuthConfig
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWTSecret           string        `json:"jwt_secret"`
	AccessTokenExpiry   time.Duration `json:"access_token_expiry"`
	RefreshTokenExpiry  time.Duration `json:"refresh_token_expiry"`
	SessionExpiry       time.Duration `json:"session_expiry"`
	MaxLoginAttempts    int           `json:"max_login_attempts"`
	LockoutDuration     time.Duration `json:"lockout_duration"`
	PasswordCost        int           `json:"password_cost"`
}

// NewAuthService 创建认证服务
func NewAuthService(db *gorm.DB, jwtService JWTService, logger Logger, config AuthConfig) Service {
	return &AuthService{
		db:         db,
		jwtService: jwtService,
		logger:     logger,
		config:     config,
	}
}

// Login 用户登录
func (s *AuthService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 记录登录尝试
	attempt := &LoginAttempt{
		ID:        uuid.New().String(),
		Username:  req.Username,
		TenantID:  req.TenantID,
		IPAddress: req.IPAddress,
		UserAgent: req.UserAgent,
		Success:   false,
		Timestamp: time.Now(),
	}
	
	// 检查登录尝试次数
	if err := s.checkLoginAttempts(ctx, req.Username, req.TenantID, req.IPAddress); err != nil {
		attempt.ErrorMsg = err.Error()
		s.db.Create(attempt)
		return nil, err
	}
	
	// 获取用户信息
	user, err := s.GetUserByUsername(ctx, req.Username, req.TenantID)
	if err != nil {
		attempt.ErrorMsg = "用户不存在"
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户名或密码错误")
	}
	
	// 检查用户状态
	if user.Status != UserStatusActive {
		attempt.ErrorMsg = fmt.Sprintf("用户状态异常: %s", user.Status)
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户账户已被禁用")
	}
	
	// 检查用户是否被锁定
	if user.LockedUntil != nil && time.Now().Before(*user.LockedUntil) {
		attempt.ErrorMsg = "用户账户已被锁定"
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户账户已被锁定，请稍后再试")
	}
	
	// 验证密码
	if !s.verifyPassword(req.Password, user.PasswordHash) {
		// 增加失败登录次数
		s.incrementFailedLogins(ctx, user.ID)
		
		attempt.ErrorMsg = "密码错误"
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户名或密码错误")
	}
	
	// 登录成功
	attempt.Success = true
	s.db.Create(attempt)
	
	// 重置失败登录次数
	s.resetFailedLogins(ctx, user.ID)
	
	// 更新最后登录时间
	s.updateLastLogin(ctx, user.ID)
	
	// 生成令牌对
	tokenPair, err := s.jwtService.GenerateTokenPair(user)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}
	
	// 创建会话
	session, err := s.CreateSession(ctx, user.ID, req.IPAddress, req.UserAgent)
	if err != nil {
		s.logger.Error("创建会话失败", "error", err, "user_id", user.ID)
	}
	
	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "authentication",
		EventName: "user_login",
		Result:    "success",
		UserID:    user.ID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		IPAddress: req.IPAddress,
		UserAgent: req.UserAgent,
		Resource:  "auth/login",
		Details: map[string]interface{}{
			"login_method": "password",
			"session_id":   session.ID,
		},
	})
	
	response := &LoginResponse{
		User:         user,
		TokenPair:    tokenPair,
		SessionID:    session.ID,
		ExpiresIn:    int(s.config.AccessTokenExpiry.Seconds()),
		Permissions:  []string{}, // TODO: 获取用户权限
	}
	
	s.logger.Info("用户登录成功", "user_id", user.ID, "username", user.Username, "tenant_id", user.TenantID)
	return response, nil
}

// Logout 用户登出
func (s *AuthService) Logout(ctx context.Context, userID string, sessionID string) error {
	// 删除指定会话
	if sessionID != "" {
		err := s.DeleteSession(ctx, sessionID)
		if err != nil {
			s.logger.Error("删除会话失败", "error", err, "session_id", sessionID)
			return fmt.Errorf("登出失败: %w", err)
		}
	}

	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "authentication",
		EventName: "user_logout",
		Result:    "success",
		UserID:    userID,
		Resource:  "auth/logout",
	})

	s.logger.Info("用户登出成功", "user_id", userID, "session_id", sessionID)
	return nil
}

// RefreshToken 刷新令牌
func (s *AuthService) RefreshToken(ctx context.Context, refreshToken string) (*TokenPair, error) {
	// TODO: 实现令牌刷新逻辑
	// 1. 验证 refresh token 的有效性
	// 2. 获取用户信息
	// 3. 生成新的 token pair
	// 4. 更新会话信息

	// 🔧 开发模式处理
	if getEnvOrDefault("APP_ENV", "development") == "development" {
		s.logger.Info("开发模式：刷新令牌", "refresh_token", refreshToken)
		return &TokenPair{
			AccessToken:  "dev-access-token-refreshed",
			RefreshToken: "dev-refresh-token-refreshed",
			ExpiresIn:    3600,
			TokenType:    "Bearer",
		}, nil
	}

	return nil, fmt.Errorf("令牌刷新功能暂未实现")
}

// CreateUser 创建用户
func (s *AuthService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查用户名是否已存在
	existingUser, err := s.GetUserByUsername(ctx, req.Username, req.TenantID)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("用户名 '%s' 已存在", req.Username)
	}
	
	// 检查邮箱是否已存在
	var emailUser User
	err = s.db.Where("email = ? AND tenant_id = ?", req.Email, req.TenantID).First(&emailUser).Error
	if err == nil {
		return nil, fmt.Errorf("邮箱 '%s' 已被使用", req.Email)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查邮箱失败: %w", err)
	}
	
	// 验证密码强度
	if err := s.validatePassword(req.Password); err != nil {
		return nil, fmt.Errorf("密码强度不足: %w", err)
	}
	
	// 哈希密码
	passwordHash, err := s.hashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码哈希失败: %w", err)
	}
	
	// 创建用户
	user := &User{
		ID:           uuid.New().String(),
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: passwordHash,
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Phone:        req.Phone,
		Status:       UserStatusActive,
		TenantID:     req.TenantID,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	
	// 在事务中创建用户和分配默认角色
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 创建用户
		if err := tx.Create(user).Error; err != nil {
			return fmt.Errorf("创建用户失败: %w", err)
		}
		
		// 分配默认角色
		if req.RoleIDs != nil && len(req.RoleIDs) > 0 {
			for _, roleID := range req.RoleIDs {
				userRole := &UserRole{
					UserID:    user.ID,
					RoleID:    roleID,
					CreatedAt: time.Now(),
					CreatedBy: req.CreatedBy,
				}
				if err := tx.Create(userRole).Error; err != nil {
					return fmt.Errorf("分配角色失败: %w", err)
				}
			}
		}
		
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "management",
		EventName: "user_created",
		Result:    "success",
		UserID:    req.CreatedBy,
		TenantID:  req.TenantID,
		Resource:  fmt.Sprintf("user/%s", user.ID),
		Details: map[string]interface{}{
			"created_user_id": user.ID,
			"username":        user.Username,
			"email":           user.Email,
		},
	})
	
	s.logger.Info("用户创建成功", "user_id", user.ID, "username", user.Username, "tenant_id", req.TenantID)
	return user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *AuthService) GetUserByUsername(ctx context.Context, username string, tenantID string) (*User, error) {
	var user User
	err := s.db.Preload("UserRoles.Role").Where("username = ? AND tenant_id = ?", username, tenantID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在: %s", username)
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	return &user, nil
}

// GetUser 获取用户信息
func (s *AuthService) GetUser(ctx context.Context, userID string) (*User, error) {
	var user User
	err := s.db.Preload("UserRoles.Role").Where("id = ?", userID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在: %s", userID)
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	return &user, nil
}

// ListUsers 获取用户列表
func (s *AuthService) ListUsers(ctx context.Context, tenantID string, filter *UserFilter) ([]*User, int64, error) {
	query := s.db.Preload("UserRoles.Role").Where("tenant_id = ?", tenantID)

	// 应用过滤条件
	if filter != nil {
		if filter.Username != "" {
			query = query.Where("username ILIKE ?", "%"+filter.Username+"%")
		}
		if filter.Email != "" {
			query = query.Where("email ILIKE ?", "%"+filter.Email+"%")
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.RoleID != "" {
			query = query.Joins("JOIN user_roles ON users.id = user_roles.user_id").
				Where("user_roles.role_id = ?", filter.RoleID)
		}
	}

	// 获取总数
	var total int64
	if err := query.Model(&User{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 分页查询
	var users []*User
	page := 1
	pageSize := 20
	if filter != nil {
		if filter.Page > 0 {
			page = filter.Page
		}
		if filter.PageSize > 0 && filter.PageSize <= 100 {
			pageSize = filter.PageSize
		}
	}

	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("查询用户列表失败: %w", err)
	}

	return users, total, nil
}

// UpdateUser 更新用户信息
func (s *AuthService) UpdateUser(ctx context.Context, userID string, req *UpdateUserRequest) (*User, error) {
	// 获取现有用户
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 构建更新字段
	updates := make(map[string]interface{})

	if req.FirstName != nil {
		updates["first_name"] = *req.FirstName
	}
	if req.LastName != nil {
		updates["last_name"] = *req.LastName
	}
	if req.Email != nil {
		// 检查邮箱是否已被其他用户使用
		var existingUser User
		err := s.db.Where("email = ? AND tenant_id = ? AND id != ?", *req.Email, user.TenantID, userID).First(&existingUser).Error
		if err == nil {
			return nil, fmt.Errorf("邮箱 '%s' 已被其他用户使用", *req.Email)
		}
		if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("检查邮箱失败: %w", err)
		}
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Avatar != nil {
		updates["avatar"] = *req.Avatar
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	// 更新时间戳
	updates["updated_at"] = time.Now()

	// 执行更新
	if err := s.db.Model(user).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新用户失败: %w", err)
	}

	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "user_management",
		EventName: "user_updated",
		Result:    "success",
		UserID:    userID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  fmt.Sprintf("user/%s", userID),
		Details: map[string]interface{}{
			"updated_fields": updates,
		},
	})

	// 重新获取更新后的用户信息
	return s.GetUser(ctx, userID)
}

// DeleteUser 删除用户
func (s *AuthService) DeleteUser(ctx context.Context, userID string) error {
	// 获取用户信息
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		return err
	}

	// 在事务中删除用户及相关数据
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 删除用户角色关联
		if err := tx.Where("user_id = ?", userID).Delete(&UserRole{}).Error; err != nil {
			return fmt.Errorf("删除用户角色关联失败: %w", err)
		}

		// 删除用户会话
		if err := tx.Where("user_id = ?", userID).Delete(&Session{}).Error; err != nil {
			return fmt.Errorf("删除用户会话失败: %w", err)
		}

		// 删除密码历史
		if err := tx.Where("user_id = ?", userID).Delete(&PasswordHistory{}).Error; err != nil {
			return fmt.Errorf("删除密码历史失败: %w", err)
		}

		// 删除登录尝试记录
		if err := tx.Where("username = ? AND tenant_id = ?", user.Username, user.TenantID).Delete(&LoginAttempt{}).Error; err != nil {
			return fmt.Errorf("删除登录尝试记录失败: %w", err)
		}

		// 删除用户
		if err := tx.Delete(user).Error; err != nil {
			return fmt.Errorf("删除用户失败: %w", err)
		}

		return nil
	})

	if err != nil {
		return err
	}

	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "user_management",
		EventName: "user_deleted",
		Result:    "success",
		UserID:    userID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  fmt.Sprintf("user/%s", userID),
	})

	s.logger.Info("用户删除成功", "user_id", userID, "username", user.Username)
	return nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(ctx context.Context, userID string, req *ChangePasswordRequest) error {
	// 获取用户信息
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		return err
	}
	
	// 验证当前密码
	if !s.verifyPassword(req.CurrentPassword, user.PasswordHash) {
		return fmt.Errorf("当前密码错误")
	}
	
	// 验证新密码强度
	if err := s.validatePassword(req.NewPassword); err != nil {
		return fmt.Errorf("新密码强度不足: %w", err)
	}
	
	// 检查密码历史
	if err := s.checkPasswordHistory(ctx, userID, req.NewPassword); err != nil {
		return err
	}
	
	// 哈希新密码
	newPasswordHash, err := s.hashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}
	
	// 在事务中更新密码和记录历史
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 保存旧密码到历史记录
		history := &PasswordHistory{
			ID:           uuid.New().String(),
			UserID:       userID,
			PasswordHash: user.PasswordHash,
			CreatedAt:    time.Now(),
		}
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("保存密码历史失败: %w", err)
		}
		
		// 更新用户密码
		if err := tx.Model(user).Update("password_hash", newPasswordHash).Error; err != nil {
			return fmt.Errorf("更新密码失败: %w", err)
		}
		
		// 清理过期的密码历史
		s.cleanupPasswordHistory(tx, userID)
		
		return nil
	})
	
	if err != nil {
		return err
	}
	
	// 撤销所有会话 (强制重新登录)
	s.DeleteUserSessions(ctx, userID)
	
	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "authentication",
		EventName: "password_changed",
		Result:    "success",
		UserID:    userID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  fmt.Sprintf("user/%s/password", userID),
	})
	
	s.logger.Info("用户密码修改成功", "user_id", userID)
	return nil
}

// HasPermission 检查用户是否有指定权限
func (s *AuthService) HasPermission(ctx context.Context, userID string, permission string) (bool, error) {
	permissions, err := s.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}
	
	// 检查是否有通配符权限
	for _, perm := range permissions {
		if perm == "*:*" || perm == permission {
			return true, nil
		}
		
		// 检查资源级通配符
		if strings.HasSuffix(perm, ":*") {
			resource := strings.TrimSuffix(perm, ":*")
			if strings.HasPrefix(permission, resource+":") {
				return true, nil
			}
		}
	}
	
	return false, nil
}

// GetUserPermissions 获取用户权限
func (s *AuthService) GetUserPermissions(ctx context.Context, userID string) ([]string, error) {
	var permissions []string
	
	// 获取用户角色
	roles, err := s.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, err
	}
	
	// 合并所有角色的权限
	permissionSet := make(map[string]bool)
	for _, role := range roles {
		for _, perm := range role.Permissions {
			permissionSet[perm] = true
		}
	}
	
	// 转换为切片
	for perm := range permissionSet {
		permissions = append(permissions, perm)
	}
	
	return permissions, nil
}

// CreateSession 创建会话
func (s *AuthService) CreateSession(ctx context.Context, userID string, ipAddress string, userAgent string) (*Session, error) {
	session := &Session{
		ID:           uuid.New().String(),
		UserID:       userID,
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
		CreatedAt:    time.Now(),
		LastActivity: time.Now(),
		ExpiresAt:    time.Now().Add(s.config.SessionExpiry),
		Data:         make(map[string]interface{}),
	}
	
	// 获取用户信息设置租户ID
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		return nil, err
	}
	session.TenantID = user.TenantID
	
	if err := s.db.Create(session).Error; err != nil {
		return nil, fmt.Errorf("创建会话失败: %w", err)
	}
	
	return session, nil
}

// GetSession 获取会话信息
func (s *AuthService) GetSession(ctx context.Context, sessionID string) (*Session, error) {
	var session Session
	err := s.db.Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("会话不存在: %s", sessionID)
		}
		return nil, fmt.Errorf("获取会话失败: %w", err)
	}
	return &session, nil
}

// GetUserSessions 获取用户会话列表
func (s *AuthService) GetUserSessions(ctx context.Context, userID string) ([]*Session, error) {
	var sessions []*Session
	err := s.db.Where("user_id = ? AND expires_at > ?", userID, time.Now()).
		Order("last_activity DESC").Find(&sessions).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户会话失败: %w", err)
	}
	return sessions, nil
}

// UpdateSessionActivity 更新会话活动时间
func (s *AuthService) UpdateSessionActivity(ctx context.Context, sessionID string) error {
	err := s.db.Model(&Session{}).Where("id = ?", sessionID).
		Update("last_activity", time.Now()).Error
	if err != nil {
		return fmt.Errorf("更新会话活动时间失败: %w", err)
	}
	return nil
}

// DeleteSession 删除会话
func (s *AuthService) DeleteSession(ctx context.Context, sessionID string) error {
	err := s.db.Where("id = ?", sessionID).Delete(&Session{}).Error
	if err != nil {
		return fmt.Errorf("删除会话失败: %w", err)
	}
	return nil
}

// DeleteUserSessions 删除用户所有会话
func (s *AuthService) DeleteUserSessions(ctx context.Context, userID string) error {
	err := s.db.Where("user_id = ?", userID).Delete(&Session{}).Error
	if err != nil {
		return fmt.Errorf("删除用户会话失败: %w", err)
	}

	s.logger.Info("用户会话已清理", "user_id", userID)
	return nil
}

// GetUserRoles 获取用户角色
func (s *AuthService) GetUserRoles(ctx context.Context, userID string) ([]*Role, error) {
	var roles []*Role
	err := s.db.Table("roles").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ?", userID).
		Find(&roles).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}
	return roles, nil
}

// AssignRole 分配角色给用户
func (s *AuthService) AssignRole(ctx context.Context, userID string, roleID string) error {
	// 检查用户是否存在
	_, err := s.GetUser(ctx, userID)
	if err != nil {
		return err
	}

	// 检查角色是否存在
	var role Role
	err = s.db.Where("id = ?", roleID).First(&role).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("角色不存在: %s", roleID)
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查是否已经分配
	var existingUserRole UserRole
	err = s.db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&existingUserRole).Error
	if err == nil {
		return fmt.Errorf("用户已拥有该角色")
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查用户角色失败: %w", err)
	}

	// 创建用户角色关联
	userRole := &UserRole{
		UserID:    userID,
		RoleID:    roleID,
		CreatedAt: time.Now(),
		CreatedBy: userID, // TODO: 从上下文获取操作者ID
	}

	if err := s.db.Create(userRole).Error; err != nil {
		return fmt.Errorf("分配角色失败: %w", err)
	}

	// 记录审计日志
	user, _ := s.GetUser(ctx, userID)
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "role_management",
		EventName: "role_assigned",
		Result:    "success",
		UserID:    userID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  fmt.Sprintf("user/%s/roles", userID),
		Details: map[string]interface{}{
			"role_id":   roleID,
			"role_name": role.Name,
		},
	})

	s.logger.Info("角色分配成功", "user_id", userID, "role_id", roleID, "role_name", role.Name)
	return nil
}

// UnassignRole 取消用户角色
func (s *AuthService) UnassignRole(ctx context.Context, userID string, roleID string) error {
	// 检查用户是否存在
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		return err
	}

	// 检查角色分配是否存在
	var userRole UserRole
	err = s.db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&userRole).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("用户未拥有该角色")
		}
		return fmt.Errorf("检查用户角色失败: %w", err)
	}

	// 删除用户角色关联
	if err := s.db.Delete(&userRole).Error; err != nil {
		return fmt.Errorf("取消角色分配失败: %w", err)
	}

	// 获取角色信息用于日志
	var role Role
	s.db.Where("id = ?", roleID).First(&role)

	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "role_management",
		EventName: "role_unassigned",
		Result:    "success",
		UserID:    userID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  fmt.Sprintf("user/%s/roles", userID),
		Details: map[string]interface{}{
			"role_id":   roleID,
			"role_name": role.Name,
		},
	})

	s.logger.Info("角色取消成功", "user_id", userID, "role_id", roleID, "role_name", role.Name)
	return nil
}

// 辅助方法

// hashPassword 哈希密码
func (s *AuthService) hashPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), s.config.PasswordCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// verifyPassword 验证密码
func (s *AuthService) verifyPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// validatePassword 验证密码强度
func (s *AuthService) validatePassword(password string) error {
	// TODO: 实现密码强度验证逻辑
	if len(password) < 8 {
		return fmt.Errorf("密码长度至少8位")
	}
	return nil
}

// checkLoginAttempts 检查登录尝试次数
func (s *AuthService) checkLoginAttempts(ctx context.Context, username, tenantID, ipAddress string) error {
	// 检查最近一段时间的失败登录次数
	var failedCount int64
	since := time.Now().Add(-s.config.LockoutDuration)
	
	err := s.db.Model(&LoginAttempt{}).
		Where("username = ? AND tenant_id = ? AND ip_address = ? AND success = false AND timestamp > ?",
			username, tenantID, ipAddress, since).
		Count(&failedCount).Error
	if err != nil {
		return fmt.Errorf("检查登录尝试失败: %w", err)
	}
	
	if failedCount >= int64(s.config.MaxLoginAttempts) {
		return fmt.Errorf("登录尝试次数过多，请稍后再试")
	}
	
	return nil
}

// incrementFailedLogins 增加失败登录次数
func (s *AuthService) incrementFailedLogins(ctx context.Context, userID string) {
	s.db.Model(&User{}).Where("id = ?", userID).UpdateColumn("failed_logins", gorm.Expr("failed_logins + 1"))
	
	// 检查是否需要锁定用户
	var user User
	if err := s.db.First(&user, "id = ?", userID).Error; err == nil {
		if user.FailedLogins >= s.config.MaxLoginAttempts {
			lockUntil := time.Now().Add(s.config.LockoutDuration)
			s.db.Model(&user).Update("locked_until", &lockUntil)
			s.logger.Warn("用户因多次登录失败被锁定", "user_id", userID, "failed_logins", user.FailedLogins)
		}
	}
}

// resetFailedLogins 重置失败登录次数
func (s *AuthService) resetFailedLogins(ctx context.Context, userID string) {
	s.db.Model(&User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"failed_logins": 0,
		"locked_until":  nil,
	})
}

// updateLastLogin 更新最后登录时间
func (s *AuthService) updateLastLogin(ctx context.Context, userID string) {
	now := time.Now()
	s.db.Model(&User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"last_login_at": &now,
		"login_count":   gorm.Expr("login_count + 1"),
	})
}

// checkPasswordHistory 检查密码历史
func (s *AuthService) checkPasswordHistory(ctx context.Context, userID string, newPassword string) error {
	// 获取密码历史
	var histories []PasswordHistory
	err := s.db.Where("user_id = ?", userID).Order("created_at DESC").Limit(12).Find(&histories).Error
	if err != nil {
		return fmt.Errorf("获取密码历史失败: %w", err)
	}
	
	// 检查新密码是否与历史密码相同
	for _, history := range histories {
		if s.verifyPassword(newPassword, history.PasswordHash) {
			return fmt.Errorf("新密码不能与最近使用的密码相同")
		}
	}
	
	return nil
}

// cleanupPasswordHistory 清理密码历史
func (s *AuthService) cleanupPasswordHistory(tx *gorm.DB, userID string) {
	// 只保留最近12次密码历史
	tx.Where("user_id = ? AND id NOT IN (SELECT id FROM password_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 12)",
		userID, userID).Delete(&PasswordHistory{})
}

// ResetPassword 重置密码请求
func (s *AuthService) ResetPassword(ctx context.Context, req *ResetPasswordRequest) error {
	// 验证请求参数
	if req.Email == "" {
		return fmt.Errorf("邮箱不能为空")
	}
	if req.TenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}

	// 查找用户
	var user User
	err := s.db.Where("email = ? AND tenant_id = ?", req.Email, req.TenantID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 为了安全，即使用户不存在也返回成功
			s.logger.Warn("重置密码请求的邮箱不存在", "email", req.Email, "tenant_id", req.TenantID)
			return nil
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查用户状态
	if user.Status != UserStatusActive {
		return fmt.Errorf("用户账户已被禁用")
	}

	// 生成重置令牌
	resetToken := uuid.New().String()
	expiresAt := time.Now().Add(time.Hour) // 1小时有效期

	// 保存重置令牌
	passwordReset := &PasswordResetToken{
		ID:        uuid.New().String(),
		UserID:    user.ID,
		Token:     resetToken,
		ExpiresAt: expiresAt,
		Used:      false,
		CreatedAt: time.Now(),
	}

	if err := s.db.Create(passwordReset).Error; err != nil {
		return fmt.Errorf("保存重置令牌失败: %w", err)
	}

	// TODO: 发送重置密码邮件
	// 这里应该调用邮件服务发送包含重置链接的邮件
	s.logger.Info("密码重置令牌已生成", "user_id", user.ID, "email", req.Email, "token", resetToken)

	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "authentication",
		EventName: "password_reset_requested",
		Result:    "success",
		UserID:    user.ID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  "auth/reset-password",
		Details: map[string]interface{}{
			"email": req.Email,
		},
	})

	return nil
}

// ConfirmResetPassword 确认重置密码
func (s *AuthService) ConfirmResetPassword(ctx context.Context, req *ConfirmResetPasswordRequest) error {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 查找重置令牌
	var passwordReset PasswordResetToken
	err := s.db.Where("token = ? AND used = false AND expires_at > ?", req.Token, time.Now()).First(&passwordReset).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("重置令牌无效或已过期")
		}
		return fmt.Errorf("查询重置令牌失败: %w", err)
	}

	// 获取用户信息
	user, err := s.GetUser(ctx, passwordReset.UserID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 验证新密码强度
	if err := s.validatePassword(req.NewPassword); err != nil {
		return fmt.Errorf("新密码强度不足: %w", err)
	}

	// 检查密码历史
	if err := s.checkPasswordHistory(ctx, user.ID, req.NewPassword); err != nil {
		return err
	}

	// 哈希新密码
	newPasswordHash, err := s.hashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 在事务中更新密码和标记令牌已使用
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 保存旧密码到历史记录
		history := &PasswordHistory{
			ID:           uuid.New().String(),
			UserID:       user.ID,
			PasswordHash: user.PasswordHash,
			CreatedAt:    time.Now(),
		}
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("保存密码历史失败: %w", err)
		}

		// 更新用户密码
		if err := tx.Model(user).Update("password_hash", newPasswordHash).Error; err != nil {
			return fmt.Errorf("更新密码失败: %w", err)
		}

		// 标记重置令牌已使用
		if err := tx.Model(&passwordReset).Updates(map[string]interface{}{
			"used":    true,
			"used_at": time.Now(),
		}).Error; err != nil {
			return fmt.Errorf("更新重置令牌状态失败: %w", err)
		}

		// 清理过期的密码历史
		s.cleanupPasswordHistory(tx, user.ID)

		return nil
	})

	if err != nil {
		return err
	}

	// 撤销所有会话 (强制重新登录)
	s.DeleteUserSessions(ctx, user.ID)

	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "authentication",
		EventName: "password_reset_confirmed",
		Result:    "success",
		UserID:    user.ID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  "auth/confirm-reset",
	})

	s.logger.Info("密码重置成功", "user_id", user.ID, "username", user.Username)
	return nil
}

// LogAuditEvent 记录审计事件
func (s *AuthService) LogAuditEvent(ctx context.Context, event *AuditEvent) error {
	// TODO: 实现审计日志记录功能
	// 这里应该将审计事件保存到数据库或发送到审计系统
	s.logger.Info("审计事件",
		"event_type", event.EventType,
		"event_name", event.EventName,
		"result", event.Result,
		"user_id", event.UserID,
		"tenant_id", event.TenantID,
		"username", event.Username,
		"resource", event.Resource,
	)
	return nil
}

// GetAuditLogs 获取审计日志（占位符实现）
func (s *AuthService) GetAuditLogs(ctx context.Context, filter *AuditFilter) ([]*AuditLog, int64, error) {
	// TODO: 实现审计日志查询功能
	return []*AuditLog{}, 0, fmt.Errorf("审计日志查询功能暂未实现")
}

// CheckPermission 检查权限
func (s *AuthService) CheckPermission(ctx context.Context, userID string, resource string, action string, scope string) (bool, error) {
	// 获取用户权限
	permissions, err := s.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("获取用户权限失败: %w", err)
	}

	// 检查是否有超级权限
	for _, perm := range permissions {
		if perm == "*:*" || perm == "*" {
			return true, nil
		}

		// 检查资源级权限
		if perm == resource+":*" || perm == resource+":"+action {
			return true, nil
		}

		// 检查通配符权限
		if strings.HasSuffix(perm, ":*") {
			permResource := strings.TrimSuffix(perm, ":*")
			if strings.HasPrefix(resource, permResource) {
				return true, nil
			}
		}
	}

	return false, nil
}

// 以下是占位符方法实现，用于满足 Service 接口的编译要求
// TODO: 根据业务需求实现具体功能

// CreateTenant 创建租户（占位符实现）
func (s *AuthService) CreateTenant(ctx context.Context, req *CreateTenantRequest) (*Tenant, error) {
	return nil, fmt.Errorf("创建租户功能暂未实现")
}

// GetTenant 获取租户（占位符实现）
func (s *AuthService) GetTenant(ctx context.Context, tenantID string) (*Tenant, error) {
	return nil, fmt.Errorf("获取租户功能暂未实现")
}

// ListTenants 获取租户列表（占位符实现）
func (s *AuthService) ListTenants(ctx context.Context, filter *TenantFilter) ([]*Tenant, int64, error) {
	return nil, 0, fmt.Errorf("租户列表功能暂未实现")
}

// UpdateTenant 更新租户（占位符实现）
func (s *AuthService) UpdateTenant(ctx context.Context, tenantID string, req *UpdateTenantRequest) (*Tenant, error) {
	return nil, fmt.Errorf("更新租户功能暂未实现")
}

// CreateRole 创建角色（占位符实现）
func (s *AuthService) CreateRole(ctx context.Context, req *CreateRoleRequest) (*Role, error) {
	return nil, fmt.Errorf("创建角色功能暂未实现")
}

// GetRole 获取角色（占位符实现）
func (s *AuthService) GetRole(ctx context.Context, roleID string) (*Role, error) {
	return nil, fmt.Errorf("获取角色功能暂未实现")
}

// ListRoles 获取角色列表（占位符实现）
func (s *AuthService) ListRoles(ctx context.Context, tenantID string) ([]*Role, error) {
	return nil, fmt.Errorf("角色列表功能暂未实现")
}

// UpdateRole 更新角色（占位符实现）
func (s *AuthService) UpdateRole(ctx context.Context, roleID string, req *UpdateRoleRequest) (*Role, error) {
	return nil, fmt.Errorf("更新角色功能暂未实现")
}

// DeleteRole 删除角色（占位符实现）
func (s *AuthService) DeleteRole(ctx context.Context, roleID string) error {
	return fmt.Errorf("删除角色功能暂未实现")
}




