package auth

import (
	"time"
)

// Tenant 租户实体
type Tenant struct {
	ID          string       `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string       `json:"name" gorm:"type:varchar(100);not null;uniqueIndex"`
	Description string       `json:"description" gorm:"type:text"`
	Status      TenantStatus `json:"status" gorm:"type:varchar(20);not null;default:active"`
	Settings    TenantSettings `json:"settings" gorm:"type:jsonb"`
	Quotas      TenantQuotas `json:"quotas" gorm:"type:jsonb"`
	CreatedAt   time.Time    `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time    `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt   *time.Time   `json:"deleted_at,omitempty" gorm:"index"`
	
	// 关联关系
	Users []User `json:"users,omitempty" gorm:"foreignKey:TenantID"`
	Roles []Role `json:"roles,omitempty" gorm:"foreignKey:TenantID"`
}

// TenantStatus 租户状态
type TenantStatus string

const (
	TenantStatusActive    TenantStatus = "active"    // 活跃
	TenantStatusSuspended TenantStatus = "suspended" // 暂停
	TenantStatusDeleted   TenantStatus = "deleted"   // 已删除
)

// TenantSettings 租户设置
type TenantSettings struct {
	PasswordPolicy PasswordPolicy `json:"password_policy"`
	SessionPolicy  SessionPolicy  `json:"session_policy"`
	SecurityPolicy SecurityPolicy `json:"security_policy"`
	Features       FeatureFlags   `json:"features"`
}

// PasswordPolicy 密码策略
type PasswordPolicy struct {
	MinLength        int  `json:"min_length"`         // 最小长度
	RequireUppercase bool `json:"require_uppercase"`  // 需要大写字母
	RequireLowercase bool `json:"require_lowercase"`  // 需要小写字母
	RequireNumbers   bool `json:"require_numbers"`    // 需要数字
	RequireSymbols   bool `json:"require_symbols"`    // 需要特殊字符
	MaxAgeDays       int  `json:"max_age_days"`       // 最大有效期
	HistoryCount     int  `json:"history_count"`      // 历史密码数量
}

// SessionPolicy 会话策略
type SessionPolicy struct {
	MaxIdleTime        string `json:"max_idle_time"`        // 最大空闲时间
	MaxSessionTime     string `json:"max_session_time"`     // 最大会话时间
	ConcurrentSessions int    `json:"concurrent_sessions"`  // 最大并发会话数
}

// SecurityPolicy 安全策略
type SecurityPolicy struct {
	IPWhitelist       []string `json:"ip_whitelist"`        // IP 白名单
	MaxLoginAttempts  int      `json:"max_login_attempts"`  // 最大登录尝试次数
	LockoutDuration   string   `json:"lockout_duration"`    // 锁定时长
	RequireMFA        bool     `json:"require_mfa"`         // 需要多因子认证
}

// FeatureFlags 功能开关
type FeatureFlags struct {
	CICDEnabled       bool `json:"cicd_enabled"`       // CI/CD 功能
	MonitoringEnabled bool `json:"monitoring_enabled"` // 监控功能
	BackupEnabled     bool `json:"backup_enabled"`     // 备份功能
	APIEnabled        bool `json:"api_enabled"`        // API 访问
}

// TenantQuotas 租户配额
type TenantQuotas struct {
	MaxApplications int    `json:"max_applications"` // 最大应用数
	MaxUsers        int    `json:"max_users"`        // 最大用户数
	MaxStorage      string `json:"max_storage"`      // 最大存储空间
	MaxCPU          string `json:"max_cpu"`          // 最大 CPU
	MaxMemory       string `json:"max_memory"`       // 最大内存
}

// User 用户实体
type User struct {
	ID           string     `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Username     string     `json:"username" gorm:"type:varchar(50);not null"`
	Email        string     `json:"email" gorm:"type:varchar(100);not null"`
	PasswordHash string     `json:"-" gorm:"type:varchar(255)"` // IDP 用户可以为空
	FirstName    string     `json:"first_name" gorm:"type:varchar(50)"`
	LastName     string     `json:"last_name" gorm:"type:varchar(50)"`
	Avatar       string     `json:"avatar" gorm:"type:varchar(500)"`
	Phone        string     `json:"phone" gorm:"type:varchar(20)"`
	Status       UserStatus `json:"status" gorm:"type:varchar(20);not null;default:active"`
	TenantID     string     `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	LastLoginAt  *time.Time `json:"last_login_at"`
	LoginCount   int        `json:"login_count" gorm:"default:0"`
	FailedLogins int        `json:"failed_logins" gorm:"default:0"`
	LockedUntil  *time.Time `json:"locked_until"`
	MFAEnabled   bool       `json:"mfa_enabled" gorm:"default:false"`
	MFASecret    string     `json:"-" gorm:"type:varchar(100)"`

	// IDP 相关字段
	IsIDPUser      bool   `json:"is_idp_user" gorm:"default:false"`      // 是否为 IDP 用户
	PrimaryIDPID   string `json:"primary_idp_id" gorm:"type:varchar(36)"` // 主要 IDP 账号 ID
	IDPSyncEnabled bool   `json:"idp_sync_enabled" gorm:"default:true"`   // 是否启用 IDP 信息同步

	CreatedAt    time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt    *time.Time `json:"deleted_at,omitempty" gorm:"index"`

	// 关联关系
	Tenant          Tenant            `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	UserRoles       []UserRole        `json:"user_roles,omitempty" gorm:"foreignKey:UserID"`
	Sessions        []Session         `json:"sessions,omitempty" gorm:"foreignKey:UserID"`
	PasswordHistory []PasswordHistory `json:"password_history,omitempty" gorm:"foreignKey:UserID"`
	IDPAccounts     []IDPAccount      `json:"idp_accounts,omitempty" gorm:"foreignKey:UserID"` // IDP 账号关联
}

// UserStatus 用户状态
type UserStatus string

const (
	UserStatusActive   UserStatus = "active"   // 活跃
	UserStatusInactive UserStatus = "inactive" // 非活跃
	UserStatusLocked   UserStatus = "locked"   // 锁定
	UserStatusDeleted  UserStatus = "deleted"  // 已删除
)

// Role 角色实体
type Role struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string    `json:"name" gorm:"type:varchar(50);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Permissions []string  `json:"permissions" gorm:"type:jsonb"`
	Scope       string    `json:"scope" gorm:"type:varchar(20);not null;default:tenant"` // global, tenant, user
	TenantID    string    `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	IsSystem    bool      `json:"is_system" gorm:"default:false"`                        // 是否为系统角色
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"index"`
	
	// 关联关系
	Tenant    Tenant     `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	UserRoles []UserRole `json:"user_roles,omitempty" gorm:"foreignKey:RoleID"`
}

// UserRole 用户角色关联
type UserRole struct {
	UserID    string    `json:"user_id" gorm:"type:varchar(36);not null"`
	RoleID    string    `json:"role_id" gorm:"type:varchar(36);not null"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	CreatedBy string    `json:"created_by" gorm:"type:varchar(36)"`
	
	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Role Role `json:"role,omitempty" gorm:"foreignKey:RoleID"`
}

// Session 会话实体
type Session struct {
	ID           string                 `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID       string                 `json:"user_id" gorm:"type:varchar(36);not null;index"`
	TenantID     string                 `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	IPAddress    string                 `json:"ip_address" gorm:"type:varchar(45)"`
	UserAgent    string                 `json:"user_agent" gorm:"type:varchar(500)"`
	CreatedAt    time.Time              `json:"created_at" gorm:"autoCreateTime"`
	LastActivity time.Time              `json:"last_activity" gorm:"autoUpdateTime"`
	ExpiresAt    time.Time              `json:"expires_at" gorm:"not null;index"`
	Data         map[string]interface{} `json:"data" gorm:"type:jsonb"`
	
	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// PasswordHistory 密码历史
type PasswordHistory struct {
	ID           string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID       string    `json:"user_id" gorm:"type:varchar(36);not null;index"`
	PasswordHash string    `json:"-" gorm:"type:varchar(255);not null"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	
	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// AuditLog 审计日志
type AuditLog struct {
	ID         string                 `json:"id" gorm:"primaryKey;type:varchar(36)"`
	EventType  string                 `json:"event_type" gorm:"type:varchar(50);not null;index"`  // authentication, authorization, management
	EventName  string                 `json:"event_name" gorm:"type:varchar(100);not null;index"` // user_login, permission_check, etc.
	Result     string                 `json:"result" gorm:"type:varchar(20);not null"`            // success, failure
	UserID     string                 `json:"user_id" gorm:"type:varchar(36);index"`
	TenantID   string                 `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	Username   string                 `json:"username" gorm:"type:varchar(50)"`
	IPAddress  string                 `json:"ip_address" gorm:"type:varchar(45);index"`
	UserAgent  string                 `json:"user_agent" gorm:"type:varchar(500)"`
	Resource   string                 `json:"resource" gorm:"type:varchar(200)"`
	Details    map[string]interface{} `json:"details" gorm:"type:jsonb"`
	RiskScore  int                    `json:"risk_score" gorm:"default:0"`                        // 风险评分 0-100
	Timestamp  time.Time              `json:"timestamp" gorm:"autoCreateTime;index"`
}

// LoginAttempt 登录尝试记录
type LoginAttempt struct {
	ID        string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Username  string    `json:"username" gorm:"type:varchar(50);not null;index"`
	TenantID  string    `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	IPAddress string    `json:"ip_address" gorm:"type:varchar(45);not null;index"`
	UserAgent string    `json:"user_agent" gorm:"type:varchar(500)"`
	Success   bool      `json:"success" gorm:"not null;index"`
	ErrorMsg  string    `json:"error_msg" gorm:"type:varchar(255)"`
	Timestamp time.Time `json:"timestamp" gorm:"autoCreateTime;index"`
}

// PasswordResetToken 密码重置令牌
type PasswordResetToken struct {
	ID        string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID    string    `json:"user_id" gorm:"type:varchar(36);not null;index"`
	Token     string    `json:"token" gorm:"type:varchar(255);not null;uniqueIndex"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null;index"`
	Used      bool      `json:"used" gorm:"default:false"`
	UsedAt    *time.Time `json:"used_at"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	
	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// RefreshToken 刷新令牌
type RefreshToken struct {
	ID        string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID    string    `json:"user_id" gorm:"type:varchar(36);not null;index"`
	Token     string    `json:"token" gorm:"type:varchar(255);not null;uniqueIndex"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null;index"`
	Revoked   bool      `json:"revoked" gorm:"default:false"`
	RevokedAt *time.Time `json:"revoked_at"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	
	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// Permission 权限实体
type Permission struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Resource    string    `json:"resource" gorm:"type:varchar(50);not null"`    // app, pipeline, build, etc.
	Action      string    `json:"action" gorm:"type:varchar(50);not null"`      // create, read, update, delete, execute
	Scope       string    `json:"scope" gorm:"type:varchar(20);not null"`       // global, tenant, user, own
	Description string    `json:"description" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// JWTBlacklist JWT 黑名单
type JWTBlacklist struct {
	JTI       string    `json:"jti" gorm:"primaryKey;type:varchar(36)"`         // JWT ID
	UserID    string    `json:"user_id" gorm:"type:varchar(36);not null;index"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null;index"`
	RevokedAt time.Time `json:"revoked_at" gorm:"autoCreateTime"`
	Reason    string    `json:"reason" gorm:"type:varchar(100)"`                // 撤销原因
}

// MFADevice 多因子认证设备
type MFADevice struct {
	ID         string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID     string    `json:"user_id" gorm:"type:varchar(36);not null;index"`
	Type       string    `json:"type" gorm:"type:varchar(20);not null"`          // totp, sms, email
	Name       string    `json:"name" gorm:"type:varchar(100);not null"`
	Secret     string    `json:"-" gorm:"type:varchar(255)"`                     // 加密存储
	Verified   bool      `json:"verified" gorm:"default:false"`
	LastUsed   *time.Time `json:"last_used"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	
	// 关联关系
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// SSOProvider SSO 提供商配置
type SSOProvider struct {
	ID           string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name         string            `json:"name" gorm:"type:varchar(50);not null;uniqueIndex"`
	Type         string            `json:"type" gorm:"type:varchar(20);not null"`              // saml, oauth, oidc
	Enabled      bool              `json:"enabled" gorm:"default:false"`
	Config       SSOConfig         `json:"config" gorm:"type:jsonb"`
	Mapping      AttributeMapping  `json:"mapping" gorm:"type:jsonb"`
	TenantID     string            `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	CreatedAt    time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
	
	// 关联关系
	Tenant Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// SSOConfig SSO 配置
type SSOConfig struct {
	// SAML 配置
	EntityID     string `json:"entity_id"`
	SSOURL       string `json:"sso_url"`
	SLOURL       string `json:"slo_url"`
	Certificate  string `json:"certificate"`
	PrivateKey   string `json:"private_key"`
	
	// OAuth/OIDC 配置
	ClientID     string   `json:"client_id"`
	ClientSecret string   `json:"client_secret"`
	AuthURL      string   `json:"auth_url"`
	TokenURL     string   `json:"token_url"`
	UserInfoURL  string   `json:"user_info_url"`
	Scopes       []string `json:"scopes"`
	
	// 通用配置
	AutoCreateUser bool `json:"auto_create_user"` // 是否自动创建用户
	DefaultRole    string `json:"default_role"`   // 默认角色
}

// AttributeMapping 属性映射
type AttributeMapping struct {
	UserID    string `json:"user_id"`
	Username  string `json:"username"`
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Roles     string `json:"roles"`
}

// APIKey API 密钥
type APIKey struct {
	ID          string     `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string     `json:"name" gorm:"type:varchar(100);not null"`
	Key         string     `json:"key" gorm:"type:varchar(255);not null;uniqueIndex"`
	KeyHash     string     `json:"-" gorm:"type:varchar(255);not null"`
	UserID      string     `json:"user_id" gorm:"type:varchar(36);not null;index"`
	TenantID    string     `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	Permissions []string   `json:"permissions" gorm:"type:jsonb"`
	ExpiresAt   *time.Time `json:"expires_at" gorm:"index"`
	LastUsed    *time.Time `json:"last_used"`
	Status      string     `json:"status" gorm:"type:varchar(20);not null;default:active"`
	CreatedAt   time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
	
	// 关联关系
	User   User   `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Tenant Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// TableName 指定表名
func (Tenant) TableName() string {
	return "tenants"
}

func (User) TableName() string {
	return "users"
}

func (Role) TableName() string {
	return "roles"
}

func (UserRole) TableName() string {
	return "user_roles"
}

func (Session) TableName() string {
	return "sessions"
}

func (PasswordHistory) TableName() string {
	return "password_history"
}

func (AuditLog) TableName() string {
	return "audit_logs"
}

func (LoginAttempt) TableName() string {
	return "login_attempts"
}

func (PasswordResetToken) TableName() string {
	return "password_reset_tokens"
}



func (RefreshToken) TableName() string {
	return "refresh_tokens"
}

func (Permission) TableName() string {
	return "permissions"
}

func (JWTBlacklist) TableName() string {
	return "jwt_blacklist"
}

func (MFADevice) TableName() string {
	return "mfa_devices"
}

func (SSOProvider) TableName() string {
	return "sso_providers"
}

// IDPProvider IDP 提供商配置 (扩展现有的 SSOProvider)
type IDPProvider struct {
	ID           string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name         string            `json:"name" gorm:"type:varchar(100);not null;uniqueIndex"`
	Type         IDPProviderType   `json:"type" gorm:"type:varchar(20);not null"`              // oidc, oauth2, saml, ldap
	Enabled      bool              `json:"enabled" gorm:"default:false"`
	Config       IDPConfig         `json:"config" gorm:"type:jsonb"`
	Mapping      IDPAttributeMapping `json:"mapping" gorm:"type:jsonb"`
	TenantID     string            `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	Priority     int               `json:"priority" gorm:"default:0"`                         // 优先级，数字越小优先级越高
	AutoCreateUser bool            `json:"auto_create_user" gorm:"default:true"`              // 是否自动创建用户
	AutoLinkUser   bool            `json:"auto_link_user" gorm:"default:false"`               // 是否自动关联现有用户
	CreatedAt    time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time         `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联关系
	Tenant      Tenant       `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	IDPAccounts []IDPAccount `json:"idp_accounts,omitempty" gorm:"foreignKey:IDPProviderID"`
}

// IDPProviderType IDP 提供商类型
type IDPProviderType string

const (
	IDPProviderOIDC   IDPProviderType = "oidc"   // OpenID Connect
	IDPProviderOAuth2 IDPProviderType = "oauth2" // OAuth 2.0
	IDPProviderSAML   IDPProviderType = "saml"   // SAML 2.0
	IDPProviderLDAP   IDPProviderType = "ldap"   // LDAP
)

// IDPConfig IDP 配置
type IDPConfig struct {
	// OAuth2/OIDC 配置
	ClientID     string   `json:"client_id"`
	ClientSecret string   `json:"client_secret"`
	AuthURL      string   `json:"auth_url"`
	TokenURL     string   `json:"token_url"`
	UserInfoURL  string   `json:"user_info_url"`
	JWKSUrl      string   `json:"jwks_url"`
	Issuer       string   `json:"issuer"`
	Scopes       []string `json:"scopes"`

	// SAML 配置
	EntityID     string `json:"entity_id"`
	SSOURL       string `json:"sso_url"`
	SLOURL       string `json:"slo_url"`
	Certificate  string `json:"certificate"`
	PrivateKey   string `json:"private_key"`

	// LDAP 配置
	Host         string `json:"host"`
	Port         int    `json:"port"`
	BaseDN       string `json:"base_dn"`
	BindDN       string `json:"bind_dn"`
	BindPassword string `json:"bind_password"`
	UserFilter   string `json:"user_filter"`

	// 通用配置
	Timeout      int    `json:"timeout"`       // 超时时间（秒）
	RedirectURL  string `json:"redirect_url"`  // 回调 URL
}

// IDPAttributeMapping IDP 属性映射
type IDPAttributeMapping struct {
	UserID    string `json:"user_id"`    // IDP 用户 ID 字段
	Username  string `json:"username"`   // 用户名字段
	Email     string `json:"email"`      // 邮箱字段
	FirstName string `json:"first_name"` // 名字字段
	LastName  string `json:"last_name"`  // 姓氏字段
	Avatar    string `json:"avatar"`     // 头像字段
	Phone     string `json:"phone"`      // 电话字段
	Roles     string `json:"roles"`      // 角色字段
	Groups    string `json:"groups"`     // 组字段
}

// IDPAccount IDP 账号关联
type IDPAccount struct {
	ID            string           `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID        string           `json:"user_id" gorm:"type:varchar(36);not null;index"`
	IDPProviderID string           `json:"idp_provider_id" gorm:"type:varchar(36);not null;index"`
	IDPUserID     string           `json:"idp_user_id" gorm:"type:varchar(255);not null"`         // IDP 中的用户 ID
	IDPUsername   string           `json:"idp_username" gorm:"type:varchar(255)"`                 // IDP 中的用户名
	IDPEmail      string           `json:"idp_email" gorm:"type:varchar(255)"`                    // IDP 中的邮箱
	Status        IDPAccountStatus `json:"status" gorm:"type:varchar(20);not null;default:active"`
	LastSyncAt    *time.Time       `json:"last_sync_at"`                                          // 最后同步时间
	SyncData      map[string]interface{} `json:"sync_data" gorm:"type:jsonb"`                 // 同步的数据
	CreatedAt     time.Time        `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time        `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联关系
	User        User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	IDPProvider IDPProvider `json:"idp_provider,omitempty" gorm:"foreignKey:IDPProviderID"`
}

// IDPAccountStatus IDP 账号状态
type IDPAccountStatus string

const (
	IDPAccountStatusActive   IDPAccountStatus = "active"   // 活跃
	IDPAccountStatusInactive IDPAccountStatus = "inactive" // 非活跃
	IDPAccountStatusRevoked  IDPAccountStatus = "revoked"  // 已撤销
)

// IDPSession IDP 会话
type IDPSession struct {
	ID            string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID        string    `json:"user_id" gorm:"type:varchar(36);not null;index"`
	IDPProviderID string    `json:"idp_provider_id" gorm:"type:varchar(36);not null;index"`
	IDPSessionID  string    `json:"idp_session_id" gorm:"type:varchar(255)"`                // IDP 会话 ID
	AccessToken   string    `json:"-" gorm:"type:text"`                                     // 访问令牌（加密存储）
	RefreshToken  string    `json:"-" gorm:"type:text"`                                     // 刷新令牌（加密存储）
	TokenType     string    `json:"token_type" gorm:"type:varchar(50)"`                     // 令牌类型
	ExpiresAt     *time.Time `json:"expires_at"`                                            // 令牌过期时间
	Scope         string    `json:"scope" gorm:"type:varchar(500)"`                         // 授权范围
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联关系
	User        User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	IDPProvider IDPProvider `json:"idp_provider,omitempty" gorm:"foreignKey:IDPProviderID"`
}

// IDPAuditLog IDP 审计日志
type IDPAuditLog struct {
	ID            string                 `json:"id" gorm:"primaryKey;type:varchar(36)"`
	EventType     string                 `json:"event_type" gorm:"type:varchar(50);not null;index"`  // login, sync, link, unlink
	UserID        string                 `json:"user_id" gorm:"type:varchar(36);index"`
	IDPProviderID string                 `json:"idp_provider_id" gorm:"type:varchar(36);not null;index"`
	IDPUserID     string                 `json:"idp_user_id" gorm:"type:varchar(255)"`
	Result        string                 `json:"result" gorm:"type:varchar(20);not null"`            // success, failure
	ErrorMessage  string                 `json:"error_message" gorm:"type:text"`
	IPAddress     string                 `json:"ip_address" gorm:"type:varchar(45);index"`
	UserAgent     string                 `json:"user_agent" gorm:"type:varchar(500)"`
	Details       map[string]interface{} `json:"details" gorm:"type:jsonb"`
	Timestamp     time.Time              `json:"timestamp" gorm:"autoCreateTime;index"`

	// 关联关系
	User        User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	IDPProvider IDPProvider `json:"idp_provider,omitempty" gorm:"foreignKey:IDPProviderID"`
}

func (APIKey) TableName() string {
	return "api_keys"
}

func (IDPProvider) TableName() string {
	return "idp_providers"
}

func (IDPAccount) TableName() string {
	return "idp_accounts"
}

func (IDPSession) TableName() string {
	return "idp_sessions"
}

func (IDPAuditLog) TableName() string {
	return "idp_audit_logs"
}
