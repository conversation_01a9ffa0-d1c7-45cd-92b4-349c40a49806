package auth

import (
	"fmt"
	"regexp"
	"strings"
	"time"
)

// LoginRequest 登录请求
type LoginRequest struct {
	Username  string `json:"username" validate:"required"`
	Password  string `json:"password" validate:"required"`
	TenantID  string `json:"tenant_id" validate:"required"`
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
	Remember  bool   `json:"remember"` // 是否记住登录状态
}

// Validate 验证登录请求
func (req *LoginRequest) Validate() error {
	if strings.TrimSpace(req.Username) == "" {
		return fmt.Errorf("用户名不能为空")
	}
	
	if strings.TrimSpace(req.Password) == "" {
		return fmt.Errorf("密码不能为空")
	}
	
	if strings.TrimSpace(req.TenantID) == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	
	return nil
}

// LoginResponse 登录响应
type LoginResponse struct {
	User        *User      `json:"user"`
	TokenPair   *TokenPair `json:"token_pair"`
	SessionID   string     `json:"session_id"`
	ExpiresIn   int        `json:"expires_in"`
	Permissions []string   `json:"permissions"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username  string   `json:"username" validate:"required,min=3,max=50"`
	Email     string   `json:"email" validate:"required,email"`
	Password  string   `json:"password" validate:"required,min=8"`
	FirstName string   `json:"first_name" validate:"max=50"`
	LastName  string   `json:"last_name" validate:"max=50"`
	Phone     string   `json:"phone" validate:"max=20"`
	TenantID  string   `json:"tenant_id" validate:"required"`
	RoleIDs   []string `json:"role_ids"`
	CreatedBy string   `json:"created_by" validate:"required"`
}

// Validate 验证创建用户请求
func (req *CreateUserRequest) Validate() error {
	if strings.TrimSpace(req.Username) == "" {
		return fmt.Errorf("用户名不能为空")
	}
	
	if len(req.Username) < 3 || len(req.Username) > 50 {
		return fmt.Errorf("用户名长度必须在3-50个字符之间")
	}
	
	// 验证用户名格式 (字母、数字、下划线)
	if matched, _ := regexp.MatchString("^[a-zA-Z0-9_]+$", req.Username); !matched {
		return fmt.Errorf("用户名只能包含字母、数字和下划线")
	}
	
	if strings.TrimSpace(req.Email) == "" {
		return fmt.Errorf("邮箱不能为空")
	}
	
	// 验证邮箱格式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(req.Email) {
		return fmt.Errorf("邮箱格式不正确")
	}
	
	if strings.TrimSpace(req.Password) == "" {
		return fmt.Errorf("密码不能为空")
	}
	
	if len(req.Password) < 8 {
		return fmt.Errorf("密码长度至少8位")
	}
	
	if strings.TrimSpace(req.TenantID) == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	
	if strings.TrimSpace(req.CreatedBy) == "" {
		return fmt.Errorf("创建者ID不能为空")
	}
	
	return nil
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	FirstName *string     `json:"first_name"`
	LastName  *string     `json:"last_name"`
	Email     *string     `json:"email"`
	Phone     *string     `json:"phone"`
	Avatar    *string     `json:"avatar"`
	Status    *UserStatus `json:"status"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required"`
}

// Validate 验证修改密码请求
func (req *ChangePasswordRequest) Validate() error {
	if strings.TrimSpace(req.CurrentPassword) == "" {
		return fmt.Errorf("当前密码不能为空")
	}
	
	if strings.TrimSpace(req.NewPassword) == "" {
		return fmt.Errorf("新密码不能为空")
	}
	
	if len(req.NewPassword) < 8 {
		return fmt.Errorf("新密码长度至少8位")
	}
	
	if req.NewPassword != req.ConfirmPassword {
		return fmt.Errorf("新密码和确认密码不匹配")
	}
	
	if req.CurrentPassword == req.NewPassword {
		return fmt.Errorf("新密码不能与当前密码相同")
	}
	
	return nil
}

// CreateTenantRequest 创建租户请求
type CreateTenantRequest struct {
	Name        string         `json:"name" validate:"required,min=2,max=100"`
	Description string         `json:"description"`
	Settings    TenantSettings `json:"settings"`
	Quotas      TenantQuotas   `json:"quotas"`
	CreatedBy   string         `json:"created_by" validate:"required"`
}

// UpdateTenantRequest 更新租户请求
type UpdateTenantRequest struct {
	Name        *string         `json:"name"`
	Description *string         `json:"description"`
	Status      *TenantStatus   `json:"status"`
	Settings    *TenantSettings `json:"settings"`
	Quotas      *TenantQuotas   `json:"quotas"`
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string   `json:"name" validate:"required,min=2,max=50"`
	Description string   `json:"description"`
	Permissions []string `json:"permissions" validate:"required"`
	Scope       string   `json:"scope" validate:"required"`
	TenantID    string   `json:"tenant_id" validate:"required"`
	CreatedBy   string   `json:"created_by" validate:"required"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        *string  `json:"name"`
	Description *string  `json:"description"`
	Permissions []string `json:"permissions"`
}

// UserFilter 用户过滤器
type UserFilter struct {
	Username string     `json:"username"`
	Email    string     `json:"email"`
	Status   UserStatus `json:"status"`
	RoleID   string     `json:"role_id"`
	Page     int        `json:"page"`
	PageSize int        `json:"page_size"`
}

// TenantFilter 租户过滤器
type TenantFilter struct {
	Name   string       `json:"name"`
	Status TenantStatus `json:"status"`
	Page   int          `json:"page"`
	PageSize int        `json:"page_size"`
}

// AuditFilter 审计日志过滤器
type AuditFilter struct {
	EventType string    `json:"event_type"`
	EventName string    `json:"event_name"`
	Result    string    `json:"result"`
	UserID    string    `json:"user_id"`
	TenantID  string    `json:"tenant_id"`
	IPAddress string    `json:"ip_address"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Page      int       `json:"page"`
	PageSize  int       `json:"page_size"`
}

// AuditEvent 审计事件
type AuditEvent struct {
	EventType string                 `json:"event_type"`
	EventName string                 `json:"event_name"`
	Result    string                 `json:"result"`
	UserID    string                 `json:"user_id"`
	TenantID  string                 `json:"tenant_id"`
	Username  string                 `json:"username"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent"`
	Resource  string                 `json:"resource"`
	Details   map[string]interface{} `json:"details"`
	RiskScore int                    `json:"risk_score"`
}

// UserResponse 用户响应
type UserResponse struct {
	*User
	Roles       []*Role   `json:"roles"`
	Permissions []string  `json:"permissions"`
	LastLogin   *time.Time `json:"last_login"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users []*UserResponse `json:"users"`
	Total int64           `json:"total"`
	Page  int             `json:"page"`
	Size  int             `json:"size"`
}

// TenantResponse 租户响应
type TenantResponse struct {
	*Tenant
	UserCount int `json:"user_count"`
	AppCount  int `json:"app_count"`
}

// TenantListResponse 租户列表响应
type TenantListResponse struct {
	Tenants []*TenantResponse `json:"tenants"`
	Total   int64             `json:"total"`
	Page    int               `json:"page"`
	Size    int               `json:"size"`
}

// RoleResponse 角色响应
type RoleResponse struct {
	*Role
	UserCount int `json:"user_count"`
}

// PermissionCheckRequest 权限检查请求
type PermissionCheckRequest struct {
	UserID     string `json:"user_id" validate:"required"`
	Resource   string `json:"resource" validate:"required"`
	Action     string `json:"action" validate:"required"`
	Scope      string `json:"scope"`
	ResourceID string `json:"resource_id"`
}

// PermissionCheckResponse 权限检查响应
type PermissionCheckResponse struct {
	Allowed bool   `json:"allowed"`
	Reason  string `json:"reason,omitempty"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Email    string `json:"email" validate:"required,email"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// ConfirmResetPasswordRequest 确认重置密码请求
type ConfirmResetPasswordRequest struct {
	Token           string `json:"token" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required"`
}

// Validate 验证确认重置密码请求
func (req *ConfirmResetPasswordRequest) Validate() error {
	if strings.TrimSpace(req.Token) == "" {
		return fmt.Errorf("重置令牌不能为空")
	}
	
	if strings.TrimSpace(req.NewPassword) == "" {
		return fmt.Errorf("新密码不能为空")
	}
	
	if len(req.NewPassword) < 8 {
		return fmt.Errorf("新密码长度至少8位")
	}
	
	if req.NewPassword != req.ConfirmPassword {
		return fmt.Errorf("新密码和确认密码不匹配")
	}
	
	return nil
}

// SessionResponse 会话响应
type SessionResponse struct {
	*Session
	Username string `json:"username"`
	IsActive bool   `json:"is_active"`
}

// SessionListResponse 会话列表响应
type SessionListResponse struct {
	Sessions []*SessionResponse `json:"sessions"`
	Total    int64              `json:"total"`
}

// AuditLogResponse 审计日志响应
type AuditLogResponse struct {
	*AuditLog
	Username   string `json:"username"`
	TenantName string `json:"tenant_name"`
}

// AuditLogListResponse 审计日志列表响应
type AuditLogListResponse struct {
	Logs  []*AuditLogResponse `json:"logs"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Size  int                 `json:"size"`
}

// UserStatistics 用户统计
type UserStatistics struct {
	TotalUsers   int64 `json:"total_users"`
	ActiveUsers  int64 `json:"active_users"`
	LockedUsers  int64 `json:"locked_users"`
	OnlineUsers  int64 `json:"online_users"`
	TodayLogins  int64 `json:"today_logins"`
	FailedLogins int64 `json:"failed_logins"`
}

// TenantStatistics 租户统计
type TenantStatistics struct {
	TotalTenants    int64 `json:"total_tenants"`
	ActiveTenants   int64 `json:"active_tenants"`
	SuspendedTenants int64 `json:"suspended_tenants"`
}

// SecurityReport 安全报告
type SecurityReport struct {
	Period           string                    `json:"period"`
	LoginAttempts    int64                     `json:"login_attempts"`
	FailedLogins     int64                     `json:"failed_logins"`
	SuccessRate      float64                   `json:"success_rate"`
	UniqueUsers      int64                     `json:"unique_users"`
	TopFailedIPs     []IPFailureCount          `json:"top_failed_ips"`
	TopFailedUsers   []UserFailureCount        `json:"top_failed_users"`
	SecurityEvents   []SecurityEventSummary    `json:"security_events"`
	RiskDistribution map[string]int64          `json:"risk_distribution"`
}

// IPFailureCount IP 失败次数统计
type IPFailureCount struct {
	IPAddress    string `json:"ip_address"`
	FailureCount int64  `json:"failure_count"`
}

// UserFailureCount 用户失败次数统计
type UserFailureCount struct {
	Username     string `json:"username"`
	FailureCount int64  `json:"failure_count"`
}

// SecurityEventSummary 安全事件摘要
type SecurityEventSummary struct {
	EventType string `json:"event_type"`
	Count     int64  `json:"count"`
}

// MFASetupRequest MFA 设置请求
type MFASetupRequest struct {
	Type string `json:"type" validate:"required"` // totp, sms, email
	Name string `json:"name" validate:"required"`
}

// MFAVerifyRequest MFA 验证请求
type MFAVerifyRequest struct {
	DeviceID string `json:"device_id" validate:"required"`
	Code     string `json:"code" validate:"required"`
}

// MFASetupResponse MFA 设置响应
type MFASetupResponse struct {
	DeviceID   string   `json:"device_id"`
	Secret     string   `json:"secret,omitempty"`     // TOTP 密钥
	QRCode     string   `json:"qr_code,omitempty"`    // TOTP 二维码
	BackupCodes []string `json:"backup_codes,omitempty"` // 备用代码
}

// APIKeyRequest API 密钥请求
type APIKeyRequest struct {
	Name        string    `json:"name" validate:"required"`
	Permissions []string  `json:"permissions" validate:"required"`
	ExpiresAt   *time.Time `json:"expires_at"`
}

// APIKeyResponse API 密钥响应
type APIKeyResponse struct {
	*APIKey
	Key string `json:"key,omitempty"` // 只在创建时返回
}

// SSOLoginRequest SSO 登录请求
type SSOLoginRequest struct {
	Provider string `json:"provider" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
	Code     string `json:"code"`     // OAuth 授权码
	State    string `json:"state"`    // OAuth 状态参数
}

// SSOLoginResponse SSO 登录响应
type SSOLoginResponse struct {
	*LoginResponse
	Provider string `json:"provider"`
	IsNewUser bool  `json:"is_new_user"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Details   string `json:"details,omitempty"`
	Timestamp string `json:"timestamp"`
}

// SuccessResponse 成功响应
type SuccessResponse struct {
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp string      `json:"timestamp"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
	Total    int64 `json:"total"`
	Pages    int   `json:"pages"`
}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status      string            `json:"status"`
	Service     string            `json:"service"`
	Version     string            `json:"version"`
	Timestamp   string            `json:"timestamp"`
	Checks      map[string]string `json:"checks"`
	Uptime      string            `json:"uptime"`
}

// ValidatePermission 验证权限格式
func ValidatePermission(permission string) error {
	// 权限格式: resource:action[:scope]
	parts := strings.Split(permission, ":")
	if len(parts) < 2 || len(parts) > 3 {
		return fmt.Errorf("权限格式错误，应为 resource:action[:scope]")
	}
	
	resource := parts[0]
	action := parts[1]
	
	// 验证资源类型
	validResources := []string{"app", "pipeline", "build", "deployment", "user", "role", "config", "monitor", "system", "*"}
	if !contains(validResources, resource) {
		return fmt.Errorf("无效的资源类型: %s", resource)
	}
	
	// 验证操作类型
	validActions := []string{"create", "read", "update", "delete", "execute", "manage", "*"}
	if !contains(validActions, action) {
		return fmt.Errorf("无效的操作类型: %s", action)
	}
	
	// 验证作用域 (可选)
	if len(parts) == 3 {
		scope := parts[2]
		validScopes := []string{"global", "tenant", "user", "own", "*"}
		if !contains(validScopes, scope) {
			return fmt.Errorf("无效的作用域: %s", scope)
		}
	}
	
	return nil
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetDefaultTenantSettings 获取默认租户设置
func GetDefaultTenantSettings() TenantSettings {
	return TenantSettings{
		PasswordPolicy: PasswordPolicy{
			MinLength:        8,
			RequireUppercase: true,
			RequireLowercase: true,
			RequireNumbers:   true,
			RequireSymbols:   false,
			MaxAgeDays:       90,
			HistoryCount:     12,
		},
		SessionPolicy: SessionPolicy{
			MaxIdleTime:        "30m",
			MaxSessionTime:     "8h",
			ConcurrentSessions: 3,
		},
		SecurityPolicy: SecurityPolicy{
			IPWhitelist:      []string{},
			MaxLoginAttempts: 5,
			LockoutDuration:  "15m",
			RequireMFA:       false,
		},
		Features: FeatureFlags{
			CICDEnabled:       true,
			MonitoringEnabled: true,
			BackupEnabled:     true,
			APIEnabled:        true,
		},
	}
}

// GetDefaultTenantQuotas 获取默认租户配额
func GetDefaultTenantQuotas() TenantQuotas {
	return TenantQuotas{
		MaxApplications: 50,
		MaxUsers:        20,
		MaxStorage:      "10Gi",
		MaxCPU:          "10",
		MaxMemory:       "20Gi",
	}
}

// UserRolesResponse 用户角色响应
type UserRolesResponse struct {
	UserID    string    `json:"user_id"`
	Roles     []*Role   `json:"roles"`
	Timestamp string    `json:"timestamp"`
}

// UserPermissionsResponse 用户权限响应
type UserPermissionsResponse struct {
	UserID      string   `json:"user_id"`
	Permissions []string `json:"permissions"`
	Timestamp   string   `json:"timestamp"`
}

// UserSessionsResponse 用户会话响应
type UserSessionsResponse struct {
	UserID    string     `json:"user_id"`
	Sessions  []*Session `json:"sessions"`
	Total     int        `json:"total"`
	Timestamp string     `json:"timestamp"`
}

// PermissionsResponse 权限列表响应
type PermissionsResponse struct {
	Permissions []Permission `json:"permissions"`
	Total       int          `json:"total"`
	Timestamp   string       `json:"timestamp"`
}

// SessionsResponse 会话列表响应
type SessionsResponse struct {
	Sessions  []*Session `json:"sessions"`
	Total     int        `json:"total"`
	Timestamp string     `json:"timestamp"`
}

// MessageResponse 消息响应
type MessageResponse struct {
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
}

// AssignRoleRequest 分配角色请求
type AssignRoleRequest struct {
	RoleID     string `json:"role_id" validate:"required"`
	AssignedBy string `json:"assigned_by"`
}
