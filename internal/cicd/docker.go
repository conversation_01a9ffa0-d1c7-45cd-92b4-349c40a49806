package cicd

import (
	"archive/tar"
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"
)

// DockerClient Docker 客户端接口
type DockerClient interface {
	// BuildImage 构建 Docker 镜像
	BuildImage(ctx context.Context, buildContext io.Reader, options BuildImageOptions) error
	
	// PushImage 推送 Docker 镜像
	PushImage(ctx context.Context, image string, options PushImageOptions) error
	
	// RunContainer 运行容器
	RunContainer(ctx context.Context, config *ContainerConfig) (*ContainerInfo, error)
	
	// StopContainer 停止容器
	StopContainer(ctx context.Context, containerID string) error
	
	// RemoveContainer 删除容器
	RemoveContainer(ctx context.Context, containerID string) error
	
	// GetContainerLogs 获取容器日志
	GetContainerLogs(ctx context.Context, containerID string) (string, error)
}

// DefaultDockerClient 默认 Docker 客户端实现
type DefaultDockerClient struct {
	logger Logger
}

// BuildImageOptions 构建镜像选项
type BuildImageOptions struct {
	Dockerfile   string            `json:"dockerfile"`    // Dockerfile 路径
	Tags         []string          `json:"tags"`          // 镜像标签
	BuildArgs    map[string]string `json:"build_args"`    // 构建参数
	Target       string            `json:"target"`        // 构建目标阶段
	NoCache      bool              `json:"no_cache"`      // 是否禁用缓存
	Pull         bool              `json:"pull"`          // 是否拉取最新基础镜像
	Remove       bool              `json:"remove"`        // 是否删除中间容器
	ForceRemove  bool              `json:"force_remove"`  // 是否强制删除中间容器
	Squash       bool              `json:"squash"`        // 是否压缩镜像层
	Labels       map[string]string `json:"labels"`        // 镜像标签
}

// PushImageOptions 推送镜像选项
type PushImageOptions struct {
	Registry     RegistryAuth `json:"registry"`      // 镜像仓库认证信息
	Tag          string       `json:"tag"`           // 镜像标签
	All          bool         `json:"all"`           // 是否推送所有标签
	PrivilegeFunc func() (string, error) `json:"-"` // 权限函数
}

// RegistryAuth 镜像仓库认证信息
type RegistryAuth struct {
	Username      string `json:"username"`
	Password      string `json:"password"`
	Email         string `json:"email"`
	ServerAddress string `json:"server_address"`
}

// ContainerConfig 容器配置
type ContainerConfig struct {
	Image        string            `json:"image"`         // 镜像名称
	Name         string            `json:"name"`          // 容器名称
	Env          []string          `json:"env"`           // 环境变量
	Cmd          []string          `json:"cmd"`           // 启动命令
	WorkingDir   string            `json:"working_dir"`   // 工作目录
	Volumes      map[string]string `json:"volumes"`       // 数据卷挂载
	Ports        map[string]string `json:"ports"`         // 端口映射
	Labels       map[string]string `json:"labels"`        // 容器标签
	RestartPolicy string           `json:"restart_policy"` // 重启策略
	NetworkMode  string            `json:"network_mode"`  // 网络模式
	Memory       int64             `json:"memory"`        // 内存限制 (字节)
	CPUShares    int64             `json:"cpu_shares"`    // CPU 权重
	Privileged   bool              `json:"privileged"`    // 是否特权模式
	AutoRemove   bool              `json:"auto_remove"`   // 是否自动删除
}

// ContainerInfo 容器信息
type ContainerInfo struct {
	ID       string            `json:"id"`        // 容器ID
	Name     string            `json:"name"`      // 容器名称
	Image    string            `json:"image"`     // 镜像名称
	Status   string            `json:"status"`    // 容器状态
	Ports    []PortBinding     `json:"ports"`     // 端口绑定
	Labels   map[string]string `json:"labels"`    // 容器标签
	Created  time.Time         `json:"created"`   // 创建时间
	Started  time.Time         `json:"started"`   // 启动时间
}

// PortBinding 端口绑定
type PortBinding struct {
	ContainerPort string `json:"container_port"` // 容器端口
	HostPort      string `json:"host_port"`      // 主机端口
	Protocol      string `json:"protocol"`       // 协议类型
}

// NewDockerClient 创建 Docker 客户端
func NewDockerClient(logger Logger) (DockerClient, error) {
	return &DefaultDockerClient{
		logger: logger,
	}, nil
}

// BuildImage 构建 Docker 镜像
func (d *DefaultDockerClient) BuildImage(ctx context.Context, buildContext io.Reader, options BuildImageOptions) error {
	d.logger.Info("开始构建 Docker 镜像", "tags", options.Tags, "dockerfile", options.Dockerfile)

	// 使用 docker build 命令构建镜像
	args := []string{"build"}

	if options.Dockerfile != "" {
		args = append(args, "-f", options.Dockerfile)
	}

	for _, tag := range options.Tags {
		args = append(args, "-t", tag)
	}

	for key, value := range options.BuildArgs {
		args = append(args, "--build-arg", fmt.Sprintf("%s=%s", key, value))
	}

	if options.NoCache {
		args = append(args, "--no-cache")
	}

	if options.Pull {
		args = append(args, "--pull")
	}

	args = append(args, ".") // 构建上下文

	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		d.logger.Error("Docker 构建失败", "error", err, "output", string(output))
		return fmt.Errorf("构建镜像失败: %w", err)
	}

	d.logger.Info("Docker 镜像构建完成", "tags", options.Tags, "output", string(output))
	return nil
}

// PushImage 推送 Docker 镜像
func (d *DefaultDockerClient) PushImage(ctx context.Context, image string, options PushImageOptions) error {
	d.logger.Info("开始推送 Docker 镜像", "image", image, "registry", options.Registry.ServerAddress)

	// 使用 docker push 命令推送镜像
	cmd := exec.CommandContext(ctx, "docker", "push", image)
	output, err := cmd.CombinedOutput()
	if err != nil {
		d.logger.Error("Docker 推送失败", "error", err, "output", string(output))
		return fmt.Errorf("推送镜像失败: %w", err)
	}

	d.logger.Info("Docker 镜像推送完成", "image", image, "output", string(output))
	return nil
}

// RunContainer 运行容器
func (d *DefaultDockerClient) RunContainer(ctx context.Context, config *ContainerConfig) (*ContainerInfo, error) {
	d.logger.Info("开始运行容器", "image", config.Image, "name", config.Name)

	// 构建 docker run 命令
	args := []string{"run", "-d"}

	if config.Name != "" {
		args = append(args, "--name", config.Name)
	}

	// 添加环境变量
	for _, env := range config.Env {
		args = append(args, "-e", env)
	}

	// 添加端口映射
	for containerPort, hostPort := range config.Ports {
		args = append(args, "-p", fmt.Sprintf("%s:%s", hostPort, containerPort))
	}

	// 添加数据卷挂载
	for hostPath, containerPath := range config.Volumes {
		args = append(args, "-v", fmt.Sprintf("%s:%s", hostPath, containerPath))
	}

	// 添加工作目录
	if config.WorkingDir != "" {
		args = append(args, "-w", config.WorkingDir)
	}

	// 添加镜像和命令
	args = append(args, config.Image)
	args = append(args, config.Cmd...)

	// 执行命令
	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		d.logger.Error("容器启动失败", "error", err, "output", string(output))
		return nil, fmt.Errorf("启动容器失败: %w", err)
	}

	containerID := strings.TrimSpace(string(output))

	// 创建容器信息
	containerInfo := &ContainerInfo{
		ID:      containerID,
		Name:    config.Name,
		Image:   config.Image,
		Status:  "running",
		Created: time.Now(),
		Started: time.Now(),
	}

	d.logger.Info("容器运行成功", "container_id", containerID, "name", config.Name)
	return containerInfo, nil
}

// StopContainer 停止容器
func (d *DefaultDockerClient) StopContainer(ctx context.Context, containerID string) error {
	d.logger.Info("停止容器", "container_id", containerID)

	cmd := exec.CommandContext(ctx, "docker", "stop", containerID)
	output, err := cmd.CombinedOutput()
	if err != nil {
		d.logger.Error("停止容器失败", "error", err, "output", string(output))
		return fmt.Errorf("停止容器失败: %w", err)
	}

	d.logger.Info("容器已停止", "container_id", containerID)
	return nil
}

// RemoveContainer 删除容器
func (d *DefaultDockerClient) RemoveContainer(ctx context.Context, containerID string) error {
	d.logger.Info("删除容器", "container_id", containerID)

	cmd := exec.CommandContext(ctx, "docker", "rm", "-f", containerID)
	output, err := cmd.CombinedOutput()
	if err != nil {
		d.logger.Error("删除容器失败", "error", err, "output", string(output))
		return fmt.Errorf("删除容器失败: %w", err)
	}

	d.logger.Info("容器已删除", "container_id", containerID)
	return nil
}

// GetContainerLogs 获取容器日志
func (d *DefaultDockerClient) GetContainerLogs(ctx context.Context, containerID string) (string, error) {
	cmd := exec.CommandContext(ctx, "docker", "logs", containerID)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("获取容器日志失败: %w", err)
	}

	return string(output), nil
}

// CreateBuildContext 创建构建上下文
func CreateBuildContext(contextPath string, dockerfilePath string) (io.Reader, error) {
	// 如果是单个 Dockerfile，创建简单的 tar 包
	if dockerfilePath != "" && contextPath == "" {
		return createDockerfileContext(dockerfilePath)
	}

	// 创建目录上下文的 tar 包
	return createDirectoryContext(contextPath, dockerfilePath)
}

// createDockerfileContext 创建 Dockerfile 上下文
func createDockerfileContext(dockerfilePath string) (io.Reader, error) {
	dockerfileContent, err := os.ReadFile(dockerfilePath)
	if err != nil {
		return nil, fmt.Errorf("读取 Dockerfile 失败: %w", err)
	}

	var buf bytes.Buffer
	tw := tar.NewWriter(&buf)

	// 添加 Dockerfile 到 tar 包
	header := &tar.Header{
		Name: "Dockerfile",
		Size: int64(len(dockerfileContent)),
		Mode: 0644,
	}

	if err := tw.WriteHeader(header); err != nil {
		return nil, fmt.Errorf("写入 tar 头失败: %w", err)
	}

	if _, err := tw.Write(dockerfileContent); err != nil {
		return nil, fmt.Errorf("写入 Dockerfile 内容失败: %w", err)
	}

	if err := tw.Close(); err != nil {
		return nil, fmt.Errorf("关闭 tar writer 失败: %w", err)
	}

	return &buf, nil
}

// createDirectoryContext 创建目录上下文
func createDirectoryContext(contextPath string, dockerfilePath string) (io.Reader, error) {
	// 简化实现，直接返回空的 tar 包
	var buf bytes.Buffer
	tw := tar.NewWriter(&buf)

	// 添加一个空文件作为占位符
	header := &tar.Header{
		Name: ".dockerignore",
		Size: 0,
		Mode: 0644,
	}

	if err := tw.WriteHeader(header); err != nil {
		return nil, fmt.Errorf("写入 tar 头失败: %w", err)
	}

	if err := tw.Close(); err != nil {
		return nil, fmt.Errorf("关闭 tar writer 失败: %w", err)
	}

	return &buf, nil
}
