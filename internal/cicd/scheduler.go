package cicd

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BuildScheduler 构建调度器接口
type BuildScheduler interface {
	// Start 启动调度器
	Start(ctx context.Context) error
	
	// Stop 停止调度器
	Stop() error
	
	// EnqueueBuild 将构建任务加入队列
	EnqueueBuild(ctx context.Context, buildID string, priority int) error
	
	// GetQueueStatus 获取队列状态
	GetQueueStatus(ctx context.Context) (*BuildQueueStatus, error)
	
	// AssignBuildToNode 将构建任务分配给节点
	AssignBuildToNode(ctx context.Context, buildID, nodeID string) error
}

// DefaultBuildScheduler 默认构建调度器实现
type DefaultBuildScheduler struct {
	db       *gorm.DB
	service  Service
	executor BuildExecutor
	logger   Logger
	
	// 调度器配置
	maxConcurrentBuilds int
	pollInterval        time.Duration
	
	// 运行状态
	running    bool
	stopChan   chan struct{}
	workerPool chan struct{}
	mutex      sync.RWMutex
}

// BuildQueueStatus 构建队列状态
type BuildQueueStatus struct {
	WaitingBuilds  int `json:"waiting_builds"`
	RunningBuilds  int `json:"running_builds"`
	CompletedBuilds int `json:"completed_builds"`
	FailedBuilds   int `json:"failed_builds"`
	TotalNodes     int `json:"total_nodes"`
	OnlineNodes    int `json:"online_nodes"`
	BusyNodes      int `json:"busy_nodes"`
}

// NewBuildScheduler 创建构建调度器
func NewBuildScheduler(db *gorm.DB, service Service, executor BuildExecutor, logger Logger) BuildScheduler {
	return &DefaultBuildScheduler{
		db:                  db,
		service:             service,
		executor:            executor,
		logger:              logger,
		maxConcurrentBuilds: 10, // 默认最大并发构建数
		pollInterval:        5 * time.Second, // 默认轮询间隔
		stopChan:            make(chan struct{}),
		workerPool:          make(chan struct{}, 10), // 工作池大小
	}
}

// Start 启动调度器
func (s *DefaultBuildScheduler) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if s.running {
		return fmt.Errorf("调度器已经在运行")
	}
	
	s.running = true
	s.logger.Info("构建调度器启动", "max_concurrent", s.maxConcurrentBuilds, "poll_interval", s.pollInterval)
	
	// 启动调度循环
	go s.scheduleLoop(ctx)
	
	// 启动节点健康检查
	go s.nodeHealthCheck(ctx)
	
	return nil
}

// Stop 停止调度器
func (s *DefaultBuildScheduler) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if !s.running {
		return fmt.Errorf("调度器未运行")
	}
	
	s.running = false
	close(s.stopChan)
	
	s.logger.Info("构建调度器已停止")
	return nil
}

// EnqueueBuild 将构建任务加入队列
func (s *DefaultBuildScheduler) EnqueueBuild(ctx context.Context, buildID string, priority int) error {
	// 检查构建是否已在队列中
	var existingQueue BuildQueue
	err := s.db.Where("build_id = ?", buildID).First(&existingQueue).Error
	if err == nil {
		return fmt.Errorf("构建任务已在队列中: %s", buildID)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查队列状态失败: %w", err)
	}
	
	// 创建队列记录
	queue := &BuildQueue{
		ID:        uuid.New().String(),
		BuildID:   buildID,
		Priority:  priority,
		Status:    QueueStatusWaiting,
		CreatedAt: time.Now(),
	}
	
	if err := s.db.Create(queue).Error; err != nil {
		return fmt.Errorf("构建任务入队失败: %w", err)
	}
	
	s.logger.Info("构建任务已入队", "build_id", buildID, "priority", priority, "queue_id", queue.ID)
	return nil
}

// GetQueueStatus 获取队列状态
func (s *DefaultBuildScheduler) GetQueueStatus(ctx context.Context) (*BuildQueueStatus, error) {
	status := &BuildQueueStatus{}
	
	// 统计队列中的构建任务
	var waitingCount, runningCount, completedCount, failedCount int64
	
	s.db.Model(&BuildQueue{}).Where("status = ?", QueueStatusWaiting).Count(&waitingCount)
	s.db.Model(&BuildQueue{}).Where("status = ?", QueueStatusRunning).Count(&runningCount)
	s.db.Model(&BuildQueue{}).Where("status = ?", QueueStatusCompleted).Count(&completedCount)
	s.db.Model(&BuildQueue{}).Where("status = ?", QueueStatusFailed).Count(&failedCount)
	
	status.WaitingBuilds = int(waitingCount)
	status.RunningBuilds = int(runningCount)
	status.CompletedBuilds = int(completedCount)
	status.FailedBuilds = int(failedCount)
	
	// 统计构建节点
	var totalNodes, onlineNodes, busyNodes int64
	
	s.db.Model(&BuildNode{}).Count(&totalNodes)
	s.db.Model(&BuildNode{}).Where("status = ?", NodeStatusOnline).Count(&onlineNodes)
	s.db.Model(&BuildNode{}).Where("status = ?", NodeStatusBusy).Count(&busyNodes)
	
	status.TotalNodes = int(totalNodes)
	status.OnlineNodes = int(onlineNodes)
	status.BusyNodes = int(busyNodes)
	
	return status, nil
}

// AssignBuildToNode 将构建任务分配给节点
func (s *DefaultBuildScheduler) AssignBuildToNode(ctx context.Context, buildID, nodeID string) error {
	// 更新队列记录
	err := s.db.Model(&BuildQueue{}).Where("build_id = ?", buildID).Updates(map[string]interface{}{
		"node_id":    nodeID,
		"status":     QueueStatusAssigned,
		"started_at": time.Now(),
	}).Error
	if err != nil {
		return fmt.Errorf("分配构建任务失败: %w", err)
	}
	
	// 更新节点负载
	err = s.db.Model(&BuildNode{}).Where("id = ?", nodeID).
		Update("current_load", gorm.Expr("current_load + ?", 1)).Error
	if err != nil {
		s.logger.Error("更新节点负载失败", "error", err, "node_id", nodeID)
	}
	
	s.logger.Info("构建任务已分配", "build_id", buildID, "node_id", nodeID)
	return nil
}

// scheduleLoop 调度循环
func (s *DefaultBuildScheduler) scheduleLoop(ctx context.Context) {
	ticker := time.NewTicker(s.pollInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			s.logger.Info("调度循环因上下文取消而停止")
			return
		case <-s.stopChan:
			s.logger.Info("调度循环因停止信号而停止")
			return
		case <-ticker.C:
			s.processQueue(ctx)
		}
	}
}

// processQueue 处理队列
func (s *DefaultBuildScheduler) processQueue(ctx context.Context) {
	// 获取等待中的构建任务（按优先级和创建时间排序）
	var waitingBuilds []BuildQueue
	err := s.db.Where("status = ?", QueueStatusWaiting).
		Order("priority DESC, created_at ASC").
		Limit(s.maxConcurrentBuilds).
		Find(&waitingBuilds).Error
	if err != nil {
		s.logger.Error("获取等待构建失败", "error", err)
		return
	}
	
	if len(waitingBuilds) == 0 {
		return
	}
	
	s.logger.Debug("处理等待构建", "count", len(waitingBuilds))
	
	// 获取可用的构建节点
	availableNodes, err := s.getAvailableNodes(ctx)
	if err != nil {
		s.logger.Error("获取可用节点失败", "error", err)
		return
	}
	
	if len(availableNodes) == 0 {
		s.logger.Debug("没有可用的构建节点")
		return
	}
	
	// 分配构建任务到节点
	for i, build := range waitingBuilds {
		if i >= len(availableNodes) {
			break // 没有更多可用节点
		}
		
		node := availableNodes[i]
		if err := s.assignAndExecuteBuild(ctx, &build, &node); err != nil {
			s.logger.Error("分配并执行构建失败", "error", err, "build_id", build.BuildID, "node_id", node.ID)
		}
	}
}

// getAvailableNodes 获取可用的构建节点
func (s *DefaultBuildScheduler) getAvailableNodes(ctx context.Context) ([]BuildNode, error) {
	var nodes []BuildNode
	err := s.db.Where("status = ? AND current_load < capacity", NodeStatusOnline).
		Order("current_load ASC").
		Find(&nodes).Error
	if err != nil {
		return nil, fmt.Errorf("获取可用节点失败: %w", err)
	}
	
	return nodes, nil
}

// assignAndExecuteBuild 分配并执行构建
func (s *DefaultBuildScheduler) assignAndExecuteBuild(ctx context.Context, queueItem *BuildQueue, node *BuildNode) error {
	// 分配构建任务到节点
	if err := s.AssignBuildToNode(ctx, queueItem.BuildID, node.ID); err != nil {
		return err
	}
	
	// 获取构建详情
	build, err := s.service.GetBuild(ctx, queueItem.BuildID)
	if err != nil {
		return fmt.Errorf("获取构建详情失败: %w", err)
	}
	
	// 在工作池中执行构建
	go func() {
		s.workerPool <- struct{}{} // 获取工作池槽位
		defer func() {
			<-s.workerPool // 释放工作池槽位
		}()
		
		// 更新队列状态为运行中
		s.db.Model(&BuildQueue{}).Where("build_id = ?", queueItem.BuildID).
			Update("status", QueueStatusRunning)
		
		// 执行构建
		if err := s.executor.ExecuteBuild(ctx, build); err != nil {
			s.logger.Error("构建执行失败", "error", err, "build_id", build.ID)
			
			// 更新队列状态为失败
			s.db.Model(&BuildQueue{}).Where("build_id = ?", queueItem.BuildID).
				Update("status", QueueStatusFailed)
		} else {
			s.logger.Info("构建执行成功", "build_id", build.ID)
			
			// 更新队列状态为完成
			s.db.Model(&BuildQueue{}).Where("build_id = ?", queueItem.BuildID).
				Update("status", QueueStatusCompleted)
		}
		
		// 减少节点负载
		s.db.Model(&BuildNode{}).Where("id = ?", node.ID).
			Update("current_load", gorm.Expr("current_load - ?", 1))
	}()
	
	return nil
}

// nodeHealthCheck 节点健康检查
func (s *DefaultBuildScheduler) nodeHealthCheck(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.checkNodeHealth(ctx)
		}
	}
}

// checkNodeHealth 检查节点健康状态
func (s *DefaultBuildScheduler) checkNodeHealth(ctx context.Context) {
	var nodes []BuildNode
	err := s.db.Find(&nodes).Error
	if err != nil {
		s.logger.Error("获取节点列表失败", "error", err)
		return
	}
	
	offlineThreshold := time.Now().Add(-5 * time.Minute) // 5分钟未心跳视为离线
	
	for _, node := range nodes {
		if node.LastSeen.Before(offlineThreshold) && node.Status != NodeStatusOffline {
			// 节点离线
			s.db.Model(&node).Update("status", NodeStatusOffline)
			s.logger.Warn("节点离线", "node_id", node.ID, "name", node.Name, "last_seen", node.LastSeen)
			
			// 取消该节点上的等待构建
			s.cancelNodeBuilds(ctx, node.ID)
		}
	}
}

// cancelNodeBuilds 取消节点上的构建任务
func (s *DefaultBuildScheduler) cancelNodeBuilds(ctx context.Context, nodeID string) {
	var queueItems []BuildQueue
	err := s.db.Where("node_id = ? AND status IN ?", nodeID, 
		[]QueueStatus{QueueStatusAssigned, QueueStatusRunning}).Find(&queueItems).Error
	if err != nil {
		s.logger.Error("获取节点构建任务失败", "error", err, "node_id", nodeID)
		return
	}
	
	for _, item := range queueItems {
		// 重新入队等待分配
		s.db.Model(&item).Updates(map[string]interface{}{
			"node_id":    nil,
			"status":     QueueStatusWaiting,
			"started_at": nil,
		})
		
		s.logger.Info("构建任务重新入队", "build_id", item.BuildID, "node_id", nodeID)
	}
}
