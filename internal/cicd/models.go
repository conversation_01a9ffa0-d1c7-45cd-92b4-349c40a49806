package cicd

import (
	"time"
)

// Pipeline 流水线定义
type Pipeline struct {
	ID          string           `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string           `json:"name" gorm:"type:varchar(100);not null"`
	Description string           `json:"description" gorm:"type:text"`
	AppID       string           `json:"app_id" gorm:"type:varchar(36);not null;index"`
	Config      PipelineConfig   `json:"config" gorm:"serializer:json"`
	Status      PipelineStatus   `json:"status" gorm:"type:varchar(20);not null;default:active"`
	TenantID    string           `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	CreatedAt   time.Time        `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time        `json:"updated_at" gorm:"autoUpdateTime"`
	
	// 关联关系
	Builds []Build `json:"builds,omitempty" gorm:"foreignKey:PipelineID"`
}

// PipelineStatus 流水线状态
type PipelineStatus string

const (
	PipelineStatusActive   PipelineStatus = "active"   // 活跃
	PipelineStatusInactive PipelineStatus = "inactive" // 非活跃
	PipelineStatusDeleted  PipelineStatus = "deleted"  // 已删除
)

// PipelineConfig 流水线配置
type PipelineConfig struct {
	APIVersion    string                 `json:"apiVersion"`
	Kind          string                 `json:"kind"`
	Metadata      PipelineMetadata       `json:"metadata"`
	Spec          PipelineSpec           `json:"spec"`
}

// PipelineMetadata 流水线元数据
type PipelineMetadata struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Labels      map[string]string `json:"labels"`
}

// PipelineSpec 流水线规格
type PipelineSpec struct {
	Triggers      []Trigger             `json:"triggers"`
	Env           map[string]string     `json:"env"`
	Secrets       []SecretRef           `json:"secrets"`
	Stages        []Stage               `json:"stages"`
	Notifications []Notification        `json:"notifications"`
	Cache         []CacheConfig         `json:"cache"`
	Services      []ServiceConfig       `json:"services"`
}

// Trigger 触发器
type Trigger struct {
	Type     string   `json:"type"`     // push, pull_request, tag, schedule
	Branches []string `json:"branches"` // 分支过滤
	Pattern  string   `json:"pattern"`  // 模式匹配
	Cron     string   `json:"cron"`     // 定时表达式
}

// SecretRef 密钥引用
type SecretRef struct {
	Name      string   `json:"name"`
	Keys      []string `json:"keys"`
	MountPath string   `json:"mount_path"`
}

// Stage 构建阶段
type Stage struct {
	Name      string   `json:"name"`
	DependsOn []string `json:"depends_on"`
	When      When     `json:"when"`
	Steps     []Step   `json:"steps"`
}

// When 执行条件
type When struct {
	Branch string `json:"branch"`
	Event  string `json:"event"`
	Status string `json:"status"`
}

// Step 构建步骤
type Step struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`
	Config       StepConfig  `json:"config"`
	AllowFailure bool        `json:"allow_failure"`
	Timeout      string      `json:"timeout"`
	Parallel     bool        `json:"parallel"`
	Artifacts    Artifacts   `json:"artifacts"`
}

// StepConfig 步骤配置
type StepConfig struct {
	Commands         []string          `json:"commands"`
	WorkingDirectory string            `json:"working_directory"`
	Dockerfile       string            `json:"dockerfile"`
	Context          string            `json:"context"`
	Tags             []string          `json:"tags"`
	BuildArgs        map[string]string `json:"build_args"`
	Registry         RegistryConfig    `json:"registry"`
	Environment      string            `json:"environment"`
	Image            string            `json:"image"`
	Strategy         string            `json:"strategy"`
	Approval         string            `json:"approval"`
}

// RegistryConfig 镜像仓库配置
type RegistryConfig struct {
	URL      string `json:"url"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// Artifacts 构建产物
type Artifacts struct {
	Paths    []string `json:"paths"`
	ExpireIn string   `json:"expire_in"`
}

// Notification 通知配置
type Notification struct {
	Type       string   `json:"type"`       // webhook, email, slack
	URL        string   `json:"url"`
	Recipients []string `json:"recipients"`
	Events     []string `json:"events"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Key   string   `json:"key"`
	Paths []string `json:"paths"`
}

// ServiceConfig 服务配置 (用于集成测试)
type ServiceConfig struct {
	Name  string            `json:"name"`
	Image string            `json:"image"`
	Env   map[string]string `json:"env"`
}

// Build 构建任务
type Build struct {
	ID           string      `json:"id" gorm:"primaryKey;type:varchar(36)"`
	PipelineID   string      `json:"pipeline_id" gorm:"type:varchar(36);not null;index"`
	AppID        string      `json:"app_id" gorm:"type:varchar(36);not null;index"`
	Number       int         `json:"number" gorm:"not null"`                    // 构建编号
	Branch       string      `json:"branch" gorm:"type:varchar(100);not null"`
	CommitHash   string      `json:"commit_hash" gorm:"type:varchar(40);not null"`
	CommitMsg    string      `json:"commit_msg" gorm:"type:text"`
	Author       string      `json:"author" gorm:"type:varchar(100)"`
	Status       BuildStatus `json:"status" gorm:"type:varchar(20);not null;default:pending"`
	TriggerType  string      `json:"trigger_type" gorm:"type:varchar(20);not null"` // push, pr, tag, manual, schedule
	TriggerBy    string      `json:"trigger_by" gorm:"type:varchar(36)"`             // 触发用户ID
	StartTime    time.Time   `json:"start_time" gorm:"autoCreateTime"`
	EndTime      *time.Time  `json:"end_time"`
	Duration     int         `json:"duration"`     // 构建时长 (秒)
	ImageTag     string      `json:"image_tag" gorm:"type:varchar(200)"`
	BuildLog     string      `json:"build_log" gorm:"type:text"`
	ErrorMsg     string      `json:"error_msg" gorm:"type:text"`
	Artifacts    []Artifact  `json:"artifacts" gorm:"type:jsonb"`
	
	// 关联关系
	Pipeline Pipeline    `json:"pipeline,omitempty" gorm:"foreignKey:PipelineID"`
	Stages   []BuildStage `json:"stages,omitempty" gorm:"foreignKey:BuildID"`
}

// BuildStatus 构建状态
type BuildStatus string

const (
	BuildStatusPending  BuildStatus = "pending"   // 等待中
	BuildStatusRunning  BuildStatus = "running"   // 运行中
	BuildStatusSuccess  BuildStatus = "success"   // 成功
	BuildStatusFailed   BuildStatus = "failed"    // 失败
	BuildStatusCanceled BuildStatus = "canceled"  // 已取消
	BuildStatusTimeout  BuildStatus = "timeout"   // 超时
	BuildStatusRetrying BuildStatus = "retrying"  // 重试中
)

// BuildStage 构建阶段
type BuildStage struct {
	ID        string           `json:"id" gorm:"primaryKey;type:varchar(36)"`
	BuildID   string           `json:"build_id" gorm:"type:varchar(36);not null;index"`
	Name      string           `json:"name" gorm:"type:varchar(100);not null"`
	Status    BuildStageStatus `json:"status" gorm:"type:varchar(20);not null;default:pending"`
	StartTime *time.Time       `json:"start_time"`
	EndTime   *time.Time       `json:"end_time"`
	Duration  int              `json:"duration"` // 阶段时长 (秒)
	Log       string           `json:"log" gorm:"type:text"`
	ErrorMsg  string           `json:"error_msg" gorm:"type:text"`
	
	// 关联关系
	Build Build     `json:"build,omitempty" gorm:"foreignKey:BuildID"`
	Steps []BuildStep `json:"steps,omitempty" gorm:"foreignKey:StageID"`
}

// BuildStageStatus 构建阶段状态
type BuildStageStatus string

const (
	BuildStageStatusPending  BuildStageStatus = "pending"  // 等待中
	BuildStageStatusRunning  BuildStageStatus = "running"  // 运行中
	BuildStageStatusSuccess  BuildStageStatus = "success"  // 成功
	BuildStageStatusFailed   BuildStageStatus = "failed"   // 失败
	BuildStageStatusSkipped  BuildStageStatus = "skipped"  // 跳过
	BuildStageStatusCanceled BuildStageStatus = "canceled" // 已取消
)

// BuildStep 构建步骤
type BuildStep struct {
	ID        string          `json:"id" gorm:"primaryKey;type:varchar(36)"`
	StageID   string          `json:"stage_id" gorm:"type:varchar(36);not null;index"`
	Name      string          `json:"name" gorm:"type:varchar(100);not null"`
	Type      string          `json:"type" gorm:"type:varchar(50);not null"`
	Status    BuildStepStatus `json:"status" gorm:"type:varchar(20);not null;default:pending"`
	StartTime *time.Time      `json:"start_time"`
	EndTime   *time.Time      `json:"end_time"`
	Duration  int             `json:"duration"` // 步骤时长 (秒)
	Log       string          `json:"log" gorm:"type:text"`
	ErrorMsg  string          `json:"error_msg" gorm:"type:text"`
	ExitCode  int             `json:"exit_code"`
	
	// 关联关系
	Stage BuildStage `json:"stage,omitempty" gorm:"foreignKey:StageID"`
}

// BuildStepStatus 构建步骤状态
type BuildStepStatus string

const (
	BuildStepStatusPending  BuildStepStatus = "pending"  // 等待中
	BuildStepStatusRunning  BuildStepStatus = "running"  // 运行中
	BuildStepStatusSuccess  BuildStepStatus = "success"  // 成功
	BuildStepStatusFailed   BuildStepStatus = "failed"   // 失败
	BuildStepStatusSkipped  BuildStepStatus = "skipped"  // 跳过
	BuildStepStatusCanceled BuildStepStatus = "canceled" // 已取消
)

// Artifact 构建产物
type Artifact struct {
	Name     string    `json:"name"`
	Path     string    `json:"path"`
	Size     int64     `json:"size"`
	Type     string    `json:"type"`     // file, archive, image
	URL      string    `json:"url"`
	ExpireAt time.Time `json:"expire_at"`
}

// Webhook Webhook 事件
type Webhook struct {
	ID        string      `json:"id" gorm:"primaryKey;type:varchar(36)"`
	AppID     string      `json:"app_id" gorm:"type:varchar(36);not null;index"`
	Source    string      `json:"source" gorm:"type:varchar(50);not null"` // gitea, github, gitlab
	Event     string      `json:"event" gorm:"type:varchar(50);not null"`  // push, pull_request, tag
	Payload   interface{} `json:"payload" gorm:"type:jsonb"`
	Processed bool        `json:"processed" gorm:"default:false"`
	CreatedAt time.Time   `json:"created_at" gorm:"autoCreateTime"`
}

// BuildNode 构建节点
type BuildNode struct {
	ID          string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string            `json:"name" gorm:"type:varchar(100);not null;uniqueIndex"`
	Address     string            `json:"address" gorm:"type:varchar(200);not null"`
	Capacity    int               `json:"capacity" gorm:"not null;default:5"`        // 并发构建数
	CurrentLoad int               `json:"current_load" gorm:"not null;default:0"`    // 当前负载
	Labels      map[string]string `json:"labels" gorm:"type:jsonb"`                  // 节点标签
	Status      NodeStatus        `json:"status" gorm:"type:varchar(20);not null;default:online"`
	LastSeen    time.Time         `json:"last_seen" gorm:"autoUpdateTime"`
	CreatedAt   time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
}

// NodeStatus 节点状态
type NodeStatus string

const (
	NodeStatusOnline  NodeStatus = "online"  // 在线
	NodeStatusOffline NodeStatus = "offline" // 离线
	NodeStatusBusy    NodeStatus = "busy"    // 繁忙
	NodeStatusError   NodeStatus = "error"   // 错误
)

// BuildQueue 构建队列
type BuildQueue struct {
	ID         string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	BuildID    string    `json:"build_id" gorm:"type:varchar(36);not null;uniqueIndex"`
	Priority   int       `json:"priority" gorm:"not null;default:0"`     // 优先级，数字越大优先级越高
	NodeID     string    `json:"node_id" gorm:"type:varchar(36);index"`  // 分配的节点ID
	Status     QueueStatus `json:"status" gorm:"type:varchar(20);not null;default:waiting"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	StartedAt  *time.Time `json:"started_at"`
	
	// 关联关系
	Build Build     `json:"build,omitempty" gorm:"foreignKey:BuildID"`
	Node  BuildNode `json:"node,omitempty" gorm:"foreignKey:NodeID"`
}

// QueueStatus 队列状态
type QueueStatus string

const (
	QueueStatusWaiting  QueueStatus = "waiting"  // 等待中
	QueueStatusAssigned QueueStatus = "assigned" // 已分配
	QueueStatusRunning  QueueStatus = "running"  // 运行中
	QueueStatusCompleted QueueStatus = "completed" // 已完成
	QueueStatusFailed   QueueStatus = "failed"   // 失败
	QueueStatusCanceled QueueStatus = "canceled" // 已取消
)

// Deployment 部署记录
type Deployment struct {
	ID            string           `json:"id" gorm:"primaryKey;type:varchar(36)"`
	AppID         string           `json:"app_id" gorm:"type:varchar(36);not null;index"`
	BuildID       string           `json:"build_id" gorm:"type:varchar(36);not null;index"`
	Environment   string           `json:"environment" gorm:"type:varchar(50);not null"`
	Version       string           `json:"version" gorm:"type:varchar(20);not null"`
	ImageTag      string           `json:"image_tag" gorm:"type:varchar(200);not null"`
	Strategy      string           `json:"strategy" gorm:"type:varchar(20);not null"` // rolling, blue_green, canary
	Status        DeploymentStatus `json:"status" gorm:"type:varchar(20);not null;default:pending"`
	Config        DeploymentConfig `json:"config" gorm:"type:jsonb"`
	StartTime     time.Time        `json:"start_time" gorm:"autoCreateTime"`
	EndTime       *time.Time       `json:"end_time"`
	Duration      int              `json:"duration"` // 部署时长 (秒)
	DeployLog     string           `json:"deploy_log" gorm:"type:text"`
	ErrorMsg      string           `json:"error_msg" gorm:"type:text"`
	RollbackFrom  string           `json:"rollback_from" gorm:"type:varchar(36)"` // 回滚来源部署ID
	
	// 关联关系
	Build Build `json:"build,omitempty" gorm:"foreignKey:BuildID"`
}

// DeploymentStatus 部署状态
type DeploymentStatus string

const (
	DeploymentStatusPending    DeploymentStatus = "pending"     // 等待中
	DeploymentStatusRunning    DeploymentStatus = "running"     // 部署中
	DeploymentStatusSuccess    DeploymentStatus = "success"     // 成功
	DeploymentStatusFailed     DeploymentStatus = "failed"      // 失败
	DeploymentStatusRolledBack DeploymentStatus = "rolled_back" // 已回滚
	DeploymentStatusCanceled   DeploymentStatus = "canceled"    // 已取消
)

// DeploymentConfig 部署配置
type DeploymentConfig struct {
	Replicas    int               `json:"replicas"`
	Resources   ResourceConfig    `json:"resources"`
	Env         map[string]string `json:"env"`
	Secrets     []SecretRef       `json:"secrets"`
	Volumes     []VolumeConfig    `json:"volumes"`
	HealthCheck HealthCheckConfig `json:"health_check"`
}

// ResourceConfig 资源配置
type ResourceConfig struct {
	Requests ResourceSpec `json:"requests"`
	Limits   ResourceSpec `json:"limits"`
}

// ResourceSpec 资源规格
type ResourceSpec struct {
	CPU    string `json:"cpu"`
	Memory string `json:"memory"`
	Disk   string `json:"disk"`
}

// VolumeConfig 存储卷配置
type VolumeConfig struct {
	Name      string `json:"name"`
	Type      string `json:"type"`
	Size      string `json:"size"`
	MountPath string `json:"mount_path"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Path                string `json:"path"`
	Port                int    `json:"port"`
	InitialDelaySeconds int    `json:"initial_delay_seconds"`
	PeriodSeconds       int    `json:"period_seconds"`
	TimeoutSeconds      int    `json:"timeout_seconds"`
	FailureThreshold    int    `json:"failure_threshold"`
}

// Environment 环境配置
type Environment struct {
	ID          string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string            `json:"name" gorm:"type:varchar(50);not null;uniqueIndex"`
	Description string            `json:"description" gorm:"type:text"`
	Type        string            `json:"type" gorm:"type:varchar(20);not null"` // development, staging, production
	Cluster     string            `json:"cluster" gorm:"type:varchar(100)"`
	Namespace   string            `json:"namespace" gorm:"type:varchar(100)"`
	Config      EnvironmentConfig `json:"config" gorm:"type:jsonb"`
	Status      string            `json:"status" gorm:"type:varchar(20);not null;default:active"`
	TenantID    string            `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
	CreatedAt   time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
}

// EnvironmentConfig 环境配置
type EnvironmentConfig struct {
	DefaultReplicas int               `json:"default_replicas"`
	Resources       ResourceConfig    `json:"resources"`
	Env             map[string]string `json:"env"`
	Secrets         []SecretRef       `json:"secrets"`
	Approval        ApprovalConfig    `json:"approval"`
}

// ApprovalConfig 审批配置
type ApprovalConfig struct {
	Required  bool     `json:"required"`
	Approvers []string `json:"approvers"` // 审批人用户ID列表
	Timeout   string   `json:"timeout"`   // 审批超时时间
}

// TableName 指定表名
func (Pipeline) TableName() string {
	return "pipelines"
}

func (Build) TableName() string {
	return "builds"
}

func (BuildStage) TableName() string {
	return "build_stages"
}

func (BuildStep) TableName() string {
	return "build_steps"
}

func (Webhook) TableName() string {
	return "webhooks"
}

func (BuildNode) TableName() string {
	return "build_nodes"
}

func (BuildQueue) TableName() string {
	return "build_queue"
}

func (Deployment) TableName() string {
	return "deployments"
}

func (Environment) TableName() string {
	return "environments"
}
