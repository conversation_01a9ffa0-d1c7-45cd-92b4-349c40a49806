package cicd

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/redis/go-redis/v9"
)

// BuildLogger 构建日志管理器接口
type BuildLogger interface {
	// StartBuildLogging 开始构建日志记录
	StartBuildLogging(ctx context.Context, buildID string) error
	
	// StopBuildLogging 停止构建日志记录
	StopBuildLogging(ctx context.Context, buildID string) error
	
	// AppendLog 追加日志
	AppendLog(ctx context.Context, buildID string, entry *BuildLogEntry) error
	
	// GetLogs 获取构建日志
	GetLogs(ctx context.Context, buildID string, options *LogQueryOptions) ([]*BuildLogEntry, error)
	
	// StreamLogs 流式获取日志
	StreamLogs(ctx context.Context, buildID string, conn *websocket.Conn) error
	
	// SubscribeToLogs 订阅日志更新
	SubscribeToLogs(ctx context.Context, buildID string) (<-chan *BuildLogEntry, error)
}

// DefaultBuildLogger 默认构建日志管理器实现
type DefaultBuildLogger struct {
	redis    *redis.Client
	logger   Logger
	
	// WebSocket 连接管理
	connections map[string][]*websocket.Conn
	connMutex   sync.RWMutex
	
	// 日志缓冲区
	logBuffers map[string][]*BuildLogEntry
	bufferMutex sync.RWMutex
	bufferSize  int
}

// LogQueryOptions 日志查询选项
type LogQueryOptions struct {
	StartTime  *time.Time `json:"start_time"`  // 开始时间
	EndTime    *time.Time `json:"end_time"`    // 结束时间
	Level      string     `json:"level"`       // 日志级别过滤
	Stage      string     `json:"stage"`       // 阶段过滤
	Step       string     `json:"step"`        // 步骤过滤
	Limit      int        `json:"limit"`       // 限制数量
	Offset     int        `json:"offset"`      // 偏移量
	Follow     bool       `json:"follow"`      // 是否跟随日志
}



// LogStreamMessage WebSocket 日志流消息
type LogStreamMessage struct {
	Type    string         `json:"type"`    // 消息类型: log, status, error
	BuildID string         `json:"build_id"` // 构建ID
	Entry   *BuildLogEntry `json:"entry"`   // 日志条目
	Status  string         `json:"status"`  // 构建状态
	Error   string         `json:"error"`   // 错误信息
}

// NewBuildLogger 创建构建日志管理器
func NewBuildLogger(redisClient *redis.Client, logger Logger) BuildLogger {
	return &DefaultBuildLogger{
		redis:       redisClient,
		logger:      logger,
		connections: make(map[string][]*websocket.Conn),
		logBuffers:  make(map[string][]*BuildLogEntry),
		bufferSize:  1000, // 默认缓冲区大小
	}
}

// StartBuildLogging 开始构建日志记录
func (bl *DefaultBuildLogger) StartBuildLogging(ctx context.Context, buildID string) error {
	bl.logger.Info("开始构建日志记录", "build_id", buildID)
	
	// 初始化日志缓冲区
	bl.bufferMutex.Lock()
	bl.logBuffers[buildID] = make([]*BuildLogEntry, 0, bl.bufferSize)
	bl.bufferMutex.Unlock()
	
	// 记录开始日志
	startEntry := &BuildLogEntry{
		BuildID:   buildID,
		Timestamp: time.Now().Format(time.RFC3339),
		Level:     "INFO",
		Stage:     "system",
		Step:      "start",
		Message:   "构建日志记录开始",
	}
	
	return bl.AppendLog(ctx, buildID, startEntry)
}

// StopBuildLogging 停止构建日志记录
func (bl *DefaultBuildLogger) StopBuildLogging(ctx context.Context, buildID string) error {
	bl.logger.Info("停止构建日志记录", "build_id", buildID)
	
	// 记录结束日志
	endEntry := &BuildLogEntry{
		BuildID:   buildID,
		Timestamp: time.Now().Format(time.RFC3339),
		Level:     "INFO",
		Stage:     "system",
		Step:      "end",
		Message:   "构建日志记录结束",
	}
	
	if err := bl.AppendLog(ctx, buildID, endEntry); err != nil {
		return err
	}
	
	// 清理缓冲区
	bl.bufferMutex.Lock()
	delete(bl.logBuffers, buildID)
	bl.bufferMutex.Unlock()
	
	// 关闭 WebSocket 连接
	bl.closeWebSocketConnections(buildID)
	
	return nil
}

// AppendLog 追加日志
func (bl *DefaultBuildLogger) AppendLog(ctx context.Context, buildID string, entry *BuildLogEntry) error {
	// 设置时间戳
	if entry.Timestamp == "" {
		entry.Timestamp = time.Now().Format(time.RFC3339)
	}
	
	// 存储到 Redis
	if err := bl.storeLogToRedis(ctx, buildID, entry); err != nil {
		bl.logger.Error("存储日志到 Redis 失败", "error", err, "build_id", buildID)
		return err
	}
	
	// 添加到缓冲区
	bl.addToBuffer(buildID, entry)
	
	// 广播到 WebSocket 连接
	bl.broadcastToWebSockets(buildID, entry)
	
	// 发布到 Redis 频道用于实时订阅
	bl.publishLogUpdate(ctx, buildID, entry)
	
	return nil
}

// GetLogs 获取构建日志
func (bl *DefaultBuildLogger) GetLogs(ctx context.Context, buildID string, options *LogQueryOptions) ([]*BuildLogEntry, error) {
	bl.logger.Debug("获取构建日志", "build_id", buildID)
	
	// 从 Redis 获取日志
	logs, err := bl.getLogsFromRedis(ctx, buildID, options)
	if err != nil {
		return nil, fmt.Errorf("从 Redis 获取日志失败: %w", err)
	}
	
	// 如果 Redis 中没有日志，尝试从缓冲区获取
	if len(logs) == 0 {
		logs = bl.getLogsFromBuffer(buildID, options)
	}
	
	return logs, nil
}

// StreamLogs 流式获取日志
func (bl *DefaultBuildLogger) StreamLogs(ctx context.Context, buildID string, conn *websocket.Conn) error {
	bl.logger.Info("开始日志流", "build_id", buildID)
	
	// 注册 WebSocket 连接
	bl.registerWebSocketConnection(buildID, conn)
	defer bl.unregisterWebSocketConnection(buildID, conn)
	
	// 发送历史日志
	if err := bl.sendHistoryLogs(ctx, buildID, conn); err != nil {
		bl.logger.Error("发送历史日志失败", "error", err, "build_id", buildID)
		return err
	}
	
	// 保持连接活跃
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// 读取客户端消息（心跳检测）
			_, _, err := conn.ReadMessage()
			if err != nil {
				bl.logger.Debug("WebSocket 连接断开", "build_id", buildID, "error", err)
				return err
			}
		}
	}
}

// SubscribeToLogs 订阅日志更新
func (bl *DefaultBuildLogger) SubscribeToLogs(ctx context.Context, buildID string) (<-chan *BuildLogEntry, error) {
	// 订阅 Redis 频道
	pubsub := bl.redis.Subscribe(ctx, bl.getLogChannelName(buildID))
	
	logChan := make(chan *BuildLogEntry, 100)
	
	go func() {
		defer close(logChan)
		defer pubsub.Close()
		
		for {
			select {
			case <-ctx.Done():
				return
			case msg := <-pubsub.Channel():
				var entry BuildLogEntry
				if err := json.Unmarshal([]byte(msg.Payload), &entry); err != nil {
					bl.logger.Error("解析日志消息失败", "error", err)
					continue
				}
				
				select {
				case logChan <- &entry:
				case <-ctx.Done():
					return
				}
			}
		}
	}()
	
	return logChan, nil
}

// 私有方法

// storeLogToRedis 存储日志到 Redis
func (bl *DefaultBuildLogger) storeLogToRedis(ctx context.Context, buildID string, entry *BuildLogEntry) error {
	// 序列化日志条目
	data, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("序列化日志条目失败: %w", err)
	}
	
	// 使用有序集合存储日志，以时间戳为分数
	key := bl.getLogKey(buildID)
	timestamp, _ := time.Parse(time.RFC3339, entry.Timestamp)
	score := float64(timestamp.UnixNano())
	
	if err := bl.redis.ZAdd(ctx, key, redis.Z{
		Score:  score,
		Member: data,
	}).Err(); err != nil {
		return fmt.Errorf("存储日志到 Redis 失败: %w", err)
	}
	
	// 设置过期时间（7天）
	bl.redis.Expire(ctx, key, 7*24*time.Hour)
	
	return nil
}

// getLogsFromRedis 从 Redis 获取日志
func (bl *DefaultBuildLogger) getLogsFromRedis(ctx context.Context, buildID string, options *LogQueryOptions) ([]*BuildLogEntry, error) {
	key := bl.getLogKey(buildID)
	
	// 构建查询参数
	var min, max string = "-inf", "+inf"
	if options != nil {
		if options.StartTime != nil {
			min = fmt.Sprintf("%d", options.StartTime.UnixNano())
		}
		if options.EndTime != nil {
			max = fmt.Sprintf("%d", options.EndTime.UnixNano())
		}
	}
	
	// 查询日志
	result, err := bl.redis.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:    min,
		Max:    max,
		Offset: int64(getOffset(options)),
		Count:  int64(getLimit(options)),
	}).Result()
	if err != nil {
		return nil, fmt.Errorf("查询 Redis 日志失败: %w", err)
	}
	
	// 反序列化日志条目
	logs := make([]*BuildLogEntry, 0, len(result))
	for _, data := range result {
		var entry BuildLogEntry
		if err := json.Unmarshal([]byte(data), &entry); err != nil {
			bl.logger.Error("反序列化日志条目失败", "error", err)
			continue
		}
		
		// 应用过滤条件
		if bl.matchesFilter(&entry, options) {
			logs = append(logs, &entry)
		}
	}
	
	return logs, nil
}

// addToBuffer 添加到缓冲区
func (bl *DefaultBuildLogger) addToBuffer(buildID string, entry *BuildLogEntry) {
	bl.bufferMutex.Lock()
	defer bl.bufferMutex.Unlock()
	
	buffer, exists := bl.logBuffers[buildID]
	if !exists {
		return
	}
	
	// 添加到缓冲区
	buffer = append(buffer, entry)
	
	// 如果超过缓冲区大小，删除最旧的日志
	if len(buffer) > bl.bufferSize {
		buffer = buffer[1:]
	}
	
	bl.logBuffers[buildID] = buffer
}

// getLogsFromBuffer 从缓冲区获取日志
func (bl *DefaultBuildLogger) getLogsFromBuffer(buildID string, options *LogQueryOptions) []*BuildLogEntry {
	bl.bufferMutex.RLock()
	defer bl.bufferMutex.RUnlock()
	
	buffer, exists := bl.logBuffers[buildID]
	if !exists {
		return nil
	}
	
	// 应用过滤和分页
	var filtered []*BuildLogEntry
	for _, entry := range buffer {
		if bl.matchesFilter(entry, options) {
			filtered = append(filtered, entry)
		}
	}
	
	// 应用分页
	offset := getOffset(options)
	limit := getLimit(options)
	
	if offset >= len(filtered) {
		return nil
	}
	
	end := offset + limit
	if end > len(filtered) {
		end = len(filtered)
	}
	
	return filtered[offset:end]
}

// registerWebSocketConnection 注册 WebSocket 连接
func (bl *DefaultBuildLogger) registerWebSocketConnection(buildID string, conn *websocket.Conn) {
	bl.connMutex.Lock()
	defer bl.connMutex.Unlock()
	
	bl.connections[buildID] = append(bl.connections[buildID], conn)
}

// unregisterWebSocketConnection 注销 WebSocket 连接
func (bl *DefaultBuildLogger) unregisterWebSocketConnection(buildID string, conn *websocket.Conn) {
	bl.connMutex.Lock()
	defer bl.connMutex.Unlock()
	
	connections := bl.connections[buildID]
	for i, c := range connections {
		if c == conn {
			bl.connections[buildID] = append(connections[:i], connections[i+1:]...)
			break
		}
	}
	
	if len(bl.connections[buildID]) == 0 {
		delete(bl.connections, buildID)
	}
}

// broadcastToWebSockets 广播到 WebSocket 连接
func (bl *DefaultBuildLogger) broadcastToWebSockets(buildID string, entry *BuildLogEntry) {
	bl.connMutex.RLock()
	connections := bl.connections[buildID]
	bl.connMutex.RUnlock()
	
	if len(connections) == 0 {
		return
	}
	
	message := &LogStreamMessage{
		Type:    "log",
		BuildID: buildID,
		Entry:   entry,
	}
	
	for _, conn := range connections {
		if err := conn.WriteJSON(message); err != nil {
			bl.logger.Error("发送 WebSocket 消息失败", "error", err, "build_id", buildID)
			// 连接出错，将在下次清理时移除
		}
	}
}

// publishLogUpdate 发布日志更新
func (bl *DefaultBuildLogger) publishLogUpdate(ctx context.Context, buildID string, entry *BuildLogEntry) {
	data, err := json.Marshal(entry)
	if err != nil {
		bl.logger.Error("序列化日志更新失败", "error", err)
		return
	}
	
	channel := bl.getLogChannelName(buildID)
	if err := bl.redis.Publish(ctx, channel, data).Err(); err != nil {
		bl.logger.Error("发布日志更新失败", "error", err, "channel", channel)
	}
}

// closeWebSocketConnections 关闭 WebSocket 连接
func (bl *DefaultBuildLogger) closeWebSocketConnections(buildID string) {
	bl.connMutex.Lock()
	defer bl.connMutex.Unlock()
	
	connections := bl.connections[buildID]
	for _, conn := range connections {
		conn.Close()
	}
	
	delete(bl.connections, buildID)
}

// sendHistoryLogs 发送历史日志
func (bl *DefaultBuildLogger) sendHistoryLogs(ctx context.Context, buildID string, conn *websocket.Conn) error {
	logs, err := bl.GetLogs(ctx, buildID, &LogQueryOptions{
		Limit: 100, // 发送最近100条日志
	})
	if err != nil {
		return err
	}
	
	for _, entry := range logs {
		message := &LogStreamMessage{
			Type:    "log",
			BuildID: buildID,
			Entry:   entry,
		}
		
		if err := conn.WriteJSON(message); err != nil {
			return err
		}
	}
	
	return nil
}

// 辅助函数



// getLogKey 获取日志存储键
func (bl *DefaultBuildLogger) getLogKey(buildID string) string {
	return fmt.Sprintf("build_logs:%s", buildID)
}

// getLogChannelName 获取日志频道名称
func (bl *DefaultBuildLogger) getLogChannelName(buildID string) string {
	return fmt.Sprintf("build_logs_channel:%s", buildID)
}

// matchesFilter 检查日志是否匹配过滤条件
func (bl *DefaultBuildLogger) matchesFilter(entry *BuildLogEntry, options *LogQueryOptions) bool {
	if options == nil {
		return true
	}
	
	if options.Level != "" && entry.Level != options.Level {
		return false
	}
	
	if options.Stage != "" && entry.Stage != options.Stage {
		return false
	}
	
	if options.Step != "" && entry.Step != options.Step {
		return false
	}
	
	if options.StartTime != nil {
		entryTime, err := time.Parse(time.RFC3339, entry.Timestamp)
		if err == nil && entryTime.Before(*options.StartTime) {
			return false
		}
	}

	if options.EndTime != nil {
		entryTime, err := time.Parse(time.RFC3339, entry.Timestamp)
		if err == nil && entryTime.After(*options.EndTime) {
			return false
		}
	}
	
	return true
}

// getOffset 获取偏移量
func getOffset(options *LogQueryOptions) int {
	if options == nil || options.Offset < 0 {
		return 0
	}
	return options.Offset
}

// getLimit 获取限制数量
func getLimit(options *LogQueryOptions) int {
	if options == nil || options.Limit <= 0 {
		return 100 // 默认限制
	}
	if options.Limit > 1000 {
		return 1000 // 最大限制
	}
	return options.Limit
}
