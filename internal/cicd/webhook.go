package cicd

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
)

// WebhookHandler Webhook 处理器接口
type WebhookHandler interface {
	// HandleGiteaWebhook 处理 Gitea Webhook
	HandleGiteaWebhook(ctx context.Context, payload []byte, signature string) error
	
	// HandleGithubWebhook 处理 GitHub Webhook
	HandleGithubWebhook(ctx context.Context, payload []byte, signature string) error
	
	// HandleGitlabWebhook 处理 GitLab Webhook
	HandleGitlabWebhook(ctx context.Context, payload []byte, token string) error
	
	// ValidateSignature 验证 Webhook 签名
	ValidateSignature(payload []byte, signature string, secret string) bool
}

// DefaultWebhookHandler 默认 Webhook 处理器实现
type DefaultWebhookHandler struct {
	service Service
	logger  Logger
	secret  string
}

// NewWebhookHandler 创建 Webhook 处理器
func NewWebhookHandler(service Service, logger Logger, secret string) WebhookHandler {
	return &DefaultWebhookHandler{
		service: service,
		logger:  logger,
		secret:  secret,
	}
}

// HandleGiteaWebhook 处理 Gitea Webhook
func (h *DefaultWebhookHandler) HandleGiteaWebhook(ctx context.Context, payload []byte, signature string) error {
	h.logger.Info("处理 Gitea Webhook", "payload_size", len(payload))
	
	// 验证签名
	if !h.ValidateSignature(payload, signature, h.secret) {
		return fmt.Errorf("Webhook 签名验证失败")
	}
	
	// 解析 Gitea Webhook 载荷
	var giteaPayload GiteaWebhookPayload
	if err := json.Unmarshal(payload, &giteaPayload); err != nil {
		return fmt.Errorf("解析 Gitea Webhook 载荷失败: %w", err)
	}
	
	// 记录 Webhook 事件
	webhook := &Webhook{
		ID:        uuid.New().String(),
		Source:    "gitea",
		Event:     h.determineGiteaEvent(&giteaPayload),
		Payload:   giteaPayload,
		Processed: false,
		CreatedAt: time.Now(),
	}
	
	// 查找关联的应用
	appID := h.findAppByRepository(giteaPayload.Repository.FullName)
	if appID != "" {
		webhook.AppID = appID
	}
	
	// 保存 Webhook 记录
	if err := h.saveWebhook(ctx, webhook); err != nil {
		return fmt.Errorf("保存 Webhook 记录失败: %w", err)
	}
	
	// 处理 Webhook 事件
	if err := h.processGiteaWebhook(ctx, &giteaPayload, webhook.ID); err != nil {
		h.logger.Error("处理 Gitea Webhook 事件失败", "error", err, "webhook_id", webhook.ID)
		return fmt.Errorf("处理 Webhook 事件失败: %w", err)
	}
	
	// 标记为已处理
	h.markWebhookProcessed(ctx, webhook.ID)
	
	h.logger.Info("Gitea Webhook 处理成功", "webhook_id", webhook.ID, "event", webhook.Event)
	return nil
}

// HandleGithubWebhook 处理 GitHub Webhook
func (h *DefaultWebhookHandler) HandleGithubWebhook(ctx context.Context, payload []byte, signature string) error {
	h.logger.Info("处理 GitHub Webhook", "payload_size", len(payload))
	
	// 验证签名
	if !h.ValidateGithubSignature(payload, signature, h.secret) {
		return fmt.Errorf("GitHub Webhook 签名验证失败")
	}
	
	// 解析 GitHub Webhook 载荷
	var githubPayload GithubWebhookPayload
	if err := json.Unmarshal(payload, &githubPayload); err != nil {
		return fmt.Errorf("解析 GitHub Webhook 载荷失败: %w", err)
	}
	
	// TODO: 实现 GitHub Webhook 处理逻辑
	h.logger.Info("GitHub Webhook 处理功能开发中", "repository", githubPayload.Repository.FullName)
	
	return nil
}

// HandleGitlabWebhook 处理 GitLab Webhook
func (h *DefaultWebhookHandler) HandleGitlabWebhook(ctx context.Context, payload []byte, token string) error {
	h.logger.Info("处理 GitLab Webhook", "payload_size", len(payload))
	
	// 验证 Token
	if token != h.secret {
		return fmt.Errorf("GitLab Webhook Token 验证失败")
	}
	
	// 解析 GitLab Webhook 载荷
	var gitlabPayload ********************
	if err := json.Unmarshal(payload, &gitlabPayload); err != nil {
		return fmt.Errorf("解析 GitLab Webhook 载荷失败: %w", err)
	}
	
	// TODO: 实现 GitLab Webhook 处理逻辑
	h.logger.Info("GitLab Webhook 处理功能开发中", "project", gitlabPayload.Project.PathWithNamespace)
	
	return nil
}

// ValidateSignature 验证 Webhook 签名
func (h *DefaultWebhookHandler) ValidateSignature(payload []byte, signature string, secret string) bool {
	if signature == "" || secret == "" {
		return false
	}
	
	// 计算期望的签名
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(payload)
	expectedSignature := hex.EncodeToString(mac.Sum(nil))
	
	// 比较签名
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// ValidateGithubSignature 验证 GitHub Webhook 签名
func (h *DefaultWebhookHandler) ValidateGithubSignature(payload []byte, signature string, secret string) bool {
	if !strings.HasPrefix(signature, "sha256=") {
		return false
	}
	
	signature = strings.TrimPrefix(signature, "sha256=")
	return h.ValidateSignature(payload, signature, secret)
}

// processGiteaWebhook 处理 Gitea Webhook 事件
func (h *DefaultWebhookHandler) processGiteaWebhook(ctx context.Context, payload *GiteaWebhookPayload, webhookID string) error {
	// 根据事件类型处理
	switch {
	case len(payload.Commits) > 0: // Push 事件
		return h.handleGiteaPushEvent(ctx, payload)
	case payload.PullRequest != nil: // Pull Request 事件
		return h.handleGiteaPullRequestEvent(ctx, payload)
	case payload.Release != nil: // Release 事件
		return h.handleGiteaReleaseEvent(ctx, payload)
	default:
		h.logger.Warn("未支持的 Gitea Webhook 事件类型", "webhook_id", webhookID)
		return nil
	}
}

// handleGiteaPushEvent 处理 Gitea Push 事件
func (h *DefaultWebhookHandler) handleGiteaPushEvent(ctx context.Context, payload *GiteaWebhookPayload) error {
	h.logger.Info("处理 Gitea Push 事件", "repository", payload.Repository.FullName, "ref", payload.Ref)
	
	// 提取分支名称
	branch := h.extractBranchFromRef(payload.Ref)
	if branch == "" {
		h.logger.Warn("无法从 ref 中提取分支名称", "ref", payload.Ref)
		return nil
	}
	
	// 查找相关的流水线
	appID := h.findAppByRepository(payload.Repository.FullName)
	if appID == "" {
		h.logger.Warn("未找到关联的应用", "repository", payload.Repository.FullName)
		return nil
	}
	
	pipelines, err := h.service.ListPipelines(ctx, appID)
	if err != nil {
		return fmt.Errorf("获取流水线列表失败: %w", err)
	}
	
	// 为每个匹配的流水线创建构建任务
	for _, pipeline := range pipelines {
		if h.shouldTriggerBuild(pipeline, "push", branch) {
			buildReq := &CreateBuildRequest{
				PipelineID:  pipeline.ID,
				Branch:      branch,
				CommitHash:  payload.After,
				CommitMsg:   h.getLatestCommitMessage(payload.Commits),
				Author:      h.getLatestCommitAuthor(payload.Commits),
				TriggerType: "push",
				Priority:    0,
			}
			
			build, err := h.service.CreateBuild(ctx, buildReq)
			if err != nil {
				h.logger.Error("创建构建任务失败", "error", err, "pipeline_id", pipeline.ID)
				continue
			}
			
			h.logger.Info("Webhook 触发构建成功", "pipeline_id", pipeline.ID, "build_id", build.ID, "commit", payload.After)
		}
	}
	
	return nil
}

// handleGiteaPullRequestEvent 处理 Gitea Pull Request 事件
func (h *DefaultWebhookHandler) handleGiteaPullRequestEvent(ctx context.Context, payload *GiteaWebhookPayload) error {
	h.logger.Info("处理 Gitea Pull Request 事件", "repository", payload.Repository.FullName, "action", payload.Action)
	
	// 只处理打开和同步的 PR
	if payload.Action != "opened" && payload.Action != "synchronized" {
		h.logger.Debug("忽略 PR 事件", "action", payload.Action)
		return nil
	}
	
	// TODO: 实现 PR 构建逻辑
	h.logger.Info("PR 构建功能开发中", "pr_number", payload.PullRequest.Number)
	
	return nil
}

// handleGiteaReleaseEvent 处理 Gitea Release 事件
func (h *DefaultWebhookHandler) handleGiteaReleaseEvent(ctx context.Context, payload *GiteaWebhookPayload) error {
	h.logger.Info("处理 Gitea Release 事件", "repository", payload.Repository.FullName, "tag", payload.Release.TagName)
	
	// TODO: 实现 Release 构建逻辑
	h.logger.Info("Release 构建功能开发中", "tag", payload.Release.TagName)
	
	return nil
}

// 辅助方法

// determineGiteaEvent 确定 Gitea 事件类型
func (h *DefaultWebhookHandler) determineGiteaEvent(payload *GiteaWebhookPayload) string {
	if len(payload.Commits) > 0 {
		return "push"
	}
	if payload.PullRequest != nil {
		return "pull_request"
	}
	if payload.Release != nil {
		return "release"
	}
	return "unknown"
}

// extractBranchFromRef 从 ref 中提取分支名称
func (h *DefaultWebhookHandler) extractBranchFromRef(ref string) string {
	if strings.HasPrefix(ref, "refs/heads/") {
		return strings.TrimPrefix(ref, "refs/heads/")
	}
	return ""
}

// findAppByRepository 根据仓库名称查找应用ID
func (h *DefaultWebhookHandler) findAppByRepository(repoFullName string) string {
	// TODO: 实现根据仓库名称查找应用ID的逻辑
	// 这需要与应用管理服务集成，或者在数据库中维护仓库与应用的映射关系
	h.logger.Debug("查找应用", "repository", repoFullName)
	return ""
}

// shouldTriggerBuild 判断是否应该触发构建
func (h *DefaultWebhookHandler) shouldTriggerBuild(pipeline *Pipeline, eventType, branch string) bool {
	// 检查流水线的触发条件
	for _, trigger := range pipeline.Config.Spec.Triggers {
		if trigger.Type == eventType {
			// 检查分支匹配
			if len(trigger.Branches) == 0 {
				return true // 没有分支限制，触发所有分支
			}
			
			for _, triggerBranch := range trigger.Branches {
				if triggerBranch == branch {
					return true
				}
			}
		}
	}
	
	return false
}

// getLatestCommitMessage 获取最新提交消息
func (h *DefaultWebhookHandler) getLatestCommitMessage(commits []GiteaCommit) string {
	if len(commits) > 0 {
		return commits[len(commits)-1].Message
	}
	return ""
}

// getLatestCommitAuthor 获取最新提交作者
func (h *DefaultWebhookHandler) getLatestCommitAuthor(commits []GiteaCommit) string {
	if len(commits) > 0 {
		return commits[len(commits)-1].Author.Username
	}
	return ""
}

// saveWebhook 保存 Webhook 记录
func (h *DefaultWebhookHandler) saveWebhook(ctx context.Context, webhook *Webhook) error {
	// TODO: 直接操作数据库保存 Webhook 记录
	// 这里暂时使用日志记录
	h.logger.Info("保存 Webhook 记录", "webhook_id", webhook.ID, "source", webhook.Source, "event", webhook.Event)
	return nil
}

// markWebhookProcessed 标记 Webhook 为已处理
func (h *DefaultWebhookHandler) markWebhookProcessed(ctx context.Context, webhookID string) {
	// TODO: 更新数据库中的 Webhook 记录状态
	h.logger.Debug("标记 Webhook 已处理", "webhook_id", webhookID)
}

// Webhook 载荷结构定义

// GiteaWebhookPayload Gitea Webhook 载荷
type GiteaWebhookPayload struct {
	Ref         string           `json:"ref"`
	Before      string           `json:"before"`
	After       string           `json:"after"`
	Repository  GiteaRepository  `json:"repository"`
	Commits     []GiteaCommit    `json:"commits"`
	Pusher      GiteaUser        `json:"pusher"`
	Action      string           `json:"action"`
	PullRequest *GiteaPullRequest `json:"pull_request"`
	Release     *GiteaRelease    `json:"release"`
}

// GiteaRepository Gitea 仓库信息
type GiteaRepository struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name"`
	HTMLURL  string `json:"html_url"`
	CloneURL string `json:"clone_url"`
	SSHURL   string `json:"ssh_url"`
}

// GiteaCommit Gitea 提交信息
type GiteaCommit struct {
	ID        string    `json:"id"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
	URL       string    `json:"url"`
	Author    GiteaUser `json:"author"`
	Committer GiteaUser `json:"committer"`
}

// GiteaUser Gitea 用户信息
type GiteaUser struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	FullName string `json:"full_name"`
}

// GiteaPullRequest Gitea Pull Request 信息
type GiteaPullRequest struct {
	ID     int    `json:"id"`
	Number int    `json:"number"`
	Title  string `json:"title"`
	Body   string `json:"body"`
	State  string `json:"state"`
	Head   struct {
		Ref string `json:"ref"`
		SHA string `json:"sha"`
	} `json:"head"`
	Base struct {
		Ref string `json:"ref"`
		SHA string `json:"sha"`
	} `json:"base"`
}

// GiteaRelease Gitea Release 信息
type GiteaRelease struct {
	ID      int    `json:"id"`
	TagName string `json:"tag_name"`
	Name    string `json:"name"`
	Body    string `json:"body"`
	Draft   bool   `json:"draft"`
}

// GithubWebhookPayload GitHub Webhook 载荷
type GithubWebhookPayload struct {
	Ref        string `json:"ref"`
	Before     string `json:"before"`
	After      string `json:"after"`
	Repository struct {
		ID       int    `json:"id"`
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		HTMLURL  string `json:"html_url"`
		CloneURL string `json:"clone_url"`
		SSHURL   string `json:"ssh_url"`
	} `json:"repository"`
	// TODO: 添加更多 GitHub 特定字段
}

// ******************** GitLab Webhook 载荷
type ******************** struct {
	ObjectKind string `json:"object_kind"`
	Ref        string `json:"ref"`
	Before     string `json:"before"`
	After      string `json:"after"`
	Project    struct {
		ID                int    `json:"id"`
		Name              string `json:"name"`
		PathWithNamespace string `json:"path_with_namespace"`
		WebURL            string `json:"web_url"`
		GitHTTPURL        string `json:"git_http_url"`
		GitSSHURL         string `json:"git_ssh_url"`
	} `json:"project"`
	// TODO: 添加更多 GitLab 特定字段
}
