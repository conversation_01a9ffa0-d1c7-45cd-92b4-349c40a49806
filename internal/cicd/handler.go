package cicd

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// Handler CI/CD HTTP 处理器
type Handler struct {
	service Service
	logger  Logger
}

// NewHandler 创建 CI/CD 处理器
func NewHandler(service Service, logger Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 流水线管理
	pipelines := router.Group("/pipelines")
	{
		pipelines.POST("", h.CreatePipeline)           // 创建流水线
		pipelines.GET("", h.ListPipelines)             // 获取流水线列表
		pipelines.GET("/:id", h.GetPipeline)           // 获取流水线详情
		pipelines.PUT("/:id", h.UpdatePipeline)        // 更新流水线
		pipelines.DELETE("/:id", h.DeletePipeline)     // 删除流水线
	}
	
	// 构建管理
	builds := router.Group("/builds")
	{
		builds.POST("", h.CreateBuild)                 // 创建构建
		builds.GET("", h.ListBuilds)                   // 获取构建列表
		builds.GET("/:id", h.GetBuild)                 // 获取构建详情
		builds.POST("/:id/cancel", h.CancelBuild)      // 取消构建
		builds.POST("/:id/retry", h.RetryBuild)        // 重试构建
		builds.GET("/:id/logs", h.GetBuildLogs)        // 获取构建日志
		builds.GET("/:id/artifacts", h.GetArtifacts)   // 获取构建产物
	}
	
	// 部署管理
	deployments := router.Group("/deployments")
	{
		deployments.POST("", h.CreateDeployment)           // 创建部署
		deployments.GET("", h.ListDeployments)             // 获取部署列表
		deployments.GET("/:id", h.GetDeployment)           // 获取部署详情
		deployments.POST("/:id/rollback", h.RollbackDeployment) // 回滚部署
	}
	
	// 环境管理
	environments := router.Group("/environments")
	{
		environments.POST("", h.CreateEnvironment)     // 创建环境
		environments.GET("", h.ListEnvironments)       // 获取环境列表
		environments.GET("/:id", h.GetEnvironment)     // 获取环境详情
	}
	
	// Webhook 处理
	webhooks := router.Group("/webhooks")
	{
		webhooks.POST("/gitea", h.HandleGiteaWebhook)   // Gitea Webhook
		webhooks.POST("/github", h.HandleGithubWebhook) // GitHub Webhook
		webhooks.POST("/gitlab", h.HandleGitlabWebhook) // GitLab Webhook
	}
	
	// 构建节点管理
	nodes := router.Group("/nodes")
	{
		nodes.POST("", h.RegisterBuildNode)         // 注册构建节点
		nodes.GET("", h.ListBuildNodes)             // 获取构建节点列表
		nodes.GET("/:id", h.GetBuildNode)           // 获取构建节点详情
		nodes.PUT("/:id/status", h.UpdateNodeStatus) // 更新节点状态
	}
	
	// 统计信息
	stats := router.Group("/stats")
	{
		stats.GET("/builds", h.GetBuildStatistics)  // 获取构建统计
		stats.GET("/nodes", h.GetNodeStatistics)   // 获取节点统计
	}
}

// CreatePipeline 创建流水线
// @Summary 创建流水线
// @Description 为应用创建 CI/CD 流水线
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param request body CreatePipelineRequest true "创建流水线请求"
// @Success 201 {object} Pipeline "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines [post]
func (h *Handler) CreatePipeline(c *gin.Context) {
	var req CreatePipelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	// 从上下文获取租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "未授权访问",
		})
		return
	}
	req.TenantID = tenantID.(string)
	
	pipeline, err := h.service.CreatePipeline(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建流水线失败", "error", err, "name", req.Name)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_PIPELINE_FAILED",
			Message: "创建流水线失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("流水线创建成功", "pipeline_id", pipeline.ID, "name", pipeline.Name)
	c.JSON(http.StatusCreated, pipeline)
}

// GetPipeline 获取流水线详情
// @Summary 获取流水线详情
// @Description 根据流水线ID获取详细信息
// @Tags CI/CD
// @Produce json
// @Param id path string true "流水线ID"
// @Success 200 {object} Pipeline "获取成功"
// @Failure 404 {object} ErrorResponse "流水线不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines/{id} [get]
func (h *Handler) GetPipeline(c *gin.Context) {
	pipelineID := c.Param("id")
	if pipelineID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_PIPELINE_ID",
			Message: "流水线ID不能为空",
		})
		return
	}
	
	pipeline, err := h.service.GetPipeline(c.Request.Context(), pipelineID)
	if err != nil {
		h.logger.Error("获取流水线失败", "error", err, "pipeline_id", pipelineID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "PIPELINE_NOT_FOUND",
			Message: "流水线不存在",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, pipeline)
}

// ListPipelines 获取流水线列表
// @Summary 获取流水线列表
// @Description 获取指定应用的流水线列表
// @Tags CI/CD
// @Produce json
// @Param app_id query string true "应用ID"
// @Success 200 {array} PipelineResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines [get]
func (h *Handler) ListPipelines(c *gin.Context) {
	appID := c.Query("app_id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "MISSING_APP_ID",
			Message: "缺少应用ID参数",
		})
		return
	}
	
	pipelines, err := h.service.ListPipelines(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("获取流水线列表失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_PIPELINES_FAILED",
			Message: "获取流水线列表失败",
			Details: err.Error(),
		})
		return
	}
	
	// 转换为响应格式
	responses := make([]*PipelineResponse, len(pipelines))
	for i, pipeline := range pipelines {
		responses[i] = &PipelineResponse{
			Pipeline:      pipeline,
			BuildCount:    len(pipeline.Builds),
			SuccessCount:  countSuccessBuilds(pipeline.Builds),
			FailureCount:  countFailedBuilds(pipeline.Builds),
			LastBuildTime: getLastBuildTime(pipeline.Builds),
		}
	}
	
	c.JSON(http.StatusOK, responses)
}

// CreateBuild 创建构建
// @Summary 创建构建
// @Description 创建新的构建任务
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param request body CreateBuildRequest true "创建构建请求"
// @Success 201 {object} Build "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds [post]
func (h *Handler) CreateBuild(c *gin.Context) {
	var req CreateBuildRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	build, err := h.service.CreateBuild(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建构建失败", "error", err, "pipeline_id", req.PipelineID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_BUILD_FAILED",
			Message: "创建构建失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("构建创建成功", "build_id", build.ID, "number", build.Number)
	c.JSON(http.StatusCreated, build)
}

// ListBuilds 获取构建列表
// @Summary 获取构建列表
// @Description 获取构建任务列表
// @Tags CI/CD
// @Produce json
// @Param app_id query string false "应用ID"
// @Param pipeline_id query string false "流水线ID"
// @Param status query string false "状态过滤"
// @Param branch query string false "分支过滤"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} BuildListResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds [get]
func (h *Handler) ListBuilds(c *gin.Context) {
	// 解析查询参数
	filter := &BuildFilter{
		AppID:       c.Query("app_id"),
		PipelineID:  c.Query("pipeline_id"),
		Status:      c.Query("status"),
		Branch:      c.Query("branch"),
		TriggerType: c.Query("trigger_type"),
		Page:        1,
		PageSize:    20,
	}
	
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}
	
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}
	
	builds, total, err := h.service.ListBuilds(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取构建列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_BUILDS_FAILED",
			Message: "获取构建列表失败",
			Details: err.Error(),
		})
		return
	}
	
	// 转换为响应格式
	buildResponses := make([]*BuildResponse, len(builds))
	for i, build := range builds {
		buildResponses[i] = &BuildResponse{
			Build:        build,
			PipelineName: build.Pipeline.Name,
			AppName:      "", // TODO: 从应用服务获取应用名称
		}
	}
	
	response := &BuildListResponse{
		Builds: buildResponses,
		Total:  total,
		Page:   filter.Page,
		Size:   len(buildResponses),
	}
	
	c.JSON(http.StatusOK, response)
}

// GetBuild 获取构建详情
// @Summary 获取构建详情
// @Description 根据构建ID获取详细信息
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {object} Build "获取成功"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id} [get]
func (h *Handler) GetBuild(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}
	
	build, err := h.service.GetBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("获取构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "BUILD_NOT_FOUND",
			Message: "构建任务不存在",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, build)
}

// CancelBuild 取消构建
// @Summary 取消构建
// @Description 取消正在进行的构建任务
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {object} object "取消成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id}/cancel [post]
func (h *Handler) CancelBuild(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}
	
	err := h.service.CancelBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("取消构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CANCEL_BUILD_FAILED",
			Message: "取消构建失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("构建已取消", "build_id", buildID)
	c.JSON(http.StatusOK, gin.H{"message": "构建已取消"})
}

// RetryBuild 重试构建
// @Summary 重试构建
// @Description 重试失败的构建任务
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {object} Build "重试成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id}/retry [post]
func (h *Handler) RetryBuild(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}
	
	newBuild, err := h.service.RetryBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("重试构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "RETRY_BUILD_FAILED",
			Message: "重试构建失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("构建重试成功", "old_build_id", buildID, "new_build_id", newBuild.ID)
	c.JSON(http.StatusOK, newBuild)
}

// HandleGiteaWebhook 处理 Gitea Webhook
// @Summary 处理 Gitea Webhook
// @Description 处理来自 Gitea 的 Webhook 事件
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param payload body object true "Webhook 载荷"
// @Success 200 {object} object "处理成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/webhooks/gitea [post]
func (h *Handler) HandleGiteaWebhook(c *gin.Context) {
	var payload interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		h.logger.Error("绑定 Webhook 载荷失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_WEBHOOK_PAYLOAD",
			Message: "Webhook 载荷格式错误",
			Details: err.Error(),
		})
		return
	}
	
	err := h.service.HandleWebhook(c.Request.Context(), "gitea", payload)
	if err != nil {
		h.logger.Error("处理 Gitea Webhook 失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "WEBHOOK_PROCESSING_FAILED",
			Message: "Webhook 处理失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("Gitea Webhook 处理成功")
	c.JSON(http.StatusOK, gin.H{"message": "Webhook 处理成功"})
}

// HandleGithubWebhook 处理 GitHub Webhook
func (h *Handler) HandleGithubWebhook(c *gin.Context) {
	// TODO: 实现 GitHub Webhook 处理逻辑
	c.JSON(http.StatusOK, gin.H{"message": "GitHub Webhook 处理功能开发中"})
}

// HandleGitlabWebhook 处理 GitLab Webhook
func (h *Handler) HandleGitlabWebhook(c *gin.Context) {
	// TODO: 实现 GitLab Webhook 处理逻辑
	c.JSON(http.StatusOK, gin.H{"message": "GitLab Webhook 处理功能开发中"})
}

// UpdatePipeline 更新流水线
// @Summary 更新流水线
// @Description 更新流水线配置信息
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param id path string true "流水线ID"
// @Param request body UpdatePipelineRequest true "更新流水线请求"
// @Success 200 {object} Pipeline "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "流水线不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines/{id} [put]
func (h *Handler) UpdatePipeline(c *gin.Context) {
	pipelineID := c.Param("id")
	if pipelineID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_PIPELINE_ID",
			Message: "流水线ID不能为空",
		})
		return
	}

	var req UpdatePipelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	pipeline, err := h.service.UpdatePipeline(c.Request.Context(), pipelineID, &req)
	if err != nil {
		h.logger.Error("更新流水线失败", "error", err, "pipeline_id", pipelineID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_PIPELINE_FAILED",
			Message: "更新流水线失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("流水线更新成功", "pipeline_id", pipelineID)
	c.JSON(http.StatusOK, pipeline)
}

// DeletePipeline 删除流水线
// @Summary 删除流水线
// @Description 删除指定的流水线
// @Tags CI/CD
// @Produce json
// @Param id path string true "流水线ID"
// @Success 200 {object} object "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "流水线不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines/{id} [delete]
func (h *Handler) DeletePipeline(c *gin.Context) {
	pipelineID := c.Param("id")
	if pipelineID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_PIPELINE_ID",
			Message: "流水线ID不能为空",
		})
		return
	}

	err := h.service.DeletePipeline(c.Request.Context(), pipelineID)
	if err != nil {
		h.logger.Error("删除流水线失败", "error", err, "pipeline_id", pipelineID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DELETE_PIPELINE_FAILED",
			Message: "删除流水线失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("流水线删除成功", "pipeline_id", pipelineID)
	c.JSON(http.StatusOK, gin.H{"message": "流水线删除成功"})
}

// CreateDeployment 创建部署
// @Summary 创建部署
// @Description 创建新的部署任务
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param request body CreateDeploymentRequest true "创建部署请求"
// @Success 201 {object} Deployment "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/deployments [post]
func (h *Handler) CreateDeployment(c *gin.Context) {
	var req CreateDeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	deployment, err := h.service.CreateDeployment(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建部署失败", "error", err, "app_id", req.AppID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_DEPLOYMENT_FAILED",
			Message: "创建部署失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("部署创建成功", "deployment_id", deployment.ID, "app_id", req.AppID)
	c.JSON(http.StatusCreated, deployment)
}

// GetDeployment 获取部署详情
// @Summary 获取部署详情
// @Description 根据部署ID获取详细信息
// @Tags CI/CD
// @Produce json
// @Param id path string true "部署ID"
// @Success 200 {object} Deployment "获取成功"
// @Failure 404 {object} ErrorResponse "部署不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/deployments/{id} [get]
func (h *Handler) GetDeployment(c *gin.Context) {
	deploymentID := c.Param("id")
	if deploymentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_DEPLOYMENT_ID",
			Message: "部署ID不能为空",
		})
		return
	}

	deployment, err := h.service.GetDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("获取部署失败", "error", err, "deployment_id", deploymentID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "DEPLOYMENT_NOT_FOUND",
			Message: "部署不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, deployment)
}

// ListDeployments 获取部署列表
// @Summary 获取部署列表
// @Description 获取部署任务列表
// @Tags CI/CD
// @Produce json
// @Param app_id query string false "应用ID"
// @Param environment query string false "环境过滤"
// @Param status query string false "状态过滤"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} DeploymentListResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/deployments [get]
func (h *Handler) ListDeployments(c *gin.Context) {
	// 解析查询参数
	filter := &DeploymentFilter{
		AppID:       c.Query("app_id"),
		Environment: c.Query("environment"),
		Status:      c.Query("status"),
		Page:        1,
		PageSize:    20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	deployments, total, err := h.service.ListDeployments(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取部署列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_DEPLOYMENTS_FAILED",
			Message: "获取部署列表失败",
			Details: err.Error(),
		})
		return
	}

	// 转换为响应格式
	deploymentResponses := make([]*DeploymentResponse, len(deployments))
	for i, deployment := range deployments {
		deploymentResponses[i] = &DeploymentResponse{
			Deployment:  deployment,
			AppName:     "", // TODO: 从应用服务获取应用名称
			BuildNumber: deployment.Build.Number,
		}
	}

	response := &DeploymentListResponse{
		Deployments: deploymentResponses,
		Total:       total,
		Page:        filter.Page,
		Size:        len(deploymentResponses),
	}

	c.JSON(http.StatusOK, response)
}

// RollbackDeployment 回滚部署
// @Summary 回滚部署
// @Description 回滚到上一个成功的部署版本
// @Tags CI/CD
// @Produce json
// @Param id path string true "部署ID"
// @Success 200 {object} object "回滚成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "部署不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/deployments/{id}/rollback [post]
func (h *Handler) RollbackDeployment(c *gin.Context) {
	deploymentID := c.Param("id")
	if deploymentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_DEPLOYMENT_ID",
			Message: "部署ID不能为空",
		})
		return
	}

	err := h.service.RollbackDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("回滚部署失败", "error", err, "deployment_id", deploymentID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "ROLLBACK_DEPLOYMENT_FAILED",
			Message: "回滚部署失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("部署回滚成功", "deployment_id", deploymentID)
	c.JSON(http.StatusOK, gin.H{"message": "部署回滚成功"})
}

// CreateEnvironment 创建环境
// @Summary 创建环境
// @Description 创建新的部署环境
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param request body CreateEnvironmentRequest true "创建环境请求"
// @Success 201 {object} Environment "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/environments [post]
func (h *Handler) CreateEnvironment(c *gin.Context) {
	var req CreateEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "未授权访问",
		})
		return
	}
	req.TenantID = tenantID.(string)

	environment, err := h.service.CreateEnvironment(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建环境失败", "error", err, "name", req.Name)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_ENVIRONMENT_FAILED",
			Message: "创建环境失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("环境创建成功", "environment_id", environment.ID, "name", environment.Name)
	c.JSON(http.StatusCreated, environment)
}

// GetEnvironment 获取环境详情
// @Summary 获取环境详情
// @Description 根据环境ID获取详细信息
// @Tags CI/CD
// @Produce json
// @Param id path string true "环境ID"
// @Success 200 {object} Environment "获取成功"
// @Failure 404 {object} ErrorResponse "环境不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/environments/{id} [get]
func (h *Handler) GetEnvironment(c *gin.Context) {
	envID := c.Param("id")
	if envID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ENVIRONMENT_ID",
			Message: "环境ID不能为空",
		})
		return
	}

	environment, err := h.service.GetEnvironment(c.Request.Context(), envID)
	if err != nil {
		h.logger.Error("获取环境失败", "error", err, "environment_id", envID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "ENVIRONMENT_NOT_FOUND",
			Message: "环境不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, environment)
}

// ListEnvironments 获取环境列表
// @Summary 获取环境列表
// @Description 获取租户的环境列表
// @Tags CI/CD
// @Produce json
// @Success 200 {array} Environment "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/environments [get]
func (h *Handler) ListEnvironments(c *gin.Context) {
	// 从上下文获取租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "未授权访问",
		})
		return
	}

	environments, err := h.service.ListEnvironments(c.Request.Context(), tenantID.(string))
	if err != nil {
		h.logger.Error("获取环境列表失败", "error", err, "tenant_id", tenantID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_ENVIRONMENTS_FAILED",
			Message: "获取环境列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, environments)
}

// RegisterBuildNode 注册构建节点
// @Summary 注册构建节点
// @Description 注册新的构建节点
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param request body RegisterNodeRequest true "注册节点请求"
// @Success 201 {object} BuildNode "注册成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/nodes [post]
func (h *Handler) RegisterBuildNode(c *gin.Context) {
	var req RegisterNodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	node, err := h.service.RegisterBuildNode(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("注册构建节点失败", "error", err, "name", req.Name)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "REGISTER_NODE_FAILED",
			Message: "注册构建节点失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("构建节点注册成功", "node_id", node.ID, "name", node.Name)
	c.JSON(http.StatusCreated, node)
}

// GetBuildNode 获取构建节点详情
// @Summary 获取构建节点详情
// @Description 根据节点ID获取详细信息
// @Tags CI/CD
// @Produce json
// @Param id path string true "节点ID"
// @Success 200 {object} BuildNode "获取成功"
// @Failure 404 {object} ErrorResponse "节点不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/nodes/{id} [get]
func (h *Handler) GetBuildNode(c *gin.Context) {
	nodeID := c.Param("id")
	if nodeID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_NODE_ID",
			Message: "节点ID不能为空",
		})
		return
	}

	node, err := h.service.GetBuildNode(c.Request.Context(), nodeID)
	if err != nil {
		h.logger.Error("获取构建节点失败", "error", err, "node_id", nodeID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "NODE_NOT_FOUND",
			Message: "构建节点不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, node)
}

// ListBuildNodes 获取构建节点列表
// @Summary 获取构建节点列表
// @Description 获取所有构建节点列表
// @Tags CI/CD
// @Produce json
// @Success 200 {array} BuildNode "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/nodes [get]
func (h *Handler) ListBuildNodes(c *gin.Context) {
	nodes, err := h.service.ListBuildNodes(c.Request.Context())
	if err != nil {
		h.logger.Error("获取构建节点列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_NODES_FAILED",
			Message: "获取构建节点列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, nodes)
}

// UpdateNodeStatus 更新节点状态
// @Summary 更新节点状态
// @Description 更新构建节点的状态
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param id path string true "节点ID"
// @Param status body object true "状态信息"
// @Success 200 {object} object "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/nodes/{id}/status [put]
func (h *Handler) UpdateNodeStatus(c *gin.Context) {
	nodeID := c.Param("id")
	if nodeID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_NODE_ID",
			Message: "节点ID不能为空",
		})
		return
	}

	var req struct {
		Status NodeStatus `json:"status"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	err := h.service.UpdateNodeStatus(c.Request.Context(), nodeID, req.Status)
	if err != nil {
		h.logger.Error("更新节点状态失败", "error", err, "node_id", nodeID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_NODE_STATUS_FAILED",
			Message: "更新节点状态失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("节点状态更新成功", "node_id", nodeID, "status", req.Status)
	c.JSON(http.StatusOK, gin.H{"message": "节点状态更新成功"})
}

// GetBuildLogs 获取构建日志
// @Summary 获取构建日志
// @Description 获取构建任务的详细日志
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {array} BuildLogEntry "获取成功"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id}/logs [get]
func (h *Handler) GetBuildLogs(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}

	// TODO: 实现构建日志获取逻辑
	// 这里先返回模拟数据
	logs := []BuildLogEntry{
		{
			Timestamp: time.Now().Format(time.RFC3339),
			Level:     "INFO",
			Stage:     "prepare",
			Step:      "checkout",
			Message:   "开始检出代码...",
			BuildID:   buildID,
		},
		{
			Timestamp: time.Now().Format(time.RFC3339),
			Level:     "INFO",
			Stage:     "build",
			Step:      "install-deps",
			Message:   "安装依赖包...",
			BuildID:   buildID,
		},
	}

	c.JSON(http.StatusOK, logs)
}

// GetArtifacts 获取构建产物
// @Summary 获取构建产物
// @Description 获取构建任务的产物列表
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {array} Artifact "获取成功"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id}/artifacts [get]
func (h *Handler) GetArtifacts(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}

	// 获取构建信息
	build, err := h.service.GetBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("获取构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "BUILD_NOT_FOUND",
			Message: "构建任务不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, build.Artifacts)
}

// GetBuildStatistics 获取构建统计
// @Summary 获取构建统计
// @Description 获取构建任务的统计信息
// @Tags CI/CD
// @Produce json
// @Success 200 {object} BuildStatistics "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/stats/builds [get]
func (h *Handler) GetBuildStatistics(c *gin.Context) {
	// TODO: 实现构建统计逻辑
	stats := BuildStatistics{
		TotalBuilds:   100,
		SuccessBuilds: 85,
		FailedBuilds:  15,
		SuccessRate:   85.0,
		AvgDuration:   300.5,
		TodayBuilds:   10,
	}

	c.JSON(http.StatusOK, stats)
}

// GetNodeStatistics 获取节点统计
// @Summary 获取节点统计
// @Description 获取构建节点的统计信息
// @Tags CI/CD
// @Produce json
// @Success 200 {object} NodeStatistics "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/stats/nodes [get]
func (h *Handler) GetNodeStatistics(c *gin.Context) {
	// TODO: 实现节点统计逻辑
	stats := NodeStatistics{
		TotalNodes:    5,
		OnlineNodes:   4,
		OfflineNodes:  1,
		BusyNodes:     2,
		TotalCapacity: 25,
		UsedCapacity:  8,
	}

	c.JSON(http.StatusOK, stats)
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Details   string `json:"details,omitempty"`
	Timestamp string `json:"timestamp"`
}

// 辅助函数
func countSuccessBuilds(builds []Build) int {
	count := 0
	for _, build := range builds {
		if build.Status == BuildStatusSuccess {
			count++
		}
	}
	return count
}

func countFailedBuilds(builds []Build) int {
	count := 0
	for _, build := range builds {
		if build.Status == BuildStatusFailed {
			count++
		}
	}
	return count
}

func getLastBuildTime(builds []Build) *time.Time {
	if len(builds) == 0 {
		return nil
	}
	
	latest := builds[0].StartTime
	for _, build := range builds {
		if build.StartTime.After(latest) {
			latest = build.StartTime
		}
	}
	
	return &latest
}
