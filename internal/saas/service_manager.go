package saas

import (
	"context"
	"fmt"
	"sync"
	"time"

	"paas-platform/internal/container"
	"paas-platform/internal/loadbalancer"
	"paas-platform/pkg/logger"
)

// ServiceManager SaaS服务管理器
type ServiceManager struct {
	containerManager container.ContainerManager
	loadBalancer     *loadbalancer.LoadBalancer
	serviceRegistry  *loadbalancer.ServiceRegistry
	logger           logger.Logger
	config           *ServiceManagerConfig
	services         map[string]*ManagedService
	mutex            sync.RWMutex
}

// ServiceManagerConfig 服务管理器配置
type ServiceManagerConfig struct {
	DefaultReplicas     int           `yaml:"default_replicas"`     // 默认副本数
	MaxReplicas         int           `yaml:"max_replicas"`         // 最大副本数
	MinReplicas         int           `yaml:"min_replicas"`         // 最小副本数
	HealthCheckInterval time.Duration `yaml:"health_check_interval"` // 健康检查间隔
	ScaleUpThreshold    float64       `yaml:"scale_up_threshold"`    // 扩容阈值
	ScaleDownThreshold  float64       `yaml:"scale_down_threshold"`  // 缩容阈值
	AutoScalingEnabled  bool          `yaml:"auto_scaling_enabled"`  // 是否启用自动扩缩容
}

// ManagedService 托管服务
type ManagedService struct {
	ID              string                    `json:"id"`
	Name            string                    `json:"name"`
	Image           string                    `json:"image"`
	Port            int                       `json:"port"`
	Replicas        int                       `json:"replicas"`
	DesiredReplicas int                       `json:"desired_replicas"`
	Instances       []*ServiceInstance        `json:"instances"`
	Config          *ServiceConfig            `json:"config"`
	Status          string                    `json:"status"` // running, stopped, scaling, error
	CreatedAt       time.Time                 `json:"created_at"`
	UpdatedAt       time.Time                 `json:"updated_at"`
	Metrics         *ServiceMetrics           `json:"metrics"`
	mutex           sync.RWMutex
}

// ServiceInstance 服务实例
type ServiceInstance struct {
	ID          string                 `json:"id"`
	ServiceID   string                 `json:"service_id"`
	ContainerID string                 `json:"container_id"`
	Host        string                 `json:"host"`
	Port        int                    `json:"port"`
	Status      string                 `json:"status"` // starting, running, stopping, stopped, error
	Health      string                 `json:"health"` // healthy, unhealthy, unknown
	CreatedAt   time.Time              `json:"created_at"`
	StartedAt   time.Time              `json:"started_at"`
	Metadata    map[string]string      `json:"metadata"`
	mutex       sync.RWMutex
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Environment     map[string]string      `json:"environment"`
	Resources       *ResourceRequirements  `json:"resources"`
	HealthCheck     *HealthCheckConfig     `json:"health_check"`
	LoadBalancing   *LoadBalancingConfig   `json:"load_balancing"`
	AutoScaling     *AutoScalingConfig     `json:"auto_scaling"`
	Volumes         []VolumeMount          `json:"volumes"`
}

// ResourceRequirements 资源需求
type ResourceRequirements struct {
	CPURequest    string `json:"cpu_request"`    // CPU请求，如 "100m"
	CPULimit      string `json:"cpu_limit"`      // CPU限制，如 "500m"
	MemoryRequest string `json:"memory_request"` // 内存请求，如 "128Mi"
	MemoryLimit   string `json:"memory_limit"`   // 内存限制，如 "512Mi"
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Path                string        `json:"path"`                 // 健康检查路径
	Port                int           `json:"port"`                 // 健康检查端口
	Interval            time.Duration `json:"interval"`             // 检查间隔
	Timeout             time.Duration `json:"timeout"`              // 超时时间
	HealthyThreshold    int           `json:"healthy_threshold"`    // 健康阈值
	UnhealthyThreshold  int           `json:"unhealthy_threshold"`  // 不健康阈值
}

// LoadBalancingConfig 负载均衡配置
type LoadBalancingConfig struct {
	Algorithm    string `json:"algorithm"`     // 负载均衡算法
	SessionAffinity bool `json:"session_affinity"` // 会话保持
}

// AutoScalingConfig 自动扩缩容配置
type AutoScalingConfig struct {
	Enabled                bool    `json:"enabled"`
	MinReplicas           int     `json:"min_replicas"`
	MaxReplicas           int     `json:"max_replicas"`
	TargetCPUUtilization  float64 `json:"target_cpu_utilization"`
	ScaleUpCooldown       time.Duration `json:"scale_up_cooldown"`
	ScaleDownCooldown     time.Duration `json:"scale_down_cooldown"`
}

// VolumeMount 卷挂载
type VolumeMount struct {
	Name      string `json:"name"`
	MountPath string `json:"mount_path"`
	ReadOnly  bool   `json:"read_only"`
}

// ServiceMetrics 服务指标
type ServiceMetrics struct {
	RequestCount    int64         `json:"request_count"`
	ErrorCount      int64         `json:"error_count"`
	AverageLatency  time.Duration `json:"average_latency"`
	CPUUsage        float64       `json:"cpu_usage"`
	MemoryUsage     int64         `json:"memory_usage"`
	LastUpdated     time.Time     `json:"last_updated"`
	mutex           sync.RWMutex
}

// ServiceDeployRequest 服务部署请求
type ServiceDeployRequest struct {
	Name        string         `json:"name" binding:"required"`
	Image       string         `json:"image" binding:"required"`
	Port        int            `json:"port" binding:"required"`
	Replicas    int            `json:"replicas"`
	Config      *ServiceConfig `json:"config"`
}

// NewServiceManager 创建服务管理器
func NewServiceManager(
	containerManager container.ContainerManager,
	loadBalancer *loadbalancer.LoadBalancer,
	serviceRegistry *loadbalancer.ServiceRegistry,
	config *ServiceManagerConfig,
	logger logger.Logger,
) *ServiceManager {
	if config == nil {
		config = getDefaultServiceManagerConfig()
	}

	sm := &ServiceManager{
		containerManager: containerManager,
		loadBalancer:     loadBalancer,
		serviceRegistry:  serviceRegistry,
		logger:           logger,
		config:           config,
		services:         make(map[string]*ManagedService),
	}

	// 启动监控任务
	go sm.startMonitoringTask()

	return sm
}

// getDefaultServiceManagerConfig 获取默认配置
func getDefaultServiceManagerConfig() *ServiceManagerConfig {
	return &ServiceManagerConfig{
		DefaultReplicas:     2,
		MaxReplicas:         10,
		MinReplicas:         1,
		HealthCheckInterval: 30 * time.Second,
		ScaleUpThreshold:    80.0,
		ScaleDownThreshold:  20.0,
		AutoScalingEnabled:  true,
	}
}

// DeployService 部署服务
func (sm *ServiceManager) DeployService(ctx context.Context, request *ServiceDeployRequest) (*ManagedService, error) {
	sm.logger.Info("开始部署服务", "name", request.Name, "image", request.Image, "replicas", request.Replicas)

	// 验证请求
	if err := sm.validateDeployRequest(request); err != nil {
		return nil, fmt.Errorf("部署请求验证失败: %w", err)
	}

	// 创建托管服务
	service := &ManagedService{
		ID:              sm.generateServiceID(),
		Name:            request.Name,
		Image:           request.Image,
		Port:            request.Port,
		Replicas:        0,
		DesiredReplicas: request.Replicas,
		Instances:       make([]*ServiceInstance, 0),
		Config:          request.Config,
		Status:          "deploying",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		Metrics:         &ServiceMetrics{},
	}

	if service.DesiredReplicas == 0 {
		service.DesiredReplicas = sm.config.DefaultReplicas
	}

	// 设置默认配置
	sm.setDefaultServiceConfig(service)

	// 保存服务
	sm.mutex.Lock()
	sm.services[service.ID] = service
	sm.mutex.Unlock()

	// 启动服务实例
	if err := sm.scaleService(ctx, service, service.DesiredReplicas); err != nil {
		service.Status = "error"
		return nil, fmt.Errorf("启动服务实例失败: %w", err)
	}

	service.Status = "running"
	service.UpdatedAt = time.Now()

	sm.logger.Info("服务部署成功", "service_id", service.ID, "name", service.Name, "replicas", service.Replicas)
	return service, nil
}

// validateDeployRequest 验证部署请求
func (sm *ServiceManager) validateDeployRequest(request *ServiceDeployRequest) error {
	if request.Name == "" {
		return fmt.Errorf("服务名称不能为空")
	}
	if request.Image == "" {
		return fmt.Errorf("镜像不能为空")
	}
	if request.Port <= 0 || request.Port > 65535 {
		return fmt.Errorf("端口必须在1-65535范围内")
	}
	if request.Replicas < 0 {
		return fmt.Errorf("副本数不能为负数")
	}
	if request.Replicas > sm.config.MaxReplicas {
		return fmt.Errorf("副本数不能超过最大限制: %d", sm.config.MaxReplicas)
	}

	// 检查服务名称是否已存在
	sm.mutex.RLock()
	for _, service := range sm.services {
		if service.Name == request.Name {
			sm.mutex.RUnlock()
			return fmt.Errorf("服务名称已存在: %s", request.Name)
		}
	}
	sm.mutex.RUnlock()

	return nil
}

// generateServiceID 生成服务ID
func (sm *ServiceManager) generateServiceID() string {
	return fmt.Sprintf("svc-%d", time.Now().UnixNano())
}

// setDefaultServiceConfig 设置默认服务配置
func (sm *ServiceManager) setDefaultServiceConfig(service *ManagedService) {
	if service.Config == nil {
		service.Config = &ServiceConfig{}
	}

	if service.Config.Resources == nil {
		service.Config.Resources = &ResourceRequirements{
			CPURequest:    "100m",
			CPULimit:      "500m",
			MemoryRequest: "128Mi",
			MemoryLimit:   "512Mi",
		}
	}

	if service.Config.HealthCheck == nil {
		service.Config.HealthCheck = &HealthCheckConfig{
			Path:               "/health",
			Port:               service.Port,
			Interval:           30 * time.Second,
			Timeout:            5 * time.Second,
			HealthyThreshold:   2,
			UnhealthyThreshold: 3,
		}
	}

	if service.Config.LoadBalancing == nil {
		service.Config.LoadBalancing = &LoadBalancingConfig{
			Algorithm:       "round_robin",
			SessionAffinity: false,
		}
	}

	if service.Config.AutoScaling == nil {
		service.Config.AutoScaling = &AutoScalingConfig{
			Enabled:               sm.config.AutoScalingEnabled,
			MinReplicas:          sm.config.MinReplicas,
			MaxReplicas:          sm.config.MaxReplicas,
			TargetCPUUtilization: 70.0,
			ScaleUpCooldown:      5 * time.Minute,
			ScaleDownCooldown:    10 * time.Minute,
		}
	}
}
