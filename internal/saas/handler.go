package saas

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"paas-platform/pkg/logger"
)

// Handler SaaS HTTP处理器
type Handler struct {
	serviceManager *ServiceManager
	logger         logger.Logger
}

// NewHandler 创建SaaS处理器
func NewHandler(serviceManager *ServiceManager, logger logger.Logger) *Handler {
	return &Handler{
		serviceManager: serviceManager,
		logger:         logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 服务管理路由
	router.POST("/services", h.DeployService)
	router.GET("/services", h.ListServices)
	router.GET("/services/:id", h.GetService)
	router.PUT("/services/:id", h.UpdateService)
	router.DELETE("/services/:id", h.DeleteService)
	
	// 服务实例管理
	router.GET("/services/:id/instances", h.GetServiceInstances)
	router.POST("/services/:id/scale", h.ScaleService)
	router.POST("/services/:id/restart", h.RestartService)
	
	// 服务监控
	router.GET("/services/:id/metrics", h.GetServiceMetrics)
	router.GET("/services/:id/logs", h.GetServiceLogs)
	router.GET("/services/:id/health", h.CheckServiceHealth)
	
	// 全局监控
	router.GET("/services/metrics/summary", h.GetMetricsSummary)
	router.GET("/services/health", h.HealthCheck)
}

// DeployService 部署服务
// @Summary 部署服务
// @Description 部署新的SaaS服务
// @Tags SaaS
// @Accept json
// @Produce json
// @Param request body ServiceDeployRequest true "服务部署请求"
// @Success 201 {object} ManagedService "部署成功"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/saas/services [post]
func (h *Handler) DeployService(c *gin.Context) {
	var request ServiceDeployRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("收到服务部署请求", "name", request.Name, "image", request.Image, "replicas", request.Replicas)

	// 部署服务
	service, err := h.serviceManager.DeployService(c.Request.Context(), &request)
	if err != nil {
		h.logger.Error("服务部署失败", "name", request.Name, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "服务部署失败",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("服务部署成功", "service_id", service.ID, "name", service.Name)

	c.JSON(http.StatusCreated, gin.H{
		"message": "服务部署成功",
		"service": service,
	})
}

// ListServices 列出所有服务
// @Summary 列出所有服务
// @Description 获取所有已部署的SaaS服务列表
// @Tags SaaS
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(10)
// @Param status query string false "服务状态过滤"
// @Success 200 {object} map[string]interface{} "服务列表"
// @Router /api/v1/saas/services [get]
func (h *Handler) ListServices(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	statusFilter := c.Query("status")

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	h.logger.Debug("列出服务", "page", page, "size", size, "status_filter", statusFilter)

	// 获取服务列表
	services := h.serviceManager.ListServices(statusFilter)

	// 分页处理
	total := len(services)
	start := (page - 1) * size
	end := start + size

	if start >= total {
		services = []*ManagedService{}
	} else {
		if end > total {
			end = total
		}
		services = services[start:end]
	}

	c.JSON(http.StatusOK, gin.H{
		"services": services,
		"pagination": gin.H{
			"page":       page,
			"size":       size,
			"total":      total,
			"total_pages": (total + size - 1) / size,
		},
		"timestamp": time.Now(),
	})
}

// GetService 获取服务详情
// @Summary 获取服务详情
// @Description 获取指定服务的详细信息
// @Tags SaaS
// @Produce json
// @Param id path string true "服务ID"
// @Success 200 {object} ManagedService "服务详情"
// @Failure 404 {object} map[string]interface{} "服务不存在"
// @Router /api/v1/saas/services/{id} [get]
func (h *Handler) GetService(c *gin.Context) {
	serviceID := c.Param("id")
	
	h.logger.Debug("获取服务详情", "service_id", serviceID)

	service := h.serviceManager.GetService(serviceID)
	if service == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "服务不存在",
			"service_id": serviceID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"service": service,
		"timestamp": time.Now(),
	})
}

// UpdateService 更新服务
// @Summary 更新服务
// @Description 更新服务配置
// @Tags SaaS
// @Accept json
// @Produce json
// @Param id path string true "服务ID"
// @Param request body ServiceUpdateRequest true "服务更新请求"
// @Success 200 {object} ManagedService "更新成功"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 404 {object} map[string]interface{} "服务不存在"
// @Router /api/v1/saas/services/{id} [put]
func (h *Handler) UpdateService(c *gin.Context) {
	serviceID := c.Param("id")
	
	var request ServiceUpdateRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("收到服务更新请求", "service_id", serviceID)

	service, err := h.serviceManager.UpdateService(c.Request.Context(), serviceID, &request)
	if err != nil {
		if err.Error() == "服务不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "服务不存在",
				"service_id": serviceID,
			})
		} else {
			h.logger.Error("服务更新失败", "service_id", serviceID, "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "服务更新失败",
				"details": err.Error(),
			})
		}
		return
	}

	h.logger.Info("服务更新成功", "service_id", serviceID)

	c.JSON(http.StatusOK, gin.H{
		"message": "服务更新成功",
		"service": service,
	})
}

// DeleteService 删除服务
// @Summary 删除服务
// @Description 删除指定的服务及其所有实例
// @Tags SaaS
// @Produce json
// @Param id path string true "服务ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Failure 404 {object} map[string]interface{} "服务不存在"
// @Failure 500 {object} map[string]interface{} "删除失败"
// @Router /api/v1/saas/services/{id} [delete]
func (h *Handler) DeleteService(c *gin.Context) {
	serviceID := c.Param("id")
	
	h.logger.Info("收到服务删除请求", "service_id", serviceID)

	if err := h.serviceManager.DeleteService(c.Request.Context(), serviceID); err != nil {
		if err.Error() == "服务不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "服务不存在",
				"service_id": serviceID,
			})
		} else {
			h.logger.Error("服务删除失败", "service_id", serviceID, "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "服务删除失败",
				"details": err.Error(),
			})
		}
		return
	}

	h.logger.Info("服务删除成功", "service_id", serviceID)

	c.JSON(http.StatusOK, gin.H{
		"message": "服务删除成功",
		"service_id": serviceID,
		"timestamp": time.Now(),
	})
}

// ScaleService 扩缩容服务
// @Summary 扩缩容服务
// @Description 调整服务的副本数量
// @Tags SaaS
// @Accept json
// @Produce json
// @Param id path string true "服务ID"
// @Param request body ScaleRequest true "扩缩容请求"
// @Success 200 {object} map[string]interface{} "扩缩容成功"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 404 {object} map[string]interface{} "服务不存在"
// @Router /api/v1/saas/services/{id}/scale [post]
func (h *Handler) ScaleService(c *gin.Context) {
	serviceID := c.Param("id")
	
	var request ScaleRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("收到服务扩缩容请求", "service_id", serviceID, "replicas", request.Replicas)

	if err := h.serviceManager.ScaleService(c.Request.Context(), serviceID, request.Replicas); err != nil {
		if err.Error() == "服务不存在" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "服务不存在",
				"service_id": serviceID,
			})
		} else {
			h.logger.Error("服务扩缩容失败", "service_id", serviceID, "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "服务扩缩容失败",
				"details": err.Error(),
			})
		}
		return
	}

	h.logger.Info("服务扩缩容成功", "service_id", serviceID, "replicas", request.Replicas)

	c.JSON(http.StatusOK, gin.H{
		"message": "服务扩缩容成功",
		"service_id": serviceID,
		"replicas": request.Replicas,
		"timestamp": time.Now(),
	})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description SaaS服务管理器健康检查
// @Tags SaaS
// @Produce json
// @Success 200 {object} map[string]interface{} "健康状态"
// @Router /api/v1/saas/services/health [get]
func (h *Handler) HealthCheck(c *gin.Context) {
	summary := h.serviceManager.GetServicesSummary()

	health := gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"service":   "saas-service-manager",
		"summary": gin.H{
			"total_services":   summary.TotalServices,
			"running_services": summary.RunningServices,
			"total_instances":  summary.TotalInstances,
			"healthy_instances": summary.HealthyInstances,
		},
	}

	// 检查服务健康状态
	if summary.RunningServices < summary.TotalServices {
		health["status"] = "warning"
		health["warning"] = "部分服务未运行"
	}

	if summary.HealthyInstances < summary.TotalInstances {
		health["status"] = "warning"
		health["warning"] = "部分实例不健康"
	}

	c.JSON(http.StatusOK, health)
}

// ServiceUpdateRequest 服务更新请求
type ServiceUpdateRequest struct {
	Image    string         `json:"image"`
	Replicas *int           `json:"replicas"`
	Config   *ServiceConfig `json:"config"`
}

// ScaleRequest 扩缩容请求
type ScaleRequest struct {
	Replicas int `json:"replicas" binding:"required,min=0"`
}

// ServicesSummary 服务摘要
type ServicesSummary struct {
	TotalServices    int `json:"total_services"`
	RunningServices  int `json:"running_services"`
	TotalInstances   int `json:"total_instances"`
	HealthyInstances int `json:"healthy_instances"`
}
