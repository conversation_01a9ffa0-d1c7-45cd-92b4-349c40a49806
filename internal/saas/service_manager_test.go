package saas

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"paas-platform/internal/container"
	"paas-platform/internal/loadbalancer"
	"paas-platform/pkg/logger"
)

// MockLoadBalancer 模拟负载均衡器
type MockLoadBalancer struct {
	mock.Mock
}

// MockServiceRegistry 模拟服务注册中心
type MockServiceRegistry struct {
	mock.Mock
}

func (m *MockServiceRegistry) RegisterService(req *loadbalancer.RegistrationRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *MockServiceRegistry) DeregisterService(serviceName, instanceID string) error {
	args := m.Called(serviceName, instanceID)
	return args.Error(0)
}

func (m *MockServiceRegistry) GetService(serviceName string) (*loadbalancer.ServiceGroup, error) {
	args := m.Called(serviceName)
	return args.Get(0).(*loadbalancer.ServiceGroup), args.Error(1)
}

func (m *MockServiceRegistry) GetHealthyInstances(serviceName string) ([]*loadbalancer.ServiceInstance, error) {
	args := m.Called(serviceName)
	return args.Get(0).([]*loadbalancer.ServiceInstance), args.Error(1)
}

func (m *MockServiceRegistry) ListServices() map[string]*loadbalancer.ServiceGroup {
	args := m.Called()
	return args.Get(0).(map[string]*loadbalancer.ServiceGroup)
}

func (m *MockServiceRegistry) UpdateInstanceStatus(serviceName, instanceID, status string) {
	m.Called(serviceName, instanceID, status)
}

func (m *MockServiceRegistry) UpdateInstanceStats(serviceName, instanceID string, requests, errors int64) {
	m.Called(serviceName, instanceID, requests, errors)
}

func (m *MockServiceRegistry) Start() {
	m.Called()
}

func (m *MockServiceRegistry) Stop() {
	m.Called()
}

// MockContainerManager 模拟容器管理器（重用FaaS测试中的）
type MockContainerManager struct {
	mock.Mock
}

func (m *MockContainerManager) CreateContainer(ctx context.Context, config *container.ContainerConfig) (*container.Container, error) {
	args := m.Called(ctx, config)
	return args.Get(0).(*container.Container), args.Error(1)
}

func (m *MockContainerManager) StartContainer(ctx context.Context, containerID string) error {
	args := m.Called(ctx, containerID)
	return args.Error(0)
}

func (m *MockContainerManager) StopContainer(ctx context.Context, containerID string, timeout time.Duration) error {
	args := m.Called(ctx, containerID, timeout)
	return args.Error(0)
}

func (m *MockContainerManager) RemoveContainer(ctx context.Context, containerID string, force bool) error {
	args := m.Called(ctx, containerID, force)
	return args.Error(0)
}

func (m *MockContainerManager) GetContainerLogs(ctx context.Context, containerID string) (interface{}, error) {
	args := m.Called(ctx, containerID)
	return args.Get(0), args.Error(1)
}

func (m *MockContainerManager) GetContainerStatus(ctx context.Context, containerID string) (*container.ContainerStatus, error) {
	args := m.Called(ctx, containerID)
	return args.Get(0).(*container.ContainerStatus), args.Error(1)
}

func (m *MockContainerManager) ExecCommand(ctx context.Context, containerID string, cmd []string) (*container.ExecResult, error) {
	args := m.Called(ctx, containerID, cmd)
	return args.Get(0).(*container.ExecResult), args.Error(1)
}

func (m *MockContainerManager) PullImage(ctx context.Context, image string) error {
	args := m.Called(ctx, image)
	return args.Error(0)
}

func (m *MockContainerManager) ImageExists(ctx context.Context, image string) (bool, error) {
	args := m.Called(ctx, image)
	return args.Bool(0), args.Error(1)
}

func (m *MockContainerManager) GetContainerStats(ctx context.Context, containerID string) (*container.ContainerStats, error) {
	args := m.Called(ctx, containerID)
	return args.Get(0).(*container.ContainerStats), args.Error(1)
}

// TestNewServiceManager 测试创建服务管理器
func TestNewServiceManager(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLoadBalancer := new(MockLoadBalancer)
	mockServiceRegistry := new(MockServiceRegistry)
	mockLogger := logger.NewConsoleLogger("test")

	config := &ServiceManagerConfig{
		DefaultReplicas:     3,
		MaxReplicas:         20,
		MinReplicas:         1,
		HealthCheckInterval: 30 * time.Second,
		ScaleUpThreshold:    80.0,
		ScaleDownThreshold:  20.0,
		AutoScalingEnabled:  true,
	}

	sm := NewServiceManager(mockContainerManager, mockLoadBalancer, mockServiceRegistry, config, mockLogger)

	assert.NotNil(t, sm)
	assert.Equal(t, mockContainerManager, sm.containerManager)
	assert.Equal(t, mockLoadBalancer, sm.loadBalancer)
	assert.Equal(t, mockServiceRegistry, sm.serviceRegistry)
	assert.Equal(t, config, sm.config)
	assert.NotNil(t, sm.services)
}

// TestValidateDeployRequest 测试部署请求验证
func TestValidateDeployRequest(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLoadBalancer := new(MockLoadBalancer)
	mockServiceRegistry := new(MockServiceRegistry)
	mockLogger := logger.NewConsoleLogger("test")

	sm := NewServiceManager(mockContainerManager, mockLoadBalancer, mockServiceRegistry, nil, mockLogger)

	tests := []struct {
		name    string
		request *ServiceDeployRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效请求",
			request: &ServiceDeployRequest{
				Name:     "test-service",
				Image:    "nginx:latest",
				Port:     80,
				Replicas: 2,
			},
			wantErr: false,
		},
		{
			name: "缺少服务名称",
			request: &ServiceDeployRequest{
				Image:    "nginx:latest",
				Port:     80,
				Replicas: 2,
			},
			wantErr: true,
			errMsg:  "服务名称不能为空",
		},
		{
			name: "缺少镜像",
			request: &ServiceDeployRequest{
				Name:     "test-service",
				Port:     80,
				Replicas: 2,
			},
			wantErr: true,
			errMsg:  "镜像不能为空",
		},
		{
			name: "无效端口",
			request: &ServiceDeployRequest{
				Name:     "test-service",
				Image:    "nginx:latest",
				Port:     0,
				Replicas: 2,
			},
			wantErr: true,
			errMsg:  "端口必须在1-65535范围内",
		},
		{
			name: "负数副本",
			request: &ServiceDeployRequest{
				Name:     "test-service",
				Image:    "nginx:latest",
				Port:     80,
				Replicas: -1,
			},
			wantErr: true,
			errMsg:  "副本数不能为负数",
		},
		{
			name: "副本数超过限制",
			request: &ServiceDeployRequest{
				Name:     "test-service",
				Image:    "nginx:latest",
				Port:     80,
				Replicas: 15, // 超过默认最大值10
			},
			wantErr: true,
			errMsg:  "副本数不能超过最大限制",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := sm.validateDeployRequest(tt.request)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestGenerateServiceID 测试生成服务ID
func TestGenerateServiceID(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLoadBalancer := new(MockLoadBalancer)
	mockServiceRegistry := new(MockServiceRegistry)
	mockLogger := logger.NewConsoleLogger("test")

	sm := NewServiceManager(mockContainerManager, mockLoadBalancer, mockServiceRegistry, nil, mockLogger)

	id1 := sm.generateServiceID()
	id2 := sm.generateServiceID()

	assert.NotEmpty(t, id1)
	assert.NotEmpty(t, id2)
	assert.NotEqual(t, id1, id2)
	assert.Contains(t, id1, "svc-")
	assert.Contains(t, id2, "svc-")
}

// TestSetDefaultServiceConfig 测试设置默认服务配置
func TestSetDefaultServiceConfig(t *testing.T) {
	mockContainerManager := new(MockContainerManager)
	mockLoadBalancer := new(MockLoadBalancer)
	mockServiceRegistry := new(MockServiceRegistry)
	mockLogger := logger.NewConsoleLogger("test")

	sm := NewServiceManager(mockContainerManager, mockLoadBalancer, mockServiceRegistry, nil, mockLogger)

	service := &ManagedService{
		ID:   "test-service-1",
		Name: "test-service",
		Port: 8080,
	}

	sm.setDefaultServiceConfig(service)

	assert.NotNil(t, service.Config)
	assert.NotNil(t, service.Config.Resources)
	assert.NotNil(t, service.Config.HealthCheck)
	assert.NotNil(t, service.Config.LoadBalancing)
	assert.NotNil(t, service.Config.AutoScaling)

	// 检查默认资源配置
	assert.Equal(t, "100m", service.Config.Resources.CPURequest)
	assert.Equal(t, "500m", service.Config.Resources.CPULimit)
	assert.Equal(t, "128Mi", service.Config.Resources.MemoryRequest)
	assert.Equal(t, "512Mi", service.Config.Resources.MemoryLimit)

	// 检查默认健康检查配置
	assert.Equal(t, "/health", service.Config.HealthCheck.Path)
	assert.Equal(t, 8080, service.Config.HealthCheck.Port)
	assert.Equal(t, 30*time.Second, service.Config.HealthCheck.Interval)
	assert.Equal(t, 5*time.Second, service.Config.HealthCheck.Timeout)

	// 检查默认负载均衡配置
	assert.Equal(t, "round_robin", service.Config.LoadBalancing.Algorithm)
	assert.False(t, service.Config.LoadBalancing.SessionAffinity)

	// 检查默认自动扩缩容配置
	assert.True(t, service.Config.AutoScaling.Enabled)
	assert.Equal(t, 1, service.Config.AutoScaling.MinReplicas)
	assert.Equal(t, 10, service.Config.AutoScaling.MaxReplicas)
	assert.Equal(t, 70.0, service.Config.AutoScaling.TargetCPUUtilization)
}

// TestServiceMetrics 测试服务指标
func TestServiceMetrics(t *testing.T) {
	metrics := &ServiceMetrics{
		RequestCount:   100,
		ErrorCount:     5,
		AverageLatency: 150 * time.Millisecond,
		CPUUsage:       45.5,
		MemoryUsage:    256 * 1024 * 1024, // 256MB
		LastUpdated:    time.Now(),
	}

	assert.Equal(t, int64(100), metrics.RequestCount)
	assert.Equal(t, int64(5), metrics.ErrorCount)
	assert.Equal(t, 150*time.Millisecond, metrics.AverageLatency)
	assert.Equal(t, 45.5, metrics.CPUUsage)
	assert.Equal(t, int64(256*1024*1024), metrics.MemoryUsage)
}

// TestServiceInstance 测试服务实例
func TestServiceInstance(t *testing.T) {
	instance := &ServiceInstance{
		ID:          "instance-1",
		ServiceID:   "service-1",
		ContainerID: "container-1",
		Host:        "localhost",
		Port:        8080,
		Status:      "running",
		Health:      "healthy",
		CreatedAt:   time.Now(),
		StartedAt:   time.Now(),
		Metadata: map[string]string{
			"version": "1.0.0",
			"region":  "us-west-1",
		},
	}

	assert.Equal(t, "instance-1", instance.ID)
	assert.Equal(t, "service-1", instance.ServiceID)
	assert.Equal(t, "container-1", instance.ContainerID)
	assert.Equal(t, "localhost", instance.Host)
	assert.Equal(t, 8080, instance.Port)
	assert.Equal(t, "running", instance.Status)
	assert.Equal(t, "healthy", instance.Health)
	assert.Equal(t, "1.0.0", instance.Metadata["version"])
	assert.Equal(t, "us-west-1", instance.Metadata["region"])
}

// TestManagedServiceConcurrency 测试托管服务的并发安全性
func TestManagedServiceConcurrency(t *testing.T) {
	service := &ManagedService{
		ID:              "test-service-1",
		Name:            "test-service",
		Image:           "nginx:latest",
		Port:            80,
		Replicas:        0,
		DesiredReplicas: 3,
		Instances:       make([]*ServiceInstance, 0),
		Status:          "running",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		Metrics:         &ServiceMetrics{},
	}

	// 并发读写服务状态
	done := make(chan bool, 2)

	go func() {
		for i := 0; i < 100; i++ {
			service.mutex.Lock()
			service.Status = "scaling"
			service.UpdatedAt = time.Now()
			service.mutex.Unlock()
			time.Sleep(1 * time.Millisecond)
		}
		done <- true
	}()

	go func() {
		for i := 0; i < 100; i++ {
			service.mutex.RLock()
			_ = service.Status
			_ = service.UpdatedAt
			service.mutex.RUnlock()
			time.Sleep(1 * time.Millisecond)
		}
		done <- true
	}()

	// 等待两个goroutine完成
	<-done
	<-done

	// 测试应该没有panic或死锁
	assert.True(t, true)
}

// TestResourceRequirements 测试资源需求配置
func TestResourceRequirements(t *testing.T) {
	resources := &ResourceRequirements{
		CPURequest:    "100m",
		CPULimit:      "500m",
		MemoryRequest: "128Mi",
		MemoryLimit:   "512Mi",
	}

	assert.Equal(t, "100m", resources.CPURequest)
	assert.Equal(t, "500m", resources.CPULimit)
	assert.Equal(t, "128Mi", resources.MemoryRequest)
	assert.Equal(t, "512Mi", resources.MemoryLimit)
}

// TestHealthCheckConfig 测试健康检查配置
func TestHealthCheckConfig(t *testing.T) {
	healthCheck := &HealthCheckConfig{
		Path:               "/health",
		Port:               8080,
		Interval:           30 * time.Second,
		Timeout:            5 * time.Second,
		HealthyThreshold:   2,
		UnhealthyThreshold: 3,
	}

	assert.Equal(t, "/health", healthCheck.Path)
	assert.Equal(t, 8080, healthCheck.Port)
	assert.Equal(t, 30*time.Second, healthCheck.Interval)
	assert.Equal(t, 5*time.Second, healthCheck.Timeout)
	assert.Equal(t, 2, healthCheck.HealthyThreshold)
	assert.Equal(t, 3, healthCheck.UnhealthyThreshold)
}

// TestAutoScalingConfig 测试自动扩缩容配置
func TestAutoScalingConfig(t *testing.T) {
	autoScaling := &AutoScalingConfig{
		Enabled:               true,
		MinReplicas:          1,
		MaxReplicas:          10,
		TargetCPUUtilization: 70.0,
		ScaleUpCooldown:      5 * time.Minute,
		ScaleDownCooldown:    10 * time.Minute,
	}

	assert.True(t, autoScaling.Enabled)
	assert.Equal(t, 1, autoScaling.MinReplicas)
	assert.Equal(t, 10, autoScaling.MaxReplicas)
	assert.Equal(t, 70.0, autoScaling.TargetCPUUtilization)
	assert.Equal(t, 5*time.Minute, autoScaling.ScaleUpCooldown)
	assert.Equal(t, 10*time.Minute, autoScaling.ScaleDownCooldown)
}
