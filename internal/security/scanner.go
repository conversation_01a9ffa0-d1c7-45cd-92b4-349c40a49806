package security

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"paas-platform/pkg/logger"
)

// SecurityScanner 安全扫描器
type SecurityScanner struct {
	db     *gorm.DB
	logger logger.Logger
	config *SecurityConfig
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	// 密码策略
	PasswordMinLength    int    `yaml:"password_min_length"`
	PasswordRequireUpper bool   `yaml:"password_require_upper"`
	PasswordRequireLower bool   `yaml:"password_require_lower"`
	PasswordRequireDigit bool   `yaml:"password_require_digit"`
	PasswordRequireSymbol bool  `yaml:"password_require_symbol"`
	
	// JWT 配置
	JWTSecret           string        `yaml:"jwt_secret"`
	JWTExpiry           time.Duration `yaml:"jwt_expiry"`
	JWTRefreshExpiry    time.Duration `yaml:"jwt_refresh_expiry"`
	
	// 限流配置
	RateLimitEnabled    bool `yaml:"rate_limit_enabled"`
	RateLimitRequests   int  `yaml:"rate_limit_requests"`
	RateLimitWindow     time.Duration `yaml:"rate_limit_window"`
	
	// CORS 配置
	CORSEnabled         bool     `yaml:"cors_enabled"`
	CORSAllowedOrigins  []string `yaml:"cors_allowed_origins"`
	CORSAllowedMethods  []string `yaml:"cors_allowed_methods"`
	CORSAllowedHeaders  []string `yaml:"cors_allowed_headers"`
	
	// 安全头配置
	SecurityHeadersEnabled bool `yaml:"security_headers_enabled"`
	
	// 输入验证
	InputValidationEnabled bool `yaml:"input_validation_enabled"`
	MaxRequestSize         int64 `yaml:"max_request_size"`
	
	// SQL 注入防护
	SQLInjectionProtection bool `yaml:"sql_injection_protection"`
	
	// XSS 防护
	XSSProtection bool `yaml:"xss_protection"`
}

// SecurityVulnerability 安全漏洞
type SecurityVulnerability struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`        // password, jwt, injection, xss, etc.
	Severity    string    `json:"severity"`    // low, medium, high, critical
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Location    string    `json:"location"`
	Solution    string    `json:"solution"`
	DetectedAt  time.Time `json:"detected_at"`
}

// SecurityReport 安全报告
type SecurityReport struct {
	ScanID          string                   `json:"scan_id"`
	ScanTime        time.Time               `json:"scan_time"`
	Vulnerabilities []SecurityVulnerability `json:"vulnerabilities"`
	Summary         SecuritySummary         `json:"summary"`
	Recommendations []string                `json:"recommendations"`
}

// SecuritySummary 安全摘要
type SecuritySummary struct {
	TotalVulnerabilities int `json:"total_vulnerabilities"`
	CriticalCount       int `json:"critical_count"`
	HighCount           int `json:"high_count"`
	MediumCount         int `json:"medium_count"`
	LowCount            int `json:"low_count"`
	SecurityScore       int `json:"security_score"` // 0-100
}

// NewSecurityScanner 创建安全扫描器
func NewSecurityScanner(db *gorm.DB, logger logger.Logger, config *SecurityConfig) *SecurityScanner {
	return &SecurityScanner{
		db:     db,
		logger: logger,
		config: config,
	}
}

// ScanSecurity 执行安全扫描
func (ss *SecurityScanner) ScanSecurity(ctx context.Context) (*SecurityReport, error) {
	scanID := generateScanID()
	ss.logger.Info("开始安全扫描", "scan_id", scanID)
	
	var vulnerabilities []SecurityVulnerability
	
	// 扫描密码安全
	passwordVulns := ss.scanPasswordSecurity()
	vulnerabilities = append(vulnerabilities, passwordVulns...)
	
	// 扫描 JWT 安全
	jwtVulns := ss.scanJWTSecurity()
	vulnerabilities = append(vulnerabilities, jwtVulns...)
	
	// 扫描 SQL 注入
	sqlVulns := ss.scanSQLInjection()
	vulnerabilities = append(vulnerabilities, sqlVulns...)
	
	// 扫描 XSS 漏洞
	xssVulns := ss.scanXSSVulnerabilities()
	vulnerabilities = append(vulnerabilities, xssVulns...)
	
	// 扫描配置安全
	configVulns := ss.scanConfigurationSecurity()
	vulnerabilities = append(vulnerabilities, configVulns...)
	
	// 生成摘要
	summary := ss.generateSummary(vulnerabilities)
	
	// 生成建议
	recommendations := ss.generateRecommendations(vulnerabilities)
	
	report := &SecurityReport{
		ScanID:          scanID,
		ScanTime:        time.Now(),
		Vulnerabilities: vulnerabilities,
		Summary:         summary,
		Recommendations: recommendations,
	}
	
	ss.logger.Info("安全扫描完成", 
		"scan_id", scanID,
		"total_vulnerabilities", summary.TotalVulnerabilities,
		"security_score", summary.SecurityScore)
	
	return report, nil
}

// scanPasswordSecurity 扫描密码安全
func (ss *SecurityScanner) scanPasswordSecurity() []SecurityVulnerability {
	var vulnerabilities []SecurityVulnerability
	
	// 检查密码策略配置
	if ss.config.PasswordMinLength < 8 {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "password",
			Severity:    "medium",
			Title:       "密码长度要求过低",
			Description: fmt.Sprintf("当前密码最小长度为 %d，建议至少 8 位", ss.config.PasswordMinLength),
			Location:    "security_config.password_min_length",
			Solution:    "将密码最小长度设置为 8 位或更多",
			DetectedAt:  time.Now(),
		})
	}
	
	if !ss.config.PasswordRequireUpper || !ss.config.PasswordRequireLower || 
	   !ss.config.PasswordRequireDigit || !ss.config.PasswordRequireSymbol {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "password",
			Severity:    "medium",
			Title:       "密码复杂度要求不足",
			Description: "密码策略未要求包含大小写字母、数字和特殊字符",
			Location:    "security_config.password_requirements",
			Solution:    "启用所有密码复杂度要求",
			DetectedAt:  time.Now(),
		})
	}
	
	return vulnerabilities
}

// scanJWTSecurity 扫描 JWT 安全
func (ss *SecurityScanner) scanJWTSecurity() []SecurityVulnerability {
	var vulnerabilities []SecurityVulnerability
	
	// 检查 JWT 密钥强度
	if len(ss.config.JWTSecret) < 32 {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "jwt",
			Severity:    "high",
			Title:       "JWT 密钥强度不足",
			Description: fmt.Sprintf("JWT 密钥长度为 %d，建议至少 32 字符", len(ss.config.JWTSecret)),
			Location:    "security_config.jwt_secret",
			Solution:    "使用至少 32 字符的强随机密钥",
			DetectedAt:  time.Now(),
		})
	}
	
	// 检查 JWT 过期时间
	if ss.config.JWTExpiry > 24*time.Hour {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "jwt",
			Severity:    "medium",
			Title:       "JWT 过期时间过长",
			Description: fmt.Sprintf("JWT 过期时间为 %v，建议不超过 24 小时", ss.config.JWTExpiry),
			Location:    "security_config.jwt_expiry",
			Solution:    "将 JWT 过期时间设置为 24 小时以内",
			DetectedAt:  time.Now(),
		})
	}
	
	return vulnerabilities
}

// scanSQLInjection 扫描 SQL 注入
func (ss *SecurityScanner) scanSQLInjection() []SecurityVulnerability {
	var vulnerabilities []SecurityVulnerability
	
	if !ss.config.SQLInjectionProtection {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "injection",
			Severity:    "high",
			Title:       "SQL 注入防护未启用",
			Description: "系统未启用 SQL 注入防护机制",
			Location:    "security_config.sql_injection_protection",
			Solution:    "启用 SQL 注入防护，使用参数化查询",
			DetectedAt:  time.Now(),
		})
	}
	
	return vulnerabilities
}

// scanXSSVulnerabilities 扫描 XSS 漏洞
func (ss *SecurityScanner) scanXSSVulnerabilities() []SecurityVulnerability {
	var vulnerabilities []SecurityVulnerability
	
	if !ss.config.XSSProtection {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "xss",
			Severity:    "medium",
			Title:       "XSS 防护未启用",
			Description: "系统未启用跨站脚本攻击防护",
			Location:    "security_config.xss_protection",
			Solution:    "启用 XSS 防护，对用户输入进行过滤和转义",
			DetectedAt:  time.Now(),
		})
	}
	
	return vulnerabilities
}

// scanConfigurationSecurity 扫描配置安全
func (ss *SecurityScanner) scanConfigurationSecurity() []SecurityVulnerability {
	var vulnerabilities []SecurityVulnerability
	
	// 检查 CORS 配置
	if !ss.config.CORSEnabled {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "configuration",
			Severity:    "low",
			Title:       "CORS 未配置",
			Description: "跨域资源共享未正确配置",
			Location:    "security_config.cors_enabled",
			Solution:    "配置适当的 CORS 策略",
			DetectedAt:  time.Now(),
		})
	}
	
	// 检查安全头
	if !ss.config.SecurityHeadersEnabled {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "configuration",
			Severity:    "medium",
			Title:       "安全头未启用",
			Description: "HTTP 安全头未正确配置",
			Location:    "security_config.security_headers_enabled",
			Solution:    "启用安全头，包括 HSTS、CSP、X-Frame-Options 等",
			DetectedAt:  time.Now(),
		})
	}
	
	// 检查限流
	if !ss.config.RateLimitEnabled {
		vulnerabilities = append(vulnerabilities, SecurityVulnerability{
			ID:          generateVulnID(),
			Type:        "configuration",
			Severity:    "medium",
			Title:       "API 限流未启用",
			Description: "API 请求限流未配置，可能导致 DDoS 攻击",
			Location:    "security_config.rate_limit_enabled",
			Solution:    "启用 API 限流保护",
			DetectedAt:  time.Now(),
		})
	}
	
	return vulnerabilities
}

// generateSummary 生成安全摘要
func (ss *SecurityScanner) generateSummary(vulnerabilities []SecurityVulnerability) SecuritySummary {
	summary := SecuritySummary{
		TotalVulnerabilities: len(vulnerabilities),
	}
	
	for _, vuln := range vulnerabilities {
		switch vuln.Severity {
		case "critical":
			summary.CriticalCount++
		case "high":
			summary.HighCount++
		case "medium":
			summary.MediumCount++
		case "low":
			summary.LowCount++
		}
	}
	
	// 计算安全分数 (0-100)
	score := 100
	score -= summary.CriticalCount * 25
	score -= summary.HighCount * 15
	score -= summary.MediumCount * 10
	score -= summary.LowCount * 5
	
	if score < 0 {
		score = 0
	}
	
	summary.SecurityScore = score
	
	return summary
}

// generateRecommendations 生成安全建议
func (ss *SecurityScanner) generateRecommendations(vulnerabilities []SecurityVulnerability) []string {
	recommendations := []string{
		"定期进行安全扫描和漏洞评估",
		"保持系统和依赖库的及时更新",
		"实施最小权限原则",
		"启用详细的安全日志记录",
		"建立安全事件响应流程",
	}
	
	// 基于发现的漏洞添加特定建议
	hasPasswordIssues := false
	hasJWTIssues := false
	hasInjectionIssues := false
	
	for _, vuln := range vulnerabilities {
		switch vuln.Type {
		case "password":
			hasPasswordIssues = true
		case "jwt":
			hasJWTIssues = true
		case "injection":
			hasInjectionIssues = true
		}
	}
	
	if hasPasswordIssues {
		recommendations = append(recommendations, "强化密码策略，要求复杂密码")
	}
	
	if hasJWTIssues {
		recommendations = append(recommendations, "优化 JWT 配置，使用强密钥和合理的过期时间")
	}
	
	if hasInjectionIssues {
		recommendations = append(recommendations, "实施严格的输入验证和参数化查询")
	}
	
	return recommendations
}

// ValidatePassword 验证密码强度
func (ss *SecurityScanner) ValidatePassword(password string) error {
	if len(password) < ss.config.PasswordMinLength {
		return fmt.Errorf("密码长度至少需要 %d 位", ss.config.PasswordMinLength)
	}
	
	if ss.config.PasswordRequireUpper && !regexp.MustCompile(`[A-Z]`).MatchString(password) {
		return fmt.Errorf("密码必须包含大写字母")
	}
	
	if ss.config.PasswordRequireLower && !regexp.MustCompile(`[a-z]`).MatchString(password) {
		return fmt.Errorf("密码必须包含小写字母")
	}
	
	if ss.config.PasswordRequireDigit && !regexp.MustCompile(`[0-9]`).MatchString(password) {
		return fmt.Errorf("密码必须包含数字")
	}
	
	if ss.config.PasswordRequireSymbol && !regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password) {
		return fmt.Errorf("密码必须包含特殊字符")
	}
	
	return nil
}

// HashPassword 安全地哈希密码
func (ss *SecurityScanner) HashPassword(password string) (string, error) {
	// 验证密码强度
	if err := ss.ValidatePassword(password); err != nil {
		return "", err
	}
	
	// 使用 bcrypt 哈希密码
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("密码哈希失败: %w", err)
	}
	
	return string(hashedBytes), nil
}

// VerifyPassword 验证密码
func (ss *SecurityScanner) VerifyPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// SanitizeInput 清理用户输入
func (ss *SecurityScanner) SanitizeInput(input string) string {
	if !ss.config.InputValidationEnabled {
		return input
	}
	
	// 移除潜在的 SQL 注入字符
	if ss.config.SQLInjectionProtection {
		input = strings.ReplaceAll(input, "'", "''")
		input = strings.ReplaceAll(input, "--", "")
		input = strings.ReplaceAll(input, "/*", "")
		input = strings.ReplaceAll(input, "*/", "")
	}
	
	// 移除潜在的 XSS 字符
	if ss.config.XSSProtection {
		input = strings.ReplaceAll(input, "<script", "&lt;script")
		input = strings.ReplaceAll(input, "</script>", "&lt;/script&gt;")
		input = strings.ReplaceAll(input, "javascript:", "")
		input = strings.ReplaceAll(input, "onload=", "")
		input = strings.ReplaceAll(input, "onerror=", "")
	}
	
	return input
}

// SecurityMiddleware 安全中间件
func (ss *SecurityScanner) SecurityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加安全头
		if ss.config.SecurityHeadersEnabled {
			c.Header("X-Content-Type-Options", "nosniff")
			c.Header("X-Frame-Options", "DENY")
			c.Header("X-XSS-Protection", "1; mode=block")
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
			c.Header("Content-Security-Policy", "default-src 'self'")
		}
		
		// 检查请求大小
		if ss.config.MaxRequestSize > 0 && c.Request.ContentLength > ss.config.MaxRequestSize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error": "请求体过大",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// 辅助函数

// generateScanID 生成扫描ID
func generateScanID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// generateVulnID 生成漏洞ID
func generateVulnID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// DefaultSecurityConfig 默认安全配置
func DefaultSecurityConfig() *SecurityConfig {
	return &SecurityConfig{
		PasswordMinLength:      8,
		PasswordRequireUpper:   true,
		PasswordRequireLower:   true,
		PasswordRequireDigit:   true,
		PasswordRequireSymbol:  true,
		JWTSecret:             generateSecureSecret(),
		JWTExpiry:             24 * time.Hour,
		JWTRefreshExpiry:      7 * 24 * time.Hour,
		RateLimitEnabled:      true,
		RateLimitRequests:     100,
		RateLimitWindow:       time.Minute,
		CORSEnabled:           true,
		CORSAllowedOrigins:    []string{"http://localhost:3000"},
		CORSAllowedMethods:    []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		CORSAllowedHeaders:    []string{"Content-Type", "Authorization"},
		SecurityHeadersEnabled: true,
		InputValidationEnabled: true,
		MaxRequestSize:        10 * 1024 * 1024, // 10MB
		SQLInjectionProtection: true,
		XSSProtection:         true,
	}
}

// generateSecureSecret 生成安全密钥
func generateSecureSecret() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	hash := sha256.Sum256(bytes)
	return hex.EncodeToString(hash[:])
}
