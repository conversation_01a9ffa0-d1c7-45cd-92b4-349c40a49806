package notification

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"paas-platform/pkg/logger"
)

// Handler 通知处理器
type Handler struct {
	service         NotificationService
	notificationSvc *NotificationService
	alertManager    *AlertManager
	logger          logger.Logger
}

// NewHandler 创建通知处理器
func NewHandler(service NotificationService, logger logger.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// NewHandlerWithAlert 创建带告警功能的通知处理器
func NewHandlerWithAlert(notificationSvc *NotificationService, alertManager *AlertManager, logger logger.Logger) *Handler {
	return &Handler{
		notificationSvc: notificationSvc,
		alertManager:    alertManager,
		logger:          logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 通知发送接口
	router.POST("/email", h.SendEmail)
	router.POST("/slack", h.SendSlack)
	router.POST("/webhook", h.SendWebhook)
	router.POST("/sms", h.SendSMS)
	router.POST("/dingtalk", h.SendDingTalk)
	router.POST("/wechat", h.SendWeChat)
	router.POST("/batch", h.SendBatch)
	
	// AlertManager Webhook 接口
	router.POST("/alertmanager", h.HandleAlertManagerWebhook)
	
	// 状态和统计接口
	router.GET("/status", h.GetStatus)
	router.GET("/statistics", h.GetStatistics)
	router.GET("/health", h.HealthCheck)
	
	// 测试接口
	router.POST("/test", h.TestNotification)
}

// SendEmail 发送邮件接口
func (h *Handler) SendEmail(c *gin.Context) {
	var req EmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析邮件请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.service.SendEmail(c.Request.Context(), &req); err != nil {
		h.logger.Error("发送邮件失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送邮件失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "邮件发送成功",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// SendSlack 发送 Slack 通知接口
func (h *Handler) SendSlack(c *gin.Context) {
	var req SlackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析 Slack 请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.service.SendSlack(c.Request.Context(), &req); err != nil {
		h.logger.Error("发送 Slack 通知失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送 Slack 通知失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Slack 通知发送成功",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// SendWebhook 发送 Webhook 通知接口
func (h *Handler) SendWebhook(c *gin.Context) {
	var req WebhookRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析 Webhook 请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.service.SendWebhook(c.Request.Context(), &req); err != nil {
		h.logger.Error("发送 Webhook 通知失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送 Webhook 通知失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Webhook 通知发送成功",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// SendSMS 发送短信接口
func (h *Handler) SendSMS(c *gin.Context) {
	var req SMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析短信请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.service.SendSMS(c.Request.Context(), &req); err != nil {
		h.logger.Error("发送短信失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送短信失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "短信发送成功",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// SendDingTalk 发送钉钉通知接口
func (h *Handler) SendDingTalk(c *gin.Context) {
	var req DingTalkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析钉钉请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.service.SendDingTalk(c.Request.Context(), &req); err != nil {
		h.logger.Error("发送钉钉通知失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送钉钉通知失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "钉钉通知发送成功",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// SendWeChat 发送企业微信通知接口
func (h *Handler) SendWeChat(c *gin.Context) {
	var req WeChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析企业微信请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.service.SendWeChat(c.Request.Context(), &req); err != nil {
		h.logger.Error("发送企业微信通知失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送企业微信通知失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "企业微信通知发送成功",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// SendBatch 批量发送通知接口
func (h *Handler) SendBatch(c *gin.Context) {
	var req BatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析批量请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	if err := h.service.SendBatch(c.Request.Context(), &req); err != nil {
		h.logger.Error("批量发送通知失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "批量发送通知失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "批量通知发送成功",
		"count": len(req.Notifications),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// AlertManagerAlert AlertManager 告警结构
type AlertManagerAlert struct {
	Status       string            `json:"status"`
	Labels       map[string]string `json:"labels"`
	Annotations  map[string]string `json:"annotations"`
	StartsAt     time.Time         `json:"startsAt"`
	EndsAt       time.Time         `json:"endsAt"`
	GeneratorURL string            `json:"generatorURL"`
	Fingerprint  string            `json:"fingerprint"`
}

// AlertManagerWebhook AlertManager Webhook 请求
type AlertManagerWebhook struct {
	Version           string              `json:"version"`
	GroupKey          string              `json:"groupKey"`
	TruncatedAlerts   int                 `json:"truncatedAlerts"`
	Status            string              `json:"status"`
	Receiver          string              `json:"receiver"`
	GroupLabels       map[string]string   `json:"groupLabels"`
	CommonLabels      map[string]string   `json:"commonLabels"`
	CommonAnnotations map[string]string   `json:"commonAnnotations"`
	ExternalURL       string              `json:"externalURL"`
	Alerts            []AlertManagerAlert `json:"alerts"`
}

// HandleAlertManagerWebhook 处理 AlertManager Webhook
func (h *Handler) HandleAlertManagerWebhook(c *gin.Context) {
	var webhook AlertManagerWebhook
	if err := c.ShouldBindJSON(&webhook); err != nil {
		h.logger.Error("解析 AlertManager Webhook 失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("收到 AlertManager Webhook",
		"status", webhook.Status,
		"receiver", webhook.Receiver,
		"alerts_count", len(webhook.Alerts))

	// 处理每个告警
	for _, alert := range webhook.Alerts {
		if err := h.processAlert(c.Request.Context(), alert, webhook); err != nil {
			h.logger.Error("处理告警失败",
				"alert", alert.Labels["alertname"],
				"error", err)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "AlertManager Webhook 处理成功",
		"processed_alerts": len(webhook.Alerts),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// processAlert 处理单个告警
func (h *Handler) processAlert(ctx gin.Context, alert AlertManagerAlert, webhook AlertManagerWebhook) error {
	// 提取告警信息
	alertName := alert.Labels["alertname"]
	service := alert.Labels["service"]
	severity := alert.Labels["severity"]
	summary := alert.Annotations["summary"]
	description := alert.Annotations["description"]

	h.logger.Info("处理告警",
		"alert", alertName,
		"service", service,
		"severity", severity)

	// 根据严重程度发送不同的通知
	switch severity {
	case "critical":
		// 发送邮件通知
		emailReq := &EmailRequest{
			To:      []string{"<EMAIL>", "<EMAIL>"},
			Subject: fmt.Sprintf("🚨 [CRITICAL] PaaS Platform - %s", alertName),
			Body: fmt.Sprintf(`
严重告警详情:

告警名称: %s
服务名称: %s
严重程度: %s
告警时间: %s
告警描述: %s
处理建议: %s

请立即处理此告警！
			`, alertName, service, severity, alert.StartsAt.Format(time.RFC3339), description, summary),
			IsHTML: false,
		}
		
		if err := h.service.SendEmail(ctx.Request.Context(), emailReq); err != nil {
			h.logger.Error("发送严重告警邮件失败", "error", err)
		}

		// 发送 Slack 通知
		slackReq := &SlackRequest{
			Channel: "#paas-critical-alerts",
			Text:    fmt.Sprintf("🚨 严重告警: %s", alertName),
			Attachments: []Attachment{
				{
					Color: "danger",
					Title: fmt.Sprintf("严重告警: %s", alertName),
					Text:  description,
					Fields: []Field{
						{Title: "服务", Value: service, Short: true},
						{Title: "严重程度", Value: severity, Short: true},
						{Title: "时间", Value: alert.StartsAt.Format("2006-01-02 15:04:05"), Short: true},
					},
					Timestamp: alert.StartsAt.Unix(),
				},
			},
		}
		
		if err := h.service.SendSlack(ctx.Request.Context(), slackReq); err != nil {
			h.logger.Error("发送严重告警 Slack 通知失败", "error", err)
		}

	case "warning":
		// 发送 Slack 通知
		slackReq := &SlackRequest{
			Channel: "#paas-warnings",
			Text:    fmt.Sprintf("⚠️ 警告: %s", alertName),
			Attachments: []Attachment{
				{
					Color: "warning",
					Title: fmt.Sprintf("警告: %s", alertName),
					Text:  description,
					Fields: []Field{
						{Title: "服务", Value: service, Short: true},
						{Title: "时间", Value: alert.StartsAt.Format("2006-01-02 15:04:05"), Short: true},
					},
					Timestamp: alert.StartsAt.Unix(),
				},
			},
		}
		
		if err := h.service.SendSlack(ctx.Request.Context(), slackReq); err != nil {
			h.logger.Error("发送警告 Slack 通知失败", "error", err)
		}
	}

	return nil
}

// GetStatus 获取服务状态
func (h *Handler) GetStatus(c *gin.Context) {
	status := h.service.GetStatus()
	c.JSON(http.StatusOK, status)
}

// GetStatistics 获取统计信息
func (h *Handler) GetStatistics(c *gin.Context) {
	status := h.service.GetStatus()
	c.JSON(http.StatusOK, gin.H{
		"statistics": status.Statistics,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// HealthCheck 健康检查
func (h *Handler) HealthCheck(c *gin.Context) {
	status := h.service.GetStatus()
	
	httpStatus := http.StatusOK
	if status.Status != "healthy" {
		httpStatus = http.StatusServiceUnavailable
	}
	
	c.JSON(httpStatus, gin.H{
		"status": status.Status,
		"service": "notification-service",
		"timestamp": time.Now().Format(time.RFC3339),
		"uptime": status.Uptime,
		"connections": status.Connections,
	})
}

// TestNotification 测试通知接口
func (h *Handler) TestNotification(c *gin.Context) {
	// 获取测试类型
	testType := c.Query("type")
	if testType == "" {
		testType = "all"
	}

	results := make(map[string]interface{})

	// 测试邮件
	if testType == "email" || testType == "all" {
		emailReq := &EmailRequest{
			To:      []string{"<EMAIL>"},
			Subject: "PaaS Platform 通知测试",
			Body:    "这是一条测试邮件，用于验证邮件通知功能是否正常工作。",
			IsHTML:  false,
		}
		
		if err := h.service.SendEmail(c.Request.Context(), emailReq); err != nil {
			results["email"] = map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
		} else {
			results["email"] = map[string]interface{}{
				"success": true,
				"message": "邮件测试成功",
			}
		}
	}

	// 测试 Slack
	if testType == "slack" || testType == "all" {
		slackReq := &SlackRequest{
			Channel: "#paas-test",
			Text:    "🧪 PaaS Platform 通知测试",
			Attachments: []Attachment{
				{
					Color: "good",
					Title: "通知系统测试",
					Text:  "这是一条测试消息，用于验证 Slack 通知功能是否正常工作。",
					Fields: []Field{
						{Title: "测试时间", Value: time.Now().Format("2006-01-02 15:04:05"), Short: true},
						{Title: "测试类型", Value: "功能测试", Short: true},
					},
					Timestamp: time.Now().Unix(),
				},
			},
		}
		
		if err := h.service.SendSlack(c.Request.Context(), slackReq); err != nil {
			results["slack"] = map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
		} else {
			results["slack"] = map[string]interface{}{
				"success": true,
				"message": "Slack 测试成功",
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "通知测试完成",
		"test_type": testType,
		"results": results,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// RegisterAlertRoutes 注册告警相关路由
func (h *Handler) RegisterAlertRoutes(router *gin.RouterGroup) {
	// 通知相关路由
	router.POST("/send", h.SendNotification)
	router.GET("/channels", h.GetChannels)
	router.POST("/test-new", h.TestNewNotification)

	// 告警规则相关路由
	alerts := router.Group("/alerts")
	{
		alerts.POST("/rules", h.CreateAlertRule)
		alerts.GET("/rules", h.ListAlertRules)
		alerts.GET("/rules/:id", h.GetAlertRule)
		alerts.PUT("/rules/:id", h.UpdateAlertRule)
		alerts.DELETE("/rules/:id", h.DeleteAlertRule)
		alerts.POST("/rules/:id/toggle", h.ToggleAlertRule)
		alerts.POST("/trigger", h.TriggerAlert)
		alerts.POST("/evaluate", h.EvaluateMetric)
	}
}

// SendNotificationRequest 发送通知请求
type SendNotificationRequest struct {
	Title      string            `json:"title" binding:"required"`
	Content    string            `json:"content" binding:"required"`
	Level      string            `json:"level" binding:"required,oneof=info warning error critical"`
	Channels   []string          `json:"channels" binding:"required,min=1"`
	Recipients []string          `json:"recipients"`
	Metadata   map[string]string `json:"metadata"`
}

// SendNotification 发送通知
func (h *Handler) SendNotification(c *gin.Context) {
	var req SendNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析发送通知请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 创建通知
	notification := &Notification{
		ID:         uuid.New().String(),
		Title:      req.Title,
		Content:    req.Content,
		Level:      req.Level,
		Channels:   req.Channels,
		Recipients: req.Recipients,
		Metadata:   req.Metadata,
		MaxRetries: 3,
	}

	// 发送通知
	if err := h.notificationSvc.SendWithRetry(c.Request.Context(), notification); err != nil {
		h.logger.Error("发送通知失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送通知失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":         "通知发送成功",
		"notification_id": notification.ID,
		"status":          notification.Status,
		"sent_at":         notification.SentAt,
		"timestamp":       time.Now().Format(time.RFC3339),
	})
}

// GetChannels 获取可用通知渠道
func (h *Handler) GetChannels(c *gin.Context) {
	channels := h.notificationSvc.GetChannels()

	c.JSON(http.StatusOK, gin.H{
		"message":  "获取通知渠道成功",
		"channels": channels,
		"count":    len(channels),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// TestNewNotificationRequest 新的测试通知请求
type TestNewNotificationRequest struct {
	Channel   string `json:"channel" binding:"required"`
	Recipient string `json:"recipient"`
}

// TestNewNotification 新的测试通知
func (h *Handler) TestNewNotification(c *gin.Context) {
	var req TestNewNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析测试通知请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 创建测试通知
	notification := &Notification{
		ID:       uuid.New().String(),
		Title:    "PaaS 平台通知测试",
		Content:  fmt.Sprintf("这是一条来自 PaaS 平台的测试通知，发送时间: %s", time.Now().Format("2006-01-02 15:04:05")),
		Level:    "info",
		Channels: []string{req.Channel},
		Metadata: map[string]string{
			"test":      "true",
			"timestamp": time.Now().Format(time.RFC3339),
		},
		MaxRetries: 1,
	}

	if req.Recipient != "" {
		notification.Recipients = []string{req.Recipient}
	}

	// 发送测试通知
	if err := h.notificationSvc.Send(c.Request.Context(), notification); err != nil {
		h.logger.Error("发送测试通知失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "发送测试通知失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":         "测试通知发送成功",
		"notification_id": notification.ID,
		"channel":         req.Channel,
		"status":          notification.Status,
		"timestamp":       time.Now().Format(time.RFC3339),
	})
}

// CreateAlertRuleRequest 创建告警规则请求
type CreateAlertRuleRequest struct {
	Name        string            `json:"name" binding:"required"`
	Description string            `json:"description"`
	Metric      string            `json:"metric" binding:"required"`
	Operator    string            `json:"operator" binding:"required,oneof=> < >= <= == !="`
	Threshold   float64           `json:"threshold" binding:"required"`
	Duration    int               `json:"duration" binding:"required,min=1"` // 秒
	Severity    string            `json:"severity" binding:"required,oneof=info warning error critical"`
	Channels    []string          `json:"channels" binding:"required,min=1"`
	Recipients  []string          `json:"recipients"`
	Enabled     bool              `json:"enabled"`
	Labels      map[string]string `json:"labels"`
}

// CreateAlertRule 创建告警规则
func (h *Handler) CreateAlertRule(c *gin.Context) {
	var req CreateAlertRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析创建告警规则请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 创建告警规则
	rule := &AlertRule{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		Metric:      req.Metric,
		Operator:    req.Operator,
		Threshold:   req.Threshold,
		Duration:    time.Duration(req.Duration) * time.Second,
		Severity:    req.Severity,
		Channels:    req.Channels,
		Recipients:  req.Recipients,
		Enabled:     req.Enabled,
		Labels:      req.Labels,
	}

	if err := h.alertManager.AddRule(rule); err != nil {
		h.logger.Error("创建告警规则失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "创建告警规则失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "告警规则创建成功",
		"rule":    rule,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ListAlertRules 列出告警规则
func (h *Handler) ListAlertRules(c *gin.Context) {
	rules := h.alertManager.ListRules()

	c.JSON(http.StatusOK, gin.H{
		"message":   "获取告警规则列表成功",
		"rules":     rules,
		"count":     len(rules),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetAlertRule 获取告警规则详情
func (h *Handler) GetAlertRule(c *gin.Context) {
	ruleID := c.Param("id")

	rule, err := h.alertManager.GetRule(ruleID)
	if err != nil {
		h.logger.Error("获取告警规则失败", "rule_id", ruleID, "error", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "告警规则不存在",
			"rule_id": ruleID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "获取告警规则成功",
		"rule":      rule,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// UpdateAlertRule 更新告警规则
func (h *Handler) UpdateAlertRule(c *gin.Context) {
	ruleID := c.Param("id")

	var req CreateAlertRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析更新告警规则请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 获取现有规则
	existingRule, err := h.alertManager.GetRule(ruleID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "告警规则不存在",
			"rule_id": ruleID,
		})
		return
	}

	// 更新规则
	rule := &AlertRule{
		ID:          ruleID,
		Name:        req.Name,
		Description: req.Description,
		Metric:      req.Metric,
		Operator:    req.Operator,
		Threshold:   req.Threshold,
		Duration:    time.Duration(req.Duration) * time.Second,
		Severity:    req.Severity,
		Channels:    req.Channels,
		Recipients:  req.Recipients,
		Enabled:     req.Enabled,
		Labels:      req.Labels,
		CreatedAt:   existingRule.CreatedAt,
		LastFired:   existingRule.LastFired,
	}

	if err := h.alertManager.UpdateRule(rule); err != nil {
		h.logger.Error("更新告警规则失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "更新告警规则失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "告警规则更新成功",
		"rule":      rule,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// DeleteAlertRule 删除告警规则
func (h *Handler) DeleteAlertRule(c *gin.Context) {
	ruleID := c.Param("id")

	if err := h.alertManager.DeleteRule(ruleID); err != nil {
		h.logger.Error("删除告警规则失败", "rule_id", ruleID, "error", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "告警规则不存在",
			"rule_id": ruleID,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "告警规则删除成功",
		"rule_id":   ruleID,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ToggleAlertRule 切换告警规则启用状态
func (h *Handler) ToggleAlertRule(c *gin.Context) {
	ruleID := c.Param("id")

	rule, err := h.alertManager.GetRule(ruleID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "告警规则不存在",
			"rule_id": ruleID,
		})
		return
	}

	// 切换启用状态
	rule.Enabled = !rule.Enabled

	if err := h.alertManager.UpdateRule(rule); err != nil {
		h.logger.Error("切换告警规则状态失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "切换告警规则状态失败",
			"details": err.Error(),
		})
		return
	}

	status := "禁用"
	if rule.Enabled {
		status = "启用"
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   fmt.Sprintf("告警规则已%s", status),
		"rule_id":   ruleID,
		"enabled":   rule.Enabled,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// TriggerAlertRequest 触发告警请求
type TriggerAlertRequest struct {
	RuleID string            `json:"rule_id" binding:"required"`
	Value  float64           `json:"value" binding:"required"`
	Labels map[string]string `json:"labels"`
}

// TriggerAlert 手动触发告警
func (h *Handler) TriggerAlert(c *gin.Context) {
	var req TriggerAlertRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析触发告警请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	rule, err := h.alertManager.GetRule(req.RuleID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "告警规则不存在",
			"rule_id": req.RuleID,
		})
		return
	}

	// 手动触发告警
	h.alertManager.EvaluateMetric(rule.Metric, req.Value, req.Labels)

	c.JSON(http.StatusOK, gin.H{
		"message":   "告警触发成功",
		"rule_id":   req.RuleID,
		"metric":    rule.Metric,
		"value":     req.Value,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// EvaluateMetricRequest 评估指标请求
type EvaluateMetricRequest struct {
	Metric string            `json:"metric" binding:"required"`
	Value  float64           `json:"value" binding:"required"`
	Labels map[string]string `json:"labels"`
}

// EvaluateMetric 评估指标
func (h *Handler) EvaluateMetric(c *gin.Context) {
	var req EvaluateMetricRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析评估指标请求失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 评估指标
	h.alertManager.EvaluateMetric(req.Metric, req.Value, req.Labels)

	c.JSON(http.StatusOK, gin.H{
		"message":   "指标评估完成",
		"metric":    req.Metric,
		"value":     req.Value,
		"labels":    req.Labels,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
