package notification

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/smtp"
	"strings"
	"sync"
	"time"

	"paas-platform/pkg/logger"
)

// NotificationService 通知服务接口
type NotificationService interface {
	SendEmail(ctx context.Context, req *EmailRequest) error
	SendSlack(ctx context.Context, req *SlackRequest) error
	SendWebhook(ctx context.Context, req *WebhookRequest) error
	SendSMS(ctx context.Context, req *SMSRequest) error
	SendDingTalk(ctx context.Context, req *DingTalkRequest) error
	SendWeChat(ctx context.Context, req *WeChatRequest) error
	SendBatch(ctx context.Context, req *BatchRequest) error
	GetStatus() *ServiceStatus
}

// notificationService 通知服务实现
type notificationService struct {
	config *Config
	logger logger.Logger
	client *http.Client
	mutex  sync.RWMutex
	
	// 统计信息
	stats *Statistics
}

// Config 通知服务配置
type Config struct {
	// 邮件配置
	Email EmailConfig `yaml:"email" json:"email"`
	
	// Slack 配置
	Slack SlackConfig `yaml:"slack" json:"slack"`
	
	// 短信配置
	SMS SMSConfig `yaml:"sms" json:"sms"`
	
	// 钉钉配置
	DingTalk DingTalkConfig `yaml:"dingtalk" json:"dingtalk"`
	
	// 企业微信配置
	WeChat WeChatConfig `yaml:"wechat" json:"wechat"`
	
	// 通用配置
	Timeout    time.Duration `yaml:"timeout" json:"timeout"`
	MaxRetries int           `yaml:"max_retries" json:"max_retries"`
	RateLimit  int           `yaml:"rate_limit" json:"rate_limit"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`
	SMTPHost   string `yaml:"smtp_host" json:"smtp_host"`
	SMTPPort   int    `yaml:"smtp_port" json:"smtp_port"`
	Username   string `yaml:"username" json:"username"`
	Password   string `yaml:"password" json:"password"`
	FromEmail  string `yaml:"from_email" json:"from_email"`
	FromName   string `yaml:"from_name" json:"from_name"`
	UseTLS     bool   `yaml:"use_tls" json:"use_tls"`
}

// SlackConfig Slack 配置
type SlackConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`
	WebhookURL string `yaml:"webhook_url" json:"webhook_url"`
	Channel    string `yaml:"channel" json:"channel"`
	Username   string `yaml:"username" json:"username"`
	IconEmoji  string `yaml:"icon_emoji" json:"icon_emoji"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	Enabled   bool   `yaml:"enabled" json:"enabled"`
	Provider  string `yaml:"provider" json:"provider"` // aliyun, tencent, twilio
	AccessKey string `yaml:"access_key" json:"access_key"`
	SecretKey string `yaml:"secret_key" json:"secret_key"`
	SignName  string `yaml:"sign_name" json:"sign_name"`
}

// DingTalkConfig 钉钉配置
type DingTalkConfig struct {
	Enabled    bool   `yaml:"enabled" json:"enabled"`
	WebhookURL string `yaml:"webhook_url" json:"webhook_url"`
	Secret     string `yaml:"secret" json:"secret"`
}

// WeChatConfig 企业微信配置
type WeChatConfig struct {
	Enabled   bool   `yaml:"enabled" json:"enabled"`
	CorpID    string `yaml:"corp_id" json:"corp_id"`
	AgentID   int    `yaml:"agent_id" json:"agent_id"`
	Secret    string `yaml:"secret" json:"secret"`
	ToUser    string `yaml:"to_user" json:"to_user"`
	ToParty   string `yaml:"to_party" json:"to_party"`
	ToTag     string `yaml:"to_tag" json:"to_tag"`
}

// 请求结构体
type EmailRequest struct {
	To      []string          `json:"to"`
	CC      []string          `json:"cc,omitempty"`
	BCC     []string          `json:"bcc,omitempty"`
	Subject string            `json:"subject"`
	Body    string            `json:"body"`
	IsHTML  bool              `json:"is_html"`
	Headers map[string]string `json:"headers,omitempty"`
}

type SlackRequest struct {
	Channel     string       `json:"channel,omitempty"`
	Text        string       `json:"text"`
	Username    string       `json:"username,omitempty"`
	IconEmoji   string       `json:"icon_emoji,omitempty"`
	Attachments []Attachment `json:"attachments,omitempty"`
}

type Attachment struct {
	Color     string  `json:"color,omitempty"`
	Title     string  `json:"title,omitempty"`
	Text      string  `json:"text,omitempty"`
	Fields    []Field `json:"fields,omitempty"`
	Timestamp int64   `json:"ts,omitempty"`
}

type Field struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

type WebhookRequest struct {
	URL     string                 `json:"url"`
	Method  string                 `json:"method"`
	Headers map[string]string      `json:"headers,omitempty"`
	Body    map[string]interface{} `json:"body"`
}

type SMSRequest struct {
	PhoneNumbers []string          `json:"phone_numbers"`
	TemplateCode string            `json:"template_code"`
	TemplateParam map[string]string `json:"template_param,omitempty"`
	SignName     string            `json:"sign_name,omitempty"`
}

type DingTalkRequest struct {
	MsgType string                 `json:"msgtype"`
	Text    map[string]interface{} `json:"text,omitempty"`
	Link    map[string]interface{} `json:"link,omitempty"`
	AtMobiles []string             `json:"at_mobiles,omitempty"`
	IsAtAll   bool                 `json:"is_at_all,omitempty"`
}

type WeChatRequest struct {
	ToUser  string `json:"touser,omitempty"`
	ToParty string `json:"toparty,omitempty"`
	ToTag   string `json:"totag,omitempty"`
	MsgType string `json:"msgtype"`
	AgentID int    `json:"agentid"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text,omitempty"`
}

type BatchRequest struct {
	Notifications []BatchNotification `json:"notifications"`
	Parallel      bool                `json:"parallel"`
}

type BatchNotification struct {
	Type string      `json:"type"` // email, slack, webhook, sms, dingtalk, wechat
	Data interface{} `json:"data"`
}

// 统计信息
type Statistics struct {
	TotalSent     int64     `json:"total_sent"`
	TotalFailed   int64     `json:"total_failed"`
	EmailSent     int64     `json:"email_sent"`
	SlackSent     int64     `json:"slack_sent"`
	SMSSent       int64     `json:"sms_sent"`
	WebhookSent   int64     `json:"webhook_sent"`
	DingTalkSent  int64     `json:"dingtalk_sent"`
	WeChatSent    int64     `json:"wechat_sent"`
	LastSentTime  time.Time `json:"last_sent_time"`
	LastErrorTime time.Time `json:"last_error_time"`
	LastError     string    `json:"last_error"`
}

type ServiceStatus struct {
	Status      string     `json:"status"`
	Uptime      string     `json:"uptime"`
	Statistics  Statistics `json:"statistics"`
	Connections struct {
		Email    bool `json:"email"`
		Slack    bool `json:"slack"`
		SMS      bool `json:"sms"`
		DingTalk bool `json:"dingtalk"`
		WeChat   bool `json:"wechat"`
	} `json:"connections"`
}

// NewNotificationService 创建通知服务
func NewNotificationService(config *Config, logger logger.Logger) NotificationService {
	return &notificationService{
		config: config,
		logger: logger,
		client: &http.Client{
			Timeout: config.Timeout,
		},
		stats: &Statistics{},
	}
}

// SendEmail 发送邮件
func (s *notificationService) SendEmail(ctx context.Context, req *EmailRequest) error {
	if !s.config.Email.Enabled {
		return fmt.Errorf("邮件服务未启用")
	}

	s.logger.Info("发送邮件通知",
		"to", req.To,
		"subject", req.Subject)

	// 构建邮件内容
	var body bytes.Buffer
	
	// 邮件头
	body.WriteString(fmt.Sprintf("From: %s <%s>\r\n", s.config.Email.FromName, s.config.Email.FromEmail))
	body.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(req.To, ",")))
	if len(req.CC) > 0 {
		body.WriteString(fmt.Sprintf("Cc: %s\r\n", strings.Join(req.CC, ",")))
	}
	body.WriteString(fmt.Sprintf("Subject: %s\r\n", req.Subject))
	
	// 自定义头
	for key, value := range req.Headers {
		body.WriteString(fmt.Sprintf("%s: %s\r\n", key, value))
	}
	
	// 内容类型
	if req.IsHTML {
		body.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
	} else {
		body.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
	}
	
	body.WriteString("\r\n")
	body.WriteString(req.Body)

	// 发送邮件
	auth := smtp.PlainAuth("", s.config.Email.Username, s.config.Email.Password, s.config.Email.SMTPHost)
	
	// 收件人列表
	recipients := append(req.To, req.CC...)
	recipients = append(recipients, req.BCC...)
	
	addr := fmt.Sprintf("%s:%d", s.config.Email.SMTPHost, s.config.Email.SMTPPort)
	err := smtp.SendMail(addr, auth, s.config.Email.FromEmail, recipients, body.Bytes())
	
	if err != nil {
		s.recordError(err)
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	s.recordSuccess("email")
	s.logger.Info("邮件发送成功", "to", req.To)
	return nil
}

// SendSlack 发送 Slack 通知
func (s *notificationService) SendSlack(ctx context.Context, req *SlackRequest) error {
	if !s.config.Slack.Enabled {
		return fmt.Errorf("Slack 服务未启用")
	}

	s.logger.Info("发送 Slack 通知", "channel", req.Channel, "text", req.Text)

	// 设置默认值
	if req.Channel == "" {
		req.Channel = s.config.Slack.Channel
	}
	if req.Username == "" {
		req.Username = s.config.Slack.Username
	}
	if req.IconEmoji == "" {
		req.IconEmoji = s.config.Slack.IconEmoji
	}

	// 构建请求体
	payload, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("构建 Slack 请求失败: %w", err)
	}

	// 发送请求
	resp, err := s.client.Post(s.config.Slack.WebhookURL, "application/json", bytes.NewBuffer(payload))
	if err != nil {
		s.recordError(err)
		return fmt.Errorf("发送 Slack 通知失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err := fmt.Errorf("Slack API 返回错误状态: %d", resp.StatusCode)
		s.recordError(err)
		return err
	}

	s.recordSuccess("slack")
	s.logger.Info("Slack 通知发送成功")
	return nil
}

// SendWebhook 发送 Webhook 通知
func (s *notificationService) SendWebhook(ctx context.Context, req *WebhookRequest) error {
	s.logger.Info("发送 Webhook 通知", "url", req.URL, "method", req.Method)

	// 构建请求体
	payload, err := json.Marshal(req.Body)
	if err != nil {
		return fmt.Errorf("构建 Webhook 请求失败: %w", err)
	}

	// 创建 HTTP 请求
	httpReq, err := http.NewRequestWithContext(ctx, req.Method, req.URL, bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("创建 HTTP 请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// 发送请求
	resp, err := s.client.Do(httpReq)
	if err != nil {
		s.recordError(err)
		return fmt.Errorf("发送 Webhook 请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		err := fmt.Errorf("Webhook 返回错误状态: %d", resp.StatusCode)
		s.recordError(err)
		return err
	}

	s.recordSuccess("webhook")
	s.logger.Info("Webhook 通知发送成功")
	return nil
}

// SendSMS 发送短信通知 (简化实现)
func (s *notificationService) SendSMS(ctx context.Context, req *SMSRequest) error {
	if !s.config.SMS.Enabled {
		return fmt.Errorf("短信服务未启用")
	}

	s.logger.Info("发送短信通知", "phones", req.PhoneNumbers, "template", req.TemplateCode)

	// 🔧 简化的短信发送实现 (实际项目中需要集成具体的短信服务商)
	// 支持阿里云、腾讯云、Twilio 等主流短信服务
	switch s.config.SMS.Provider {
	case "aliyun":
		s.logger.Info("使用阿里云短信服务发送", "phones", req.PhoneNumbers)
		// TODO: 实现阿里云短信发送逻辑
	case "tencent":
		s.logger.Info("使用腾讯云短信服务发送", "phones", req.PhoneNumbers)
		// TODO: 实现腾讯云短信发送逻辑
	case "twilio":
		s.logger.Info("使用 Twilio 短信服务发送", "phones", req.PhoneNumbers)
		// TODO: 实现 Twilio 短信发送逻辑
	default:
		s.logger.Info("短信发送功能需要配置具体的服务商", "provider", s.config.SMS.Provider)
	}

	s.recordSuccess("sms")
	return nil
}

// SendDingTalk 发送钉钉通知
func (s *notificationService) SendDingTalk(ctx context.Context, req *DingTalkRequest) error {
	if !s.config.DingTalk.Enabled {
		return fmt.Errorf("钉钉服务未启用")
	}

	s.logger.Info("发送钉钉通知", "msgtype", req.MsgType)

	// 构建请求体
	payload, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("构建钉钉请求失败: %w", err)
	}

	// 发送请求
	resp, err := s.client.Post(s.config.DingTalk.WebhookURL, "application/json", bytes.NewBuffer(payload))
	if err != nil {
		s.recordError(err)
		return fmt.Errorf("发送钉钉通知失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err := fmt.Errorf("钉钉 API 返回错误状态: %d", resp.StatusCode)
		s.recordError(err)
		return err
	}

	s.recordSuccess("dingtalk")
	s.logger.Info("钉钉通知发送成功")
	return nil
}

// SendWeChat 发送企业微信通知
func (s *notificationService) SendWeChat(ctx context.Context, req *WeChatRequest) error {
	if !s.config.WeChat.Enabled {
		return fmt.Errorf("企业微信服务未启用")
	}

	s.logger.Info("发送企业微信通知", "touser", req.ToUser, "msgtype", req.MsgType)

	// 这里应该实现企业微信的完整认证和发送流程
	// 目前只是记录日志
	s.logger.Info("企业微信发送功能需要完整实现")

	s.recordSuccess("wechat")
	return nil
}

// SendBatch 批量发送通知
func (s *notificationService) SendBatch(ctx context.Context, req *BatchRequest) error {
	s.logger.Info("批量发送通知", "count", len(req.Notifications), "parallel", req.Parallel)

	if req.Parallel {
		// 并行发送
		var wg sync.WaitGroup
		errChan := make(chan error, len(req.Notifications))

		for _, notification := range req.Notifications {
			wg.Add(1)
			go func(n BatchNotification) {
				defer wg.Done()
				if err := s.sendSingleNotification(ctx, n); err != nil {
					errChan <- err
				}
			}(notification)
		}

		wg.Wait()
		close(errChan)

		// 收集错误
		var errors []string
		for err := range errChan {
			errors = append(errors, err.Error())
		}

		if len(errors) > 0 {
			return fmt.Errorf("批量发送部分失败: %s", strings.Join(errors, "; "))
		}
	} else {
		// 串行发送
		for _, notification := range req.Notifications {
			if err := s.sendSingleNotification(ctx, notification); err != nil {
				return fmt.Errorf("批量发送失败: %w", err)
			}
		}
	}

	s.logger.Info("批量通知发送完成")
	return nil
}

// sendSingleNotification 发送单个通知
func (s *notificationService) sendSingleNotification(ctx context.Context, notification BatchNotification) error {
	switch notification.Type {
	case "email":
		if req, ok := notification.Data.(*EmailRequest); ok {
			return s.SendEmail(ctx, req)
		}
	case "slack":
		if req, ok := notification.Data.(*SlackRequest); ok {
			return s.SendSlack(ctx, req)
		}
	case "webhook":
		if req, ok := notification.Data.(*WebhookRequest); ok {
			return s.SendWebhook(ctx, req)
		}
	case "sms":
		if req, ok := notification.Data.(*SMSRequest); ok {
			return s.SendSMS(ctx, req)
		}
	case "dingtalk":
		if req, ok := notification.Data.(*DingTalkRequest); ok {
			return s.SendDingTalk(ctx, req)
		}
	case "wechat":
		if req, ok := notification.Data.(*WeChatRequest); ok {
			return s.SendWeChat(ctx, req)
		}
	}
	return fmt.Errorf("不支持的通知类型: %s", notification.Type)
}

// GetStatus 获取服务状态
func (s *notificationService) GetStatus() *ServiceStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	status := &ServiceStatus{
		Status:     "healthy",
		Statistics: *s.stats,
	}

	// 检查各服务连接状态
	status.Connections.Email = s.config.Email.Enabled
	status.Connections.Slack = s.config.Slack.Enabled
	status.Connections.SMS = s.config.SMS.Enabled
	status.Connections.DingTalk = s.config.DingTalk.Enabled
	status.Connections.WeChat = s.config.WeChat.Enabled

	return status
}

// recordSuccess 记录成功统计
func (s *notificationService) recordSuccess(notificationType string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.stats.TotalSent++
	s.stats.LastSentTime = time.Now()

	switch notificationType {
	case "email":
		s.stats.EmailSent++
	case "slack":
		s.stats.SlackSent++
	case "sms":
		s.stats.SMSSent++
	case "webhook":
		s.stats.WebhookSent++
	case "dingtalk":
		s.stats.DingTalkSent++
	case "wechat":
		s.stats.WeChatSent++
	}
}

// recordError 记录错误统计
func (s *notificationService) recordError(err error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.stats.TotalFailed++
	s.stats.LastErrorTime = time.Now()
	s.stats.LastError = err.Error()
}
