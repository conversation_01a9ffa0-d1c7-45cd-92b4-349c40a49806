package notification

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/smtp"
	"strings"
	"time"

	"paas-platform/pkg/logger"
)

// NotificationService 通知服务
type NotificationService struct {
	logger   logger.Logger
	config   *Config
	channels map[string]Channel
}

// Config 通知配置
type Config struct {
	Email    EmailConfig    `json:"email"`
	Slack    SlackConfig    `json:"slack"`
	DingTalk DingTalkConfig `json:"dingtalk"`
	WeChat   WeChatConfig   `json:"wechat"`
	SMS      SMSConfig      `json:"sms"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	Enabled  bool   `json:"enabled"`
	SMTPHost string `json:"smtp_host"`
	SMTPPort int    `json:"smtp_port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	TLS      bool   `json:"tls"`
}

// SlackConfig Slack 配置
type SlackConfig struct {
	Enabled    bool   `json:"enabled"`
	WebhookURL string `json:"webhook_url"`
	Channel    string `json:"channel"`
	Username   string `json:"username"`
}

// DingTalkConfig 钉钉配置
type DingTalkConfig struct {
	Enabled    bool   `json:"enabled"`
	WebhookURL string `json:"webhook_url"`
	Secret     string `json:"secret"`
}

// WeChatConfig 企业微信配置
type WeChatConfig struct {
	Enabled    bool   `json:"enabled"`
	WebhookURL string `json:"webhook_url"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	Enabled   bool   `json:"enabled"`
	Provider  string `json:"provider"`
	AccessKey string `json:"access_key"`
	SecretKey string `json:"secret_key"`
	SignName  string `json:"sign_name"`
}

// Notification 通知消息
type Notification struct {
	ID          string            `json:"id"`
	Title       string            `json:"title"`
	Content     string            `json:"content"`
	Level       string            `json:"level"` // info, warning, error, critical
	Channels    []string          `json:"channels"`
	Recipients  []string          `json:"recipients"`
	Metadata    map[string]string `json:"metadata"`
	CreatedAt   time.Time         `json:"created_at"`
	SentAt      *time.Time        `json:"sent_at,omitempty"`
	Status      string            `json:"status"` // pending, sent, failed
	RetryCount  int               `json:"retry_count"`
	MaxRetries  int               `json:"max_retries"`
}

// Channel 通知渠道接口
type Channel interface {
	Send(ctx context.Context, notification *Notification) error
	GetName() string
	IsEnabled() bool
}

// NewNotificationService 创建通知服务
func NewNotificationService(config *Config, logger logger.Logger) *NotificationService {
	service := &NotificationService{
		logger:   logger,
		config:   config,
		channels: make(map[string]Channel),
	}

	// 初始化通知渠道
	service.initChannels()

	return service
}

// initChannels 初始化通知渠道
func (ns *NotificationService) initChannels() {
	// 邮件渠道
	if ns.config.Email.Enabled {
		ns.channels["email"] = NewEmailChannel(&ns.config.Email, ns.logger)
	}

	// Slack 渠道
	if ns.config.Slack.Enabled {
		ns.channels["slack"] = NewSlackChannel(&ns.config.Slack, ns.logger)
	}

	// 钉钉渠道
	if ns.config.DingTalk.Enabled {
		ns.channels["dingtalk"] = NewDingTalkChannel(&ns.config.DingTalk, ns.logger)
	}

	// 企业微信渠道
	if ns.config.WeChat.Enabled {
		ns.channels["wechat"] = NewWeChatChannel(&ns.config.WeChat, ns.logger)
	}

	// 短信渠道
	if ns.config.SMS.Enabled {
		ns.channels["sms"] = NewSMSChannel(&ns.config.SMS, ns.logger)
	}

	ns.logger.Info(fmt.Sprintf("初始化了 %d 个通知渠道", len(ns.channels)))
}

// Send 发送通知
func (ns *NotificationService) Send(ctx context.Context, notification *Notification) error {
	notification.CreatedAt = time.Now()
	notification.Status = "pending"

	if notification.MaxRetries == 0 {
		notification.MaxRetries = 3
	}

	// 验证通知内容
	if err := ns.validateNotification(notification); err != nil {
		return fmt.Errorf("通知验证失败: %w", err)
	}

	// 发送到指定渠道
	var errors []string
	successCount := 0

	for _, channelName := range notification.Channels {
		channel, exists := ns.channels[channelName]
		if !exists {
			errors = append(errors, fmt.Sprintf("渠道 %s 不存在", channelName))
			continue
		}

		if !channel.IsEnabled() {
			errors = append(errors, fmt.Sprintf("渠道 %s 未启用", channelName))
			continue
		}

		if err := channel.Send(ctx, notification); err != nil {
			ns.logger.Error(fmt.Sprintf("发送通知到 %s 失败: %v", channelName, err))
			errors = append(errors, fmt.Sprintf("%s: %v", channelName, err))
		} else {
			successCount++
			ns.logger.Info(fmt.Sprintf("成功发送通知到 %s", channelName))
		}
	}

	// 更新通知状态
	if successCount > 0 {
		notification.Status = "sent"
		now := time.Now()
		notification.SentAt = &now
	} else {
		notification.Status = "failed"
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分渠道发送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// SendWithRetry 带重试的发送通知
func (ns *NotificationService) SendWithRetry(ctx context.Context, notification *Notification) error {
	var lastErr error

	for i := 0; i <= notification.MaxRetries; i++ {
		notification.RetryCount = i

		if err := ns.Send(ctx, notification); err != nil {
			lastErr = err
			ns.logger.Warn(fmt.Sprintf("通知发送失败，第 %d 次重试: %v", i, err))

			if i < notification.MaxRetries {
				// 指数退避重试
				backoff := time.Duration(1<<uint(i)) * time.Second
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(backoff):
					continue
				}
			}
		} else {
			return nil
		}
	}

	return fmt.Errorf("重试 %d 次后仍然失败: %w", notification.MaxRetries, lastErr)
}

// validateNotification 验证通知内容
func (ns *NotificationService) validateNotification(notification *Notification) error {
	if notification.Title == "" {
		return fmt.Errorf("通知标题不能为空")
	}

	if notification.Content == "" {
		return fmt.Errorf("通知内容不能为空")
	}

	if len(notification.Channels) == 0 {
		return fmt.Errorf("至少需要指定一个通知渠道")
	}

	validLevels := map[string]bool{
		"info":     true,
		"warning":  true,
		"error":    true,
		"critical": true,
	}

	if !validLevels[notification.Level] {
		return fmt.Errorf("无效的通知级别: %s", notification.Level)
	}

	return nil
}

// GetChannels 获取可用渠道
func (ns *NotificationService) GetChannels() []string {
	var channels []string
	for name, channel := range ns.channels {
		if channel.IsEnabled() {
			channels = append(channels, name)
		}
	}
	return channels
}

// EmailChannel 邮件通知渠道
type EmailChannel struct {
	config *EmailConfig
	logger logger.Logger
}

// NewEmailChannel 创建邮件渠道
func NewEmailChannel(config *EmailConfig, logger logger.Logger) *EmailChannel {
	return &EmailChannel{
		config: config,
		logger: logger,
	}
}

// Send 发送邮件
func (ec *EmailChannel) Send(ctx context.Context, notification *Notification) error {
	if len(notification.Recipients) == 0 {
		return fmt.Errorf("邮件收件人不能为空")
	}

	// 构建邮件内容
	subject := fmt.Sprintf("[%s] %s", strings.ToUpper(notification.Level), notification.Title)
	body := ec.buildEmailBody(notification)

	// SMTP 认证
	auth := smtp.PlainAuth("", ec.config.Username, ec.config.Password, ec.config.SMTPHost)

	// 构建邮件消息
	msg := ec.buildEmailMessage(ec.config.From, notification.Recipients, subject, body)

	// 发送邮件
	addr := fmt.Sprintf("%s:%d", ec.config.SMTPHost, ec.config.SMTPPort)
	return smtp.SendMail(addr, auth, ec.config.From, notification.Recipients, []byte(msg))
}

// buildEmailBody 构建邮件正文
func (ec *EmailChannel) buildEmailBody(notification *Notification) string {
	var body strings.Builder

	body.WriteString(fmt.Sprintf("告警级别: %s\n", strings.ToUpper(notification.Level)))
	body.WriteString(fmt.Sprintf("告警时间: %s\n", notification.CreatedAt.Format("2006-01-02 15:04:05")))
	body.WriteString(fmt.Sprintf("告警内容: %s\n\n", notification.Content))

	if len(notification.Metadata) > 0 {
		body.WriteString("详细信息:\n")
		for key, value := range notification.Metadata {
			body.WriteString(fmt.Sprintf("  %s: %s\n", key, value))
		}
	}

	body.WriteString("\n---\n")
	body.WriteString("此邮件由 PaaS 平台自动发送，请勿回复。")

	return body.String()
}

// buildEmailMessage 构建邮件消息
func (ec *EmailChannel) buildEmailMessage(from string, to []string, subject, body string) string {
	var msg strings.Builder

	msg.WriteString(fmt.Sprintf("From: %s\r\n", from))
	msg.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(to, ",")))
	msg.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	msg.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
	msg.WriteString("\r\n")
	msg.WriteString(body)

	return msg.String()
}

// GetName 获取渠道名称
func (ec *EmailChannel) GetName() string {
	return "email"
}

// IsEnabled 检查是否启用
func (ec *EmailChannel) IsEnabled() bool {
	return ec.config.Enabled
}

// SlackChannel Slack 通知渠道
type SlackChannel struct {
	config *SlackConfig
	logger logger.Logger
}

// NewSlackChannel 创建 Slack 渠道
func NewSlackChannel(config *SlackConfig, logger logger.Logger) *SlackChannel {
	return &SlackChannel{
		config: config,
		logger: logger,
	}
}

// Send 发送 Slack 消息
func (sc *SlackChannel) Send(ctx context.Context, notification *Notification) error {
	payload := map[string]interface{}{
		"channel":  sc.config.Channel,
		"username": sc.config.Username,
		"text":     notification.Title,
		"attachments": []map[string]interface{}{
			{
				"color": sc.getLevelColor(notification.Level),
				"fields": []map[string]interface{}{
					{
						"title": "级别",
						"value": strings.ToUpper(notification.Level),
						"short": true,
					},
					{
						"title": "时间",
						"value": notification.CreatedAt.Format("2006-01-02 15:04:05"),
						"short": true,
					},
					{
						"title": "详情",
						"value": notification.Content,
						"short": false,
					},
				},
			},
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化 Slack 消息失败: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", sc.config.WebhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建 Slack 请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送 Slack 消息失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Slack API 返回错误状态: %d", resp.StatusCode)
	}

	return nil
}

// getLevelColor 获取级别对应的颜色
func (sc *SlackChannel) getLevelColor(level string) string {
	colors := map[string]string{
		"info":     "good",
		"warning":  "warning",
		"error":    "danger",
		"critical": "#ff0000",
	}

	if color, exists := colors[level]; exists {
		return color
	}
	return "good"
}

// GetName 获取渠道名称
func (sc *SlackChannel) GetName() string {
	return "slack"
}

// IsEnabled 检查是否启用
func (sc *SlackChannel) IsEnabled() bool {
	return sc.config.Enabled
}

// DingTalkChannel 钉钉通知渠道
type DingTalkChannel struct {
	config *DingTalkConfig
	logger logger.Logger
}

// NewDingTalkChannel 创建钉钉渠道
func NewDingTalkChannel(config *DingTalkConfig, logger logger.Logger) *DingTalkChannel {
	return &DingTalkChannel{
		config: config,
		logger: logger,
	}
}

// Send 发送钉钉消息
func (dc *DingTalkChannel) Send(ctx context.Context, notification *Notification) error {
	content := fmt.Sprintf("**%s**\n\n级别: %s\n时间: %s\n内容: %s",
		notification.Title,
		strings.ToUpper(notification.Level),
		notification.CreatedAt.Format("2006-01-02 15:04:05"),
		notification.Content,
	)

	payload := map[string]interface{}{
		"msgtype": "markdown",
		"markdown": map[string]interface{}{
			"title": notification.Title,
			"text":  content,
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化钉钉消息失败: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", dc.config.WebhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建钉钉请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送钉钉消息失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("钉钉 API 返回错误状态: %d", resp.StatusCode)
	}

	return nil
}

// GetName 获取渠道名称
func (dc *DingTalkChannel) GetName() string {
	return "dingtalk"
}

// IsEnabled 检查是否启用
func (dc *DingTalkChannel) IsEnabled() bool {
	return dc.config.Enabled
}

// WeChatChannel 企业微信通知渠道
type WeChatChannel struct {
	config *WeChatConfig
	logger logger.Logger
}

// NewWeChatChannel 创建企业微信渠道
func NewWeChatChannel(config *WeChatConfig, logger logger.Logger) *WeChatChannel {
	return &WeChatChannel{
		config: config,
		logger: logger,
	}
}

// Send 发送企业微信消息
func (wc *WeChatChannel) Send(ctx context.Context, notification *Notification) error {
	content := fmt.Sprintf("%s\n级别: %s\n时间: %s\n内容: %s",
		notification.Title,
		strings.ToUpper(notification.Level),
		notification.CreatedAt.Format("2006-01-02 15:04:05"),
		notification.Content,
	)

	payload := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]interface{}{
			"content": content,
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化企业微信消息失败: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", wc.config.WebhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建企业微信请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送企业微信消息失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("企业微信 API 返回错误状态: %d", resp.StatusCode)
	}

	return nil
}

// GetName 获取渠道名称
func (wc *WeChatChannel) GetName() string {
	return "wechat"
}

// IsEnabled 检查是否启用
func (wc *WeChatChannel) IsEnabled() bool {
	return wc.config.Enabled
}

// SMSChannel 短信通知渠道
type SMSChannel struct {
	config *SMSConfig
	logger logger.Logger
}

// NewSMSChannel 创建短信渠道
func NewSMSChannel(config *SMSConfig, logger logger.Logger) *SMSChannel {
	return &SMSChannel{
		config: config,
		logger: logger,
	}
}

// Send 发送短信
func (sms *SMSChannel) Send(ctx context.Context, notification *Notification) error {
	// 这里是短信发送的示例实现
	// 实际使用时需要根据具体的短信服务商 API 进行实现
	sms.logger.Info(fmt.Sprintf("模拟发送短信: %s - %s", notification.Title, notification.Content))
	return nil
}

// GetName 获取渠道名称
func (sms *SMSChannel) GetName() string {
	return "sms"
}

// IsEnabled 检查是否启用
func (sms *SMSChannel) IsEnabled() bool {
	return sms.config.Enabled
}

// AlertRule 告警规则
type AlertRule struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Metric      string            `json:"metric"`      // 监控指标名称
	Operator    string            `json:"operator"`    // 比较操作符: >, <, >=, <=, ==, !=
	Threshold   float64           `json:"threshold"`   // 阈值
	Duration    time.Duration     `json:"duration"`    // 持续时间
	Severity    string            `json:"severity"`    // 严重程度: info, warning, error, critical
	Channels    []string          `json:"channels"`    // 通知渠道
	Recipients  []string          `json:"recipients"`  // 接收人
	Enabled     bool              `json:"enabled"`     // 是否启用
	Labels      map[string]string `json:"labels"`      // 标签
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	LastFired   *time.Time        `json:"last_fired,omitempty"`
}

// AlertManager 告警管理器
type AlertManager struct {
	rules              map[string]*AlertRule
	notificationSvc    *NotificationService
	logger             logger.Logger
	mutex              sync.RWMutex
	evaluationInterval time.Duration
	ctx                context.Context
	cancel             context.CancelFunc
}

// NewAlertManager 创建告警管理器
func NewAlertManager(notificationSvc *NotificationService, logger logger.Logger) *AlertManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &AlertManager{
		rules:              make(map[string]*AlertRule),
		notificationSvc:    notificationSvc,
		logger:             logger,
		evaluationInterval: 30 * time.Second,
		ctx:                ctx,
		cancel:             cancel,
	}
}

// AddRule 添加告警规则
func (am *AlertManager) AddRule(rule *AlertRule) error {
	if err := am.validateRule(rule); err != nil {
		return fmt.Errorf("告警规则验证失败: %w", err)
	}

	am.mutex.Lock()
	defer am.mutex.Unlock()

	rule.CreatedAt = time.Now()
	rule.UpdatedAt = time.Now()
	am.rules[rule.ID] = rule

	am.logger.Info(fmt.Sprintf("添加告警规则: %s", rule.Name))
	return nil
}

// UpdateRule 更新告警规则
func (am *AlertManager) UpdateRule(rule *AlertRule) error {
	if err := am.validateRule(rule); err != nil {
		return fmt.Errorf("告警规则验证失败: %w", err)
	}

	am.mutex.Lock()
	defer am.mutex.Unlock()

	if _, exists := am.rules[rule.ID]; !exists {
		return fmt.Errorf("告警规则不存在: %s", rule.ID)
	}

	rule.UpdatedAt = time.Now()
	am.rules[rule.ID] = rule

	am.logger.Info(fmt.Sprintf("更新告警规则: %s", rule.Name))
	return nil
}

// DeleteRule 删除告警规则
func (am *AlertManager) DeleteRule(ruleID string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	if _, exists := am.rules[ruleID]; !exists {
		return fmt.Errorf("告警规则不存在: %s", ruleID)
	}

	delete(am.rules, ruleID)
	am.logger.Info(fmt.Sprintf("删除告警规则: %s", ruleID))
	return nil
}

// GetRule 获取告警规则
func (am *AlertManager) GetRule(ruleID string) (*AlertRule, error) {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	rule, exists := am.rules[ruleID]
	if !exists {
		return nil, fmt.Errorf("告警规则不存在: %s", ruleID)
	}

	return rule, nil
}

// ListRules 列出所有告警规则
func (am *AlertManager) ListRules() []*AlertRule {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	rules := make([]*AlertRule, 0, len(am.rules))
	for _, rule := range am.rules {
		rules = append(rules, rule)
	}

	return rules
}

// EvaluateMetric 评估指标是否触发告警
func (am *AlertManager) EvaluateMetric(metricName string, value float64, labels map[string]string) {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	for _, rule := range am.rules {
		if !rule.Enabled || rule.Metric != metricName {
			continue
		}

		// 检查标签匹配
		if !am.labelsMatch(rule.Labels, labels) {
			continue
		}

		// 评估阈值
		if am.evaluateThreshold(rule, value) {
			am.fireAlert(rule, value, labels)
		}
	}
}

// validateRule 验证告警规则
func (am *AlertManager) validateRule(rule *AlertRule) error {
	if rule.Name == "" {
		return fmt.Errorf("规则名称不能为空")
	}

	if rule.Metric == "" {
		return fmt.Errorf("监控指标不能为空")
	}

	validOperators := map[string]bool{
		">": true, "<": true, ">=": true, "<=": true, "==": true, "!=": true,
	}
	if !validOperators[rule.Operator] {
		return fmt.Errorf("无效的操作符: %s", rule.Operator)
	}

	validSeverities := map[string]bool{
		"info": true, "warning": true, "error": true, "critical": true,
	}
	if !validSeverities[rule.Severity] {
		return fmt.Errorf("无效的严重程度: %s", rule.Severity)
	}

	if len(rule.Channels) == 0 {
		return fmt.Errorf("至少需要指定一个通知渠道")
	}

	return nil
}

// labelsMatch 检查标签是否匹配
func (am *AlertManager) labelsMatch(ruleLabels, metricLabels map[string]string) bool {
	for key, value := range ruleLabels {
		if metricValue, exists := metricLabels[key]; !exists || metricValue != value {
			return false
		}
	}
	return true
}

// evaluateThreshold 评估阈值
func (am *AlertManager) evaluateThreshold(rule *AlertRule, value float64) bool {
	switch rule.Operator {
	case ">":
		return value > rule.Threshold
	case "<":
		return value < rule.Threshold
	case ">=":
		return value >= rule.Threshold
	case "<=":
		return value <= rule.Threshold
	case "==":
		return value == rule.Threshold
	case "!=":
		return value != rule.Threshold
	default:
		return false
	}
}

// fireAlert 触发告警
func (am *AlertManager) fireAlert(rule *AlertRule, value float64, labels map[string]string) {
	// 检查是否在冷却期内
	if rule.LastFired != nil && time.Since(*rule.LastFired) < rule.Duration {
		return
	}

	// 创建通知
	notification := &Notification{
		ID:       fmt.Sprintf("alert-%s-%d", rule.ID, time.Now().Unix()),
		Title:    fmt.Sprintf("告警: %s", rule.Name),
		Content:  am.buildAlertContent(rule, value, labels),
		Level:    rule.Severity,
		Channels: rule.Channels,
		Recipients: rule.Recipients,
		Metadata: map[string]string{
			"rule_id":    rule.ID,
			"rule_name":  rule.Name,
			"metric":     rule.Metric,
			"value":      fmt.Sprintf("%.2f", value),
			"threshold":  fmt.Sprintf("%.2f", rule.Threshold),
			"operator":   rule.Operator,
		},
		MaxRetries: 3,
	}

	// 发送通知
	go func() {
		if err := am.notificationSvc.SendWithRetry(am.ctx, notification); err != nil {
			am.logger.Error(fmt.Sprintf("发送告警通知失败: %v", err))
		} else {
			am.logger.Info(fmt.Sprintf("成功发送告警通知: %s", rule.Name))
		}
	}()

	// 更新最后触发时间
	now := time.Now()
	rule.LastFired = &now
}

// buildAlertContent 构建告警内容
func (am *AlertManager) buildAlertContent(rule *AlertRule, value float64, labels map[string]string) string {
	var content strings.Builder

	content.WriteString(fmt.Sprintf("告警规则: %s\n", rule.Name))
	content.WriteString(fmt.Sprintf("监控指标: %s\n", rule.Metric))
	content.WriteString(fmt.Sprintf("当前值: %.2f\n", value))
	content.WriteString(fmt.Sprintf("阈值条件: %s %.2f\n", rule.Operator, rule.Threshold))

	if rule.Description != "" {
		content.WriteString(fmt.Sprintf("描述: %s\n", rule.Description))
	}

	if len(labels) > 0 {
		content.WriteString("\n标签信息:\n")
		for key, val := range labels {
			content.WriteString(fmt.Sprintf("  %s: %s\n", key, val))
		}
	}

	return content.String()
}

// Start 启动告警管理器
func (am *AlertManager) Start() {
	am.logger.Info("启动告警管理器")

	ticker := time.NewTicker(am.evaluationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-am.ctx.Done():
			am.logger.Info("告警管理器已停止")
			return
		case <-ticker.C:
			// 这里可以添加定期检查逻辑
			am.logger.Debug("告警管理器定期检查")
		}
	}
}

// Stop 停止告警管理器
func (am *AlertManager) Stop() {
	am.logger.Info("停止告警管理器")
	am.cancel()
}
