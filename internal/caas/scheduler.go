package caas

import (
	"context"
	"fmt"
	"math"
	"sort"
	"sync"
	"time"

	"paas-platform/internal/container"
	"paas-platform/pkg/logger"
)

// ContainerScheduler 容器调度器
type ContainerScheduler struct {
	containerManager container.ContainerManager
	logger           logger.Logger
	nodes            map[string]*SchedulerNode
	schedulingPolicy *SchedulingPolicy
	mutex            sync.RWMutex
}

// SchedulerNode 调度节点
type SchedulerNode struct {
	ID               string                 `json:"id"`
	Name             string                 `json:"name"`
	Status           string                 `json:"status"` // ready, not_ready, unknown
	Capacity         *NodeCapacity          `json:"capacity"`
	Allocatable      *NodeCapacity          `json:"allocatable"`
	Used             *NodeCapacity          `json:"used"`
	Labels           map[string]string      `json:"labels"`
	Taints           []NodeTaint            `json:"taints"`
	Conditions       []NodeCondition        `json:"conditions"`
	ContainerCount   int                    `json:"container_count"`
	LastHeartbeat    time.Time              `json:"last_heartbeat"`
	CreatedAt        time.Time              `json:"created_at"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// NodeCapacity 节点容量
type NodeCapacity struct {
	CPU     string `json:"cpu"`     // CPU核心数，如 "4"
	Memory  string `json:"memory"`  // 内存大小，如 "8Gi"
	Storage string `json:"storage"` // 存储大小，如 "100Gi"
	Pods    int    `json:"pods"`    // 最大Pod数
}

// NodeTaint 节点污点
type NodeTaint struct {
	Key    string `json:"key"`
	Value  string `json:"value"`
	Effect string `json:"effect"` // NoSchedule, PreferNoSchedule, NoExecute
}

// NodeCondition 节点条件
type NodeCondition struct {
	Type               string    `json:"type"` // Ready, MemoryPressure, DiskPressure, PIDPressure
	Status             string    `json:"status"` // True, False, Unknown
	LastTransitionTime time.Time `json:"last_transition_time"`
	Reason             string    `json:"reason"`
	Message            string    `json:"message"`
}

// SchedulingPolicy 调度策略
type SchedulingPolicy struct {
	Algorithm           string                 `json:"algorithm"` // least_allocated, most_allocated, balanced
	NodeSelector        map[string]string      `json:"node_selector"`
	Affinity            *NodeAffinity          `json:"affinity"`
	AntiAffinity        *NodeAntiAffinity      `json:"anti_affinity"`
	Tolerations         []Toleration           `json:"tolerations"`
	PriorityClass       string                 `json:"priority_class"`
	SchedulingTimeout   time.Duration          `json:"scheduling_timeout"`
	MaxRetries          int                    `json:"max_retries"`
}

// NodeAffinity 节点亲和性
type NodeAffinity struct {
	RequiredDuringScheduling  []NodeSelectorTerm `json:"required_during_scheduling"`
	PreferredDuringScheduling []PreferredSchedulingTerm `json:"preferred_during_scheduling"`
}

// NodeAntiAffinity 节点反亲和性
type NodeAntiAffinity struct {
	RequiredDuringScheduling  []NodeSelectorTerm `json:"required_during_scheduling"`
	PreferredDuringScheduling []PreferredSchedulingTerm `json:"preferred_during_scheduling"`
}

// NodeSelectorTerm 节点选择器条件
type NodeSelectorTerm struct {
	MatchExpressions []NodeSelectorRequirement `json:"match_expressions"`
	MatchFields      []NodeSelectorRequirement `json:"match_fields"`
}

// NodeSelectorRequirement 节点选择器要求
type NodeSelectorRequirement struct {
	Key      string   `json:"key"`
	Operator string   `json:"operator"` // In, NotIn, Exists, DoesNotExist, Gt, Lt
	Values   []string `json:"values"`
}

// PreferredSchedulingTerm 首选调度条件
type PreferredSchedulingTerm struct {
	Weight     int32            `json:"weight"`
	Preference NodeSelectorTerm `json:"preference"`
}

// Toleration 容忍度
type Toleration struct {
	Key               string        `json:"key"`
	Operator          string        `json:"operator"` // Equal, Exists
	Value             string        `json:"value"`
	Effect            string        `json:"effect"` // NoSchedule, PreferNoSchedule, NoExecute
	TolerationSeconds *int64        `json:"toleration_seconds"`
}

// SchedulingRequest 调度请求
type SchedulingRequest struct {
	ContainerConfig *container.ContainerConfig `json:"container_config"`
	Requirements    *SchedulingRequirements    `json:"requirements"`
	Constraints     *SchedulingConstraints     `json:"constraints"`
	Priority        int32                      `json:"priority"`
}

// SchedulingRequirements 调度要求
type SchedulingRequirements struct {
	CPU     string            `json:"cpu"`
	Memory  string            `json:"memory"`
	Storage string            `json:"storage"`
	Labels  map[string]string `json:"labels"`
}

// SchedulingConstraints 调度约束
type SchedulingConstraints struct {
	NodeSelector    map[string]string `json:"node_selector"`
	Affinity        *NodeAffinity     `json:"affinity"`
	AntiAffinity    *NodeAntiAffinity `json:"anti_affinity"`
	Tolerations     []Toleration      `json:"tolerations"`
	TopologySpread  []TopologySpreadConstraint `json:"topology_spread"`
}

// TopologySpreadConstraint 拓扑分布约束
type TopologySpreadConstraint struct {
	MaxSkew           int32             `json:"max_skew"`
	TopologyKey       string            `json:"topology_key"`
	WhenUnsatisfiable string            `json:"when_unsatisfiable"` // DoNotSchedule, ScheduleAnyway
	LabelSelector     map[string]string `json:"label_selector"`
}

// SchedulingResult 调度结果
type SchedulingResult struct {
	NodeID    string    `json:"node_id"`
	NodeName  string    `json:"node_name"`
	Score     float64   `json:"score"`
	Reason    string    `json:"reason"`
	Timestamp time.Time `json:"timestamp"`
}

// NewContainerScheduler 创建容器调度器
func NewContainerScheduler(containerManager container.ContainerManager, logger logger.Logger) *ContainerScheduler {
	scheduler := &ContainerScheduler{
		containerManager: containerManager,
		logger:           logger,
		nodes:            make(map[string]*SchedulerNode),
		schedulingPolicy: getDefaultSchedulingPolicy(),
	}

	// 初始化默认节点（本地节点）
	scheduler.initializeDefaultNode()

	// 启动节点监控
	go scheduler.startNodeMonitoring()

	return scheduler
}

// getDefaultSchedulingPolicy 获取默认调度策略
func getDefaultSchedulingPolicy() *SchedulingPolicy {
	return &SchedulingPolicy{
		Algorithm:         "least_allocated",
		NodeSelector:      make(map[string]string),
		SchedulingTimeout: 30 * time.Second,
		MaxRetries:        3,
	}
}

// initializeDefaultNode 初始化默认节点
func (cs *ContainerScheduler) initializeDefaultNode() {
	defaultNode := &SchedulerNode{
		ID:     "local-node-1",
		Name:   "local-node",
		Status: "ready",
		Capacity: &NodeCapacity{
			CPU:     "4",
			Memory:  "8Gi",
			Storage: "100Gi",
			Pods:    100,
		},
		Allocatable: &NodeCapacity{
			CPU:     "3.5",
			Memory:  "7Gi",
			Storage: "90Gi",
			Pods:    90,
		},
		Used: &NodeCapacity{
			CPU:     "0",
			Memory:  "0",
			Storage: "0",
			Pods:    0,
		},
		Labels: map[string]string{
			"node.paas.io/type": "worker",
			"node.paas.io/zone": "local",
		},
		Taints:         make([]NodeTaint, 0),
		Conditions:     make([]NodeCondition, 0),
		ContainerCount: 0,
		LastHeartbeat:  time.Now(),
		CreatedAt:      time.Now(),
		Metadata:       make(map[string]interface{}),
	}

	cs.mutex.Lock()
	cs.nodes[defaultNode.ID] = defaultNode
	cs.mutex.Unlock()

	cs.logger.Info("默认调度节点初始化完成", "node_id", defaultNode.ID, "node_name", defaultNode.Name)
}

// ScheduleContainer 调度容器
func (cs *ContainerScheduler) ScheduleContainer(ctx context.Context, containerConfig *container.ContainerConfig) (string, error) {
	cs.logger.Debug("开始调度容器", "container_name", containerConfig.Name)

	// 构建调度请求
	request := &SchedulingRequest{
		ContainerConfig: containerConfig,
		Requirements:    cs.extractRequirements(containerConfig),
		Priority:        0, // 默认优先级
	}

	// 获取可用节点
	availableNodes := cs.getAvailableNodes()
	if len(availableNodes) == 0 {
		return "", fmt.Errorf("没有可用的调度节点")
	}

	// 过滤节点
	filteredNodes := cs.filterNodes(availableNodes, request)
	if len(filteredNodes) == 0 {
		return "", fmt.Errorf("没有满足条件的节点")
	}

	// 评分和排序
	scoredNodes := cs.scoreNodes(filteredNodes, request)
	sort.Slice(scoredNodes, func(i, j int) bool {
		return scoredNodes[i].Score > scoredNodes[j].Score
	})

	// 选择最佳节点
	selectedNode := scoredNodes[0]
	
	// 更新节点资源使用情况
	if err := cs.updateNodeUsage(selectedNode.NodeID, request.Requirements); err != nil {
		cs.logger.Warn("更新节点资源使用失败", "node_id", selectedNode.NodeID, "error", err)
	}

	cs.logger.Info("容器调度成功", "container_name", containerConfig.Name, 
		"node_id", selectedNode.NodeID, "score", selectedNode.Score)

	return selectedNode.NodeID, nil
}

// extractRequirements 提取资源要求
func (cs *ContainerScheduler) extractRequirements(config *container.ContainerConfig) *SchedulingRequirements {
	requirements := &SchedulingRequirements{
		CPU:     "100m",  // 默认CPU要求
		Memory:  "128Mi", // 默认内存要求
		Storage: "1Gi",   // 默认存储要求
		Labels:  make(map[string]string),
	}

	// 从容器配置中提取资源要求
	if config.Resources != nil {
		if config.Resources.CPULimit != "" {
			requirements.CPU = config.Resources.CPULimit
		}
		if config.Resources.MemoryLimit != "" {
			requirements.Memory = config.Resources.MemoryLimit
		}
	}

	// 复制标签
	for k, v := range config.Labels {
		requirements.Labels[k] = v
	}

	return requirements
}

// getAvailableNodes 获取可用节点
func (cs *ContainerScheduler) getAvailableNodes() []*SchedulerNode {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	availableNodes := make([]*SchedulerNode, 0)
	for _, node := range cs.nodes {
		if node.Status == "ready" {
			availableNodes = append(availableNodes, node)
		}
	}

	return availableNodes
}

// filterNodes 过滤节点
func (cs *ContainerScheduler) filterNodes(nodes []*SchedulerNode, request *SchedulingRequest) []*SchedulerNode {
	filteredNodes := make([]*SchedulerNode, 0)

	for _, node := range nodes {
		if cs.nodeMatchesRequirements(node, request) {
			filteredNodes = append(filteredNodes, node)
		}
	}

	return filteredNodes
}

// nodeMatchesRequirements 检查节点是否满足要求
func (cs *ContainerScheduler) nodeMatchesRequirements(node *SchedulerNode, request *SchedulingRequest) bool {
	// 检查资源容量
	if !cs.hasEnoughResources(node, request.Requirements) {
		return false
	}

	// 检查节点选择器
	if request.Constraints != nil && request.Constraints.NodeSelector != nil {
		for key, value := range request.Constraints.NodeSelector {
			if nodeValue, exists := node.Labels[key]; !exists || nodeValue != value {
				return false
			}
		}
	}

	// 检查污点和容忍度
	if !cs.toleratesTaints(node, request.Constraints) {
		return false
	}

	return true
}

// hasEnoughResources 检查节点是否有足够资源
func (cs *ContainerScheduler) hasEnoughResources(node *SchedulerNode, requirements *SchedulingRequirements) bool {
	// 简化的资源检查逻辑
	// 在实际实现中，需要解析资源字符串并进行精确比较
	return node.ContainerCount < 50 // 简单的容器数量限制
}

// toleratesTaints 检查是否容忍节点污点
func (cs *ContainerScheduler) toleratesTaints(node *SchedulerNode, constraints *SchedulingConstraints) bool {
	if len(node.Taints) == 0 {
		return true
	}

	if constraints == nil || len(constraints.Tolerations) == 0 {
		return false
	}

	// 检查每个污点是否有对应的容忍度
	for _, taint := range node.Taints {
		tolerated := false
		for _, toleration := range constraints.Tolerations {
			if cs.tolerationMatches(toleration, taint) {
				tolerated = true
				break
			}
		}
		if !tolerated {
			return false
		}
	}

	return true
}

// tolerationMatches 检查容忍度是否匹配污点
func (cs *ContainerScheduler) tolerationMatches(toleration Toleration, taint NodeTaint) bool {
	if toleration.Key != taint.Key {
		return false
	}

	if toleration.Effect != "" && toleration.Effect != taint.Effect {
		return false
	}

	if toleration.Operator == "Equal" {
		return toleration.Value == taint.Value
	}

	if toleration.Operator == "Exists" {
		return true
	}

	return false
}

// scoreNodes 为节点评分
func (cs *ContainerScheduler) scoreNodes(nodes []*SchedulerNode, request *SchedulingRequest) []*SchedulingResult {
	results := make([]*SchedulingResult, 0, len(nodes))

	for _, node := range nodes {
		score := cs.calculateNodeScore(node, request)
		result := &SchedulingResult{
			NodeID:    node.ID,
			NodeName:  node.Name,
			Score:     score,
			Reason:    cs.getScoreReason(score),
			Timestamp: time.Now(),
		}
		results = append(results, result)
	}

	return results
}

// calculateNodeScore 计算节点分数
func (cs *ContainerScheduler) calculateNodeScore(node *SchedulerNode, request *SchedulingRequest) float64 {
	switch cs.schedulingPolicy.Algorithm {
	case "least_allocated":
		return cs.calculateLeastAllocatedScore(node)
	case "most_allocated":
		return cs.calculateMostAllocatedScore(node)
	case "balanced":
		return cs.calculateBalancedScore(node)
	default:
		return cs.calculateLeastAllocatedScore(node)
	}
}

// calculateLeastAllocatedScore 计算最少分配分数
func (cs *ContainerScheduler) calculateLeastAllocatedScore(node *SchedulerNode) float64 {
	// 基于容器数量的简单评分
	maxContainers := 50.0
	usedContainers := float64(node.ContainerCount)
	
	if usedContainers >= maxContainers {
		return 0.0
	}
	
	return (maxContainers - usedContainers) / maxContainers * 100.0
}

// calculateMostAllocatedScore 计算最多分配分数
func (cs *ContainerScheduler) calculateMostAllocatedScore(node *SchedulerNode) float64 {
	return 100.0 - cs.calculateLeastAllocatedScore(node)
}

// calculateBalancedScore 计算平衡分数
func (cs *ContainerScheduler) calculateBalancedScore(node *SchedulerNode) float64 {
	leastScore := cs.calculateLeastAllocatedScore(node)
	mostScore := cs.calculateMostAllocatedScore(node)
	
	// 平衡分数：避免极端情况
	return math.Min(leastScore, mostScore)
}

// getScoreReason 获取评分原因
func (cs *ContainerScheduler) getScoreReason(score float64) string {
	if score >= 80 {
		return "excellent_fit"
	} else if score >= 60 {
		return "good_fit"
	} else if score >= 40 {
		return "acceptable_fit"
	} else {
		return "poor_fit"
	}
}

// updateNodeUsage 更新节点资源使用情况
func (cs *ContainerScheduler) updateNodeUsage(nodeID string, requirements *SchedulingRequirements) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	node, exists := cs.nodes[nodeID]
	if !exists {
		return fmt.Errorf("节点不存在: %s", nodeID)
	}

	// 增加容器计数
	node.ContainerCount++
	
	// 更新最后心跳时间
	node.LastHeartbeat = time.Now()

	cs.logger.Debug("节点资源使用情况已更新", "node_id", nodeID, "container_count", node.ContainerCount)
	return nil
}

// startNodeMonitoring 启动节点监控
func (cs *ContainerScheduler) startNodeMonitoring() {
	ticker := time.NewTicker(60 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		cs.updateNodeStatus()
	}
}

// updateNodeStatus 更新节点状态
func (cs *ContainerScheduler) updateNodeStatus() {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	now := time.Now()
	for _, node := range cs.nodes {
		// 检查节点心跳
		if now.Sub(node.LastHeartbeat) > 5*time.Minute {
			node.Status = "not_ready"
		} else {
			node.Status = "ready"
		}
	}
}
