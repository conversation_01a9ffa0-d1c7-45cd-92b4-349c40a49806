package script

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"paas-platform/internal/container"
	"paas-platform/internal/runtime"
	"paas-platform/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ScriptExecutionService Python脚本执行服务接口
type ScriptExecutionService interface {
	// 提交执行任务
	SubmitTask(ctx context.Context, req *ExecutionRequest) (*Task, error)

	// 获取任务状态
	GetTaskStatus(ctx context.Context, taskID string) (*TaskStatusResponse, error)

	// 验证任务访问权限
	ValidateTaskAccess(ctx context.Context, taskID, tenantID string) error

	// 取消任务
	CancelTask(ctx context.Context, taskID string) error

	// 获取任务结果
	GetTaskResult(ctx context.Context, taskID string) (*TaskResult, error)

	// 获取任务列表
	ListTasks(ctx context.Context, filter *TaskFilter) ([]*Task, int64, error)

	// 获取执行日志
	GetExecutionLogs(ctx context.Context, taskID string) ([]LogEntry, error)

	// 清理过期任务
	CleanupExpiredTasks(ctx context.Context, retentionDays int) error

	// 产物管理
	GetTaskArtifact(ctx context.Context, taskID, artifactName string) (*TaskArtifact, error)
	ArtifactExists(ctx context.Context, path string) bool

	// 模板管理
	CreateTemplate(ctx context.Context, params *CreateTemplateParams) (*ScriptTemplate, error)
	ListTemplates(ctx context.Context, filter *TemplateFilter) ([]*ScriptTemplate, int64, error)
	GetTemplate(ctx context.Context, templateID string) (*ScriptTemplate, error)
	UpdateTemplate(ctx context.Context, templateID string, params *UpdateTemplateParams) (*ScriptTemplate, error)
	DeleteTemplate(ctx context.Context, templateID string) error

	// 调度管理
	CreateSchedule(ctx context.Context, params *CreateScheduleParams) (*TaskSchedule, error)
	ListSchedules(ctx context.Context, filter *ScheduleFilter) ([]*TaskSchedule, int64, error)
	GetSchedule(ctx context.Context, scheduleID string) (*TaskSchedule, error)
	UpdateSchedule(ctx context.Context, scheduleID string, params *UpdateScheduleParams) (*TaskSchedule, error)
	DeleteSchedule(ctx context.Context, scheduleID string) error
	TriggerSchedule(ctx context.Context, scheduleID string, params *TriggerScheduleParams) (*Task, error)
	ValidateCronExpression(cronExpr string) error

	// 统计功能
	GetStatsOverview(ctx context.Context, tenantID string) (*StatsOverviewResponse, error)
	GetMetrics(ctx context.Context, query *MetricsQuery) ([]MetricPoint, error)
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	AppID         string                 `json:"app_id" validate:"required"`       // 应用ID
	ScriptPath    string                 `json:"script_path" validate:"required"`  // 脚本路径
	RuntimeType   string                 `json:"runtime_type"`                     // 运行时类型
	RuntimeConfig *RuntimeConfig         `json:"runtime_config"`                   // 运行时配置
	Parameters    map[string]interface{} `json:"parameters"`                       // 执行参数
	Environment   map[string]string      `json:"environment"`                      // 环境变量
	Timeout       time.Duration          `json:"timeout"`                          // 超时时间
	Priority      int                    `json:"priority"`                         // 优先级 (1-10)
	Callback      string                 `json:"callback"`                         // 回调URL
	UserID        string                 `json:"user_id"`                          // 用户ID
	TenantID      string                 `json:"tenant_id"`                        // 租户ID
}

// RuntimeConfig 运行时配置
type RuntimeConfig struct {
	Type         string                 `json:"type"`
	Version      string                 `json:"version"`
	ImageTag     string                 `json:"image_tag"`
	Dependencies *DependencyConfig      `json:"dependencies"`
	Resources    *ResourceLimits        `json:"resources"`
	Options      map[string]interface{} `json:"options"`
}

// DependencyConfig 依赖配置
type DependencyConfig struct {
	Type     string                 `json:"type"`
	File     string                 `json:"file"`
	Packages []string               `json:"packages"`
	Registry string                 `json:"registry"`
	Options  map[string]interface{} `json:"options"`
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	CPULimit    string `json:"cpu_limit"`
	MemoryLimit string `json:"memory_limit"`
	DiskLimit   string `json:"disk_limit"`
}

// TaskFilter 任务过滤器
type TaskFilter struct {
	AppID    string     `json:"app_id"`
	UserID   string     `json:"user_id"`
	TenantID string     `json:"tenant_id"`
	Status   TaskStatus `json:"status"`
	Page     int        `json:"page"`
	PageSize int        `json:"page_size"`
}

// TaskStatusResponse 任务状态响应
type TaskStatusResponse struct {
	ID                  string    `json:"id"`
	Status              string    `json:"status"`
	Progress            int       `json:"progress"`
	StartTime           *time.Time `json:"start_time"`
	EstimatedCompletion *time.Time `json:"estimated_completion"`
	Message             string    `json:"message"`
}

// TaskResult 任务结果
type TaskResult struct {
	TaskID      string                 `json:"task_id"`
	Status      string                 `json:"status"`
	ExitCode    int                    `json:"exit_code"`
	Output      string                 `json:"output"`
	Error       string                 `json:"error"`
	Duration    int64                  `json:"duration"` // 毫秒
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Resources   ResourceUsage          `json:"resources"`
	Artifacts   []Artifact             `json:"artifacts"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Source    string    `json:"source"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU使用率 (%)
	MemoryUsage int64   `json:"memory_usage"` // 内存使用量 (字节)
	DiskUsage   int64   `json:"disk_usage"`   // 磁盘使用量 (字节)
	NetworkRx   int64   `json:"network_rx"`   // 网络接收字节
	NetworkTx   int64   `json:"network_tx"`   // 网络发送字节
}

// Artifact 执行产物
type Artifact struct {
	Name     string `json:"name"`
	Path     string `json:"path"`
	Size     int64  `json:"size"`
	MimeType string `json:"mime_type"`
	URL      string `json:"url"`
}

// CreateTemplateParams 创建模板参数
type CreateTemplateParams struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Language    string                 `json:"language"`
	Version     string                 `json:"version"`
	Content     string                 `json:"content"`
	Parameters  map[string]interface{} `json:"parameters"`
	Environment map[string]string      `json:"environment"`
	Resources   map[string]interface{} `json:"resources"`
	Tags        []string               `json:"tags"`
	TenantID    string                 `json:"tenant_id"`
	CreatedBy   string                 `json:"created_by"`
}

// UpdateTemplateParams 更新模板参数
type UpdateTemplateParams struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Content     string                 `json:"content"`
	Parameters  map[string]interface{} `json:"parameters"`
	Environment map[string]string      `json:"environment"`
	Resources   map[string]interface{} `json:"resources"`
	Tags        []string               `json:"tags"`
	Status      ScriptTemplateStatus   `json:"status"`
}

// TemplateFilter 模板过滤器
type TemplateFilter struct {
	Category string               `json:"category"`
	Language string               `json:"language"`
	Status   ScriptTemplateStatus `json:"status"`
	TenantID string               `json:"tenant_id"`
	Page     int                  `json:"page"`
	PageSize int                  `json:"page_size"`
}

// CreateScheduleParams 创建调度参数
type CreateScheduleParams struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	AppID       string                 `json:"app_id"`
	ScriptPath  string                 `json:"script_path"`
	Parameters  map[string]interface{} `json:"parameters"`
	Environment map[string]string      `json:"environment"`
	CronExpr    string                 `json:"cron_expr"`
	Timezone    string                 `json:"timezone"`
	TenantID    string                 `json:"tenant_id"`
	CreatedBy   string                 `json:"created_by"`
}

// UpdateScheduleParams 更新调度参数
type UpdateScheduleParams struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Environment map[string]string      `json:"environment"`
	CronExpr    string                 `json:"cron_expr"`
	Timezone    string                 `json:"timezone"`
	Status      TaskScheduleStatus     `json:"status"`
}

// ScheduleFilter 调度过滤器
type ScheduleFilter struct {
	AppID    string             `json:"app_id"`
	Status   TaskScheduleStatus `json:"status"`
	TenantID string             `json:"tenant_id"`
	Page     int                `json:"page"`
	PageSize int                `json:"page_size"`
}

// TriggerScheduleParams 触发调度参数
type TriggerScheduleParams struct {
	Parameters  map[string]interface{} `json:"parameters"`
	Environment map[string]string      `json:"environment"`
	UserID      string                 `json:"user_id"`
}

// MetricsQuery 指标查询参数
type MetricsQuery struct {
	TenantID  string `json:"tenant_id"`
	TimeRange string `json:"time_range"`
	Interval  string `json:"interval"`
}

// scriptExecutionService 脚本执行服务实现
type scriptExecutionService struct {
	db               *gorm.DB
	containerManager container.ContainerManager
	runtimeManager   runtime.RuntimeManager
	logger           logger.Logger
	config           ServiceConfig
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	DefaultTimeout      time.Duration `json:"default_timeout"`
	MaxConcurrentTasks  int           `json:"max_concurrent_tasks"`
	TaskRetentionDays   int           `json:"task_retention_days"`
	DefaultPythonImage  string        `json:"default_python_image"`
	WorkspaceBasePath   string        `json:"workspace_base_path"`
	ArtifactBasePath    string        `json:"artifact_base_path"`
	CallbackTimeout     time.Duration `json:"callback_timeout"`
}

// NewScriptExecutionService 创建脚本执行服务
func NewScriptExecutionService(
	db *gorm.DB,
	containerManager container.ContainerManager,
	runtimeManager runtime.RuntimeManager,
	logger logger.Logger,
	config ServiceConfig,
) ScriptExecutionService {
	return &scriptExecutionService{
		db:               db,
		containerManager: containerManager,
		runtimeManager:   runtimeManager,
		logger:           logger,
		config:           config,
	}
}

// SubmitTask 提交执行任务
func (s *scriptExecutionService) SubmitTask(ctx context.Context, req *ExecutionRequest) (*Task, error) {
	// 验证请求参数
	if err := s.validateRequest(req); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 检查并发任务限制
	if err := s.checkConcurrentLimit(ctx, req.TenantID); err != nil {
		return nil, fmt.Errorf("并发任务限制: %w", err)
	}

	// 创建任务
	task := &Task{
		ID:          uuid.New().String(),
		AppID:       req.AppID,
		UserID:      req.UserID,
		TenantID:    req.TenantID,
		ScriptPath:  req.ScriptPath,
		Parameters:  s.marshalJSON(req.Parameters),
		Environment: s.marshalJSON(req.Environment),
		Status:      TaskStatusPending,
		Priority:    req.Priority,
		Timeout:     int(req.Timeout.Seconds()),
		Callback:    req.Callback,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 设置默认值
	if task.Timeout == 0 {
		task.Timeout = int(s.config.DefaultTimeout.Seconds())
	}
	if task.Priority == 0 {
		task.Priority = 5 // 默认优先级
	}

	// 保存任务到数据库
	if err := s.db.Create(task).Error; err != nil {
		return nil, fmt.Errorf("保存任务失败: %w", err)
	}

	// 异步执行任务
	go s.executeTaskAsync(context.Background(), task)

	s.logger.Info("脚本执行任务已提交", "task_id", task.ID, "app_id", req.AppID, "script_path", req.ScriptPath)
	return task, nil
}

// GetTaskStatus 获取任务状态
func (s *scriptExecutionService) GetTaskStatus(ctx context.Context, taskID string) (*TaskStatusResponse, error) {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return nil, fmt.Errorf("任务不存在: %w", err)
	}

	status := &TaskStatusResponse{
		ID:      task.ID,
		Status:  string(task.Status),
		Message: task.Error,
	}

	// 计算进度
	if task.Status == TaskStatusRunning {
		status.Progress = s.calculateProgress(&task)
		status.EstimatedCompletion = s.estimateCompletion(&task)
	}

	if task.StartTime != nil {
		status.StartTime = task.StartTime
	}

	return status, nil
}

// ValidateTaskAccess 验证任务访问权限
func (s *scriptExecutionService) ValidateTaskAccess(ctx context.Context, taskID, tenantID string) error {
	var task Task
	if err := s.db.Where("id = ? AND tenant_id = ?", taskID, tenantID).First(&task).Error; err != nil {
		return fmt.Errorf("任务不存在或无权访问: %w", err)
	}
	return nil
}

// CancelTask 取消任务
func (s *scriptExecutionService) CancelTask(ctx context.Context, taskID string) error {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return fmt.Errorf("任务不存在: %w", err)
	}

	// 只能取消待执行或运行中的任务
	if task.Status != TaskStatusPending && task.Status != TaskStatusQueued && task.Status != TaskStatusRunning {
		return fmt.Errorf("任务状态不允许取消: %s", task.Status)
	}

	// 如果任务正在运行，停止容器
	if task.ContainerID != "" {
		if err := s.containerManager.RemoveContainer(ctx, task.ContainerID, true); err != nil {
			s.logger.Error("停止容器失败", "error", err, "container_id", task.ContainerID)
		}
	}

	// 更新任务状态
	endTime := time.Now()
	updates := map[string]interface{}{
		"status":     TaskStatusCanceled,
		"end_time":   &endTime,
		"updated_at": time.Now(),
	}

	if err := s.db.Model(&task).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	s.logger.Info("任务已取消", "task_id", taskID)
	return nil
}

// GetTaskResult 获取任务结果
func (s *scriptExecutionService) GetTaskResult(ctx context.Context, taskID string) (*TaskResult, error) {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return nil, fmt.Errorf("任务不存在: %w", err)
	}

	result := &TaskResult{
		TaskID:   task.ID,
		Status:   string(task.Status),
		Output:   task.Output,
		Error:    task.Error,
		StartTime: time.Time{},
		EndTime:   time.Time{},
	}

	if task.ExitCode != nil {
		result.ExitCode = *task.ExitCode
	}

	if task.StartTime != nil {
		result.StartTime = *task.StartTime
	}

	if task.EndTime != nil {
		result.EndTime = *task.EndTime
		if !task.StartTime.IsZero() {
			result.Duration = task.EndTime.Sub(*task.StartTime).Milliseconds()
		}
	}

	// 解析资源使用情况
	if task.Resources != nil {
		var resources ResourceUsage
		if err := json.Unmarshal(task.Resources, &resources); err == nil {
			result.Resources = resources
		}
	}

	// 获取产物列表
	artifacts, err := s.getTaskArtifacts(ctx, taskID)
	if err != nil {
		s.logger.Error("获取任务产物失败", "error", err, "task_id", taskID)
	} else {
		result.Artifacts = artifacts
	}

	return result, nil
}

// ListTasks 获取任务列表
func (s *scriptExecutionService) ListTasks(ctx context.Context, filter *TaskFilter) ([]*Task, int64, error) {
	query := s.db.Model(&Task{})

	// 应用过滤条件
	if filter.AppID != "" {
		query = query.Where("app_id = ?", filter.AppID)
	}
	if filter.UserID != "" {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.TenantID != "" {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取任务总数失败: %w", err)
	}

	// 分页查询
	var tasks []*Task
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(filter.PageSize).Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("查询任务列表失败: %w", err)
	}

	return tasks, total, nil
}

// GetExecutionLogs 获取执行日志
func (s *scriptExecutionService) GetExecutionLogs(ctx context.Context, taskID string) ([]LogEntry, error) {
	var task Task
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return nil, fmt.Errorf("任务不存在: %w", err)
	}

	var logs []LogEntry

	// 如果容器存在，获取容器日志
	if task.ContainerID != "" {
		containerLogs, err := s.containerManager.GetContainerLogs(ctx, task.ContainerID)
		if err != nil {
			s.logger.Error("获取容器日志失败", "error", err, "container_id", task.ContainerID)
		} else {
			// 解析容器日志
			logs = s.parseContainerLogs(containerLogs)
		}
	}

	// 添加任务状态变更日志
	statusLogs := s.getTaskStatusLogs(ctx, taskID)
	logs = append(logs, statusLogs...)

	return logs, nil
}

// CleanupExpiredTasks 清理过期任务
func (s *scriptExecutionService) CleanupExpiredTasks(ctx context.Context, retentionDays int) error {
	expiredTime := time.Now().AddDate(0, 0, -retentionDays)

	// 查找过期任务
	var expiredTasks []Task
	if err := s.db.Where("created_at < ? AND status IN ?", expiredTime, 
		[]TaskStatus{TaskStatusCompleted, TaskStatusFailed, TaskStatusCanceled}).
		Find(&expiredTasks).Error; err != nil {
		return fmt.Errorf("查询过期任务失败: %w", err)
	}

	// 清理过期任务
	for _, task := range expiredTasks {
		// 清理任务产物
		if err := s.cleanupTaskArtifacts(ctx, task.ID); err != nil {
			s.logger.Error("清理任务产物失败", "error", err, "task_id", task.ID)
		}

		// 删除任务记录
		if err := s.db.Delete(&task).Error; err != nil {
			s.logger.Error("删除过期任务失败", "error", err, "task_id", task.ID)
		}
	}

	s.logger.Info("过期任务清理完成", "count", len(expiredTasks), "retention_days", retentionDays)
	return nil
}

// 私有方法

// executeTaskAsync 异步执行任务
func (s *scriptExecutionService) executeTaskAsync(ctx context.Context, task *Task) {
	// 更新任务状态为排队中
	s.updateTaskStatus(task.ID, TaskStatusQueued, "")

	// 执行任务
	if err := s.executeTask(ctx, task); err != nil {
		s.logger.Error("任务执行失败", "error", err, "task_id", task.ID)
		s.updateTaskStatus(task.ID, TaskStatusFailed, err.Error())
	}
}

// executeTask 执行任务
func (s *scriptExecutionService) executeTask(ctx context.Context, task *Task) error {
	// 更新任务状态为运行中
	startTime := time.Now()
	s.updateTaskStatus(task.ID, TaskStatusRunning, "")
	s.db.Model(task).Update("start_time", &startTime)

	// 创建执行容器
	containerSpec := s.buildContainerSpec(task)
	container, err := s.containerManager.CreateContainer(ctx, containerSpec)
	if err != nil {
		return fmt.Errorf("创建容器失败: %w", err)
	}

	// 更新容器ID
	s.db.Model(task).Update("container_id", container.ID)

	// 启动容器
	if err := s.containerManager.StartContainer(ctx, container.ID); err != nil {
		s.containerManager.RemoveContainer(ctx, container.ID, true)
		return fmt.Errorf("启动容器失败: %w", err)
	}

	// 执行Python脚本
	result, err := s.executeScript(ctx, container.ID, task)
	if err != nil {
		s.containerManager.RemoveContainer(ctx, container.ID, true)
		return fmt.Errorf("执行脚本失败: %w", err)
	}

	// 收集执行结果
	if err := s.collectExecutionResult(ctx, task, result); err != nil {
		s.logger.Error("收集执行结果失败", "error", err, "task_id", task.ID)
	}

	// 销毁容器
	if err := s.containerManager.RemoveContainer(ctx, container.ID, true); err != nil {
		s.logger.Error("销毁容器失败", "error", err, "container_id", container.ID)
	}

	// 更新任务状态
	endTime := time.Now()
	status := TaskStatusCompleted
	if result.ExitCode != 0 {
		status = TaskStatusFailed
	}

	updates := map[string]interface{}{
		"status":     status,
		"end_time":   &endTime,
		"exit_code":  result.ExitCode,
		"output":     result.Stdout,
		"error":      result.Stderr,
		"updated_at": time.Now(),
	}

	s.db.Model(task).Updates(updates)

	// 发送回调通知
	if task.Callback != "" {
		go s.sendCallback(task, result)
	}

	s.logger.Info("任务执行完成", "task_id", task.ID, "status", status, "exit_code", result.ExitCode)
	return nil
}

// 辅助方法

// validateRequest 验证请求参数
func (s *scriptExecutionService) validateRequest(req *ExecutionRequest) error {
	if req.AppID == "" {
		return fmt.Errorf("应用ID不能为空")
	}
	if req.ScriptPath == "" {
		return fmt.Errorf("脚本路径不能为空")
	}
	if req.Priority < 1 || req.Priority > 10 {
		return fmt.Errorf("优先级必须在1-10之间")
	}
	return nil
}

// checkConcurrentLimit 检查并发限制
func (s *scriptExecutionService) checkConcurrentLimit(ctx context.Context, tenantID string) error {
	var count int64
	err := s.db.Model(&Task{}).Where("tenant_id = ? AND status IN ?", tenantID, 
		[]TaskStatus{TaskStatusPending, TaskStatusQueued, TaskStatusRunning}).Count(&count).Error
	if err != nil {
		return fmt.Errorf("检查并发任务数失败: %w", err)
	}

	if int(count) >= s.config.MaxConcurrentTasks {
		return fmt.Errorf("超过最大并发任务数限制: %d", s.config.MaxConcurrentTasks)
	}

	return nil
}

// marshalJSON 序列化JSON
func (s *scriptExecutionService) marshalJSON(data interface{}) []byte {
	if data == nil {
		return nil
	}
	bytes, _ := json.Marshal(data)
	return bytes
}

// updateTaskStatus 更新任务状态
func (s *scriptExecutionService) updateTaskStatus(taskID string, status TaskStatus, errorMsg string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	if errorMsg != "" {
		updates["error"] = errorMsg
	}
	s.db.Model(&Task{}).Where("id = ?", taskID).Updates(updates)
}

// buildContainerSpec 构建容器规格
func (s *scriptExecutionService) buildContainerSpec(task *Task) *container.ContainerConfig {
	// 解析环境变量
	env := make(map[string]string)
	if task.Environment != nil {
		json.Unmarshal(task.Environment, &env)
	}

	// 添加默认环境变量
	env["TASK_ID"] = task.ID
	env["SCRIPT_PATH"] = task.ScriptPath
	env["PYTHONUNBUFFERED"] = "1"

	// 解析执行参数
	var parameters map[string]interface{}
	if task.Parameters != nil {
		json.Unmarshal(task.Parameters, &parameters)
	}

	// 构建执行命令
	command := []string{"python", task.ScriptPath}

	// 添加参数到命令行
	for key, value := range parameters {
		command = append(command, fmt.Sprintf("--%s", key))
		command = append(command, fmt.Sprintf("%v", value))
	}

	// 转换环境变量为数组格式
	envArray := make([]string, 0, len(env))
	for k, v := range env {
		envArray = append(envArray, fmt.Sprintf("%s=%s", k, v))
	}

	return &container.ContainerConfig{
		Image:      s.config.DefaultPythonImage,
		Name:       fmt.Sprintf("script-task-%s", task.ID),
		Command:    command,
		Env:        envArray,
		WorkingDir: "/workspace",
		Resources: &container.ResourceConfig{
			CPULimit:    "1",
			MemoryLimit: "512m",
			DiskLimit:   "1g",
		},
		Volumes: []container.VolumeMount{
			{
				Source:   fmt.Sprintf("%s/%s", s.config.WorkspaceBasePath, task.AppID),
				Target:   "/workspace",
				Type:     "bind",
				ReadOnly: true,
			},
			{
				Source:   fmt.Sprintf("%s/%s", s.config.ArtifactBasePath, task.ID),
				Target:   "/output",
				Type:     "bind",
				ReadOnly: false,
			},
		},
		NetworkMode: "bridge",
		AutoRemove:  true,
		Labels: map[string]string{
			"task_id": task.ID,
			"app_id":  task.AppID,
		},
	}
}

// executeScript 执行Python脚本
func (s *scriptExecutionService) executeScript(ctx context.Context, containerID string, task *Task) (*container.ExecResult, error) {
	// 设置超时上下文
	execCtx, cancel := context.WithTimeout(ctx, time.Duration(task.Timeout)*time.Second)
	defer cancel()

	// 构建执行命令
	var parameters map[string]interface{}
	if task.Parameters != nil {
		json.Unmarshal(task.Parameters, &parameters)
	}

	command := []string{"python", task.ScriptPath}
	for key, value := range parameters {
		command = append(command, fmt.Sprintf("--%s", key))
		command = append(command, fmt.Sprintf("%v", value))
	}

	// 执行命令
	result, err := s.containerManager.ExecCommand(execCtx, containerID, command)
	if err != nil {
		return nil, fmt.Errorf("执行命令失败: %w", err)
	}

	return result, nil
}

// collectExecutionResult 收集执行结果
func (s *scriptExecutionService) collectExecutionResult(ctx context.Context, task *Task, result *container.ExecResult) error {
	// 收集资源使用情况
	resourceUsage := ResourceUsage{
		CPUUsage:    0, // TODO: 从容器指标获取
		MemoryUsage: 0, // TODO: 从容器指标获取
	}

	resourceBytes, _ := json.Marshal(resourceUsage)
	s.db.Model(task).Update("resources", resourceBytes)

	// 收集输出产物
	artifacts, err := s.collectArtifacts(ctx, task.ID)
	if err != nil {
		s.logger.Error("收集产物失败", "error", err, "task_id", task.ID)
	} else {
		// 保存产物信息
		for _, artifact := range artifacts {
			artifactRecord := &TaskArtifact{
				ID:       uuid.New().String(),
				TaskID:   task.ID,
				Name:     artifact.Name,
				Path:     artifact.Path,
				Size:     artifact.Size,
				MimeType: artifact.MimeType,
				URL:      artifact.URL,
				CreatedAt: time.Now(),
			}
			s.db.Create(artifactRecord)
		}
	}

	return nil
}

// collectArtifacts 收集产物
func (s *scriptExecutionService) collectArtifacts(ctx context.Context, taskID string) ([]Artifact, error) {
	// TODO: 实现产物收集逻辑
	// 1. 扫描输出目录
	// 2. 上传到存储服务
	// 3. 生成访问URL
	return []Artifact{}, nil
}

// calculateProgress 计算进度
func (s *scriptExecutionService) calculateProgress(task *Task) int {
	if task.StartTime == nil {
		return 0
	}

	elapsed := time.Since(*task.StartTime)
	timeout := time.Duration(task.Timeout) * time.Second

	progress := int(float64(elapsed) / float64(timeout) * 100)
	if progress > 95 {
		progress = 95 // 最多显示95%，避免超时前显示100%
	}

	return progress
}

// estimateCompletion 估算完成时间
func (s *scriptExecutionService) estimateCompletion(task *Task) *time.Time {
	if task.StartTime == nil {
		return nil
	}

	timeout := time.Duration(task.Timeout) * time.Second
	completion := task.StartTime.Add(timeout)
	return &completion
}

// parseContainerLogs 解析容器日志
func (s *scriptExecutionService) parseContainerLogs(logs io.ReadCloser) []LogEntry {
	defer logs.Close()

	var entries []LogEntry

	// 读取日志内容
	content, err := io.ReadAll(logs)
	if err != nil {
		s.logger.Error("读取容器日志失败", "error", err)
		return entries
	}

	logContent := string(content)

	// TODO: 实现日志解析逻辑
	// 1. 按行分割日志
	lines := strings.Split(logContent, "\n")
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			entries = append(entries, LogEntry{
				Timestamp: time.Now(),
				Level:     "INFO",
				Message:   line,
			})
		}
	}

	return entries
}

// getTaskStatusLogs 获取任务状态变更日志
func (s *scriptExecutionService) getTaskStatusLogs(ctx context.Context, taskID string) []LogEntry {
	var logs []LogEntry

	// TODO: 从任务事件表获取状态变更记录

	return logs
}

// calculateNextRun 计算下次运行时间
func (s *scriptExecutionService) calculateNextRun(cronExpr, timezone string) (*time.Time, error) {
	// TODO: 实现基于Cron表达式的下次运行时间计算
	// 这里应该使用cron库来解析表达式并计算下次运行时间

	// 简单实现：假设每小时运行一次
	nextRun := time.Now().Add(1 * time.Hour)
	return &nextRun, nil
}

// getTaskStatsForPeriod 获取指定时间段的任务统计
func (s *scriptExecutionService) getTaskStatsForPeriod(ctx context.Context, tenantID string, startTime, endTime time.Time) (*TaskStatsData, error) {
	var stats struct {
		TotalTasks     int     `json:"total_tasks"`
		CompletedTasks int     `json:"completed_tasks"`
		FailedTasks    int     `json:"failed_tasks"`
		CanceledTasks  int     `json:"canceled_tasks"`
		AvgDuration    float64 `json:"avg_duration"`
	}

	query := `
		SELECT
			COUNT(*) as total_tasks,
			COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_tasks,
			COUNT(CASE WHEN status = 'canceled' THEN 1 END) as canceled_tasks,
			AVG(CASE WHEN duration > 0 THEN duration ELSE 0 END) as avg_duration
		FROM script_tasks
		WHERE tenant_id = ? AND created_at >= ? AND created_at < ?
	`

	if err := s.db.Raw(query, tenantID, startTime, endTime).Scan(&stats).Error; err != nil {
		return nil, fmt.Errorf("查询任务统计失败: %w", err)
	}

	successRate := 0.0
	if stats.TotalTasks > 0 {
		successRate = float64(stats.CompletedTasks) / float64(stats.TotalTasks) * 100
	}

	return &TaskStatsData{
		TotalTasks:     stats.TotalTasks,
		CompletedTasks: stats.CompletedTasks,
		FailedTasks:    stats.FailedTasks,
		CanceledTasks:  stats.CanceledTasks,
		SuccessRate:    successRate,
		AvgDuration:    stats.AvgDuration,
	}, nil
}

// getResourceUsageForPeriod 获取指定时间段的资源使用情况
func (s *scriptExecutionService) getResourceUsageForPeriod(ctx context.Context, tenantID string, startTime, endTime time.Time) (float64, int64) {
	var result struct {
		AvgCPU    float64 `json:"avg_cpu"`
		AvgMemory int64   `json:"avg_memory"`
	}

	query := `
		SELECT
			AVG(cpu_usage) as avg_cpu,
			AVG(memory_usage) as avg_memory
		FROM script_execution_metrics m
		JOIN script_tasks t ON m.task_id = t.id
		WHERE t.tenant_id = ? AND m.timestamp >= ? AND m.timestamp < ?
	`

	if err := s.db.Raw(query, tenantID, startTime, endTime).Scan(&result).Error; err != nil {
		s.logger.Error("查询资源使用情况失败", "error", err)
		return 0, 0
	}

	return result.AvgCPU, result.AvgMemory
}

// GetTaskArtifact 获取任务产物
func (s *scriptExecutionService) GetTaskArtifact(ctx context.Context, taskID, artifactName string) (*TaskArtifact, error) {
	var artifact TaskArtifact
	if err := s.db.Where("task_id = ? AND name = ?", taskID, artifactName).First(&artifact).Error; err != nil {
		return nil, fmt.Errorf("产物不存在: %w", err)
	}
	return &artifact, nil
}

// ArtifactExists 检查产物文件是否存在
func (s *scriptExecutionService) ArtifactExists(ctx context.Context, path string) bool {
	// TODO: 实现文件存在性检查
	// 这里应该检查实际的文件系统或对象存储
	return true
}

// CreateTemplate 创建脚本模板
func (s *scriptExecutionService) CreateTemplate(ctx context.Context, params *CreateTemplateParams) (*ScriptTemplate, error) {
	template := &ScriptTemplate{
		ID:          uuid.New().String(),
		Name:        params.Name,
		Description: params.Description,
		Category:    params.Category,
		Language:    params.Language,
		Version:     params.Version,
		Content:     params.Content,
		Parameters:  s.marshalJSON(params.Parameters),
		Environment: s.marshalJSON(params.Environment),
		Resources:   s.marshalJSON(params.Resources),
		Tags:        s.marshalJSON(params.Tags),
		Status:      ScriptTemplateStatusActive,
		TenantID:    params.TenantID,
		CreatedBy:   params.CreatedBy,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(template).Error; err != nil {
		return nil, fmt.Errorf("创建模板失败: %w", err)
	}

	return template, nil
}

// ListTemplates 获取模板列表
func (s *scriptExecutionService) ListTemplates(ctx context.Context, filter *TemplateFilter) ([]*ScriptTemplate, int64, error) {
	query := s.db.Model(&ScriptTemplate{}).Where("tenant_id = ?", filter.TenantID)

	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}
	if filter.Language != "" {
		query = query.Where("language = ?", filter.Language)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计模板数量失败: %w", err)
	}

	var templates []*ScriptTemplate
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Offset(offset).Limit(filter.PageSize).Order("created_at DESC").Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("查询模板列表失败: %w", err)
	}

	return templates, total, nil
}

// GetTemplate 获取模板详情
func (s *scriptExecutionService) GetTemplate(ctx context.Context, templateID string) (*ScriptTemplate, error) {
	var template ScriptTemplate
	if err := s.db.Where("id = ?", templateID).First(&template).Error; err != nil {
		return nil, fmt.Errorf("模板不存在: %w", err)
	}
	return &template, nil
}

// UpdateTemplate 更新模板
func (s *scriptExecutionService) UpdateTemplate(ctx context.Context, templateID string, params *UpdateTemplateParams) (*ScriptTemplate, error) {
	var template ScriptTemplate
	if err := s.db.Where("id = ?", templateID).First(&template).Error; err != nil {
		return nil, fmt.Errorf("模板不存在: %w", err)
	}

	// 更新字段
	if params.Name != "" {
		template.Name = params.Name
	}
	if params.Description != "" {
		template.Description = params.Description
	}
	if params.Content != "" {
		template.Content = params.Content
	}
	if params.Parameters != nil {
		template.Parameters = s.marshalJSON(params.Parameters)
	}
	if params.Environment != nil {
		template.Environment = s.marshalJSON(params.Environment)
	}
	if params.Resources != nil {
		template.Resources = s.marshalJSON(params.Resources)
	}
	if params.Tags != nil {
		template.Tags = s.marshalJSON(params.Tags)
	}
	if params.Status != "" {
		template.Status = params.Status
	}
	template.UpdatedAt = time.Now()

	if err := s.db.Save(&template).Error; err != nil {
		return nil, fmt.Errorf("更新模板失败: %w", err)
	}

	return &template, nil
}

// DeleteTemplate 删除模板
func (s *scriptExecutionService) DeleteTemplate(ctx context.Context, templateID string) error {
	result := s.db.Where("id = ?", templateID).Delete(&ScriptTemplate{})
	if result.Error != nil {
		return fmt.Errorf("删除模板失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("模板不存在")
	}
	return nil
}

// CreateSchedule 创建任务调度
func (s *scriptExecutionService) CreateSchedule(ctx context.Context, params *CreateScheduleParams) (*TaskSchedule, error) {
	// 解析下次运行时间
	nextRun, err := s.calculateNextRun(params.CronExpr, params.Timezone)
	if err != nil {
		return nil, fmt.Errorf("计算下次运行时间失败: %w", err)
	}

	schedule := &TaskSchedule{
		ID:          uuid.New().String(),
		Name:        params.Name,
		Description: params.Description,
		AppID:       params.AppID,
		ScriptPath:  params.ScriptPath,
		Parameters:  s.marshalJSON(params.Parameters),
		Environment: s.marshalJSON(params.Environment),
		CronExpr:    params.CronExpr,
		Timezone:    params.Timezone,
		Status:      TaskScheduleStatusActive,
		NextRun:     nextRun,
		TenantID:    params.TenantID,
		CreatedBy:   params.CreatedBy,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(schedule).Error; err != nil {
		return nil, fmt.Errorf("创建调度失败: %w", err)
	}

	return schedule, nil
}

// ListSchedules 获取调度列表
func (s *scriptExecutionService) ListSchedules(ctx context.Context, filter *ScheduleFilter) ([]*TaskSchedule, int64, error) {
	query := s.db.Model(&TaskSchedule{}).Where("tenant_id = ?", filter.TenantID)

	if filter.AppID != "" {
		query = query.Where("app_id = ?", filter.AppID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计调度数量失败: %w", err)
	}

	var schedules []*TaskSchedule
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Offset(offset).Limit(filter.PageSize).Order("created_at DESC").Find(&schedules).Error; err != nil {
		return nil, 0, fmt.Errorf("查询调度列表失败: %w", err)
	}

	return schedules, total, nil
}

// GetSchedule 获取调度详情
func (s *scriptExecutionService) GetSchedule(ctx context.Context, scheduleID string) (*TaskSchedule, error) {
	var schedule TaskSchedule
	if err := s.db.Where("id = ?", scheduleID).First(&schedule).Error; err != nil {
		return nil, fmt.Errorf("调度不存在: %w", err)
	}
	return &schedule, nil
}

// UpdateSchedule 更新调度
func (s *scriptExecutionService) UpdateSchedule(ctx context.Context, scheduleID string, params *UpdateScheduleParams) (*TaskSchedule, error) {
	var schedule TaskSchedule
	if err := s.db.Where("id = ?", scheduleID).First(&schedule).Error; err != nil {
		return nil, fmt.Errorf("调度不存在: %w", err)
	}

	// 更新字段
	if params.Name != "" {
		schedule.Name = params.Name
	}
	if params.Description != "" {
		schedule.Description = params.Description
	}
	if params.Parameters != nil {
		schedule.Parameters = s.marshalJSON(params.Parameters)
	}
	if params.Environment != nil {
		schedule.Environment = s.marshalJSON(params.Environment)
	}
	if params.CronExpr != "" {
		schedule.CronExpr = params.CronExpr
		// 重新计算下次运行时间
		nextRun, err := s.calculateNextRun(params.CronExpr, params.Timezone)
		if err != nil {
			return nil, fmt.Errorf("计算下次运行时间失败: %w", err)
		}
		schedule.NextRun = nextRun
	}
	if params.Timezone != "" {
		schedule.Timezone = params.Timezone
	}
	if params.Status != "" {
		schedule.Status = params.Status
	}
	schedule.UpdatedAt = time.Now()

	if err := s.db.Save(&schedule).Error; err != nil {
		return nil, fmt.Errorf("更新调度失败: %w", err)
	}

	return &schedule, nil
}

// DeleteSchedule 删除调度
func (s *scriptExecutionService) DeleteSchedule(ctx context.Context, scheduleID string) error {
	result := s.db.Where("id = ?", scheduleID).Delete(&TaskSchedule{})
	if result.Error != nil {
		return fmt.Errorf("删除调度失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("调度不存在")
	}
	return nil
}

// TriggerSchedule 手动触发调度
func (s *scriptExecutionService) TriggerSchedule(ctx context.Context, scheduleID string, params *TriggerScheduleParams) (*Task, error) {
	// 获取调度信息
	schedule, err := s.GetSchedule(ctx, scheduleID)
	if err != nil {
		return nil, err
	}

	// 合并参数和环境变量
	var scheduleParams map[string]interface{}
	if schedule.Parameters != nil {
		json.Unmarshal(schedule.Parameters, &scheduleParams)
	}
	if scheduleParams == nil {
		scheduleParams = make(map[string]interface{})
	}
	for k, v := range params.Parameters {
		scheduleParams[k] = v
	}

	var scheduleEnv map[string]string
	if schedule.Environment != nil {
		json.Unmarshal(schedule.Environment, &scheduleEnv)
	}
	if scheduleEnv == nil {
		scheduleEnv = make(map[string]string)
	}
	for k, v := range params.Environment {
		scheduleEnv[k] = v
	}

	// 创建执行请求
	execReq := &ExecutionRequest{
		AppID:       schedule.AppID,
		ScriptPath:  schedule.ScriptPath,
		Parameters:  scheduleParams,
		Environment: scheduleEnv,
		Timeout:     time.Duration(s.config.DefaultTimeout),
		Priority:    5, // 默认优先级
		UserID:      params.UserID,
		TenantID:    schedule.TenantID,
	}

	// 提交任务
	task, err := s.SubmitTask(ctx, execReq)
	if err != nil {
		return nil, fmt.Errorf("提交调度任务失败: %w", err)
	}

	// 更新调度的最后运行时间
	schedule.LastRun = &task.CreatedAt
	schedule.RunCount++
	s.db.Save(schedule)

	return task, nil
}

// ValidateCronExpression 验证Cron表达式
func (s *scriptExecutionService) ValidateCronExpression(cronExpr string) error {
	// TODO: 实现Cron表达式验证
	// 这里应该使用cron库来验证表达式的有效性
	if cronExpr == "" {
		return fmt.Errorf("Cron表达式不能为空")
	}
	// 简单验证：检查是否包含5个或6个字段
	fields := strings.Fields(cronExpr)
	if len(fields) < 5 || len(fields) > 6 {
		return fmt.Errorf("无效的Cron表达式格式")
	}
	return nil
}

// GetStatsOverview 获取统计概览
func (s *scriptExecutionService) GetStatsOverview(ctx context.Context, tenantID string) (*StatsOverviewResponse, error) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	yesterday := today.AddDate(0, 0, -1)
	thisWeekStart := today.AddDate(0, 0, -int(today.Weekday()))
	thisMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 获取今日统计
	todayStats, err := s.getTaskStatsForPeriod(ctx, tenantID, today, now)
	if err != nil {
		return nil, fmt.Errorf("获取今日统计失败: %w", err)
	}

	// 获取昨日统计
	yesterdayStats, err := s.getTaskStatsForPeriod(ctx, tenantID, yesterday, today)
	if err != nil {
		return nil, fmt.Errorf("获取昨日统计失败: %w", err)
	}

	// 获取本周统计
	thisWeekStats, err := s.getTaskStatsForPeriod(ctx, tenantID, thisWeekStart, now)
	if err != nil {
		return nil, fmt.Errorf("获取本周统计失败: %w", err)
	}

	// 获取本月统计
	thisMonthStats, err := s.getTaskStatsForPeriod(ctx, tenantID, thisMonthStart, now)
	if err != nil {
		return nil, fmt.Errorf("获取本月统计失败: %w", err)
	}

	return &StatsOverviewResponse{
		Today:     *todayStats,
		Yesterday: *yesterdayStats,
		ThisWeek:  *thisWeekStats,
		ThisMonth: *thisMonthStats,
	}, nil
}

// GetMetrics 获取指标数据
func (s *scriptExecutionService) GetMetrics(ctx context.Context, query *MetricsQuery) ([]MetricPoint, error) {
	// 解析时间范围
	endTime := time.Now()
	var startTime time.Time

	switch query.TimeRange {
	case "1h":
		startTime = endTime.Add(-1 * time.Hour)
	case "6h":
		startTime = endTime.Add(-6 * time.Hour)
	case "24h":
		startTime = endTime.Add(-24 * time.Hour)
	case "7d":
		startTime = endTime.AddDate(0, 0, -7)
	case "30d":
		startTime = endTime.AddDate(0, 0, -30)
	default:
		startTime = endTime.Add(-24 * time.Hour)
	}

	// 解析时间间隔
	var interval time.Duration
	switch query.Interval {
	case "5m":
		interval = 5 * time.Minute
	case "15m":
		interval = 15 * time.Minute
	case "1h":
		interval = 1 * time.Hour
	case "6h":
		interval = 6 * time.Hour
	case "1d":
		interval = 24 * time.Hour
	default:
		interval = 1 * time.Hour
	}

	var metrics []MetricPoint
	currentTime := startTime

	for currentTime.Before(endTime) {
		nextTime := currentTime.Add(interval)
		if nextTime.After(endTime) {
			nextTime = endTime
		}

		// 获取该时间段的统计数据
		stats, err := s.getTaskStatsForPeriod(ctx, query.TenantID, currentTime, nextTime)
		if err != nil {
			s.logger.Error("获取时间段统计失败", "error", err, "start", currentTime, "end", nextTime)
			// 继续处理下一个时间段
			currentTime = nextTime
			continue
		}

		// 获取资源使用情况
		avgCPU, avgMemory := s.getResourceUsageForPeriod(ctx, query.TenantID, currentTime, nextTime)

		metrics = append(metrics, MetricPoint{
			Timestamp:   currentTime,
			TaskCount:   stats.TotalTasks,
			SuccessRate: stats.SuccessRate,
			AvgDuration: stats.AvgDuration,
			CPUUsage:    avgCPU,
			MemoryUsage: avgMemory,
		})

		currentTime = nextTime
	}

	return metrics, nil
}

// getTaskArtifacts 获取任务产物
func (s *scriptExecutionService) getTaskArtifacts(ctx context.Context, taskID string) ([]Artifact, error) {
	var artifacts []TaskArtifact
	if err := s.db.Where("task_id = ?", taskID).Find(&artifacts).Error; err != nil {
		return nil, fmt.Errorf("查询任务产物失败: %w", err)
	}

	var result []Artifact
	for _, artifact := range artifacts {
		result = append(result, Artifact{
			Name:     artifact.Name,
			Path:     artifact.Path,
			Size:     artifact.Size,
			MimeType: artifact.MimeType,
			URL:      artifact.URL,
		})
	}

	return result, nil
}

// cleanupTaskArtifacts 清理任务产物
func (s *scriptExecutionService) cleanupTaskArtifacts(ctx context.Context, taskID string) error {
	// 删除产物记录
	if err := s.db.Where("task_id = ?", taskID).Delete(&TaskArtifact{}).Error; err != nil {
		return fmt.Errorf("删除产物记录失败: %w", err)
	}

	// TODO: 删除实际文件
	// 1. 获取产物文件路径
	// 2. 从存储服务删除文件

	return nil
}

// sendCallback 发送回调通知
func (s *scriptExecutionService) sendCallback(task *Task, result *container.ExecResult) {
	// TODO: 实现回调通知逻辑
	// 1. 构造回调数据
	// 2. 发送HTTP请求
	// 3. 处理重试逻辑

	s.logger.Info("发送回调通知", "task_id", task.ID, "callback", task.Callback)
}
