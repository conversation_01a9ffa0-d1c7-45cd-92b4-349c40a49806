package script

import (
	"time"
	"gorm.io/datatypes"
)

// Task 脚本执行任务
type Task struct {
	ID            string         `json:"id" gorm:"primaryKey"`
	AppID         string         `json:"app_id" gorm:"not null;index"`
	UserID        string         `json:"user_id" gorm:"not null;index"`
	TenantID      string         `json:"tenant_id" gorm:"not null;index"`
	ScriptPath    string         `json:"script_path" gorm:"not null"`
	RuntimeType   string         `json:"runtime_type" gorm:"not null;index"` // 运行时类型
	RuntimeConfig datatypes.JSON `json:"runtime_config"`                     // 运行时配置
	Parameters    datatypes.JSON `json:"parameters"`
	Environment   datatypes.JSON `json:"environment"`
	Status        TaskStatus     `json:"status" gorm:"not null;index"`
	Priority      int            `json:"priority" gorm:"default:5;index"`
	Timeout       int            `json:"timeout" gorm:"default:300"` // 秒
	ContainerID   string         `json:"container_id" gorm:"index"`
	ImageTag      string         `json:"image_tag"`
	StartTime     *time.Time     `json:"start_time"`
	EndTime       *time.Time     `json:"end_time"`
	Duration      int            `json:"duration"` // 秒
	ExitCode      *int           `json:"exit_code"`
	Output        string         `json:"output" gorm:"type:text"`
	Error         string         `json:"error" gorm:"type:text"`
	Resources     datatypes.JSON `json:"resources"`
	Callback      string         `json:"callback"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
}

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"   // 待执行
	TaskStatusQueued    TaskStatus = "queued"    // 排队中
	TaskStatusRunning   TaskStatus = "running"   // 运行中
	TaskStatusCompleted TaskStatus = "completed" // 已完成
	TaskStatusFailed    TaskStatus = "failed"    // 失败
	TaskStatusCanceled  TaskStatus = "canceled"  // 已取消
	TaskStatusTimeout   TaskStatus = "timeout"   // 超时
)

// TaskArtifact 任务产物
type TaskArtifact struct {
	ID        string    `json:"id" gorm:"primaryKey"`
	TaskID    string    `json:"task_id" gorm:"not null;index"`
	Name      string    `json:"name" gorm:"not null"`
	Path      string    `json:"path" gorm:"not null"`
	Size      int64     `json:"size"`
	MimeType  string    `json:"mime_type"`
	URL       string    `json:"url"`
	Hash      string    `json:"hash"`
	CreatedAt time.Time `json:"created_at"`
}

// TaskEvent 任务事件
type TaskEvent struct {
	ID        string          `json:"id" gorm:"primaryKey"`
	TaskID    string          `json:"task_id" gorm:"not null;index"`
	EventType TaskEventType   `json:"event_type" gorm:"not null"`
	Message   string          `json:"message" gorm:"type:text"`
	Data      datatypes.JSON  `json:"data"`
	Timestamp time.Time       `json:"timestamp" gorm:"not null;index"`
	CreatedAt time.Time       `json:"created_at"`
}

// TaskEventType 任务事件类型
type TaskEventType string

const (
	TaskEventCreated   TaskEventType = "created"
	TaskEventQueued    TaskEventType = "queued"
	TaskEventStarted   TaskEventType = "started"
	TaskEventProgress  TaskEventType = "progress"
	TaskEventCompleted TaskEventType = "completed"
	TaskEventFailed    TaskEventType = "failed"
	TaskEventCanceled  TaskEventType = "canceled"
	TaskEventTimeout   TaskEventType = "timeout"
)

// TaskQueue 任务队列
type TaskQueue struct {
	ID        string           `json:"id" gorm:"primaryKey"`
	TaskID    string           `json:"task_id" gorm:"not null;uniqueIndex"`
	Priority  int              `json:"priority" gorm:"not null;index"`
	Status    TaskQueueStatus  `json:"status" gorm:"not null;index"`
	NodeID    string           `json:"node_id" gorm:"index"`
	CreatedAt time.Time        `json:"created_at"`
	UpdatedAt time.Time        `json:"updated_at"`
}

// TaskQueueStatus 任务队列状态
type TaskQueueStatus string

const (
	TaskQueueStatusPending   TaskQueueStatus = "pending"
	TaskQueueStatusAssigned  TaskQueueStatus = "assigned"
	TaskQueueStatusProcessing TaskQueueStatus = "processing"
	TaskQueueStatusCompleted TaskQueueStatus = "completed"
	TaskQueueStatusFailed    TaskQueueStatus = "failed"
	TaskQueueStatusCanceled  TaskQueueStatus = "canceled"
)

// ExecutionNode 执行节点
type ExecutionNode struct {
	ID            string              `json:"id" gorm:"primaryKey"`
	Name          string              `json:"name" gorm:"not null;uniqueIndex"`
	Address       string              `json:"address" gorm:"not null"`
	Status        ExecutionNodeStatus `json:"status" gorm:"not null;index"`
	Capacity      int                 `json:"capacity" gorm:"default:10"`
	CurrentLoad   int                 `json:"current_load" gorm:"default:0"`
	Resources     datatypes.JSON      `json:"resources"`
	Labels        datatypes.JSON      `json:"labels"`
	LastHeartbeat time.Time           `json:"last_heartbeat"`
	CreatedAt     time.Time           `json:"created_at"`
	UpdatedAt     time.Time           `json:"updated_at"`
}

// ExecutionNodeStatus 执行节点状态
type ExecutionNodeStatus string

const (
	ExecutionNodeStatusOnline  ExecutionNodeStatus = "online"
	ExecutionNodeStatusOffline ExecutionNodeStatus = "offline"
	ExecutionNodeStatusBusy    ExecutionNodeStatus = "busy"
	ExecutionNodeStatusError   ExecutionNodeStatus = "error"
)

// ScriptTemplate 脚本模板
type ScriptTemplate struct {
	ID          string                  `json:"id" gorm:"primaryKey"`
	Name        string                  `json:"name" gorm:"not null"`
	Description string                  `json:"description" gorm:"type:text"`
	Category    string                  `json:"category" gorm:"index"`
	Language    string                  `json:"language" gorm:"not null"`
	Version     string                  `json:"version" gorm:"not null"`
	Content     string                  `json:"content" gorm:"type:text"`
	Parameters  datatypes.JSON          `json:"parameters"`
	Environment datatypes.JSON          `json:"environment"`
	Resources   datatypes.JSON          `json:"resources"`
	Tags        datatypes.JSON          `json:"tags"`
	Status      ScriptTemplateStatus    `json:"status" gorm:"not null"`
	TenantID    string                  `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string                  `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time               `json:"created_at"`
	UpdatedAt   time.Time               `json:"updated_at"`
}

// ScriptTemplateStatus 脚本模板状态
type ScriptTemplateStatus string

const (
	ScriptTemplateStatusActive   ScriptTemplateStatus = "active"
	ScriptTemplateStatusInactive ScriptTemplateStatus = "inactive"
	ScriptTemplateStatusDraft    ScriptTemplateStatus = "draft"
)

// ExecutionMetrics 执行指标
type ExecutionMetrics struct {
	ID              string    `json:"id" gorm:"primaryKey"`
	TaskID          string    `json:"task_id" gorm:"not null;index"`
	Timestamp       time.Time `json:"timestamp" gorm:"not null;index"`
	CPUUsage        float64   `json:"cpu_usage"`        // CPU使用率 (%)
	MemoryUsage     int64     `json:"memory_usage"`     // 内存使用量 (字节)
	MemoryLimit     int64     `json:"memory_limit"`     // 内存限制 (字节)
	DiskUsage       int64     `json:"disk_usage"`       // 磁盘使用量 (字节)
	NetworkRx       int64     `json:"network_rx"`       // 网络接收字节
	NetworkTx       int64     `json:"network_tx"`       // 网络发送字节
	ProcessCount    int       `json:"process_count"`    // 进程数量
	FileDescCount   int       `json:"file_desc_count"`  // 文件描述符数量
	ExecutionTime   int64     `json:"execution_time"`   // 执行时间 (毫秒)
	CreatedAt       time.Time `json:"created_at"`
}

// TaskSchedule 任务调度
type TaskSchedule struct {
	ID          string              `json:"id" gorm:"primaryKey"`
	Name        string              `json:"name" gorm:"not null"`
	Description string              `json:"description" gorm:"type:text"`
	AppID       string              `json:"app_id" gorm:"not null;index"`
	ScriptPath  string              `json:"script_path" gorm:"not null"`
	Parameters  datatypes.JSON      `json:"parameters"`
	Environment datatypes.JSON      `json:"environment"`
	CronExpr    string              `json:"cron_expr" gorm:"not null"`
	Timezone    string              `json:"timezone" gorm:"default:'UTC'"`
	Status      TaskScheduleStatus  `json:"status" gorm:"not null;index"`
	NextRun     *time.Time          `json:"next_run"`
	LastRun     *time.Time          `json:"last_run"`
	RunCount    int                 `json:"run_count" gorm:"default:0"`
	FailCount   int                 `json:"fail_count" gorm:"default:0"`
	TenantID    string              `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string              `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
}

// TaskScheduleStatus 任务调度状态
type TaskScheduleStatus string

const (
	TaskScheduleStatusActive   TaskScheduleStatus = "active"
	TaskScheduleStatusInactive TaskScheduleStatus = "inactive"
	TaskScheduleStatusPaused   TaskScheduleStatus = "paused"
	TaskScheduleStatusError    TaskScheduleStatus = "error"
)

// TaskCallback 任务回调
type TaskCallback struct {
	ID          string              `json:"id" gorm:"primaryKey"`
	TaskID      string              `json:"task_id" gorm:"not null;index"`
	URL         string              `json:"url" gorm:"not null"`
	Method      string              `json:"method" gorm:"default:'POST'"`
	Headers     datatypes.JSON      `json:"headers"`
	Body        string              `json:"body" gorm:"type:text"`
	Status      TaskCallbackStatus  `json:"status" gorm:"not null;index"`
	RetryCount  int                 `json:"retry_count" gorm:"default:0"`
	MaxRetries  int                 `json:"max_retries" gorm:"default:3"`
	NextRetry   *time.Time          `json:"next_retry"`
	Response    string              `json:"response" gorm:"type:text"`
	Error       string              `json:"error" gorm:"type:text"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
}

// TaskCallbackStatus 任务回调状态
type TaskCallbackStatus string

const (
	TaskCallbackStatusPending   TaskCallbackStatus = "pending"
	TaskCallbackStatusSuccess   TaskCallbackStatus = "success"
	TaskCallbackStatusFailed    TaskCallbackStatus = "failed"
	TaskCallbackStatusRetrying  TaskCallbackStatus = "retrying"
	TaskCallbackStatusAbandoned TaskCallbackStatus = "abandoned"
)

// TaskStatistics 任务统计
type TaskStatistics struct {
	ID              string    `json:"id" gorm:"primaryKey"`
	Date            time.Time `json:"date" gorm:"not null;uniqueIndex:idx_stats_date_tenant"`
	TenantID        string    `json:"tenant_id" gorm:"not null;uniqueIndex:idx_stats_date_tenant"`
	TotalTasks      int       `json:"total_tasks" gorm:"default:0"`
	CompletedTasks  int       `json:"completed_tasks" gorm:"default:0"`
	FailedTasks     int       `json:"failed_tasks" gorm:"default:0"`
	CanceledTasks   int       `json:"canceled_tasks" gorm:"default:0"`
	AvgDuration     float64   `json:"avg_duration"`     // 平均执行时间 (秒)
	TotalDuration   int64     `json:"total_duration"`   // 总执行时间 (秒)
	AvgCPUUsage     float64   `json:"avg_cpu_usage"`    // 平均CPU使用率
	AvgMemoryUsage  int64     `json:"avg_memory_usage"` // 平均内存使用量
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Task) TableName() string {
	return "script_tasks"
}

func (TaskArtifact) TableName() string {
	return "script_task_artifacts"
}

func (TaskEvent) TableName() string {
	return "script_task_events"
}

func (TaskQueue) TableName() string {
	return "script_task_queue"
}

func (ExecutionNode) TableName() string {
	return "script_execution_nodes"
}

func (ScriptTemplate) TableName() string {
	return "script_templates"
}

func (ExecutionMetrics) TableName() string {
	return "script_execution_metrics"
}

func (TaskSchedule) TableName() string {
	return "script_task_schedules"
}

func (TaskCallback) TableName() string {
	return "script_task_callbacks"
}

func (TaskStatistics) TableName() string {
	return "script_task_statistics"
}
