package script

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Handler 脚本执行HTTP处理器
type Handler struct {
	service ScriptExecutionService
	logger  logger.Logger
}

// NewHandler 创建脚本执行处理器
func NewHandler(service ScriptExecutionService, logger logger.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 脚本执行相关路由
	scripts := router.Group("/scripts")
	{
		scripts.POST("/execute", h.ExecuteScript)           // 执行脚本
		scripts.GET("/tasks", h.ListTasks)                  // 获取任务列表
		scripts.GET("/tasks/:id", h.GetTaskStatus)          // 获取任务状态
		scripts.GET("/tasks/:id/result", h.GetTaskResult)   // 获取任务结果
		scripts.GET("/tasks/:id/logs", h.GetTaskLogs)       // 获取任务日志
		scripts.DELETE("/tasks/:id", h.CancelTask)          // 取消任务
		scripts.GET("/tasks/:id/artifacts/:name", h.DownloadArtifact) // 下载产物
	}

	// 脚本模板相关路由
	templates := router.Group("/script-templates")
	{
		templates.POST("", h.CreateTemplate)                // 创建模板
		templates.GET("", h.ListTemplates)                  // 获取模板列表
		templates.GET("/:id", h.GetTemplate)                // 获取模板详情
		templates.PUT("/:id", h.UpdateTemplate)             // 更新模板
		templates.DELETE("/:id", h.DeleteTemplate)          // 删除模板
	}

	// 任务调度相关路由
	schedules := router.Group("/script-schedules")
	{
		schedules.POST("", h.CreateSchedule)                // 创建调度
		schedules.GET("", h.ListSchedules)                  // 获取调度列表
		schedules.GET("/:id", h.GetSchedule)                // 获取调度详情
		schedules.PUT("/:id", h.UpdateSchedule)             // 更新调度
		schedules.DELETE("/:id", h.DeleteSchedule)          // 删除调度
		schedules.POST("/:id/trigger", h.TriggerSchedule)   // 手动触发调度
	}

	// 统计相关路由
	stats := router.Group("/script-stats")
	{
		stats.GET("/overview", h.GetStatsOverview)          // 获取统计概览
		stats.GET("/metrics", h.GetMetrics)                 // 获取指标数据
	}
}

// ExecuteScript 执行脚本
func (h *Handler) ExecuteScript(c *gin.Context) {
	var req ExecuteScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 构建执行请求
	execReq := &ExecutionRequest{
		AppID:       req.AppID,
		ScriptPath:  req.ScriptPath,
		Parameters:  req.Parameters,
		Environment: req.Environment,
		Timeout:     time.Duration(req.Timeout) * time.Second,
		Priority:    req.Priority,
		Callback:    req.Callback,
		UserID:      userID.(string),
		TenantID:    tenantID.(string),
	}

	// 提交执行任务
	task, err := h.service.SubmitTask(c.Request.Context(), execReq)
	if err != nil {
		h.logger.Error("提交脚本执行任务失败", "error", err, "app_id", req.AppID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "SUBMIT_TASK_FAILED",
			Message: "提交执行任务失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("脚本执行任务已提交", "task_id", task.ID, "app_id", req.AppID)
	c.JSON(http.StatusCreated, ExecuteScriptResponse{
		TaskID:    task.ID,
		Status:    string(task.Status),
		Message:   "任务已提交",
		CreatedAt: task.CreatedAt,
	})
}

// GetTaskStatus 获取任务状态
func (h *Handler) GetTaskStatus(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	status, err := h.service.GetTaskStatus(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("获取任务状态失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "TASK_NOT_FOUND",
			Message: "任务不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, status)
}

// GetTaskResult 获取任务结果
func (h *Handler) GetTaskResult(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	result, err := h.service.GetTaskResult(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("获取任务结果失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "TASK_NOT_FOUND",
			Message: "任务不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ListTasks 获取任务列表
func (h *Handler) ListTasks(c *gin.Context) {
	// 解析查询参数
	filter := &TaskFilter{
		AppID:    c.Query("app_id"),
		Status:   TaskStatus(c.Query("status")),
		Page:     1,
		PageSize: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	// 从上下文获取租户信息
	tenantID, _ := c.Get("tenant_id")
	filter.TenantID = tenantID.(string)

	// 查询任务列表
	tasks, total, err := h.service.ListTasks(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取任务列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_TASKS_FAILED",
			Message: "获取任务列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ListTasksResponse{
		Tasks: tasks,
		Pagination: PaginationResponse{
			Page:     filter.Page,
			PageSize: filter.PageSize,
			Total:    total,
		},
	})
}

// GetTaskLogs 获取任务日志
func (h *Handler) GetTaskLogs(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	logs, err := h.service.GetExecutionLogs(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("获取任务日志失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_LOGS_FAILED",
			Message: "获取任务日志失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, GetTaskLogsResponse{
		TaskID: taskID,
		Logs:   logs,
	})
}

// CancelTask 取消任务
func (h *Handler) CancelTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	err := h.service.CancelTask(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("取消任务失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CANCEL_TASK_FAILED",
			Message: "取消任务失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("任务已取消", "task_id", taskID)
	c.JSON(http.StatusOK, gin.H{
		"message": "任务已取消",
		"task_id": taskID,
	})
}

// DownloadArtifact 下载产物
func (h *Handler) DownloadArtifact(c *gin.Context) {
	taskID := c.Param("id")
	artifactName := c.Param("name")

	if taskID == "" || artifactName == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "任务ID和产物名称不能为空",
		})
		return
	}

	// 从上下文获取租户信息进行权限验证
	tenantID, _ := c.Get("tenant_id")
	userID, _ := c.Get("user_id")

	// 验证任务权限
	if err := h.service.ValidateTaskAccess(c.Request.Context(), taskID, tenantID.(string)); err != nil {
		h.logger.Warn("用户尝试访问其他租户的任务产物",
			"user_id", userID, "tenant_id", tenantID, "task_id", taskID, "error", err)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权访问该任务的产物",
		})
		return
	}

	// 获取产物信息
	artifact, err := h.service.GetTaskArtifact(c.Request.Context(), taskID, artifactName)
	if err != nil {
		h.logger.Error("获取任务产物失败", "error", err, "task_id", taskID, "artifact", artifactName)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "ARTIFACT_NOT_FOUND",
			Message: "产物不存在",
			Details: err.Error(),
		})
		return
	}

	// 检查文件是否存在
	if !h.service.ArtifactExists(c.Request.Context(), artifact.Path) {
		h.logger.Error("产物文件不存在", "path", artifact.Path, "task_id", taskID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "FILE_NOT_FOUND",
			Message: "产物文件不存在",
		})
		return
	}

	// 设置响应头
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", artifactName))
	c.Header("Content-Type", artifact.MimeType)
	c.Header("Content-Length", fmt.Sprintf("%d", artifact.Size))

	// 返回文件流
	h.logger.Info("开始下载产物", "task_id", taskID, "artifact", artifactName, "user_id", userID)
	c.File(artifact.Path)
}

// CreateTemplate 创建脚本模板
func (h *Handler) CreateTemplate(c *gin.Context) {
	var req CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 创建模板
	template, err := h.service.CreateTemplate(c.Request.Context(), &CreateTemplateParams{
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		Language:    req.Language,
		Version:     req.Version,
		Content:     req.Content,
		Parameters:  req.Parameters,
		Environment: req.Environment,
		Resources:   req.Resources,
		Tags:        req.Tags,
		TenantID:    tenantID.(string),
		CreatedBy:   userID.(string),
	})
	if err != nil {
		h.logger.Error("创建脚本模板失败", "error", err, "name", req.Name)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_TEMPLATE_FAILED",
			Message: "创建脚本模板失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("脚本模板创建成功", "template_id", template.ID, "name", req.Name, "user_id", userID)
	c.JSON(http.StatusCreated, CreateTemplateResponse{
		ID:        template.ID,
		Name:      template.Name,
		Version:   template.Version,
		Status:    string(template.Status),
		CreatedAt: template.CreatedAt,
	})
}

// ListTemplates 获取模板列表
func (h *Handler) ListTemplates(c *gin.Context) {
	// 解析查询参数
	filter := &TemplateFilter{
		Category: c.Query("category"),
		Language: c.Query("language"),
		Status:   ScriptTemplateStatus(c.Query("status")),
		Page:     1,
		PageSize: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	// 从上下文获取租户信息
	tenantID, _ := c.Get("tenant_id")
	filter.TenantID = tenantID.(string)

	// 查询模板列表
	templates, total, err := h.service.ListTemplates(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取模板列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_TEMPLATES_FAILED",
			Message: "获取模板列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ListTemplatesResponse{
		Templates: templates,
		Pagination: PaginationResponse{
			Page:     filter.Page,
			PageSize: filter.PageSize,
			Total:    total,
		},
	})
}

// GetTemplate 获取模板详情
func (h *Handler) GetTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TEMPLATE_ID",
			Message: "模板ID不能为空",
		})
		return
	}

	// 从上下文获取租户信息进行权限验证
	tenantID, _ := c.Get("tenant_id")

	template, err := h.service.GetTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.logger.Error("获取模板详情失败", "error", err, "template_id", templateID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "TEMPLATE_NOT_FOUND",
			Message: "模板不存在",
			Details: err.Error(),
		})
		return
	}

	// 验证权限：检查模板是否属于当前租户
	if template.TenantID != tenantID.(string) {
		h.logger.Warn("用户尝试访问其他租户的模板",
			"tenant_id", tenantID, "template_id", templateID)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权访问该模板",
		})
		return
	}

	c.JSON(http.StatusOK, template)
}

// UpdateTemplate 更新模板
func (h *Handler) UpdateTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TEMPLATE_ID",
			Message: "模板ID不能为空",
		})
		return
	}

	var req UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 验证模板存在和权限
	existingTemplate, err := h.service.GetTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.logger.Error("获取模板失败", "error", err, "template_id", templateID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "TEMPLATE_NOT_FOUND",
			Message: "模板不存在",
			Details: err.Error(),
		})
		return
	}

	// 验证权限：检查模板是否属于当前租户
	if existingTemplate.TenantID != tenantID.(string) {
		h.logger.Warn("用户尝试更新其他租户的模板",
			"user_id", userID, "tenant_id", tenantID, "template_id", templateID)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权更新该模板",
		})
		return
	}

	// 更新模板
	template, err := h.service.UpdateTemplate(c.Request.Context(), templateID, &UpdateTemplateParams{
		Name:        req.Name,
		Description: req.Description,
		Content:     req.Content,
		Parameters:  req.Parameters,
		Environment: req.Environment,
		Resources:   req.Resources,
		Tags:        req.Tags,
		Status:      ScriptTemplateStatus(req.Status),
	})
	if err != nil {
		h.logger.Error("更新模板失败", "error", err, "template_id", templateID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_TEMPLATE_FAILED",
			Message: "更新模板失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("模板更新成功", "template_id", templateID, "user_id", userID)
	c.JSON(http.StatusOK, template)
}

// DeleteTemplate 删除模板
func (h *Handler) DeleteTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TEMPLATE_ID",
			Message: "模板ID不能为空",
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 验证模板存在和权限
	existingTemplate, err := h.service.GetTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.logger.Error("获取模板失败", "error", err, "template_id", templateID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "TEMPLATE_NOT_FOUND",
			Message: "模板不存在",
			Details: err.Error(),
		})
		return
	}

	// 验证权限：检查模板是否属于当前租户
	if existingTemplate.TenantID != tenantID.(string) {
		h.logger.Warn("用户尝试删除其他租户的模板",
			"user_id", userID, "tenant_id", tenantID, "template_id", templateID)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权删除该模板",
		})
		return
	}

	// 删除模板
	err = h.service.DeleteTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.logger.Error("删除模板失败", "error", err, "template_id", templateID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DELETE_TEMPLATE_FAILED",
			Message: "删除模板失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("模板删除成功", "template_id", templateID, "user_id", userID)
	c.JSON(http.StatusOK, gin.H{
		"message":     "模板已删除",
		"template_id": templateID,
	})
}

// CreateSchedule 创建任务调度
func (h *Handler) CreateSchedule(c *gin.Context) {
	var req CreateScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 验证 Cron 表达式
	if err := h.service.ValidateCronExpression(req.CronExpr); err != nil {
		h.logger.Error("无效的Cron表达式", "error", err, "cron", req.CronExpr)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_CRON_EXPRESSION",
			Message: "无效的Cron表达式",
			Details: err.Error(),
		})
		return
	}

	// 创建调度
	schedule, err := h.service.CreateSchedule(c.Request.Context(), &CreateScheduleParams{
		Name:        req.Name,
		Description: req.Description,
		AppID:       req.AppID,
		ScriptPath:  req.ScriptPath,
		Parameters:  req.Parameters,
		Environment: req.Environment,
		CronExpr:    req.CronExpr,
		Timezone:    req.Timezone,
		TenantID:    tenantID.(string),
		CreatedBy:   userID.(string),
	})
	if err != nil {
		h.logger.Error("创建任务调度失败", "error", err, "name", req.Name)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_SCHEDULE_FAILED",
			Message: "创建任务调度失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("任务调度创建成功", "schedule_id", schedule.ID, "name", req.Name, "user_id", userID)
	c.JSON(http.StatusCreated, CreateScheduleResponse{
		ID:        schedule.ID,
		Name:      schedule.Name,
		Status:    string(schedule.Status),
		NextRun:   schedule.NextRun,
		CreatedAt: schedule.CreatedAt,
	})
}

// ListSchedules 获取调度列表
func (h *Handler) ListSchedules(c *gin.Context) {
	// 解析查询参数
	filter := &ScheduleFilter{
		AppID:  c.Query("app_id"),
		Status: TaskScheduleStatus(c.Query("status")),
		Page:   1,
		PageSize: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	// 从上下文获取租户信息
	tenantID, _ := c.Get("tenant_id")
	filter.TenantID = tenantID.(string)

	// 查询调度列表
	schedules, total, err := h.service.ListSchedules(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取调度列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_SCHEDULES_FAILED",
			Message: "获取调度列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ListSchedulesResponse{
		Schedules: schedules,
		Pagination: PaginationResponse{
			Page:     filter.Page,
			PageSize: filter.PageSize,
			Total:    total,
		},
	})
}

// GetSchedule 获取调度详情
func (h *Handler) GetSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	if scheduleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_SCHEDULE_ID",
			Message: "调度ID不能为空",
		})
		return
	}

	// 从上下文获取租户信息进行权限验证
	tenantID, _ := c.Get("tenant_id")

	schedule, err := h.service.GetSchedule(c.Request.Context(), scheduleID)
	if err != nil {
		h.logger.Error("获取调度详情失败", "error", err, "schedule_id", scheduleID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "SCHEDULE_NOT_FOUND",
			Message: "调度不存在",
			Details: err.Error(),
		})
		return
	}

	// 验证权限：检查调度是否属于当前租户
	if schedule.TenantID != tenantID.(string) {
		h.logger.Warn("用户尝试访问其他租户的调度",
			"tenant_id", tenantID, "schedule_id", scheduleID)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权访问该调度",
		})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// UpdateSchedule 更新调度
func (h *Handler) UpdateSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	if scheduleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_SCHEDULE_ID",
			Message: "调度ID不能为空",
		})
		return
	}

	var req UpdateScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 验证调度存在和权限
	existingSchedule, err := h.service.GetSchedule(c.Request.Context(), scheduleID)
	if err != nil {
		h.logger.Error("获取调度失败", "error", err, "schedule_id", scheduleID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "SCHEDULE_NOT_FOUND",
			Message: "调度不存在",
			Details: err.Error(),
		})
		return
	}

	// 验证权限：检查调度是否属于当前租户
	if existingSchedule.TenantID != tenantID.(string) {
		h.logger.Warn("用户尝试更新其他租户的调度",
			"user_id", userID, "tenant_id", tenantID, "schedule_id", scheduleID)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权更新该调度",
		})
		return
	}

	// 验证 Cron 表达式（如果提供）
	if req.CronExpr != "" {
		if err := h.service.ValidateCronExpression(req.CronExpr); err != nil {
			h.logger.Error("无效的Cron表达式", "error", err, "cron", req.CronExpr)
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Code:    "INVALID_CRON_EXPRESSION",
				Message: "无效的Cron表达式",
				Details: err.Error(),
			})
			return
		}
	}

	// 更新调度
	schedule, err := h.service.UpdateSchedule(c.Request.Context(), scheduleID, &UpdateScheduleParams{
		Name:        req.Name,
		Description: req.Description,
		Parameters:  req.Parameters,
		Environment: req.Environment,
		CronExpr:    req.CronExpr,
		Timezone:    req.Timezone,
		Status:      TaskScheduleStatus(req.Status),
	})
	if err != nil {
		h.logger.Error("更新调度失败", "error", err, "schedule_id", scheduleID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_SCHEDULE_FAILED",
			Message: "更新调度失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("调度更新成功", "schedule_id", scheduleID, "user_id", userID)
	c.JSON(http.StatusOK, schedule)
}

// DeleteSchedule 删除调度
func (h *Handler) DeleteSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	if scheduleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_SCHEDULE_ID",
			Message: "调度ID不能为空",
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 验证调度存在和权限
	existingSchedule, err := h.service.GetSchedule(c.Request.Context(), scheduleID)
	if err != nil {
		h.logger.Error("获取调度失败", "error", err, "schedule_id", scheduleID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "SCHEDULE_NOT_FOUND",
			Message: "调度不存在",
			Details: err.Error(),
		})
		return
	}

	// 验证权限：检查调度是否属于当前租户
	if existingSchedule.TenantID != tenantID.(string) {
		h.logger.Warn("用户尝试删除其他租户的调度",
			"user_id", userID, "tenant_id", tenantID, "schedule_id", scheduleID)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权删除该调度",
		})
		return
	}

	// 删除调度
	err = h.service.DeleteSchedule(c.Request.Context(), scheduleID)
	if err != nil {
		h.logger.Error("删除调度失败", "error", err, "schedule_id", scheduleID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DELETE_SCHEDULE_FAILED",
			Message: "删除调度失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("调度删除成功", "schedule_id", scheduleID, "user_id", userID)
	c.JSON(http.StatusOK, gin.H{
		"message":     "调度已删除",
		"schedule_id": scheduleID,
	})
}

// TriggerSchedule 手动触发调度
func (h *Handler) TriggerSchedule(c *gin.Context) {
	scheduleID := c.Param("id")
	if scheduleID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_SCHEDULE_ID",
			Message: "调度ID不能为空",
		})
		return
	}

	var req TriggerScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 验证调度存在和权限
	schedule, err := h.service.GetSchedule(c.Request.Context(), scheduleID)
	if err != nil {
		h.logger.Error("获取调度失败", "error", err, "schedule_id", scheduleID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "SCHEDULE_NOT_FOUND",
			Message: "调度不存在",
			Details: err.Error(),
		})
		return
	}

	// 验证权限：检查调度是否属于当前租户
	if schedule.TenantID != tenantID.(string) {
		h.logger.Warn("用户尝试触发其他租户的调度",
			"user_id", userID, "tenant_id", tenantID, "schedule_id", scheduleID)
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:    "ACCESS_DENIED",
			Message: "无权触发该调度",
		})
		return
	}

	// 手动触发调度
	task, err := h.service.TriggerSchedule(c.Request.Context(), scheduleID, &TriggerScheduleParams{
		Parameters:  req.Parameters,
		Environment: req.Environment,
		UserID:      userID.(string),
	})
	if err != nil {
		h.logger.Error("触发调度失败", "error", err, "schedule_id", scheduleID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "TRIGGER_SCHEDULE_FAILED",
			Message: "触发调度失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("调度手动触发成功", "schedule_id", scheduleID, "task_id", task.ID, "user_id", userID)
	c.JSON(http.StatusOK, TriggerScheduleResponse{
		TaskID:      task.ID,
		ScheduleID:  scheduleID,
		Status:      string(task.Status),
		TriggeredAt: task.CreatedAt,
	})
}

// GetStatsOverview 获取统计概览
func (h *Handler) GetStatsOverview(c *gin.Context) {
	// 从上下文获取租户信息
	tenantID, _ := c.Get("tenant_id")

	// 获取统计概览数据
	overview, err := h.service.GetStatsOverview(c.Request.Context(), tenantID.(string))
	if err != nil {
		h.logger.Error("获取统计概览失败", "error", err, "tenant_id", tenantID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_STATS_FAILED",
			Message: "获取统计概览失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, overview)
}

// GetMetrics 获取指标数据
func (h *Handler) GetMetrics(c *gin.Context) {
	// 解析查询参数
	timeRange := c.DefaultQuery("time_range", "24h")  // 24h, 7d, 30d
	interval := c.DefaultQuery("interval", "1h")      // 1h, 6h, 1d

	// 验证时间范围参数
	validTimeRanges := map[string]bool{
		"1h": true, "6h": true, "24h": true, "7d": true, "30d": true,
	}
	if !validTimeRanges[timeRange] {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TIME_RANGE",
			Message: "无效的时间范围，支持: 1h, 6h, 24h, 7d, 30d",
		})
		return
	}

	// 验证间隔参数
	validIntervals := map[string]bool{
		"5m": true, "15m": true, "1h": true, "6h": true, "1d": true,
	}
	if !validIntervals[interval] {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_INTERVAL",
			Message: "无效的时间间隔，支持: 5m, 15m, 1h, 6h, 1d",
		})
		return
	}

	// 从上下文获取租户信息
	tenantID, _ := c.Get("tenant_id")

	// 获取指标数据
	metrics, err := h.service.GetMetrics(c.Request.Context(), &MetricsQuery{
		TenantID:  tenantID.(string),
		TimeRange: timeRange,
		Interval:  interval,
	})
	if err != nil {
		h.logger.Error("获取指标数据失败", "error", err, "tenant_id", tenantID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_METRICS_FAILED",
			Message: "获取指标数据失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, MetricsResponse{
		TimeRange: timeRange,
		Interval:  interval,
		Metrics:   metrics,
	})
}
