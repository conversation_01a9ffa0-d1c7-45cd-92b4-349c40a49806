package tenant

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gorm.io/gorm"
	"paas-platform/pkg/logger"
)

// TenantQuotaManager 租户配额管理器
type TenantQuotaManager struct {
	db           *gorm.DB
	logger       logger.Logger
	quotas       map[string]*TenantQuota
	usage        map[string]*ResourceUsage
	mutex        sync.RWMutex
	usageTracker *UsageTracker
	alertManager *QuotaAlertManager
}

// TenantQuota 租户配额定义
type TenantQuota struct {
	ID           string    `json:"id" gorm:"primaryKey"`
	TenantID     string    `json:"tenant_id" gorm:"uniqueIndex;not null"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	
	// 计算资源配额
	CPULimit     string    `json:"cpu_limit"`     // "4 cores" 或 "4000m"
	MemoryLimit  string    `json:"memory_limit"`  // "8Gi" 或 "8192Mi"
	StorageLimit string    `json:"storage_limit"` // "100Gi"
	
	// 应用和用户配额
	AppLimit     int       `json:"app_limit"`     // 最大应用数
	UserLimit    int       `json:"user_limit"`    // 最大用户数
	
	// 网络配额
	BandwidthLimit string  `json:"bandwidth_limit"` // "1Gbps"
	ConnectionLimit int    `json:"connection_limit"` // 最大连接数
	
	// 数据库配额
	DatabaseLimit int      `json:"database_limit"` // 最大数据库数
	BackupLimit   int      `json:"backup_limit"`   // 最大备份数
	
	// 时间配额
	BuildTimeLimit   time.Duration `json:"build_time_limit"`   // 构建时间限制
	ExecutionTimeLimit time.Duration `json:"execution_time_limit"` // 执行时间限制
	
	// 配额状态
	Status       string    `json:"status"` // active, suspended, expired
	EffectiveAt  time.Time `json:"effective_at"`
	ExpiresAt    *time.Time `json:"expires_at"`
	
	// 审计信息
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	CreatedBy    string    `json:"created_by"`
	UpdatedBy    string    `json:"updated_by"`
	
	// 扩展配置
	CustomLimits map[string]interface{} `json:"custom_limits" gorm:"type:jsonb"`
	Tags         map[string]string      `json:"tags" gorm:"type:jsonb"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	ID         string    `json:"id" gorm:"primaryKey"`
	TenantID   string    `json:"tenant_id" gorm:"index;not null"`
	
	// 计算资源使用
	CPUUsed     float64   `json:"cpu_used"`     // 已使用CPU（核心数）
	MemoryUsed  int64     `json:"memory_used"`  // 已使用内存（字节）
	StorageUsed int64     `json:"storage_used"` // 已使用存储（字节）
	
	// 应用和用户使用
	AppCount    int       `json:"app_count"`    // 当前应用数
	UserCount   int       `json:"user_count"`   // 当前用户数
	
	// 网络使用
	BandwidthUsed   float64 `json:"bandwidth_used"`   // 已使用带宽
	ConnectionCount int     `json:"connection_count"` // 当前连接数
	
	// 数据库使用
	DatabaseCount int     `json:"database_count"` // 当前数据库数
	BackupCount   int     `json:"backup_count"`   // 当前备份数
	
	// 时间使用
	BuildTimeUsed     time.Duration `json:"build_time_used"`     // 已使用构建时间
	ExecutionTimeUsed time.Duration `json:"execution_time_used"` // 已使用执行时间
	
	// 使用率计算
	CPUUtilization     float64 `json:"cpu_utilization"`     // CPU使用率
	MemoryUtilization  float64 `json:"memory_utilization"`  // 内存使用率
	StorageUtilization float64 `json:"storage_utilization"` // 存储使用率
	
	// 时间信息
	LastUpdated time.Time `json:"last_updated"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	// 扩展使用情况
	CustomUsage map[string]interface{} `json:"custom_usage" gorm:"type:jsonb"`
}

// QuotaViolation 配额违规记录
type QuotaViolation struct {
	ID           string    `json:"id" gorm:"primaryKey"`
	TenantID     string    `json:"tenant_id" gorm:"index;not null"`
	ResourceType string    `json:"resource_type"` // cpu, memory, storage, app_count, etc.
	ViolationType string   `json:"violation_type"` // exceeded, approaching
	
	// 违规详情
	LimitValue   float64   `json:"limit_value"`
	CurrentValue float64   `json:"current_value"`
	Percentage   float64   `json:"percentage"` // 使用率百分比
	
	// 处理状态
	Status       string    `json:"status"` // active, resolved, ignored
	Action       string    `json:"action"` // alert, throttle, suspend
	
	// 时间信息
	DetectedAt   time.Time `json:"detected_at"`
	ResolvedAt   *time.Time `json:"resolved_at"`
	
	// 通知信息
	NotificationSent bool   `json:"notification_sent"`
	AlertLevel       string `json:"alert_level"` // info, warning, critical
	
	// 扩展信息
	Details      map[string]interface{} `json:"details" gorm:"type:jsonb"`
}

// UsageTracker 使用情况追踪器
type UsageTracker struct {
	db     *gorm.DB
	logger logger.Logger
	mutex  sync.RWMutex
}

// QuotaAlertManager 配额告警管理器
type QuotaAlertManager struct {
	logger       logger.Logger
	thresholds   map[string]float64 // 告警阈值
	notifier     NotificationService
}

// NotificationService 通知服务接口
type NotificationService interface {
	SendQuotaAlert(ctx context.Context, alert *QuotaAlert) error
}

// QuotaAlert 配额告警
type QuotaAlert struct {
	TenantID     string    `json:"tenant_id"`
	ResourceType string    `json:"resource_type"`
	AlertLevel   string    `json:"alert_level"`
	Message      string    `json:"message"`
	CurrentUsage float64   `json:"current_usage"`
	Limit        float64   `json:"limit"`
	Percentage   float64   `json:"percentage"`
	Timestamp    time.Time `json:"timestamp"`
}

// NewTenantQuotaManager 创建租户配额管理器
func NewTenantQuotaManager(db *gorm.DB, logger logger.Logger) *TenantQuotaManager {
	manager := &TenantQuotaManager{
		db:           db,
		logger:       logger,
		quotas:       make(map[string]*TenantQuota),
		usage:        make(map[string]*ResourceUsage),
		usageTracker: NewUsageTracker(db, logger),
		alertManager: NewQuotaAlertManager(logger),
	}

	// 启动后台任务
	go manager.startUsageMonitoring()
	go manager.startQuotaEnforcement()

	return manager
}

// NewUsageTracker 创建使用情况追踪器
func NewUsageTracker(db *gorm.DB, logger logger.Logger) *UsageTracker {
	return &UsageTracker{
		db:     db,
		logger: logger,
	}
}

// NewQuotaAlertManager 创建配额告警管理器
func NewQuotaAlertManager(logger logger.Logger) *QuotaAlertManager {
	return &QuotaAlertManager{
		logger: logger,
		thresholds: map[string]float64{
			"warning":  80.0, // 80%时发出警告
			"critical": 95.0, // 95%时发出严重告警
		},
	}
}

// CreateQuota 创建租户配额
func (qm *TenantQuotaManager) CreateQuota(ctx context.Context, quota *TenantQuota) error {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	// 验证配额参数
	if err := qm.validateQuota(quota); err != nil {
		return fmt.Errorf("配额验证失败: %w", err)
	}

	// 保存到数据库
	if err := qm.db.WithContext(ctx).Create(quota).Error; err != nil {
		return fmt.Errorf("创建配额失败: %w", err)
	}

	// 更新内存缓存
	qm.quotas[quota.TenantID] = quota

	// 初始化使用情况记录
	usage := &ResourceUsage{
		TenantID:    quota.TenantID,
		LastUpdated: time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	if err := qm.db.WithContext(ctx).Create(usage).Error; err != nil {
		qm.logger.Warn("初始化使用情况记录失败", "tenant_id", quota.TenantID, "error", err)
	}

	qm.logger.Info("租户配额创建成功", "tenant_id", quota.TenantID, "quota_id", quota.ID)
	return nil
}

// GetQuota 获取租户配额
func (qm *TenantQuotaManager) GetQuota(ctx context.Context, tenantID string) (*TenantQuota, error) {
	qm.mutex.RLock()
	defer qm.mutex.RUnlock()

	// 先从缓存获取
	if quota, exists := qm.quotas[tenantID]; exists {
		return quota, nil
	}

	// 从数据库获取
	var quota TenantQuota
	if err := qm.db.WithContext(ctx).Where("tenant_id = ?", tenantID).First(&quota).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("租户配额不存在: %s", tenantID)
		}
		return nil, fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 更新缓存
	qm.quotas[tenantID] = &quota
	return &quota, nil
}

// UpdateQuota 更新租户配额
func (qm *TenantQuotaManager) UpdateQuota(ctx context.Context, tenantID string, updates map[string]interface{}) error {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	// 更新数据库
	if err := qm.db.WithContext(ctx).Model(&TenantQuota{}).
		Where("tenant_id = ?", tenantID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("更新租户配额失败: %w", err)
	}

	// 清除缓存，强制重新加载
	delete(qm.quotas, tenantID)

	qm.logger.Info("租户配额更新成功", "tenant_id", tenantID)
	return nil
}

// CheckQuota 检查资源配额
func (qm *TenantQuotaManager) CheckQuota(ctx context.Context, tenantID string, resourceType string, requestedAmount float64) (bool, error) {
	quota, err := qm.GetQuota(ctx, tenantID)
	if err != nil {
		return false, err
	}

	usage, err := qm.GetUsage(ctx, tenantID)
	if err != nil {
		return false, err
	}

	// 根据资源类型检查配额
	switch resourceType {
	case "cpu":
		limit := qm.parseCPULimit(quota.CPULimit)
		if usage.CPUUsed+requestedAmount > limit {
			return false, fmt.Errorf("CPU配额不足: 当前使用 %.2f, 请求 %.2f, 限制 %.2f", 
				usage.CPUUsed, requestedAmount, limit)
		}
	case "memory":
		limit := qm.parseMemoryLimit(quota.MemoryLimit)
		if float64(usage.MemoryUsed)+requestedAmount > limit {
			return false, fmt.Errorf("内存配额不足: 当前使用 %d, 请求 %.0f, 限制 %.0f", 
				usage.MemoryUsed, requestedAmount, limit)
		}
	case "storage":
		limit := qm.parseStorageLimit(quota.StorageLimit)
		if float64(usage.StorageUsed)+requestedAmount > limit {
			return false, fmt.Errorf("存储配额不足: 当前使用 %d, 请求 %.0f, 限制 %.0f", 
				usage.StorageUsed, requestedAmount, limit)
		}
	case "app_count":
		if usage.AppCount+int(requestedAmount) > quota.AppLimit {
			return false, fmt.Errorf("应用数量配额不足: 当前 %d, 请求 %.0f, 限制 %d", 
				usage.AppCount, requestedAmount, quota.AppLimit)
		}
	case "user_count":
		if usage.UserCount+int(requestedAmount) > quota.UserLimit {
			return false, fmt.Errorf("用户数量配额不足: 当前 %d, 请求 %.0f, 限制 %d", 
				usage.UserCount, requestedAmount, quota.UserLimit)
		}
	default:
		return false, fmt.Errorf("不支持的资源类型: %s", resourceType)
	}

	return true, nil
}

// GetUsage 获取租户资源使用情况
func (qm *TenantQuotaManager) GetUsage(ctx context.Context, tenantID string) (*ResourceUsage, error) {
	qm.mutex.RLock()
	defer qm.mutex.RUnlock()

	// 先从缓存获取
	if usage, exists := qm.usage[tenantID]; exists {
		return usage, nil
	}

	// 从数据库获取
	var usage ResourceUsage
	if err := qm.db.WithContext(ctx).Where("tenant_id = ?", tenantID).First(&usage).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建初始使用记录
			usage = ResourceUsage{
				TenantID:    tenantID,
				LastUpdated: time.Now(),
				UpdatedAt:   time.Now(),
			}
			if err := qm.db.WithContext(ctx).Create(&usage).Error; err != nil {
				return nil, fmt.Errorf("创建使用记录失败: %w", err)
			}
		} else {
			return nil, fmt.Errorf("获取使用情况失败: %w", err)
		}
	}

	// 更新缓存
	qm.usage[tenantID] = &usage
	return &usage, nil
}

// UpdateUsage 更新资源使用情况
func (qm *TenantQuotaManager) UpdateUsage(ctx context.Context, tenantID string, resourceType string, delta float64) error {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	usage, err := qm.GetUsage(ctx, tenantID)
	if err != nil {
		return err
	}

	// 更新使用量
	switch resourceType {
	case "cpu":
		usage.CPUUsed += delta
	case "memory":
		usage.MemoryUsed += int64(delta)
	case "storage":
		usage.StorageUsed += int64(delta)
	case "app_count":
		usage.AppCount += int(delta)
	case "user_count":
		usage.UserCount += int(delta)
	}

	usage.LastUpdated = time.Now()
	usage.UpdatedAt = time.Now()

	// 保存到数据库
	if err := qm.db.WithContext(ctx).Save(usage).Error; err != nil {
		return fmt.Errorf("更新使用情况失败: %w", err)
	}

	// 更新缓存
	qm.usage[tenantID] = usage

	// 检查是否需要告警
	go qm.checkQuotaViolation(ctx, tenantID, resourceType)

	return nil
}

// validateQuota 验证配额参数
func (qm *TenantQuotaManager) validateQuota(quota *TenantQuota) error {
	if quota.TenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	if quota.CPULimit == "" {
		return fmt.Errorf("CPU限制不能为空")
	}
	if quota.MemoryLimit == "" {
		return fmt.Errorf("内存限制不能为空")
	}
	if quota.StorageLimit == "" {
		return fmt.Errorf("存储限制不能为空")
	}
	if quota.AppLimit <= 0 {
		return fmt.Errorf("应用数量限制必须大于0")
	}
	if quota.UserLimit <= 0 {
		return fmt.Errorf("用户数量限制必须大于0")
	}
	return nil
}

// parseCPULimit 解析CPU限制
func (qm *TenantQuotaManager) parseCPULimit(limit string) float64 {
	// 简化实现，实际应该解析 "4 cores" 或 "4000m" 格式
	// 这里假设已经是数字格式
	return 4.0 // 默认4核心
}

// parseMemoryLimit 解析内存限制
func (qm *TenantQuotaManager) parseMemoryLimit(limit string) float64 {
	// 简化实现，实际应该解析 "8Gi" 或 "8192Mi" 格式
	// 这里假设已经是字节数
	return 8 * 1024 * 1024 * 1024 // 默认8GB
}

// parseStorageLimit 解析存储限制
func (qm *TenantQuotaManager) parseStorageLimit(limit string) float64 {
	// 简化实现，实际应该解析 "100Gi" 格式
	// 这里假设已经是字节数
	return 100 * 1024 * 1024 * 1024 // 默认100GB
}

// checkQuotaViolation 检查配额违规
func (qm *TenantQuotaManager) checkQuotaViolation(ctx context.Context, tenantID string, resourceType string) {
	quota, err := qm.GetQuota(ctx, tenantID)
	if err != nil {
		qm.logger.Error("获取配额失败", "tenant_id", tenantID, "error", err)
		return
	}

	usage, err := qm.GetUsage(ctx, tenantID)
	if err != nil {
		qm.logger.Error("获取使用情况失败", "tenant_id", tenantID, "error", err)
		return
	}

	// 计算使用率并检查是否需要告警
	var percentage float64
	var limit float64
	var current float64

	switch resourceType {
	case "cpu":
		limit = qm.parseCPULimit(quota.CPULimit)
		current = usage.CPUUsed
		percentage = (current / limit) * 100
	case "memory":
		limit = qm.parseMemoryLimit(quota.MemoryLimit)
		current = float64(usage.MemoryUsed)
		percentage = (current / limit) * 100
	case "storage":
		limit = qm.parseStorageLimit(quota.StorageLimit)
		current = float64(usage.StorageUsed)
		percentage = (current / limit) * 100
	}

	// 检查告警阈值
	if percentage >= qm.alertManager.thresholds["critical"] {
		qm.alertManager.sendAlert(ctx, &QuotaAlert{
			TenantID:     tenantID,
			ResourceType: resourceType,
			AlertLevel:   "critical",
			Message:      fmt.Sprintf("%s使用率达到%.1f%%，已超过严重告警阈值", resourceType, percentage),
			CurrentUsage: current,
			Limit:        limit,
			Percentage:   percentage,
			Timestamp:    time.Now(),
		})
	} else if percentage >= qm.alertManager.thresholds["warning"] {
		qm.alertManager.sendAlert(ctx, &QuotaAlert{
			TenantID:     tenantID,
			ResourceType: resourceType,
			AlertLevel:   "warning",
			Message:      fmt.Sprintf("%s使用率达到%.1f%%，已超过警告阈值", resourceType, percentage),
			CurrentUsage: current,
			Limit:        limit,
			Percentage:   percentage,
			Timestamp:    time.Now(),
		})
	}
}

// sendAlert 发送告警
func (am *QuotaAlertManager) sendAlert(ctx context.Context, alert *QuotaAlert) {
	am.logger.Warn("配额告警", 
		"tenant_id", alert.TenantID,
		"resource_type", alert.ResourceType,
		"alert_level", alert.AlertLevel,
		"percentage", alert.Percentage,
		"message", alert.Message)

	// 这里可以集成实际的通知服务
	if am.notifier != nil {
		if err := am.notifier.SendQuotaAlert(ctx, alert); err != nil {
			am.logger.Error("发送配额告警失败", "error", err)
		}
	}
}

// startUsageMonitoring 启动使用情况监控
func (qm *TenantQuotaManager) startUsageMonitoring() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		qm.collectUsageMetrics()
	}
}

// startQuotaEnforcement 启动配额执行
func (qm *TenantQuotaManager) startQuotaEnforcement() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		qm.enforceQuotas()
	}
}

// collectUsageMetrics 收集使用指标
func (qm *TenantQuotaManager) collectUsageMetrics() {
	// 实现使用指标收集逻辑
	qm.logger.Debug("收集使用指标")
}

// enforceQuotas 执行配额限制
func (qm *TenantQuotaManager) enforceQuotas() {
	// 实现配额执行逻辑
	qm.logger.Debug("执行配额限制")
}
