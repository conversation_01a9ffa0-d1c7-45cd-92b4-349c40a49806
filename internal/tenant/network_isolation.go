package tenant

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"gorm.io/gorm"
	"paas-platform/pkg/logger"
)

// TenantNetworkIsolation 租户网络隔离管理器
type TenantNetworkIsolation struct {
	db              *gorm.DB
	logger          logger.Logger
	networkPolicies map[string]*NetworkPolicy
	firewallRules   map[string][]*FirewallRule
	subnetManager   *SubnetManager
	dnsManager      *DNSManager
	mutex           sync.RWMutex
}

// NetworkPolicy 网络策略
type NetworkPolicy struct {
	ID           string    `json:"id" gorm:"primaryKey"`
	TenantID     string    `json:"tenant_id" gorm:"uniqueIndex;not null"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	
	// 隔离模式
	IsolationMode string   `json:"isolation_mode"` // strict, moderate, loose, custom
	
	// 网络配置
	SubnetCIDR    string   `json:"subnet_cidr"`    // 租户专用子网
	Gateway       string   `json:"gateway"`        // 网关地址
	DNSServers    []string `json:"dns_servers" gorm:"type:jsonb"`
	
	// 访问控制
	AllowedCIDRs  []string `json:"allowed_cidrs" gorm:"type:jsonb"`  // 允许访问的CIDR
	BlockedCIDRs  []string `json:"blocked_cidrs" gorm:"type:jsonb"`  // 禁止访问的CIDR
	AllowedPorts  []int    `json:"allowed_ports" gorm:"type:jsonb"`  // 允许的端口
	BlockedPorts  []int    `json:"blocked_ports" gorm:"type:jsonb"`  // 禁止的端口
	
	// 服务间通信
	AllowInterTenantComm bool     `json:"allow_inter_tenant_comm"` // 是否允许租户间通信
	AllowedTenants       []string `json:"allowed_tenants" gorm:"type:jsonb"` // 允许通信的租户
	
	// QoS配置
	BandwidthLimit   string `json:"bandwidth_limit"`   // 带宽限制
	ConnectionLimit  int    `json:"connection_limit"`  // 连接数限制
	RateLimitRPS     int    `json:"rate_limit_rps"`    // 请求速率限制
	
	// 安全配置
	EnableDPI        bool   `json:"enable_dpi"`        // 深度包检测
	EnableIDS        bool   `json:"enable_ids"`        // 入侵检测
	EnableFirewall   bool   `json:"enable_firewall"`   // 防火墙
	
	// 状态和时间
	Status       string    `json:"status"` // active, inactive, pending
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	
	// 扩展配置
	CustomRules  map[string]interface{} `json:"custom_rules" gorm:"type:jsonb"`
	Tags         map[string]string      `json:"tags" gorm:"type:jsonb"`
}

// FirewallRule 防火墙规则
type FirewallRule struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	TenantID    string    `json:"tenant_id" gorm:"index;not null"`
	PolicyID    string    `json:"policy_id" gorm:"index"`
	
	// 规则基本信息
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Priority    int       `json:"priority"` // 优先级，数字越小优先级越高
	
	// 规则条件
	Direction   string    `json:"direction"`   // inbound, outbound, both
	Protocol    string    `json:"protocol"`    // tcp, udp, icmp, all
	SourceIP    string    `json:"source_ip"`   // 源IP或CIDR
	DestIP      string    `json:"dest_ip"`     // 目标IP或CIDR
	SourcePort  string    `json:"source_port"` // 源端口或端口范围
	DestPort    string    `json:"dest_port"`   // 目标端口或端口范围
	
	// 规则动作
	Action      string    `json:"action"`      // allow, deny, log, drop
	LogEnabled  bool      `json:"log_enabled"` // 是否记录日志
	
	// 时间限制
	TimeRestriction *TimeRestriction `json:"time_restriction" gorm:"embedded"`
	
	// 状态和统计
	Status      string    `json:"status"` // active, inactive
	HitCount    int64     `json:"hit_count"` // 命中次数
	LastHit     *time.Time `json:"last_hit"`
	
	// 审计信息
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	CreatedBy   string    `json:"created_by"`
	
	// 扩展配置
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
}

// TimeRestriction 时间限制
type TimeRestriction struct {
	Enabled     bool     `json:"enabled"`
	StartTime   string   `json:"start_time"`   // HH:MM格式
	EndTime     string   `json:"end_time"`     // HH:MM格式
	Weekdays    []int    `json:"weekdays"`     // 0-6，0为周日
	DateRange   *DateRange `json:"date_range"`
}

// DateRange 日期范围
type DateRange struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
}

// SubnetManager 子网管理器
type SubnetManager struct {
	db           *gorm.DB
	logger       logger.Logger
	allocatedIPs map[string]map[string]bool // tenant_id -> ip -> allocated
	subnetPool   *SubnetPool
	mutex        sync.RWMutex
}

// SubnetPool 子网池
type SubnetPool struct {
	BaseNetwork *net.IPNet
	SubnetSize  int // 子网掩码位数
	Allocated   map[string]bool // 已分配的子网
}

// DNSManager DNS管理器
type DNSManager struct {
	db     *gorm.DB
	logger logger.Logger
	zones  map[string]*DNSZone
	mutex  sync.RWMutex
}

// DNSZone DNS区域
type DNSZone struct {
	TenantID string            `json:"tenant_id"`
	Domain   string            `json:"domain"`
	Records  map[string]string `json:"records"` // name -> ip
}

// NetworkIsolationStats 网络隔离统计
type NetworkIsolationStats struct {
	TenantID           string    `json:"tenant_id"`
	TotalConnections   int64     `json:"total_connections"`
	BlockedConnections int64     `json:"blocked_connections"`
	AllowedConnections int64     `json:"allowed_connections"`
	BandwidthUsed      int64     `json:"bandwidth_used"`
	PacketsIn          int64     `json:"packets_in"`
	PacketsOut         int64     `json:"packets_out"`
	BytesIn            int64     `json:"bytes_in"`
	BytesOut           int64     `json:"bytes_out"`
	LastUpdated        time.Time `json:"last_updated"`
}

// NewTenantNetworkIsolation 创建租户网络隔离管理器
func NewTenantNetworkIsolation(db *gorm.DB, logger logger.Logger) *TenantNetworkIsolation {
	isolation := &TenantNetworkIsolation{
		db:              db,
		logger:          logger,
		networkPolicies: make(map[string]*NetworkPolicy),
		firewallRules:   make(map[string][]*FirewallRule),
		subnetManager:   NewSubnetManager(db, logger),
		dnsManager:      NewDNSManager(db, logger),
	}

	// 启动后台任务
	go isolation.startPolicyEnforcement()
	go isolation.startStatsCollection()

	return isolation
}

// NewSubnetManager 创建子网管理器
func NewSubnetManager(db *gorm.DB, logger logger.Logger) *SubnetManager {
	// 初始化子网池 (10.0.0.0/8)
	_, baseNetwork, _ := net.ParseCIDR("10.0.0.0/8")
	
	return &SubnetManager{
		db:           db,
		logger:       logger,
		allocatedIPs: make(map[string]map[string]bool),
		subnetPool: &SubnetPool{
			BaseNetwork: baseNetwork,
			SubnetSize:  24, // /24子网，每个租户256个IP
			Allocated:   make(map[string]bool),
		},
	}
}

// NewDNSManager 创建DNS管理器
func NewDNSManager(db *gorm.DB, logger logger.Logger) *DNSManager {
	return &DNSManager{
		db:     db,
		logger: logger,
		zones:  make(map[string]*DNSZone),
	}
}

// CreateNetworkPolicy 创建网络策略
func (tni *TenantNetworkIsolation) CreateNetworkPolicy(ctx context.Context, policy *NetworkPolicy) error {
	tni.mutex.Lock()
	defer tni.mutex.Unlock()

	// 验证策略参数
	if err := tni.validateNetworkPolicy(policy); err != nil {
		return fmt.Errorf("网络策略验证失败: %w", err)
	}

	// 分配子网
	subnet, err := tni.subnetManager.AllocateSubnet(ctx, policy.TenantID)
	if err != nil {
		return fmt.Errorf("分配子网失败: %w", err)
	}
	policy.SubnetCIDR = subnet

	// 设置默认网关
	policy.Gateway = tni.calculateGateway(subnet)

	// 保存到数据库
	if err := tni.db.WithContext(ctx).Create(policy).Error; err != nil {
		return fmt.Errorf("创建网络策略失败: %w", err)
	}

	// 更新内存缓存
	tni.networkPolicies[policy.TenantID] = policy

	// 应用网络策略
	if err := tni.applyNetworkPolicy(ctx, policy); err != nil {
		tni.logger.Error("应用网络策略失败", "tenant_id", policy.TenantID, "error", err)
	}

	tni.logger.Info("网络策略创建成功", "tenant_id", policy.TenantID, "subnet", subnet)
	return nil
}

// GetNetworkPolicy 获取网络策略
func (tni *TenantNetworkIsolation) GetNetworkPolicy(ctx context.Context, tenantID string) (*NetworkPolicy, error) {
	tni.mutex.RLock()
	defer tni.mutex.RUnlock()

	// 先从缓存获取
	if policy, exists := tni.networkPolicies[tenantID]; exists {
		return policy, nil
	}

	// 从数据库获取
	var policy NetworkPolicy
	if err := tni.db.WithContext(ctx).Where("tenant_id = ?", tenantID).First(&policy).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("网络策略不存在: %s", tenantID)
		}
		return nil, fmt.Errorf("获取网络策略失败: %w", err)
	}

	// 更新缓存
	tni.networkPolicies[tenantID] = &policy
	return &policy, nil
}

// CreateFirewallRule 创建防火墙规则
func (tni *TenantNetworkIsolation) CreateFirewallRule(ctx context.Context, rule *FirewallRule) error {
	// 验证规则参数
	if err := tni.validateFirewallRule(rule); err != nil {
		return fmt.Errorf("防火墙规则验证失败: %w", err)
	}

	// 保存到数据库
	if err := tni.db.WithContext(ctx).Create(rule).Error; err != nil {
		return fmt.Errorf("创建防火墙规则失败: %w", err)
	}

	// 更新内存缓存
	tni.mutex.Lock()
	if _, exists := tni.firewallRules[rule.TenantID]; !exists {
		tni.firewallRules[rule.TenantID] = make([]*FirewallRule, 0)
	}
	tni.firewallRules[rule.TenantID] = append(tni.firewallRules[rule.TenantID], rule)
	tni.mutex.Unlock()

	// 应用防火墙规则
	if err := tni.applyFirewallRule(ctx, rule); err != nil {
		tni.logger.Error("应用防火墙规则失败", "rule_id", rule.ID, "error", err)
	}

	tni.logger.Info("防火墙规则创建成功", "tenant_id", rule.TenantID, "rule_id", rule.ID)
	return nil
}

// CheckNetworkAccess 检查网络访问权限
func (tni *TenantNetworkIsolation) CheckNetworkAccess(ctx context.Context, tenantID string, sourceIP, destIP string, port int, protocol string) (bool, string, error) {
	policy, err := tni.GetNetworkPolicy(ctx, tenantID)
	if err != nil {
		return false, "策略不存在", err
	}

	// 检查基本网络策略
	if !tni.isIPAllowed(sourceIP, policy.AllowedCIDRs) {
		return false, "源IP不在允许列表中", nil
	}

	if tni.isIPBlocked(destIP, policy.BlockedCIDRs) {
		return false, "目标IP在禁止列表中", nil
	}

	if !tni.isPortAllowed(port, policy.AllowedPorts) {
		return false, "端口不在允许列表中", nil
	}

	if tni.isPortBlocked(port, policy.BlockedPorts) {
		return false, "端口在禁止列表中", nil
	}

	// 检查防火墙规则
	rules := tni.getFirewallRules(tenantID)
	for _, rule := range rules {
		if tni.matchesFirewallRule(rule, sourceIP, destIP, port, protocol) {
			if rule.Action == "deny" || rule.Action == "drop" {
				return false, fmt.Sprintf("被防火墙规则阻止: %s", rule.Name), nil
			}
			if rule.Action == "allow" {
				return true, "防火墙规则允许", nil
			}
		}
	}

	// 默认根据隔离模式决定
	switch policy.IsolationMode {
	case "strict":
		return false, "严格隔离模式，默认拒绝", nil
	case "moderate":
		return true, "适中隔离模式，默认允许", nil
	case "loose":
		return true, "宽松隔离模式，默认允许", nil
	default:
		return false, "未知隔离模式", nil
	}
}

// AllocateSubnet 分配子网
func (sm *SubnetManager) AllocateSubnet(ctx context.Context, tenantID string) (string, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// 查找可用的子网
	baseIP := sm.subnetPool.BaseNetwork.IP
	mask := sm.subnetPool.BaseNetwork.Mask

	// 简化实现：基于租户ID哈希分配子网
	// 实际实现应该更复杂，确保不冲突
	subnetIndex := sm.hashTenantID(tenantID) % 65536 // 2^16个可能的/24子网
	
	// 计算子网IP
	subnetIP := make(net.IP, len(baseIP))
	copy(subnetIP, baseIP)
	subnetIP[1] = byte(subnetIndex >> 8)
	subnetIP[2] = byte(subnetIndex & 0xFF)
	
	subnet := fmt.Sprintf("%s/%d", subnetIP.String(), sm.subnetPool.SubnetSize)
	
	// 检查是否已分配
	if sm.subnetPool.Allocated[subnet] {
		return "", fmt.Errorf("子网已被分配: %s", subnet)
	}
	
	// 标记为已分配
	sm.subnetPool.Allocated[subnet] = true
	
	sm.logger.Info("子网分配成功", "tenant_id", tenantID, "subnet", subnet)
	return subnet, nil
}

// validateNetworkPolicy 验证网络策略
func (tni *TenantNetworkIsolation) validateNetworkPolicy(policy *NetworkPolicy) error {
	if policy.TenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	if policy.IsolationMode == "" {
		policy.IsolationMode = "moderate" // 默认适中模式
	}
	if policy.Name == "" {
		policy.Name = fmt.Sprintf("policy-%s", policy.TenantID)
	}
	return nil
}

// validateFirewallRule 验证防火墙规则
func (tni *TenantNetworkIsolation) validateFirewallRule(rule *FirewallRule) error {
	if rule.TenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	if rule.Direction == "" {
		return fmt.Errorf("方向不能为空")
	}
	if rule.Action == "" {
		return fmt.Errorf("动作不能为空")
	}
	return nil
}

// calculateGateway 计算网关地址
func (tni *TenantNetworkIsolation) calculateGateway(subnet string) string {
	ip, ipNet, err := net.ParseCIDR(subnet)
	if err != nil {
		return ""
	}
	
	// 网关通常是子网的第一个可用IP
	gateway := make(net.IP, len(ip))
	copy(gateway, ipNet.IP)
	gateway[3] = 1 // .1作为网关
	
	return gateway.String()
}

// isIPAllowed 检查IP是否在允许列表中
func (tni *TenantNetworkIsolation) isIPAllowed(ip string, allowedCIDRs []string) bool {
	if len(allowedCIDRs) == 0 {
		return true // 没有限制则允许
	}
	
	for _, cidr := range allowedCIDRs {
		if tni.ipInCIDR(ip, cidr) {
			return true
		}
	}
	return false
}

// isIPBlocked 检查IP是否在禁止列表中
func (tni *TenantNetworkIsolation) isIPBlocked(ip string, blockedCIDRs []string) bool {
	for _, cidr := range blockedCIDRs {
		if tni.ipInCIDR(ip, cidr) {
			return true
		}
	}
	return false
}

// isPortAllowed 检查端口是否允许
func (tni *TenantNetworkIsolation) isPortAllowed(port int, allowedPorts []int) bool {
	if len(allowedPorts) == 0 {
		return true // 没有限制则允许
	}
	
	for _, allowedPort := range allowedPorts {
		if port == allowedPort {
			return true
		}
	}
	return false
}

// isPortBlocked 检查端口是否被禁止
func (tni *TenantNetworkIsolation) isPortBlocked(port int, blockedPorts []int) bool {
	for _, blockedPort := range blockedPorts {
		if port == blockedPort {
			return true
		}
	}
	return false
}

// ipInCIDR 检查IP是否在CIDR范围内
func (tni *TenantNetworkIsolation) ipInCIDR(ip, cidr string) bool {
	_, ipNet, err := net.ParseCIDR(cidr)
	if err != nil {
		return false
	}
	
	ipAddr := net.ParseIP(ip)
	if ipAddr == nil {
		return false
	}
	
	return ipNet.Contains(ipAddr)
}

// getFirewallRules 获取防火墙规则
func (tni *TenantNetworkIsolation) getFirewallRules(tenantID string) []*FirewallRule {
	tni.mutex.RLock()
	defer tni.mutex.RUnlock()
	
	if rules, exists := tni.firewallRules[tenantID]; exists {
		return rules
	}
	return nil
}

// matchesFirewallRule 检查是否匹配防火墙规则
func (tni *TenantNetworkIsolation) matchesFirewallRule(rule *FirewallRule, sourceIP, destIP string, port int, protocol string) bool {
	// 简化的匹配逻辑
	if rule.Protocol != "all" && rule.Protocol != protocol {
		return false
	}
	
	if rule.SourceIP != "" && rule.SourceIP != "any" && !tni.ipInCIDR(sourceIP, rule.SourceIP) {
		return false
	}
	
	if rule.DestIP != "" && rule.DestIP != "any" && !tni.ipInCIDR(destIP, rule.DestIP) {
		return false
	}
	
	// 端口匹配逻辑（简化）
	if rule.DestPort != "" && rule.DestPort != "any" {
		// 这里应该解析端口范围，简化为直接比较
		return fmt.Sprintf("%d", port) == rule.DestPort
	}
	
	return true
}

// hashTenantID 计算租户ID哈希
func (sm *SubnetManager) hashTenantID(tenantID string) int {
	hash := 0
	for _, char := range tenantID {
		hash = hash*31 + int(char)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// applyNetworkPolicy 应用网络策略
func (tni *TenantNetworkIsolation) applyNetworkPolicy(ctx context.Context, policy *NetworkPolicy) error {
	// 这里应该调用底层网络配置API
	// 例如配置iptables、创建网络命名空间等
	tni.logger.Info("应用网络策略", "tenant_id", policy.TenantID, "policy_id", policy.ID)
	return nil
}

// applyFirewallRule 应用防火墙规则
func (tni *TenantNetworkIsolation) applyFirewallRule(ctx context.Context, rule *FirewallRule) error {
	// 这里应该调用底层防火墙配置API
	// 例如配置iptables规则
	tni.logger.Info("应用防火墙规则", "tenant_id", rule.TenantID, "rule_id", rule.ID)
	return nil
}

// startPolicyEnforcement 启动策略执行
func (tni *TenantNetworkIsolation) startPolicyEnforcement() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		tni.enforcePolicies()
	}
}

// startStatsCollection 启动统计收集
func (tni *TenantNetworkIsolation) startStatsCollection() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		tni.collectNetworkStats()
	}
}

// enforcePolicies 执行策略
func (tni *TenantNetworkIsolation) enforcePolicies() {
	// 实现策略执行逻辑
	tni.logger.Debug("执行网络策略")
}

// collectNetworkStats 收集网络统计
func (tni *TenantNetworkIsolation) collectNetworkStats() {
	// 实现网络统计收集逻辑
	tni.logger.Debug("收集网络统计")
}
