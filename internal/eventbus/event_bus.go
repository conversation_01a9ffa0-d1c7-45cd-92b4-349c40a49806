package eventbus

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"paas-platform/pkg/logger"
)

// EventBus 事件总线接口
type EventBus interface {
	// 发布事件
	Publish(ctx context.Context, event *Event) error
	
	// 订阅事件
	Subscribe(eventType string, handler EventHandler) (*Subscription, error)
	
	// 取消订阅
	Unsubscribe(subscription *Subscription) error
	
	// 启动事件总线
	Start() error
	
	// 停止事件总线
	Stop() error
	
	// 获取统计信息
	GetStats() *EventBusStats
}

// Event 事件结构
type Event struct {
	ID          string                 `json:"id"`          // 事件唯一标识
	Type        string                 `json:"type"`        // 事件类型
	Source      string                 `json:"source"`      // 事件源
	Subject     string                 `json:"subject"`     // 事件主题
	Data        interface{}            `json:"data"`        // 事件数据
	Metadata    map[string]interface{} `json:"metadata"`    // 元数据
	Timestamp   time.Time              `json:"timestamp"`   // 事件时间戳
	Version     string                 `json:"version"`     // 事件版本
	ContentType string                 `json:"content_type"` // 内容类型
	TraceID     string                 `json:"trace_id"`    // 链路追踪ID
}

// EventHandler 事件处理器接口
type EventHandler interface {
	Handle(ctx context.Context, event *Event) error
	GetName() string
	GetEventTypes() []string
}

// Subscription 订阅信息
type Subscription struct {
	ID          string       `json:"id"`
	EventType   string       `json:"event_type"`
	Handler     EventHandler `json:"-"`
	Filter      *EventFilter `json:"filter"`
	Config      *SubscriptionConfig `json:"config"`
	CreatedAt   time.Time    `json:"created_at"`
	LastTriggered time.Time  `json:"last_triggered"`
	TriggerCount int64       `json:"trigger_count"`
	ErrorCount   int64       `json:"error_count"`
}

// EventFilter 事件过滤器
type EventFilter struct {
	Source      []string               `json:"source"`      // 允许的事件源
	Subject     []string               `json:"subject"`     // 允许的事件主题
	Attributes  map[string]interface{} `json:"attributes"`  // 属性过滤
	Expression  string                 `json:"expression"`  // 过滤表达式
}

// SubscriptionConfig 订阅配置
type SubscriptionConfig struct {
	MaxRetries      int           `json:"max_retries"`      // 最大重试次数
	RetryInterval   time.Duration `json:"retry_interval"`   // 重试间隔
	Timeout         time.Duration `json:"timeout"`          // 处理超时
	DeadLetterQueue bool          `json:"dead_letter_queue"` // 死信队列
	Async           bool          `json:"async"`            // 异步处理
	BatchSize       int           `json:"batch_size"`       // 批处理大小
	BufferSize      int           `json:"buffer_size"`      // 缓冲区大小
}

// EventBusStats 事件总线统计
type EventBusStats struct {
	TotalEvents       int64         `json:"total_events"`
	PublishedEvents   int64         `json:"published_events"`
	ProcessedEvents   int64         `json:"processed_events"`
	FailedEvents      int64         `json:"failed_events"`
	ActiveSubscriptions int         `json:"active_subscriptions"`
	AverageLatency    time.Duration `json:"average_latency"`
	LastEventTime     time.Time     `json:"last_event_time"`
	Uptime            time.Duration `json:"uptime"`
}

// InMemoryEventBus 内存事件总线实现
type InMemoryEventBus struct {
	subscriptions map[string][]*Subscription
	handlers      map[string]EventHandler
	eventQueue    chan *EventMessage
	stats         *EventBusStats
	logger        logger.Logger
	config        *EventBusConfig
	mutex         sync.RWMutex
	running       bool
	startTime     time.Time
	workers       []*EventWorker
}

// EventMessage 事件消息
type EventMessage struct {
	Event        *Event
	Subscription *Subscription
	RetryCount   int
	CreatedAt    time.Time
}

// EventWorker 事件工作器
type EventWorker struct {
	ID       int
	eventBus *InMemoryEventBus
	queue    chan *EventMessage
	logger   logger.Logger
	running  bool
}

// EventBusConfig 事件总线配置
type EventBusConfig struct {
	QueueSize       int           `yaml:"queue_size"`       // 队列大小
	WorkerCount     int           `yaml:"worker_count"`     // 工作器数量
	MaxRetries      int           `yaml:"max_retries"`      // 默认最大重试次数
	RetryInterval   time.Duration `yaml:"retry_interval"`   // 默认重试间隔
	ProcessTimeout  time.Duration `yaml:"process_timeout"`  // 处理超时
	EnableMetrics   bool          `yaml:"enable_metrics"`   // 启用指标
	EnableTracing   bool          `yaml:"enable_tracing"`   // 启用链路追踪
	PersistEvents   bool          `yaml:"persist_events"`   // 持久化事件
}

// NewInMemoryEventBus 创建内存事件总线
func NewInMemoryEventBus(config *EventBusConfig, logger logger.Logger) *InMemoryEventBus {
	if config == nil {
		config = getDefaultEventBusConfig()
	}

	bus := &InMemoryEventBus{
		subscriptions: make(map[string][]*Subscription),
		handlers:      make(map[string]EventHandler),
		eventQueue:    make(chan *EventMessage, config.QueueSize),
		stats: &EventBusStats{
			TotalEvents:         0,
			PublishedEvents:     0,
			ProcessedEvents:     0,
			FailedEvents:        0,
			ActiveSubscriptions: 0,
			AverageLatency:      0,
			LastEventTime:       time.Time{},
			Uptime:              0,
		},
		logger:  logger,
		config:  config,
		running: false,
		workers: make([]*EventWorker, 0),
	}

	return bus
}

// getDefaultEventBusConfig 获取默认配置
func getDefaultEventBusConfig() *EventBusConfig {
	return &EventBusConfig{
		QueueSize:      10000,
		WorkerCount:    10,
		MaxRetries:     3,
		RetryInterval:  5 * time.Second,
		ProcessTimeout: 30 * time.Second,
		EnableMetrics:  true,
		EnableTracing:  true,
		PersistEvents:  false,
	}
}

// Start 启动事件总线
func (bus *InMemoryEventBus) Start() error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	if bus.running {
		return fmt.Errorf("事件总线已经在运行")
	}

	bus.running = true
	bus.startTime = time.Now()

	// 启动工作器
	for i := 0; i < bus.config.WorkerCount; i++ {
		worker := &EventWorker{
			ID:       i,
			eventBus: bus,
			queue:    bus.eventQueue,
			logger:   bus.logger,
			running:  true,
		}
		bus.workers = append(bus.workers, worker)
		go worker.start()
	}

	// 启动统计更新
	go bus.updateStats()

	bus.logger.Info("事件总线启动成功", "worker_count", bus.config.WorkerCount, "queue_size", bus.config.QueueSize)
	return nil
}

// Stop 停止事件总线
func (bus *InMemoryEventBus) Stop() error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	if !bus.running {
		return fmt.Errorf("事件总线未运行")
	}

	bus.running = false

	// 停止所有工作器
	for _, worker := range bus.workers {
		worker.stop()
	}

	// 关闭事件队列
	close(bus.eventQueue)

	bus.logger.Info("事件总线已停止")
	return nil
}

// Publish 发布事件
func (bus *InMemoryEventBus) Publish(ctx context.Context, event *Event) error {
	if !bus.running {
		return fmt.Errorf("事件总线未运行")
	}

	// 设置事件ID和时间戳
	if event.ID == "" {
		event.ID = uuid.New().String()
	}
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}

	// 更新统计
	bus.stats.TotalEvents++
	bus.stats.PublishedEvents++
	bus.stats.LastEventTime = event.Timestamp

	// 查找匹配的订阅
	bus.mutex.RLock()
	subscriptions, exists := bus.subscriptions[event.Type]
	bus.mutex.RUnlock()

	if !exists || len(subscriptions) == 0 {
		bus.logger.Debug("没有找到事件订阅", "event_type", event.Type, "event_id", event.ID)
		return nil
	}

	// 为每个订阅创建事件消息
	for _, subscription := range subscriptions {
		if bus.matchesFilter(event, subscription.Filter) {
			eventMessage := &EventMessage{
				Event:        event,
				Subscription: subscription,
				RetryCount:   0,
				CreatedAt:    time.Now(),
			}

			// 异步发送到队列
			select {
			case bus.eventQueue <- eventMessage:
				bus.logger.Debug("事件已加入队列", "event_id", event.ID, "subscription_id", subscription.ID)
			default:
				bus.logger.Warn("事件队列已满，丢弃事件", "event_id", event.ID, "event_type", event.Type)
				bus.stats.FailedEvents++
			}
		}
	}

	bus.logger.Info("事件发布成功", "event_id", event.ID, "event_type", event.Type, "source", event.Source)
	return nil
}

// Subscribe 订阅事件
func (bus *InMemoryEventBus) Subscribe(eventType string, handler EventHandler) (*Subscription, error) {
	subscription := &Subscription{
		ID:        uuid.New().String(),
		EventType: eventType,
		Handler:   handler,
		Config:    getDefaultSubscriptionConfig(),
		CreatedAt: time.Now(),
	}

	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	// 添加到订阅列表
	if _, exists := bus.subscriptions[eventType]; !exists {
		bus.subscriptions[eventType] = make([]*Subscription, 0)
	}
	bus.subscriptions[eventType] = append(bus.subscriptions[eventType], subscription)

	// 注册处理器
	bus.handlers[subscription.ID] = handler

	// 更新统计
	bus.stats.ActiveSubscriptions++

	bus.logger.Info("事件订阅成功", "subscription_id", subscription.ID, "event_type", eventType, "handler", handler.GetName())
	return subscription, nil
}

// Unsubscribe 取消订阅
func (bus *InMemoryEventBus) Unsubscribe(subscription *Subscription) error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	// 从订阅列表中移除
	if subscriptions, exists := bus.subscriptions[subscription.EventType]; exists {
		for i, sub := range subscriptions {
			if sub.ID == subscription.ID {
				bus.subscriptions[subscription.EventType] = append(subscriptions[:i], subscriptions[i+1:]...)
				break
			}
		}
	}

	// 移除处理器
	delete(bus.handlers, subscription.ID)

	// 更新统计
	bus.stats.ActiveSubscriptions--

	bus.logger.Info("取消事件订阅", "subscription_id", subscription.ID, "event_type", subscription.EventType)
	return nil
}

// GetStats 获取统计信息
func (bus *InMemoryEventBus) GetStats() *EventBusStats {
	bus.mutex.RLock()
	defer bus.mutex.RUnlock()

	// 计算运行时间
	if bus.running {
		bus.stats.Uptime = time.Since(bus.startTime)
	}

	// 返回统计信息副本
	return &EventBusStats{
		TotalEvents:         bus.stats.TotalEvents,
		PublishedEvents:     bus.stats.PublishedEvents,
		ProcessedEvents:     bus.stats.ProcessedEvents,
		FailedEvents:        bus.stats.FailedEvents,
		ActiveSubscriptions: bus.stats.ActiveSubscriptions,
		AverageLatency:      bus.stats.AverageLatency,
		LastEventTime:       bus.stats.LastEventTime,
		Uptime:              bus.stats.Uptime,
	}
}

// matchesFilter 检查事件是否匹配过滤器
func (bus *InMemoryEventBus) matchesFilter(event *Event, filter *EventFilter) bool {
	if filter == nil {
		return true
	}

	// 检查事件源
	if len(filter.Source) > 0 {
		matched := false
		for _, source := range filter.Source {
			if event.Source == source {
				matched = true
				break
			}
		}
		if !matched {
			return false
		}
	}

	// 检查事件主题
	if len(filter.Subject) > 0 {
		matched := false
		for _, subject := range filter.Subject {
			if event.Subject == subject {
				matched = true
				break
			}
		}
		if !matched {
			return false
		}
	}

	// 检查属性过滤
	if len(filter.Attributes) > 0 {
		for key, expectedValue := range filter.Attributes {
			if actualValue, exists := event.Metadata[key]; !exists || actualValue != expectedValue {
				return false
			}
		}
	}

	return true
}

// getDefaultSubscriptionConfig 获取默认订阅配置
func getDefaultSubscriptionConfig() *SubscriptionConfig {
	return &SubscriptionConfig{
		MaxRetries:      3,
		RetryInterval:   5 * time.Second,
		Timeout:         30 * time.Second,
		DeadLetterQueue: false,
		Async:           true,
		BatchSize:       1,
		BufferSize:      100,
	}
}

// updateStats 更新统计信息
func (bus *InMemoryEventBus) updateStats() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for bus.running {
		select {
		case <-ticker.C:
			// 更新平均延迟等统计信息
			// 这里可以添加更复杂的统计逻辑
		}
	}
}

// start 启动事件工作器
func (worker *EventWorker) start() {
	worker.eventBus.logger.Debug("事件工作器启动", "worker_id", worker.ID)

	for worker.running {
		select {
		case eventMessage, ok := <-worker.queue:
			if !ok {
				worker.running = false
				break
			}
			worker.processEvent(eventMessage)
		}
	}

	worker.eventBus.logger.Debug("事件工作器停止", "worker_id", worker.ID)
}

// stop 停止事件工作器
func (worker *EventWorker) stop() {
	worker.running = false
}

// processEvent 处理事件
func (worker *EventWorker) processEvent(eventMessage *EventMessage) {
	startTime := time.Now()
	
	ctx, cancel := context.WithTimeout(context.Background(), eventMessage.Subscription.Config.Timeout)
	defer cancel()

	// 获取处理器
	handler := eventMessage.Subscription.Handler
	if handler == nil {
		worker.logger.Error("事件处理器为空", "subscription_id", eventMessage.Subscription.ID)
		worker.eventBus.stats.FailedEvents++
		return
	}

	// 处理事件
	err := handler.Handle(ctx, eventMessage.Event)
	
	duration := time.Since(startTime)
	
	if err != nil {
		worker.logger.Error("事件处理失败", "event_id", eventMessage.Event.ID, 
			"handler", handler.GetName(), "error", err, "retry_count", eventMessage.RetryCount)
		
		// 重试逻辑
		if eventMessage.RetryCount < eventMessage.Subscription.Config.MaxRetries {
			eventMessage.RetryCount++
			
			// 延迟重试
			go func() {
				time.Sleep(eventMessage.Subscription.Config.RetryInterval)
				select {
				case worker.queue <- eventMessage:
					worker.logger.Debug("事件重试", "event_id", eventMessage.Event.ID, "retry_count", eventMessage.RetryCount)
				default:
					worker.logger.Warn("重试队列已满", "event_id", eventMessage.Event.ID)
				}
			}()
		} else {
			worker.eventBus.stats.FailedEvents++
			eventMessage.Subscription.ErrorCount++
		}
	} else {
		worker.logger.Debug("事件处理成功", "event_id", eventMessage.Event.ID, 
			"handler", handler.GetName(), "duration", duration)
		
		worker.eventBus.stats.ProcessedEvents++
		eventMessage.Subscription.TriggerCount++
		eventMessage.Subscription.LastTriggered = time.Now()
	}
}
