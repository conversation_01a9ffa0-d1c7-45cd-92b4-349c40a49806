package eventbus

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"paas-platform/pkg/logger"
)

// 定义PaaS平台的事件类型常量
const (
	// 应用生命周期事件
	EventTypeAppCreated    = "app.created"
	EventTypeAppUpdated    = "app.updated"
	EventTypeAppDeleted    = "app.deleted"
	EventTypeAppDeployed   = "app.deployed"
	EventTypeAppStarted    = "app.started"
	EventTypeAppStopped    = "app.stopped"
	EventTypeAppScaled     = "app.scaled"
	
	// 容器生命周期事件
	EventTypeContainerCreated = "container.created"
	EventTypeContainerStarted = "container.started"
	EventTypeContainerStopped = "container.stopped"
	EventTypeContainerFailed  = "container.failed"
	EventTypeContainerRemoved = "container.removed"
	
	// 服务事件
	EventTypeServiceRegistered   = "service.registered"
	EventTypeServiceDeregistered = "service.deregistered"
	EventTypeServiceHealthy      = "service.healthy"
	EventTypeServiceUnhealthy    = "service.unhealthy"
	
	// 资源事件
	EventTypeResourceAllocated = "resource.allocated"
	EventTypeResourceReleased  = "resource.released"
	EventTypeResourceExhausted = "resource.exhausted"
	
	// 用户事件
	EventTypeUserLogin    = "user.login"
	EventTypeUserLogout   = "user.logout"
	EventTypeUserCreated  = "user.created"
	EventTypeUserUpdated  = "user.updated"
	EventTypeUserDeleted  = "user.deleted"
	
	// 系统事件
	EventTypeSystemAlert     = "system.alert"
	EventTypeSystemMaintenance = "system.maintenance"
	EventTypeSystemBackup   = "system.backup"
	
	// CI/CD事件
	EventTypeBuildStarted   = "build.started"
	EventTypeBuildCompleted = "build.completed"
	EventTypeBuildFailed    = "build.failed"
	EventTypeDeployStarted  = "deploy.started"
	EventTypeDeployCompleted = "deploy.completed"
	EventTypeDeployFailed   = "deploy.failed"
)

// 事件数据结构定义

// AppEventData 应用事件数据
type AppEventData struct {
	AppID       string            `json:"app_id"`
	AppName     string            `json:"app_name"`
	UserID      string            `json:"user_id"`
	Image       string            `json:"image"`
	Replicas    int               `json:"replicas"`
	Status      string            `json:"status"`
	Environment map[string]string `json:"environment"`
	Resources   *ResourceInfo     `json:"resources"`
}

// ContainerEventData 容器事件数据
type ContainerEventData struct {
	ContainerID   string        `json:"container_id"`
	ContainerName string        `json:"container_name"`
	AppID         string        `json:"app_id"`
	Image         string        `json:"image"`
	Status        string        `json:"status"`
	ExitCode      int           `json:"exit_code"`
	Resources     *ResourceInfo `json:"resources"`
	Node          string        `json:"node"`
}

// ServiceEventData 服务事件数据
type ServiceEventData struct {
	ServiceID   string            `json:"service_id"`
	ServiceName string            `json:"service_name"`
	AppID       string            `json:"app_id"`
	Host        string            `json:"host"`
	Port        int               `json:"port"`
	Status      string            `json:"status"`
	HealthCheck *HealthCheckInfo  `json:"health_check"`
	Metadata    map[string]string `json:"metadata"`
}

// ResourceEventData 资源事件数据
type ResourceEventData struct {
	ResourceType string  `json:"resource_type"` // cpu, memory, disk, network
	NodeID       string  `json:"node_id"`
	Used         float64 `json:"used"`
	Total        float64 `json:"total"`
	Percentage   float64 `json:"percentage"`
	Threshold    float64 `json:"threshold"`
}

// UserEventData 用户事件数据
type UserEventData struct {
	UserID    string            `json:"user_id"`
	Username  string            `json:"username"`
	Email     string            `json:"email"`
	Role      string            `json:"role"`
	IP        string            `json:"ip"`
	UserAgent string            `json:"user_agent"`
	Metadata  map[string]string `json:"metadata"`
}

// SystemEventData 系统事件数据
type SystemEventData struct {
	Component   string            `json:"component"`
	Level       string            `json:"level"` // info, warning, error, critical
	Message     string            `json:"message"`
	Details     map[string]interface{} `json:"details"`
	AffectedServices []string     `json:"affected_services"`
}

// BuildEventData 构建事件数据
type BuildEventData struct {
	BuildID     string            `json:"build_id"`
	AppID       string            `json:"app_id"`
	UserID      string            `json:"user_id"`
	Branch      string            `json:"branch"`
	Commit      string            `json:"commit"`
	Status      string            `json:"status"`
	Duration    time.Duration     `json:"duration"`
	LogURL      string            `json:"log_url"`
	ArtifactURL string            `json:"artifact_url"`
}

// ResourceInfo 资源信息
type ResourceInfo struct {
	CPU    string `json:"cpu"`
	Memory string `json:"memory"`
	Disk   string `json:"disk"`
}

// HealthCheckInfo 健康检查信息
type HealthCheckInfo struct {
	Type     string        `json:"type"`
	URL      string        `json:"url"`
	Interval time.Duration `json:"interval"`
	Timeout  time.Duration `json:"timeout"`
	Status   string        `json:"status"`
}

// 事件处理器实现

// LoggingEventHandler 日志事件处理器
type LoggingEventHandler struct {
	logger logger.Logger
	name   string
}

// NewLoggingEventHandler 创建日志事件处理器
func NewLoggingEventHandler(logger logger.Logger) *LoggingEventHandler {
	return &LoggingEventHandler{
		logger: logger,
		name:   "logging-handler",
	}
}

func (h *LoggingEventHandler) Handle(ctx context.Context, event *Event) error {
	h.logger.Info("处理事件", 
		"event_id", event.ID,
		"event_type", event.Type,
		"source", event.Source,
		"subject", event.Subject,
		"timestamp", event.Timestamp)
	
	// 记录事件数据
	if event.Data != nil {
		dataJSON, _ := json.Marshal(event.Data)
		h.logger.Debug("事件数据", "event_id", event.ID, "data", string(dataJSON))
	}
	
	return nil
}

func (h *LoggingEventHandler) GetName() string {
	return h.name
}

func (h *LoggingEventHandler) GetEventTypes() []string {
	return []string{"*"} // 处理所有事件类型
}

// MetricsEventHandler 指标事件处理器
type MetricsEventHandler struct {
	logger logger.Logger
	name   string
}

// NewMetricsEventHandler 创建指标事件处理器
func NewMetricsEventHandler(logger logger.Logger) *MetricsEventHandler {
	return &MetricsEventHandler{
		logger: logger,
		name:   "metrics-handler",
	}
}

func (h *MetricsEventHandler) Handle(ctx context.Context, event *Event) error {
	// 根据事件类型更新不同的指标
	switch event.Type {
	case EventTypeAppCreated, EventTypeAppDeployed:
		h.updateAppMetrics(event)
	case EventTypeContainerCreated, EventTypeContainerStarted:
		h.updateContainerMetrics(event)
	case EventTypeServiceRegistered:
		h.updateServiceMetrics(event)
	case EventTypeResourceExhausted:
		h.updateResourceMetrics(event)
	}
	
	return nil
}

func (h *MetricsEventHandler) updateAppMetrics(event *Event) {
	h.logger.Debug("更新应用指标", "event_type", event.Type, "event_id", event.ID)
	// 这里可以集成Prometheus等指标系统
}

func (h *MetricsEventHandler) updateContainerMetrics(event *Event) {
	h.logger.Debug("更新容器指标", "event_type", event.Type, "event_id", event.ID)
}

func (h *MetricsEventHandler) updateServiceMetrics(event *Event) {
	h.logger.Debug("更新服务指标", "event_type", event.Type, "event_id", event.ID)
}

func (h *MetricsEventHandler) updateResourceMetrics(event *Event) {
	h.logger.Debug("更新资源指标", "event_type", event.Type, "event_id", event.ID)
}

func (h *MetricsEventHandler) GetName() string {
	return h.name
}

func (h *MetricsEventHandler) GetEventTypes() []string {
	return []string{
		EventTypeAppCreated, EventTypeAppDeployed,
		EventTypeContainerCreated, EventTypeContainerStarted,
		EventTypeServiceRegistered,
		EventTypeResourceExhausted,
	}
}

// NotificationEventHandler 通知事件处理器
type NotificationEventHandler struct {
	logger logger.Logger
	name   string
}

// NewNotificationEventHandler 创建通知事件处理器
func NewNotificationEventHandler(logger logger.Logger) *NotificationEventHandler {
	return &NotificationEventHandler{
		logger: logger,
		name:   "notification-handler",
	}
}

func (h *NotificationEventHandler) Handle(ctx context.Context, event *Event) error {
	// 根据事件类型发送不同的通知
	switch event.Type {
	case EventTypeSystemAlert:
		return h.sendAlert(event)
	case EventTypeAppFailed, EventTypeBuildFailed, EventTypeDeployFailed:
		return h.sendFailureNotification(event)
	case EventTypeAppDeployed, EventTypeBuildCompleted, EventTypeDeployCompleted:
		return h.sendSuccessNotification(event)
	}
	
	return nil
}

func (h *NotificationEventHandler) sendAlert(event *Event) error {
	h.logger.Warn("发送系统告警", "event_id", event.ID, "source", event.Source)
	
	// 这里可以集成邮件、短信、Slack等通知渠道
	var alertData SystemEventData
	if err := h.parseEventData(event, &alertData); err != nil {
		return fmt.Errorf("解析告警数据失败: %w", err)
	}
	
	h.logger.Info("系统告警", 
		"component", alertData.Component,
		"level", alertData.Level,
		"message", alertData.Message)
	
	return nil
}

func (h *NotificationEventHandler) sendFailureNotification(event *Event) error {
	h.logger.Error("发送失败通知", "event_type", event.Type, "event_id", event.ID)
	
	// 发送失败通知逻辑
	return nil
}

func (h *NotificationEventHandler) sendSuccessNotification(event *Event) error {
	h.logger.Info("发送成功通知", "event_type", event.Type, "event_id", event.ID)
	
	// 发送成功通知逻辑
	return nil
}

func (h *NotificationEventHandler) parseEventData(event *Event, target interface{}) error {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return err
	}
	
	return json.Unmarshal(dataBytes, target)
}

func (h *NotificationEventHandler) GetName() string {
	return h.name
}

func (h *NotificationEventHandler) GetEventTypes() []string {
	return []string{
		EventTypeSystemAlert,
		EventTypeAppFailed,
		EventTypeBuildFailed,
		EventTypeDeployFailed,
		EventTypeAppDeployed,
		EventTypeBuildCompleted,
		EventTypeDeployCompleted,
	}
}

// AuditEventHandler 审计事件处理器
type AuditEventHandler struct {
	logger logger.Logger
	name   string
}

// NewAuditEventHandler 创建审计事件处理器
func NewAuditEventHandler(logger logger.Logger) *AuditEventHandler {
	return &AuditEventHandler{
		logger: logger,
		name:   "audit-handler",
	}
}

func (h *AuditEventHandler) Handle(ctx context.Context, event *Event) error {
	// 记录审计日志
	auditLog := map[string]interface{}{
		"event_id":   event.ID,
		"event_type": event.Type,
		"source":     event.Source,
		"subject":    event.Subject,
		"timestamp":  event.Timestamp,
		"trace_id":   event.TraceID,
		"data":       event.Data,
		"metadata":   event.Metadata,
	}
	
	auditJSON, _ := json.Marshal(auditLog)
	h.logger.Info("审计日志", "audit", string(auditJSON))
	
	// 这里可以将审计日志写入专门的审计数据库
	return nil
}

func (h *AuditEventHandler) GetName() string {
	return h.name
}

func (h *AuditEventHandler) GetEventTypes() []string {
	return []string{
		EventTypeUserLogin, EventTypeUserLogout,
		EventTypeUserCreated, EventTypeUserUpdated, EventTypeUserDeleted,
		EventTypeAppCreated, EventTypeAppUpdated, EventTypeAppDeleted,
		EventTypeSystemMaintenance,
	}
}

// 事件构建器

// EventBuilder 事件构建器
type EventBuilder struct {
	event *Event
}

// NewEventBuilder 创建事件构建器
func NewEventBuilder() *EventBuilder {
	return &EventBuilder{
		event: &Event{
			Metadata:  make(map[string]interface{}),
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}
}

func (b *EventBuilder) WithType(eventType string) *EventBuilder {
	b.event.Type = eventType
	return b
}

func (b *EventBuilder) WithSource(source string) *EventBuilder {
	b.event.Source = source
	return b
}

func (b *EventBuilder) WithSubject(subject string) *EventBuilder {
	b.event.Subject = subject
	return b
}

func (b *EventBuilder) WithData(data interface{}) *EventBuilder {
	b.event.Data = data
	return b
}

func (b *EventBuilder) WithMetadata(key string, value interface{}) *EventBuilder {
	b.event.Metadata[key] = value
	return b
}

func (b *EventBuilder) WithTraceID(traceID string) *EventBuilder {
	b.event.TraceID = traceID
	return b
}

func (b *EventBuilder) Build() *Event {
	return b.event
}

// 便捷的事件创建函数

// CreateAppEvent 创建应用事件
func CreateAppEvent(eventType, appID, appName, userID string, data *AppEventData) *Event {
	return NewEventBuilder().
		WithType(eventType).
		WithSource("app-manager").
		WithSubject(fmt.Sprintf("app/%s", appID)).
		WithData(data).
		WithMetadata("app_id", appID).
		WithMetadata("app_name", appName).
		WithMetadata("user_id", userID).
		Build()
}

// CreateContainerEvent 创建容器事件
func CreateContainerEvent(eventType, containerID, appID string, data *ContainerEventData) *Event {
	return NewEventBuilder().
		WithType(eventType).
		WithSource("container-manager").
		WithSubject(fmt.Sprintf("container/%s", containerID)).
		WithData(data).
		WithMetadata("container_id", containerID).
		WithMetadata("app_id", appID).
		Build()
}

// CreateSystemEvent 创建系统事件
func CreateSystemEvent(eventType, component string, data *SystemEventData) *Event {
	return NewEventBuilder().
		WithType(eventType).
		WithSource(component).
		WithSubject("system").
		WithData(data).
		WithMetadata("component", component).
		WithMetadata("level", data.Level).
		Build()
}
