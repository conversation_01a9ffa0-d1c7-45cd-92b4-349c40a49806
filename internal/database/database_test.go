package database

import (
	"os"
	"path/filepath"
	"testing"
)

// TestEnsureSQLiteDir 测试 SQLite 目录创建功能
func TestEnsureSQLiteDir(t *testing.T) {
	tests := []struct {
		name    string
		dsn     string
		wantErr bool
		setup   func(t *testing.T) string
		cleanup func(t *testing.T, testDir string)
	}{
		{
			name:    "创建新目录",
			dsn:     "",
			wantErr: false,
			setup: func(t *testing.T) string {
				// 创建临时测试目录
				testDir := filepath.Join(os.TempDir(), "paas_test_db_"+t.Name())
				return testDir
			},
			cleanup: func(t *testing.T, testDir string) {
				os.RemoveAll(testDir)
			},
		},
		{
			name:    "目录已存在",
			dsn:     "",
			wantErr: false,
			setup: func(t *testing.T) string {
				// 创建临时测试目录
				testDir := filepath.Join(os.TempDir(), "paas_test_db_existing_"+t.Name())
				err := os.MkdirAll(testDir, 0755)
				if err != nil {
					t.Fatal(err)
				}
				return testDir
			},
			cleanup: func(t *testing.T, testDir string) {
				os.RemoveAll(testDir)
			},
		},
		{
			name:    "嵌套目录创建",
			dsn:     "",
			wantErr: false,
			setup: func(t *testing.T) string {
				// 创建深层嵌套的测试目录路径
				testDir := filepath.Join(os.TempDir(), "paas_test_db_nested_"+t.Name(), "level1", "level2")
				return testDir
			},
			cleanup: func(t *testing.T, testDir string) {
				// 清理整个测试目录树
				rootDir := filepath.Join(os.TempDir(), "paas_test_db_nested_"+t.Name())
				os.RemoveAll(rootDir)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试环境
			testDir := tt.setup(t)
			defer tt.cleanup(t, testDir)

			// 构造完整的 DSN 路径
			dsn := filepath.Join(testDir, "test.db")

			// 执行测试
			err := ensureSQLiteDir(dsn)

			// 验证结果
			if tt.wantErr {
				if err == nil {
					t.Errorf("期望错误，但没有错误")
				}
			} else {
				if err != nil {
					t.Errorf("不期望错误，但得到错误: %v", err)
				}

				// 验证目录是否创建成功
				dir := filepath.Dir(dsn)
				stat, err := os.Stat(dir)
				if err != nil {
					t.Errorf("目录不存在: %v", err)
				}
				if !stat.IsDir() {
					t.Errorf("路径不是目录")
				}

				// 验证目录权限
				if stat.Mode().Perm() != os.FileMode(0755) {
					t.Errorf("目录权限不正确，期望 0755，得到 %v", stat.Mode().Perm())
				}
			}
		})
	}
}

// TestNewConnectionWithConfig 测试数据库连接配置
func TestNewConnectionWithConfig(t *testing.T) {
	t.Run("不支持的数据库驱动", func(t *testing.T) {
		config := &Config{
			Driver:          "unsupported",
			DSN:             "test.db",
			MaxOpenConns:    10,
			MaxIdleConns:    5,
			ConnMaxLifetime: 3600,
			LogLevel:        "silent",
		}

		// 执行测试
		db, err := NewConnectionWithConfig(config)

		// 验证结果
		if err == nil {
			t.Errorf("期望错误，但没有错误")
		}
		if db != nil {
			t.Errorf("期望 db 为 nil，但不是")
		}
	})
}

// TestParseLogLevel 测试日志级别解析
func TestParseLogLevel(t *testing.T) {
	tests := []struct {
		name     string
		logLevel string
		want     int // logger.LogLevel 的值
	}{
		{
			name:     "silent 级别",
			logLevel: "silent",
			want:     1, // logger.Silent
		},
		{
			name:     "error 级别",
			logLevel: "error",
			want:     2, // logger.Error
		},
		{
			name:     "warn 级别",
			logLevel: "warn",
			want:     3, // logger.Warn
		},
		{
			name:     "info 级别",
			logLevel: "info",
			want:     4, // logger.Info
		},
		{
			name:     "未知级别默认为 warn",
			logLevel: "unknown",
			want:     3, // logger.Warn
		},
		{
			name:     "空字符串默认为 warn",
			logLevel: "",
			want:     3, // logger.Warn
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := parseLogLevel(tt.logLevel)
			if int(got) != tt.want {
				t.Errorf("parseLogLevel(%s) = %d, want %d", tt.logLevel, int(got), tt.want)
			}
		})
	}
}

// TestDatabaseConfigValidation 测试数据库配置验证
func TestDatabaseConfigValidation(t *testing.T) {
	t.Run("有效的 SQLite 配置", func(t *testing.T) {
		config := &Config{
			Driver:          "sqlite",
			DSN:             "./test.db",
			MaxOpenConns:    100,
			MaxIdleConns:    10,
			ConnMaxLifetime: 3600,
			LogLevel:        "warn",
		}

		// 验证配置字段
		if config.Driver == "" {
			t.Error("Driver 不应该为空")
		}
		if config.DSN == "" {
			t.Error("DSN 不应该为空")
		}
		if config.Driver != "sqlite" && config.Driver != "postgres" {
			t.Errorf("不支持的驱动: %s", config.Driver)
		}
	})

	t.Run("无效的驱动", func(t *testing.T) {
		config := &Config{
			Driver:          "mysql",
			DSN:             "test",
			MaxOpenConns:    100,
			MaxIdleConns:    10,
			ConnMaxLifetime: 3600,
			LogLevel:        "warn",
		}

		// 验证这是一个无效的驱动
		if config.Driver == "sqlite" || config.Driver == "postgres" {
			t.Error("这应该是一个无效的驱动")
		}
	})
}
