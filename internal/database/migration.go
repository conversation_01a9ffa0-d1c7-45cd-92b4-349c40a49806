package database

import (
	"fmt"
	"sort"
	"time"

	"gorm.io/gorm"
)

// Migration 迁移接口
type Migration interface {
	Version() string                    // 迁移版本
	Description() string                // 迁移描述
	Up(db *gorm.DB) error              // 升级操作
	Down(db *gorm.DB) error            // 降级操作
}

// MigrationRecord 迁移记录
type MigrationRecord struct {
	Version     string    `gorm:"primaryKey;type:varchar(20)"`
	Description string    `gorm:"type:varchar(255);not null"`
	AppliedAt   time.Time `gorm:"not null;default:CURRENT_TIMESTAMP"`
	Checksum    string    `gorm:"type:varchar(64)"`
}

// TableName 指定表名
func (MigrationRecord) TableName() string {
	return "schema_migrations"
}

// MigrationManager 迁移管理器
type MigrationManager struct {
	db         *gorm.DB
	migrations []Migration
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB) *MigrationManager {
	return &MigrationManager{
		db:         db,
		migrations: make([]Migration, 0),
	}
}

// RegisterMigration 注册迁移
func (m *MigrationManager) RegisterMigration(migration Migration) {
	m.migrations = append(m.migrations, migration)
}

// RegisterMigrations 批量注册迁移
func (m *MigrationManager) RegisterMigrations(migrations ...Migration) {
	m.migrations = append(m.migrations, migrations...)
}

// Migrate 执行迁移
func (m *MigrationManager) Migrate() error {
	// 创建迁移记录表
	if err := m.createMigrationTable(); err != nil {
		return fmt.Errorf("创建迁移记录表失败: %w", err)
	}
	
	// 排序迁移 (按版本号)
	sort.Slice(m.migrations, func(i, j int) bool {
		return m.migrations[i].Version() < m.migrations[j].Version()
	})
	
	// 执行未应用的迁移
	for _, migration := range m.migrations {
		applied, err := m.isMigrationApplied(migration.Version())
		if err != nil {
			return fmt.Errorf("检查迁移状态失败: %w", err)
		}
		
		if !applied {
			if err := m.applyMigration(migration); err != nil {
				return fmt.Errorf("应用迁移 %s 失败: %w", migration.Version(), err)
			}
		}
	}
	
	return nil
}

// Rollback 回滚迁移
func (m *MigrationManager) Rollback(targetVersion string) error {
	// 获取已应用的迁移
	appliedMigrations, err := m.getAppliedMigrations()
	if err != nil {
		return fmt.Errorf("获取已应用迁移失败: %w", err)
	}
	
	// 按版本倒序排列
	sort.Slice(appliedMigrations, func(i, j int) bool {
		return appliedMigrations[i].Version > appliedMigrations[j].Version
	})
	
	// 回滚到目标版本
	for _, record := range appliedMigrations {
		if record.Version <= targetVersion {
			break
		}
		
		// 找到对应的迁移
		migration := m.findMigration(record.Version)
		if migration == nil {
			return fmt.Errorf("找不到版本 %s 的迁移", record.Version)
		}
		
		// 执行回滚
		if err := m.rollbackMigration(migration); err != nil {
			return fmt.Errorf("回滚迁移 %s 失败: %w", record.Version, err)
		}
	}
	
	return nil
}

// GetMigrationStatus 获取迁移状态
func (m *MigrationManager) GetMigrationStatus() ([]*MigrationStatus, error) {
	appliedMigrations, err := m.getAppliedMigrations()
	if err != nil {
		return nil, err
	}
	
	appliedMap := make(map[string]*MigrationRecord)
	for _, record := range appliedMigrations {
		appliedMap[record.Version] = record
	}
	
	var statuses []*MigrationStatus
	for _, migration := range m.migrations {
		status := &MigrationStatus{
			Version:     migration.Version(),
			Description: migration.Description(),
			Applied:     false,
		}
		
		if record, exists := appliedMap[migration.Version()]; exists {
			status.Applied = true
			status.AppliedAt = &record.AppliedAt
		}
		
		statuses = append(statuses, status)
	}
	
	return statuses, nil
}

// createMigrationTable 创建迁移记录表
func (m *MigrationManager) createMigrationTable() error {
	return m.db.AutoMigrate(&MigrationRecord{})
}

// isMigrationApplied 检查迁移是否已应用
func (m *MigrationManager) isMigrationApplied(version string) (bool, error) {
	var count int64
	err := m.db.Model(&MigrationRecord{}).Where("version = ?", version).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// applyMigration 应用迁移
func (m *MigrationManager) applyMigration(migration Migration) error {
	// 在事务中执行迁移
	return m.db.Transaction(func(tx *gorm.DB) error {
		// 执行迁移
		if err := migration.Up(tx); err != nil {
			return fmt.Errorf("执行迁移失败: %w", err)
		}
		
		// 记录迁移
		record := &MigrationRecord{
			Version:     migration.Version(),
			Description: migration.Description(),
			AppliedAt:   time.Now(),
			Checksum:    m.calculateChecksum(migration),
		}
		
		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("记录迁移失败: %w", err)
		}
		
		return nil
	})
}

// rollbackMigration 回滚迁移
func (m *MigrationManager) rollbackMigration(migration Migration) error {
	// 在事务中执行回滚
	return m.db.Transaction(func(tx *gorm.DB) error {
		// 执行回滚
		if err := migration.Down(tx); err != nil {
			return fmt.Errorf("执行回滚失败: %w", err)
		}
		
		// 删除迁移记录
		if err := tx.Delete(&MigrationRecord{}, "version = ?", migration.Version()).Error; err != nil {
			return fmt.Errorf("删除迁移记录失败: %w", err)
		}
		
		return nil
	})
}

// getAppliedMigrations 获取已应用的迁移
func (m *MigrationManager) getAppliedMigrations() ([]*MigrationRecord, error) {
	var records []*MigrationRecord
	err := m.db.Order("version ASC").Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// findMigration 查找迁移
func (m *MigrationManager) findMigration(version string) Migration {
	for _, migration := range m.migrations {
		if migration.Version() == version {
			return migration
		}
	}
	return nil
}

// calculateChecksum 计算迁移校验和
func (m *MigrationManager) calculateChecksum(migration Migration) string {
	// TODO: 实现迁移内容的校验和计算
	return fmt.Sprintf("checksum-%s", migration.Version())
}

// MigrationStatus 迁移状态
type MigrationStatus struct {
	Version     string     `json:"version"`
	Description string     `json:"description"`
	Applied     bool       `json:"applied"`
	AppliedAt   *time.Time `json:"applied_at,omitempty"`
}

// 具体迁移实现示例

// CreateApplicationsTable 创建应用表迁移
type CreateApplicationsTable struct{}

func (m *CreateApplicationsTable) Version() string {
	return "001"
}

func (m *CreateApplicationsTable) Description() string {
	return "创建应用管理相关表"
}

func (m *CreateApplicationsTable) Up(db *gorm.DB) error {
	// 导入应用模型
	// 这里需要导入具体的模型定义
	return db.Exec(`
		CREATE TABLE IF NOT EXISTS applications (
			id VARCHAR(36) PRIMARY KEY,
			name VARCHAR(100) NOT NULL,
			description TEXT,
			language VARCHAR(20) NOT NULL,
			framework VARCHAR(50),
			version VARCHAR(20) NOT NULL,
			git_repo VARCHAR(500),
			git_branch VARCHAR(100) DEFAULT 'main',
			status VARCHAR(20) NOT NULL DEFAULT 'created',
			tenant_id VARCHAR(36) NOT NULL,
			user_id VARCHAR(36) NOT NULL,
			config JSONB,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE UNIQUE INDEX IF NOT EXISTS idx_applications_name_tenant 
		ON applications(name, tenant_id);
		
		CREATE INDEX IF NOT EXISTS idx_applications_tenant_id 
		ON applications(tenant_id);
		
		CREATE INDEX IF NOT EXISTS idx_applications_status 
		ON applications(status);
	`).Error
}

func (m *CreateApplicationsTable) Down(db *gorm.DB) error {
	return db.Exec("DROP TABLE IF EXISTS applications").Error
}

// CreateInstancesTable 创建实例表迁移
type CreateInstancesTable struct{}

func (m *CreateInstancesTable) Version() string {
	return "002"
}

func (m *CreateInstancesTable) Description() string {
	return "创建应用实例表"
}

func (m *CreateInstancesTable) Up(db *gorm.DB) error {
	return db.Exec(`
		CREATE TABLE IF NOT EXISTS app_instances (
			id VARCHAR(36) PRIMARY KEY,
			app_id VARCHAR(36) NOT NULL,
			version VARCHAR(20) NOT NULL,
			status VARCHAR(20) NOT NULL,
			container_id VARCHAR(100),
			image_tag VARCHAR(200),
			port INTEGER NOT NULL,
			internal_ip VARCHAR(45),
			health_status VARCHAR(20) DEFAULT 'unknown',
			last_health_check TIMESTAMP,
			metrics JSONB,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			
			FOREIGN KEY (app_id) REFERENCES applications(id) ON DELETE CASCADE
		);
		
		CREATE INDEX IF NOT EXISTS idx_app_instances_app_id 
		ON app_instances(app_id);
		
		CREATE INDEX IF NOT EXISTS idx_app_instances_status 
		ON app_instances(status);
	`).Error
}

func (m *CreateInstancesTable) Down(db *gorm.DB) error {
	return db.Exec("DROP TABLE IF EXISTS app_instances").Error
}
