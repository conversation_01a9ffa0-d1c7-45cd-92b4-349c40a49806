package database

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"paas-platform/internal/app"

	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Config 数据库配置
type Config struct {
	Driver          string `mapstructure:"driver"`           // sqlite, postgres
	DSN             string `mapstructure:"dsn"`              // 数据源名称
	MaxOpenConns    int    `mapstructure:"max_open_conns"`   // 最大打开连接数
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`   // 最大空闲连接数
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"` // 连接最大生存时间(秒)
	LogLevel        string `mapstructure:"log_level"`        // 日志级别
}

// NewConnection 创建数据库连接
func NewConnection() (*gorm.DB, error) {
	config := &Config{
		Driver:          viper.GetString("database.driver"),
		DSN:             viper.GetString("database.dsn"),
		MaxOpenConns:    viper.GetInt("database.max_open_conns"),
		MaxIdleConns:    viper.GetInt("database.max_idle_conns"),
		ConnMaxLifetime: viper.GetInt("database.conn_max_lifetime"),
		LogLevel:        viper.GetString("database.log_level"),
	}
	
	// 设置默认值
	if config.MaxOpenConns == 0 {
		config.MaxOpenConns = 100
	}
	if config.MaxIdleConns == 0 {
		config.MaxIdleConns = 10
	}
	if config.ConnMaxLifetime == 0 {
		config.ConnMaxLifetime = 3600 // 1小时
	}
	if config.LogLevel == "" {
		config.LogLevel = "warn"
	}
	
	return NewConnectionWithConfig(config)
}

// NewConnectionWithConfig 使用指定配置创建数据库连接
func NewConnectionWithConfig(config *Config) (*gorm.DB, error) {
	var dialector gorm.Dialector

	// 根据驱动类型选择方言
	switch config.Driver {
	case "sqlite":
		// 确保 SQLite 数据库文件的目录存在
		if err := ensureSQLiteDir(config.DSN); err != nil {
			return nil, fmt.Errorf("创建 SQLite 数据库目录失败: %w", err)
		}
		dialector = sqlite.Open(config.DSN)
	case "postgres":
		dialector = postgres.Open(config.DSN)
	default:
		return nil, fmt.Errorf("不支持的数据库驱动: %s", config.Driver)
	}
	
	// 配置 GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(parseLogLevel(config.LogLevel)),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	}
	
	// 创建数据库连接
	db, err := gorm.Open(dialector, gormConfig)
	if err != nil {
		return nil, fmt.Errorf("数据库连接失败: %w", err)
	}
	
	// 获取底层 sql.DB 对象
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %w", err)
	}
	
	// 设置连接池参数
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Duration(config.ConnMaxLifetime) * time.Second)
	
	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}
	
	return db, nil
}

// ensureSQLiteDir 确保 SQLite 数据库文件的目录存在
func ensureSQLiteDir(dsn string) error {
	// 获取数据库文件的目录路径
	dir := filepath.Dir(dsn)

	// 如果目录不存在，创建目录
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录 %s 失败: %w", dir, err)
		}
		fmt.Printf("已创建数据库目录: %s\n", dir)
	}

	return nil
}

// parseLogLevel 解析日志级别
func parseLogLevel(level string) logger.LogLevel {
	switch level {
	case "silent":
		return logger.Silent
	case "error":
		return logger.Error
	case "warn":
		return logger.Warn
	case "info":
		return logger.Info
	default:
		return logger.Warn
	}
}

// AutoMigrate 自动迁移数据库表
func AutoMigrate(db *gorm.DB) error {
	// 迁移应用管理相关表
	err := db.AutoMigrate(
		&app.Application{},
		&app.AppInstance{},
		&app.BuildTask{},
		&app.AppVersion{},
	)
	if err != nil {
		return fmt.Errorf("应用管理表迁移失败: %w", err)
	}
	
	// TODO: 添加其他模块的表迁移
	
	return nil
}

// CreateIndexes 创建数据库索引
func CreateIndexes(db *gorm.DB) error {
	// 应用表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_applications_tenant_id ON applications(tenant_id)").Error; err != nil {
		return fmt.Errorf("创建应用租户索引失败: %w", err)
	}
	
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status)").Error; err != nil {
		return fmt.Errorf("创建应用状态索引失败: %w", err)
	}
	
	// 实例表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_app_instances_app_id ON app_instances(app_id)").Error; err != nil {
		return fmt.Errorf("创建实例应用ID索引失败: %w", err)
	}
	
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_app_instances_status ON app_instances(status)").Error; err != nil {
		return fmt.Errorf("创建实例状态索引失败: %w", err)
	}
	
	// 构建任务表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_build_tasks_app_id ON build_tasks(app_id)").Error; err != nil {
		return fmt.Errorf("创建构建任务应用ID索引失败: %w", err)
	}
	
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_build_tasks_status ON build_tasks(status)").Error; err != nil {
		return fmt.Errorf("创建构建任务状态索引失败: %w", err)
	}
	
	// 版本表索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_app_versions_app_id ON app_versions(app_id)").Error; err != nil {
		return fmt.Errorf("创建版本应用ID索引失败: %w", err)
	}
	
	return nil
}

// HealthCheck 数据库健康检查
func HealthCheck(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库实例失败: %w", err)
	}
	
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接检查失败: %w", err)
	}
	
	return nil
}

// GetStats 获取数据库统计信息
func GetStats(db *gorm.DB) (map[string]interface{}, error) {
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %w", err)
	}
	
	stats := sqlDB.Stats()
	
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}, nil
}

// BackupDatabase 数据库备份
func BackupDatabase(db *gorm.DB, backupPath string) error {
	// TODO: 实现数据库备份逻辑
	// 根据不同的数据库类型实现相应的备份策略
	return fmt.Errorf("数据库备份功能开发中")
}

// Restore 数据库恢复
func Restore(db *gorm.DB, backupPath string) error {
	// TODO: 实现数据库恢复逻辑
	// 根据不同的数据库类型实现相应的恢复策略
	return fmt.Errorf("数据库恢复功能开发中")
}
