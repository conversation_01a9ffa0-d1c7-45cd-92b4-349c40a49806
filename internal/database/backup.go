package database

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BackupService 备份服务
type BackupService struct {
	db      *gorm.DB
	adapter DatabaseAdapter
	config  BackupConfig
	logger  Logger
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
}

// BackupConfig 备份配置
type BackupConfig struct {
	Type        string        `json:"type"`         // full, incremental
	Schedule    string        `json:"schedule"`     // cron 表达式
	Retention   int           `json:"retention"`    // 保留天数
	Compression string        `json:"compression"`  // gzip, lz4, none
	Encryption  string        `json:"encryption"`   // aes256, none
	Storage     StorageConfig `json:"storage"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type      string `json:"type"`       // local, s3, oss
	Path      string `json:"path"`       // 存储路径
	Bucket    string `json:"bucket"`     // 存储桶
	Region    string `json:"region"`     // 区域
	AccessKey string `json:"access_key"` // 访问密钥
	SecretKey string `json:"secret_key"` // 密钥
	Endpoint  string `json:"endpoint"`   // 端点
}

// Backup 备份记录
type Backup struct {
	ID          string      `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string      `json:"name" gorm:"type:varchar(100);not null"`
	Type        string      `json:"type" gorm:"type:varchar(20);not null"`        // full, incremental
	Status      BackupStatus `json:"status" gorm:"type:varchar(20);not null"`
	Size        int64       `json:"size"`                                         // 备份大小 (字节)
	Path        string      `json:"path" gorm:"type:varchar(500)"`                // 备份文件路径
	Checksum    string      `json:"checksum" gorm:"type:varchar(64)"`             // 文件校验和
	StartTime   time.Time   `json:"start_time" gorm:"autoCreateTime"`
	EndTime     *time.Time  `json:"end_time"`
	Duration    int         `json:"duration"`                                     // 备份时长 (秒)
	ErrorMsg    string      `json:"error_msg" gorm:"type:text"`
	Metadata    BackupMetadata `json:"metadata" gorm:"type:jsonb"`
	CreatedBy   string      `json:"created_by" gorm:"type:varchar(36)"`           // 创建者用户ID
	TenantID    string      `json:"tenant_id" gorm:"type:varchar(36);not null;index"`
}

// BackupStatus 备份状态
type BackupStatus string

const (
	BackupStatusPending  BackupStatus = "pending"  // 等待中
	BackupStatusRunning  BackupStatus = "running"  // 运行中
	BackupStatusSuccess  BackupStatus = "success"  // 成功
	BackupStatusFailed   BackupStatus = "failed"   // 失败
	BackupStatusCanceled BackupStatus = "canceled" // 已取消
)

// BackupMetadata 备份元数据
type BackupMetadata struct {
	DatabaseDriver  string            `json:"database_driver"`
	DatabaseVersion string            `json:"database_version"`
	PlatformVersion string            `json:"platform_version"`
	TableCount      int               `json:"table_count"`
	RecordCount     map[string]int64  `json:"record_count"`
	Compression     string            `json:"compression"`
	Encryption      string            `json:"encryption"`
}

// TableName 指定表名
func (Backup) TableName() string {
	return "backups"
}

// NewBackupService 创建备份服务
func NewBackupService(db *gorm.DB, adapter DatabaseAdapter, config BackupConfig, logger Logger) *BackupService {
	return &BackupService{
		db:      db,
		adapter: adapter,
		config:  config,
		logger:  logger,
	}
}

// CreateBackup 创建备份
func (s *BackupService) CreateBackup(ctx context.Context, name string, tenantID string, userID string) (*Backup, error) {
	// 创建备份记录
	backup := &Backup{
		ID:        uuid.New().String(),
		Name:      name,
		Type:      s.config.Type,
		Status:    BackupStatusPending,
		StartTime: time.Now(),
		CreatedBy: userID,
		TenantID:  tenantID,
	}
	
	// 保存备份记录
	if err := s.db.Create(backup).Error; err != nil {
		return nil, fmt.Errorf("创建备份记录失败: %w", err)
	}
	
	// 异步执行备份
	go s.executeBackup(ctx, backup)
	
	s.logger.Info("备份任务创建成功", "backup_id", backup.ID, "name", name)
	return backup, nil
}

// executeBackup 执行备份
func (s *BackupService) executeBackup(ctx context.Context, backup *Backup) {
	// 更新状态为运行中
	s.updateBackupStatus(backup.ID, BackupStatusRunning, "")
	
	// 生成备份文件路径
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("%s_%s_%s.sql", backup.Name, backup.Type, timestamp)
	backupPath := filepath.Join(s.config.Storage.Path, filename)
	
	// 确保备份目录存在
	if err := os.MkdirAll(s.config.Storage.Path, 0755); err != nil {
		s.updateBackupStatus(backup.ID, BackupStatusFailed, fmt.Sprintf("创建备份目录失败: %v", err))
		return
	}
	
	// 执行数据库备份
	if err := s.adapter.Backup(s.getDatabaseName(), backupPath); err != nil {
		s.updateBackupStatus(backup.ID, BackupStatusFailed, fmt.Sprintf("数据库备份失败: %v", err))
		return
	}
	
	// 获取备份文件信息
	fileInfo, err := os.Stat(backupPath)
	if err != nil {
		s.updateBackupStatus(backup.ID, BackupStatusFailed, fmt.Sprintf("获取备份文件信息失败: %v", err))
		return
	}
	
	// 计算校验和
	checksum, err := s.calculateFileChecksum(backupPath)
	if err != nil {
		s.logger.Warn("计算备份文件校验和失败", "error", err, "backup_id", backup.ID)
	}
	
	// 收集元数据
	metadata, err := s.collectBackupMetadata()
	if err != nil {
		s.logger.Warn("收集备份元数据失败", "error", err, "backup_id", backup.ID)
	}
	
	// 更新备份记录
	endTime := time.Now()
	duration := int(endTime.Sub(backup.StartTime).Seconds())
	
	updates := map[string]interface{}{
		"status":   BackupStatusSuccess,
		"size":     fileInfo.Size(),
		"path":     backupPath,
		"checksum": checksum,
		"end_time": &endTime,
		"duration": duration,
		"metadata": metadata,
	}
	
	if err := s.db.Model(backup).Updates(updates).Error; err != nil {
		s.logger.Error("更新备份记录失败", "error", err, "backup_id", backup.ID)
		return
	}
	
	s.logger.Info("备份执行完成", "backup_id", backup.ID, "size", fileInfo.Size(), "duration", duration)
}

// RestoreFromBackup 从备份恢复
func (s *BackupService) RestoreFromBackup(ctx context.Context, backupID string) error {
	// 获取备份记录
	var backup Backup
	err := s.db.First(&backup, "id = ?", backupID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("备份记录不存在: %s", backupID)
		}
		return fmt.Errorf("获取备份记录失败: %w", err)
	}
	
	// 检查备份状态
	if backup.Status != BackupStatusSuccess {
		return fmt.Errorf("备份状态异常，无法恢复: %s", backup.Status)
	}
	
	// 检查备份文件是否存在
	if _, err := os.Stat(backup.Path); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", backup.Path)
	}
	
	// 验证备份文件完整性
	if backup.Checksum != "" {
		currentChecksum, err := s.calculateFileChecksum(backup.Path)
		if err != nil {
			return fmt.Errorf("计算备份文件校验和失败: %w", err)
		}
		
		if currentChecksum != backup.Checksum {
			return fmt.Errorf("备份文件校验和不匹配，文件可能已损坏")
		}
	}
	
	// 执行恢复
	if err := s.adapter.Restore(s.getDatabaseName(), backup.Path); err != nil {
		return fmt.Errorf("数据库恢复失败: %w", err)
	}
	
	s.logger.Info("数据库恢复完成", "backup_id", backupID, "backup_name", backup.Name)
	return nil
}

// ListBackups 获取备份列表
func (s *BackupService) ListBackups(ctx context.Context, tenantID string) ([]*Backup, error) {
	var backups []*Backup
	err := s.db.Where("tenant_id = ?", tenantID).Order("start_time DESC").Find(&backups).Error
	if err != nil {
		return nil, fmt.Errorf("获取备份列表失败: %w", err)
	}
	return backups, nil
}

// DeleteBackup 删除备份
func (s *BackupService) DeleteBackup(ctx context.Context, backupID string) error {
	// 获取备份记录
	var backup Backup
	err := s.db.First(&backup, "id = ?", backupID).Error
	if err != nil {
		return fmt.Errorf("获取备份记录失败: %w", err)
	}
	
	// 删除备份文件
	if backup.Path != "" {
		if err := os.Remove(backup.Path); err != nil && !os.IsNotExist(err) {
			s.logger.Warn("删除备份文件失败", "error", err, "path", backup.Path)
		}
	}
	
	// 删除备份记录
	if err := s.db.Delete(&backup).Error; err != nil {
		return fmt.Errorf("删除备份记录失败: %w", err)
	}
	
	s.logger.Info("备份删除成功", "backup_id", backupID)
	return nil
}

// CleanupExpiredBackups 清理过期备份
func (s *BackupService) CleanupExpiredBackups(ctx context.Context) error {
	if s.config.Retention <= 0 {
		return nil // 不清理
	}
	
	// 计算过期时间
	expireTime := time.Now().AddDate(0, 0, -s.config.Retention)
	
	// 查找过期备份
	var expiredBackups []*Backup
	err := s.db.Where("start_time < ? AND status = ?", expireTime, BackupStatusSuccess).Find(&expiredBackups).Error
	if err != nil {
		return fmt.Errorf("查找过期备份失败: %w", err)
	}
	
	// 删除过期备份
	for _, backup := range expiredBackups {
		if err := s.DeleteBackup(ctx, backup.ID); err != nil {
			s.logger.Error("删除过期备份失败", "error", err, "backup_id", backup.ID)
			continue
		}
	}
	
	s.logger.Info("过期备份清理完成", "count", len(expiredBackups))
	return nil
}

// updateBackupStatus 更新备份状态
func (s *BackupService) updateBackupStatus(backupID string, status BackupStatus, errorMsg string) {
	updates := map[string]interface{}{
		"status": status,
	}
	
	if errorMsg != "" {
		updates["error_msg"] = errorMsg
	}
	
	if status == BackupStatusSuccess || status == BackupStatusFailed || status == BackupStatusCanceled {
		endTime := time.Now()
		updates["end_time"] = &endTime
		
		// 计算时长
		var backup Backup
		if err := s.db.First(&backup, "id = ?", backupID).Error; err == nil {
			duration := int(endTime.Sub(backup.StartTime).Seconds())
			updates["duration"] = duration
		}
	}
	
	if err := s.db.Model(&Backup{}).Where("id = ?", backupID).Updates(updates).Error; err != nil {
		s.logger.Error("更新备份状态失败", "error", err, "backup_id", backupID)
	}
}

// getDatabaseName 获取数据库名称
func (s *BackupService) getDatabaseName() string {
	// TODO: 从 DSN 中解析数据库名称
	return "paas_platform"
}

// calculateFileChecksum 计算文件校验和
func (s *BackupService) calculateFileChecksum(filePath string) (string, error) {
	// TODO: 实现文件校验和计算 (MD5, SHA256)
	return fmt.Sprintf("checksum-%d", time.Now().Unix()), nil
}

// collectBackupMetadata 收集备份元数据
func (s *BackupService) collectBackupMetadata() (BackupMetadata, error) {
	metadata := BackupMetadata{
		DatabaseDriver:  s.adapter.GetDriverName(),
		PlatformVersion: "1.0.0",
		RecordCount:     make(map[string]int64),
		Compression:     s.config.Compression,
		Encryption:      s.config.Encryption,
	}
	
	// 统计各表记录数
	tables := []string{
		"applications", "app_instances", "build_tasks", "app_versions",
		"pipelines", "builds", "build_stages", "build_steps",
		"deployments", "environments", "users", "roles", "tenants",
	}
	
	for _, table := range tables {
		var count int64
		if err := s.db.Table(table).Count(&count).Error; err != nil {
			s.logger.Warn("统计表记录数失败", "table", table, "error", err)
			continue
		}
		metadata.RecordCount[table] = count
	}
	
	metadata.TableCount = len(tables)
	
	return metadata, nil
}

// ScheduledBackupService 定时备份服务
type ScheduledBackupService struct {
	backupService *BackupService
	logger        Logger
	stopChan      chan struct{}
}

// NewScheduledBackupService 创建定时备份服务
func NewScheduledBackupService(backupService *BackupService, logger Logger) *ScheduledBackupService {
	return &ScheduledBackupService{
		backupService: backupService,
		logger:        logger,
		stopChan:      make(chan struct{}),
	}
}

// Start 启动定时备份
func (s *ScheduledBackupService) Start(ctx context.Context) {
	// TODO: 实现基于 cron 表达式的定时备份
	ticker := time.NewTicker(24 * time.Hour) // 每天执行一次
	defer ticker.Stop()
	
	s.logger.Info("定时备份服务启动")
	
	for {
		select {
		case <-ctx.Done():
			s.logger.Info("定时备份服务停止")
			return
		case <-s.stopChan:
			s.logger.Info("定时备份服务停止")
			return
		case <-ticker.C:
			s.performScheduledBackup(ctx)
		}
	}
}

// Stop 停止定时备份
func (s *ScheduledBackupService) Stop() {
	close(s.stopChan)
}

// performScheduledBackup 执行定时备份
func (s *ScheduledBackupService) performScheduledBackup(ctx context.Context) {
	// 生成备份名称
	name := fmt.Sprintf("scheduled_%s", time.Now().Format("20060102_150405"))
	
	// 创建备份 (系统备份，使用默认租户)
	_, err := s.backupService.CreateBackup(ctx, name, "system", "system")
	if err != nil {
		s.logger.Error("定时备份失败", "error", err, "name", name)
		return
	}
	
	// 清理过期备份
	if err := s.backupService.CleanupExpiredBackups(ctx); err != nil {
		s.logger.Error("清理过期备份失败", "error", err)
	}
	
	s.logger.Info("定时备份完成", "name", name)
}

// RestoreService 恢复服务
type RestoreService struct {
	db      *gorm.DB
	adapter DatabaseAdapter
	logger  Logger
}

// NewRestoreService 创建恢复服务
func NewRestoreService(db *gorm.DB, adapter DatabaseAdapter, logger Logger) *RestoreService {
	return &RestoreService{
		db:      db,
		adapter: adapter,
		logger:  logger,
	}
}

// RestoreFromBackup 从备份恢复
func (s *RestoreService) RestoreFromBackup(ctx context.Context, backupID string) error {
	// 获取备份记录
	var backup Backup
	err := s.db.First(&backup, "id = ?", backupID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("备份记录不存在: %s", backupID)
		}
		return fmt.Errorf("获取备份记录失败: %w", err)
	}
	
	// 验证备份
	if err := s.validateBackup(&backup); err != nil {
		return fmt.Errorf("备份验证失败: %w", err)
	}
	
	// 执行恢复
	if err := s.adapter.Restore(s.getDatabaseName(), backup.Path); err != nil {
		return fmt.Errorf("数据库恢复失败: %w", err)
	}
	
	s.logger.Info("数据库恢复完成", "backup_id", backupID, "backup_name", backup.Name)
	return nil
}

// validateBackup 验证备份
func (s *RestoreService) validateBackup(backup *Backup) error {
	// 检查备份状态
	if backup.Status != BackupStatusSuccess {
		return fmt.Errorf("备份状态异常: %s", backup.Status)
	}
	
	// 检查备份文件是否存在
	if _, err := os.Stat(backup.Path); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", backup.Path)
	}
	
	// 验证文件校验和
	if backup.Checksum != "" {
		currentChecksum, err := s.calculateFileChecksum(backup.Path)
		if err != nil {
			return fmt.Errorf("计算文件校验和失败: %w", err)
		}
		
		if currentChecksum != backup.Checksum {
			return fmt.Errorf("备份文件校验和不匹配，文件可能已损坏")
		}
	}
	
	return nil
}

// getDatabaseName 获取数据库名称
func (s *RestoreService) getDatabaseName() string {
	// TODO: 从配置或 DSN 中解析数据库名称
	return "paas_platform"
}

// calculateFileChecksum 计算文件校验和
func (s *RestoreService) calculateFileChecksum(filePath string) (string, error) {
	// TODO: 实现文件校验和计算
	return fmt.Sprintf("checksum-%d", time.Now().Unix()), nil
}
