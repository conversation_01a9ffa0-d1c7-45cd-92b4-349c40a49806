package database

import (
	"context"
	"fmt"
	"reflect"

	"gorm.io/gorm"
)

// BaseRepository 基础仓库接口
type BaseRepository[T any] interface {
	// 基础 CRUD 操作
	Create(ctx context.Context, entity *T) error
	GetByID(ctx context.Context, id string) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id string) error
	
	// 查询操作
	List(ctx context.Context, filter Filter) ([]*T, error)
	Count(ctx context.Context, filter Filter) (int64, error)
	Exists(ctx context.Context, filter Filter) (bool, error)
	
	// 批量操作
	BatchCreate(ctx context.Context, entities []*T) error
	BatchUpdate(ctx context.Context, entities []*T) error
	BatchDelete(ctx context.Context, ids []string) error
	
	// 事务操作
	WithTx(tx *gorm.DB) BaseRepository[T]
}

// Filter 查询过滤器接口
type Filter interface {
	Apply(db *gorm.DB) *gorm.DB
}

// repository 基础仓库实现
type repository[T any] struct {
	db    *gorm.DB
	model T
}

// NewRepository 创建基础仓库
func NewRepository[T any](db *gorm.DB) BaseRepository[T] {
	var model T
	return &repository[T]{
		db:    db,
		model: model,
	}
}

// Create 创建实体
func (r *repository[T]) Create(ctx context.Context, entity *T) error {
	if err := r.db.WithContext(ctx).Create(entity).Error; err != nil {
		return fmt.Errorf("创建实体失败: %w", err)
	}
	return nil
}

// GetByID 根据ID获取实体
func (r *repository[T]) GetByID(ctx context.Context, id string) (*T, error) {
	var entity T
	err := r.db.WithContext(ctx).First(&entity, "id = ?", id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("实体不存在: %s", id)
		}
		return nil, fmt.Errorf("获取实体失败: %w", err)
	}
	return &entity, nil
}

// Update 更新实体
func (r *repository[T]) Update(ctx context.Context, entity *T) error {
	if err := r.db.WithContext(ctx).Save(entity).Error; err != nil {
		return fmt.Errorf("更新实体失败: %w", err)
	}
	return nil
}

// Delete 删除实体 (软删除)
func (r *repository[T]) Delete(ctx context.Context, id string) error {
	if err := r.db.WithContext(ctx).Delete(&r.model, "id = ?", id).Error; err != nil {
		return fmt.Errorf("删除实体失败: %w", err)
	}
	return nil
}

// List 获取实体列表
func (r *repository[T]) List(ctx context.Context, filter Filter) ([]*T, error) {
	var entities []*T
	query := r.db.WithContext(ctx).Model(&r.model)
	
	if filter != nil {
		query = filter.Apply(query)
	}
	
	if err := query.Find(&entities).Error; err != nil {
		return nil, fmt.Errorf("获取实体列表失败: %w", err)
	}
	
	return entities, nil
}

// Count 统计实体数量
func (r *repository[T]) Count(ctx context.Context, filter Filter) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&r.model)
	
	if filter != nil {
		query = filter.Apply(query)
	}
	
	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("统计实体数量失败: %w", err)
	}
	
	return count, nil
}

// Exists 检查实体是否存在
func (r *repository[T]) Exists(ctx context.Context, filter Filter) (bool, error) {
	count, err := r.Count(ctx, filter)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// BatchCreate 批量创建实体
func (r *repository[T]) BatchCreate(ctx context.Context, entities []*T) error {
	if len(entities) == 0 {
		return nil
	}
	
	// 使用批量插入提高性能
	if err := r.db.WithContext(ctx).CreateInBatches(entities, 100).Error; err != nil {
		return fmt.Errorf("批量创建实体失败: %w", err)
	}
	
	return nil
}

// BatchUpdate 批量更新实体
func (r *repository[T]) BatchUpdate(ctx context.Context, entities []*T) error {
	if len(entities) == 0 {
		return nil
	}
	
	// 在事务中执行批量更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, entity := range entities {
			if err := tx.Save(entity).Error; err != nil {
				return fmt.Errorf("批量更新实体失败: %w", err)
			}
		}
		return nil
	})
}

// BatchDelete 批量删除实体
func (r *repository[T]) BatchDelete(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}
	
	if err := r.db.WithContext(ctx).Delete(&r.model, "id IN ?", ids).Error; err != nil {
		return fmt.Errorf("批量删除实体失败: %w", err)
	}
	
	return nil
}

// WithTx 使用事务
func (r *repository[T]) WithTx(tx *gorm.DB) BaseRepository[T] {
	return &repository[T]{
		db:    tx,
		model: r.model,
	}
}

// QueryBuilder 查询构建器
type QueryBuilder struct {
	db     *gorm.DB
	model  interface{}
	wheres []WhereClause
	orders []OrderClause
	limit  int
	offset int
	preloads []string
}

// WhereClause WHERE 条件
type WhereClause struct {
	Column   string
	Operator string
	Value    interface{}
}

// OrderClause 排序条件
type OrderClause struct {
	Column string
	Desc   bool
}

// NewQueryBuilder 创建查询构建器
func NewQueryBuilder(db *gorm.DB, model interface{}) *QueryBuilder {
	return &QueryBuilder{
		db:    db,
		model: model,
	}
}

// Where 添加 WHERE 条件
func (qb *QueryBuilder) Where(column, operator string, value interface{}) *QueryBuilder {
	qb.wheres = append(qb.wheres, WhereClause{
		Column:   column,
		Operator: operator,
		Value:    value,
	})
	return qb
}

// OrderBy 添加排序条件
func (qb *QueryBuilder) OrderBy(column string, desc bool) *QueryBuilder {
	qb.orders = append(qb.orders, OrderClause{
		Column: column,
		Desc:   desc,
	})
	return qb
}

// Limit 设置查询限制
func (qb *QueryBuilder) Limit(limit int) *QueryBuilder {
	qb.limit = limit
	return qb
}

// Offset 设置查询偏移
func (qb *QueryBuilder) Offset(offset int) *QueryBuilder {
	qb.offset = offset
	return qb
}

// Preload 预加载关联
func (qb *QueryBuilder) Preload(association string) *QueryBuilder {
	qb.preloads = append(qb.preloads, association)
	return qb
}

// Build 构建查询
func (qb *QueryBuilder) Build() *gorm.DB {
	query := qb.db.Model(qb.model)
	
	// 应用 WHERE 条件
	for _, where := range qb.wheres {
		switch where.Operator {
		case "=":
			query = query.Where(fmt.Sprintf("%s = ?", where.Column), where.Value)
		case "!=":
			query = query.Where(fmt.Sprintf("%s != ?", where.Column), where.Value)
		case ">":
			query = query.Where(fmt.Sprintf("%s > ?", where.Column), where.Value)
		case ">=":
			query = query.Where(fmt.Sprintf("%s >= ?", where.Column), where.Value)
		case "<":
			query = query.Where(fmt.Sprintf("%s < ?", where.Column), where.Value)
		case "<=":
			query = query.Where(fmt.Sprintf("%s <= ?", where.Column), where.Value)
		case "LIKE":
			query = query.Where(fmt.Sprintf("%s LIKE ?", where.Column), where.Value)
		case "IN":
			query = query.Where(fmt.Sprintf("%s IN ?", where.Column), where.Value)
		case "NOT IN":
			query = query.Where(fmt.Sprintf("%s NOT IN ?", where.Column), where.Value)
		case "IS NULL":
			query = query.Where(fmt.Sprintf("%s IS NULL", where.Column))
		case "IS NOT NULL":
			query = query.Where(fmt.Sprintf("%s IS NOT NULL", where.Column))
		}
	}
	
	// 应用排序条件
	for _, order := range qb.orders {
		if order.Desc {
			query = query.Order(fmt.Sprintf("%s DESC", order.Column))
		} else {
			query = query.Order(fmt.Sprintf("%s ASC", order.Column))
		}
	}
	
	// 应用预加载
	for _, preload := range qb.preloads {
		query = query.Preload(preload)
	}
	
	// 应用分页
	if qb.limit > 0 {
		query = query.Limit(qb.limit)
	}
	if qb.offset > 0 {
		query = query.Offset(qb.offset)
	}
	
	return query
}

// Find 执行查询并返回结果
func (qb *QueryBuilder) Find() (interface{}, error) {
	query := qb.Build()
	
	// 创建结果切片
	modelType := reflect.TypeOf(qb.model)
	sliceType := reflect.SliceOf(reflect.PtrTo(modelType))
	results := reflect.New(sliceType).Interface()
	
	if err := query.Find(results).Error; err != nil {
		return nil, fmt.Errorf("查询执行失败: %w", err)
	}
	
	return results, nil
}

// First 获取第一个结果
func (qb *QueryBuilder) First() (interface{}, error) {
	query := qb.Build()
	
	// 创建结果对象
	modelType := reflect.TypeOf(qb.model)
	result := reflect.New(modelType).Interface()
	
	if err := query.First(result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("记录不存在")
		}
		return nil, fmt.Errorf("查询执行失败: %w", err)
	}
	
	return result, nil
}

// Count 统计数量
func (qb *QueryBuilder) Count() (int64, error) {
	query := qb.Build()
	
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("统计查询失败: %w", err)
	}
	
	return count, nil
}
