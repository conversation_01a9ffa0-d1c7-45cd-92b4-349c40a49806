package database

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// DatabaseAdapter 数据库适配器接口
type DatabaseAdapter interface {
	Connect(dsn string) (*gorm.DB, error)
	GetDialector(dsn string) gorm.Dialector
	GetDefaultPort() int
	GetDriverName() string
	SupportFeatures() []string
	
	// 数据库特定操作
	CreateDatabase(name string) error
	DropDatabase(name string) error
	ListDatabases() ([]string, error)
	
	// 备份恢复
	Backup(dbName, backupPath string) error
	Restore(dbName, backupPath string) error
}

// SQLiteAdapter SQLite 适配器
type SQLiteAdapter struct {
	dsn string
}

// NewSQLiteAdapter 创建 SQLite 适配器
func NewSQLiteAdapter() DatabaseAdapter {
	return &SQLiteAdapter{}
}

// Connect 连接 SQLite 数据库
func (a *SQLiteAdapter) Connect(dsn string) (*gorm.DB, error) {
	a.dsn = dsn
	
	// 确保数据库目录存在
	dir := filepath.Dir(dsn)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建数据库目录失败: %w", err)
	}
	
	db, err := gorm.Open(sqlite.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("SQLite 连接失败: %w", err)
	}
	
	// 启用外键约束
	if err := db.Exec("PRAGMA foreign_keys = ON").Error; err != nil {
		return nil, fmt.Errorf("启用外键约束失败: %w", err)
	}
	
	// 设置 WAL 模式提高并发性能
	if err := db.Exec("PRAGMA journal_mode = WAL").Error; err != nil {
		return nil, fmt.Errorf("设置 WAL 模式失败: %w", err)
	}
	
	return db, nil
}

// GetDialector 获取 SQLite 方言
func (a *SQLiteAdapter) GetDialector(dsn string) gorm.Dialector {
	return sqlite.Open(dsn)
}

// GetDefaultPort 获取默认端口
func (a *SQLiteAdapter) GetDefaultPort() int {
	return 0 // SQLite 不使用端口
}

// GetDriverName 获取驱动名称
func (a *SQLiteAdapter) GetDriverName() string {
	return "sqlite"
}

// SupportFeatures 支持的功能
func (a *SQLiteAdapter) SupportFeatures() []string {
	return []string{
		"transactions",
		"foreign_keys",
		"json",
		"full_text_search",
		"wal_mode",
	}
}

// CreateDatabase 创建数据库 (SQLite 自动创建)
func (a *SQLiteAdapter) CreateDatabase(name string) error {
	// SQLite 在连接时自动创建数据库文件
	return nil
}

// DropDatabase 删除数据库
func (a *SQLiteAdapter) DropDatabase(name string) error {
	if err := os.Remove(name); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("删除 SQLite 数据库失败: %w", err)
	}
	return nil
}

// ListDatabases 列出数据库 (SQLite 不适用)
func (a *SQLiteAdapter) ListDatabases() ([]string, error) {
	return []string{a.dsn}, nil
}

// Backup 备份 SQLite 数据库
func (a *SQLiteAdapter) Backup(dbName, backupPath string) error {
	// 确保备份目录存在
	dir := filepath.Dir(backupPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}
	
	// 复制数据库文件
	cmd := exec.Command("cp", dbName, backupPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("SQLite 备份失败: %w", err)
	}
	
	return nil
}

// Restore 恢复 SQLite 数据库
func (a *SQLiteAdapter) Restore(dbName, backupPath string) error {
	// 检查备份文件是否存在
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", backupPath)
	}
	
	// 复制备份文件
	cmd := exec.Command("cp", backupPath, dbName)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("SQLite 恢复失败: %w", err)
	}
	
	return nil
}

// PostgreSQLAdapter PostgreSQL 适配器
type PostgreSQLAdapter struct {
	dsn string
}

// NewPostgreSQLAdapter 创建 PostgreSQL 适配器
func NewPostgreSQLAdapter() DatabaseAdapter {
	return &PostgreSQLAdapter{}
}

// Connect 连接 PostgreSQL 数据库
func (a *PostgreSQLAdapter) Connect(dsn string) (*gorm.DB, error) {
	a.dsn = dsn
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("PostgreSQL 连接失败: %w", err)
	}
	
	// 设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取 SQL DB 失败: %w", err)
	}
	
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetConnMaxLifetime(time.Hour)
	
	return db, nil
}

// GetDialector 获取 PostgreSQL 方言
func (a *PostgreSQLAdapter) GetDialector(dsn string) gorm.Dialector {
	return postgres.Open(dsn)
}

// GetDefaultPort 获取默认端口
func (a *PostgreSQLAdapter) GetDefaultPort() int {
	return 5432
}

// GetDriverName 获取驱动名称
func (a *PostgreSQLAdapter) GetDriverName() string {
	return "postgres"
}

// SupportFeatures 支持的功能
func (a *PostgreSQLAdapter) SupportFeatures() []string {
	return []string{
		"transactions",
		"foreign_keys",
		"json",
		"jsonb",
		"arrays",
		"full_text_search",
		"partial_indexes",
		"concurrent_indexes",
		"extensions",
	}
}

// CreateDatabase 创建 PostgreSQL 数据库
func (a *PostgreSQLAdapter) CreateDatabase(name string) error {
	// TODO: 实现 PostgreSQL 数据库创建逻辑
	return fmt.Errorf("PostgreSQL 数据库创建功能开发中")
}

// DropDatabase 删除 PostgreSQL 数据库
func (a *PostgreSQLAdapter) DropDatabase(name string) error {
	// TODO: 实现 PostgreSQL 数据库删除逻辑
	return fmt.Errorf("PostgreSQL 数据库删除功能开发中")
}

// ListDatabases 列出 PostgreSQL 数据库
func (a *PostgreSQLAdapter) ListDatabases() ([]string, error) {
	// TODO: 实现 PostgreSQL 数据库列表功能
	return nil, fmt.Errorf("PostgreSQL 数据库列表功能开发中")
}

// Backup 备份 PostgreSQL 数据库
func (a *PostgreSQLAdapter) Backup(dbName, backupPath string) error {
	// 使用 pg_dump 进行备份
	cmd := exec.Command("pg_dump", "-h", "localhost", "-U", "paas", "-d", dbName, "-f", backupPath)
	cmd.Env = append(os.Environ(), "PGPASSWORD=paas123")
	
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("PostgreSQL 备份失败: %w", err)
	}
	
	return nil
}

// Restore 恢复 PostgreSQL 数据库
func (a *PostgreSQLAdapter) Restore(dbName, backupPath string) error {
	// 使用 psql 进行恢复
	cmd := exec.Command("psql", "-h", "localhost", "-U", "paas", "-d", dbName, "-f", backupPath)
	cmd.Env = append(os.Environ(), "PGPASSWORD=paas123")
	
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("PostgreSQL 恢复失败: %w", err)
	}
	
	return nil
}

// DatabaseFactory 数据库工厂
type DatabaseFactory struct {
	adapters map[string]DatabaseAdapter
}

// NewDatabaseFactory 创建数据库工厂
func NewDatabaseFactory() *DatabaseFactory {
	factory := &DatabaseFactory{
		adapters: make(map[string]DatabaseAdapter),
	}
	
	// 注册默认适配器
	factory.RegisterAdapter("sqlite", NewSQLiteAdapter())
	factory.RegisterAdapter("postgres", NewPostgreSQLAdapter())
	
	return factory
}

// RegisterAdapter 注册数据库适配器
func (f *DatabaseFactory) RegisterAdapter(driver string, adapter DatabaseAdapter) {
	f.adapters[driver] = adapter
}

// GetAdapter 获取数据库适配器
func (f *DatabaseFactory) GetAdapter(driver string) (DatabaseAdapter, error) {
	adapter, exists := f.adapters[driver]
	if !exists {
		return nil, fmt.Errorf("不支持的数据库驱动: %s", driver)
	}
	return adapter, nil
}

// CreateConnection 创建数据库连接
func (f *DatabaseFactory) CreateConnection(driver, dsn string) (*gorm.DB, error) {
	adapter, err := f.GetAdapter(driver)
	if err != nil {
		return nil, err
	}
	
	return adapter.Connect(dsn)
}

// ListSupportedDrivers 列出支持的数据库驱动
func (f *DatabaseFactory) ListSupportedDrivers() []string {
	drivers := make([]string, 0, len(f.adapters))
	for driver := range f.adapters {
		drivers = append(drivers, driver)
	}
	return drivers
}
