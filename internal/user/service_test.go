package user

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"paas-platform/internal/testing"
	"paas-platform/pkg/logger"
)

// UserServiceTestSuite 用户服务测试套件
type UserServiceTestSuite struct {
	testing.TestFramework
	service    UserService
	mockRepo   *MockUserRepository
	mockLogger *testing.MockLogger
}

// SetupSuite 测试套件初始化
func (suite *UserServiceTestSuite) SetupSuite() {
	suite.TestFramework.SetupSuite()
	
	// 自动迁移用户相关表
	err := suite.DB.AutoMigrate(
		&User{},
		&Role{},
		&UserRole{},
		&UserSession{},
	)
	suite.Require().NoError(err)
	
	// 初始化模拟对象
	suite.mockRepo = &MockUserRepository{}
	suite.mockLogger = &testing.MockLogger{}
	
	// 创建用户服务
	suite.service = NewUserService(suite.mockRepo, suite.mockLogger)
}

// TestCreateUser 测试创建用户
func (suite *UserServiceTestSuite) TestCreateUser() {
	ctx := suite.GetTestContext()
	testData := testing.NewTestData()
	
	suite.Run("成功创建用户", func() {
		// Given
		req := &CreateUserRequest{
			Name:     "张三",
			Email:    testData.GenerateEmail(),
			Password: "password123",
		}
		
		expectedUser := &User{
			ID:        testData.GenerateUUID(),
			Name:      req.Name,
			Email:     req.Email,
			Status:    UserStatusActive,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		suite.mockRepo.On("GetByEmail", ctx, req.Email).Return(nil, gorm.ErrRecordNotFound)
		suite.mockRepo.On("Create", ctx, mock.AnythingOfType("*user.User")).Return(nil).Run(func(args mock.Arguments) {
			user := args.Get(2).(*User)
			user.ID = expectedUser.ID
			user.CreatedAt = expectedUser.CreatedAt
			user.UpdatedAt = expectedUser.UpdatedAt
		})
		suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
		
		// When
		user, err := suite.service.CreateUser(ctx, req)
		
		// Then
		suite.AssertNoError(err)
		suite.AssertNotNil(user)
		suite.AssertEqual(req.Name, user.Name)
		suite.AssertEqual(req.Email, user.Email)
		suite.AssertEqual(UserStatusActive, user.Status)
		suite.AssertNotEmpty(user.ID)
		suite.AssertNotEmpty(user.PasswordHash)
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("邮箱已存在", func() {
		// Given
		req := &CreateUserRequest{
			Name:     "李四",
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		existingUser := &User{
			ID:    testData.GenerateUUID(),
			Email: req.Email,
		}
		
		suite.mockRepo.On("GetByEmail", ctx, req.Email).Return(existingUser, nil)
		
		// When
		user, err := suite.service.CreateUser(ctx, req)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(user)
		suite.AssertContains(err.Error(), "邮箱已存在")
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("邮箱格式无效", func() {
		// Given
		req := &CreateUserRequest{
			Name:     "王五",
			Email:    "invalid-email",
			Password: "password123",
		}
		
		// When
		user, err := suite.service.CreateUser(ctx, req)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(user)
		suite.AssertContains(err.Error(), "邮箱格式无效")
	})
	
	suite.Run("密码过短", func() {
		// Given
		req := &CreateUserRequest{
			Name:     "赵六",
			Email:    testData.GenerateEmail(),
			Password: "123",
		}
		
		// When
		user, err := suite.service.CreateUser(ctx, req)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(user)
		suite.AssertContains(err.Error(), "密码长度至少6位")
	})
}

// TestAuthenticateUser 测试用户认证
func (suite *UserServiceTestSuite) TestAuthenticateUser() {
	ctx := suite.GetTestContext()
	testData := testing.NewTestData()
	
	suite.Run("认证成功", func() {
		// Given
		email := testData.GenerateEmail()
		password := "password123"
		hashedPassword, _ := hashPassword(password)
		
		user := &User{
			ID:           testData.GenerateUUID(),
			Email:        email,
			PasswordHash: hashedPassword,
			Status:       UserStatusActive,
		}
		
		req := &AuthenticateRequest{
			Email:    email,
			Password: password,
		}
		
		suite.mockRepo.On("GetByEmail", ctx, email).Return(user, nil)
		suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
		
		// When
		authUser, err := suite.service.AuthenticateUser(ctx, req)
		
		// Then
		suite.AssertNoError(err)
		suite.AssertNotNil(authUser)
		suite.AssertEqual(user.ID, authUser.ID)
		suite.AssertEqual(user.Email, authUser.Email)
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("用户不存在", func() {
		// Given
		req := &AuthenticateRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		suite.mockRepo.On("GetByEmail", ctx, req.Email).Return(nil, gorm.ErrRecordNotFound)
		
		// When
		user, err := suite.service.AuthenticateUser(ctx, req)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(user)
		suite.AssertContains(err.Error(), "用户不存在或密码错误")
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("密码错误", func() {
		// Given
		email := testData.GenerateEmail()
		hashedPassword, _ := hashPassword("correct_password")
		
		user := &User{
			ID:           testData.GenerateUUID(),
			Email:        email,
			PasswordHash: hashedPassword,
			Status:       UserStatusActive,
		}
		
		req := &AuthenticateRequest{
			Email:    email,
			Password: "wrong_password",
		}
		
		suite.mockRepo.On("GetByEmail", ctx, email).Return(user, nil)
		
		// When
		authUser, err := suite.service.AuthenticateUser(ctx, req)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(authUser)
		suite.AssertContains(err.Error(), "用户不存在或密码错误")
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("用户已禁用", func() {
		// Given
		email := testData.GenerateEmail()
		password := "password123"
		hashedPassword, _ := hashPassword(password)
		
		user := &User{
			ID:           testData.GenerateUUID(),
			Email:        email,
			PasswordHash: hashedPassword,
			Status:       UserStatusInactive,
		}
		
		req := &AuthenticateRequest{
			Email:    email,
			Password: password,
		}
		
		suite.mockRepo.On("GetByEmail", ctx, email).Return(user, nil)
		
		// When
		authUser, err := suite.service.AuthenticateUser(ctx, req)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(authUser)
		suite.AssertContains(err.Error(), "用户账户已被禁用")
		suite.mockRepo.AssertExpectations(suite.T())
	})
}

// TestGetUser 测试获取用户
func (suite *UserServiceTestSuite) TestGetUser() {
	ctx := suite.GetTestContext()
	testData := testing.NewTestData()
	
	suite.Run("成功获取用户", func() {
		// Given
		userID := testData.GenerateUUID()
		user := &User{
			ID:    userID,
			Name:  "测试用户",
			Email: testData.GenerateEmail(),
		}
		
		suite.mockRepo.On("GetByID", ctx, userID).Return(user, nil)
		
		// When
		result, err := suite.service.GetUser(ctx, userID)
		
		// Then
		suite.AssertNoError(err)
		suite.AssertNotNil(result)
		suite.AssertEqual(user.ID, result.ID)
		suite.AssertEqual(user.Name, result.Name)
		suite.AssertEqual(user.Email, result.Email)
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("用户不存在", func() {
		// Given
		userID := testData.GenerateUUID()
		
		suite.mockRepo.On("GetByID", ctx, userID).Return(nil, gorm.ErrRecordNotFound)
		
		// When
		user, err := suite.service.GetUser(ctx, userID)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(user)
		suite.AssertContains(err.Error(), "用户不存在")
		suite.mockRepo.AssertExpectations(suite.T())
	})
}

// TestUpdateUser 测试更新用户
func (suite *UserServiceTestSuite) TestUpdateUser() {
	ctx := suite.GetTestContext()
	testData := testing.NewTestData()
	
	suite.Run("成功更新用户", func() {
		// Given
		userID := testData.GenerateUUID()
		existingUser := &User{
			ID:    userID,
			Name:  "原始名称",
			Email: testData.GenerateEmail(),
		}
		
		req := &UpdateUserRequest{
			Name:  "更新后的名称",
			Email: testData.GenerateEmail(),
		}
		
		suite.mockRepo.On("GetByID", ctx, userID).Return(existingUser, nil)
		suite.mockRepo.On("Update", ctx, mock.AnythingOfType("*user.User")).Return(nil)
		suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
		
		// When
		user, err := suite.service.UpdateUser(ctx, userID, req)
		
		// Then
		suite.AssertNoError(err)
		suite.AssertNotNil(user)
		suite.AssertEqual(req.Name, user.Name)
		suite.AssertEqual(req.Email, user.Email)
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("用户不存在", func() {
		// Given
		userID := testData.GenerateUUID()
		req := &UpdateUserRequest{
			Name: "更新后的名称",
		}
		
		suite.mockRepo.On("GetByID", ctx, userID).Return(nil, gorm.ErrRecordNotFound)
		
		// When
		user, err := suite.service.UpdateUser(ctx, userID, req)
		
		// Then
		suite.AssertError(err)
		suite.AssertNil(user)
		suite.AssertContains(err.Error(), "用户不存在")
		suite.mockRepo.AssertExpectations(suite.T())
	})
}

// TestDeleteUser 测试删除用户
func (suite *UserServiceTestSuite) TestDeleteUser() {
	ctx := suite.GetTestContext()
	testData := testing.NewTestData()
	
	suite.Run("成功删除用户", func() {
		// Given
		userID := testData.GenerateUUID()
		user := &User{
			ID:     userID,
			Status: UserStatusActive,
		}
		
		suite.mockRepo.On("GetByID", ctx, userID).Return(user, nil)
		suite.mockRepo.On("Update", ctx, mock.AnythingOfType("*user.User")).Return(nil)
		suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
		
		// When
		err := suite.service.DeleteUser(ctx, userID)
		
		// Then
		suite.AssertNoError(err)
		suite.mockRepo.AssertExpectations(suite.T())
	})
	
	suite.Run("用户不存在", func() {
		// Given
		userID := testData.GenerateUUID()
		
		suite.mockRepo.On("GetByID", ctx, userID).Return(nil, gorm.ErrRecordNotFound)
		
		// When
		err := suite.service.DeleteUser(ctx, userID)
		
		// Then
		suite.AssertError(err)
		suite.AssertContains(err.Error(), "用户不存在")
		suite.mockRepo.AssertExpectations(suite.T())
	})
}

// MockUserRepository 模拟用户仓库
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id string) (*User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, req *ListUsersRequest) ([]*User, int64, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*User), args.Get(1).(int64), args.Error(2)
}

// 运行测试套件
func TestUserService(t *testing.T) {
	suite.Run(t, new(UserServiceTestSuite))
}
