package app

import (
	"fmt"
	"strings"
)

// CreateAppRequest 创建应用请求
type CreateAppRequest struct {
	Name        string    `json:"name" validate:"required,min=1,max=100"`
	Description string    `json:"description"`
	Language    string    `json:"language" validate:"required,oneof=nodejs python"`
	Framework   string    `json:"framework"`
	Version     string    `json:"version" validate:"required"`
	GitRepo     string    `json:"git_repo" validate:"required,url"`
	GitBranch   string    `json:"git_branch"`
	TenantID    string    `json:"tenant_id" validate:"required"`
	UserID      string    `json:"user_id" validate:"required"`
	Config      AppConfig `json:"config"`
}

// Validate 验证创建应用请求
func (req *CreateAppRequest) Validate() error {
	if strings.TrimSpace(req.Name) == "" {
		return fmt.Errorf("应用名称不能为空")
	}
	
	if len(req.Name) > 100 {
		return fmt.Errorf("应用名称长度不能超过100个字符")
	}
	
	if req.Language != "nodejs" && req.Language != "python" {
		return fmt.Errorf("不支持的语言类型: %s", req.Language)
	}
	
	if strings.TrimSpace(req.GitRepo) == "" {
		return fmt.Errorf("Git 仓库地址不能为空")
	}
	
	if req.GitBranch == "" {
		req.GitBranch = "main"
	}
	
	if strings.TrimSpace(req.TenantID) == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	
	if strings.TrimSpace(req.UserID) == "" {
		return fmt.Errorf("用户ID不能为空")
	}
	
	// 验证配置
	if err := req.Config.Validate(); err != nil {
		return fmt.Errorf("应用配置验证失败: %w", err)
	}
	
	return nil
}

// UpdateAppRequest 更新应用请求
type UpdateAppRequest struct {
	Description *string    `json:"description"`
	GitRepo     *string    `json:"git_repo"`
	GitBranch   *string    `json:"git_branch"`
	Config      *AppConfig `json:"config"`
}

// DeployRequest 部署请求
type DeployRequest struct {
	Version    string            `json:"version"`
	Branch     string            `json:"branch"`
	CommitHash string            `json:"commit_hash"`
	Config     *AppConfig        `json:"config"`
	Env        map[string]string `json:"env"`
}

// CreateVersionRequest 创建版本请求
type CreateVersionRequest struct {
	Version    string    `json:"version" validate:"required"`
	CommitHash string    `json:"commit_hash" validate:"required"`
	Config     AppConfig `json:"config"`
}

// CreateBuildRequest 创建构建请求
type CreateBuildRequest struct {
	Branch     string `json:"branch" validate:"required"`
	CommitHash string `json:"commit_hash" validate:"required"`
	CommitMsg  string `json:"commit_msg"`
}

// AppFilter 应用过滤器
type AppFilter struct {
	Language string `json:"language"`
	Status   string `json:"status"`
	Search   string `json:"search"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

// AppResponse 应用响应
type AppResponse struct {
	*Application
	InstanceCount int `json:"instance_count"`
	RunningCount  int `json:"running_count"`
}

// AppListResponse 应用列表响应
type AppListResponse struct {
	Apps  []*AppResponse `json:"apps"`
	Total int64          `json:"total"`
	Page  int            `json:"page"`
	Size  int            `json:"size"`
}

// InstanceResponse 实例响应
type InstanceResponse struct {
	*AppInstance
	AppName string `json:"app_name"`
}

// BuildResponse 构建响应
type BuildResponse struct {
	*BuildTask
	AppName string `json:"app_name"`
}

// DeployResponse 部署响应
type DeployResponse struct {
	AppID      string `json:"app_id"`
	InstanceID string `json:"instance_id"`
	Status     string `json:"status"`
	Message    string `json:"message"`
}

// ScaleResponse 扩缩容响应
type ScaleResponse struct {
	AppID           string `json:"app_id"`
	CurrentReplicas int    `json:"current_replicas"`
	DesiredReplicas int    `json:"desired_replicas"`
	Status          string `json:"status"`
}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status    string            `json:"status"`
	Timestamp string            `json:"timestamp"`
	Version   string            `json:"version"`
	Checks    map[string]string `json:"checks"`
	Uptime    int64             `json:"uptime"`
}

// MetricsResponse 指标响应
type MetricsResponse struct {
	AppID     string                 `json:"app_id"`
	Instances []InstanceMetricsData  `json:"instances"`
	Summary   AppMetricsSummary      `json:"summary"`
}

// InstanceMetricsData 实例指标数据
type InstanceMetricsData struct {
	InstanceID string          `json:"instance_id"`
	Metrics    InstanceMetrics `json:"metrics"`
}

// AppMetricsSummary 应用指标汇总
type AppMetricsSummary struct {
	TotalInstances   int     `json:"total_instances"`
	RunningInstances int     `json:"running_instances"`
	AvgCPUUsage      float64 `json:"avg_cpu_usage"`
	AvgMemoryUsage   float64 `json:"avg_memory_usage"`
	TotalRequests    int64   `json:"total_requests"`
	TotalErrors      int64   `json:"total_errors"`
	ErrorRate        float64 `json:"error_rate"`
}

// Validate 验证应用配置
func (c *AppConfig) Validate() error {
	// 验证运行时配置
	if err := c.Runtime.Validate(); err != nil {
		return fmt.Errorf("运行时配置错误: %w", err)
	}
	
	// 验证部署配置
	if err := c.Deploy.Validate(); err != nil {
		return fmt.Errorf("部署配置错误: %w", err)
	}
	
	return nil
}

// Validate 验证运行时配置
func (r *RuntimeConfig) Validate() error {
	if r.Language == "" {
		return fmt.Errorf("语言类型不能为空")
	}
	
	if r.Language != "nodejs" && r.Language != "python" {
		return fmt.Errorf("不支持的语言类型: %s", r.Language)
	}
	
	if r.Version == "" {
		return fmt.Errorf("语言版本不能为空")
	}
	
	return nil
}

// Validate 验证部署配置
func (d *DeployConfig) Validate() error {
	if d.Port <= 0 || d.Port > 65535 {
		return fmt.Errorf("端口号必须在 1-65535 范围内")
	}
	
	if d.Instances < 0 || d.Instances > 100 {
		return fmt.Errorf("实例数量必须在 0-100 范围内")
	}
	
	// 验证健康检查配置
	if err := d.HealthCheck.Validate(); err != nil {
		return fmt.Errorf("健康检查配置错误: %w", err)
	}
	
	// 验证自动扩缩容配置
	if err := d.Autoscaling.Validate(); err != nil {
		return fmt.Errorf("自动扩缩容配置错误: %w", err)
	}
	
	return nil
}

// Validate 验证健康检查配置
func (h *HealthCheckConfig) Validate() error {
	if h.Path == "" {
		h.Path = "/health"
	}
	
	if h.Port <= 0 || h.Port > 65535 {
		return fmt.Errorf("健康检查端口号必须在 1-65535 范围内")
	}
	
	if h.PeriodSeconds <= 0 {
		h.PeriodSeconds = 10
	}
	
	if h.TimeoutSeconds <= 0 {
		h.TimeoutSeconds = 5
	}
	
	if h.FailureThreshold <= 0 {
		h.FailureThreshold = 3
	}
	
	return nil
}

// Validate 验证自动扩缩容配置
func (a *AutoscalingConfig) Validate() error {
	if !a.Enabled {
		return nil
	}
	
	if a.MinReplicas < 0 {
		return fmt.Errorf("最小副本数不能小于0")
	}
	
	if a.MaxReplicas <= 0 {
		return fmt.Errorf("最大副本数必须大于0")
	}
	
	if a.MinReplicas > a.MaxReplicas {
		return fmt.Errorf("最小副本数不能大于最大副本数")
	}
	
	if a.TargetCPUUtilization <= 0 || a.TargetCPUUtilization > 100 {
		return fmt.Errorf("CPU 使用率阈值必须在 1-100 范围内")
	}
	
	if a.TargetMemoryUtilization <= 0 || a.TargetMemoryUtilization > 100 {
		return fmt.Errorf("内存使用率阈值必须在 1-100 范围内")
	}
	
	return nil
}
