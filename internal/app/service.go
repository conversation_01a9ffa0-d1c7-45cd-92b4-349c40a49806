package app

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Service 应用管理服务接口
type Service interface {
	// 应用管理
	CreateApp(ctx context.Context, req *CreateAppRequest) (*Application, error)
	GetApp(ctx context.Context, appID string) (*Application, error)
	GetAppByName(ctx context.Context, name string, tenantID string) (*Application, error)
	ListApps(ctx context.Context, tenantID string, filter *AppFilter) ([]*Application, int64, error)
	UpdateApp(ctx context.Context, appID string, req *UpdateAppRequest) (*Application, error)
	DeleteApp(ctx context.Context, appID string) error
	
	// 应用部署
	DeployApp(ctx context.Context, appID string, req *DeployRequest) (*AppInstance, error)
	StopApp(ctx context.Context, appID string) error
	RestartApp(ctx context.Context, appID string) error
	ScaleApp(ctx context.Context, appID string, instances int) error
	
	// 版本管理
	CreateVersion(ctx context.Context, appID string, req *CreateVersionRequest) (*AppVersion, error)
	ListVersions(ctx context.Context, appID string) ([]*AppVersion, error)
	RollbackToVersion(ctx context.Context, appID string, versionID string) error
	
	// 实例管理
	GetInstance(ctx context.Context, instanceID string) (*AppInstance, error)
	ListInstances(ctx context.Context, appID string) ([]*AppInstance, error)
	UpdateInstanceStatus(ctx context.Context, instanceID string, status InstanceStatus) error
	UpdateInstanceMetrics(ctx context.Context, instanceID string, metrics *InstanceMetrics) error
	
	// 构建管理
	CreateBuild(ctx context.Context, appID string, req *CreateBuildRequest) (*BuildTask, error)
	GetBuild(ctx context.Context, buildID string) (*BuildTask, error)
	ListBuilds(ctx context.Context, appID string) ([]*BuildTask, error)
	UpdateBuildStatus(ctx context.Context, buildID string, status BuildStatus, log string) error
}

// AppService 应用管理服务实现
type AppService struct {
	db     *gorm.DB
	logger Logger
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
}

// NewAppService 创建应用管理服务
func NewAppService(db *gorm.DB, logger Logger) Service {
	return &AppService{
		db:     db,
		logger: logger,
	}
}

// CreateApp 创建应用
func (s *AppService) CreateApp(ctx context.Context, req *CreateAppRequest) (*Application, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查应用名称是否已存在
	var existingApp Application
	err := s.db.Where("name = ? AND tenant_id = ?", req.Name, req.TenantID).First(&existingApp).Error
	if err == nil {
		return nil, fmt.Errorf("应用名称 '%s' 已存在", req.Name)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查应用名称失败: %w", err)
	}
	
	// 创建应用实体
	app := &Application{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		Language:    req.Language,
		Framework:   req.Framework,
		Version:     req.Version,
		GitRepo:     req.GitRepo,
		GitBranch:   req.GitBranch,
		Status:      AppStatusCreated,
		TenantID:    req.TenantID,
		UserID:      req.UserID,
		Config:      req.Config,
	}
	
	// 保存到数据库
	if err := s.db.Create(app).Error; err != nil {
		return nil, fmt.Errorf("创建应用失败: %w", err)
	}
	
	s.logger.Info("应用创建成功", "app_id", app.ID, "name", app.Name, "tenant_id", app.TenantID)
	return app, nil
}

// GetApp 获取应用详情
func (s *AppService) GetApp(ctx context.Context, appID string) (*Application, error) {
	var app Application
	err := s.db.Preload("Instances").Preload("Builds").First(&app, "id = ?", appID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("应用不存在: %s", appID)
		}
		return nil, fmt.Errorf("获取应用失败: %w", err)
	}
	return &app, nil
}

// GetAppByName 根据名称获取应用
func (s *AppService) GetAppByName(ctx context.Context, name string, tenantID string) (*Application, error) {
	var app Application
	err := s.db.Where("name = ? AND tenant_id = ?", name, tenantID).First(&app).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("应用不存在: %s", name)
		}
		return nil, fmt.Errorf("获取应用失败: %w", err)
	}
	return &app, nil
}

// ListApps 获取应用列表
func (s *AppService) ListApps(ctx context.Context, tenantID string, filter *AppFilter) ([]*Application, int64, error) {
	query := s.db.Where("tenant_id = ?", tenantID)
	
	// 应用过滤条件
	if filter != nil {
		if filter.Language != "" {
			query = query.Where("language = ?", filter.Language)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.Search != "" {
			query = query.Where("name ILIKE ? OR description ILIKE ?", 
				"%"+filter.Search+"%", "%"+filter.Search+"%")
		}
	}
	
	// 获取总数
	var total int64
	if err := query.Model(&Application{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取应用总数失败: %w", err)
	}
	
	// 分页查询
	var apps []*Application
	offset := 0
	limit := 20
	if filter != nil {
		if filter.Page > 0 {
			offset = (filter.Page - 1) * filter.PageSize
		}
		if filter.PageSize > 0 && filter.PageSize <= 100 {
			limit = filter.PageSize
		}
	}
	
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&apps).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取应用列表失败: %w", err)
	}
	
	return apps, total, nil
}

// UpdateApp 更新应用
func (s *AppService) UpdateApp(ctx context.Context, appID string, req *UpdateAppRequest) (*Application, error) {
	// 获取现有应用
	app, err := s.GetApp(ctx, appID)
	if err != nil {
		return nil, err
	}
	
	// 更新字段
	updates := make(map[string]interface{})
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.GitRepo != nil {
		updates["git_repo"] = *req.GitRepo
	}
	if req.GitBranch != nil {
		updates["git_branch"] = *req.GitBranch
	}
	if req.Config != nil {
		updates["config"] = *req.Config
	}
	
	// 执行更新
	if err := s.db.Model(app).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新应用失败: %w", err)
	}
	
	s.logger.Info("应用更新成功", "app_id", appID)
	return s.GetApp(ctx, appID)
}

// DeleteApp 删除应用
func (s *AppService) DeleteApp(ctx context.Context, appID string) error {
	// 检查应用是否存在
	app, err := s.GetApp(ctx, appID)
	if err != nil {
		return err
	}

	// 检查是否有运行中的实例
	var runningInstances int64
	err = s.db.Model(&AppInstance{}).Where("app_id = ? AND status IN ?",
		appID, []InstanceStatus{InstanceStatusRunning, InstanceStatusStarting}).Count(&runningInstances).Error
	if err != nil {
		return fmt.Errorf("检查运行实例失败: %w", err)
	}

	if runningInstances > 0 {
		return fmt.Errorf("无法删除应用，存在 %d 个运行中的实例", runningInstances)
	}

	// 软删除应用 (更新状态为已删除)
	if err := s.db.Model(app).Update("status", AppStatusDeleted).Error; err != nil {
		return fmt.Errorf("删除应用失败: %w", err)
	}

	s.logger.Info("应用删除成功", "app_id", appID, "name", app.Name)
	return nil
}

// DeployApp 部署应用
func (s *AppService) DeployApp(ctx context.Context, appID string, req *DeployRequest) (*AppInstance, error) {
	// 获取应用信息
	app, err := s.GetApp(ctx, appID)
	if err != nil {
		return nil, err
	}

	// 创建新实例
	instance := &AppInstance{
		ID:          uuid.New().String(),
		AppID:       appID,
		Version:     req.Version,
		Status:      InstanceStatusPending,
		Port:        app.Config.Deploy.Port,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 保存实例到数据库
	if err := s.db.Create(instance).Error; err != nil {
		return nil, fmt.Errorf("创建实例失败: %w", err)
	}

	// 更新应用状态为部署中
	if err := s.db.Model(app).Update("status", AppStatusDeploying).Error; err != nil {
		s.logger.Error("更新应用状态失败", "error", err, "app_id", appID)
	}

	// TODO: 异步启动容器部署流程
	go s.deployInstance(ctx, instance, app)

	s.logger.Info("应用部署启动", "app_id", appID, "instance_id", instance.ID)
	return instance, nil
}

// StopApp 停止应用
func (s *AppService) StopApp(ctx context.Context, appID string) error {
	// 获取应用的所有运行实例
	instances, err := s.ListInstances(ctx, appID)
	if err != nil {
		return err
	}

	// 停止所有实例
	for _, instance := range instances {
		if instance.Status == InstanceStatusRunning || instance.Status == InstanceStatusStarting {
			if err := s.stopInstance(ctx, instance); err != nil {
				s.logger.Error("停止实例失败", "error", err, "instance_id", instance.ID)
				continue
			}
		}
	}

	// 更新应用状态
	if err := s.db.Model(&Application{}).Where("id = ?", appID).Update("status", AppStatusStopped).Error; err != nil {
		return fmt.Errorf("更新应用状态失败: %w", err)
	}

	s.logger.Info("应用停止成功", "app_id", appID)
	return nil
}

// RestartApp 重启应用
func (s *AppService) RestartApp(ctx context.Context, appID string) error {
	// 先停止应用
	if err := s.StopApp(ctx, appID); err != nil {
		return fmt.Errorf("停止应用失败: %w", err)
	}

	// 等待一段时间确保实例完全停止
	time.Sleep(5 * time.Second)

	// 重新部署应用
	app, err := s.GetApp(ctx, appID)
	if err != nil {
		return err
	}

	deployReq := &DeployRequest{
		Version: app.Version,
		Branch:  app.GitBranch,
	}

	_, err = s.DeployApp(ctx, appID, deployReq)
	if err != nil {
		return fmt.Errorf("重新部署应用失败: %w", err)
	}

	s.logger.Info("应用重启成功", "app_id", appID)
	return nil
}

// ScaleApp 扩缩容应用
func (s *AppService) ScaleApp(ctx context.Context, appID string, instances int) error {
	// 获取应用信息
	app, err := s.GetApp(ctx, appID)
	if err != nil {
		return err
	}

	// 获取当前实例列表
	currentInstances, err := s.ListInstances(ctx, appID)
	if err != nil {
		return err
	}

	currentCount := len(currentInstances)

	if instances > currentCount {
		// 扩容：创建新实例
		for i := currentCount; i < instances; i++ {
			deployReq := &DeployRequest{
				Version: app.Version,
				Branch:  app.GitBranch,
			}
			_, err := s.DeployApp(ctx, appID, deployReq)
			if err != nil {
				s.logger.Error("扩容创建实例失败", "error", err, "app_id", appID)
				continue
			}
		}
	} else if instances < currentCount {
		// 缩容：停止多余实例
		instancesToStop := currentCount - instances
		for i := 0; i < instancesToStop && i < len(currentInstances); i++ {
			instance := currentInstances[i]
			if err := s.stopInstance(ctx, instance); err != nil {
				s.logger.Error("缩容停止实例失败", "error", err, "instance_id", instance.ID)
				continue
			}
		}
	}

	s.logger.Info("应用扩缩容完成", "app_id", appID, "from", currentCount, "to", instances)
	return nil
}

// deployInstance 部署实例 (内部方法)
func (s *AppService) deployInstance(ctx context.Context, instance *AppInstance, app *Application) {
	// 更新实例状态为启动中
	s.UpdateInstanceStatus(ctx, instance.ID, InstanceStatusStarting)

	// TODO: 实现具体的容器部署逻辑
	// 1. 构建 Docker 镜像
	// 2. 启动容器
	// 3. 配置网络和存储
	// 4. 启动健康检查

	// 模拟部署过程
	time.Sleep(10 * time.Second)

	// 更新实例状态为运行中
	s.UpdateInstanceStatus(ctx, instance.ID, InstanceStatusRunning)

	// 更新应用状态为运行中
	s.db.Model(app).Update("status", AppStatusRunning)

	s.logger.Info("实例部署完成", "instance_id", instance.ID, "app_id", app.ID)
}

// stopInstance 停止实例 (内部方法)
func (s *AppService) stopInstance(ctx context.Context, instance *AppInstance) error {
	// 更新实例状态为停止中
	if err := s.UpdateInstanceStatus(ctx, instance.ID, InstanceStatusStopping); err != nil {
		return err
	}

	// TODO: 实现具体的容器停止逻辑
	// 1. 停止容器
	// 2. 清理网络配置
	// 3. 清理存储卷

	// 模拟停止过程
	time.Sleep(3 * time.Second)

	// 更新实例状态为已停止
	if err := s.UpdateInstanceStatus(ctx, instance.ID, InstanceStatusStopped); err != nil {
		return err
	}

	s.logger.Info("实例停止完成", "instance_id", instance.ID)
	return nil
}

// CreateVersion 创建版本
func (s *AppService) CreateVersion(ctx context.Context, appID string, req *CreateVersionRequest) (*AppVersion, error) {
	// 检查应用是否存在
	_, err := s.GetApp(ctx, appID)
	if err != nil {
		return nil, err
	}

	// 检查版本是否已存在
	var existingVersion AppVersion
	err = s.db.Where("app_id = ? AND version = ?", appID, req.Version).First(&existingVersion).Error
	if err == nil {
		return nil, fmt.Errorf("版本 '%s' 已存在", req.Version)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查版本失败: %w", err)
	}

	// 创建版本
	version := &AppVersion{
		ID:         uuid.New().String(),
		AppID:      appID,
		Version:    req.Version,
		CommitHash: req.CommitHash,
		Config:     req.Config,
		IsActive:   false,
		CreatedAt:  time.Now(),
	}

	if err := s.db.Create(version).Error; err != nil {
		return nil, fmt.Errorf("创建版本失败: %w", err)
	}

	s.logger.Info("版本创建成功", "app_id", appID, "version", req.Version)
	return version, nil
}

// ListVersions 获取版本列表
func (s *AppService) ListVersions(ctx context.Context, appID string) ([]*AppVersion, error) {
	var versions []*AppVersion
	err := s.db.Where("app_id = ?", appID).Order("created_at DESC").Find(&versions).Error
	if err != nil {
		return nil, fmt.Errorf("获取版本列表失败: %w", err)
	}
	return versions, nil
}

// RollbackToVersion 回滚到指定版本
func (s *AppService) RollbackToVersion(ctx context.Context, appID string, versionID string) error {
	// 获取目标版本
	var targetVersion AppVersion
	err := s.db.Where("id = ? AND app_id = ?", versionID, appID).First(&targetVersion).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("版本不存在")
		}
		return fmt.Errorf("获取版本失败: %w", err)
	}

	// 停止当前应用
	if err := s.StopApp(ctx, appID); err != nil {
		return fmt.Errorf("停止当前应用失败: %w", err)
	}

	// 部署目标版本
	deployReq := &DeployRequest{
		Version:    targetVersion.Version,
		CommitHash: targetVersion.CommitHash,
		Config:     &targetVersion.Config,
	}

	_, err = s.DeployApp(ctx, appID, deployReq)
	if err != nil {
		return fmt.Errorf("部署目标版本失败: %w", err)
	}

	// 更新版本活跃状态
	s.db.Model(&AppVersion{}).Where("app_id = ?", appID).Update("is_active", false)
	s.db.Model(&targetVersion).Update("is_active", true)

	s.logger.Info("应用回滚成功", "app_id", appID, "version", targetVersion.Version)
	return nil
}

// GetInstance 获取实例详情
func (s *AppService) GetInstance(ctx context.Context, instanceID string) (*AppInstance, error) {
	var instance AppInstance
	err := s.db.Preload("Application").First(&instance, "id = ?", instanceID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("实例不存在: %s", instanceID)
		}
		return nil, fmt.Errorf("获取实例失败: %w", err)
	}
	return &instance, nil
}

// ListInstances 获取实例列表
func (s *AppService) ListInstances(ctx context.Context, appID string) ([]*AppInstance, error) {
	var instances []*AppInstance
	err := s.db.Preload("Application").Where("app_id = ?", appID).Order("created_at DESC").Find(&instances).Error
	if err != nil {
		return nil, fmt.Errorf("获取实例列表失败: %w", err)
	}
	return instances, nil
}

// UpdateInstanceStatus 更新实例状态
func (s *AppService) UpdateInstanceStatus(ctx context.Context, instanceID string, status InstanceStatus) error {
	err := s.db.Model(&AppInstance{}).Where("id = ?", instanceID).Updates(map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}).Error
	if err != nil {
		return fmt.Errorf("更新实例状态失败: %w", err)
	}

	s.logger.Debug("实例状态更新", "instance_id", instanceID, "status", status)
	return nil
}

// UpdateInstanceMetrics 更新实例指标
func (s *AppService) UpdateInstanceMetrics(ctx context.Context, instanceID string, metrics *InstanceMetrics) error {
	metrics.UpdatedAt = time.Now()

	err := s.db.Model(&AppInstance{}).Where("id = ?", instanceID).Updates(map[string]interface{}{
		"metrics":    metrics,
		"updated_at": time.Now(),
	}).Error
	if err != nil {
		return fmt.Errorf("更新实例指标失败: %w", err)
	}

	return nil
}

// CreateBuild 创建构建任务
func (s *AppService) CreateBuild(ctx context.Context, appID string, req *CreateBuildRequest) (*BuildTask, error) {
	// 检查应用是否存在
	_, err := s.GetApp(ctx, appID)
	if err != nil {
		return nil, err
	}

	// 创建构建任务
	build := &BuildTask{
		ID:         uuid.New().String(),
		AppID:      appID,
		Branch:     req.Branch,
		CommitHash: req.CommitHash,
		CommitMsg:  req.CommitMsg,
		Status:     BuildStatusPending,
		StartTime:  time.Now(),
	}

	if err := s.db.Create(build).Error; err != nil {
		return nil, fmt.Errorf("创建构建任务失败: %w", err)
	}

	// TODO: 异步启动构建流程
	go s.executeBuild(ctx, build)

	s.logger.Info("构建任务创建成功", "build_id", build.ID, "app_id", appID)
	return build, nil
}

// GetBuild 获取构建详情
func (s *AppService) GetBuild(ctx context.Context, buildID string) (*BuildTask, error) {
	var build BuildTask
	err := s.db.Preload("Application").First(&build, "id = ?", buildID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("构建任务不存在: %s", buildID)
		}
		return nil, fmt.Errorf("获取构建任务失败: %w", err)
	}
	return &build, nil
}

// ListBuilds 获取构建列表
func (s *AppService) ListBuilds(ctx context.Context, appID string) ([]*BuildTask, error) {
	var builds []*BuildTask
	err := s.db.Where("app_id = ?", appID).Order("start_time DESC").Find(&builds).Error
	if err != nil {
		return nil, fmt.Errorf("获取构建列表失败: %w", err)
	}
	return builds, nil
}

// UpdateBuildStatus 更新构建状态
func (s *AppService) UpdateBuildStatus(ctx context.Context, buildID string, status BuildStatus, log string) error {
	updates := map[string]interface{}{
		"status":    status,
		"build_log": log,
	}

	// 如果构建完成，设置结束时间
	if status == BuildStatusSuccess || status == BuildStatusFailed || status == BuildStatusCanceled {
		endTime := time.Now()
		updates["end_time"] = &endTime

		// 计算构建时长
		var build BuildTask
		if err := s.db.First(&build, "id = ?", buildID).Error; err == nil {
			duration := int(endTime.Sub(build.StartTime).Seconds())
			updates["duration"] = duration
		}
	}

	err := s.db.Model(&BuildTask{}).Where("id = ?", buildID).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新构建状态失败: %w", err)
	}

	s.logger.Info("构建状态更新", "build_id", buildID, "status", status)
	return nil
}

// executeBuild 执行构建 (内部方法)
func (s *AppService) executeBuild(ctx context.Context, build *BuildTask) {
	// 更新构建状态为运行中
	s.UpdateBuildStatus(ctx, build.ID, BuildStatusRunning, "开始构建...")

	// TODO: 实现具体的构建逻辑
	// 1. 克隆代码
	// 2. 检测应用类型
	// 3. 安装依赖
	// 4. 运行测试
	// 5. 构建 Docker 镜像
	// 6. 推送镜像到仓库

	// 模拟构建过程
	time.Sleep(30 * time.Second)

	// 更新构建状态为成功
	s.UpdateBuildStatus(ctx, build.ID, BuildStatusSuccess, "构建完成")

	s.logger.Info("构建执行完成", "build_id", build.ID)
}
