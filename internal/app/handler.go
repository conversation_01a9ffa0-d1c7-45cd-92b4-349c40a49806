package app

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// Handler 应用管理 HTTP 处理器
type Handler struct {
	service Service
	logger  Logger
}

// NewHandler 创建应用管理处理器
func NewHandler(service Service, logger Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	apps := router.Group("/apps")
	{
		apps.POST("", h.CreateApp)           // 创建应用
		apps.GET("", h.ListApps)             // 获取应用列表
		apps.GET("/:id", h.GetApp)           // 获取应用详情
		apps.PUT("/:id", h.UpdateApp)        // 更新应用
		apps.DELETE("/:id", h.DeleteApp)     // 删除应用
		
		// 应用操作
		apps.POST("/:id/deploy", h.Deploy<PERSON>pp)    // 部署应用
		apps.POST("/:id/stop", h.StopApp)        // 停止应用
		apps.POST("/:id/restart", h.RestartApp)  // 重启应用
		apps.POST("/:id/scale", h.ScaleApp)      // 扩缩容
		
		// 版本管理
		apps.GET("/:id/versions", h.ListVersions)              // 获取版本列表
		apps.POST("/:id/versions", h.CreateVersion)            // 创建版本
		apps.POST("/:id/rollback/:version", h.RollbackVersion) // 回滚版本
		
		// 实例管理
		apps.GET("/:id/instances", h.ListInstances)     // 获取实例列表
		apps.GET("/:id/instances/:iid", h.GetInstance)  // 获取实例详情
		
		// 构建管理
		apps.GET("/:id/builds", h.ListBuilds)       // 获取构建列表
		apps.POST("/:id/builds", h.CreateBuild)     // 创建构建
		apps.GET("/:id/builds/:bid", h.GetBuild)    // 获取构建详情
		
		// 监控相关
		apps.GET("/:id/metrics", h.GetMetrics)      // 获取应用指标
		apps.GET("/:id/logs", h.GetLogs)            // 获取应用日志
	}
}

// CreateApp 创建应用
// @Summary 创建应用
// @Description 创建新的应用
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param request body CreateAppRequest true "创建应用请求"
// @Success 201 {object} Application "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps [post]
func (h *Handler) CreateApp(c *gin.Context) {
	var req CreateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	// 从上下文获取租户ID和用户ID (通过中间件设置)
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "未授权访问",
		})
		return
	}
	req.TenantID = tenantID.(string)
	
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED", 
			Message: "未授权访问",
		})
		return
	}
	req.UserID = userID.(string)
	
	// 创建应用
	app, err := h.service.CreateApp(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建应用失败", "error", err, "name", req.Name)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_APP_FAILED",
			Message: "创建应用失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("应用创建成功", "app_id", app.ID, "name", app.Name)
	c.JSON(http.StatusCreated, app)
}

// GetApp 获取应用详情
// @Summary 获取应用详情
// @Description 根据应用ID获取应用详细信息
// @Tags 应用管理
// @Produce json
// @Param id path string true "应用ID"
// @Success 200 {object} Application "获取成功"
// @Failure 404 {object} ErrorResponse "应用不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps/{id} [get]
func (h *Handler) GetApp(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}
	
	app, err := h.service.GetApp(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("获取应用失败", "error", err, "app_id", appID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "APP_NOT_FOUND",
			Message: "应用不存在",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, app)
}

// ListApps 获取应用列表
// @Summary 获取应用列表
// @Description 获取当前租户下的应用列表
// @Tags 应用管理
// @Produce json
// @Param language query string false "语言类型过滤"
// @Param status query string false "状态过滤"
// @Param search query string false "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} AppListResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps [get]
func (h *Handler) ListApps(c *gin.Context) {
	// 获取租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "未授权访问",
		})
		return
	}
	
	// 解析查询参数
	filter := &AppFilter{
		Language: c.Query("language"),
		Status:   c.Query("status"),
		Search:   c.Query("search"),
		Page:     1,
		PageSize: 20,
	}
	
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}
	
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}
	
	// 获取应用列表
	apps, total, err := h.service.ListApps(c.Request.Context(), tenantID.(string), filter)
	if err != nil {
		h.logger.Error("获取应用列表失败", "error", err, "tenant_id", tenantID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_APPS_FAILED",
			Message: "获取应用列表失败",
			Details: err.Error(),
		})
		return
	}
	
	// 转换为响应格式
	appResponses := make([]*AppResponse, len(apps))
	for i, app := range apps {
		appResponses[i] = &AppResponse{
			Application:   app,
			InstanceCount: len(app.Instances),
			RunningCount:  countRunningInstances(app.Instances),
		}
	}
	
	response := &AppListResponse{
		Apps:  appResponses,
		Total: total,
		Page:  filter.Page,
		Size:  len(appResponses),
	}
	
	c.JSON(http.StatusOK, response)
}

// UpdateApp 更新应用
// @Summary 更新应用
// @Description 更新应用配置信息
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param id path string true "应用ID"
// @Param request body UpdateAppRequest true "更新应用请求"
// @Success 200 {object} Application "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "应用不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps/{id} [put]
func (h *Handler) UpdateApp(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}
	
	var req UpdateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	app, err := h.service.UpdateApp(c.Request.Context(), appID, &req)
	if err != nil {
		h.logger.Error("更新应用失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_APP_FAILED",
			Message: "更新应用失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("应用更新成功", "app_id", appID)
	c.JSON(http.StatusOK, app)
}

// DeleteApp 删除应用
// @Summary 删除应用
// @Description 删除指定的应用
// @Tags 应用管理
// @Produce json
// @Param id path string true "应用ID"
// @Success 204 "删除成功"
// @Failure 404 {object} ErrorResponse "应用不存在"
// @Failure 409 {object} ErrorResponse "应用正在运行，无法删除"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps/{id} [delete]
func (h *Handler) DeleteApp(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}
	
	err := h.service.DeleteApp(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("删除应用失败", "error", err, "app_id", appID)
		
		// 根据错误类型返回不同的状态码
		if err.Error() == "应用不存在" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Code:    "APP_NOT_FOUND",
				Message: "应用不存在",
			})
		} else if strings.Contains(err.Error(), "运行中的实例") {
			c.JSON(http.StatusConflict, ErrorResponse{
				Code:    "APP_RUNNING",
				Message: "应用正在运行，无法删除",
				Details: err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Code:    "DELETE_APP_FAILED",
				Message: "删除应用失败",
				Details: err.Error(),
			})
		}
		return
	}
	
	h.logger.Info("应用删除成功", "app_id", appID)
	c.Status(http.StatusNoContent)
}

// DeployApp 部署应用
// @Summary 部署应用
// @Description 部署或重新部署应用
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param id path string true "应用ID"
// @Param request body DeployRequest true "部署请求"
// @Success 200 {object} DeployResponse "部署成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "应用不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps/{id}/deploy [post]
func (h *Handler) DeployApp(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}
	
	var req DeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	instance, err := h.service.DeployApp(c.Request.Context(), appID, &req)
	if err != nil {
		h.logger.Error("部署应用失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DEPLOY_APP_FAILED",
			Message: "部署应用失败",
			Details: err.Error(),
		})
		return
	}
	
	response := &DeployResponse{
		AppID:      appID,
		InstanceID: instance.ID,
		Status:     string(instance.Status),
		Message:    "部署任务已启动",
	}
	
	h.logger.Info("应用部署启动", "app_id", appID, "instance_id", instance.ID)
	c.JSON(http.StatusOK, response)
}

// ScaleApp 扩缩容应用
// @Summary 扩缩容应用
// @Description 调整应用实例数量
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param id path string true "应用ID"
// @Param instances body object true "实例数量" example({"instances": 3})
// @Success 200 {object} ScaleResponse "扩缩容成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "应用不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps/{id}/scale [post]
func (h *Handler) ScaleApp(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}
	
	var req struct {
		Instances int `json:"instances" validate:"min=0,max=100"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	if req.Instances < 0 || req.Instances > 100 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_INSTANCES",
			Message: "实例数量必须在 0-100 范围内",
		})
		return
	}
	
	err := h.service.ScaleApp(c.Request.Context(), appID, req.Instances)
	if err != nil {
		h.logger.Error("扩缩容失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "SCALE_APP_FAILED",
			Message: "扩缩容失败",
			Details: err.Error(),
		})
		return
	}
	
	// 获取当前实例数量
	instances, err := h.service.ListInstances(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("获取实例列表失败", "error", err, "app_id", appID)
	}
	
	response := &ScaleResponse{
		AppID:           appID,
		CurrentReplicas: len(instances),
		DesiredReplicas: req.Instances,
		Status:          "scaling",
	}
	
	h.logger.Info("应用扩缩容启动", "app_id", appID, "instances", req.Instances)
	c.JSON(http.StatusOK, response)
}

// ListInstances 获取实例列表
// @Summary 获取实例列表
// @Description 获取应用的所有实例
// @Tags 应用管理
// @Produce json
// @Param id path string true "应用ID"
// @Success 200 {array} InstanceResponse "获取成功"
// @Failure 404 {object} ErrorResponse "应用不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/apps/{id}/instances [get]
func (h *Handler) ListInstances(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}
	
	instances, err := h.service.ListInstances(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("获取实例列表失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_INSTANCES_FAILED",
			Message: "获取实例列表失败",
			Details: err.Error(),
		})
		return
	}
	
	// 转换为响应格式
	responses := make([]*InstanceResponse, len(instances))
	for i, instance := range instances {
		responses[i] = &InstanceResponse{
			AppInstance: instance,
			AppName:     instance.Application.Name,
		}
	}
	
	c.JSON(http.StatusOK, responses)
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Details   string `json:"details,omitempty"`
	Timestamp string `json:"timestamp"`
}

// StopApp 停止应用
func (h *Handler) StopApp(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	err := h.service.StopApp(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("停止应用失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "STOP_APP_FAILED",
			Message: "停止应用失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("应用停止成功", "app_id", appID)
	c.JSON(http.StatusOK, gin.H{"message": "应用停止成功"})
}

// RestartApp 重启应用
func (h *Handler) RestartApp(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	err := h.service.RestartApp(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("重启应用失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "RESTART_APP_FAILED",
			Message: "重启应用失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("应用重启成功", "app_id", appID)
	c.JSON(http.StatusOK, gin.H{"message": "应用重启成功"})
}

// CreateVersion 创建版本
func (h *Handler) CreateVersion(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	var req CreateVersionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	version, err := h.service.CreateVersion(c.Request.Context(), appID, &req)
	if err != nil {
		h.logger.Error("创建版本失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_VERSION_FAILED",
			Message: "创建版本失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("版本创建成功", "app_id", appID, "version", version.Version)
	c.JSON(http.StatusCreated, version)
}

// ListVersions 获取版本列表
func (h *Handler) ListVersions(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	versions, err := h.service.ListVersions(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("获取版本列表失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_VERSIONS_FAILED",
			Message: "获取版本列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, versions)
}

// RollbackVersion 回滚版本
func (h *Handler) RollbackVersion(c *gin.Context) {
	appID := c.Param("id")
	versionID := c.Param("version")

	if appID == "" || versionID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_PARAMS",
			Message: "应用ID和版本ID不能为空",
		})
		return
	}

	err := h.service.RollbackToVersion(c.Request.Context(), appID, versionID)
	if err != nil {
		h.logger.Error("版本回滚失败", "error", err, "app_id", appID, "version_id", versionID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "ROLLBACK_FAILED",
			Message: "版本回滚失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("版本回滚成功", "app_id", appID, "version_id", versionID)
	c.JSON(http.StatusOK, gin.H{"message": "版本回滚成功"})
}

// GetInstance 获取实例详情
func (h *Handler) GetInstance(c *gin.Context) {
	instanceID := c.Param("iid")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_INSTANCE_ID",
			Message: "实例ID不能为空",
		})
		return
	}

	instance, err := h.service.GetInstance(c.Request.Context(), instanceID)
	if err != nil {
		h.logger.Error("获取实例失败", "error", err, "instance_id", instanceID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "INSTANCE_NOT_FOUND",
			Message: "实例不存在",
			Details: err.Error(),
		})
		return
	}

	response := &InstanceResponse{
		AppInstance: instance,
		AppName:     instance.Application.Name,
	}

	c.JSON(http.StatusOK, response)
}

// CreateBuild 创建构建
func (h *Handler) CreateBuild(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	var req CreateBuildRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	build, err := h.service.CreateBuild(c.Request.Context(), appID, &req)
	if err != nil {
		h.logger.Error("创建构建失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_BUILD_FAILED",
			Message: "创建构建失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("构建创建成功", "build_id", build.ID, "app_id", appID)
	c.JSON(http.StatusCreated, build)
}

// GetBuild 获取构建详情
func (h *Handler) GetBuild(c *gin.Context) {
	buildID := c.Param("bid")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}

	build, err := h.service.GetBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("获取构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "BUILD_NOT_FOUND",
			Message: "构建任务不存在",
			Details: err.Error(),
		})
		return
	}

	response := &BuildResponse{
		BuildTask: build,
		AppName:   build.Application.Name,
	}

	c.JSON(http.StatusOK, response)
}

// ListBuilds 获取构建列表
func (h *Handler) ListBuilds(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	builds, err := h.service.ListBuilds(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("获取构建列表失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_BUILDS_FAILED",
			Message: "获取构建列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, builds)
}

// GetMetrics 获取应用指标
func (h *Handler) GetMetrics(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	// TODO: 实现指标收集逻辑
	response := &MetricsResponse{
		AppID:     appID,
		Instances: []InstanceMetricsData{},
		Summary: AppMetricsSummary{
			TotalInstances:   0,
			RunningInstances: 0,
			AvgCPUUsage:      0,
			AvgMemoryUsage:   0,
			TotalRequests:    0,
			TotalErrors:      0,
			ErrorRate:        0,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetLogs 获取应用日志
func (h *Handler) GetLogs(c *gin.Context) {
	appID := c.Param("id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_APP_ID",
			Message: "应用ID不能为空",
		})
		return
	}

	// TODO: 实现日志获取逻辑
	c.JSON(http.StatusOK, gin.H{
		"app_id": appID,
		"logs":   []string{},
	})
}

// countRunningInstances 统计运行中的实例数量
func countRunningInstances(instances []AppInstance) int {
	count := 0
	for _, instance := range instances {
		if instance.Status == InstanceStatusRunning {
			count++
		}
	}
	return count
}
