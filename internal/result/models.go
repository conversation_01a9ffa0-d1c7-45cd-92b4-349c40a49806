package result

import (
	"time"
	"gorm.io/datatypes"
)

// ExecutionRecord 执行记录
type ExecutionRecord struct {
	ID          string         `json:"id" gorm:"primaryKey"`
	TaskID      string         `json:"task_id" gorm:"not null;uniqueIndex"`
	AppID       string         `json:"app_id" gorm:"not null;index"`
	UserID      string         `json:"user_id" gorm:"not null;index"`
	TenantID    string         `json:"tenant_id" gorm:"not null;index"`
	Status      string         `json:"status" gorm:"not null;index"`
	ExitCode    int            `json:"exit_code"`
	Output      string         `json:"output" gorm:"type:text"`
	Error       string         `json:"error" gorm:"type:text"`
	StartTime   time.Time      `json:"start_time" gorm:"not null;index"`
	EndTime     time.Time      `json:"end_time" gorm:"not null;index"`
	Duration    int64          `json:"duration"`     // 毫秒
	Resources   datatypes.JSON `json:"resources"`    // 资源使用情况
	Metadata    datatypes.JSON `json:"metadata"`     // 元数据
	CreatedAt   time.Time      `json:"created_at"`
}

// ArtifactRecord 产物记录
type ArtifactRecord struct {
	ID        string    `json:"id" gorm:"primaryKey"`
	TaskID    string    `json:"task_id" gorm:"not null;index"`
	Name      string    `json:"name" gorm:"not null"`
	Path      string    `json:"path" gorm:"not null"`
	Size      int64     `json:"size"`
	MimeType  string    `json:"mime_type"`
	URL       string    `json:"url"`
	Hash      string    `json:"hash"`
	StorageKey string   `json:"storage_key"`
	Status    ArtifactStatus `json:"status" gorm:"not null;index"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ArtifactStatus 产物状态
type ArtifactStatus string

const (
	ArtifactStatusUploading ArtifactStatus = "uploading"
	ArtifactStatusAvailable ArtifactStatus = "available"
	ArtifactStatusExpired   ArtifactStatus = "expired"
	ArtifactStatusDeleted   ArtifactStatus = "deleted"
	ArtifactStatusError     ArtifactStatus = "error"
)

// ResultMetrics 结果指标
type ResultMetrics struct {
	ID              string    `json:"id" gorm:"primaryKey"`
	TaskID          string    `json:"task_id" gorm:"not null;index"`
	Timestamp       time.Time `json:"timestamp" gorm:"not null;index"`
	CPUUsage        float64   `json:"cpu_usage"`        // CPU使用率 (%)
	MemoryUsage     int64     `json:"memory_usage"`     // 内存使用量 (字节)
	MemoryLimit     int64     `json:"memory_limit"`     // 内存限制 (字节)
	DiskUsage       int64     `json:"disk_usage"`       // 磁盘使用量 (字节)
	NetworkRx       int64     `json:"network_rx"`       // 网络接收字节
	NetworkTx       int64     `json:"network_tx"`       // 网络发送字节
	ProcessCount    int       `json:"process_count"`    // 进程数量
	FileDescCount   int       `json:"file_desc_count"`  // 文件描述符数量
	CreatedAt       time.Time `json:"created_at"`
}

// ResultEvent 结果事件
type ResultEvent struct {
	ID        string           `json:"id" gorm:"primaryKey"`
	TaskID    string           `json:"task_id" gorm:"not null;index"`
	EventType ResultEventType  `json:"event_type" gorm:"not null"`
	Message   string           `json:"message" gorm:"type:text"`
	Data      datatypes.JSON   `json:"data"`
	Timestamp time.Time        `json:"timestamp" gorm:"not null;index"`
	CreatedAt time.Time        `json:"created_at"`
}

// ResultEventType 结果事件类型
type ResultEventType string

const (
	ResultEventCollected    ResultEventType = "collected"
	ResultEventArtifactSaved ResultEventType = "artifact_saved"
	ResultEventExported     ResultEventType = "exported"
	ResultEventCleaned      ResultEventType = "cleaned"
	ResultEventError        ResultEventType = "error"
)

// ExportJob 导出任务
type ExportJob struct {
	ID          string          `json:"id" gorm:"primaryKey"`
	UserID      string          `json:"user_id" gorm:"not null;index"`
	TenantID    string          `json:"tenant_id" gorm:"not null;index"`
	Format      string          `json:"format" gorm:"not null"`
	Filter      datatypes.JSON  `json:"filter"`
	Status      ExportJobStatus `json:"status" gorm:"not null;index"`
	FilePath    string          `json:"file_path"`
	FileSize    int64           `json:"file_size"`
	DownloadURL string          `json:"download_url"`
	ExpiresAt   *time.Time      `json:"expires_at"`
	Error       string          `json:"error" gorm:"type:text"`
	Progress    int             `json:"progress" gorm:"default:0"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

// ExportJobStatus 导出任务状态
type ExportJobStatus string

const (
	ExportJobStatusPending   ExportJobStatus = "pending"
	ExportJobStatusRunning   ExportJobStatus = "running"
	ExportJobStatusCompleted ExportJobStatus = "completed"
	ExportJobStatusFailed    ExportJobStatus = "failed"
	ExportJobStatusExpired   ExportJobStatus = "expired"
)

// ResultArchive 结果归档
type ResultArchive struct {
	ID          string              `json:"id" gorm:"primaryKey"`
	TaskID      string              `json:"task_id" gorm:"not null;uniqueIndex"`
	ArchivePath string              `json:"archive_path" gorm:"not null"`
	ArchiveSize int64               `json:"archive_size"`
	Compression string              `json:"compression"`
	Encryption  bool                `json:"encryption"`
	Status      ResultArchiveStatus `json:"status" gorm:"not null;index"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
}

// ResultArchiveStatus 结果归档状态
type ResultArchiveStatus string

const (
	ResultArchiveStatusPending   ResultArchiveStatus = "pending"
	ResultArchiveStatusArchiving ResultArchiveStatus = "archiving"
	ResultArchiveStatusArchived  ResultArchiveStatus = "archived"
	ResultArchiveStatusFailed    ResultArchiveStatus = "failed"
	ResultArchiveStatusRestored  ResultArchiveStatus = "restored"
)

// ResultBackup 结果备份
type ResultBackup struct {
	ID          string             `json:"id" gorm:"primaryKey"`
	Name        string             `json:"name" gorm:"not null"`
	Description string             `json:"description" gorm:"type:text"`
	BackupPath  string             `json:"backup_path" gorm:"not null"`
	BackupSize  int64              `json:"backup_size"`
	TaskCount   int                `json:"task_count"`
	StartDate   time.Time          `json:"start_date"`
	EndDate     time.Time          `json:"end_date"`
	Status      ResultBackupStatus `json:"status" gorm:"not null;index"`
	TenantID    string             `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string             `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time          `json:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at"`
}

// ResultBackupStatus 结果备份状态
type ResultBackupStatus string

const (
	ResultBackupStatusPending   ResultBackupStatus = "pending"
	ResultBackupStatusRunning   ResultBackupStatus = "running"
	ResultBackupStatusCompleted ResultBackupStatus = "completed"
	ResultBackupStatusFailed    ResultBackupStatus = "failed"
)

// ResultStatistics 结果统计
type ResultStatistics struct {
	ID              string    `json:"id" gorm:"primaryKey"`
	Date            time.Time `json:"date" gorm:"not null;uniqueIndex:idx_result_stats_date_tenant"`
	TenantID        string    `json:"tenant_id" gorm:"not null;uniqueIndex:idx_result_stats_date_tenant"`
	TotalResults    int       `json:"total_results" gorm:"default:0"`
	SuccessResults  int       `json:"success_results" gorm:"default:0"`
	FailedResults   int       `json:"failed_results" gorm:"default:0"`
	TotalArtifacts  int       `json:"total_artifacts" gorm:"default:0"`
	TotalSize       int64     `json:"total_size" gorm:"default:0"`
	AvgDuration     float64   `json:"avg_duration"`     // 平均执行时间 (毫秒)
	AvgCPUUsage     float64   `json:"avg_cpu_usage"`    // 平均CPU使用率
	AvgMemoryUsage  int64     `json:"avg_memory_usage"` // 平均内存使用量
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// ResultRetentionPolicy 结果保留策略
type ResultRetentionPolicy struct {
	ID              string                     `json:"id" gorm:"primaryKey"`
	Name            string                     `json:"name" gorm:"not null"`
	Description     string                     `json:"description" gorm:"type:text"`
	RetentionDays   int                        `json:"retention_days" gorm:"not null"`
	ArchiveEnabled  bool                       `json:"archive_enabled" gorm:"default:false"`
	ArchiveDays     int                        `json:"archive_days"`
	CompressionType string                     `json:"compression_type"`
	EncryptionEnabled bool                     `json:"encryption_enabled" gorm:"default:false"`
	Status          ResultRetentionPolicyStatus `json:"status" gorm:"not null;index"`
	TenantID        string                     `json:"tenant_id" gorm:"not null;index"`
	CreatedBy       string                     `json:"created_by" gorm:"not null"`
	CreatedAt       time.Time                  `json:"created_at"`
	UpdatedAt       time.Time                  `json:"updated_at"`
}

// ResultRetentionPolicyStatus 结果保留策略状态
type ResultRetentionPolicyStatus string

const (
	ResultRetentionPolicyStatusActive   ResultRetentionPolicyStatus = "active"
	ResultRetentionPolicyStatusInactive ResultRetentionPolicyStatus = "inactive"
	ResultRetentionPolicyStatusDraft    ResultRetentionPolicyStatus = "draft"
)

// ResultAccessLog 结果访问日志
type ResultAccessLog struct {
	ID        string              `json:"id" gorm:"primaryKey"`
	TaskID    string              `json:"task_id" gorm:"not null;index"`
	UserID    string              `json:"user_id" gorm:"not null;index"`
	Action    ResultAccessAction  `json:"action" gorm:"not null"`
	Resource  string              `json:"resource"`
	IPAddress string              `json:"ip_address"`
	UserAgent string              `json:"user_agent"`
	Timestamp time.Time           `json:"timestamp" gorm:"not null;index"`
	CreatedAt time.Time           `json:"created_at"`
}

// ResultAccessAction 结果访问动作
type ResultAccessAction string

const (
	ResultAccessActionView     ResultAccessAction = "view"
	ResultAccessActionDownload ResultAccessAction = "download"
	ResultAccessActionExport   ResultAccessAction = "export"
	ResultAccessActionDelete   ResultAccessAction = "delete"
)

// ResultNotification 结果通知
type ResultNotification struct {
	ID          string                    `json:"id" gorm:"primaryKey"`
	TaskID      string                    `json:"task_id" gorm:"not null;index"`
	UserID      string                    `json:"user_id" gorm:"not null;index"`
	Type        ResultNotificationType    `json:"type" gorm:"not null"`
	Title       string                    `json:"title" gorm:"not null"`
	Message     string                    `json:"message" gorm:"type:text"`
	Data        datatypes.JSON            `json:"data"`
	Status      ResultNotificationStatus  `json:"status" gorm:"not null;index"`
	ReadAt      *time.Time                `json:"read_at"`
	CreatedAt   time.Time                 `json:"created_at"`
}

// ResultNotificationType 结果通知类型
type ResultNotificationType string

const (
	ResultNotificationTypeSuccess ResultNotificationType = "success"
	ResultNotificationTypeFailure ResultNotificationType = "failure"
	ResultNotificationTypeWarning ResultNotificationType = "warning"
	ResultNotificationTypeInfo    ResultNotificationType = "info"
)

// ResultNotificationStatus 结果通知状态
type ResultNotificationStatus string

const (
	ResultNotificationStatusUnread ResultNotificationStatus = "unread"
	ResultNotificationStatusRead   ResultNotificationStatus = "read"
	ResultNotificationStatusDeleted ResultNotificationStatus = "deleted"
)

// TableName 指定表名
func (ExecutionRecord) TableName() string {
	return "execution_records"
}

func (ArtifactRecord) TableName() string {
	return "artifact_records"
}

func (ResultMetrics) TableName() string {
	return "result_metrics"
}

func (ResultEvent) TableName() string {
	return "result_events"
}

func (ExportJob) TableName() string {
	return "export_jobs"
}

func (ResultArchive) TableName() string {
	return "result_archives"
}

func (ResultBackup) TableName() string {
	return "result_backups"
}

func (ResultStatistics) TableName() string {
	return "result_statistics"
}

func (ResultRetentionPolicy) TableName() string {
	return "result_retention_policies"
}

func (ResultAccessLog) TableName() string {
	return "result_access_logs"
}

func (ResultNotification) TableName() string {
	return "result_notifications"
}
