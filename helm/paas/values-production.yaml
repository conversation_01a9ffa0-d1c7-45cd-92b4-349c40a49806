# 生产环境 Helm Values 配置
# 针对生产环境优化的配置参数

# 全局配置
global:
  environment: production
  imageRegistry: registry.example.com
  imagePullSecrets:
    - name: registry-secret

# 镜像配置
image:
  registry: registry.example.com
  namespace: paas
  tag: latest
  pullPolicy: Always

# API Gateway 配置
apiGateway:
  enabled: true
  replicaCount: 3
  image:
    repository: api-gateway
    tag: ""
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"
    limits:
      memory: "1Gi"
      cpu: "1000m"
      ephemeral-storage: "4Gi"
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  service:
    type: ClusterIP
    port: 80
    targetPort: 8080
  
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    hosts:
      - host: api.paas.example.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: api-paas-tls
        hosts:
          - api.paas.example.com

# 用户服务配置
userService:
  enabled: true
  replicaCount: 2
  image:
    repository: user-service
    tag: ""
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
      ephemeral-storage: "1Gi"
    limits:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# 应用管理服务配置
appManager:
  enabled: true
  replicaCount: 2
  image:
    repository: app-manager
    tag: ""
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"
    limits:
      memory: "1Gi"
      cpu: "1000m"
      ephemeral-storage: "4Gi"
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# CI/CD 服务配置
cicdService:
  enabled: true
  replicaCount: 1
  image:
    repository: ci-cd-service
    tag: ""
  
  resources:
    requests:
      memory: "1Gi"
      cpu: "1000m"
      ephemeral-storage: "5Gi"
    limits:
      memory: "2Gi"
      cpu: "2000m"
      ephemeral-storage: "10Gi"
  
  persistence:
    enabled: true
    size: 50Gi
    storageClass: fast-ssd

# 监控服务配置
monitoringService:
  enabled: true
  replicaCount: 1
  image:
    repository: monitoring-service
    tag: ""
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"
    limits:
      memory: "1Gi"
      cpu: "1000m"
      ephemeral-storage: "4Gi"

# 通知服务配置
notificationService:
  enabled: true
  replicaCount: 1
  image:
    repository: notification-service
    tag: ""
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
      ephemeral-storage: "1Gi"
    limits:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"

# 负载均衡器配置
loadBalancer:
  enabled: true
  replicaCount: 2
  image:
    repository: load-balancer
    tag: ""
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
      ephemeral-storage: "1Gi"
    limits:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"
  
  service:
    type: LoadBalancer
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-type: nlb
      service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"

# 前端应用配置
web:
  enabled: true
  replicaCount: 2
  image:
    repository: web
    tag: ""
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
      ephemeral-storage: "512Mi"
    limits:
      memory: "256Mi"
      cpu: "200m"
      ephemeral-storage: "1Gi"
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    hosts:
      - host: paas.example.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: paas-tls
        hosts:
          - paas.example.com

# 数据库配置
postgresql:
  enabled: true
  auth:
    postgresPassword: ""  # 从 secret 获取
    username: paas_user
    password: ""  # 从 secret 获取
    database: paas_production
  
  primary:
    persistence:
      enabled: true
      size: 100Gi
      storageClass: fast-ssd
    
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"
    
    configuration: |
      max_connections = 200
      shared_buffers = 1GB
      effective_cache_size = 3GB
      maintenance_work_mem = 256MB
      checkpoint_completion_target = 0.9
      wal_buffers = 16MB
      default_statistics_target = 100
      random_page_cost = 1.1
      effective_io_concurrency = 200
      work_mem = 4MB
      min_wal_size = 1GB
      max_wal_size = 4GB
  
  readReplicas:
    replicaCount: 1
    persistence:
      enabled: true
      size: 100Gi
      storageClass: fast-ssd

# Redis 配置
redis:
  enabled: true
  auth:
    enabled: true
    password: ""  # 从 secret 获取
  
  master:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: fast-ssd
    
    resources:
      requests:
        memory: "1Gi"
        cpu: "500m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
    
    configuration: |
      maxmemory 1gb
      maxmemory-policy allkeys-lru
      save 900 1
      save 300 10
      save 60 10000
  
  replica:
    replicaCount: 1
    persistence:
      enabled: true
      size: 20Gi
      storageClass: fast-ssd

# 监控配置
monitoring:
  prometheus:
    enabled: true
    retention: 30d
    storageSize: 50Gi
    storageClass: fast-ssd
    
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"
  
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi
      storageClass: fast-ssd
    
    ingress:
      enabled: true
      hosts:
        - grafana.paas.example.com
      tls:
        - secretName: grafana-tls
          hosts:
            - grafana.paas.example.com
  
  alertmanager:
    enabled: true
    persistence:
      enabled: true
      size: 5Gi
      storageClass: fast-ssd

# 日志配置
logging:
  fluentd:
    enabled: true
    elasticsearch:
      host: elasticsearch.logging.svc.cluster.local
      port: 9200
    
    resources:
      requests:
        memory: "512Mi"
        cpu: "500m"
      limits:
        memory: "1Gi"
        cpu: "1000m"

# 安全配置
security:
  networkPolicies:
    enabled: true
  
  podSecurityPolicy:
    enabled: true
  
  rbac:
    enabled: true
  
  serviceAccount:
    create: true
    annotations: {}

# 备份配置
backup:
  enabled: true
  schedule: "0 2 * * *"  # 每天凌晨2点
  retention: 30  # 保留30天
  
  storage:
    type: s3
    bucket: paas-backups-prod
    region: us-west-2

# 配置映射
configMaps:
  app:
    environment: production
    logLevel: info
    logFormat: json
    metricsEnabled: "true"
    tracingEnabled: "true"
    corsOrigins: "https://paas.example.com"
    sessionTimeout: "3600"
    rateLimitRequests: "1000"
    rateLimitWindow: "60"

# 密钥配置（引用外部密钥）
secrets:
  database:
    name: database-secret
  redis:
    name: redis-secret
  jwt:
    name: jwt-secret
  registry:
    name: registry-secret

# 服务网格配置（如果使用 Istio）
serviceMesh:
  enabled: false
  istio:
    enabled: false
    gateway:
      enabled: false
    virtualService:
      enabled: false

# 存储类配置
storageClasses:
  fast-ssd:
    provisioner: kubernetes.io/aws-ebs
    parameters:
      type: gp3
      iops: "3000"
      throughput: "125"
    reclaimPolicy: Retain
    allowVolumeExpansion: true
