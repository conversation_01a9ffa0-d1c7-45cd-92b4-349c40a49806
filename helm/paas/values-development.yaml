# 开发环境 Helm Values 配置
# 针对开发环境优化的配置参数

# 全局配置
global:
  environment: development
  imageRegistry: registry.example.com
  imagePullSecrets: []

# 镜像配置
image:
  registry: registry.example.com
  namespace: paas
  tag: latest
  pullPolicy: Always

# API Gateway 配置
apiGateway:
  enabled: true
  replicaCount: 1
  image:
    repository: api-gateway
    tag: ""
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
      ephemeral-storage: "1Gi"
    limits:
      memory: "256Mi"
      cpu: "250m"
      ephemeral-storage: "2Gi"
  
  autoscaling:
    enabled: false
  
  service:
    type: ClusterIP
    port: 80
    targetPort: 8080
  
  ingress:
    enabled: true
    className: nginx
    annotations: {}
    hosts:
      - host: dev.paas.example.com
        paths:
          - path: /
            pathType: Prefix
    tls: []

# 用户服务配置
userService:
  enabled: true
  replicaCount: 1
  image:
    repository: user-service
    tag: ""
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
      ephemeral-storage: "512Mi"
    limits:
      memory: "256Mi"
      cpu: "200m"
      ephemeral-storage: "1Gi"
  
  autoscaling:
    enabled: false

# 应用管理服务配置
appManager:
  enabled: true
  replicaCount: 1
  image:
    repository: app-manager
    tag: ""
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
      ephemeral-storage: "1Gi"
    limits:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"
  
  autoscaling:
    enabled: false

# CI/CD 服务配置
cicdService:
  enabled: true
  replicaCount: 1
  image:
    repository: ci-cd-service
    tag: ""
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"
    limits:
      memory: "1Gi"
      cpu: "1000m"
      ephemeral-storage: "4Gi"
  
  persistence:
    enabled: true
    size: 10Gi
    storageClass: standard

# 监控服务配置
monitoringService:
  enabled: true
  replicaCount: 1
  image:
    repository: monitoring-service
    tag: ""
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
      ephemeral-storage: "1Gi"
    limits:
      memory: "512Mi"
      cpu: "500m"
      ephemeral-storage: "2Gi"

# 通知服务配置
notificationService:
  enabled: true
  replicaCount: 1
  image:
    repository: notification-service
    tag: ""
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
      ephemeral-storage: "512Mi"
    limits:
      memory: "256Mi"
      cpu: "200m"
      ephemeral-storage: "1Gi"

# 负载均衡器配置
loadBalancer:
  enabled: true
  replicaCount: 1
  image:
    repository: load-balancer
    tag: ""
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
      ephemeral-storage: "512Mi"
    limits:
      memory: "256Mi"
      cpu: "200m"
      ephemeral-storage: "1Gi"
  
  service:
    type: ClusterIP

# 前端应用配置
web:
  enabled: true
  replicaCount: 1
  image:
    repository: web
    tag: ""
  
  resources:
    requests:
      memory: "64Mi"
      cpu: "50m"
      ephemeral-storage: "256Mi"
    limits:
      memory: "128Mi"
      cpu: "100m"
      ephemeral-storage: "512Mi"
  
  autoscaling:
    enabled: false
  
  ingress:
    enabled: true
    className: nginx
    annotations: {}
    hosts:
      - host: dev.paas.example.com
        paths:
          - path: /
            pathType: Prefix
    tls: []

# 数据库配置
postgresql:
  enabled: true
  auth:
    postgresPassword: "dev_password"
    username: paas_user
    password: "dev_password"
    database: paas_development
  
  primary:
    persistence:
      enabled: true
      size: 10Gi
      storageClass: standard
    
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"
    
    configuration: |
      max_connections = 50
      shared_buffers = 128MB
      effective_cache_size = 256MB
      maintenance_work_mem = 64MB
      checkpoint_completion_target = 0.9
      wal_buffers = 4MB
      default_statistics_target = 100
      random_page_cost = 4
      effective_io_concurrency = 2
      work_mem = 2MB
      min_wal_size = 80MB
      max_wal_size = 1GB
  
  readReplicas:
    replicaCount: 0

# Redis 配置
redis:
  enabled: true
  auth:
    enabled: false
  
  master:
    persistence:
      enabled: true
      size: 2Gi
      storageClass: standard
    
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
    
    configuration: |
      maxmemory 128mb
      maxmemory-policy allkeys-lru
      save 900 1
      save 300 10
      save 60 10000
  
  replica:
    replicaCount: 0

# 监控配置
monitoring:
  prometheus:
    enabled: true
    retention: 7d
    storageSize: 5Gi
    storageClass: standard
    
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "1Gi"
        cpu: "500m"
  
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 2Gi
      storageClass: standard
    
    ingress:
      enabled: true
      hosts:
        - grafana-dev.paas.example.com
      tls: []
  
  alertmanager:
    enabled: false

# 日志配置
logging:
  fluentd:
    enabled: false

# 安全配置
security:
  networkPolicies:
    enabled: false
  
  podSecurityPolicy:
    enabled: false
  
  rbac:
    enabled: true
  
  serviceAccount:
    create: true
    annotations: {}

# 备份配置
backup:
  enabled: false

# 配置映射
configMaps:
  app:
    environment: development
    logLevel: debug
    logFormat: text
    metricsEnabled: "true"
    tracingEnabled: "false"
    corsOrigins: "*"
    sessionTimeout: "7200"
    rateLimitRequests: "100"
    rateLimitWindow: "60"

# 密钥配置
secrets:
  database:
    name: database-secret
  redis:
    name: redis-secret
  jwt:
    name: jwt-secret

# 服务网格配置
serviceMesh:
  enabled: false

# 存储类配置
storageClasses:
  standard:
    provisioner: kubernetes.io/aws-ebs
    parameters:
      type: gp2
    reclaimPolicy: Delete
    allowVolumeExpansion: true
