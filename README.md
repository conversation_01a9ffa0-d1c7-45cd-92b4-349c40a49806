# PaaS 平台

一个完整的 Platform as a Service (PaaS) 平台，支持应用的完整生命周期管理、CI/CD 流水线、多数据库支持等功能。

## 功能特性

- 🚀 **应用管理**: 支持 Node.js、Python 等多种应用类型的部署和管理
- 🔄 **CI/CD**: 完整的持续集成和持续部署流水线
- 🗄️ **多数据库**: 支持 SQLite、PostgreSQL 等多种数据库
- 👥 **多租户**: 完整的用户认证和 RBAC 权限管理
- 📊 **监控**: 应用性能监控、日志聚合和分析
- ⚖️ **负载均衡**: 内置负载均衡器和服务发现
- 🔧 **配置管理**: 集中化配置管理和热更新
- 🔒 **安全**: 网络隔离、密钥管理、安全扫描

## 架构设计

### 微服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 管理界面   │    │   API Gateway   │    │   Load Balancer │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                            │                            │
┌─────────────┐  ┌─────────────┐  │  ┌─────────────┐  ┌─────────────┐
│ App Manager │  │ CI/CD Service│  │  │User Service │  │Config Service│
└─────────────┘  └─────────────┘  │  └─────────────┘  └─────────────┘
         │                │       │           │               │
         └────────────────┼───────┼───────────┼───────────────┘
                          │       │           │
                    ┌─────────────┐  ┌─────────────┐
                    │Monitor Service│  │Database Service│
                    └─────────────┘  └─────────────┘
```

### 技术栈

- **后端**: Go 1.24+, Gin Web Framework
- **数据库**: PostgreSQL, SQLite, Redis
- **容器化**: Docker, Docker Compose
- **前端**: Vue.js 3 + TypeScript
- **代码仓库**: Gitea
- **API 文档**: Swagger/OpenAPI

## 快速开始

### 环境要求

- Go 1.24+ (推荐使用最新稳定版本)
- Docker & Docker Compose
- PostgreSQL (可选，默认使用 SQLite)

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd paas-platform
```

2. 安装依赖
```bash
go mod tidy
```

3. 启动开发环境
```bash
docker-compose -f docker-compose.dev.yml up -d
```

4. 运行服务
```bash
# 启动 API Gateway
go run cmd/api-gateway/main.go

# 启动应用管理服务
go run cmd/app-manager/main.go

# 启动其他服务...
```

## 项目结构

```
paas-platform/
├── cmd/                    # 各服务的入口文件
│   ├── api-gateway/
│   ├── app-manager/
│   ├── cicd-service/
│   ├── user-service/
│   └── ...
├── internal/               # 内部包
│   ├── app/               # 应用管理
│   ├── auth/              # 认证授权
│   ├── cicd/              # CI/CD
│   ├── config/            # 配置管理
│   ├── database/          # 数据库
│   ├── monitor/           # 监控
│   └── common/            # 公共组件
├── pkg/                   # 公共库
│   ├── logger/            # 日志
│   ├── errors/            # 错误处理
│   ├── utils/             # 工具函数
│   └── middleware/        # 中间件
├── api/                   # API 定义
│   ├── openapi/           # OpenAPI 规范
│   └── proto/             # gRPC 定义
├── configs/               # 配置文件
├── deployments/           # 部署配置
│   ├── docker/
│   ├── k8s/
│   └── helm/
├── docs/                  # 文档
├── scripts/               # 脚本
├── test/                  # 测试
├── web/                   # 前端代码
└── examples/              # 示例应用
```

## 开发指南

### 代码规范

- 遵循 Go 官方代码规范
- 使用 gofmt 格式化代码
- 添加详细的中文注释
- 编写单元测试

### API 设计

- RESTful API 设计
- 统一的响应格式
- 完整的错误处理
- API 版本管理

### 数据库设计

- 使用 GORM 作为 ORM
- 数据库迁移管理
- 多数据库适配器模式

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License