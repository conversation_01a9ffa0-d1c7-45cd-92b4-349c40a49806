# GitLab CI/CD 流水线配置
# 实现自动化构建、测试和部署

# 全局变量
variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  REGISTRY: $CI_REGISTRY
  NAMESPACE: paas
  KU<PERSON>CONFIG: /tmp/kubeconfig
  HELM_EXPERIMENTAL_OCI: 1

# 阶段定义
stages:
  - validate
  - test
  - build
  - security
  - deploy-dev
  - deploy-staging
  - deploy-prod
  - cleanup

# 模板定义
.docker_template: &docker_template
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

.kubectl_template: &kubectl_template
  image: bitnami/kubectl:latest
  before_script:
    - echo $KUBECONFIG_CONTENT | base64 -d > $KUBECONFIG
    - chmod 600 $KUBECONFIG

.helm_template: &helm_template
  image: alpine/helm:latest
  before_script:
    - echo $KUBECONFIG_CONTENT | base64 -d > $KUBECONFIG
    - chmod 600 $KUBECONFIG
    - helm repo add stable https://charts.helm.sh/stable
    - helm repo update

# 代码质量检查
code_quality:
  stage: validate
  image: golangci/golangci-lint:latest
  script:
    - golangci-lint run --timeout 10m
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 前端代码检查
frontend_lint:
  stage: validate
  image: node:18-alpine
  cache:
    paths:
      - web/node_modules/
  before_script:
    - cd web
    - npm ci
  script:
    - npm run lint
    - npm run type-check
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 单元测试 - 后端
test_backend:
  stage: test
  image: golang:1.21-alpine
  services:
    - postgres:15-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_pass
    DATABASE_URL: ********************************************/test_db?sslmode=disable
    REDIS_URL: redis://redis:6379
  before_script:
    - apk add --no-cache git make
    - go mod download
  script:
    - make test-unit
    - make test-integration
  coverage: '/coverage: \d+\.\d+% of statements/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - coverage.html
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 单元测试 - 前端
test_frontend:
  stage: test
  image: node:18-alpine
  cache:
    paths:
      - web/node_modules/
  before_script:
    - cd web
    - npm ci
  script:
    - npm run test:unit
    - npm run test:coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: web/coverage/cobertura-coverage.xml
    paths:
      - web/coverage/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 端到端测试
e2e_test:
  stage: test
  image: cypress/included:12.17.0
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    CYPRESS_baseUrl: http://localhost:3000
  before_script:
    - docker-compose -f docker-compose.test.yml up -d
    - sleep 30  # 等待服务启动
  script:
    - cd web
    - npm ci
    - npm run test:e2e:headless
  after_script:
    - docker-compose -f docker-compose.test.yml down
  artifacts:
    when: always
    paths:
      - web/cypress/screenshots/
      - web/cypress/videos/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual

# 构建后端服务镜像
.build_service_template: &build_service_template
  <<: *docker_template
  stage: build
  script:
    - |
      for service in api-gateway user-service app-manager ci-cd-service monitoring-service notification-service load-balancer; do
        if [ -d "services/$service" ]; then
          echo "Building $service..."
          docker build \
            --build-arg VERSION=$CI_COMMIT_SHA \
            --build-arg BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ) \
            --build-arg GIT_COMMIT=$CI_COMMIT_SHA \
            -t $REGISTRY/$NAMESPACE/$service:$CI_COMMIT_SHA \
            -t $REGISTRY/$NAMESPACE/$service:latest \
            -f docker/Dockerfile.go-service \
            services/$service
          
          docker push $REGISTRY/$NAMESPACE/$service:$CI_COMMIT_SHA
          docker push $REGISTRY/$NAMESPACE/$service:latest
        fi
      done
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

build_services:
  <<: *build_service_template

# 构建前端镜像
build_frontend:
  <<: *docker_template
  stage: build
  script:
    - |
      cd web
      docker build \
        --build-arg VERSION=$CI_COMMIT_SHA \
        --build-arg BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ) \
        --build-arg GIT_COMMIT=$CI_COMMIT_SHA \
        -t $REGISTRY/$NAMESPACE/web:$CI_COMMIT_SHA \
        -t $REGISTRY/$NAMESPACE/web:latest \
        -f ../docker/Dockerfile.frontend \
        .
      
      docker push $REGISTRY/$NAMESPACE/web:$CI_COMMIT_SHA
      docker push $REGISTRY/$NAMESPACE/web:latest
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# 安全扫描
security_scan:
  stage: security
  image: aquasec/trivy:latest
  script:
    - |
      for service in api-gateway user-service app-manager ci-cd-service monitoring-service notification-service load-balancer web; do
        echo "Scanning $service..."
        trivy image --exit-code 0 --severity HIGH,CRITICAL --format json --output $service-scan.json $REGISTRY/$NAMESPACE/$service:$CI_COMMIT_SHA
        trivy image --exit-code 1 --severity CRITICAL $REGISTRY/$NAMESPACE/$service:$CI_COMMIT_SHA
      done
  artifacts:
    paths:
      - "*-scan.json"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# 部署到开发环境
deploy_dev:
  <<: *helm_template
  stage: deploy-dev
  environment:
    name: development
    url: https://dev.paas.example.com
  script:
    - |
      helm upgrade --install paas-dev ./helm/paas \
        --namespace paas-dev \
        --create-namespace \
        --set image.tag=$CI_COMMIT_SHA \
        --set image.registry=$REGISTRY \
        --set environment=development \
        --set replicaCount=1 \
        --set ingress.hosts[0].host=dev.paas.example.com \
        --wait \
        --timeout=10m
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# 部署到预发布环境
deploy_staging:
  <<: *helm_template
  stage: deploy-staging
  environment:
    name: staging
    url: https://staging.paas.example.com
  script:
    - |
      helm upgrade --install paas-staging ./helm/paas \
        --namespace paas-staging \
        --create-namespace \
        --set image.tag=$CI_COMMIT_SHA \
        --set image.registry=$REGISTRY \
        --set environment=staging \
        --set replicaCount=2 \
        --set ingress.hosts[0].host=staging.paas.example.com \
        --wait \
        --timeout=10m
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
    - if: $CI_COMMIT_TAG

# 部署到生产环境
deploy_prod:
  <<: *helm_template
  stage: deploy-prod
  environment:
    name: production
    url: https://paas.example.com
  script:
    - |
      # 使用生产部署脚本
      chmod +x scripts/deploy-production.sh
      export VERSION=$CI_COMMIT_SHA
      export ENVIRONMENT=production
      export NAMESPACE=paas-prod
      export DEPLOYMENT_TYPE=rolling
      ./scripts/deploy-production.sh
  rules:
    - if: $CI_COMMIT_TAG
      when: manual
  only:
    - tags

# 性能测试
performance_test:
  stage: deploy-staging
  image: loadimpact/k6:latest
  script:
    - k6 run --out json=performance-results.json tests/performance/load-test.js
  artifacts:
    paths:
      - performance-results.json
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

# 清理旧镜像
cleanup_images:
  <<: *docker_template
  stage: cleanup
  script:
    - |
      # 保留最近 10 个版本的镜像
      for service in api-gateway user-service app-manager ci-cd-service monitoring-service notification-service load-balancer web; do
        echo "Cleaning up old images for $service..."
        # 这里可以添加清理逻辑
        # 例如使用 GitLab Container Registry API 或 Docker Registry API
      done
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
  allow_failure: true

# 发布通知
notify_success:
  stage: cleanup
  image: curlimages/curl:latest
  script:
    - |
      if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
          --data "{\"text\":\"✅ PaaS 平台部署成功\",\"attachments\":[{\"text\":\"版本: $CI_COMMIT_SHA\\n环境: $CI_ENVIRONMENT_NAME\\n分支: $CI_COMMIT_BRANCH\\n提交者: $GITLAB_USER_NAME\"}]}" \
          $SLACK_WEBHOOK_URL
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG
  when: on_success

notify_failure:
  stage: cleanup
  image: curlimages/curl:latest
  script:
    - |
      if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
          --data "{\"text\":\"❌ PaaS 平台部署失败\",\"attachments\":[{\"text\":\"版本: $CI_COMMIT_SHA\\n环境: $CI_ENVIRONMENT_NAME\\n分支: $CI_COMMIT_BRANCH\\n提交者: $GITLAB_USER_NAME\\n流水线: $CI_PIPELINE_URL\"}]}" \
          $SLACK_WEBHOOK_URL
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG
  when: on_failure
