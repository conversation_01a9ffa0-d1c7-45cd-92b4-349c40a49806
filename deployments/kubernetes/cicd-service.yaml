# CI/CD 服务 Kubernetes 部署配置
apiVersion: v1
kind: Namespace
metadata:
  name: cicd-system
  labels:
    name: cicd-system

---
# ConfigMap 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: cicd-config
  namespace: cicd-system
data:
  cicd-service.yaml: |
    server:
      port: 8082
      mode: release
    database:
      driver: postgres
      dsn: "host=postgres-service user=cicd password=cicd123 dbname=cicd_db port=5432 sslmode=disable TimeZone=Asia/Shanghai"
    redis:
      addr: "redis-service:6379"
      password: ""
      db: 1
    log:
      level: info
      format: json
    build:
      workspace:
        base_path: "/tmp/builds"
    docker:
      host: "unix:///var/run/docker.sock"
    webhook:
      secret: "webhook-secret-key"

---
# Secret 配置
apiVersion: v1
kind: Secret
metadata:
  name: cicd-secrets
  namespace: cicd-system
type: Opaque
data:
  database-password: Y2ljZDEyMw==  # base64 encoded "cicd123"
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleQ==  # base64 encoded "your-jwt-secret-key"
  webhook-secret: d2ViaG9vay1zZWNyZXQta2V5  # base64 encoded "webhook-secret-key"

---
# PersistentVolumeClaim 数据持久化
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: cicd-data-pvc
  namespace: cicd-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: build-workspace-pvc
  namespace: cicd-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: standard

---
# Deployment 部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cicd-service
  namespace: cicd-system
  labels:
    app: cicd-service
    version: v1.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cicd-service
  template:
    metadata:
      labels:
        app: cicd-service
        version: v1.0.0
    spec:
      serviceAccountName: cicd-service-account
      containers:
      - name: cicd-service
        image: cicd-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8082
          name: http
        env:
        - name: GIN_MODE
          value: "release"
        - name: LOG_LEVEL
          value: "info"
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cicd-secrets
              key: database-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: cicd-secrets
              key: jwt-secret
        - name: WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: cicd-secrets
              key: webhook-secret
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
          readOnly: true
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
        - name: build-workspace
          mountPath: /tmp/builds
        - name: docker-socket
          mountPath: /var/run/docker.sock
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8082
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8082
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          runAsGroup: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config-volume
        configMap:
          name: cicd-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: cicd-data-pvc
      - name: logs-volume
        emptyDir: {}
      - name: build-workspace
        persistentVolumeClaim:
          claimName: build-workspace-pvc
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
          type: Socket
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
# Service 服务配置
apiVersion: v1
kind: Service
metadata:
  name: cicd-service
  namespace: cicd-system
  labels:
    app: cicd-service
spec:
  type: ClusterIP
  ports:
  - port: 8082
    targetPort: 8082
    protocol: TCP
    name: http
  selector:
    app: cicd-service

---
# ServiceAccount 服务账户
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cicd-service-account
  namespace: cicd-system

---
# ClusterRole 集群角色
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cicd-service-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["extensions", "networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
# ClusterRoleBinding 集群角色绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cicd-service-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cicd-service-role
subjects:
- kind: ServiceAccount
  name: cicd-service-account
  namespace: cicd-system

---
# Ingress 入口配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cicd-service-ingress
  namespace: cicd-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - cicd.example.com
    secretName: cicd-tls-secret
  rules:
  - host: cicd.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cicd-service
            port:
              number: 8082

---
# HorizontalPodAutoscaler 水平扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cicd-service-hpa
  namespace: cicd-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cicd-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# PodDisruptionBudget 中断预算
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cicd-service-pdb
  namespace: cicd-system
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: cicd-service

---
# NetworkPolicy 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cicd-service-netpol
  namespace: cicd-system
spec:
  podSelector:
    matchLabels:
      app: cicd-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8082
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 80    # HTTP
