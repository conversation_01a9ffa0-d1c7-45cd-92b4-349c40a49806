# 配置服务 Kubernetes 部署文件
apiVersion: v1
kind: Namespace
metadata:
  name: paas-system
  labels:
    name: paas-system

---
# ConfigMap 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: config-service-config
  namespace: paas-system
data:
  config-service.yaml: |
    port: 8084
    database:
      type: postgres
      host: postgres-service
      port: 5432
      user: paas_user
      dbname: paas_config
      sslmode: disable
    redis:
      addr: redis-service:6379
      db: 0
    log:
      level: info
      format: json
    service:
      cache:
        enabled: true
        ttl: 1h
      audit:
        enabled: true
        retention: 90d
      validation:
        enabled: true
      hot_reload:
        enabled: true
        interval: 30s

---
# Secret 密钥
apiVersion: v1
kind: Secret
metadata:
  name: config-service-secret
  namespace: paas-system
type: Opaque
data:
  db-password: cGFhc19wYXNzd29yZA==  # paas_password
  encryption-key: cGFhcy1jb25maWctZW5jcnlwdGlvbi1rZXktMzItY2hhcnM=  # paas-config-encryption-key-32-chars
  redis-password: ""

---
# Deployment 部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: config-service
  namespace: paas-system
  labels:
    app: config-service
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: config-service
  template:
    metadata:
      labels:
        app: config-service
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8084"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: config-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: config-service
        image: paas/config-service:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8084
          protocol: TCP
        env:
        - name: DB_TYPE
          value: "postgres"
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_USER
          value: "paas_user"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: config-service-secret
              key: db-password
        - name: DB_NAME
          value: "paas_config"
        - name: DB_SSLMODE
          value: "disable"
        - name: REDIS_ADDR
          value: "redis-service:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: config-service-secret
              key: redis-password
        - name: REDIS_DB
          value: "0"
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: config-service-secret
              key: encryption-key
        - name: LOG_LEVEL
          value: "info"
        - name: LOG_FORMAT
          value: "json"
        - name: PORT
          value: "8084"
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
          readOnly: true
        - name: data-volume
          mountPath: /app/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: config-volume
        configMap:
          name: config-service-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: config-service-pvc
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - config-service
              topologyKey: kubernetes.io/hostname

---
# Service 服务
apiVersion: v1
kind: Service
metadata:
  name: config-service
  namespace: paas-system
  labels:
    app: config-service
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8084"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8084
    targetPort: http
    protocol: TCP
  selector:
    app: config-service

---
# ServiceAccount 服务账户
apiVersion: v1
kind: ServiceAccount
metadata:
  name: config-service
  namespace: paas-system
  labels:
    app: config-service

---
# ClusterRole 集群角色
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: config-service
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding 集群角色绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: config-service
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: config-service
subjects:
- kind: ServiceAccount
  name: config-service
  namespace: paas-system

---
# PersistentVolumeClaim 持久化存储
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: config-service-pvc
  namespace: paas-system
  labels:
    app: config-service
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
# HorizontalPodAutoscaler 水平扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: config-service-hpa
  namespace: paas-system
  labels:
    app: config-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: config-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# PodDisruptionBudget 中断预算
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: config-service-pdb
  namespace: paas-system
  labels:
    app: config-service
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: config-service

---
# NetworkPolicy 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: config-service-netpol
  namespace: paas-system
  labels:
    app: config-service
spec:
  podSelector:
    matchLabels:
      app: config-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: paas-system
    - namespaceSelector:
        matchLabels:
          name: default
    ports:
    - protocol: TCP
      port: 8084
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: paas-system
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
  - to: []
    ports:
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
