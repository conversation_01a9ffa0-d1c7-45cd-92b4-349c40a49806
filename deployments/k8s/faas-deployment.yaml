apiVersion: v1
kind: Namespace
metadata:
  name: paas-faas
  labels:
    name: paas-faas
    type: faas

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: faas-config
  namespace: paas-faas
data:
  config.yaml: |
    server:
      port: 8087
      host: "0.0.0.0"
    
    executor:
      max_concurrent_executions: 100
      default_timeout: "30s"
      max_execution_time: "5m"
      container_pool_size: 10
      prewarm_containers: 3
      cleanup_interval: "5m"
      
    resources:
      cpu_limit: "0.5"
      memory_limit: "512m"
      disk_limit: "1g"
    
    database:
      host: postgres-service
      port: 5432
      name: paas_faas
      user: paas_user
      password: paas_password
      
    redis:
      host: redis-service
      port: 6379
      password: paas_redis_password
      
    logging:
      level: info
      format: json

---
apiVersion: v1
kind: Secret
metadata:
  name: faas-secrets
  namespace: paas-faas
type: Opaque
data:
  db-password: cGFhc19wYXNzd29yZA== # paas_password
  redis-password: cGFhc19yZWRpc19wYXNzd29yZA== # paas_redis_password

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: faas-executor
  namespace: paas-faas
  labels:
    app: faas-executor
    component: executor
spec:
  replicas: 3
  selector:
    matchLabels:
      app: faas-executor
  template:
    metadata:
      labels:
        app: faas-executor
        component: executor
    spec:
      serviceAccountName: faas-executor
      containers:
      - name: faas-executor
        image: paas/faas-executor:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8087
          name: http
        env:
        - name: CONFIG_FILE
          value: "/etc/config/config.yaml"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: faas-secrets
              key: db-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: faas-secrets
              key: redis-password
        volumeMounts:
        - name: config-volume
          mountPath: /etc/config
        - name: docker-socket
          mountPath: /var/run/docker.sock
        - name: temp-storage
          mountPath: /tmp/faas
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/faas/functions/health
            port: 8087
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/faas/functions/health
            port: 8087
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config-volume
        configMap:
          name: faas-config
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
          type: Socket
      - name: temp-storage
        emptyDir:
          sizeLimit: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: faas-executor-service
  namespace: paas-faas
  labels:
    app: faas-executor
spec:
  type: ClusterIP
  ports:
  - port: 8087
    targetPort: 8087
    protocol: TCP
    name: http
  selector:
    app: faas-executor

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: faas-executor
  namespace: paas-faas

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: faas-executor
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: faas-executor
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: faas-executor
subjects:
- kind: ServiceAccount
  name: faas-executor
  namespace: paas-faas

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: faas-executor-hpa
  namespace: paas-faas
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: faas-executor
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: faas-ingress
  namespace: paas-faas
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - faas.paas.example.com
    secretName: faas-tls
  rules:
  - host: faas.paas.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: faas-executor-service
            port:
              number: 8087
