version: '3.8'

services:
  # SaaS服务管理器
  saas-manager:
    build:
      context: ../../
      dockerfile: docker/Dockerfile.saas-manager
    container_name: paas-saas-manager
    ports:
      - "8088:8088"
    environment:
      - SAAS_PORT=8088
      - SA<PERSON>_DEFAULT_REPLICAS=2
      - SAAS_MAX_REPLICAS=10
      - SAAS_MIN_REPLICAS=1
      - SAAS_HEALTH_CHECK_INTERVAL=30s
      - SAAS_SCALE_UP_THRESHOLD=80.0
      - SAAS_SCALE_DOWN_THRESHOLD=20.0
      - SAAS_AUTO_SCALING_ENABLED=true
      - DOCKER_HOST=unix:///var/run/docker.sock
      - LOG_LEVEL=info
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - saas_logs:/app/logs
      - saas_data:/app/data
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/api/v1/saas/services/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - postgres
      - load-balancer
    labels:
      - "paas.service=saas-manager"
      - "paas.type=saas"
      - "paas.version=1.0.0"

  # 负载均衡器
  load-balancer:
    build:
      context: ../../
      dockerfile: docker/Dockerfile.load-balancer
    container_name: paas-load-balancer
    ports:
      - "8086:8086"
    environment:
      - LB_PORT=8086
      - LB_ALGORITHM=round_robin
      - LB_HEALTH_CHECK_INTERVAL=30s
      - LB_HEALTH_CHECK_TIMEOUT=5s
      - LB_SESSION_AFFINITY=false
      - LOG_LEVEL=info
    volumes:
      - lb_logs:/app/logs
      - lb_config:/app/config
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - service-registry
    labels:
      - "paas.service=load-balancer"
      - "paas.type=infrastructure"

  # 服务注册中心
  service-registry:
    build:
      context: ../../
      dockerfile: docker/Dockerfile.service-registry
    container_name: paas-service-registry
    ports:
      - "8089:8089"
    environment:
      - REGISTRY_PORT=8089
      - REGISTRY_CLEANUP_INTERVAL=5m
      - REGISTRY_INSTANCE_TTL=10m
      - LOG_LEVEL=info
    volumes:
      - registry_logs:/app/logs
      - registry_data:/app/data
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8089/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - redis
    labels:
      - "paas.service=service-registry"
      - "paas.type=infrastructure"

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: paas-redis
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=paas_redis_password
    command: redis-server --requirepass paas_redis_password
    volumes:
      - redis_data:/data
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=paas_saas
      - POSTGRES_USER=paas_user
      - POSTGRES_PASSWORD=paas_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../scripts/init-saas-db.sql:/docker-entrypoint-initdb.d/init-saas-db.sql
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U paas_user -d paas_saas"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: paas-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - paas-network
    restart: unless-stopped
    depends_on:
      - load-balancer
    labels:
      - "paas.service=nginx"
      - "paas.type=proxy"

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: paas-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - paas-network
    restart: unless-stopped

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: paas-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - paas-network
    restart: unless-stopped
    depends_on:
      - prometheus

volumes:
  saas_logs:
    driver: local
  saas_data:
    driver: local
  lb_logs:
    driver: local
  lb_config:
    driver: local
  registry_logs:
    driver: local
  registry_data:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  paas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
