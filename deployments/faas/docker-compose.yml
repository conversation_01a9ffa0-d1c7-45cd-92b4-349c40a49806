version: '3.8'

services:
  # FaaS执行器服务
  faas-executor:
    build:
      context: ../../
      dockerfile: docker/Dockerfile.faas-executor
    container_name: paas-faas-executor
    ports:
      - "8087:8087"
    environment:
      - FAAS_PORT=8087
      - FAAS_MAX_CONCURRENT_EXECUTIONS=100
      - FAAS_DEFAULT_TIMEOUT=30s
      - FAAS_MAX_EXECUTION_TIME=5m
      - FAAS_CONTAINER_POOL_SIZE=10
      - FAAS_PREWARM_CONTAINERS=3
      - FAAS_CLEANUP_INTERVAL=5m
      - FAAS_CPU_LIMIT=0.5
      - FAAS_MEMORY_LIMIT=512m
      - FAAS_DISK_LIMIT=1g
      - DOCKER_HOST=unix:///var/run/docker.sock
      - LOG_LEVEL=info
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - faas_logs:/app/logs
      - faas_temp:/tmp/faas
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8087/api/v1/faas/functions/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - postgres
    labels:
      - "paas.service=faas-executor"
      - "paas.type=faas"
      - "paas.version=1.0.0"

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: paas-redis
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=paas_redis_password
    command: redis-server --requirepass paas_redis_password
    volumes:
      - redis_data:/data
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=paas_faas
      - POSTGRES_USER=paas_user
      - POSTGRES_PASSWORD=paas_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../scripts/init-faas-db.sql:/docker-entrypoint-initdb.d/init-faas-db.sql
    networks:
      - paas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U paas_user -d paas_faas"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: paas-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - paas-network
    restart: unless-stopped

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: paas-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - paas-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Jaeger链路追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: paas-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - paas-network
    restart: unless-stopped

  # 日志收集器
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: paas-fluentd
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./fluentd/fluent.conf:/fluentd/etc/fluent.conf
      - faas_logs:/var/log/faas
    networks:
      - paas-network
    restart: unless-stopped

volumes:
  faas_logs:
    driver: local
  faas_temp:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  paas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
