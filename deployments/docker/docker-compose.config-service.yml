# 配置服务 Docker Compose 文件
version: '3.8'

services:
  # 配置服务
  config-service:
    build:
      context: ../../
      dockerfile: deployments/docker/config-service.Dockerfile
    container_name: paas-config-service
    restart: unless-stopped
    ports:
      - "8084:8084"
    environment:
      # 数据库配置
      DB_TYPE: postgres
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: paas_user
      DB_PASSWORD: paas_password
      DB_NAME: paas_config
      DB_SSLMODE: disable
      
      # Redis 配置
      REDIS_ADDR: redis:6379
      REDIS_PASSWORD: ""
      REDIS_DB: 0
      
      # 加密配置
      ENCRYPTION_KEY: "paas-config-encryption-key-32-chars"
      
      # 日志配置
      LOG_LEVEL: info
      LOG_FORMAT: json
      
      # 服务配置
      PORT: 8084
    volumes:
      - config_data:/app/data
      - ./configs:/app/configs:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - paas-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: paas_config
      POSTGRES_USER: paas_user
      POSTGRES_PASSWORD: paas_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - paas-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U paas_user -d paas_config"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: paas-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - paas-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: paas-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - paas-network
    depends_on:
      - config-service

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: paas-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3000:3000"
    networks:
      - paas-network
    depends_on:
      - prometheus

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: paas-nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - paas-network
    depends_on:
      - config-service

# 网络配置
networks:
  paas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  config_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local
