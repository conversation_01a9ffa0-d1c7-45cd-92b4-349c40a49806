# PaaS 平台生产环境 Docker Compose 配置
# 完整的生产环境部署配置，包含所有服务和监控组件

version: '3.8'

# 网络配置
networks:
  paas-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  paas-logs:
    driver: local
  paas-data:
    driver: local

# 服务配置
services:
  # 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: paas-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: paas_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - paas-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: paas-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-password}
    volumes:
      - redis-data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - paas-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 用户认证服务
  user-service:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile.multi-stage
      args:
        SERVICE_NAME: user-service
        VERSION: ${VERSION:-latest}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: paas-user-service
    restart: unless-stopped
    environment:
      - SERVICE_NAME=user-service
      - PORT=8081
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=paas_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - paas-logs:/app/logs
      - paas-data:/app/data
    ports:
      - "8081:8081"
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 应用管理服务
  app-manager:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile.multi-stage
      args:
        SERVICE_NAME: app-manager
        VERSION: ${VERSION:-latest}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: paas-app-manager
    restart: unless-stopped
    environment:
      - SERVICE_NAME=app-manager
      - PORT=8082
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=paas_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - paas-logs:/app/logs
      - paas-data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    ports:
      - "8082:8082"
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CI/CD 服务
  cicd-service:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile.multi-stage
      args:
        SERVICE_NAME: cicd-service
        VERSION: ${VERSION:-latest}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: paas-cicd-service
    restart: unless-stopped
    environment:
      - SERVICE_NAME=cicd-service
      - PORT=8083
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=paas_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - paas-logs:/app/logs
      - paas-data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    ports:
      - "8083:8083"
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务
  monitor-service:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile.multi-stage
      args:
        SERVICE_NAME: monitor-service
        VERSION: ${VERSION:-latest}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: paas-monitor-service
    restart: unless-stopped
    environment:
      - SERVICE_NAME=monitor-service
      - PORT=8084
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=paas_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - paas-logs:/app/logs
      - paas-data:/app/data
    ports:
      - "8084:8084"
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 通知服务
  notification-service:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile.multi-stage
      args:
        SERVICE_NAME: notification-service
        VERSION: ${VERSION:-latest}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: paas-notification-service
    restart: unless-stopped
    environment:
      - SERVICE_NAME=notification-service
      - PORT=8085
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=paas_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    volumes:
      - paas-logs:/app/logs
      - paas-data:/app/data
    ports:
      - "8085:8085"
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 负载均衡服务
  loadbalancer-service:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile.multi-stage
      args:
        SERVICE_NAME: loadbalancer-service
        VERSION: ${VERSION:-latest}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: paas-loadbalancer-service
    restart: unless-stopped
    environment:
      - SERVICE_NAME=loadbalancer-service
      - PORT=8086
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=paas_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - paas-logs:/app/logs
      - paas-data:/app/data
    ports:
      - "8086:8086"
    networks:
      - paas-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API 网关
  api-gateway:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile.multi-stage
      args:
        SERVICE_NAME: api-gateway
        VERSION: ${VERSION:-latest}
        BUILD_TIME: ${BUILD_TIME}
        GIT_COMMIT: ${GIT_COMMIT}
    container_name: paas-api-gateway
    restart: unless-stopped
    environment:
      - SERVICE_NAME=api-gateway
      - PORT=8080
      - USER_SERVICE_URL=http://user-service:8081
      - APP_MANAGER_URL=http://app-manager:8082
      - CICD_SERVICE_URL=http://cicd-service:8083
      - MONITOR_SERVICE_URL=http://monitor-service:8084
      - NOTIFICATION_SERVICE_URL=http://notification-service:8085
      - LOADBALANCER_SERVICE_URL=http://loadbalancer-service:8086
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - paas-logs:/app/logs
      - paas-data:/app/data
    ports:
      - "8080:8080"
    networks:
      - paas-network
    depends_on:
      - user-service
      - app-manager
      - cicd-service
      - monitor-service
      - notification-service
      - loadbalancer-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
