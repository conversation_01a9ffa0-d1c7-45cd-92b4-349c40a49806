# CI/CD 服务 Docker Compose 配置
# 包含 CI/CD 服务及其依赖服务的完整部署配置

version: '3.8'

services:
  # CI/CD 服务
  cicd-service:
    build:
      context: ../../
      dockerfile: deployments/docker/Dockerfile
    container_name: cicd-service
    restart: unless-stopped
    ports:
      - "8082:8082"
    environment:
      - GIN_MODE=release
      - LOG_LEVEL=info
      - DATABASE_DRIVER=postgres
      - DATABASE_DSN=host=postgres user=cicd password=cicd123 dbname=cicd_db port=5432 sslmode=disable TimeZone=Asia/Shanghai
      - REDIS_ADDR=redis:6379
      - REDIS_PASSWORD=
      - REDIS_DB=1
    volumes:
      - ./configs:/app/configs:ro
      - cicd_logs:/app/logs
      - cicd_data:/app/data
      - build_workspace:/tmp/builds
      - /var/run/docker.sock:/var/run/docker.sock:ro  # Docker socket 挂载
    depends_on:
      - postgres
      - redis
    networks:
      - cicd-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cicd.rule=Host(`cicd.example.com`)"
      - "traefik.http.routers.cicd.tls=true"
      - "traefik.http.services.cicd.loadbalancer.server.port=8082"

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: cicd-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=cicd_db
      - POSTGRES_USER=cicd
      - POSTGRES_PASSWORD=cicd123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - cicd-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cicd -d cicd_db"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: cicd-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - cicd-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Docker Registry (可选)
  registry:
    image: registry:2
    container_name: cicd-registry
    restart: unless-stopped
    environment:
      - REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY=/var/lib/registry
      - REGISTRY_AUTH=htpasswd
      - REGISTRY_AUTH_HTPASSWD_REALM=Registry Realm
      - REGISTRY_AUTH_HTPASSWD_PATH=/auth/htpasswd
    volumes:
      - registry_data:/var/lib/registry
      - ./auth:/auth:ro
    ports:
      - "5000:5000"
    networks:
      - cicd-network

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: cicd-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - cicd-service
    networks:
      - cicd-network

  # Prometheus 监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: cicd-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - cicd-network

  # Grafana 仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: cicd-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - cicd-network

# 网络配置
networks:
  cicd-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  registry_data:
    driver: local
  cicd_logs:
    driver: local
  cicd_data:
    driver: local
  build_workspace:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
