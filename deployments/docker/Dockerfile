# CI/CD 服务 Dockerfile
# 多阶段构建，优化镜像大小和安全性

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache git ca-certificates tzdata

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o cicd-service \
    ./cmd/cicd-service

# 运行阶段
FROM alpine:3.18

# 安装运行时依赖
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    curl \
    docker-cli \
    git \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -g 1001 -S cicd && \
    adduser -u 1001 -S cicd -G cicd

# 创建必要的目录
RUN mkdir -p /app/configs /app/logs /app/data /tmp/builds && \
    chown -R cicd:cicd /app /tmp/builds

# 切换到非 root 用户
USER cicd

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder --chown=cicd:cicd /app/cicd-service .

# 复制配置文件
COPY --chown=cicd:cicd configs/cicd-service.yaml ./configs/

# 设置环境变量
ENV GIN_MODE=release
ENV LOG_LEVEL=info

# 暴露端口
EXPOSE 8082

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8082/health || exit 1

# 启动命令
CMD ["./cicd-service"]
