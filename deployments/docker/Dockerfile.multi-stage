# PaaS 平台多阶段构建 Dockerfile
# 优化的生产环境镜像构建

# 第一阶段：构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建参数
ARG SERVICE_NAME
ARG VERSION=latest
ARG BUILD_TIME
ARG GIT_COMMIT

# 设置构建标志
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# 构建应用
RUN go build \
    -ldflags="-s -w -X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT}" \
    -o /app/bin/${SERVICE_NAME} \
    ./cmd/${SERVICE_NAME}/main.go

# 第二阶段：运行阶段
FROM alpine:3.18 AS runtime

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    curl \
    jq \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -g 1001 -S paas && \
    adduser -u 1001 -S paas -G paas

# 创建必要目录
RUN mkdir -p /app/bin /app/configs /app/logs /app/data && \
    chown -R paas:paas /app

# 切换到非 root 用户
USER paas

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
ARG SERVICE_NAME
COPY --from=builder --chown=paas:paas /app/bin/${SERVICE_NAME} /app/bin/

# 复制配置文件
COPY --chown=paas:paas configs/ /app/configs/

# 复制启动脚本
COPY --chown=paas:paas scripts/docker-entrypoint.sh /app/bin/
RUN chmod +x /app/bin/docker-entrypoint.sh

# 设置环境变量
ENV SERVICE_NAME=${SERVICE_NAME}
ENV CONFIG_PATH=/app/configs
ENV LOG_PATH=/app/logs
ENV DATA_PATH=/app/data

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8080}/health || exit 1

# 暴露端口
EXPOSE ${PORT:-8080}

# 设置入口点
ENTRYPOINT ["/app/bin/docker-entrypoint.sh"]

# 默认命令
CMD ["/app/bin/${SERVICE_NAME}"]

# 元数据标签
LABEL maintainer="PaaS Platform Team" \
      version="${VERSION}" \
      description="PaaS Platform ${SERVICE_NAME} Service" \
      org.opencontainers.image.title="paas-${SERVICE_NAME}" \
      org.opencontainers.image.description="PaaS Platform ${SERVICE_NAME} Service" \
      org.opencontainers.image.version="${VERSION}" \
      org.opencontainers.image.created="${BUILD_TIME}" \
      org.opencontainers.image.revision="${GIT_COMMIT}" \
      org.opencontainers.image.vendor="PaaS Platform" \
      org.opencontainers.image.licenses="MIT"
