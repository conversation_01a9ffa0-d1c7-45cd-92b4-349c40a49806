# PaaS 平台生产环境部署配置

# 基础配置
environment: production
namespace: paas-production

# 镜像配置
image:
  registry: registry.example.com
  tag: v1.0.0
  pullPolicy: Always
  pullSecrets:
    - name: registry-secret

# 副本配置
replicas:
  api-gateway: 3
  user-service: 3
  app-manager: 2
  cicd-service: 2
  monitor-service: 2
  notification-service: 2
  loadbalancer-service: 2

# 资源配置
resources:
  api-gateway:
    requests:
      cpu: 500m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  user-service:
    requests:
      cpu: 300m
      memory: 256Mi
    limits:
      cpu: 600m
      memory: 512Mi
  app-manager:
    requests:
      cpu: 500m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  cicd-service:
    requests:
      cpu: 400m
      memory: 512Mi
    limits:
      cpu: 800m
      memory: 1Gi
  monitor-service:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 400m
      memory: 512Mi
  notification-service:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 400m
      memory: 512Mi
  loadbalancer-service:
    requests:
      cpu: 300m
      memory: 256Mi
    limits:
      cpu: 600m
      memory: 512Mi

# 存储配置
storage:
  postgres:
    size: 100Gi
    storageClass: fast-ssd
    backup:
      enabled: true
      schedule: "0 2 * * *"
      retention: 30d
  redis:
    size: 20Gi
    storageClass: fast-ssd
  logs:
    size: 50Gi
    storageClass: standard
  data:
    size: 200Gi
    storageClass: standard

# 数据库配置
database:
  postgres:
    host: postgres-primary.paas-production.svc.cluster.local
    port: 5432
    database: paas_platform
    username: postgres
    ssl_mode: require
    max_connections: 100
    connection_timeout: 30s
    pool_size: 20
  redis:
    host: redis-master.paas-production.svc.cluster.local
    port: 6379
    database: 0
    password_secret: redis-secret
    max_connections: 50
    timeout: 5s

# 网络配置
networking:
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: letsencrypt-prod
    hosts:
      - host: paas.example.com
        paths:
          - path: /
            pathType: Prefix
            service: api-gateway
    tls:
      - secretName: paas-tls
        hosts:
          - paas.example.com
  
  service:
    type: ClusterIP
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-type: nlb
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp

# 安全配置
security:
  podSecurityPolicy:
    enabled: true
  networkPolicy:
    enabled: true
    ingress:
      - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
  rbac:
    enabled: true
    serviceAccount:
      create: true
      name: paas-platform
  secrets:
    jwt_secret: jwt-secret
    api_key: api-key-secret
    database_password: postgres-secret
    redis_password: redis-secret

# 监控配置
monitoring:
  prometheus:
    enabled: true
    scrapeInterval: 30s
    retention: 30d
    storage: 100Gi
  grafana:
    enabled: true
    adminPassword: grafana-admin-secret
  alertmanager:
    enabled: true
    config: alertmanager-config
  serviceMonitor:
    enabled: true
    interval: 30s
    path: /metrics

# 日志配置
logging:
  level: info
  format: json
  output: stdout
  fluentd:
    enabled: true
    elasticsearch:
      host: elasticsearch.logging.svc.cluster.local
      port: 9200
      index: paas-platform-prod
  retention: 30d

# 自动扩缩容配置
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60

# 健康检查配置
healthCheck:
  livenessProbe:
    httpGet:
      path: /health
      port: 8080
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /ready
      port: 8080
    initialDelaySeconds: 10
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# 备份配置
backup:
  enabled: true
  schedule: "0 2 * * *"
  retention: 30
  storage:
    type: s3
    bucket: paas-platform-backups
    region: us-west-2
    accessKey: backup-access-key
    secretKey: backup-secret-key

# 部署策略配置
deployment:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  progressDeadlineSeconds: 600
  revisionHistoryLimit: 10

# 环境变量配置
env:
  NODE_ENV: production
  LOG_LEVEL: info
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "true"
  CACHE_TTL: "3600"
  SESSION_TIMEOUT: "1800"
  MAX_REQUEST_SIZE: "10MB"
  RATE_LIMIT_REQUESTS: "1000"
  RATE_LIMIT_WINDOW: "60"

# 配置映射
configMaps:
  app-config:
    data:
      app.yaml: |
        server:
          port: 8080
          host: 0.0.0.0
          timeout: 30s
        database:
          max_connections: 100
          timeout: 30s
        cache:
          ttl: 3600
          max_size: 1000
        security:
          cors_enabled: true
          csrf_enabled: true
          rate_limiting: true

# 密钥配置
secrets:
  postgres-secret:
    type: Opaque
    data:
      host: cG9zdGdyZXMtcHJpbWFyeS5wYWFzLXByb2R1Y3Rpb24uc3ZjLmNsdXN0ZXIubG9jYWw=
      username: cG9zdGdyZXM=
      password: <base64-encoded-password>
      database: cGFhc19wbGF0Zm9ybQ==
  
  redis-secret:
    type: Opaque
    data:
      password: <base64-encoded-password>
  
  jwt-secret:
    type: Opaque
    data:
      secret: <base64-encoded-jwt-secret>

# 服务质量配置
qos:
  class: Guaranteed  # Guaranteed, Burstable, BestEffort

# 节点选择器
nodeSelector:
  node-type: compute
  environment: production

# 容忍度配置
tolerations:
  - key: "dedicated"
    operator: "Equal"
    value: "paas-platform"
    effect: "NoSchedule"

# 亲和性配置
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - paas-platform
        topologyKey: kubernetes.io/hostname

# 初始化容器配置
initContainers:
  - name: wait-for-db
    image: postgres:15-alpine
    command:
      - sh
      - -c
      - |
        until pg_isready -h postgres-primary -p 5432; do
          echo "Waiting for database..."
          sleep 2
        done
  
  - name: migrate-db
    image: migrate/migrate:latest
    command:
      - migrate
      - -path
      - /migrations
      - -database
      - **************************************************/paas_platform?sslmode=require
      - up

# 边车容器配置
sidecars:
  - name: log-forwarder
    image: fluent/fluent-bit:latest
    volumeMounts:
      - name: logs
        mountPath: /var/log
      - name: fluent-bit-config
        mountPath: /fluent-bit/etc

# 通知配置
notifications:
  slack:
    enabled: true
    webhook_url_secret: slack-webhook-secret
    channel: "#paas-alerts"
  email:
    enabled: true
    smtp_host: smtp.example.com
    smtp_port: 587
    username_secret: email-username-secret
    password_secret: email-password-secret
    from: "<EMAIL>"
    to:
      - "<EMAIL>"
      - "<EMAIL>"
