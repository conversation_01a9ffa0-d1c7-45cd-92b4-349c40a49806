# PaaS 平台测试环境部署配置

# 基础配置
environment: staging
namespace: paas-staging

# 镜像配置
image:
  registry: registry.example.com
  tag: latest
  pullPolicy: Always
  pullSecrets:
    - name: registry-secret

# 副本配置
replicas:
  api-gateway: 2
  user-service: 2
  app-manager: 1
  cicd-service: 1
  monitor-service: 1
  notification-service: 1
  loadbalancer-service: 1

# 资源配置
resources:
  api-gateway:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi
  user-service:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 300m
      memory: 256Mi
  app-manager:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi
  cicd-service:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 400m
      memory: 512Mi
  monitor-service:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi
  notification-service:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi
  loadbalancer-service:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 300m
      memory: 256Mi

# 存储配置
storage:
  postgres:
    size: 20Gi
    storageClass: standard
    backup:
      enabled: true
      schedule: "0 3 * * *"
      retention: 7d
  redis:
    size: 5Gi
    storageClass: standard
  logs:
    size: 10Gi
    storageClass: standard
  data:
    size: 50Gi
    storageClass: standard

# 数据库配置
database:
  postgres:
    host: postgres.paas-staging.svc.cluster.local
    port: 5432
    database: paas_platform_staging
    username: postgres
    ssl_mode: disable
    max_connections: 50
    connection_timeout: 30s
    pool_size: 10
  redis:
    host: redis.paas-staging.svc.cluster.local
    port: 6379
    database: 0
    password_secret: redis-secret
    max_connections: 20
    timeout: 5s

# 网络配置
networking:
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "false"
    hosts:
      - host: paas-staging.example.com
        paths:
          - path: /
            pathType: Prefix
            service: api-gateway
    tls:
      - secretName: paas-staging-tls
        hosts:
          - paas-staging.example.com
  
  service:
    type: ClusterIP

# 安全配置
security:
  podSecurityPolicy:
    enabled: false
  networkPolicy:
    enabled: false
  rbac:
    enabled: true
    serviceAccount:
      create: true
      name: paas-platform-staging
  secrets:
    jwt_secret: jwt-secret-staging
    api_key: api-key-secret-staging
    database_password: postgres-secret-staging
    redis_password: redis-secret-staging

# 监控配置
monitoring:
  prometheus:
    enabled: true
    scrapeInterval: 60s
    retention: 7d
    storage: 20Gi
  grafana:
    enabled: true
    adminPassword: grafana-admin-secret-staging
  alertmanager:
    enabled: false
  serviceMonitor:
    enabled: true
    interval: 60s
    path: /metrics

# 日志配置
logging:
  level: debug
  format: json
  output: stdout
  fluentd:
    enabled: false
  retention: 7d

# 自动扩缩容配置
autoscaling:
  enabled: false

# 健康检查配置
healthCheck:
  livenessProbe:
    httpGet:
      path: /health
      port: 8080
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /ready
      port: 8080
    initialDelaySeconds: 10
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# 备份配置
backup:
  enabled: false

# 部署策略配置
deployment:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
      maxSurge: 50%
  progressDeadlineSeconds: 300
  revisionHistoryLimit: 5

# 环境变量配置
env:
  NODE_ENV: staging
  LOG_LEVEL: debug
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "false"
  CACHE_TTL: "1800"
  SESSION_TIMEOUT: "3600"
  MAX_REQUEST_SIZE: "10MB"
  RATE_LIMIT_REQUESTS: "500"
  RATE_LIMIT_WINDOW: "60"

# 配置映射
configMaps:
  app-config:
    data:
      app.yaml: |
        server:
          port: 8080
          host: 0.0.0.0
          timeout: 30s
        database:
          max_connections: 50
          timeout: 30s
        cache:
          ttl: 1800
          max_size: 500
        security:
          cors_enabled: true
          csrf_enabled: false
          rate_limiting: false

# 密钥配置
secrets:
  postgres-secret-staging:
    type: Opaque
    data:
      host: cG9zdGdyZXMucGFhcy1zdGFnaW5nLnN2Yy5jbHVzdGVyLmxvY2Fs
      username: cG9zdGdyZXM=
      password: <base64-encoded-password>
      database: cGFhc19wbGF0Zm9ybV9zdGFnaW5n
  
  redis-secret-staging:
    type: Opaque
    data:
      password: <base64-encoded-password>
  
  jwt-secret-staging:
    type: Opaque
    data:
      secret: <base64-encoded-jwt-secret>

# 服务质量配置
qos:
  class: Burstable

# 节点选择器
nodeSelector:
  node-type: general
  environment: staging

# 容忍度配置
tolerations: []

# 亲和性配置
affinity: {}

# 初始化容器配置
initContainers:
  - name: wait-for-db
    image: postgres:15-alpine
    command:
      - sh
      - -c
      - |
        until pg_isready -h postgres -p 5432; do
          echo "Waiting for database..."
          sleep 2
        done

# 边车容器配置
sidecars: []

# 通知配置
notifications:
  slack:
    enabled: true
    webhook_url_secret: slack-webhook-secret-staging
    channel: "#paas-staging"
  email:
    enabled: false
