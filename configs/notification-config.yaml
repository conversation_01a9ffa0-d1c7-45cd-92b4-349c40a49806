# PaaS 平台告警通知系统配置文件
# 定义了各种通知渠道的配置和告警规则

# 🔔 通知服务配置
notification:
  # 全局配置
  global:
    timeout: "30s"
    max_retries: 3
    rate_limit: 100  # 每分钟最大发送数量
    
    # 默认模板
    default_templates:
      email_subject: "[{{ .Severity | upper }}] PaaS Platform Alert: {{ .AlertName }}"
      email_body: |
        告警详情:
        - 告警名称: {{ .AlertName }}
        - 严重程度: {{ .Severity }}
        - 服务名称: {{ .Service }}
        - 告警时间: {{ .StartsAt }}
        - 告警描述: {{ .Description }}
        - 处理建议: {{ .Summary }}
        
        {{ if .RunbookURL }}
        处理手册: {{ .RunbookURL }}
        {{ end }}
        
        {{ if .DashboardURL }}
        监控面板: {{ .DashboardURL }}
        {{ end }}

  # 📧 邮件通知配置
  email:
    enabled: true
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_app_password"
    from_email: "<EMAIL>"
    from_name: "PaaS Platform Alerts"
    use_tls: true
    
    # 收件人配置
    recipients:
      # 严重告警收件人
      critical:
        - "<EMAIL>"
        - "<EMAIL>"
        - "<EMAIL>"
      
      # 警告告警收件人
      warning:
        - "<EMAIL>"
        - "<EMAIL>"
      
      # 信息告警收件人
      info:
        - "<EMAIL>"
    
    # 邮件模板
    templates:
      critical:
        subject: "🚨 [CRITICAL] PaaS Platform - {{ .AlertName }}"
        body: |
          <h2 style="color: #d73027;">🚨 严重告警</h2>
          <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr><td><strong>告警名称</strong></td><td>{{ .AlertName }}</td></tr>
            <tr><td><strong>服务名称</strong></td><td>{{ .Service }}</td></tr>
            <tr><td><strong>严重程度</strong></td><td style="color: #d73027;">{{ .Severity }}</td></tr>
            <tr><td><strong>告警时间</strong></td><td>{{ .StartsAt }}</td></tr>
            <tr><td><strong>告警描述</strong></td><td>{{ .Description }}</td></tr>
          </table>
          <p><strong>处理建议:</strong> {{ .Summary }}</p>
          {{ if .RunbookURL }}<p><a href="{{ .RunbookURL }}">📖 查看处理手册</a></p>{{ end }}
          {{ if .DashboardURL }}<p><a href="{{ .DashboardURL }}">📊 查看监控面板</a></p>{{ end }}
        is_html: true
      
      warning:
        subject: "⚠️ [WARNING] PaaS Platform - {{ .AlertName }}"
        body: |
          <h2 style="color: #f46d43;">⚠️ 警告告警</h2>
          <p><strong>告警:</strong> {{ .Summary }}</p>
          <p><strong>服务:</strong> {{ .Service }}</p>
          <p><strong>时间:</strong> {{ .StartsAt }}</p>
          <p><strong>描述:</strong> {{ .Description }}</p>
          {{ if .RunbookURL }}<p><a href="{{ .RunbookURL }}">查看处理手册</a></p>{{ end }}
        is_html: true

  # 💬 Slack 通知配置
  slack:
    enabled: true
    webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
    username: "PaaS AlertBot"
    icon_emoji: ":rotating_light:"
    
    # 频道配置
    channels:
      critical: "#paas-critical-alerts"
      warning: "#paas-warnings"
      info: "#paas-info"
      security: "#paas-security"
      database: "#paas-database"
      gateway: "#paas-gateway"
    
    # 消息模板
    templates:
      critical:
        color: "danger"
        title: "🚨 严重告警: {{ .AlertName }}"
        text: |
          *服务:* {{ .Service }}
          *严重程度:* {{ .Severity }}
          *时间:* {{ .StartsAt }}
          *描述:* {{ .Description }}
          {{ if .RunbookURL }}*处理手册:* <{{ .RunbookURL }}|查看>{{ end }}
          {{ if .DashboardURL }}*监控面板:* <{{ .DashboardURL }}|查看>{{ end }}
      
      warning:
        color: "warning"
        title: "⚠️ 警告: {{ .AlertName }}"
        text: |
          *服务:* {{ .Service }}
          *描述:* {{ .Description }}
          *时间:* {{ .StartsAt }}

  # 📱 短信通知配置
  sms:
    enabled: false  # 默认关闭，需要配置短信服务商
    provider: "aliyun"  # aliyun, tencent, twilio
    access_key: "your_access_key"
    secret_key: "your_secret_key"
    sign_name: "PaaS平台"
    
    # 短信模板
    templates:
      critical: "【PaaS平台】严重告警：{{ .AlertName }}，服务：{{ .Service }}，请立即处理！"
      warning: "【PaaS平台】警告：{{ .AlertName }}，服务：{{ .Service }}，请关注。"
    
    # 接收人配置
    recipients:
      critical:
        - "+86138****1234"  # 运维负责人
        - "+86139****5678"  # 技术负责人
      warning:
        - "+86138****1234"

  # 🔔 钉钉通知配置
  dingtalk:
    enabled: true
    webhook_url: "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN"
    secret: "your_secret_key"
    
    # 消息模板
    templates:
      critical:
        msgtype: "markdown"
        title: "🚨 PaaS平台严重告警"
        text: |
          ## 🚨 严重告警
          
          **告警名称:** {{ .AlertName }}
          
          **服务名称:** {{ .Service }}
          
          **严重程度:** <font color=#d73027>{{ .Severity }}</font>
          
          **告警时间:** {{ .StartsAt }}
          
          **告警描述:** {{ .Description }}
          
          **处理建议:** {{ .Summary }}
          
          {{ if .RunbookURL }}[📖 处理手册]({{ .RunbookURL }}){{ end }}
          {{ if .DashboardURL }}[📊 监控面板]({{ .DashboardURL }}){{ end }}
      
      warning:
        msgtype: "text"
        text: "⚠️ PaaS平台警告\n告警: {{ .Summary }}\n服务: {{ .Service }}\n时间: {{ .StartsAt }}"

  # 🏢 企业微信通知配置
  wechat:
    enabled: false  # 默认关闭，需要配置企业微信
    corp_id: "your_corp_id"
    agent_id: 1000001
    secret: "your_secret"
    to_user: "@all"  # 发送给所有人
    to_party: ""     # 发送给特定部门
    to_tag: ""       # 发送给特定标签
    
    # 消息模板
    templates:
      critical: "🚨 PaaS平台严重告警\n告警: {{ .AlertName }}\n服务: {{ .Service }}\n时间: {{ .StartsAt }}\n描述: {{ .Description }}"
      warning: "⚠️ PaaS平台警告\n告警: {{ .AlertName }}\n服务: {{ .Service }}\n时间: {{ .StartsAt }}"

# 🚨 告警路由规则
routing:
  # 默认路由
  default:
    - type: "email"
      severity: ["critical", "warning"]
      recipients: "warning"
    - type: "slack"
      severity: ["critical", "warning", "info"]
      channel: "warning"
  
  # 按严重程度路由
  severity_routes:
    critical:
      - type: "email"
        recipients: "critical"
        template: "critical"
      - type: "slack"
        channel: "critical"
        template: "critical"
      - type: "dingtalk"
        template: "critical"
      - type: "sms"
        recipients: "critical"
        template: "critical"
    
    warning:
      - type: "email"
        recipients: "warning"
        template: "warning"
      - type: "slack"
        channel: "warning"
        template: "warning"
      - type: "dingtalk"
        template: "warning"
  
  # 按服务路由
  service_routes:
    user-service:
      - type: "slack"
        channel: "security"
        condition: "category == 'security'"
    
    database:
      - type: "slack"
        channel: "database"
      - type: "email"
        recipients: ["<EMAIL>"]
    
    api-gateway:
      - type: "slack"
        channel: "gateway"

# 🔇 静默规则
silencing:
  # 全局静默时间 (维护窗口)
  maintenance_windows:
    - name: "周末维护"
      start: "02:00"
      end: "06:00"
      days: ["saturday", "sunday"]
      timezone: "Asia/Shanghai"
    
    - name: "日常维护"
      start: "03:00"
      end: "04:00"
      days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
      timezone: "Asia/Shanghai"
  
  # 告警抑制规则
  inhibit_rules:
    # 服务下线时抑制其他告警
    - source_matchers:
        - alertname: "ServiceDown"
      target_matchers:
        - service: "{{ .service }}"
      equal: ["service", "instance"]
    
    # 严重告警抑制警告告警
    - source_matchers:
        - severity: "critical"
      target_matchers:
        - severity: "warning"
      equal: ["alertname", "service"]

# 📊 通知统计和监控
monitoring:
  # 统计配置
  statistics:
    enabled: true
    retention_days: 30
    
    # 统计指标
    metrics:
      - "total_notifications_sent"
      - "notifications_by_type"
      - "notifications_by_severity"
      - "notification_success_rate"
      - "notification_latency"
  
  # 健康检查
  health_check:
    enabled: true
    interval: "5m"
    timeout: "10s"
    
    # 检查项目
    checks:
      - name: "email_smtp"
        type: "smtp"
        config:
          host: "smtp.gmail.com"
          port: 587
      
      - name: "slack_webhook"
        type: "http"
        config:
          url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
          method: "POST"
      
      - name: "dingtalk_webhook"
        type: "http"
        config:
          url: "https://oapi.dingtalk.com/robot/send"
          method: "POST"

# 🔧 高级配置
advanced:
  # 重试配置
  retry:
    max_attempts: 3
    backoff_strategy: "exponential"  # linear, exponential
    initial_delay: "1s"
    max_delay: "60s"
  
  # 批量发送配置
  batch:
    enabled: true
    max_batch_size: 100
    flush_interval: "10s"
  
  # 限流配置
  rate_limiting:
    enabled: true
    global_limit: 1000  # 每小时全局限制
    per_channel_limit: 100  # 每小时每渠道限制
    
    # 突发限制
    burst_limit: 10  # 突发请求限制
    burst_window: "1m"  # 突发窗口时间
  
  # 去重配置
  deduplication:
    enabled: true
    window: "5m"  # 去重时间窗口
    key_fields: ["alertname", "service", "severity"]

---
# 📝 配置说明
# 
# 本配置文件定义了 PaaS 平台的完整告警通知系统：
# 
# 1. 🔔 多渠道通知：邮件、Slack、短信、钉钉、企业微信
# 2. 🚨 智能路由：按严重程度、服务、类别路由
# 3. 🔇 静默管理：维护窗口、抑制规则
# 4. 📊 监控统计：发送统计、健康检查
# 5. 🔧 高级功能：重试、批量、限流、去重
# 
# 使用此配置可以实现完整的告警通知管理。
