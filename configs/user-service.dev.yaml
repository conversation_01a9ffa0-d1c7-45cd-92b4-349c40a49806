# 用户认证服务开发环境配置文件
# 专门负责用户管理、认证授权、权限控制等功能
# ⚠️ 警告：此配置仅适用于开发环境，绝不能在生产环境使用！

# 服务器配置
server:
  port: 8085                          # 用户服务端口
  mode: debug                         # 运行模式: debug (开发)
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置 - 开发环境使用SQLite
database:
  driver: sqlite                      # 数据库驱动
  dsn: "./data/user-service.db"       # 数据库文件路径
  max_open_conns: 50                  # 最大打开连接数
  max_idle_conns: 10                  # 最大空闲连接数
  conn_max_lifetime: 1h               # 连接最大生命周期
  log_level: info                     # 数据库日志级别

# Redis配置 - 开发环境 (可选)
redis:
  addr: "localhost:6379"              # Redis地址
  password: ""                        # Redis密码
  db: 0                              # Redis数据库编号
  pool_size: 5                       # 连接池大小 (开发环境较小)
  min_idle_conns: 2                  # 最小空闲连接数
  dial_timeout: 5s                   # 连接超时时间

# 日志配置 - 开发环境详细日志
log:
  level: debug                        # 日志级别: debug (开发详细日志)
  format: text                        # 日志格式: text (开发易读)
  output: stdout                      # 日志输出: stdout
  service_name: "user-service-dev"    # 服务名称

# 🔓 安全配置 - 开发环境认证设置
security:
  # JWT认证配置 - 开发环境简化
  jwt:
    enabled: false                    # ❌ 禁用JWT校验 (开发环境)
    dev_mode: true                    # ✅ 启用开发模式
    dev_token: "user-service-dev-token-2024" # 🔑 开发令牌
    secret: "user-service-dev-jwt-secret" # 开发JWT密钥
    expires_in: 24h                   # Token过期时间
    refresh_expires_in: 168h          # 刷新Token过期时间
    
    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "dev-user-001"              # 开发用户ID
      tenant_id: "dev-tenant-001"     # 开发租户ID
      username: "认证开发者"           # 开发用户名
      email: "auth-dev@localhost"     # 开发用户邮箱
      roles: ["admin", "user_manager", "auth_admin"] # 认证管理权限
      permissions: ["*"]              # 所有权限
    
    # 🛤️ 跳过认证的路径
    skip_paths:
      - "/health"
      - "/ready"
      - "/metrics"
      - "/api/v1/auth/login"
      - "/api/v1/auth/register"
      - "/api/v1/auth/refresh"
      - "/api/v1/auth/reset-password"
      - "/api/v1/auth/verify-email"
      - "/swagger"
      - "/swagger/*"
  
  # 密码策略 - 开发环境宽松
  password:
    min_length: 4                     # 最小长度 (开发环境较短)
    require_uppercase: false          # 不需要大写字母
    require_lowercase: false          # 不需要小写字母
    require_numbers: false            # 不需要数字
    require_symbols: false            # 不需要特殊字符
    max_age_days: 365                 # 密码有效期 (开发环境较长)
    history_count: 1                  # 密码历史记录 (开发环境较少)
  
  # 登录安全 - 开发环境宽松
  login:
    max_attempts: 100                 # 最大登录尝试次数 (开发环境很高)
    lockout_duration: 1m              # 账户锁定时间 (开发环境很短)
    session_timeout: 168h             # 会话超时时间 (开发环境较长)
    require_captcha_after: 1000       # 验证码触发阈值 (开发环境很高)
  
  # 会话管理 - 开发环境简化
  session:
    enabled: true                     # 启用会话管理
    store_type: "memory"              # 内存存储 (开发环境)
    max_concurrent: 100               # 单用户最大并发会话数 (开发环境很高)
    cleanup_interval: 10m             # 会话清理间隔 (开发环境较频繁)
  
  # 权限控制 - 开发环境简化
  rbac:
    enabled: false                    # 禁用RBAC (开发环境)
    cache_enabled: true               # 启用权限缓存
    cache_ttl: 5m                     # 权限缓存过期时间 (开发环境较短)
    default_role: "admin"             # 默认管理员角色
  
  # 审计日志 - 开发环境简化
  audit:
    enabled: true                     # 启用审计 (用于调试)
    log_login: true                   # 记录登录事件
    log_logout: true                  # 记录登出事件
    log_permission_check: false       # 不记录权限检查
    retention_days: 7                 # 审计日志保留天数 (开发环境较短)

# 用户管理配置 - 开发环境
user:
  # 注册配置 - 开发环境简化
  registration:
    enabled: true                     # 允许用户注册
    require_email_verification: false # 不需要邮箱验证 (开发环境)
    require_admin_approval: false     # 不需要管理员审批
    default_role: "admin"             # 新用户默认管理员角色 (开发环境)
    auto_activate: true               # 自动激活账户
  
  # 用户资料 - 开发环境宽松
  profile:
    allow_username_change: true       # 允许修改用户名
    allow_email_change: true          # 允许修改邮箱
    require_current_password: false   # 不需要当前密码 (开发环境)
  
  # 账户管理 - 开发环境
  account:
    soft_delete: false                # 硬删除用户 (开发环境)
    retention_days: 1                 # 保留天数 (开发环境很短)
    allow_reactivation: true          # 允许重新激活

# 租户管理配置 - 开发环境
tenant:
  # 多租户支持 - 开发环境简化
  enabled: false                      # 禁用多租户 (开发环境)
  isolation_level: "loose"            # 宽松隔离
  default_tenant: "dev-tenant-001"    # 开发默认租户
  
  # 租户配置 - 开发环境宽松
  settings:
    max_users_per_tenant: 10000       # 每个租户最大用户数 (开发环境很高)
    max_roles_per_tenant: 1000        # 每个租户最大角色数 (开发环境很高)
    allow_custom_roles: true          # 允许自定义角色

# 通知配置 - 开发环境禁用
notification:
  # 邮件通知 - 开发环境禁用
  email:
    enabled: false                    # 禁用邮件通知
    smtp_host: ""
    smtp_port: 587
    smtp_username: ""
    smtp_password: ""
    from_address: "dev@localhost"     # 开发环境邮箱
  
  # 短信通知 - 开发环境禁用
  sms:
    enabled: false                    # 禁用短信通知

# 缓存配置 - 开发环境
cache:
  # 用户信息缓存
  user:
    enabled: true                     # 启用用户缓存
    ttl: 5m                          # 缓存过期时间 (开发环境较短)
    max_size: 1000                   # 最大缓存用户数 (开发环境较少)
  
  # 权限缓存
  permission:
    enabled: true                     # 启用权限缓存
    ttl: 1m                          # 缓存过期时间 (开发环境很短)
    max_size: 5000                   # 最大缓存权限数 (开发环境较少)
  
  # 会话缓存
  session:
    enabled: true                     # 启用会话缓存
    ttl: 1h                          # 缓存过期时间 (开发环境较短)

# 监控配置 - 开发环境
monitoring:
  # 指标收集
  metrics:
    enabled: true                     # 启用指标收集
    endpoint: "/metrics"              # 指标端点
    interval: "30s"                   # 收集间隔
    detailed: true                    # 详细指标 (开发环境)
    
    # 自定义指标
    custom_metrics:
      login_attempts: true            # 登录尝试次数
      active_sessions: true           # 活跃会话数
      permission_checks: true         # 权限检查次数
  
  # 健康检查
  health:
    check_interval: "10s"             # 健康检查间隔 (开发环境更频繁)
    timeout: "5s"                     # 健康检查超时
    detailed: true                    # 详细健康信息
    
    # 健康检查项
    checks:
      database: true                  # 数据库连接检查
      redis: false                    # Redis连接检查 (开发环境可选)
      jwt_service: true               # JWT服务检查

# 🔧 开发环境特殊配置
development:
  # 调试功能
  debug: true                         # 启用调试模式
  pprof:
    enabled: true                     # 启用性能分析
    port: 6065                        # pprof端口
  
  # 热重载
  hot_reload:
    enabled: true                     # 启用热重载
    watch_paths: ["./internal/auth", "./configs"] # 监控路径
  
  # API文档
  swagger:
    enabled: true                     # 启用Swagger文档
    path: "/swagger"                  # 文档路径
    detailed: true                    # 详细文档
  
  # 测试功能
  test:
    enabled: true                     # 启用测试功能
    mock_users: true                  # 模拟用户数据
    test_endpoints: true              # 测试端点
    auto_create_admin: true           # 自动创建管理员用户
  
  # 开发工具
  dev_tools:
    enabled: true                     # 启用开发工具
    user_manager: true                # 用户管理工具
    role_editor: true                 # 角色编辑器
    permission_viewer: true           # 权限查看器

# 🚨 开发模式警告
warnings:
  show_dev_mode_warning: true
  dev_mode_banner: |
    ⚠️  用户认证服务开发模式已启用
    🔓 用户认证已禁用，所有认证请求将使用默认开发用户
    🧑‍💻 任意用户名/密码都可以登录成功
    👑 开发用户拥有管理员权限和所有权限
    🚨 此配置仅适用于开发环境！
