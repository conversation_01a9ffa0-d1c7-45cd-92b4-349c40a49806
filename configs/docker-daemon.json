{"description": "Docker daemon配置文件 - 用于PaaS平台的存储管理和性能优化", "storage-driver": "overlay2", "storage-opts": ["overlay2.override_kernel_check=true"], "data-root": "/var/lib/docker", "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "5", "compress": "true"}, "max-concurrent-downloads": 3, "max-concurrent-uploads": 5, "max-download-attempts": 5, "default-ulimits": {"nofile": {"Name": "nofile", "Hard": 64000, "Soft": 64000}}, "live-restore": true, "userland-proxy": false, "no-new-privileges": true, "default-runtime": "runc", "runtimes": {"runc": {"path": "runc"}}, "exec-opts": ["native.cgroupdriver=systemd"], "bridge": "docker0", "fixed-cidr": "**********/16", "default-address-pools": [{"base": "**********/12", "size": 24}], "dns": ["*******", "*******"], "dns-search": ["paas.local"], "insecure-registries": ["localhost:5000", "registry.paas.local:5000"], "registry-mirrors": ["https://docker.mirrors.ustc.edu.cn", "https://hub-mirror.c.163.com"], "experimental": false, "features": {"buildkit": true}, "builder": {"gc": {"enabled": true, "defaultKeepStorage": "20GB", "policy": [{"keepStorage": "10GB", "filter": ["unused-for=2160h"]}, {"keepStorage": "50GB", "filter": ["unused-for=168h"]}, {"keepStorage": "100GB", "all": true}]}}, "containerd": "/run/containerd/containerd.sock", "containerd-namespace": "moby", "containerd-plugin-namespace": "moby-plugins", "metrics-addr": "127.0.0.1:9323", "shutdown-timeout": 15, "debug": false, "hosts": ["unix:///var/run/docker.sock"], "icc": true, "ip": "0.0.0.0", "iptables": true, "ipv6": false, "ip-forward": true, "ip-masq": true, "selinux-enabled": false, "userns-remap": "", "cgroup-parent": "", "default-shm-size": "64M", "init": false, "init-path": "/usr/libexec/docker-init", "seccomp-profile": "", "apparmor-profile": "", "raw-logs": false, "swarm-default-advertise-addr": "", "api-cors-header": "", "cluster-store": "", "cluster-store-opts": {}, "cluster-advertise": "", "node-generic-resources": [], "default-network-opts": {}, "authorization-plugins": [], "allow-nondistributable-artifacts": [], "disable-legacy-registry": false, "default-gateway": "", "default-gateway-v6": "", "labels": ["environment=production", "platform=paas", "region=local", "version=1.0.0"], "pidfile": "/var/run/docker.pid", "graph": "", "mtu": 0, "log-level": "info", "tls": false, "tlsverify": false, "tlscert": "", "tlskey": "", "tlscacert": "", "group": "docker", "fixed-cidr-v6": "", "dns-opts": [], "exec-root": "/var/run/docker", "oom-score-adjust": -500}