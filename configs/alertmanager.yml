# PaaS 平台 AlertManager 配置文件
# 定义了告警路由、接收器和通知方式

# 全局配置
global:
  # SMTP 服务器配置
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your_email_password'
  smtp_require_tls: true
  
  # Slack 全局配置
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
  
  # 解决时间
  resolve_timeout: 5m

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 告警路由配置
route:
  # 分组标签
  group_by: ['alertname', 'cluster', 'service']
  
  # 分组等待时间
  group_wait: 10s
  
  # 分组间隔时间
  group_interval: 10s
  
  # 重复发送间隔
  repeat_interval: 1h
  
  # 默认接收器
  receiver: 'default'
  
  # 子路由配置
  routes:
    # 🚨 严重告警路由
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      group_interval: 5s
      repeat_interval: 5m
      continue: true  # 继续匹配其他路由
    
    # ⚠️ 警告告警路由
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      group_interval: 30s
      repeat_interval: 30m
      continue: true
    
    # 🔐 安全相关告警
    - match:
        category: security
      receiver: 'security-alerts'
      group_wait: 5s
      repeat_interval: 10m
      continue: true
    
    # 📊 数据库告警
    - match:
        category: database
      receiver: 'database-alerts'
      group_wait: 15s
      repeat_interval: 20m
      continue: true
    
    # 🌐 API 网关告警
    - match:
        service: api-gateway
      receiver: 'gateway-alerts'
      group_wait: 10s
      repeat_interval: 15m
      continue: true
    
    # 🔧 开发环境告警 (降低优先级)
    - match:
        environment: development
      receiver: 'dev-alerts'
      group_wait: 1m
      repeat_interval: 2h
    
    # 📱 移动端告警 (如果有)
    - match_re:
        alertname: '.*Mobile.*'
      receiver: 'mobile-alerts'
      repeat_interval: 1h

# 抑制规则
inhibit_rules:
  # 服务下线时抑制其他相关告警
  - source_match:
      alertname: 'ServiceDown'
    target_match:
      service: '{{ .service }}'
    equal: ['service', 'instance']
  
  # 严重告警抑制警告告警
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service']
  
  # 磁盘空间严重不足抑制一般不足告警
  - source_match:
      alertname: 'DiskSpaceCritical'
    target_match:
      alertname: 'DiskSpaceLow'
    equal: ['instance', 'mountpoint']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default'
    webhook_configs:
      - url: 'http://webhook-receiver:8080/alerts'
        send_resolved: true
        http_config:
          basic_auth:
            username: 'webhook_user'
            password: 'webhook_password'
        title: 'PaaS Platform Alert'
        text: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          服务: {{ .Labels.service }}
          严重程度: {{ .Labels.severity }}
          状态: {{ .Status }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          描述: {{ .Annotations.description }}
          {{ end }}

  # 🚨 严重告警接收器
  - name: 'critical-alerts'
    # 邮件通知
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[CRITICAL] PaaS Platform - {{ .GroupLabels.alertname }}'
        html: |
          <h2>🚨 严重告警</h2>
          <table border="1" style="border-collapse: collapse;">
            <tr><th>告警名称</th><th>服务</th><th>严重程度</th><th>状态</th><th>开始时间</th></tr>
            {{ range .Alerts }}
            <tr>
              <td>{{ .Labels.alertname }}</td>
              <td>{{ .Labels.service }}</td>
              <td>{{ .Labels.severity }}</td>
              <td>{{ .Status }}</td>
              <td>{{ .StartsAt.Format "2006-01-02 15:04:05" }}</td>
            </tr>
            {{ end }}
          </table>
          <h3>详细信息:</h3>
          {{ range .Alerts }}
          <p><strong>{{ .Annotations.summary }}</strong></p>
          <p>{{ .Annotations.description }}</p>
          <p><a href="{{ .Annotations.runbook_url }}">处理手册</a> | 
             <a href="{{ .Annotations.dashboard_url }}">监控面板</a></p>
          <hr>
          {{ end }}
    
    # Slack 通知
    slack_configs:
      - channel: '#paas-critical-alerts'
        username: 'AlertManager'
        icon_emoji: ':rotating_light:'
        title: '🚨 严重告警: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *服务*: {{ .Labels.service }}
          *严重程度*: {{ .Labels.severity }}
          *状态*: {{ .Status }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          *描述*: {{ .Annotations.description }}
          {{ if .Annotations.runbook_url }}*处理手册*: <{{ .Annotations.runbook_url }}|查看>{{ end }}
          {{ if .Annotations.dashboard_url }}*监控面板*: <{{ .Annotations.dashboard_url }}|查看>{{ end }}
          ---
          {{ end }}
        send_resolved: true
    
    # 企业微信通知 (Webhook)
    webhook_configs:
      - url: 'http://webhook-receiver:8080/wechat'
        send_resolved: true
        title: '🚨 PaaS平台严重告警'
        text: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # ⚠️ 警告告警接收器
  - name: 'warning-alerts'
    slack_configs:
      - channel: '#paas-warnings'
        username: 'AlertManager'
        icon_emoji: ':warning:'
        title: '⚠️ 警告: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *服务*: {{ .Labels.service }}
          *描述*: {{ .Annotations.description }}
          {{ end }}
        send_resolved: true
    
    # 邮件通知 (仅工作时间)
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] PaaS Platform - {{ .GroupLabels.alertname }}'
        body: |
          警告告警详情:
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          服务: {{ .Labels.service }}
          描述: {{ .Annotations.description }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 🔐 安全告警接收器
  - name: 'security-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[SECURITY] PaaS Platform - {{ .GroupLabels.alertname }}'
        body: |
          🔐 安全告警:
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          服务: {{ .Labels.service }}
          描述: {{ .Annotations.description }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
    
    slack_configs:
      - channel: '#paas-security'
        username: 'SecurityBot'
        icon_emoji: ':shield:'
        title: '🔐 安全告警: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *服务*: {{ .Labels.service }}
          *描述*: {{ .Annotations.description }}
          {{ end }}

  # 📊 数据库告警接收器
  - name: 'database-alerts'
    slack_configs:
      - channel: '#paas-database'
        username: 'DatabaseBot'
        icon_emoji: ':floppy_disk:'
        title: '📊 数据库告警: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          {{ end }}

  # 🌐 API 网关告警接收器
  - name: 'gateway-alerts'
    slack_configs:
      - channel: '#paas-gateway'
        username: 'GatewayBot'
        icon_emoji: ':globe_with_meridians:'
        title: '🌐 网关告警: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          {{ end }}

  # 🔧 开发环境告警接收器
  - name: 'dev-alerts'
    slack_configs:
      - channel: '#paas-dev'
        username: 'DevBot'
        icon_emoji: ':hammer_and_wrench:'
        title: '🔧 开发环境告警: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *环境*: {{ .Labels.environment }}
          {{ end }}

  # 📱 移动端告警接收器
  - name: 'mobile-alerts'
    slack_configs:
      - channel: '#paas-mobile'
        username: 'MobileBot'
        icon_emoji: ':iphone:'
        title: '📱 移动端告警: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          {{ end }}

---
# 📝 AlertManager 配置说明
# 
# 本配置文件定义了 PaaS 平台的告警管理：
# 
# 1. 🛣️ 告警路由：
#    - 按严重程度路由 (critical, warning)
#    - 按类别路由 (security, database, gateway)
#    - 按环境路由 (development, production)
# 
# 2. 📢 通知方式：
#    - 邮件通知：运维团队、管理层
#    - Slack 通知：不同频道分类接收
#    - Webhook 通知：企业微信、自定义系统
# 
# 3. 🔇 抑制规则：
#    - 服务下线抑制相关告警
#    - 严重告警抑制警告告警
#    - 避免告警风暴
# 
# 4. ⏰ 时间控制：
#    - 分组等待和间隔时间
#    - 重复发送间隔
#    - 解决超时时间
# 
# 使用此配置可以实现智能的告警分发和通知。
