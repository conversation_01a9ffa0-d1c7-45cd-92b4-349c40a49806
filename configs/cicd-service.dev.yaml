# CI/CD服务开发环境配置文件
# 此配置专门用于开发环境，禁用了用户认证以简化开发流程
# ⚠️ 警告：此配置仅适用于开发环境，绝不能在生产环境使用！

# 服务器配置
server:
  port: 8082                          # 服务端口
  mode: debug                         # 运行模式: debug
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置 - 开发环境
database:
  driver: sqlite                      # 数据库驱动
  dsn: "./data/cicd.db"              # 数据库文件路径
  max_open_conns: 50                  # 最大打开连接数
  max_idle_conns: 10                  # 最大空闲连接数
  conn_max_lifetime: 3600             # 连接最大生存时间(秒)
  log_level: info                     # 数据库日志级别

# Redis配置 - 开发环境 (可选)
redis:
  addr: "localhost:6379"              # Redis地址
  password: ""                        # Redis密码
  db: 1                              # Redis数据库编号
  pool_size: 5                       # 连接池大小 - 开发环境较小
  min_idle_conns: 2                  # 最小空闲连接数
  dial_timeout: 5s                   # 连接超时时间
  read_timeout: 3s                   # 读取超时时间
  write_timeout: 3s                  # 写入超时时间

# 日志配置 - 开发环境
log:
  level: debug                        # 日志级别: debug
  format: text                        # 日志格式: text
  output: stdout                      # 日志输出
  service_name: "cicd-service-dev"    # 服务名称

# 🔓 安全配置 - 开发环境认证设置
security:
  # JWT配置 - 开发环境跳过认证
  jwt:
    enabled: false                    # ❌ 禁用JWT校验
    dev_mode: true                    # ✅ 启用开发模式
    dev_token: "cicd-dev-token-2024"  # 🔑 开发令牌
    secret: "cicd-dev-jwt-secret"     # 开发JWT密钥
    expire_hours: 24                  # Token过期时间
    
    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "cicd-dev-user-001"         # 开发用户ID
      tenant_id: "dev-tenant-001"     # 开发租户ID
      username: "CI/CD开发者"          # 开发用户名
      email: "cicd-dev@localhost"     # 开发用户邮箱
      roles: ["admin", "cicd_manager"] # CI/CD管理权限
      permissions: ["cicd:*"]         # CI/CD相关所有权限
    
    # 跳过认证的路径
    skip_paths:
      - "/health"
      - "/ready"
      - "/metrics"
      - "/webhook"                    # Webhook接口
      - "/swagger"
      - "/swagger/*"
  
  # API限流配置 - 开发环境宽松限制
  rate_limit:
    enabled: false                    # 禁用限流 (开发环境)
    requests_per_minute: 1000         # 每分钟请求数限制
    burst: 100                        # 突发请求数限制

# 构建配置 - 开发环境优化
build:
  # 构建节点配置
  nodes:
    default_capacity: 2               # 默认节点容量 - 开发环境较小
    heartbeat_interval: 30s           # 心跳检测间隔
    offline_timeout: 180s             # 节点离线超时时间 - 开发环境较短
  
  # 构建队列配置
  queue:
    max_concurrent_builds: 3          # 最大并发构建数 - 开发环境较少
    default_priority: 0               # 默认优先级
    retry_attempts: 2                 # 重试次数 - 开发环境较少
    retry_delay: 30s                  # 重试延迟 - 开发环境较短
  
  # 构建超时配置 - 开发环境较短超时
  timeout:
    default_build: 1800s              # 默认构建超时时间 (30分钟)
    default_stage: 900s               # 默认阶段超时时间 (15分钟)
    default_step: 300s                # 默认步骤超时时间 (5分钟)
  
  # 构建工作空间配置
  workspace:
    base_path: "/tmp/builds"          # 构建工作空间路径
    cleanup_after: 2h                 # 构建完成后清理时间 - 开发环境较短
    max_size: 5GB                     # 工作空间最大大小 - 开发环境较小

# Docker配置 - 开发环境
docker:
  # Docker守护进程配置
  host: "unix:///var/run/docker.sock" # 本地Docker守护进程
  api_version: "1.41"                 # Docker API版本
  
  # 镜像仓库配置 - 开发环境
  registry:
    default_url: "localhost:5000"     # 本地镜像仓库
    username: ""                      # 用户名
    password: ""                      # 密码
    insecure: true                    # 允许不安全连接
  
  # 构建配置 - 开发环境优化
  build:
    context_size_limit: 500MB         # 构建上下文大小限制 - 开发环境较小
    build_timeout: 900s               # 镜像构建超时时间 - 开发环境较短
    no_cache: false                   # 启用缓存
    pull_always: false                # 不总是拉取基础镜像

# 部署配置 - 开发环境简化
deploy:
  # 部署策略配置
  strategies:
    rolling:
      max_unavailable: 1              # 滚动更新最大不可用实例数
      max_surge: 1                    # 滚动更新最大超出实例数
      timeout: 180s                   # 部署超时时间 - 开发环境较短
    
    blue_green:
      auto_promote: true              # 自动切换 - 开发环境
      promotion_timeout: 300s         # 切换超时时间
      rollback_timeout: 180s          # 回滚超时时间
    
    canary:
      auto_promote: true              # 自动推广
      analysis_interval: 30s          # 分析间隔 - 开发环境较短
      success_rate_threshold: 95.0    # 成功率阈值 - 开发环境较宽松
      response_time_threshold: 1000   # 响应时间阈值 (ms)

# Webhook配置 - 开发环境
webhook:
  secret: "dev-webhook-secret"        # Webhook密钥
  timeout: 30s                        # Webhook处理超时时间
  max_payload_size: 5MB               # 最大载荷大小 - 开发环境较小
  
  # 支持的Git服务 - 开发环境配置
  sources:
    gitea:
      enabled: true                   # 启用Gitea集成
      api_url: "http://localhost:3000/api/v1" # 本地Gitea API
      token: ""                       # Gitea API Token
    
    github:
      enabled: false                  # 禁用GitHub集成 (开发环境)
      api_url: "https://api.github.com"
      token: ""
    
    gitlab:
      enabled: false                  # 禁用GitLab集成 (开发环境)
      api_url: "https://gitlab.com/api/v4"
      token: ""

# 通知配置 - 开发环境简化
notification:
  # 邮件通知 - 开发环境禁用
  email:
    enabled: false                    # 禁用邮件通知
  
  # Slack通知 - 开发环境禁用
  slack:
    enabled: false                    # 禁用Slack通知
  
  # 企业微信通知 - 开发环境禁用
  wechat:
    enabled: false                    # 禁用企业微信通知

# 缓存配置 - 开发环境
cache:
  # 构建缓存
  build:
    enabled: true                     # 启用构建缓存
    ttl: 24h                         # 缓存生存时间 - 开发环境较短
    max_size: 10GB                   # 最大缓存大小 - 开发环境较小
  
  # 依赖缓存
  dependencies:
    enabled: true                     # 启用依赖缓存
    ttl: 168h                        # 缓存生存时间 (7天)
    max_size: 5GB                    # 最大缓存大小 - 开发环境较小

# 监控配置 - 开发环境
monitoring:
  # 指标收集
  metrics:
    enabled: true                     # 启用指标收集
    endpoint: "/metrics"              # 指标端点
    interval: 30s                     # 收集间隔
  
  # 健康检查
  health:
    check_interval: 15s               # 健康检查间隔
    timeout: 5s                       # 健康检查超时时间

# 功能开关 - 开发环境
features:
  parallel_builds: true              # 支持并行构建
  build_cache: true                  # 启用构建缓存
  auto_deployment: true              # 支持自动部署
  rollback: true                     # 支持回滚
  approval_workflow: false           # 禁用审批工作流 (开发环境)
  multi_arch_build: false            # 禁用多架构构建 (开发环境)

# 🔧 开发环境特殊配置
development:
  debug: true                         # 调试模式
  pprof:
    enabled: true                     # 性能分析
    port: 6062                        # pprof端口
  
  # CI/CD调试
  cicd_debug:
    enabled: true                     # 启用CI/CD调试
    keep_build_logs: true             # 保留构建日志
    verbose_output: true              # 详细输出
  
  # API文档
  swagger:
    enabled: true                     # 启用API文档
    path: "/swagger"                  # 文档路径

# 🚨 开发模式警告
warnings:
  show_dev_mode_warning: true
  dev_mode_banner: |
    ⚠️  CI/CD服务开发模式已启用
    🔓 用户认证已禁用，所有CI/CD操作将使用默认开发用户
    🐳 构建将在本地Docker环境中执行
    📦 镜像将推送到本地仓库 (localhost:5000)
    🚨 此配置仅适用于开发环境！
