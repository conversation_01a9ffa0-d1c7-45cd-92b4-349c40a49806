# 镜像仓库生命周期管理配置
# 用于管理私有镜像仓库的镜像生命周期和自动清理策略

# 仓库配置
registry:
  # 仓库地址
  url: "localhost:5000"
  
  # 认证信息（可选）
  username: ""
  password: ""
  
  # 仓库类型
  type: "docker-registry"  # docker-registry, harbor, nexus, artifactory
  
  # API版本
  api_version: "v2"
  
  # 连接配置
  connection:
    timeout: "30s"
    retry_count: 3
    retry_delay: "5s"
    
  # TLS配置
  tls:
    enabled: false
    verify_ssl: true
    ca_cert_path: ""
    client_cert_path: ""
    client_key_path: ""

# 生命周期管理策略
lifecycle:
  # 全局保留策略
  global:
    # 保留最新的版本数量
    keep_versions: 5
    
    # 保留天数
    keep_days: 30
    
    # 保留最小版本数（即使超过保留天数）
    min_keep_versions: 2
    
    # 保留最大版本数（即使在保留天数内）
    max_keep_versions: 20
  
  # 按仓库名称模式的特殊策略
  repository_policies:
    # 生产环境镜像
    - pattern: "paas-*/prod"
      keep_versions: 10
      keep_days: 90
      min_keep_versions: 5
      description: "生产环境镜像保留更长时间"
    
    # 测试环境镜像
    - pattern: "paas-*/test"
      keep_versions: 3
      keep_days: 14
      min_keep_versions: 1
      description: "测试环境镜像保留较短时间"
    
    # 开发环境镜像
    - pattern: "paas-*/dev"
      keep_versions: 2
      keep_days: 7
      min_keep_versions: 1
      description: "开发环境镜像保留最短时间"
    
    # 基础镜像
    - pattern: "base/*"
      keep_versions: 15
      keep_days: 180
      min_keep_versions: 5
      description: "基础镜像保留更多版本"
    
    # 临时构建镜像
    - pattern: "build/*"
      keep_versions: 1
      keep_days: 3
      min_keep_versions: 0
      description: "临时构建镜像快速清理"
  
  # 按标签模式的策略
  tag_policies:
    # 保护标签（永不删除）
    protected_tags:
      - "latest"
      - "stable"
      - "release-*"
      - "v*.*.*"
      - "prod-*"
    
    # 临时标签（快速清理）
    temporary_tags:
      - "temp-*"
      - "test-*"
      - "dev-*"
      - "feature-*"
      - "hotfix-*"
    
    # 按标签模式的保留策略
    tag_retention:
      - pattern: "v*.*.*"
        keep_versions: 20
        keep_days: 365
        description: "语义化版本标签"
      
      - pattern: "release-*"
        keep_versions: 15
        keep_days: 180
        description: "发布版本标签"
      
      - pattern: "feature-*"
        keep_versions: 2
        keep_days: 14
        description: "功能分支标签"
      
      - pattern: "hotfix-*"
        keep_versions: 5
        keep_days: 30
        description: "热修复标签"

# 清理配置
cleanup:
  # 清理模式
  mode: "safe"  # safe, aggressive, custom
  
  # 安全模式配置
  safe_mode:
    # 需要确认才能删除
    require_confirmation: true
    
    # 删除前备份镜像信息
    backup_before_delete: true
    
    # 检查镜像使用情况
    check_image_usage: true
    
    # 最大单次删除数量
    max_delete_count: 50
  
  # 激进模式配置
  aggressive_mode:
    # 自动删除无需确认
    auto_delete: true
    
    # 忽略镜像使用检查
    ignore_usage_check: false
    
    # 最大单次删除数量
    max_delete_count: 200
  
  # 清理排除规则
  exclusions:
    # 排除的仓库
    repositories:
      - "system/*"
      - "backup/*"
      - "archive/*"
    
    # 排除的标签
    tags:
      - "latest"
      - "stable"
      - "backup-*"
    
    # 排除最近创建的镜像（小时）
    recent_hours: 24
    
    # 排除正在使用的镜像
    exclude_in_use: true
  
  # 批处理配置
  batch:
    # 批处理大小
    size: 10
    
    # 批处理间隔（秒）
    interval: 5
    
    # 并发处理数
    concurrency: 3

# 垃圾回收配置
garbage_collection:
  # 是否启用自动垃圾回收
  enabled: true
  
  # 垃圾回收模式
  mode: "auto"  # auto, manual, scheduled
  
  # 自动垃圾回收触发条件
  auto_triggers:
    # 清理后自动执行
    after_cleanup: true
    
    # 存储使用率超过阈值时执行
    storage_threshold: 80
    
    # 定期执行（小时）
    periodic_hours: 24
  
  # 垃圾回收选项
  options:
    # 删除未引用的层
    delete_untagged: true
    
    # 删除未引用的清单
    delete_untagged_manifests: true
    
    # 并行处理
    parallel: true
    
    # 详细输出
    verbose: false

# 监控配置
monitoring:
  # 启用监控
  enabled: true
  
  # 监控指标
  metrics:
    # 仓库大小
    repository_size: true
    
    # 镜像数量
    image_count: true
    
    # 标签数量
    tag_count: true
    
    # 清理统计
    cleanup_stats: true
  
  # 告警配置
  alerts:
    # 仓库大小告警阈值（GB）
    repository_size_threshold: 50
    
    # 镜像数量告警阈值
    image_count_threshold: 1000
    
    # 清理失败告警
    cleanup_failure_alert: true
  
  # 通知配置
  notifications:
    # Webhook通知
    webhook:
      enabled: true
      url: "http://notification-service:8085/webhook/registry-cleanup"
      timeout: "30s"
    
    # 邮件通知
    email:
      enabled: false
      smtp_server: "smtp.example.com:587"
      from: "<EMAIL>"
      to: ["<EMAIL>"]
    
    # Slack通知
    slack:
      enabled: false
      webhook_url: ""
      channel: "#registry-alerts"

# 调度配置
scheduling:
  # 启用定时清理
  enabled: true
  
  # 清理计划
  schedules:
    # 每日快速清理
    daily_cleanup:
      cron: "0 2 * * *"  # 每天凌晨2点
      mode: "safe"
      targets: ["temp-*", "test-*", "dev-*"]
      description: "每日清理临时镜像"
    
    # 每周深度清理
    weekly_cleanup:
      cron: "0 3 * * 0"  # 每周日凌晨3点
      mode: "aggressive"
      targets: ["all"]
      description: "每周深度清理所有仓库"
    
    # 每月归档清理
    monthly_cleanup:
      cron: "0 4 1 * *"  # 每月1号凌晨4点
      mode: "custom"
      keep_days: 90
      keep_versions: 20
      description: "每月归档清理"
  
  # 调度选项
  options:
    # 最大执行时间（分钟）
    max_execution_time: 120
    
    # 并发执行限制
    max_concurrent_jobs: 1
    
    # 失败重试次数
    retry_count: 3
    
    # 重试间隔（分钟）
    retry_interval: 30

# 报告配置
reporting:
  # 启用报告
  enabled: true
  
  # 报告格式
  format: "markdown"  # markdown, json, html, csv
  
  # 报告内容
  content:
    # 包含清理统计
    cleanup_stats: true
    
    # 包含仓库详情
    repository_details: true
    
    # 包含错误信息
    error_details: true
    
    # 包含建议
    recommendations: true
  
  # 报告存储
  storage:
    # 本地文件
    local:
      enabled: true
      path: "/app/reports"
      retention_days: 30
    
    # 远程存储
    remote:
      enabled: false
      type: "s3"  # s3, gcs, azure
      bucket: "registry-reports"
      prefix: "cleanup-reports/"
  
  # 报告分发
  distribution:
    # 邮件发送
    email:
      enabled: false
      recipients: ["<EMAIL>"]
      subject: "镜像仓库清理报告"
    
    # Webhook发送
    webhook:
      enabled: true
      url: "http://notification-service:8085/webhook/registry-report"

# 日志配置
logging:
  # 日志级别
  level: "info"  # debug, info, warn, error
  
  # 日志格式
  format: "json"  # text, json
  
  # 日志输出
  output:
    # 控制台输出
    console:
      enabled: true
      level: "info"
    
    # 文件输出
    file:
      enabled: true
      path: "/app/logs/registry-lifecycle.log"
      level: "debug"
      max_size: "100MB"
      max_files: 10
      compress: true
    
    # 远程日志
    remote:
      enabled: false
      type: "elasticsearch"  # elasticsearch, fluentd, logstash
      endpoint: "http://elasticsearch:9200"
      index: "registry-lifecycle"

# 性能配置
performance:
  # 并发限制
  concurrency:
    # 最大并发API请求
    max_api_requests: 10
    
    # 最大并发删除操作
    max_delete_operations: 5
    
    # 最大并发仓库处理
    max_repository_processing: 3
  
  # 超时配置
  timeouts:
    # API请求超时
    api_request: "30s"
    
    # 删除操作超时
    delete_operation: "60s"
    
    # 总体操作超时
    total_operation: "3600s"
  
  # 缓存配置
  cache:
    # 启用缓存
    enabled: true
    
    # 缓存TTL
    ttl: "300s"
    
    # 缓存大小
    max_size: "100MB"
  
  # 限流配置
  rate_limiting:
    # 启用限流
    enabled: true
    
    # 每秒请求数
    requests_per_second: 10
    
    # 突发请求数
    burst_size: 20

# 版本信息
version: "1.0.0"
last_updated: "2024-08-16"
schema_version: "1.0"
