# PaaS 平台应用性能监控 (APM) 配置文件
# 定义了完整的 APM 监控系统配置

# 🔍 APM 基础配置
apm:
  # 服务标识
  service_name: "paas-platform"
  service_version: "1.0.0"
  environment: "production"  # development, staging, production
  
  # 采样配置
  sampling:
    # 采样率 (0.0 - 1.0)
    rate: 0.1
    # 最大采样数 (每秒)
    max_per_second: 1000
    # 强制采样的端点
    force_sample_endpoints:
      - "/api/v1/auth/login"
      - "/api/v1/auth/logout"
      - "/health"
  
  # 指标收集配置
  metrics:
    enabled: true
    # 指标收集间隔
    collection_interval: "15s"
    # 指标保留时间
    retention_period: "30d"
    # 指标端点
    endpoint: "/metrics"
    
    # 自定义指标
    custom_metrics:
      # 业务指标
      business:
        - name: "user_registrations_total"
          type: "counter"
          description: "用户注册总数"
          labels: ["source", "method"]
        
        - name: "application_deployments_total"
          type: "counter"
          description: "应用部署总数"
          labels: ["status", "runtime"]
        
        - name: "pipeline_executions_total"
          type: "counter"
          description: "流水线执行总数"
          labels: ["status", "type"]
      
      # 系统指标
      system:
        - name: "database_query_duration_seconds"
          type: "histogram"
          description: "数据库查询持续时间"
          labels: ["query_type", "table"]
          buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
        
        - name: "cache_operations_total"
          type: "counter"
          description: "缓存操作总数"
          labels: ["operation", "result"]
        
        - name: "queue_size"
          type: "gauge"
          description: "队列大小"
          labels: ["queue_name"]

# 📊 Prometheus 配置
prometheus:
  # 服务器配置
  server:
    enabled: true
    host: "0.0.0.0"
    port: 9090
    
    # 数据存储
    storage:
      path: "/var/lib/prometheus"
      retention_time: "30d"
      retention_size: "10GB"
    
    # 全局配置
    global:
      scrape_interval: "15s"
      evaluation_interval: "15s"
      external_labels:
        cluster: "paas-platform"
        environment: "production"
  
  # 抓取配置
  scrape_configs:
    # PaaS 平台服务
    - job_name: "paas-services"
      scrape_interval: "15s"
      metrics_path: "/metrics"
      static_configs:
        - targets:
          - "user-service:8083"
          - "app-manager:8081"
          - "api-gateway:8080"
          - "cicd-service:8082"
          - "config-service:8084"
          - "script-service:8084"
          - "monitor-service:8085"
      
      # 服务标签
      relabel_configs:
        - source_labels: [__address__]
          target_label: instance
        - source_labels: [__address__]
          regex: '([^:]+):(\d+)'
          target_label: service
          replacement: '${1}'
    
    # Node Exporter (系统指标)
    - job_name: "node-exporter"
      scrape_interval: "15s"
      static_configs:
        - targets:
          - "node-exporter:9100"
    
    # PostgreSQL Exporter
    - job_name: "postgres-exporter"
      scrape_interval: "30s"
      static_configs:
        - targets:
          - "postgres-exporter:9187"
    
    # Redis Exporter
    - job_name: "redis-exporter"
      scrape_interval: "30s"
      static_configs:
        - targets:
          - "redis-exporter:9121"

# 📈 Grafana 配置
grafana:
  # 服务器配置
  server:
    enabled: true
    host: "0.0.0.0"
    port: 3001
    root_url: "http://localhost:3001"
  
  # 数据源配置
  datasources:
    - name: "Prometheus"
      type: "prometheus"
      url: "http://prometheus:9090"
      access: "proxy"
      is_default: true
    
    - name: "Elasticsearch"
      type: "elasticsearch"
      url: "http://elasticsearch:9200"
      access: "proxy"
      database: "paas-logs-*"
      time_field: "@timestamp"
  
  # 仪表板配置
  dashboards:
    # 系统概览仪表板
    - name: "PaaS Platform Overview"
      file: "dashboards/overview.json"
      folder: "PaaS Platform"
    
    # 服务监控仪表板
    - name: "Service Monitoring"
      file: "dashboards/services.json"
      folder: "PaaS Platform"
    
    # 业务指标仪表板
    - name: "Business Metrics"
      file: "dashboards/business.json"
      folder: "PaaS Platform"
    
    # 错误和告警仪表板
    - name: "Errors and Alerts"
      file: "dashboards/errors.json"
      folder: "PaaS Platform"

# 🚨 告警配置
alerting:
  # AlertManager 配置
  alertmanager:
    enabled: true
    host: "0.0.0.0"
    port: 9093
    
    # 告警路由
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      
      # 子路由
      routes:
        - match:
            severity: critical
          receiver: 'critical-alerts'
          group_wait: 5s
          repeat_interval: 5m
        
        - match:
            severity: warning
          receiver: 'warning-alerts'
          repeat_interval: 30m
  
  # 告警接收器
  receivers:
    - name: 'default'
      webhook_configs:
        - url: 'http://webhook-service:8080/alerts'
          send_resolved: true
    
    - name: 'critical-alerts'
      email_configs:
        - to: '<EMAIL>'
          subject: '[CRITICAL] PaaS Platform Alert'
          body: |
            Alert: {{ .GroupLabels.alertname }}
            Service: {{ .GroupLabels.service }}
            Severity: {{ .CommonLabels.severity }}
            Description: {{ .CommonAnnotations.description }}
      
      slack_configs:
        - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
          channel: '#paas-alerts'
          title: 'Critical Alert: {{ .GroupLabels.alertname }}'
          text: '{{ .CommonAnnotations.description }}'
    
    - name: 'warning-alerts'
      slack_configs:
        - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
          channel: '#paas-warnings'
          title: 'Warning: {{ .GroupLabels.alertname }}'
          text: '{{ .CommonAnnotations.description }}'

# 📋 告警规则
alert_rules:
  # 系统可用性告警
  - name: "system-availability"
    rules:
      - alert: "ServiceDown"
        expr: 'up == 0'
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.instance }} 不可用"
          description: "服务已经下线超过1分钟"
      
      - alert: "HighErrorRate"
        expr: 'rate(paas_http_requests_total{status_code=~"5.."}[5m]) > 0.01'
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.service }} 错误率过高"
          description: "5分钟内错误率超过1%"
  
  # 性能告警
  - name: "performance"
    rules:
      - alert: "HighResponseTime"
        expr: 'histogram_quantile(0.95, rate(paas_http_request_duration_seconds_bucket[5m])) > 0.5'
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.service }} 响应时间过长"
          description: "P95响应时间超过500ms"
      
      - alert: "HighMemoryUsage"
        expr: '(paas_system_memory_usage_bytes / 1024 / 1024 / 1024) > 2'
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.service }} 内存使用过高"
          description: "内存使用超过2GB"
  
  # 业务告警
  - name: "business"
    rules:
      - alert: "LoginFailureSpike"
        expr: 'rate(paas_login_attempts_total{result="failure"}[5m]) > 10'
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "登录失败率激增"
          description: "5分钟内登录失败率超过每分钟10次"
      
      - alert: "DatabaseConnectionHigh"
        expr: 'paas_database_connections > 80'
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "数据库连接数超过80个"

# 🔍 链路追踪配置
tracing:
  # Jaeger 配置
  jaeger:
    enabled: true
    
    # 收集器配置
    collector:
      host: "jaeger-collector"
      port: 14268
      endpoint: "/api/traces"
    
    # 查询服务配置
    query:
      host: "jaeger-query"
      port: 16686
    
    # 采样配置
    sampling:
      type: "probabilistic"
      param: 0.1  # 10% 采样率
    
    # 标签配置
    tags:
      service.name: "paas-platform"
      service.version: "1.0.0"
      deployment.environment: "production"
  
  # OpenTelemetry 配置
  opentelemetry:
    enabled: true
    
    # 导出器配置
    exporters:
      jaeger:
        endpoint: "http://jaeger-collector:14268/api/traces"
      
      prometheus:
        endpoint: "http://prometheus:9090/api/v1/write"
    
    # 资源配置
    resource:
      service.name: "paas-platform"
      service.version: "1.0.0"
      deployment.environment: "production"

# 🔧 性能优化配置
performance:
  # 缓存配置
  cache:
    # 指标缓存
    metrics_cache:
      enabled: true
      ttl: "5m"
      max_size: 10000
    
    # 查询结果缓存
    query_cache:
      enabled: true
      ttl: "1m"
      max_size: 1000
  
  # 批处理配置
  batch_processing:
    # 指标批处理
    metrics:
      batch_size: 1000
      flush_interval: "10s"
    
    # 日志批处理
    logs:
      batch_size: 500
      flush_interval: "5s"
  
  # 压缩配置
  compression:
    enabled: true
    algorithm: "gzip"
    level: 6

# 🔒 安全配置
security:
  # 认证配置
  authentication:
    enabled: true
    type: "basic"  # basic, oauth2, jwt
    
    # 基础认证
    basic:
      username: "admin"
      password: "secure_password"
  
  # TLS 配置
  tls:
    enabled: false  # 开发环境关闭，生产环境开启
    cert_file: "/etc/ssl/certs/apm.crt"
    key_file: "/etc/ssl/private/apm.key"
  
  # 访问控制
  access_control:
    # IP 白名单
    allowed_ips:
      - "127.0.0.1"
      - "10.0.0.0/8"
      - "**********/12"
      - "***********/16"

---
# 📝 配置说明
# 
# 本配置文件定义了 PaaS 平台的完整 APM 监控系统：
# 
# 1. 🔍 APM 基础配置：服务标识、采样、指标收集
# 2. 📊 Prometheus：指标存储和查询
# 3. 📈 Grafana：可视化仪表板
# 4. 🚨 AlertManager：告警管理和通知
# 5. 🔍 Jaeger：分布式链路追踪
# 6. 🔧 性能优化：缓存、批处理、压缩
# 7. 🔒 安全配置：认证、TLS、访问控制
# 
# 使用此配置可以实现完整的应用性能监控和可观测性。
