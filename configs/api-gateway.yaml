# API网关服务配置文件
# 统一处理前端API请求，提供认证和路由转发功能

# 服务器配置
server:
  port: 8080                          # API网关端口 (前端默认连接端口)
  mode: release                       # 运行模式: release (生产), debug (开发)
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置 (用于认证服务)
database:
  driver: postgres                    # 数据库驱动: postgres, mysql, sqlite
  dsn: "host=localhost user=paas password=paas123 dbname=paas_auth port=5432 sslmode=disable TimeZone=Asia/Shanghai"
  max_open_conns: 100                 # 最大打开连接数
  max_idle_conns: 20                  # 最大空闲连接数
  conn_max_lifetime: 3600             # 连接最大生存时间(秒)
  log_level: warn                     # 数据库日志级别

# 日志配置
log:
  level: info                         # 日志级别: debug, info, warn, error
  format: json                        # 日志格式: json, text
  output: stdout                      # 日志输出: stdout, stderr, file
  service_name: "api-gateway"         # 服务名称

# 🔒 安全配置
security:
  # JWT认证配置
  jwt:
    enabled: true                     # 是否启用 JWT 校验 (环境变量: PAAS_AUTH_ENABLED)
    secret: "your-jwt-secret-key"     # JWT 密钥 (环境变量: PAAS_JWT_SECRET)
    expires_in: 24h                   # Token 过期时间
    refresh_expires_in: 168h          # 刷新 Token 过期时间
    
    # 🔧 开发模式配置
    dev_mode: false                   # 开发模式 (环境变量: PAAS_DEV_MODE)
    dev_token: "dev-token"            # 开发模式令牌 (环境变量: PAAS_DEV_TOKEN)
    
    # 🧑‍💻 开发模式用户信息
    dev_user:                         
      id: "dev-user-001"              # 环境变量: PAAS_DEV_USER_ID
      tenant_id: "dev-tenant-001"     # 环境变量: PAAS_DEV_USER_TENANT_ID
      username: "开发者"               # 环境变量: PAAS_DEV_USER_USERNAME
      email: "developer@localhost"    # 环境变量: PAAS_DEV_USER_EMAIL
      roles: ["admin", "developer"]   # 环境变量: PAAS_DEV_USER_ROLES (逗号分隔)
    
    # 🛤️ 跳过认证的路径
    skip_paths:                       
      - "/health"                     # 健康检查
      - "/ready"                      # 就绪检查
      - "/metrics"                    # 监控指标
      - "/api/v1/auth/login"          # 登录接口
      - "/api/v1/auth/register"       # 注册接口
      - "/api/v1/auth/refresh"        # 刷新令牌
      - "/api/v1/auth/reset-password" # 重置密码
      - "/swagger"                    # API文档
      - "/swagger/*"                  # API文档资源
  
  # CORS配置
  cors:
    enabled: true                     # 启用CORS
    allow_origins: ["*"]              # 允许的来源
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]              # 允许的头部
    expose_headers: ["*"]             # 暴露的头部
    allow_credentials: true           # 允许凭据
    max_age: 86400                    # 预检请求缓存时间

# 🔀 路由代理配置
proxy:
  # 后端服务配置
  services:
    # 应用管理服务
    app_manager:
      url: "http://localhost:8081"    # 应用管理服务地址
      path_prefix: "/api/v1/apps"     # 路径前缀
      timeout: 30s                    # 请求超时时间
      retry_count: 3                  # 重试次数
      health_check: "/health"         # 健康检查路径
    
    # 脚本执行服务
    script_service:
      url: "http://localhost:8084"    # 脚本执行服务地址
      path_prefix: "/api/v1/scripts"  # 路径前缀
      timeout: 60s                    # 请求超时时间 (脚本执行较长)
      retry_count: 2                  # 重试次数
      health_check: "/health"         # 健康检查路径
    
    # CI/CD服务
    cicd_service:
      url: "http://localhost:8082"    # CI/CD服务地址
      path_prefix: "/api/v1/cicd"     # 路径前缀
      timeout: 120s                   # 请求超时时间 (构建部署较长)
      retry_count: 2                  # 重试次数
      health_check: "/health"         # 健康检查路径
    
    # 配置服务
    config_service:
      url: "http://localhost:8083"    # 配置服务地址
      path_prefix: "/api/v1/configs"  # 路径前缀
      timeout: 30s                    # 请求超时时间
      retry_count: 3                  # 重试次数
      health_check: "/health"         # 健康检查路径
  
  # 负载均衡配置
  load_balancer:
    strategy: "round_robin"           # 负载均衡策略: round_robin, least_conn, ip_hash
    health_check_interval: 30s        # 健康检查间隔
    health_check_timeout: 5s          # 健康检查超时
    max_fails: 3                      # 最大失败次数
    fail_timeout: 30s                 # 失败超时时间

# 🔄 缓存配置
cache:
  enabled: true                       # 启用缓存
  type: "memory"                      # 缓存类型: memory, redis
  ttl: "5m"                          # 缓存过期时间
  max_size: 1000                     # 最大缓存条目数
  
  # Redis缓存配置 (当type为redis时)
  redis:
    addr: "localhost:6379"            # Redis地址
    password: ""                      # Redis密码
    db: 0                            # Redis数据库编号
    pool_size: 10                    # 连接池大小

# 📊 监控配置
monitoring:
  # 指标收集
  metrics:
    enabled: true                     # 启用指标收集
    endpoint: "/metrics"              # 指标端点
    interval: "30s"                   # 收集间隔
  
  # 健康检查
  health:
    check_interval: "30s"             # 健康检查间隔
    timeout: "5s"                     # 健康检查超时
  
  # 链路追踪
  tracing:
    enabled: false                    # 启用链路追踪
    jaeger_endpoint: ""               # Jaeger端点
    sample_rate: 0.1                  # 采样率

# 🚦 限流配置
rate_limit:
  enabled: true                       # 启用限流
  global:
    requests_per_second: 1000         # 全局每秒请求数限制
    burst: 2000                       # 突发请求数限制
  
  # 按路径限流
  paths:
    "/api/v1/auth/login":
      requests_per_minute: 60         # 登录接口限流
      burst: 10
    "/api/v1/auth/register":
      requests_per_minute: 30         # 注册接口限流
      burst: 5

# 🔐 安全策略
security_policies:
  # IP白名单
  ip_whitelist:
    enabled: false                    # 启用IP白名单
    ips: []                          # 允许的IP列表
  
  # IP黑名单
  ip_blacklist:
    enabled: false                    # 启用IP黑名单
    ips: []                          # 禁止的IP列表
  
  # 请求大小限制
  request_limits:
    max_body_size: "10MB"            # 最大请求体大小
    max_header_size: "1MB"           # 最大请求头大小
  
  # 安全头部
  security_headers:
    enabled: true                     # 启用安全头部
    x_frame_options: "DENY"          # X-Frame-Options
    x_content_type_options: "nosniff" # X-Content-Type-Options
    x_xss_protection: "1; mode=block" # X-XSS-Protection
    strict_transport_security: "max-age=31536000; includeSubDomains" # HSTS

# 📝 日志配置
logging:
  # 访问日志
  access_log:
    enabled: true                     # 启用访问日志
    format: "json"                    # 日志格式
    fields: ["timestamp", "method", "path", "status", "latency", "ip", "user_agent"]
  
  # 错误日志
  error_log:
    enabled: true                     # 启用错误日志
    level: "error"                    # 错误日志级别
    stack_trace: true                 # 包含堆栈跟踪

# 🔧 开发环境特殊配置
development:
  # 调试功能
  debug: false                        # 调试模式 (生产环境关闭)
  pprof:
    enabled: false                    # 性能分析 (生产环境关闭)
    port: 6060                        # pprof端口
  
  # API文档
  swagger:
    enabled: false                    # Swagger文档 (生产环境关闭)
    path: "/swagger"                  # 文档路径
  
  # 开发工具
  dev_tools:
    enabled: false                    # 开发工具 (生产环境关闭)
    debug_endpoints: false            # 调试端点

# 🚨 告警配置
alerts:
  # 服务健康告警
  health_check:
    enabled: true                     # 启用健康检查告警
    threshold: 3                      # 连续失败次数阈值
    interval: "1m"                    # 检查间隔
  
  # 错误率告警
  error_rate:
    enabled: true                     # 启用错误率告警
    threshold: 5.0                    # 错误率阈值 (%)
    window: "5m"                      # 统计窗口
  
  # 响应时间告警
  response_time:
    enabled: true                     # 启用响应时间告警
    threshold: "2s"                   # 响应时间阈值
    percentile: 95                    # 百分位数
