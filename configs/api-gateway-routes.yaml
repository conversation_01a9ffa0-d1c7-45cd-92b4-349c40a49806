# PaaS 平台 API Gateway 路由配置
# 用于生产环境的微服务路由规则

# 🔧 API Gateway 基础配置
gateway:
  name: "paas-api-gateway"
  version: "1.0.0"
  port: 8080
  
  # 🔒 全局安全配置
  security:
    cors:
      enabled: true
      allowed_origins: ["*"]
      allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
      allowed_headers: ["*"]
      max_age: 86400
    
    rate_limit:
      enabled: true
      requests_per_minute: 1000
      burst_size: 100

# 🛣️ 路由规则配置
routes:
  # 🔐 认证服务路由 -> User Service (8083)
  - name: "auth-service"
    path: "/api/v1/auth/*"
    target: "http://user-service:8083"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware:
      - "cors"
      - "rate_limit"
      - "request_logging"
    description: "用户认证相关API，包括登录、注册、密码管理等"

  # 🆔 IDP 认证服务路由 -> User Service (8083)
  - name: "idp-auth-service"
    path: "/api/v1/idp/*"
    target: "http://user-service:8083"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware:
      - "cors"
      - "rate_limit"
      - "request_logging"
    description: "IDP 认证相关API，包括 IDP 登录、回调、账号关联等"
    
  # 👥 用户管理路由 -> User Service (8083)
  - name: "user-management"
    path: "/api/v1/users/*"
    target: "http://user-service:8083"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware:
      - "cors"
      - "rate_limit"
      - "auth_required"
      - "request_logging"
    description: "用户管理API，需要认证"
    
  # 📱 应用管理路由 -> App Manager (8081)
  - name: "app-management"
    path: "/api/v1/apps/*"
    target: "http://app-manager:8081"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware:
      - "cors"
      - "rate_limit"
      - "auth_required"
      - "request_logging"
    description: "应用管理API，需要认证"
    
  # 🔄 CI/CD 流水线路由 -> CI/CD Service (8082)
  - name: "cicd-service"
    path: "/api/v1/pipelines/*"
    target: "http://cicd-service:8082"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware:
      - "cors"
      - "rate_limit"
      - "auth_required"
      - "request_logging"
    description: "CI/CD 流水线管理API"
    
  # ⚙️ 配置管理路由 -> Config Service (8084)
  - name: "config-service"
    path: "/api/v1/configs/*"
    target: "http://config-service:8084"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware:
      - "cors"
      - "rate_limit"
      - "auth_required"
      - "request_logging"
    description: "配置管理API"
    
  # 📜 脚本执行路由 -> Script Service (8084)
  - name: "script-service"
    path: "/api/v1/scripts/*"
    target: "http://script-service:8084"
    methods: ["GET", "POST", "PUT", "DELETE"]
    middleware:
      - "cors"
      - "rate_limit"
      - "auth_required"
      - "request_logging"
    description: "脚本执行服务API"
    
  # 📊 监控服务路由 -> Monitor Service (8085)
  - name: "monitor-service"
    path: "/api/v1/monitoring/*"
    target: "http://monitor-service:8085"
    methods: ["GET", "POST"]
    middleware:
      - "cors"
      - "rate_limit"
      - "auth_required"
      - "request_logging"
    description: "监控和指标API"

# 🔧 健康检查路由
health_checks:
  - name: "gateway-health"
    path: "/health"
    response:
      status: "healthy"
      service: "api-gateway"
      version: "1.0.0"
      timestamp: "auto"
      
  - name: "services-health"
    path: "/health/services"
    checks:
      - service: "user-service"
        url: "http://user-service:8083/health"
        timeout: 5s
      - service: "app-manager"
        url: "http://app-manager:8081/health"
        timeout: 5s
      - service: "cicd-service"
        url: "http://cicd-service:8082/health"
        timeout: 5s
      - service: "config-service"
        url: "http://config-service:8084/health"
        timeout: 5s
      - service: "script-service"
        url: "http://script-service:8084/health"
        timeout: 5s

# 🔒 中间件配置
middleware:
  # 混合认证中间件（支持 JWT 和 IDP）
  auth_required:
    type: "hybrid"
    config:
      # 认证服务端点，用于验证Token
      auth_service_url: "http://user-service:8083/api/v1/auth/validate"
      token_header: "Authorization"
      token_prefix: "Bearer "
      cache_ttl: 300  # Token验证结果缓存5分钟

      # IDP 认证配置
      idp_enabled: true
      allow_local_auth: true

      # 跳过认证的路径
      skip_paths:
        - "/health"
        - "/ready"
        - "/metrics"
        - "/swagger"
        - "/swagger/*"
        - "/api/v1/auth/login"
        - "/api/v1/auth/register"
        - "/api/v1/idp/login/*"
        - "/api/v1/idp/callback/*"

  # 纯 JWT 认证中间件（向后兼容）
  jwt_auth:
    type: "jwt"
    config:
      auth_service_url: "http://user-service:8083/api/v1/auth/validate"
      token_header: "Authorization"
      token_prefix: "Bearer "
      cache_ttl: 300
      
  # 请求日志中间件
  request_logging:
    type: "logging"
    config:
      log_level: "info"
      include_headers: false
      include_body: false
      max_body_size: 1024
      
  # 限流中间件
  rate_limit:
    type: "rate_limit"
    config:
      requests_per_minute: 1000
      burst_size: 100
      key_generator: "ip_and_user"  # 基于IP和用户ID限流

# 🔧 负载均衡配置
load_balancing:
  strategy: "round_robin"  # 轮询策略
  health_check:
    enabled: true
    interval: 30s
    timeout: 5s
    unhealthy_threshold: 3
    healthy_threshold: 2

# 📊 监控和指标
monitoring:
  metrics:
    enabled: true
    path: "/metrics"
    format: "prometheus"
    
  tracing:
    enabled: true
    service_name: "paas-api-gateway"
    jaeger_endpoint: "http://jaeger:14268/api/traces"
    
  logging:
    level: "info"
    format: "json"
    output: "stdout"

# 🔧 开发环境特殊配置
development:
  # 开发模式下的特殊路由
  dev_routes:
    - name: "dev-auth-bypass"
      path: "/api/v1/dev/auth"
      target: "http://user-service:8083"
      middleware: ["cors"]
      description: "开发环境认证绕过"
      
  # 开发模式调试
  debug:
    enabled: true
    log_requests: true
    log_responses: true
    cors_allow_all: true

---
# 📝 配置说明
# 
# 本配置文件定义了 PaaS 平台的 API Gateway 路由规则：
# 
# 1. 🔐 认证功能完全由 User Service (8083) 处理
# 2. 📱 应用管理功能由 App Manager (8081) 处理  
# 3. 🔄 CI/CD 功能由 CI/CD Service (8082) 处理
# 4. ⚙️ 配置管理由 Config Service (8084) 处理
# 5. 📜 脚本执行由 Script Service (8084) 处理
# 6. 📊 监控功能由 Monitor Service (8085) 处理
# 
# 这样确保了微服务的单一职责原则，解决了之前认证功能混乱的架构问题。
