# API网关开发环境配置文件
# 此配置专门用于开发环境，提供统一的API入口和认证处理
# ⚠️ 警告：此配置仅适用于开发环境，绝不能在生产环境使用！

# 服务器配置
server:
  port: 8080                          # API网关端口 (前端默认连接端口)
  mode: debug                         # 运行模式: debug (开发)
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置 - 开发环境使用SQLite
database:
  driver: sqlite                      # 数据库驱动
  dsn: "./data/api-gateway.db"        # 数据库文件路径
  max_open_conns: 50                  # 最大打开连接数
  max_idle_conns: 10                  # 最大空闲连接数
  conn_max_lifetime: 1h               # 连接最大生命周期
  log_level: info                     # 数据库日志级别

# 日志配置 - 开发环境详细日志
log:
  level: debug                        # 日志级别: debug (开发详细日志)
  format: text                        # 日志格式: text (开发易读)
  output: stdout                      # 日志输出: stdout
  service_name: "api-gateway-dev"     # 服务名称

# 🔓 安全配置 - 开发环境认证设置
security:
  # JWT认证配置 - 开发环境可选择跳过
  jwt:
    enabled: false                    # ❌ 禁用JWT校验 (开发环境)
    dev_mode: true                    # ✅ 启用开发模式
    dev_token: "api-gateway-dev-token-2024" # 🔑 开发令牌
    secret: "api-gateway-dev-jwt-secret" # 开发JWT密钥
    expires_in: 24h                   # Token过期时间
    refresh_expires_in: 168h          # 刷新Token过期时间
    
    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "api-gateway-dev-user-001"  # 开发用户ID
      tenant_id: "dev-tenant-001"     # 开发租户ID
      username: "API网关开发者"        # 开发用户名
      email: "api-dev@localhost"      # 开发用户邮箱
      roles: ["admin", "api_user"]    # API访问权限
      permissions: ["*"]              # 所有权限
    
    # 🛤️ 跳过认证的路径
    skip_paths:
      - "/health"                     # 健康检查
      - "/ready"                      # 就绪检查
      - "/metrics"                    # 监控指标
      - "/api/v1/auth/login"          # 登录接口
      - "/api/v1/auth/register"       # 注册接口
      - "/api/v1/auth/refresh"        # 刷新令牌
      - "/api/v1/auth/reset-password" # 重置密码
      - "/swagger"                    # API文档
      - "/swagger/*"                  # API文档资源
  
  # CORS配置 - 开发环境宽松设置
  cors:
    enabled: true                     # 启用CORS
    allow_origins: ["*"]              # 允许所有来源 (开发环境)
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
    allow_headers: ["*"]              # 允许所有头部
    expose_headers: ["*"]             # 暴露所有头部
    allow_credentials: true           # 允许凭据
    max_age: 86400                    # 预检请求缓存时间

# 🔀 路由代理配置 - 开发环境
proxy:
  # 后端服务配置
  services:
    # 应用管理服务
    app_manager:
      url: "http://localhost:8081"    # 应用管理服务地址
      path_prefix: "/api/v1/apps"     # 路径前缀
      timeout: 30s                    # 请求超时时间
      retry_count: 3                  # 重试次数
      health_check: "/health"         # 健康检查路径
      strip_prefix: false             # 不去除路径前缀
    
    # 脚本执行服务
    script_service:
      url: "http://localhost:8084"    # 脚本执行服务地址
      path_prefix: "/api/v1/scripts"  # 路径前缀
      timeout: 120s                   # 请求超时时间 (脚本执行较长)
      retry_count: 2                  # 重试次数
      health_check: "/health"         # 健康检查路径
      strip_prefix: false             # 不去除路径前缀
    
    # CI/CD服务
    cicd_service:
      url: "http://localhost:8082"    # CI/CD服务地址
      path_prefix: "/api/v1/cicd"     # 路径前缀
      timeout: 180s                   # 请求超时时间 (构建部署较长)
      retry_count: 2                  # 重试次数
      health_check: "/health"         # 健康检查路径
      strip_prefix: false             # 不去除路径前缀
    
    # 配置服务
    config_service:
      url: "http://localhost:8083"    # 配置服务地址
      path_prefix: "/api/v1/configs"  # 路径前缀
      timeout: 30s                    # 请求超时时间
      retry_count: 3                  # 重试次数
      health_check: "/health"         # 健康检查路径
      strip_prefix: false             # 不去除路径前缀
  
  # 负载均衡配置 - 开发环境简化
  load_balancer:
    strategy: "round_robin"           # 负载均衡策略
    health_check_interval: 15s        # 健康检查间隔 (开发环境更频繁)
    health_check_timeout: 5s          # 健康检查超时
    max_fails: 2                      # 最大失败次数 (开发环境较少)
    fail_timeout: 15s                 # 失败超时时间 (开发环境较短)

# 🔄 缓存配置 - 开发环境
cache:
  enabled: true                       # 启用缓存
  type: "memory"                      # 内存缓存 (开发环境)
  ttl: "1m"                          # 缓存过期时间 (开发环境较短)
  max_size: 500                      # 最大缓存条目数 (开发环境较小)

# 📊 监控配置 - 开发环境
monitoring:
  # 指标收集
  metrics:
    enabled: true                     # 启用指标收集
    endpoint: "/metrics"              # 指标端点
    interval: "30s"                   # 收集间隔
    detailed: true                    # 详细指标 (开发环境)
  
  # 健康检查
  health:
    check_interval: "10s"             # 健康检查间隔 (开发环境更频繁)
    timeout: "5s"                     # 健康检查超时
    detailed: true                    # 详细健康信息
  
  # 链路追踪 - 开发环境启用
  tracing:
    enabled: true                     # 启用链路追踪 (开发环境)
    jaeger_endpoint: "http://localhost:14268/api/traces"
    sample_rate: 1.0                  # 全量采样 (开发环境)

# 🚦 限流配置 - 开发环境宽松限制
rate_limit:
  enabled: false                      # 禁用限流 (开发环境)
  global:
    requests_per_second: 10000        # 全局每秒请求数限制 (开发环境很高)
    burst: 20000                      # 突发请求数限制
  
  # 按路径限流 - 开发环境宽松
  paths:
    "/api/v1/auth/login":
      requests_per_minute: 600        # 登录接口限流 (开发环境宽松)
      burst: 100
    "/api/v1/auth/register":
      requests_per_minute: 300        # 注册接口限流
      burst: 50

# 🛤️ 路由配置 - 微服务代理规则
routes:
  # 🔐 认证相关路由 -> 用户认证服务 (推荐架构)
  - path: "/api/v1/auth/*"
    target: "http://localhost:8083"    # 🔧 修复：User Service 端口是 8083
    strip_prefix: false
    timeout: 30s
    retry_count: 3
    description: "用户认证、登录、注册、权限管理"
    health_check: "/health"

  # 📱 应用管理路由 -> 应用管理服务
  - path: "/api/v1/apps/*"
    target: "http://localhost:8081"    # App Manager
    strip_prefix: false
    timeout: 30s
    retry_count: 3
    description: "应用生命周期管理、部署、监控"
    health_check: "/health"

  # 🔧 脚本执行路由 -> 脚本执行服务
  - path: "/api/v1/scripts/*"
    target: "http://localhost:8084"    # Script Service
    strip_prefix: false
    timeout: 120s                      # 脚本执行时间较长
    retry_count: 2
    description: "脚本执行、任务调度、模板管理"
    health_check: "/health"

  # 🚀 CI/CD路由 -> CI/CD服务
  - path: "/api/v1/cicd/*"
    target: "http://localhost:8082"    # CI/CD Service
    strip_prefix: false
    timeout: 180s                      # 构建部署时间较长
    retry_count: 2
    description: "构建流水线、部署管理、版本控制"
    health_check: "/health"

  # ⚙️ 配置管理路由 -> 配置服务
  - path: "/api/v1/configs/*"
    target: "http://localhost:8083"    # Config Service
    strip_prefix: false
    timeout: 30s
    retry_count: 3
    description: "配置管理、环境变量、密钥管理"
    health_check: "/health"

  # 🔄 默认路由 - 兜底处理
  - path: "/api/*"
    target: "http://localhost:8081"    # 默认转发到App Manager
    strip_prefix: false
    timeout: 30s
    retry_count: 1
    description: "默认路由，处理未匹配的API请求"

# 🔐 安全策略 - 开发环境
security_policies:
  # IP白名单 - 开发环境禁用
  ip_whitelist:
    enabled: false                    # 禁用IP白名单
    ips: []
  
  # IP黑名单 - 开发环境禁用
  ip_blacklist:
    enabled: false                    # 禁用IP黑名单
    ips: []
  
  # 请求大小限制 - 开发环境宽松
  request_limits:
    max_body_size: "100MB"           # 最大请求体大小 (开发环境较大)
    max_header_size: "10MB"          # 最大请求头大小
  
  # 安全头部 - 开发环境简化
  security_headers:
    enabled: false                    # 禁用安全头部 (开发环境)

# 📝 日志配置 - 开发环境详细日志
logging:
  # 访问日志
  access_log:
    enabled: true                     # 启用访问日志
    format: "text"                    # 文本格式 (开发环境易读)
    fields: ["timestamp", "method", "path", "status", "latency", "ip", "user_id"]
    include_body: true                # 包含请求体 (开发环境调试)
  
  # 错误日志
  error_log:
    enabled: true                     # 启用错误日志
    level: "debug"                    # 调试级别 (开发环境)
    stack_trace: true                 # 包含堆栈跟踪
    include_context: true             # 包含上下文信息

# 🔧 开发环境特殊配置
development:
  # 调试功能
  debug: true                         # 启用调试模式
  pprof:
    enabled: true                     # 启用性能分析
    port: 6060                        # pprof端口
  
  # 热重载
  hot_reload:
    enabled: true                     # 启用热重载
    watch_paths: ["./configs", "./internal"] # 监控路径
  
  # API文档
  swagger:
    enabled: true                     # 启用Swagger文档
    path: "/swagger"                  # 文档路径
    detailed: true                    # 详细文档
  
  # 开发工具
  dev_tools:
    enabled: true                     # 启用开发工具
    debug_endpoints: true             # 调试端点
    request_logger: true              # 请求日志记录器
    response_inspector: true          # 响应检查器
  
  # 模拟数据
  mock_data:
    enabled: true                     # 启用模拟数据
    auth_bypass: true                 # 认证绕过
    test_users: true                  # 测试用户

# 🚨 开发模式警告
warnings:
  show_dev_mode_warning: true
  dev_mode_banner: |
    ⚠️  API网关开发模式已启用
    🔓 用户认证已禁用，所有API请求将跳过身份验证
    🔀 请求将代理到各个后端服务
    📱 前端可直接访问 http://localhost:8080/api
    🚨 此配置仅适用于开发环境！
