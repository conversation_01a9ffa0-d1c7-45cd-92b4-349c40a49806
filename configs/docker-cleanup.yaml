# Docker镜像清理配置文件
# 用于PaaS平台的镜像存储管理

# 清理策略配置
cleanup:
  # 保留策略
  keep:
    images: 5                           # 保留最新的镜像数量
    days: 7                             # 保留天数
  
  # 排除模式 - 这些镜像不会被清理
  exclude_patterns:
    - "paas-*:latest"                   # PaaS平台最新镜像
    - "postgres:*"                      # PostgreSQL数据库镜像
    - "redis:*"                         # Redis缓存镜像
    - "nginx:*"                         # Nginx Web服务器镜像
    - "prometheus:*"                    # Prometheus监控镜像
    - "grafana:*"                       # Grafana可视化镜像
    - "elasticsearch:*"                 # Elasticsearch搜索引擎镜像
    - "kibana:*"                        # Kibana日志分析镜像
    - "alpine:*"                        # Alpine基础镜像
    - "golang:*"                        # Go语言构建镜像
    - "node:*"                          # Node.js构建镜像
  
  # 清理选项
  options:
    dry_run: false                      # 默认试运行模式
    force: false                        # 默认强制模式
    verbose: false                      # 详细输出
  
  # 定时清理配置
  schedule:
    enabled: true                       # 启用定时清理
    cron: "0 2 * * *"                  # 每天凌晨2点执行
    cleanup_types:                      # 定时清理类型
      - "dangling"                      # 清理悬空镜像
      - "unused"                        # 清理未使用镜像
      - "old"                           # 清理过期镜像
  
  # 清理规则
  rules:
    # 悬空镜像清理规则
    dangling:
      enabled: true
      force: true                       # 悬空镜像可以强制删除
    
    # 未使用镜像清理规则
    unused:
      enabled: true
      grace_period: "24h"               # 宽限期，镜像停止使用24小时后才清理
      exclude_recent: true              # 排除最近创建的镜像
    
    # 过期镜像清理规则
    old:
      enabled: true
      keep_latest: 3                    # 每个仓库至少保留3个最新版本
      max_age: "30d"                    # 最大保留30天
    
    # 容器清理规则
    containers:
      enabled: true
      states: ["exited", "dead"]        # 清理已退出和死亡状态的容器
      exclude_labels:                   # 排除带有特定标签的容器
        - "paas.keep=true"
        - "backup=true"
    
    # 数据卷清理规则
    volumes:
      enabled: false                    # 默认不清理数据卷（安全考虑）
      exclude_patterns:
        - "*-data"                      # 排除数据卷
        - "*-logs"                      # 排除日志卷
    
    # 网络清理规则
    networks:
      enabled: true
      exclude_names:                    # 排除特定网络
        - "bridge"
        - "host"
        - "none"
        - "paas-network"

# 监控配置
monitoring:
  # 告警阈值
  thresholds:
    disk_usage_percent: 80              # 磁盘使用率告警阈值
    image_count: 100                    # 镜像数量告警阈值
    container_count: 200                # 容器数量告警阈值
    volume_count: 50                    # 数据卷数量告警阈值
    
  # 通知配置
  notifications:
    enabled: true
    channels:
      webhook:
        enabled: true
        url: "http://notification-service:8085/webhook/docker-cleanup"
        timeout: "30s"
      email:
        enabled: false
        smtp_server: "smtp.example.com:587"
        from: "<EMAIL>"
        to: ["<EMAIL>"]
      slack:
        enabled: false
        webhook_url: ""
        channel: "#paas-alerts"

# 存储配置
storage:
  # Docker根目录监控
  docker_root: "/var/lib/docker"
  
  # 存储限制
  limits:
    max_images_size: "50GB"             # 镜像最大总大小
    max_containers_size: "20GB"         # 容器最大总大小
    max_volumes_size: "100GB"           # 数据卷最大总大小
  
  # 清理触发条件
  triggers:
    disk_usage_threshold: 85            # 磁盘使用率达到85%时触发清理
    free_space_threshold: "10GB"        # 剩余空间少于10GB时触发清理

# 日志配置
logging:
  level: "info"                         # 日志级别: debug, info, warn, error
  format: "json"                        # 日志格式: text, json
  file: "/app/logs/docker-cleanup.log"  # 日志文件路径
  max_size: "100MB"                     # 最大日志文件大小
  max_files: 10                         # 保留日志文件数量
  compress: true                        # 压缩旧日志文件

# 安全配置
security:
  # 操作确认
  require_confirmation: true            # 需要用户确认
  confirmation_timeout: "30s"          # 确认超时时间
  
  # 安全检查
  safety_checks:
    verify_image_usage: true            # 验证镜像使用情况
    check_running_containers: true      # 检查运行中的容器
    validate_exclude_patterns: true     # 验证排除模式
  
  # 备份配置
  backup:
    enabled: false                      # 启用备份（删除前备份镜像信息）
    retention: "7d"                     # 备份保留时间
    location: "/app/backups/docker"     # 备份位置

# 性能配置
performance:
  # 并发控制
  max_concurrent_operations: 5         # 最大并发操作数
  operation_timeout: "300s"            # 操作超时时间
  
  # 批处理配置
  batch_size: 10                       # 批处理大小
  batch_interval: "1s"                 # 批处理间隔
  
  # 资源限制
  memory_limit: "512MB"                # 内存限制
  cpu_limit: "0.5"                     # CPU限制

# 报告配置
reporting:
  # 清理报告
  cleanup_report:
    enabled: true
    format: "markdown"                  # 报告格式: text, markdown, json
    include_details: true               # 包含详细信息
    save_to_file: true                  # 保存到文件
    file_path: "/app/reports/cleanup-report-{date}.md"
  
  # 统计报告
  statistics:
    enabled: true
    interval: "daily"                   # 统计间隔: hourly, daily, weekly
    metrics:
      - "images_cleaned"
      - "space_reclaimed"
      - "containers_removed"
      - "volumes_removed"
      - "networks_removed"

# 集成配置
integrations:
  # Prometheus指标
  prometheus:
    enabled: true
    endpoint: "/metrics"
    port: 9090
    metrics:
      - "docker_cleanup_images_removed_total"
      - "docker_cleanup_space_reclaimed_bytes"
      - "docker_cleanup_duration_seconds"
      - "docker_cleanup_errors_total"
  
  # Grafana仪表板
  grafana:
    enabled: true
    dashboard_id: "docker-cleanup"
    refresh_interval: "5m"
  
  # 外部API
  external_apis:
    registry_api:
      enabled: false
      url: "https://registry.example.com/api/v2"
      auth_token: ""
    
    kubernetes_api:
      enabled: true
      config_path: "/etc/kubernetes/config"
      namespace: "paas-system"

# 环境特定配置
environments:
  development:
    cleanup:
      schedule:
        cron: "0 */6 * * *"             # 开发环境每6小时清理一次
      keep:
        images: 3                       # 开发环境保留较少镜像
        days: 3
  
  staging:
    cleanup:
      schedule:
        cron: "0 1 * * *"               # 测试环境每天凌晨1点清理
      keep:
        images: 5
        days: 7
  
  production:
    cleanup:
      schedule:
        cron: "0 2 * * 0"               # 生产环境每周日凌晨2点清理
      keep:
        images: 10                      # 生产环境保留更多镜像
        days: 14
    security:
      require_confirmation: true        # 生产环境需要确认
      backup:
        enabled: true                   # 生产环境启用备份

# 版本信息
version: "1.0.0"
last_updated: "2024-08-16"
