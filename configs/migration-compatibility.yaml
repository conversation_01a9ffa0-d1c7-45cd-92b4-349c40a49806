# PaaS 平台架构重构兼容性配置
# 确保从 App Manager 认证到 User Service 认证的平滑过渡

# 🔧 迁移配置
migration:
  # 迁移模式：parallel（并行运行）、cutover（切换）、rollback（回滚）
  mode: "parallel"
  
  # 迁移阶段
  phase: "preparation"  # preparation, migration, validation, cutover, cleanup
  
  # 兼容性设置
  compatibility:
    # 是否启用向后兼容模式
    backward_compatible: true
    
    # 兼容性持续时间（天）
    compatibility_duration_days: 30
    
    # 是否保留旧的认证端点
    keep_legacy_endpoints: true
    
    # 数据同步模式
    data_sync_mode: "bidirectional"  # unidirectional, bidirectional, none

# 🔐 认证服务配置
auth_services:
  # 新的 User Service 配置
  user_service:
    enabled: true
    host: "localhost"
    port: 8083
    base_path: "/api/v1/auth"
    health_check: "/health"
    
    # 服务权重（用于负载均衡）
    weight: 100
    
    # 超时配置
    timeout:
      connect: 5s
      read: 30s
      write: 30s
    
    # 重试配置
    retry:
      max_attempts: 3
      backoff: "exponential"
      initial_delay: 100ms
      max_delay: 5s
  
  # 旧的 App Manager 认证（兼容模式）
  app_manager_auth:
    enabled: true  # 在兼容期间保持启用
    host: "localhost"
    port: 8081
    base_path: "/api/v1/auth"
    health_check: "/health"
    
    # 服务权重（逐渐降低）
    weight: 0  # 已设为0，不再接收新请求
    
    # 标记为遗留服务
    legacy: true
    deprecation_date: "2025-09-15"
    removal_date: "2025-10-15"

# 🛣️ 路由策略配置
routing:
  # 路由策略：round_robin, weighted, failover, canary
  strategy: "weighted"
  
  # 金丝雀部署配置
  canary:
    enabled: true
    # 新服务流量百分比
    new_service_percentage: 100
    # 旧服务流量百分比
    legacy_service_percentage: 0
    
    # 金丝雀规则
    rules:
      # 基于用户的路由
      - type: "user_based"
        condition: "user.email LIKE '%@test.com'"
        target: "user_service"
      
      # 基于请求头的路由
      - type: "header_based"
        condition: "X-Migration-Test: true"
        target: "user_service"
      
      # 默认路由
      - type: "default"
        target: "user_service"
  
  # 故障转移配置
  failover:
    enabled: true
    # 健康检查失败阈值
    failure_threshold: 3
    # 恢复检查间隔
    recovery_interval: 30s
    # 故障转移目标
    fallback_service: "app_manager_auth"

# 🔄 数据同步配置
data_sync:
  enabled: true
  
  # 同步间隔
  sync_interval: "5m"
  
  # 同步的数据类型
  sync_types:
    - "users"
    - "roles"
    - "user_roles"
    - "user_sessions"
  
  # 冲突解决策略
  conflict_resolution:
    # 策略：latest_wins, source_wins, target_wins, manual
    strategy: "latest_wins"
    
    # 冲突日志
    log_conflicts: true
    conflict_log_path: "/var/log/paas/data_sync_conflicts.log"
  
  # 同步验证
  validation:
    enabled: true
    # 验证间隔
    validation_interval: "1h"
    # 数据一致性检查
    consistency_checks:
      - "user_count"
      - "role_assignments"
      - "data_integrity"

# 🔍 监控和告警配置
monitoring:
  # 迁移监控
  migration_metrics:
    enabled: true
    
    # 关键指标
    metrics:
      - name: "migration_success_rate"
        description: "迁移成功率"
        threshold: 0.95
      
      - name: "auth_response_time"
        description: "认证响应时间"
        threshold: 200  # 毫秒
      
      - name: "data_sync_lag"
        description: "数据同步延迟"
        threshold: 300  # 秒
      
      - name: "error_rate"
        description: "错误率"
        threshold: 0.01  # 1%
  
  # 告警配置
  alerts:
    # 迁移失败告警
    - name: "migration_failure"
      condition: "migration_success_rate < 0.9"
      severity: "critical"
      notification: ["email", "slack"]
    
    # 性能降级告警
    - name: "performance_degradation"
      condition: "auth_response_time > 500"
      severity: "warning"
      notification: ["slack"]
    
    # 数据同步延迟告警
    - name: "data_sync_delay"
      condition: "data_sync_lag > 600"
      severity: "warning"
      notification: ["email"]

# 🧪 测试配置
testing:
  # 自动化测试
  automated_tests:
    enabled: true
    
    # 测试套件
    test_suites:
      - name: "auth_functionality"
        description: "认证功能测试"
        tests:
          - "login_test"
          - "token_validation_test"
          - "logout_test"
          - "password_reset_test"
      
      - name: "data_consistency"
        description: "数据一致性测试"
        tests:
          - "user_data_sync_test"
          - "role_assignment_test"
          - "session_management_test"
      
      - name: "performance"
        description: "性能测试"
        tests:
          - "load_test"
          - "stress_test"
          - "concurrent_user_test"
    
    # 测试调度
    schedule:
      # 功能测试：每小时
      functional: "0 * * * *"
      # 性能测试：每天
      performance: "0 2 * * *"
      # 数据一致性测试：每30分钟
      consistency: "*/30 * * * *"
  
  # 测试环境配置
  test_environments:
    - name: "staging"
      description: "预发布环境"
      config:
        user_service_url: "http://staging-user-service:8083"
        app_manager_url: "http://staging-app-manager:8081"
    
    - name: "integration"
      description: "集成测试环境"
      config:
        user_service_url: "http://integration-user-service:8083"
        app_manager_url: "http://integration-app-manager:8081"

# 🔧 回滚配置
rollback:
  # 回滚策略
  strategy: "immediate"  # immediate, gradual, scheduled
  
  # 回滚触发条件
  triggers:
    # 错误率阈值
    - type: "error_rate"
      threshold: 0.05  # 5%
      duration: "5m"
    
    # 响应时间阈值
    - type: "response_time"
      threshold: 1000  # 毫秒
      duration: "10m"
    
    # 可用性阈值
    - type: "availability"
      threshold: 0.95  # 95%
      duration: "5m"
  
  # 回滚步骤
  steps:
    1. "停止新服务流量路由"
    2. "恢复旧服务流量路由"
    3. "验证旧服务功能正常"
    4. "通知相关人员"
    5. "记录回滚原因和时间"
  
  # 回滚验证
  validation:
    enabled: true
    timeout: "10m"
    checks:
      - "service_health"
      - "auth_functionality"
      - "user_access"

# 📝 日志配置
logging:
  # 迁移日志
  migration_logs:
    enabled: true
    level: "info"
    format: "json"
    output: "/var/log/paas/migration.log"
    
    # 日志轮转
    rotation:
      max_size: "100MB"
      max_files: 10
      max_age: "30d"
  
  # 审计日志
  audit_logs:
    enabled: true
    events:
      - "user_login"
      - "user_logout"
      - "role_assignment"
      - "service_switch"
      - "data_sync"
    
    output: "/var/log/paas/audit.log"

# 🔒 安全配置
security:
  # 迁移期间的安全措施
  migration_security:
    # 加强认证
    enhanced_auth: true
    
    # 会话管理
    session_management:
      # 强制重新登录
      force_relogin: false
      # 会话超时（分钟）
      timeout: 30
      # 并发会话限制
      max_concurrent_sessions: 5
    
    # 访问控制
    access_control:
      # IP 白名单
      ip_whitelist: []
      # 用户代理检查
      user_agent_validation: true
      # 请求频率限制
      rate_limiting:
        enabled: true
        requests_per_minute: 100

# 📊 报告配置
reporting:
  # 迁移报告
  migration_reports:
    enabled: true
    
    # 报告类型
    types:
      - "daily_summary"
      - "weekly_detailed"
      - "monthly_analysis"
    
    # 报告内容
    content:
      - "migration_progress"
      - "performance_metrics"
      - "error_analysis"
      - "user_feedback"
    
    # 报告分发
    distribution:
      email: ["<EMAIL>", "<EMAIL>"]
      slack: "#paas-migration"
      dashboard: true

---
# 📝 配置说明
# 
# 本配置文件定义了 PaaS 平台架构重构过程中的兼容性设置：
# 
# 1. 🔄 支持新旧系统并行运行
# 2. 🛣️ 提供灵活的流量路由策略
# 3. 🔍 全面的监控和告警机制
# 4. 🧪 自动化测试和验证
# 5. 🔧 快速回滚能力
# 6. 📊 详细的迁移报告
# 
# 使用此配置可以确保架构重构过程的安全性和可靠性。
