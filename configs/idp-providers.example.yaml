# IDP 提供商配置示例
# 此文件展示了如何配置不同类型的 IDP 提供商
# 复制此文件为 idp-providers.yaml 并根据实际情况修改配置

# 🆔 IDP 提供商配置
idp_providers:
  # 🔵 Azure AD (OIDC) 配置示例
  - name: "azure-ad"
    type: "oidc"
    enabled: true
    tenant_id: "default"
    priority: 1
    auto_create_user: true
    auto_link_user: false
    config:
      client_id: "your-azure-client-id"
      client_secret: "your-azure-client-secret"
      issuer: "https://login.microsoftonline.com/{tenant-id}/v2.0"
      auth_url: "https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/authorize"
      token_url: "https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/token"
      user_info_url: "https://graph.microsoft.com/v1.0/me"
      jwks_url: "https://login.microsoftonline.com/{tenant-id}/discovery/v2.0/keys"
      scopes: ["openid", "profile", "email", "User.Read"]
      timeout: 30
      redirect_url: "https://your-domain.com/api/v1/idp/callback/azure-ad"
    mapping:
      user_id: "id"
      username: "userPrincipalName"
      email: "mail"
      first_name: "givenName"
      last_name: "surname"
      avatar: "photo"
      phone: "mobilePhone"
      roles: "roles"
      groups: "groups"

  # 🟢 Google (OIDC) 配置示例
  - name: "google"
    type: "oidc"
    enabled: true
    tenant_id: "default"
    priority: 2
    auto_create_user: true
    auto_link_user: false
    config:
      client_id: "your-google-client-id"
      client_secret: "your-google-client-secret"
      issuer: "https://accounts.google.com"
      auth_url: "https://accounts.google.com/o/oauth2/v2/auth"
      token_url: "https://oauth2.googleapis.com/token"
      user_info_url: "https://www.googleapis.com/oauth2/v2/userinfo"
      jwks_url: "https://www.googleapis.com/oauth2/v3/certs"
      scopes: ["openid", "profile", "email"]
      timeout: 30
      redirect_url: "https://your-domain.com/api/v1/idp/callback/google"
    mapping:
      user_id: "id"
      username: "email"
      email: "email"
      first_name: "given_name"
      last_name: "family_name"
      avatar: "picture"
      phone: ""
      roles: ""
      groups: ""

  # 🟡 GitHub (OAuth2) 配置示例
  - name: "github"
    type: "oauth2"
    enabled: false
    tenant_id: "default"
    priority: 3
    auto_create_user: true
    auto_link_user: false
    config:
      client_id: "your-github-client-id"
      client_secret: "your-github-client-secret"
      auth_url: "https://github.com/login/oauth/authorize"
      token_url: "https://github.com/login/oauth/access_token"
      user_info_url: "https://api.github.com/user"
      scopes: ["user:email", "read:user"]
      timeout: 30
      redirect_url: "https://your-domain.com/api/v1/idp/callback/github"
    mapping:
      user_id: "id"
      username: "login"
      email: "email"
      first_name: "name"
      last_name: ""
      avatar: "avatar_url"
      phone: ""
      roles: ""
      groups: ""

  # 🔴 自定义 OIDC 提供商配置示例
  - name: "custom-oidc"
    type: "oidc"
    enabled: false
    tenant_id: "default"
    priority: 4
    auto_create_user: true
    auto_link_user: false
    config:
      client_id: "your-custom-client-id"
      client_secret: "your-custom-client-secret"
      issuer: "https://your-idp.com"
      auth_url: "https://your-idp.com/auth"
      token_url: "https://your-idp.com/token"
      user_info_url: "https://your-idp.com/userinfo"
      jwks_url: "https://your-idp.com/.well-known/jwks.json"
      scopes: ["openid", "profile", "email"]
      timeout: 30
      redirect_url: "https://your-domain.com/api/v1/idp/callback/custom-oidc"
    mapping:
      user_id: "sub"
      username: "preferred_username"
      email: "email"
      first_name: "given_name"
      last_name: "family_name"
      avatar: "picture"
      phone: "phone_number"
      roles: "roles"
      groups: "groups"

  # 🟠 SAML 提供商配置示例
  - name: "saml-idp"
    type: "saml"
    enabled: false
    tenant_id: "default"
    priority: 5
    auto_create_user: true
    auto_link_user: false
    config:
      entity_id: "https://your-domain.com/saml/metadata"
      sso_url: "https://your-saml-idp.com/sso"
      slo_url: "https://your-saml-idp.com/slo"
      certificate: |
        -----BEGIN CERTIFICATE-----
        YOUR_SAML_CERTIFICATE_HERE
        -----END CERTIFICATE-----
      private_key: |
        -----BEGIN PRIVATE KEY-----
        YOUR_PRIVATE_KEY_HERE
        -----END PRIVATE KEY-----
      timeout: 30
      redirect_url: "https://your-domain.com/api/v1/idp/callback/saml-idp"
    mapping:
      user_id: "NameID"
      username: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
      email: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
      first_name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
      last_name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
      avatar: ""
      phone: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/mobilephone"
      roles: "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
      groups: "http://schemas.xmlsoap.org/claims/Group"

  # 🔵 LDAP 提供商配置示例
  - name: "ldap"
    type: "ldap"
    enabled: false
    tenant_id: "default"
    priority: 6
    auto_create_user: true
    auto_link_user: false
    config:
      host: "ldap.your-domain.com"
      port: 389
      base_dn: "dc=your-domain,dc=com"
      bind_dn: "cn=admin,dc=your-domain,dc=com"
      bind_password: "your-bind-password"
      user_filter: "(&(objectClass=person)(uid=%s))"
      timeout: 30
    mapping:
      user_id: "uid"
      username: "uid"
      email: "mail"
      first_name: "givenName"
      last_name: "sn"
      avatar: ""
      phone: "telephoneNumber"
      roles: "memberOf"
      groups: "memberOf"

# 🔧 全局 IDP 配置
global_config:
  # 默认租户 ID
  default_tenant_id: "default"
  
  # 会话配置
  session:
    timeout: "24h"              # 会话超时时间
    refresh_threshold: "1h"     # 刷新阈值
    max_concurrent: 5           # 最大并发会话数
  
  # 同步配置
  sync:
    enabled: true               # 是否启用自动同步
    interval: "1h"              # 同步间隔
    batch_size: 100             # 批量同步大小
    retry_count: 3              # 重试次数
    
  # 安全配置
  security:
    require_https: true         # 是否要求 HTTPS
    validate_issuer: true       # 是否验证颁发者
    validate_audience: true     # 是否验证受众
    clock_skew: "5m"           # 时钟偏差容忍度
    
  # 日志配置
  logging:
    level: "info"               # 日志级别
    audit_enabled: true         # 是否启用审计日志
    sensitive_data_mask: true   # 是否屏蔽敏感数据

# 📝 配置说明：
# 1. 每个 IDP 提供商都需要唯一的 name
# 2. type 支持: oidc, oauth2, saml, ldap
# 3. priority 数字越小优先级越高
# 4. auto_create_user: 是否自动创建不存在的用户
# 5. auto_link_user: 是否自动关联现有用户（根据邮箱匹配）
# 6. mapping 配置用于将 IDP 返回的字段映射到本地用户字段
# 7. 生产环境请确保所有敏感信息（如 client_secret）通过环境变量配置
