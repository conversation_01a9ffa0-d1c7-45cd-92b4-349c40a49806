# 脚本执行服务开发环境配置文件
# 此配置专门用于开发环境，禁用了用户认证以简化开发流程
# ⚠️ 警告：此配置仅适用于开发环境，绝不能在生产环境使用！

# 服务器配置
server:
  port: 8084                          # 服务端口
  mode: debug                         # 运行模式: debug (开发)
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置 - 开发环境
database:
  driver: sqlite                      # 数据库驱动
  dsn: "./data/script-service.db"     # 数据库文件路径
  max_open_conns: 50                  # 最大打开连接数
  max_idle_conns: 10                  # 最大空闲连接数
  conn_max_lifetime: 1h               # 连接最大生命周期
  log_level: info                     # 数据库日志级别

# 日志配置 - 开发环境
log:
  level: debug                        # 日志级别: debug
  format: text                        # 日志格式: text (开发易读)
  output: stdout                      # 日志输出
  service_name: "script-service-dev"  # 服务名称

# 🔓 安全配置 - 开发环境认证设置
security:
  # JWT配置 - 开发环境跳过认证
  jwt:
    enabled: false                    # ❌ 禁用JWT校验
    dev_mode: true                    # ✅ 启用开发模式
    dev_token: "script-dev-token-2024" # 🔑 开发令牌
    secret: "script-dev-jwt-secret"   # 开发JWT密钥
    expires_in: 24h                   # Token过期时间
    
    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "script-dev-user-001"       # 开发用户ID
      tenant_id: "dev-tenant-001"     # 开发租户ID
      username: "脚本开发者"           # 开发用户名
      email: "script-dev@localhost"   # 开发用户邮箱
      roles: ["admin", "script_executor"] # 脚本执行权限
      permissions: ["script:*"]       # 脚本相关所有权限
    
    # 跳过认证的路径
    skip_paths:
      - "/health"
      - "/ready"
      - "/metrics"
      - "/swagger"
      - "/swagger/*"
  
  # 权限配置 - 开发环境宽松权限
  rbac:
    enabled: false                    # 禁用RBAC (开发环境)
    default_role: "admin"             # 默认管理员角色
  
  # 审计配置 - 开发环境简化审计
  audit:
    enabled: true                     # 启用审计 (用于调试)
    log_requests: true                # 记录请求
    log_responses: false              # 不记录响应 (减少日志量)

# 脚本执行配置 - 开发环境优化
script:
  # 基础配置
  default_timeout: 600                # 默认超时时间(秒) - 开发环境更长
  max_concurrent_tasks: 5             # 最大并发任务数 - 开发环境较少
  task_retention_days: 7              # 任务保留天数 - 开发环境较短
  callback_timeout: 60                # 回调超时时间(秒)
  
  # 存储配置
  workspace_base_path: "./data/workspaces"  # 工作空间路径
  artifact_base_path: "./data/artifacts"    # 产物存储路径
  max_artifact_size: 50MB             # 单个产物最大大小 - 开发环境较小
  
  # 运行时配置 - 开发环境使用轻量镜像
  default_python_image: "python:3.11-alpine"
  default_nodejs_image: "node:18-alpine"
  default_go_image: "golang:1.21-alpine"
  
  # 资源限制 - 开发环境较宽松
  default_cpu_limit: "1000m"          # CPU限制
  default_memory_limit: "1Gi"         # 内存限制
  default_disk_limit: "2Gi"           # 磁盘限制

# Docker配置 - 开发环境
docker:
  # 本地Docker配置
  host: "unix:///var/run/docker.sock" # 本地Docker守护进程
  api_version: "1.41"                 # Docker API版本
  timeout: 30s                        # 连接超时
  
  # TLS配置 - 开发环境不使用TLS
  tls:
    enabled: false                    # 禁用TLS
    verify: false                     # 不验证证书
  
  # 连接池配置
  connection:
    max_idle_conns: 5                 # 最大空闲连接数
    max_conns_per_host: 10            # 每个主机最大连接数
    idle_conn_timeout: 60s            # 空闲连接超时
    keep_alive: 30s                   # 保持连接时间

# 缓存配置 - 开发环境
cache:
  enabled: true                       # 启用缓存
  type: "memory"                      # 内存缓存
  ttl: 30m                           # 缓存过期时间
  max_size: 500                      # 最大缓存条目数

# 监控配置 - 开发环境
monitoring:
  metrics:
    enabled: true                     # 启用指标收集
    endpoint: "/metrics"              # 指标端点
    interval: "30s"                   # 收集间隔
  
  health:
    check_interval: "15s"             # 健康检查间隔
    timeout: "5s"                     # 健康检查超时

# 🔧 开发环境特殊配置
development:
  debug: true                         # 调试模式
  pprof:
    enabled: true                     # 性能分析
    port: 6064                        # pprof端口
  
  # 脚本调试
  script_debug:
    enabled: true                     # 启用脚本调试
    keep_containers: true             # 保留容器用于调试
    verbose_logs: true                # 详细日志
  
  # 测试数据
  test_data:
    enabled: true                     # 启用测试数据
    sample_scripts: true              # 加载示例脚本
  
  # API文档
  swagger:
    enabled: true                     # 启用API文档
    path: "/swagger"                  # 文档路径

# 🚨 开发模式警告
warnings:
  show_dev_mode_warning: true
  dev_mode_banner: |
    ⚠️  脚本服务开发模式已启用
    🔓 用户认证已禁用，所有脚本执行请求将使用默认开发用户
    🐳 Docker容器将在本地运行，请确保Docker服务已启动
    🚨 此配置仅适用于开发环境！
