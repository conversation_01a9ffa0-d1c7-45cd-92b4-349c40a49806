# CI/CD 服务配置文件

# 服务器配置
server:
  port: 8082                    # 服务端口
  mode: debug                   # 运行模式: debug, release, test
  read_timeout: 30s             # 读取超时时间
  write_timeout: 30s            # 写入超时时间
  idle_timeout: 60s             # 空闲超时时间

# 数据库配置
database:
  driver: sqlite                # 数据库驱动: sqlite, postgres
  dsn: "./data/cicd.db"         # 数据源名称
  max_open_conns: 100           # 最大打开连接数
  max_idle_conns: 10            # 最大空闲连接数
  conn_max_lifetime: 3600       # 连接最大生存时间(秒)
  log_level: warn               # 数据库日志级别: silent, error, warn, info

# PostgreSQL 配置示例 (生产环境推荐)
# database:
#   driver: postgres
#   dsn: "host=localhost user=paas password=paas123 dbname=paas_cicd port=5432 sslmode=disable TimeZone=Asia/Shanghai"
#   max_open_conns: 100
#   max_idle_conns: 10
#   conn_max_lifetime: 3600
#   log_level: warn

# Redis 配置 (用于任务队列和缓存)
redis:
  addr: "localhost:6379"       # Redis 地址
  password: ""                 # Redis 密码
  db: 1                        # Redis 数据库编号
  pool_size: 10                # 连接池大小
  min_idle_conns: 5            # 最小空闲连接数
  dial_timeout: 5s             # 连接超时时间
  read_timeout: 3s             # 读取超时时间
  write_timeout: 3s            # 写入超时时间

# 日志配置
log:
  level: info                   # 日志级别: debug, info, warn, error
  format: json                  # 日志格式: json, text
  output: stdout                # 日志输出: stdout, stderr, file
  file_path: "./logs/cicd-service.log"  # 日志文件路径 (当 output 为 file 时)
  max_size: 100                 # 日志文件最大大小 (MB)
  max_backups: 5                # 保留的日志文件数量
  max_age: 30                   # 日志文件保留天数
  compress: true                # 是否压缩旧日志文件

# 构建配置
build:
  # 构建节点配置
  nodes:
    default_capacity: 5         # 默认节点容量
    heartbeat_interval: 30s     # 心跳检测间隔
    offline_timeout: 300s       # 节点离线超时时间
  
  # 构建队列配置
  queue:
    max_concurrent_builds: 10   # 最大并发构建数
    default_priority: 0         # 默认优先级
    retry_attempts: 3           # 重试次数
    retry_delay: 60s            # 重试延迟
  
  # 构建超时配置
  timeout:
    default_build: 3600s        # 默认构建超时时间 (1小时)
    default_stage: 1800s        # 默认阶段超时时间 (30分钟)
    default_step: 600s          # 默认步骤超时时间 (10分钟)
  
  # 构建工作空间配置
  workspace:
    base_path: "/tmp/builds"    # 构建工作空间基础路径
    cleanup_after: 24h          # 构建完成后清理时间
    max_size: 10GB              # 工作空间最大大小

# Docker 配置
docker:
  # Docker 守护进程配置
  host: "unix:///var/run/docker.sock"  # Docker 守护进程地址
  api_version: "1.41"                   # Docker API 版本
  
  # 镜像仓库配置
  registry:
    default_url: "localhost:5000"      # 默认镜像仓库地址
    username: ""                       # 镜像仓库用户名
    password: ""                       # 镜像仓库密码
    insecure: true                     # 是否允许不安全的连接
  
  # 构建配置
  build:
    context_size_limit: 1GB            # 构建上下文大小限制
    build_timeout: 1800s               # 镜像构建超时时间
    no_cache: false                    # 是否禁用缓存
    pull_always: false                 # 是否总是拉取基础镜像

# 部署配置
deploy:
  # 部署策略配置
  strategies:
    rolling:
      max_unavailable: 1               # 滚动更新最大不可用实例数
      max_surge: 1                     # 滚动更新最大超出实例数
      timeout: 300s                    # 部署超时时间
    
    blue_green:
      auto_promote: false              # 是否自动切换
      promotion_timeout: 600s          # 切换超时时间
      rollback_timeout: 300s           # 回滚超时时间
    
    canary:
      auto_promote: true               # 是否自动推广
      analysis_interval: 60s           # 分析间隔
      success_rate_threshold: 99.5     # 成功率阈值
      response_time_threshold: 500     # 响应时间阈值 (ms)

# Webhook 配置
webhook:
  secret: "webhook-secret-key"         # Webhook 密钥
  timeout: 30s                         # Webhook 处理超时时间
  max_payload_size: 10MB               # 最大载荷大小
  
  # 支持的 Git 服务
  sources:
    gitea:
      enabled: true                    # 是否启用 Gitea 集成
      api_url: "http://localhost:3000/api/v1"  # Gitea API 地址
      token: ""                        # Gitea API Token
    
    github:
      enabled: false                   # 是否启用 GitHub 集成
      api_url: "https://api.github.com"
      token: ""                        # GitHub API Token
    
    gitlab:
      enabled: false                   # 是否启用 GitLab 集成
      api_url: "https://gitlab.com/api/v4"
      token: ""                        # GitLab API Token

# 通知配置
notification:
  # 邮件通知
  email:
    enabled: false                     # 是否启用邮件通知
    smtp_host: "smtp.example.com"      # SMTP 服务器地址
    smtp_port: 587                     # SMTP 端口
    username: ""                       # SMTP 用户名
    password: ""                       # SMTP 密码
    from: "<EMAIL>"        # 发件人邮箱
  
  # Slack 通知
  slack:
    enabled: false                     # 是否启用 Slack 通知
    webhook_url: ""                    # Slack Webhook URL
    channel: "#ci-cd"                  # 默认频道
  
  # 企业微信通知
  wechat:
    enabled: false                     # 是否启用企业微信通知
    webhook_url: ""                    # 企业微信 Webhook URL

# 缓存配置
cache:
  # 构建缓存
  build:
    enabled: true                      # 是否启用构建缓存
    ttl: 168h                          # 缓存生存时间 (7天)
    max_size: 50GB                     # 最大缓存大小
  
  # 依赖缓存
  dependencies:
    enabled: true                      # 是否启用依赖缓存
    ttl: 720h                          # 缓存生存时间 (30天)
    max_size: 20GB                     # 最大缓存大小

# 监控配置
monitoring:
  # 指标收集
  metrics:
    enabled: true                      # 是否启用指标收集
    endpoint: "/metrics"               # 指标端点
    interval: 15s                      # 收集间隔
  
  # 健康检查
  health:
    check_interval: 30s                # 健康检查间隔
    timeout: 10s                       # 健康检查超时时间

# 安全配置
security:
  # 🔒 JWT认证配置 (可通过环境变量覆盖)
  jwt:
    enabled: true                      # 是否启用 JWT 校验 (环境变量: PAAS_AUTH_ENABLED)
    secret: "your-jwt-secret-key"      # JWT 密钥 (环境变量: PAAS_JWT_SECRET)
    expire_hours: 24                   # Token 过期时间 (小时)

    # 🔧 开发模式配置
    dev_mode: false                    # 开发模式 (环境变量: PAAS_DEV_MODE)
    dev_token: "cicd-dev-token"        # 开发令牌 (环境变量: PAAS_DEV_TOKEN)

    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "cicd-dev-user-001"          # 环境变量: PAAS_DEV_USER_ID
      tenant_id: "dev-tenant-001"      # 环境变量: PAAS_DEV_USER_TENANT_ID
      username: "CI/CD开发者"           # 环境变量: PAAS_DEV_USER_USERNAME
      email: "cicd-dev@localhost"      # 环境变量: PAAS_DEV_USER_EMAIL
      roles: ["admin", "cicd_manager"] # 环境变量: PAAS_DEV_USER_ROLES

    # 🛤️ 跳过认证的路径
    skip_paths:
      - "/health"
      - "/ready"
      - "/metrics"
      - "/webhook"                     # Webhook接口
      - "/swagger"
      - "/swagger/*"

  # API 限流配置
  rate_limit:
    enabled: true                      # 是否启用限流 (开发环境可设为false)
    requests_per_minute: 100           # 每分钟请求数限制
    burst: 20                          # 突发请求数限制

# 功能开关
features:
  parallel_builds: true                # 是否支持并行构建
  build_cache: true                    # 是否启用构建缓存
  auto_deployment: true                # 是否支持自动部署
  rollback: true                       # 是否支持回滚
  approval_workflow: true              # 是否启用审批工作流
  multi_arch_build: false              # 是否支持多架构构建
