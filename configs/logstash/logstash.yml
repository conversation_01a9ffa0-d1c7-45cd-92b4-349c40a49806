# PaaS 平台 Logstash 配置文件
# 定义了日志处理管道的基础配置

# 节点配置
node.name: paas-logstash-node

# 数据路径
path.data: /usr/share/logstash/data
path.logs: /usr/share/logstash/logs

# 管道配置
pipeline.workers: 2
pipeline.batch.size: 125
pipeline.batch.delay: 50

# 队列配置
queue.type: memory
queue.max_bytes: 1gb

# HTTP API 配置
http.host: "0.0.0.0"
http.port: 9600

# 日志配置
log.level: info
log.format: plain

# 监控配置
monitoring.enabled: true
monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]

# 配置重载
config.reload.automatic: true
config.reload.interval: 3s

# 死信队列配置
dead_letter_queue.enable: true
dead_letter_queue.max_bytes: 1gb

# X-Pack 配置
xpack.monitoring.enabled: true
xpack.monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
