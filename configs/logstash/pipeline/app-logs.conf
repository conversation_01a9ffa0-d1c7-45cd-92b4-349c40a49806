# PaaS 平台应用日志处理管道
# 处理来自各个微服务的应用日志

input {
  # Filebeat 输入
  beats {
    port => 5044
    type => "app-logs"
  }
  
  # TCP 输入 (用于直接日志发送)
  tcp {
    port => 5000
    type => "app-logs"
    codec => json_lines
  }
  
  # HTTP 输入 (用于 HTTP 日志发送)
  http {
    port => 8080
    type => "app-logs"
    codec => json
  }
}

filter {
  # 只处理应用日志
  if [type] == "app-logs" {
    
    # 解析 JSON 格式的日志
    if [message] =~ /^\{.*\}$/ {
      json {
        source => "message"
        target => "parsed"
      }
      
      # 提取解析后的字段
      if [parsed] {
        mutate {
          add_field => {
            "log_level" => "%{[parsed][level]}"
            "service_name" => "%{[parsed][service]}"
            "component" => "%{[parsed][component]}"
            "request_id" => "%{[parsed][request_id]}"
            "user_id" => "%{[parsed][user_id]}"
            "session_id" => "%{[parsed][session_id]}"
          }
        }
        
        # 移除原始 parsed 字段
        mutate {
          remove_field => ["parsed"]
        }
      }
    }
    
    # 解析结构化日志 (Go 应用)
    if [message] =~ /^time=/ {
      grok {
        match => { 
          "message" => "time=%{TIMESTAMP_ISO8601:timestamp} level=%{WORD:log_level} msg=\"%{DATA:log_message}\"( service=%{WORD:service_name})?( component=%{WORD:component})?( request_id=%{UUID:request_id})?( user_id=%{UUID:user_id})?( error=\"%{DATA:error_message}\")?" 
        }
      }
    }
    
    # 解析 Gin 框架访问日志
    if [message] =~ /^\[GIN\]/ {
      grok {
        match => { 
          "message" => "\[GIN\] %{TIMESTAMP_ISO8601:timestamp} \| %{INT:status_code} \| %{DATA:response_time} \| %{IPORHOST:client_ip} \| %{WORD:http_method} \"%{URIPATH:request_path}\"" 
        }
      }
      
      mutate {
        add_field => { "log_type" => "access" }
        add_field => { "service_name" => "gin-server" }
      }
      
      # 转换响应时间为数值
      if [response_time] {
        mutate {
          gsub => [ "response_time", "[μms]", "" ]
        }
        mutate {
          convert => { "response_time" => "float" }
        }
      }
    }
    
    # 解析错误日志
    if [log_level] in ["ERROR", "error", "FATAL", "fatal"] {
      mutate {
        add_field => { "log_type" => "error" }
        add_tag => ["error"]
      }
      
      # 提取堆栈跟踪
      if [message] =~ /stack trace|stacktrace|panic/ {
        mutate {
          add_tag => ["stack_trace"]
        }
      }
    }
    
    # 解析警告日志
    if [log_level] in ["WARN", "warn", "WARNING", "warning"] {
      mutate {
        add_field => { "log_type" => "warning" }
        add_tag => ["warning"]
      }
    }
    
    # 解析信息日志
    if [log_level] in ["INFO", "info"] {
      mutate {
        add_field => { "log_type" => "info" }
      }
    }
    
    # 解析调试日志
    if [log_level] in ["DEBUG", "debug"] {
      mutate {
        add_field => { "log_type" => "debug" }
      }
    }
    
    # 从文件路径提取服务名称
    if [log][file][path] {
      grok {
        match => { 
          "[log][file][path]" => "/var/log/paas/%{WORD:service_name}/" 
        }
        tag_on_failure => ["_grokparsefailure_service"]
      }
    }
    
    # 设置默认服务名称
    if ![service_name] {
      mutate {
        add_field => { "service_name" => "unknown" }
      }
    }
    
    # 标准化时间戳
    if [timestamp] {
      date {
        match => [ "timestamp", "ISO8601" ]
        target => "@timestamp"
      }
    }
    
    # 添加环境标签
    mutate {
      add_field => { 
        "environment" => "development"
        "platform" => "paas"
        "log_source" => "application"
      }
    }
    
    # 地理位置解析 (如果有 IP)
    if [client_ip] and [client_ip] !~ /^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.|127\.)/ {
      geoip {
        source => "client_ip"
        target => "geoip"
      }
    }
    
    # 用户代理解析
    if [user_agent] {
      useragent {
        source => "user_agent"
        target => "ua"
      }
    }
    
    # 清理字段
    mutate {
      remove_field => ["message", "host", "agent", "ecs", "input"]
    }
  }
}

output {
  # 输出到 Elasticsearch
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    
    # 根据日志类型和服务创建索引
    index => "paas-app-logs-%{service_name}-%{+YYYY.MM.dd}"
    
    # 文档类型
    document_type => "_doc"
    
    # 模板配置
    template_name => "paas-app-logs"
    template_pattern => "paas-app-logs-*"
    template => "/usr/share/logstash/templates/app-logs-template.json"
    template_overwrite => true
    
    # 性能优化
    workers => 2
    flush_size => 500
    idle_flush_time => 1
  }
  
  # 错误日志额外输出到专门的索引
  if "error" in [tags] {
    elasticsearch {
      hosts => ["http://elasticsearch:9200"]
      index => "paas-error-logs-%{+YYYY.MM.dd}"
      document_type => "_doc"
    }
  }
  
  # 调试输出 (开发环境)
  if [log_level] == "DEBUG" {
    stdout {
      codec => rubydebug {
        metadata => false
      }
    }
  }
}

# 错误处理
if "_grokparsefailure" in [tags] {
  mutate {
    add_field => { "parse_error" => "grok_parse_failure" }
  }
}

if "_jsonparsefailure" in [tags] {
  mutate {
    add_field => { "parse_error" => "json_parse_failure" }
  }
}
