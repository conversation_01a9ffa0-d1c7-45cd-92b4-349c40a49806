# PaaS 平台 Logstash 管道配置
# 定义了多个日志处理管道

# 应用日志管道
- pipeline.id: paas-app-logs
  path.config: "/usr/share/logstash/pipeline/app-logs.conf"
  pipeline.workers: 2
  pipeline.batch.size: 125

# 访问日志管道
- pipeline.id: paas-access-logs
  path.config: "/usr/share/logstash/pipeline/access-logs.conf"
  pipeline.workers: 1
  pipeline.batch.size: 250

# 错误日志管道
- pipeline.id: paas-error-logs
  path.config: "/usr/share/logstash/pipeline/error-logs.conf"
  pipeline.workers: 1
  pipeline.batch.size: 50

# 系统日志管道
- pipeline.id: paas-system-logs
  path.config: "/usr/share/logstash/pipeline/system-logs.conf"
  pipeline.workers: 1
  pipeline.batch.size: 100

# 审计日志管道
- pipeline.id: paas-audit-logs
  path.config: "/usr/share/logstash/pipeline/audit-logs.conf"
  pipeline.workers: 1
  pipeline.batch.size: 50
