# 应用管理服务开发环境配置文件
# 此配置专门用于开发环境，禁用了用户认证以简化开发流程
# ⚠️ 警告：此配置仅适用于开发环境，绝不能在生产环境使用！

# 服务器配置
server:
  port: 8081                          # 服务端口
  mode: debug                         # 运行模式: debug (开发), release (生产)
  read_timeout: 30s                   # 读取超时时间
  write_timeout: 30s                  # 写入超时时间
  idle_timeout: 60s                   # 空闲超时时间

# 数据库配置 - 开发环境使用SQLite
database:
  driver: sqlite                      # 数据库驱动
  dsn: "./data/app-manager.db"        # 数据库文件路径
  max_open_conns: 25                  # 最大打开连接数
  max_idle_conns: 5                   # 最大空闲连接数
  conn_max_lifetime: 1h               # 连接最大生命周期
  log_level: info                     # 数据库日志级别

# 日志配置 - 开发环境详细日志
log:
  level: debug                        # 日志级别: debug (开发详细日志)
  format: text                        # 日志格式: text (开发易读), json (生产)
  output: stdout                      # 日志输出: stdout
  service_name: "app-manager-dev"     # 服务名称

# 🔓 安全配置 - 开发环境认证设置
security:
  jwt:
    # 🚨 开发环境认证配置 - 仅用于开发！
    enabled: true                     # ✅ 启用JWT校验 (但在开发模式下会简化处理)
    dev_mode: true                    # ✅ 启用开发模式
    dev_token: "dev-token-2024"       # 🔑 开发令牌 (可选使用)
    secret: "dev-jwt-secret-key-not-for-production"  # 开发用JWT密钥
    expires_in: 24h                   # Token过期时间
    refresh_expires_in: 168h          # 刷新Token过期时间
    
    # 🧑‍💻 开发模式默认用户信息
    dev_user:
      id: "dev-user-001"              # 开发用户ID
      tenant_id: "dev-tenant-001"     # 开发租户ID
      username: "开发者"               # 开发用户名
      email: "developer@localhost"    # 开发用户邮箱
      roles: ["admin", "developer"]   # 开发用户角色 (拥有管理员权限)
      permissions: ["*"]              # 开发用户权限 (所有权限)
    
    # 🛤️ 跳过认证的路径 (这些路径始终不需要认证)
    skip_paths:
      - "/health"                     # 健康检查
      - "/ready"                      # 就绪检查
      - "/metrics"                    # 监控指标
      - "/api/v1/auth/login"          # 登录接口
      - "/api/v1/auth/register"       # 注册接口
      - "/api/v1/auth/refresh"        # 刷新令牌
      - "/swagger"                    # API文档
      - "/swagger/*"                  # API文档资源

# 应用管理配置
app:
  # 存储配置
  storage:
    type: "local"                     # 存储类型: local (本地存储)
    base_path: "./data/storage"       # 存储基础路径
    max_size: "10GB"                  # 最大存储大小
  
  # 模板配置
  template:
    base_path: "./data/templates"     # 模板基础路径
    cache_enabled: true               # 启用模板缓存
    cache_ttl: "1h"                   # 缓存过期时间
  
  # 部署配置
  deployment:
    timeout: "300s"                   # 部署超时时间
    max_concurrent: 5                 # 最大并发部署数
    retry_count: 3                    # 重试次数

# 缓存配置 - 开发环境使用内存缓存
cache:
  enabled: true                       # 启用缓存
  type: "memory"                      # 缓存类型: memory (内存)
  ttl: "30m"                          # 缓存过期时间 (开发环境较短)
  max_size: 1000                      # 最大缓存条目数

# 监控配置 - 开发环境监控
monitoring:
  metrics:
    enabled: true                     # 启用指标收集
    endpoint: "/metrics"              # 指标端点
    interval: "30s"                   # 收集间隔
  
  health:
    check_interval: "10s"             # 健康检查间隔 (开发环境更频繁)
    timeout: "5s"                     # 健康检查超时

# 🔧 开发环境特殊配置
development:
  # 调试功能
  debug: true                         # 启用调试模式
  pprof:
    enabled: true                     # 启用性能分析
    port: 6061                        # pprof端口
  
  # 热重载
  hot_reload:
    enabled: true                     # 启用热重载
    watch_paths: ["./internal/app", "./configs"]  # 监控路径
  
  # API文档
  swagger:
    enabled: true                     # 启用Swagger文档
    path: "/swagger"                  # 文档路径
  
  # 开发工具
  dev_tools:
    enabled: true                     # 启用开发工具
    mock_data: true                   # 启用模拟数据
    test_endpoints: true              # 启用测试端点

# 🚨 安全警告配置
warnings:
  show_dev_mode_warning: true         # 显示开发模式警告
  dev_mode_banner: |
    ⚠️  开发模式已启用 - 用户认证已禁用
    🔓 所有API请求将使用默认开发用户身份
    🚨 此配置仅适用于开发环境，请勿在生产环境使用！
