# PaaS 平台 Prometheus 告警规则
# 定义了系统可用性、性能和业务相关的告警规则

groups:
  # 🚨 系统可用性告警
  - name: system-availability
    interval: 30s
    rules:
      # 服务下线告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: availability
        annotations:
          summary: "服务 {{ $labels.instance }} 不可用"
          description: "服务 {{ $labels.service }} ({{ $labels.instance }}) 已经下线超过1分钟"
          runbook_url: "https://wiki.paas.com/runbooks/service-down"
          dashboard_url: "http://grafana.paas.local/d/service-overview"

      # 高错误率告警
      - alert: HighErrorRate
        expr: |
          (
            rate(paas_http_requests_total{status_code=~"5.."}[5m]) /
            rate(paas_http_requests_total[5m])
          ) * 100 > 5
        for: 5m
        labels:
          severity: warning
          category: availability
        annotations:
          summary: "{{ $labels.service }} 错误率过高"
          description: "服务 {{ $labels.service }} 在过去5分钟内错误率为 {{ $value | humanizePercentage }}，超过5%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/high-error-rate"

      # 服务响应时间过长
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            rate(paas_http_request_duration_seconds_bucket[5m])
          ) > 0.5
        for: 10m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "{{ $labels.service }} 响应时间过长"
          description: "服务 {{ $labels.service }} P95响应时间为 {{ $value | humanizeDuration }}，超过500ms阈值"
          runbook_url: "https://wiki.paas.com/runbooks/high-response-time"

  # 🔧 系统资源告警
  - name: system-resources
    interval: 30s
    rules:
      # 高内存使用率
      - alert: HighMemoryUsage
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) /
            node_memory_MemTotal_bytes
          ) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "系统内存使用率过高"
          description: "主机 {{ $labels.instance }} 内存使用率为 {{ $value | humanizePercentage }}，超过85%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/high-memory-usage"

      # 高 CPU 使用率
      - alert: HighCPUUsage
        expr: |
          (
            100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
          ) > 80
        for: 10m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "系统 CPU 使用率过高"
          description: "主机 {{ $labels.instance }} CPU使用率为 {{ $value | humanizePercentage }}，超过80%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/high-cpu-usage"

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: |
          (
            (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) /
            node_filesystem_size_bytes{fstype!="tmpfs"}
          ) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "磁盘空间不足"
          description: "主机 {{ $labels.instance }} 挂载点 {{ $labels.mountpoint }} 磁盘使用率为 {{ $value | humanizePercentage }}，超过85%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/disk-space-low"

      # 磁盘空间严重不足
      - alert: DiskSpaceCritical
        expr: |
          (
            (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) /
            node_filesystem_size_bytes{fstype!="tmpfs"}
          ) * 100 > 95
        for: 1m
        labels:
          severity: critical
          category: resources
        annotations:
          summary: "磁盘空间严重不足"
          description: "主机 {{ $labels.instance }} 挂载点 {{ $labels.mountpoint }} 磁盘使用率为 {{ $value | humanizePercentage }}，超过95%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/disk-space-critical"

  # 🔐 认证服务告警
  - name: authentication-service
    interval: 30s
    rules:
      # 登录失败率激增
      - alert: LoginFailureSpike
        expr: |
          rate(paas_login_attempts_total{result="failure"}[5m]) > 10
        for: 5m
        labels:
          severity: warning
          category: security
          service: user-service
        annotations:
          summary: "登录失败率激增"
          description: "过去5分钟内登录失败率为 {{ $value | humanize }} 次/秒，超过10次/秒阈值，可能存在暴力破解攻击"
          runbook_url: "https://wiki.paas.com/runbooks/login-failure-spike"

      # 活跃会话数异常
      - alert: ActiveSessionsHigh
        expr: paas_active_sessions > 1000
        for: 10m
        labels:
          severity: warning
          category: capacity
          service: user-service
        annotations:
          summary: "活跃会话数过高"
          description: "当前活跃会话数为 {{ $value }}，超过1000个阈值"
          runbook_url: "https://wiki.paas.com/runbooks/active-sessions-high"

  # 📊 数据库告警
  - name: database
    interval: 30s
    rules:
      # 数据库连接数过高
      - alert: DatabaseConnectionsHigh
        expr: paas_database_connections > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "数据库连接数过高"
          description: "当前数据库连接数为 {{ $value }}，超过80个阈值"
          runbook_url: "https://wiki.paas.com/runbooks/database-connections-high"

      # 数据库查询时间过长
      - alert: DatabaseSlowQueries
        expr: |
          histogram_quantile(0.95,
            rate(paas_database_query_duration_seconds_bucket[5m])
          ) > 1
        for: 10m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "数据库查询时间过长"
          description: "数据库P95查询时间为 {{ $value | humanizeDuration }}，超过1秒阈值"
          runbook_url: "https://wiki.paas.com/runbooks/database-slow-queries"

  # 🔴 Redis 缓存告警
  - name: redis-cache
    interval: 30s
    rules:
      # Redis 连接数过高
      - alert: RedisConnectionsHigh
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          category: cache
        annotations:
          summary: "Redis 连接数过高"
          description: "Redis 当前连接数为 {{ $value }}，超过100个阈值"
          runbook_url: "https://wiki.paas.com/runbooks/redis-connections-high"

      # Redis 内存使用率过高
      - alert: RedisMemoryHigh
        expr: |
          (redis_memory_used_bytes / redis_memory_max_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: cache
        annotations:
          summary: "Redis 内存使用率过高"
          description: "Redis 内存使用率为 {{ $value | humanizePercentage }}，超过85%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/redis-memory-high"

      # 缓存命中率过低
      - alert: CacheHitRateLow
        expr: paas_cache_hit_rate < 0.8
        for: 10m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "缓存命中率过低"
          description: "{{ $labels.cache_type }} 缓存命中率为 {{ $value | humanizePercentage }}，低于80%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/cache-hit-rate-low"

  # 📱 应用管理服务告警
  - name: application-management
    interval: 30s
    rules:
      # 应用部署失败率过高
      - alert: DeploymentFailureRateHigh
        expr: |
          (
            rate(paas_application_deployments_total{status="failed"}[10m]) /
            rate(paas_application_deployments_total[10m])
          ) * 100 > 20
        for: 10m
        labels:
          severity: warning
          category: deployment
          service: app-manager
        annotations:
          summary: "应用部署失败率过高"
          description: "过去10分钟内应用部署失败率为 {{ $value | humanizePercentage }}，超过20%阈值"
          runbook_url: "https://wiki.paas.com/runbooks/deployment-failure-rate-high"

  # 🔧 CI/CD 流水线告警
  - name: cicd-pipeline
    interval: 30s
    rules:
      # 流水线执行时间过长
      - alert: PipelineExecutionTimeLong
        expr: |
          histogram_quantile(0.95,
            rate(paas_pipeline_execution_duration_seconds_bucket[10m])
          ) > 1800  # 30分钟
        for: 5m
        labels:
          severity: warning
          category: cicd
          service: cicd-service
        annotations:
          summary: "CI/CD 流水线执行时间过长"
          description: "流水线P95执行时间为 {{ $value | humanizeDuration }}，超过30分钟阈值"
          runbook_url: "https://wiki.paas.com/runbooks/pipeline-execution-time-long"

  # 🌐 API 网关告警
  - name: api-gateway
    interval: 30s
    rules:
      # API 网关请求量激增
      - alert: APIGatewayRequestSpike
        expr: |
          rate(paas_http_requests_total{service="api-gateway"}[5m]) > 1000
        for: 5m
        labels:
          severity: warning
          category: traffic
          service: api-gateway
        annotations:
          summary: "API 网关请求量激增"
          description: "API 网关请求量为 {{ $value | humanize }} 请求/秒，超过1000请求/秒阈值"
          runbook_url: "https://wiki.paas.com/runbooks/api-gateway-request-spike"

---
# 📝 告警规则说明
# 
# 本文件定义了 PaaS 平台的完整告警规则：
# 
# 1. 🚨 系统可用性：服务下线、错误率、响应时间
# 2. 🔧 系统资源：内存、CPU、磁盘使用率
# 3. 🔐 认证服务：登录失败、会话管理
# 4. 📊 数据库：连接数、查询性能
# 5. 🔴 缓存：Redis 连接、内存、命中率
# 6. 📱 应用管理：部署失败率
# 7. 🔧 CI/CD：流水线执行时间
# 8. 🌐 API 网关：请求量监控
# 
# 告警级别：
# - critical: 严重告警，需要立即处理
# - warning: 警告告警，需要关注和处理
# 
# 每个告警都包含：
# - 触发条件和持续时间
# - 严重级别和分类标签
# - 详细描述和处理建议
# - 相关文档和仪表板链接
