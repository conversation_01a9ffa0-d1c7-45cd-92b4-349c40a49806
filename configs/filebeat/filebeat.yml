# PaaS 平台 Filebeat 配置文件
# 收集和转发日志文件到 Logstash

# 文件输入配置
filebeat.inputs:
  # PaaS 应用日志
  - type: log
    enabled: true
    paths:
      - /var/log/paas/user-service/*.log
      - /var/log/paas/app-manager/*.log
      - /var/log/paas/api-gateway/*.log
      - /var/log/paas/cicd-service/*.log
      - /var/log/paas/config-service/*.log
      - /var/log/paas/script-service/*.log
    
    # 字段配置
    fields:
      log_type: application
      environment: development
      platform: paas
    fields_under_root: true
    
    # 多行日志配置 (处理堆栈跟踪)
    multiline.pattern: '^[0-9]{4}-[0-9]{2}-[0-9]{2}'
    multiline.negate: true
    multiline.match: after
    multiline.max_lines: 500
    multiline.timeout: 5s
    
    # 排除配置
    exclude_lines: ['^DEBUG', '^TRACE']
    exclude_files: ['\.gz$', '\.zip$']
    
    # 解析器配置
    parsers:
      - ndjson:
          keys_under_root: true
          add_error_key: true
          message_key: message

  # Docker 容器日志
  - type: container
    enabled: true
    paths:
      - '/var/lib/docker/containers/*/*.log'
    
    # 容器标签过滤
    containers.ids:
      - '*'
    
    # 字段配置
    fields:
      log_type: container
      environment: development
    fields_under_root: true
    
    # 处理器配置
    processors:
      - add_docker_metadata:
          host: "unix:///var/run/docker.sock"
      - decode_json_fields:
          fields: ["message"]
          target: ""
          overwrite_keys: true

  # 系统日志
  - type: syslog
    enabled: true
    protocol.udp:
      host: "0.0.0.0:514"
    
    fields:
      log_type: system
      environment: development
    fields_under_root: true

  # Nginx 访问日志 (如果有)
  - type: log
    enabled: false
    paths:
      - /var/log/nginx/access.log
    
    fields:
      log_type: access
      service: nginx
    fields_under_root: true

# 全局处理器
processors:
  # 添加主机信息
  - add_host_metadata:
      when.not.contains.tags: forwarded
      
  # 添加 Docker 元数据
  - add_docker_metadata: ~
  
  # 添加 Kubernetes 元数据 (如果在 K8s 环境)
  - add_kubernetes_metadata:
      host: ${NODE_NAME}
      matchers:
        - logs_path:
            logs_path: "/var/log/containers/"
  
  # 删除不需要的字段
  - drop_fields:
      fields: ["agent", "ecs", "host.architecture", "host.os.family"]
  
  # 重命名字段
  - rename:
      fields:
        - from: "host.name"
          to: "hostname"

# 输出配置
output.logstash:
  hosts: ["logstash:5044"]
  
  # 负载均衡
  loadbalance: true
  
  # 压缩
  compression_level: 3
  
  # 重试配置
  max_retries: 3
  backoff.init: 1s
  backoff.max: 60s
  
  # 批量发送
  bulk_max_size: 2048
  
  # 超时配置
  timeout: 30s

# 可选：直接输出到 Elasticsearch
# output.elasticsearch:
#   hosts: ["elasticsearch:9200"]
#   index: "paas-logs-%{+yyyy.MM.dd}"
#   template.name: "paas-logs"
#   template.pattern: "paas-logs-*"

# 日志配置
logging.level: info
logging.to_files: true
logging.files:
  path: /usr/share/filebeat/logs
  name: filebeat
  keepfiles: 7
  permissions: 0644

# 监控配置
monitoring.enabled: true
monitoring.elasticsearch:
  hosts: ["elasticsearch:9200"]

# HTTP 端点配置
http.enabled: true
http.host: 0.0.0.0
http.port: 5066

# 队列配置
queue.mem:
  events: 4096
  flush.min_events: 512
  flush.timeout: 1s

# 性能调优
filebeat.registry.flush: 1s
filebeat.shutdown_timeout: 5s

# 模块配置
filebeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: true
  reload.period: 10s

# 自动发现配置 (Docker)
filebeat.autodiscover:
  providers:
    - type: docker
      hints.enabled: true
      hints.default_config:
        type: container
        paths:
          - /var/lib/docker/containers/${data.docker.container.id}/*.log
      
      # 模板配置
      templates:
        - condition:
            contains:
              docker.container.image: "paas"
          config:
            - type: container
              paths:
                - /var/lib/docker/containers/${data.docker.container.id}/*.log
              fields:
                log_type: application
                service: ${data.docker.container.labels.service}
              fields_under_root: true

# X-Pack 配置
xpack.monitoring.enabled: true
xpack.monitoring.elasticsearch:
  hosts: ["elasticsearch:9200"]
