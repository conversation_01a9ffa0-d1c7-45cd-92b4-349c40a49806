# 配置服务配置文件

# 服务端口
port: 8084

# 数据库配置
database:
  type: "sqlite"                    # postgres, sqlite
  filepath: "./data/config-service.db"
  
  # PostgreSQL 配置（生产环境）
  # type: "postgres"
  # host: "localhost"
  # port: 5432
  # user: "paas_user"
  # password: "paas_password"
  # dbname: "paas_config"
  # sslmode: "disable"

# Redis 配置
redis:
  addr: "localhost:6379"
  password: ""
  db: 0

# 加密配置
encryption:
  key: "paas-config-encryption-key-32-chars"  # 32字符的加密密钥

# 日志配置
log:
  level: "info"                     # debug, info, warn, error
  format: "json"                    # json, text

# 服务配置
service:
  # 缓存配置
  cache:
    enabled: true
    ttl: "1h"                       # 缓存过期时间
    
  # 版本管理
  version:
    limit: 100                      # 保留的版本数量
    
  # 审计配置
  audit:
    enabled: true
    retention: "90d"                # 审计日志保留时间
    
  # 验证配置
  validation:
    enabled: true
    strict: false                   # 严格模式
    
  # 热更新配置
  hot_reload:
    enabled: true
    interval: "30s"                 # 检查间隔
    
  # 权限配置
  permissions:
    enabled: true
    default_role: "viewer"          # 默认角色
    
  # 分发配置
  distribution:
    enabled: true
    timeout: "30s"                  # 分发超时时间
    retry_count: 3                  # 重试次数
    
  # WebSocket 配置
  websocket:
    enabled: true
    ping_interval: "54s"            # Ping 间隔
    pong_timeout: "60s"             # Pong 超时
    buffer_size: 256                # 发送缓冲区大小
    
  # 模板配置
  template:
    enabled: true
    cache_size: 100                 # 模板缓存大小
    
  # 密钥轮换配置
  secret_rotation:
    enabled: true
    default_interval: "30d"         # 默认轮换间隔
    notify_before: "7d"             # 提前通知时间
    
  # 配置同步
  sync:
    enabled: false
    mode: "master_slave"            # master_slave, peer_to_peer
    interval: "5m"                  # 同步间隔
    
  # 监控配置
  monitoring:
    enabled: true
    metrics_path: "/metrics"        # 指标路径
    health_path: "/health"          # 健康检查路径
    
  # 限流配置
  rate_limit:
    enabled: true
    requests_per_minute: 1000       # 每分钟请求数
    burst: 100                      # 突发请求数
    
  # 安全配置
  security:
    cors:
      enabled: true
      allowed_origins: ["*"]
      allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
      allowed_headers: ["*"]
    
    # 🔒 JWT认证配置 (可通过环境变量覆盖)
    jwt:
      enabled: true                   # 是否启用 JWT 校验 (环境变量: PAAS_AUTH_ENABLED)
      secret: "config-service-jwt-secret" # JWT密钥 (环境变量: PAAS_JWT_SECRET)
      expiry: "24h"                   # Token过期时间

      # 🔧 开发模式配置
      dev_mode: false                 # 开发模式 (环境变量: PAAS_DEV_MODE)
      dev_token: "config-dev-token"   # 开发令牌 (环境变量: PAAS_DEV_TOKEN)

      # 🧑‍💻 开发模式用户信息
      dev_user:
        id: "config-dev-user-001"     # 环境变量: PAAS_DEV_USER_ID
        tenant_id: "dev-tenant-001"   # 环境变量: PAAS_DEV_USER_TENANT_ID
        username: "配置管理员"         # 环境变量: PAAS_DEV_USER_USERNAME
        email: "config-dev@localhost" # 环境变量: PAAS_DEV_USER_EMAIL
        roles: ["admin", "config_manager"] # 环境变量: PAAS_DEV_USER_ROLES

      # 🛤️ 跳过认证的路径
      skip_paths:
        - "/health"
        - "/ready"
        - "/metrics"
        - "/swagger"
        - "/swagger/*"
        - "/ws"                       # WebSocket连接
    
    # API 密钥配置
    api_key:
      enabled: false
      header: "X-API-Key"
    
    # TLS 配置
    tls:
      enabled: false
      cert_file: ""
      key_file: ""

# 环境特定配置
environments:
  development:
    log:
      level: "debug"
    database:
      type: "sqlite"
      filepath: "./data/config-service-dev.db"
    
  testing:
    log:
      level: "debug"
    database:
      type: "sqlite"
      filepath: ":memory:"
    
  production:
    log:
      level: "warn"
    database:
      type: "postgres"
      host: "${DB_HOST}"
      port: 5432
      user: "${DB_USER}"
      password: "${DB_PASSWORD}"
      dbname: "${DB_NAME}"
      sslmode: "require"
    redis:
      addr: "${REDIS_ADDR}"
      password: "${REDIS_PASSWORD}"
    encryption:
      key: "${ENCRYPTION_KEY}"
    security:
      tls:
        enabled: true
        cert_file: "/etc/ssl/certs/config-service.crt"
        key_file: "/etc/ssl/private/config-service.key"

# 功能开关
features:
  graphql_api: false                # GraphQL API
  grpc_api: false                   # gRPC API
  config_templates: true            # 配置模板
  feature_flags: true               # 功能开关
  config_sets: true                 # 配置集
  secret_management: true           # 密钥管理
  audit_logging: true               # 审计日志
  config_validation: true           # 配置验证
  hot_reload: true                  # 热重载
  websocket_notifications: true     # WebSocket 通知
  config_distribution: true         # 配置分发
  multi_environment: true           # 多环境支持
  config_inheritance: true          # 配置继承
  config_encryption: true           # 配置加密
  config_versioning: true           # 配置版本管理

# 集成配置
integrations:
  # 用户服务集成
  user_service:
    enabled: true
    endpoint: "http://localhost:8083"
    timeout: "10s"
    
  # 应用管理服务集成
  app_manager:
    enabled: true
    endpoint: "http://localhost:8081"
    timeout: "10s"
    
  # 监控服务集成
  monitor_service:
    enabled: true
    endpoint: "http://localhost:8085"
    timeout: "10s"
    
  # 外部配置中心集成
  external_config:
    enabled: false
    type: "consul"                  # consul, etcd, vault
    endpoint: "http://localhost:8500"
    
  # 消息队列集成
  message_queue:
    enabled: false
    type: "rabbitmq"                # rabbitmq, kafka, nats
    endpoint: "amqp://localhost:5672"

# 性能配置
performance:
  # 数据库连接池
  db_pool:
    max_open_conns: 25
    max_idle_conns: 5
    conn_max_lifetime: "1h"
    
  # Redis 连接池
  redis_pool:
    max_active: 100
    max_idle: 10
    idle_timeout: "5m"
    
  # HTTP 服务器
  http_server:
    read_timeout: "30s"
    write_timeout: "30s"
    idle_timeout: "120s"
    max_header_bytes: 1048576       # 1MB
    
  # 并发控制
  concurrency:
    max_workers: 100
    queue_size: 1000

# 开发配置
development:
  # 调试模式
  debug: true
  
  # 性能分析
  pprof:
    enabled: true
    port: 6060
    
  # 热重载
  hot_reload:
    enabled: true
    watch_paths: ["./internal/config", "./configs"]
    
  # 测试数据
  test_data:
    enabled: true
    load_on_startup: true
    
  # API 文档
  swagger:
    enabled: true
    path: "/swagger"
    
  # 开发工具
  dev_tools:
    enabled: true
    config_editor: true
    log_viewer: true
