# 应用管理服务配置文件

# 服务配置
server:
  port: 8082                    # 服务端口
  mode: debug                   # 运行模式: debug, release, test
  read_timeout: 30s             # 读取超时
  write_timeout: 30s            # 写入超时
  idle_timeout: 60s             # 空闲超时

# 数据库配置
database:
  driver: sqlite                # 数据库驱动: sqlite, postgres
  dsn: "./data/app-manager.db"  # 数据源名称
  max_open_conns: 100           # 最大打开连接数
  max_idle_conns: 10            # 最大空闲连接数
  conn_max_lifetime: 3600       # 连接最大生存时间(秒)
  log_level: warn               # 数据库日志级别: silent, error, warn, info

# PostgreSQL 配置示例 (生产环境)
# database:
#   driver: postgres
#   dsn: "host=localhost user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai"
#   max_open_conns: 100
#   max_idle_conns: 10
#   conn_max_lifetime: 3600
#   log_level: error

# 日志配置
log:
  level: info                   # 日志级别: debug, info, warn, error, fatal
  format: json                  # 日志格式: json, text
  output: stdout                # 输出目标: stdout, stderr, file

# Redis 配置 (用于缓存和消息队列)
# 支持条件启用：当 Redis 不可用时自动降级到内存缓存
redis:
  enabled: true                 # 是否启用 Redis 缓存 (false 时使用内存缓存)
  addr: "localhost:6379"        # Redis 地址
  password: ""                  # Redis 密码
  db: 0                         # Redis 数据库编号
  pool_size: 10                 # 连接池大小
  min_idle_conns: 5             # 最小空闲连接数
  dial_timeout: 5s              # 连接超时
  read_timeout: 3s              # 读取超时
  write_timeout: 3s             # 写入超时

# Docker 配置
docker:
  host: "unix:///var/run/docker.sock"  # Docker 守护进程地址
  api_version: "1.41"                   # Docker API 版本
  registry:                             # 镜像仓库配置
    url: "localhost:5000"               # 仓库地址
    username: ""                        # 用户名
    password: ""                        # 密码
    insecure: true                      # 是否允许不安全连接

# 应用配置
app:
  default_instances: 1          # 默认实例数量
  max_instances: 100            # 最大实例数量
  health_check_timeout: 30s     # 健康检查超时
  deployment_timeout: 300s      # 部署超时
  build_timeout: 1800s          # 构建超时
  
  # 支持的语言和版本
  supported_languages:
    nodejs:
      versions: ["16", "18", "20"]
      default_version: "18"
      frameworks: ["express", "koa", "fastify", "nest"]
    python:
      versions: ["3.8", "3.9", "3.10", "3.11"]
      default_version: "3.11"
      frameworks: ["flask", "django", "fastapi", "tornado"]

# 存储配置
storage:
  type: local                   # 存储类型: local, s3, oss
  local:
    base_path: "./data/storage" # 本地存储基础路径
    max_size: "10Gi"            # 最大存储大小
  
  # S3 配置示例
  # s3:
  #   endpoint: "s3.amazonaws.com"
  #   region: "us-east-1"
  #   bucket: "paas-storage"
  #   access_key: ""
  #   secret_key: ""

# 监控配置
monitoring:
  enabled: true                 # 是否启用监控
  metrics_path: "/metrics"      # 指标路径
  health_path: "/health"        # 健康检查路径
  ready_path: "/ready"          # 就绪检查路径
  
  # Prometheus 配置
  prometheus:
    enabled: true
    namespace: "paas"           # 指标命名空间
    subsystem: "app_manager"    # 指标子系统

# 安全配置
security:
  jwt:
    # 🔒 JWT认证开关 (可通过环境变量 PAAS_AUTH_ENABLED 覆盖)
    enabled: true                     # 是否启用 JWT 校验 (false 时禁用认证)
    secret: "your-jwt-secret-key"     # JWT 密钥 (可通过环境变量 PAAS_JWT_SECRET 覆盖)
    expires_in: 24h                   # Token 过期时间
    refresh_expires_in: 168h          # 刷新 Token 过期时间

    # 🔧 开发模式配置 (可通过环境变量 PAAS_DEV_MODE 覆盖)
    dev_mode: false                   # 开发模式 (true 时使用默认用户)
    dev_token: "dev-token"            # 开发模式令牌 (可通过环境变量 PAAS_DEV_TOKEN 覆盖)

    # 🧑‍💻 开发模式用户信息 (可通过环境变量覆盖)
    dev_user:
      id: "dev-user-123"              # 环境变量: PAAS_DEV_USER_ID
      tenant_id: "dev-tenant-123"     # 环境变量: PAAS_DEV_USER_TENANT_ID
      username: "开发用户"             # 环境变量: PAAS_DEV_USER_USERNAME
      email: "<EMAIL>"        # 环境变量: PAAS_DEV_USER_EMAIL
      roles: ["admin"]                # 环境变量: PAAS_DEV_USER_ROLES (逗号分隔)

    # 🛤️ 跳过认证的路径
    skip_paths:
      - "/health"                     # 健康检查
      - "/ready"                      # 就绪检查
      - "/metrics"                    # 监控指标
      - "/api/v1/auth/login"          # 登录接口
      - "/api/v1/auth/register"       # 注册接口
      - "/api/v1/auth/refresh"        # 刷新令牌
      - "/swagger"                    # API文档
      - "/swagger/*"                  # API文档资源

  cors:
    allowed_origins: ["*"]            # 允许的源
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Origin", "Content-Type", "Authorization"]
    max_age: 86400                    # 预检请求缓存时间

# 限流配置
rate_limit:
  enabled: true                 # 是否启用限流
  requests_per_minute: 1000     # 每分钟请求数限制
  burst: 100                    # 突发请求数

# 外部服务配置
external_services:
  # Gitea 配置
  gitea:
    url: "http://localhost:3000"      # Gitea 服务地址
    admin_token: ""                   # 管理员 Token
    webhook_secret: "webhook-secret"  # Webhook 密钥
  
  # CI/CD 服务配置
  cicd:
    url: "http://localhost:8082"      # CI/CD 服务地址
    timeout: 30s                      # 请求超时
  
  # 用户服务配置
  user_service:
    url: "http://localhost:8083"      # 用户服务地址
    timeout: 10s                      # 请求超时
  
  # 配置服务配置
  config_service:
    url: "http://localhost:8084"      # 配置服务地址
    timeout: 10s                      # 请求超时
  
  # 监控服务配置
  monitor_service:
    url: "http://localhost:8085"      # 监控服务地址
    timeout: 10s                      # 请求超时
