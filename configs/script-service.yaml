# 脚本执行服务配置文件

# 服务器配置
server:
  port: 8084                          # 服务端口
  mode: debug                         # 运行模式: debug, release, test
  read_timeout: 60s                   # 读取超时
  write_timeout: 60s                  # 写入超时
  idle_timeout: 120s                  # 空闲超时

# 数据库配置
database:
  driver: sqlite                      # 数据库驱动: sqlite, postgres, mysql
  dsn: "./data/script-service.db"     # 数据库连接字符串
  max_open_conns: 100                 # 最大打开连接数
  max_idle_conns: 10                  # 最大空闲连接数
  conn_max_lifetime: 1h               # 连接最大生命周期
  
  # PostgreSQL 配置示例
  # driver: postgres
  # dsn: "host=localhost user=paas password=paas dbname=script_service port=5432 sslmode=disable TimeZone=Asia/Shanghai"
  
  # MySQL 配置示例
  # driver: mysql
  # dsn: "paas:paas@tcp(localhost:3306)/script_service?charset=utf8mb4&parseTime=True&loc=Local"

# 日志配置
log:
  level: info                         # 日志级别: debug, info, warn, error
  format: json                        # 日志格式: json, text
  output: stdout                      # 输出目标: stdout, stderr, file
  file_path: "./logs/script-service.log"  # 日志文件路径
  max_size: 100                       # 日志文件最大大小(MB)
  max_backups: 5                      # 保留的日志文件数量
  max_age: 30                         # 日志文件保留天数
  compress: true                      # 是否压缩旧日志文件

# 脚本执行配置
script:
  # 基础配置
  default_timeout: 300                # 默认超时时间(秒)
  max_concurrent_tasks: 10            # 最大并发任务数
  task_retention_days: 30             # 任务保留天数
  callback_timeout: 30                # 回调超时时间(秒)
  
  # 存储配置
  workspace_base_path: "./data/workspaces"  # 工作空间基础路径
  artifact_base_path: "./data/artifacts"    # 产物存储基础路径
  max_artifact_size: 100MB            # 单个产物最大大小
  
  # 运行时配置
  default_python_image: "python:3.11-alpine"  # 默认Python镜像
  default_nodejs_image: "node:18-alpine"      # 默认Node.js镜像
  default_go_image: "golang:1.21-alpine"      # 默认Go镜像
  
  # 资源限制
  default_cpu_limit: "500m"           # 默认CPU限制
  default_memory_limit: "512Mi"       # 默认内存限制
  default_disk_limit: "1Gi"           # 默认磁盘限制
  
  # 网络配置
  network_mode: "bridge"              # 网络模式
  dns_servers:                        # DNS服务器
    - "*******"
    - "*******"

# Docker 配置
docker:
  # 远程Docker主机配置
  host: "tcp://*************:2376"   # 远程Docker守护进程地址
  api_version: "1.41"                 # Docker API版本
  timeout: 30s                        # 连接超时时间

  # TLS安全连接配置
  tls:
    enabled: true                     # 是否启用TLS加密
    verify: true                      # 是否验证服务器证书
    cert_path: "/etc/docker/certs"    # 证书文件路径
    ca_file: "ca.pem"                 # CA证书文件名
    cert_file: "cert.pem"             # 客户端证书文件名
    key_file: "key.pem"               # 客户端私钥文件名
    insecure_skip_verify: false       # 是否跳过证书验证（不推荐）

  # 连接池配置
  connection:
    max_idle_conns: 10                # 最大空闲连接数
    max_conns_per_host: 20            # 每个主机最大连接数
    idle_conn_timeout: 90s            # 空闲连接超时
    keep_alive: 30s                   # 保持连接时间

  # 镜像仓库配置
  registry:
    default_url: "docker.io"          # 默认镜像仓库
    username: ""                      # 镜像仓库用户名
    password: ""                      # 镜像仓库密码
    insecure: false                   # 是否允许不安全连接

  # 构建配置
  build:
    context_size_limit: 100MB         # 构建上下文大小限制
    build_timeout: 1800s              # 构建超时时间
    no_cache: false                   # 是否禁用缓存
    pull_always: false                # 是否总是拉取基础镜像

  # 备用配置（本地Docker）
  fallback:
    enabled: true                     # 是否启用备用连接
    host: "unix:///var/run/docker.sock"  # 本地Docker socket

# 调度配置
scheduler:
  enabled: true                       # 是否启用调度功能
  check_interval: 60s                 # 调度检查间隔
  max_missed_runs: 3                  # 最大错过运行次数
  timezone: "Asia/Shanghai"           # 默认时区
  
  # 调度器类型配置
  type: "cron"                        # 调度器类型: cron, simple
  
  # Cron调度器配置
  cron:
    location: "Asia/Shanghai"         # 时区位置
    
  # 简单调度器配置
  simple:
    min_interval: 60s                 # 最小间隔时间

# 监控配置
monitoring:
  enabled: true                       # 是否启用监控
  metrics_path: "/metrics"            # 指标路径
  
  # Prometheus配置
  prometheus:
    enabled: true                     # 是否启用Prometheus指标
    namespace: "paas_script"          # 指标命名空间
    
  # 健康检查配置
  health:
    check_interval: 30s               # 健康检查间隔
    timeout: 10s                      # 健康检查超时

# 安全配置
security:
  # 🔒 JWT认证配置 (可通过环境变量覆盖)
  jwt:
    enabled: true                     # 是否启用 JWT 校验 (环境变量: PAAS_AUTH_ENABLED)
    secret: "your-jwt-secret-key"     # JWT密钥 (环境变量: PAAS_JWT_SECRET)
    expires_in: 24h                   # JWT过期时间
    refresh_expires_in: 168h          # 刷新Token过期时间

    # 🔧 开发模式配置
    dev_mode: false                   # 开发模式 (环境变量: PAAS_DEV_MODE)
    dev_token: "script-dev-token"     # 开发令牌 (环境变量: PAAS_DEV_TOKEN)

    # 🧑‍💻 开发模式用户信息
    dev_user:
      id: "script-dev-user-001"       # 环境变量: PAAS_DEV_USER_ID
      tenant_id: "dev-tenant-001"     # 环境变量: PAAS_DEV_USER_TENANT_ID
      username: "脚本开发者"           # 环境变量: PAAS_DEV_USER_USERNAME
      email: "script-dev@localhost"   # 环境变量: PAAS_DEV_USER_EMAIL
      roles: ["admin", "script_executor"] # 环境变量: PAAS_DEV_USER_ROLES

    # 🛤️ 跳过认证的路径
    skip_paths:
      - "/health"
      - "/ready"
      - "/metrics"
      - "/swagger"
      - "/swagger/*"

  # 权限配置
  rbac:
    enabled: true                     # 是否启用RBAC (开发环境可设为false)
    default_role: "user"              # 默认角色

  # 审计配置
  audit:
    enabled: true                     # 是否启用审计
    log_requests: true                # 是否记录请求
    log_responses: false              # 是否记录响应

# 缓存配置
cache:
  enabled: true                       # 是否启用缓存
  type: "memory"                      # 缓存类型: memory, redis
  ttl: 1h                            # 缓存过期时间
  
  # Redis配置
  redis:
    addr: "localhost:6379"            # Redis地址
    password: ""                      # Redis密码
    db: 0                            # Redis数据库
    pool_size: 10                    # 连接池大小

# 通知配置
notification:
  enabled: true                       # 是否启用通知
  
  # 邮件通知配置
  email:
    enabled: false                    # 是否启用邮件通知
    smtp_host: "smtp.example.com"     # SMTP服务器
    smtp_port: 587                    # SMTP端口
    username: ""                      # 用户名
    password: ""                      # 密码
    from: "<EMAIL>"       # 发件人
    
  # Webhook通知配置
  webhook:
    enabled: true                     # 是否启用Webhook通知
    timeout: 30s                      # 请求超时
    retry_count: 3                    # 重试次数
    retry_interval: 5s                # 重试间隔

# 集成配置
integrations:
  # 用户服务集成
  user_service:
    enabled: true                     # 是否启用用户服务集成
    endpoint: "http://localhost:8083" # 用户服务端点
    timeout: 10s                      # 请求超时
    
  # 应用管理服务集成
  app_manager:
    enabled: true                     # 是否启用应用管理服务集成
    endpoint: "http://localhost:8081" # 应用管理服务端点
    timeout: 10s                      # 请求超时
    
  # 配置服务集成
  config_service:
    enabled: true                     # 是否启用配置服务集成
    endpoint: "http://localhost:8082" # 配置服务端点
    timeout: 10s                      # 请求超时

# 开发配置
development:
  debug: true                         # 是否启用调试模式
  hot_reload: true                    # 是否启用热重载
  mock_external_services: false       # 是否模拟外部服务
  
  # 测试配置
  test:
    database_url: ":memory:"          # 测试数据库URL
    cleanup_after_test: true          # 测试后是否清理数据
