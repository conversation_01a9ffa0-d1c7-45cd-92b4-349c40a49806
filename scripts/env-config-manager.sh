#!/bin/bash

# 环境配置管理脚本
# 管理开发、测试、生产环境的配置和部署流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/config"
ENVIRONMENTS=("development" "staging" "production")

echo -e "${PURPLE}🔧 PaaS 平台环境配置管理器${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：验证环境名称
validate_environment() {
    local env=$1
    for valid_env in "${ENVIRONMENTS[@]}"; do
        if [[ "$env" == "$valid_env" ]]; then
            return 0
        fi
    done
    return 1
}

# 函数：创建环境配置目录结构
create_config_structure() {
    print_title "创建配置目录结构"
    
    for env in "${ENVIRONMENTS[@]}"; do
        local env_dir="$CONFIG_DIR/$env"
        mkdir -p "$env_dir"/{secrets,configmaps,values}
        
        print_info "创建 $env 环境配置目录"
    done
    
    # 创建共享配置目录
    mkdir -p "$CONFIG_DIR/shared"/{secrets,configmaps,values}
    
    print_success "配置目录结构创建完成"
}

# 函数：生成环境配置文件
generate_env_config() {
    local environment=$1
    
    if ! validate_environment "$environment"; then
        print_error "无效的环境名称: $environment"
        return 1
    fi
    
    print_title "生成 $environment 环境配置"
    
    local env_dir="$CONFIG_DIR/$environment"
    
    # 生成应用配置
    cat > "$env_dir/values/app-config.yaml" << EOF
# $environment 环境应用配置
environment: $environment

# 镜像配置
image:
  registry: registry.example.com
  namespace: paas
  tag: latest
  pullPolicy: Always

# 副本配置
replicaCount:
  apiGateway: $([ "$environment" = "production" ] && echo "3" || echo "1")
  userService: $([ "$environment" = "production" ] && echo "2" || echo "1")
  appManager: $([ "$environment" = "production" ] && echo "2" || echo "1")
  cicdService: 1
  monitoringService: 1
  notificationService: 1
  loadBalancer: $([ "$environment" = "production" ] && echo "2" || echo "1")
  web: $([ "$environment" = "production" ] && echo "2" || echo "1")

# 资源配置
resources:
  apiGateway:
    requests:
      memory: "256Mi"
      cpu: "250m"
    limits:
      memory: $([ "$environment" = "production" ] && echo "512Mi" || echo "256Mi")
      cpu: $([ "$environment" = "production" ] && echo "500m" || echo "250m")
  
  userService:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: $([ "$environment" = "production" ] && echo "256Mi" || echo "128Mi")
      cpu: $([ "$environment" = "production" ] && echo "200m" || echo "100m")

# 数据库配置
database:
  host: postgres-$(echo $environment | tr '[:upper:]' '[:lower:]')
  port: 5432
  name: paas_$(echo $environment | tr '[:upper:]' '[:lower:]')
  ssl: $([ "$environment" = "production" ] && echo "require" || echo "disable")

# Redis 配置
redis:
  host: redis-$(echo $environment | tr '[:upper:]' '[:lower:]')
  port: 6379
  database: 0

# 日志配置
logging:
  level: $([ "$environment" = "production" ] && echo "info" || echo "debug")
  format: json

# 监控配置
monitoring:
  enabled: true
  prometheus:
    scrape: true
  grafana:
    enabled: $([ "$environment" = "production" ] && echo "true" || echo "false")

# 安全配置
security:
  tls:
    enabled: $([ "$environment" = "production" ] && echo "true" || echo "false")
  networkPolicies:
    enabled: $([ "$environment" = "production" ] && echo "true" || echo "false")

# Ingress 配置
ingress:
  enabled: true
  className: nginx
  hosts:
    - host: $([ "$environment" = "production" ] && echo "paas.example.com" || echo "$environment.paas.example.com")
      paths:
        - path: /
          pathType: Prefix
  tls:
    enabled: $([ "$environment" = "production" ] && echo "true" || echo "false")
    secretName: paas-tls-cert

# 自动扩缩配置
autoscaling:
  enabled: $([ "$environment" = "production" ] && echo "true" || echo "false")
  minReplicas: $([ "$environment" = "production" ] && echo "2" || echo "1")
  maxReplicas: $([ "$environment" = "production" ] && echo "10" || echo "3")
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
EOF

    # 生成数据库配置
    cat > "$env_dir/configmaps/database-config.yaml" << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: database-config
  namespace: paas-$environment
data:
  host: postgres-$environment
  port: "5432"
  name: paas_$environment
  ssl_mode: $([ "$environment" = "production" ] && echo "require" || echo "disable")
  max_connections: $([ "$environment" = "production" ] && echo "100" || echo "20")
  connection_timeout: "30"
EOF

    # 生成 Redis 配置
    cat > "$env_dir/configmaps/redis-config.yaml" << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: paas-$environment
data:
  host: redis-$environment
  port: "6379"
  database: "0"
  max_connections: $([ "$environment" = "production" ] && echo "100" || echo "20")
  timeout: "5"
EOF

    # 生成应用配置 ConfigMap
    cat > "$env_dir/configmaps/app-config.yaml" << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: paas-$environment
data:
  environment: $environment
  log_level: $([ "$environment" = "production" ] && echo "info" || echo "debug")
  log_format: json
  metrics_enabled: "true"
  tracing_enabled: $([ "$environment" = "production" ] && echo "true" || echo "false")
  cors_origins: $([ "$environment" = "production" ] && echo "https://paas.example.com" || echo "*")
  session_timeout: $([ "$environment" = "production" ] && echo "3600" || echo "7200")
  rate_limit_requests: $([ "$environment" = "production" ] && echo "1000" || echo "100")
  rate_limit_window: "60"
EOF

    # 生成密钥模板
    cat > "$env_dir/secrets/database-secret.yaml.template" << EOF
apiVersion: v1
kind: Secret
metadata:
  name: database-secret
  namespace: paas-$environment
type: Opaque
data:
  host: \$(echo -n "postgres-$environment" | base64)
  username: \$(echo -n "paas_user" | base64)
  password: \$(echo -n "CHANGE_ME_$(openssl rand -hex 16)" | base64)
  database: \$(echo -n "paas_$environment" | base64)
EOF

    cat > "$env_dir/secrets/redis-secret.yaml.template" << EOF
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: paas-$environment
type: Opaque
data:
  host: \$(echo -n "redis-$environment" | base64)
  password: \$(echo -n "CHANGE_ME_$(openssl rand -hex 16)" | base64)
EOF

    cat > "$env_dir/secrets/jwt-secret.yaml.template" << EOF
apiVersion: v1
kind: Secret
metadata:
  name: jwt-secret
  namespace: paas-$environment
type: Opaque
data:
  secret: \$(echo -n "$(openssl rand -hex 32)" | base64)
  issuer: \$(echo -n "paas-$environment" | base64)
  expiry: \$(echo -n "$([ "$environment" = "production" ] && echo "3600" || echo "7200")" | base64)
EOF

    print_success "$environment 环境配置文件生成完成"
}

# 函数：应用环境配置
apply_env_config() {
    local environment=$1
    
    if ! validate_environment "$environment"; then
        print_error "无效的环境名称: $environment"
        return 1
    fi
    
    print_title "应用 $environment 环境配置"
    
    local env_dir="$CONFIG_DIR/$environment"
    local namespace="paas-$environment"
    
    # 检查 kubectl 连接
    if ! kubectl cluster-info &> /dev/null; then
        print_error "无法连接到 Kubernetes 集群"
        return 1
    fi
    
    # 创建命名空间
    kubectl create namespace "$namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # 应用 ConfigMaps
    if [[ -d "$env_dir/configmaps" ]]; then
        print_info "应用 ConfigMaps..."
        for config_file in "$env_dir/configmaps"/*.yaml; do
            if [[ -f "$config_file" ]]; then
                kubectl apply -f "$config_file"
                print_info "已应用: $(basename "$config_file")"
            fi
        done
    fi
    
    # 应用 Secrets（需要先处理模板）
    if [[ -d "$env_dir/secrets" ]]; then
        print_info "处理并应用 Secrets..."
        for secret_template in "$env_dir/secrets"/*.yaml.template; do
            if [[ -f "$secret_template" ]]; then
                local secret_file="${secret_template%.template}"
                # 处理模板变量
                envsubst < "$secret_template" > "$secret_file"
                kubectl apply -f "$secret_file"
                print_info "已应用: $(basename "$secret_file")"
                # 删除临时文件
                rm -f "$secret_file"
            fi
        done
    fi
    
    print_success "$environment 环境配置应用完成"
}

# 函数：验证环境配置
validate_env_config() {
    local environment=$1
    
    if ! validate_environment "$environment"; then
        print_error "无效的环境名称: $environment"
        return 1
    fi
    
    print_title "验证 $environment 环境配置"
    
    local namespace="paas-$environment"
    local errors=0
    
    # 检查命名空间
    if ! kubectl get namespace "$namespace" &> /dev/null; then
        print_error "命名空间 $namespace 不存在"
        ((errors++))
    else
        print_success "命名空间 $namespace 存在"
    fi
    
    # 检查 ConfigMaps
    local required_configmaps=("database-config" "redis-config" "app-config")
    for cm in "${required_configmaps[@]}"; do
        if kubectl get configmap "$cm" -n "$namespace" &> /dev/null; then
            print_success "ConfigMap $cm 存在"
        else
            print_error "ConfigMap $cm 不存在"
            ((errors++))
        fi
    done
    
    # 检查 Secrets
    local required_secrets=("database-secret" "redis-secret" "jwt-secret")
    for secret in "${required_secrets[@]}"; do
        if kubectl get secret "$secret" -n "$namespace" &> /dev/null; then
            print_success "Secret $secret 存在"
        else
            print_error "Secret $secret 不存在"
            ((errors++))
        fi
    done
    
    if [[ $errors -eq 0 ]]; then
        print_success "$environment 环境配置验证通过"
        return 0
    else
        print_error "$environment 环境配置验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 函数：备份环境配置
backup_env_config() {
    local environment=$1
    
    if ! validate_environment "$environment"; then
        print_error "无效的环境名称: $environment"
        return 1
    fi
    
    print_title "备份 $environment 环境配置"
    
    local backup_dir="$PROJECT_ROOT/backups/config/$(date +%Y%m%d_%H%M%S)_$environment"
    mkdir -p "$backup_dir"
    
    local namespace="paas-$environment"
    
    # 备份 ConfigMaps
    kubectl get configmaps -n "$namespace" -o yaml > "$backup_dir/configmaps.yaml"
    
    # 备份 Secrets（不包含敏感数据）
    kubectl get secrets -n "$namespace" -o yaml | \
        sed 's/data:/data: # REDACTED/g' > "$backup_dir/secrets-structure.yaml"
    
    # 备份命名空间配置
    kubectl get namespace "$namespace" -o yaml > "$backup_dir/namespace.yaml"
    
    print_success "环境配置已备份到: $backup_dir"
}

# 函数：比较环境配置
compare_env_configs() {
    local env1=$1
    local env2=$2
    
    if ! validate_environment "$env1" || ! validate_environment "$env2"; then
        print_error "无效的环境名称"
        return 1
    fi
    
    print_title "比较 $env1 和 $env2 环境配置"
    
    local env1_dir="$CONFIG_DIR/$env1"
    local env2_dir="$CONFIG_DIR/$env2"
    
    # 比较配置文件
    if command -v diff &> /dev/null; then
        print_info "配置文件差异:"
        diff -r "$env1_dir" "$env2_dir" || true
    else
        print_warning "diff 命令不可用，无法比较配置"
    fi
}

# 函数：同步环境配置
sync_env_configs() {
    local source_env=$1
    local target_env=$2
    
    if ! validate_environment "$source_env" || ! validate_environment "$target_env"; then
        print_error "无效的环境名称"
        return 1
    fi
    
    print_title "从 $source_env 同步配置到 $target_env"
    
    local source_dir="$CONFIG_DIR/$source_env"
    local target_dir="$CONFIG_DIR/$target_env"
    
    # 备份目标环境配置
    backup_env_config "$target_env"
    
    # 复制配置文件
    cp -r "$source_dir"/* "$target_dir"/
    
    # 更新环境特定的值
    find "$target_dir" -name "*.yaml" -type f -exec sed -i "s/$source_env/$target_env/g" {} \;
    
    print_success "配置同步完成"
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台环境配置管理器"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  init                     初始化配置目录结构"
    echo "  generate <env>           生成指定环境的配置文件"
    echo "  apply <env>              应用指定环境的配置到 Kubernetes"
    echo "  validate <env>           验证指定环境的配置"
    echo "  backup <env>             备份指定环境的配置"
    echo "  compare <env1> <env2>    比较两个环境的配置"
    echo "  sync <source> <target>   从源环境同步配置到目标环境"
    echo "  list                     列出所有支持的环境"
    echo ""
    echo "支持的环境: ${ENVIRONMENTS[*]}"
    echo ""
    echo "示例:"
    echo "  $0 init                           # 初始化配置结构"
    echo "  $0 generate development           # 生成开发环境配置"
    echo "  $0 apply production               # 应用生产环境配置"
    echo "  $0 validate staging               # 验证预发布环境配置"
    echo "  $0 compare development production # 比较开发和生产环境配置"
}

# 主函数
main() {
    case "${1:-help}" in
        "init")
            create_config_structure
            ;;
        "generate")
            if [[ -z "$2" ]]; then
                print_error "请指定环境名称"
                show_help
                exit 1
            fi
            generate_env_config "$2"
            ;;
        "apply")
            if [[ -z "$2" ]]; then
                print_error "请指定环境名称"
                show_help
                exit 1
            fi
            apply_env_config "$2"
            ;;
        "validate")
            if [[ -z "$2" ]]; then
                print_error "请指定环境名称"
                show_help
                exit 1
            fi
            validate_env_config "$2"
            ;;
        "backup")
            if [[ -z "$2" ]]; then
                print_error "请指定环境名称"
                show_help
                exit 1
            fi
            backup_env_config "$2"
            ;;
        "compare")
            if [[ -z "$2" || -z "$3" ]]; then
                print_error "请指定两个环境名称"
                show_help
                exit 1
            fi
            compare_env_configs "$2" "$3"
            ;;
        "sync")
            if [[ -z "$2" || -z "$3" ]]; then
                print_error "请指定源环境和目标环境名称"
                show_help
                exit 1
            fi
            sync_env_configs "$2" "$3"
            ;;
        "list")
            print_info "支持的环境:"
            for env in "${ENVIRONMENTS[@]}"; do
                echo "  - $env"
            done
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
