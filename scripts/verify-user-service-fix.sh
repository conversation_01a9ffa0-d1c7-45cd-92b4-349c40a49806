#!/bin/bash

# User Service构建错误修复验证脚本
# 验证所有Handler方法已正确实现，服务能够成功构建和运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 显示修复说明
show_fix_info() {
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║              User Service修复验证                         ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  🎯 目标: 验证Handler方法实现完整性                       ║${NC}"
    echo -e "${CYAN}║  🔧 修复: 添加所有缺失的认证和用户管理方法                ║${NC}"
    echo -e "${CYAN}║  ✅ 结果: User Service能够成功构建和运行                  ║${NC}"
    echo -e "${CYAN}║  🚀 效果: 支持完整的认证架构                              ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 验证构建
verify_build() {
    log_test "验证User Service构建..."
    
    # 清理之前的构建
    rm -f bin/user-service
    
    # 构建User Service
    if go build -o bin/user-service ./cmd/user-service; then
        log_success "✅ User Service构建成功"
        
        # 检查二进制文件
        if [ -f "bin/user-service" ]; then
            local file_size=$(ls -lh bin/user-service | awk '{print $5}')
            log_info "二进制文件大小: $file_size"
        fi
        
        return 0
    else
        log_error "❌ User Service构建失败"
        return 1
    fi
}

# 验证Handler方法实现
verify_handler_methods() {
    log_test "验证Handler方法实现..."
    
    local handler_file="internal/auth/handler.go"
    local missing_methods=()
    
    # 检查关键方法是否存在
    local required_methods=(
        "ResetPassword"
        "ConfirmResetPassword"
        "UpdateUser"
        "DeleteUser"
        "GetUserRoles"
        "AssignRole"
        "UnassignRole"
        "GetUserPermissions"
        "GetUserSessions"
        "DeleteUserSessions"
        "CreateUser"
        "ListUsers"
        "GetUser"
        "ChangePassword"
        "CheckPermission"
    )
    
    for method in "${required_methods[@]}"; do
        if grep -q "func (h \*Handler) $method" "$handler_file"; then
            log_success "✅ $method 方法已实现"
        else
            log_error "❌ $method 方法缺失"
            missing_methods+=("$method")
        fi
    done
    
    if [ ${#missing_methods[@]} -eq 0 ]; then
        log_success "✅ 所有必需的Handler方法都已实现"
        return 0
    else
        log_error "❌ 发现 ${#missing_methods[@]} 个缺失的方法"
        return 1
    fi
}

# 验证类型定义
verify_type_definitions() {
    log_test "验证类型定义..."
    
    local dto_file="internal/auth/dto.go"
    local missing_types=()
    
    # 检查响应类型是否存在
    local required_types=(
        "UserRolesResponse"
        "UserPermissionsResponse"
        "UserSessionsResponse"
        "PermissionsResponse"
        "SessionsResponse"
        "MessageResponse"
        "AssignRoleRequest"
    )
    
    for type_name in "${required_types[@]}"; do
        if grep -q "type $type_name struct" "$dto_file"; then
            log_success "✅ $type_name 类型已定义"
        else
            log_error "❌ $type_name 类型缺失"
            missing_types+=("$type_name")
        fi
    done
    
    if [ ${#missing_types[@]} -eq 0 ]; then
        log_success "✅ 所有必需的类型定义都已存在"
        return 0
    else
        log_error "❌ 发现 ${#missing_types[@]} 个缺失的类型"
        return 1
    fi
}

# 验证服务启动
verify_service_startup() {
    log_test "验证User Service启动..."
    
    # 检查端口是否被占用
    if lsof -Pi :8085 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_info "端口8085已被占用，尝试终止占用进程..."
        lsof -ti:8085 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 创建必要目录
    mkdir -p data logs
    
    # 启动User Service
    log_info "启动User Service..."
    nohup ./bin/user-service --config=configs/user-service.dev.yaml > logs/user-service-test.log 2>&1 &
    local service_pid=$!
    
    # 等待服务启动
    sleep 3
    
    # 验证服务状态
    if curl -s http://localhost:8085/health > /dev/null 2>&1; then
        log_success "✅ User Service启动成功 (PID: $service_pid)"
        
        # 获取服务信息
        local health_response=$(curl -s http://localhost:8085/health 2>/dev/null || echo "{}")
        local service_name=$(echo "$health_response" | jq -r '.service // "unknown"' 2>/dev/null || echo "unknown")
        local dev_mode=$(echo "$health_response" | jq -r '.dev_mode // false' 2>/dev/null || echo "false")
        
        log_info "服务名称: $service_name"
        log_info "开发模式: $dev_mode"
        
        # 停止测试服务
        kill $service_pid 2>/dev/null || true
        
        return 0
    else
        log_error "❌ User Service启动失败"
        
        # 显示错误日志
        if [ -f "logs/user-service-test.log" ]; then
            echo "错误日志:"
            tail -10 logs/user-service-test.log
        fi
        
        # 清理进程
        kill $service_pid 2>/dev/null || true
        
        return 1
    fi
}

# 验证认证API
verify_auth_api() {
    log_test "验证认证API功能..."
    
    # 启动User Service用于测试
    nohup ./bin/user-service --config=configs/user-service.dev.yaml > logs/user-service-test.log 2>&1 &
    local service_pid=$!
    
    # 等待服务启动
    sleep 3
    
    # 测试登录API
    local login_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d '{"username":"test","password":"test"}' \
        http://localhost:8085/api/v1/auth/login 2>/dev/null || echo "HTTP_CODE:000")
    
    local login_code=$(echo "$login_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    local login_body=$(echo "$login_response" | sed 's/HTTP_CODE:[0-9]*$//')
    
    # 停止测试服务
    kill $service_pid 2>/dev/null || true
    
    if [ "$login_code" = "200" ]; then
        log_success "✅ 登录API测试成功 (HTTP $login_code)"
        
        # 检查响应格式
        local access_token=$(echo "$login_body" | jq -r '.token_pair.access_token // empty' 2>/dev/null || echo "")
        local user_id=$(echo "$login_body" | jq -r '.user.id // empty' 2>/dev/null || echo "")
        
        if [ -n "$access_token" ] && [ -n "$user_id" ]; then
            log_success "✅ 登录响应格式正确"
            log_info "用户ID: $user_id"
            log_info "Token: ${access_token:0:20}..."
        else
            log_error "❌ 登录响应格式不正确"
            return 1
        fi
        
        return 0
    else
        log_error "❌ 登录API测试失败 (HTTP $login_code)"
        echo "响应: $login_body"
        return 1
    fi
}

# 运行完整验证
run_complete_verification() {
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                User Service修复验证                       ║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    # 1. 验证构建
    total_tests=$((total_tests + 1))
    if verify_build; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 2. 验证Handler方法
    total_tests=$((total_tests + 1))
    if verify_handler_methods; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 3. 验证类型定义
    total_tests=$((total_tests + 1))
    if verify_type_definitions; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 4. 验证服务启动
    total_tests=$((total_tests + 1))
    if verify_service_startup; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 5. 验证认证API
    total_tests=$((total_tests + 1))
    if verify_auth_api; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 显示验证结果
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                        验证结果                            ║${NC}"
    echo -e "${BLUE}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BLUE}║  总验证项: $total_tests                                           ║${NC}"
    echo -e "${BLUE}║  通过: ${GREEN}$passed_tests${BLUE}                                              ║${NC}"
    echo -e "${BLUE}║  失败: ${RED}$failed_tests${BLUE}                                              ║${NC}"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${BLUE}║  状态: ${GREEN}✅ 所有验证通过${BLUE}                                  ║${NC}"
        echo -e "${BLUE}║  结论: ${GREEN}User Service修复成功${BLUE}                           ║${NC}"
        echo -e "${BLUE}║  建议: ${GREEN}可以使用新的认证架构${BLUE}                          ║${NC}"
    else
        echo -e "${BLUE}║  状态: ${RED}❌ 部分验证失败${BLUE}                                  ║${NC}"
        echo -e "${BLUE}║  结论: ${RED}User Service仍有问题${BLUE}                            ║${NC}"
        echo -e "${BLUE}║  建议: ${RED}检查错误并继续修复${BLUE}                            ║${NC}"
    fi
    
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    return $failed_tests
}

# 显示修复总结
show_fix_summary() {
    echo -e "${CYAN}📋 User Service修复总结${NC}"
    echo
    echo -e "${GREEN}✅ 已修复的问题:${NC}"
    echo "• 添加了所有缺失的Handler方法实现"
    echo "• 实现了ResetPassword、ConfirmResetPassword等认证方法"
    echo "• 实现了UpdateUser、DeleteUser等用户管理方法"
    echo "• 实现了GetUserRoles、AssignRole等角色管理方法"
    echo "• 实现了GetUserPermissions、GetUserSessions等权限和会话方法"
    echo "• 添加了所有必需的请求和响应类型定义"
    echo "• 修复了配置文件解析问题"
    echo "• 确保开发模式下所有方法都有适当的处理"
    echo
    echo -e "${BLUE}🏗️ 架构改进:${NC}"
    echo "• User Service现在是完全独立的认证服务"
    echo "• 支持完整的用户管理、角色管理、权限控制功能"
    echo "• 通过API Gateway提供统一的服务入口"
    echo "• 符合微服务单一职责和服务自治原则"
    echo
    echo -e "${YELLOW}🚀 下一步:${NC}"
    echo "• 运行完整的架构测试: ./scripts/test-new-architecture.sh"
    echo "• 切换前端到新架构: ./scripts/switch-frontend-to-gateway.sh"
    echo "• 启动前端测试登录: ./scripts/start-frontend-dev.sh"
    echo
}

# 主函数
main() {
    echo -e "${GREEN}🔧 User Service构建错误修复验证${NC}"
    echo
    
    # 显示修复说明
    show_fix_info
    
    # 检查jq工具
    if ! command -v jq &> /dev/null; then
        log_info "jq工具未安装，JSON解析功能受限"
        echo "安装命令: sudo apt-get install jq 或 brew install jq"
        echo
    fi
    
    # 运行完整验证
    if run_complete_verification; then
        log_success "🎉 User Service修复验证全部通过！"
        echo
        echo -e "${GREEN}✅ 修复验证结果:${NC}"
        echo -e "${GREEN}• User Service能够成功构建${NC}"
        echo -e "${GREEN}• 所有Handler方法都已正确实现${NC}"
        echo -e "${GREEN}• 类型定义完整${NC}"
        echo -e "${GREEN}• 服务能够正常启动和运行${NC}"
        echo -e "${GREEN}• 认证API功能正常${NC}"
        echo
        echo -e "${CYAN}🎯 结论: User Service构建错误已完全修复${NC}"
        
        # 显示修复总结
        show_fix_summary
    else
        log_error "❌ User Service修复验证失败"
        echo
        echo -e "${RED}❌ 验证结果:${NC}"
        echo -e "${RED}• 部分功能仍有问题${NC}"
        echo -e "${RED}• 需要进一步修复${NC}"
        echo
        echo -e "${YELLOW}🔧 建议: 检查错误日志，继续修复问题${NC}"
        exit 1
    fi
}

# 执行主函数
main "$@"
