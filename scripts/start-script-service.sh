#!/bin/bash

# PaaS 平台脚本执行服务启动脚本
# 用于启动脚本执行服务，支持开发和生产环境

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
SERVICE_NAME="script-service"
LOG_FILE="${PROJECT_ROOT}/logs/${SERVICE_NAME}.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
PaaS 平台脚本执行服务启动脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -d, --daemon        以守护进程模式运行
    -b, --build         构建服务
    -c, --clean         清理数据和日志
    -s, --skip-deps     跳过依赖检查
    --port PORT         指定服务端口 (默认: 8084)
    --config FILE       指定配置文件路径
    --log-level LEVEL   设置日志级别 (debug, info, warn, error)

示例:
    $0                  # 启动服务
    $0 -d               # 以守护进程模式启动
    $0 -b               # 构建并启动服务
    $0 -c               # 清理数据
    $0 --port 9000      # 在端口9000启动服务

环境变量:
    SCRIPT_SERVICE_PORT     服务端口
    SCRIPT_SERVICE_CONFIG   配置文件路径
    DEBUG                   是否启用调试模式
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 Go 环境
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装，请先安装 Go 1.21+"
        exit 1
    fi
    
    # 检查 Go 版本
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    if [[ "$(printf '%s\n' "1.21" "$GO_VERSION" | sort -V | head -n1)" != "1.21" ]]; then
        log_error "Go 版本过低，需要 1.21+，当前版本: $GO_VERSION"
        exit 1
    fi
    
    # 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装: $(docker --version)"
        
        # 检查 Docker 是否运行
        if ! docker info &> /dev/null; then
            log_warn "Docker 守护进程未运行，脚本执行功能可能受限"
        fi
    else
        log_warn "Docker 未安装，脚本执行功能将受限"
    fi
    
    # 检查 Git (可选)
    if command -v git &> /dev/null; then
        log_info "Git 已安装: $(git --version)"
    else
        log_warn "Git 未安装，代码仓库功能可能受限"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=(
        "${PROJECT_ROOT}/bin"
        "${PROJECT_ROOT}/logs"
        "${PROJECT_ROOT}/data"
        "${PROJECT_ROOT}/data/workspaces"
        "${PROJECT_ROOT}/data/artifacts"
        "${PROJECT_ROOT}/data/templates"
    )
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_debug "创建目录: $dir"
        fi
    done
}

# 构建服务
build_service() {
    log_info "构建脚本执行服务..."
    
    cd "${PROJECT_ROOT}"
    
    # 设置构建环境变量
    export CGO_ENABLED=1
    export GOOS=linux
    export GOARCH=amd64
    
    # 构建服务
    if go build -o "bin/${SERVICE_NAME}" "cmd/${SERVICE_NAME}/main.go"; then
        log_info "服务构建成功"
    else
        log_error "服务构建失败"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":${port} "; then
            log_error "端口 ${port} 已被占用"
            exit 1
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep -q ":${port} "; then
            log_error "端口 ${port} 已被占用"
            exit 1
        fi
    else
        log_warn "无法检查端口占用情况，请确保端口 ${port} 可用"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 检查数据库文件是否存在
    local db_file="${PROJECT_ROOT}/data/${SERVICE_NAME}.db"
    if [[ ! -f "$db_file" ]]; then
        log_info "创建数据库文件: $db_file"
        touch "$db_file"
    fi
    
    # 数据库迁移将在服务启动时自动执行
    log_info "数据库初始化完成"
}

# 启动服务
start_service() {
    local daemon_mode=${1:-false}
    local port=${2:-8084}
    local config_file=${3:-"${PROJECT_ROOT}/configs/${SERVICE_NAME}.yaml"}
    
    log_info "启动脚本执行服务..."
    log_info "端口: $port"
    log_info "配置文件: $config_file"
    
    # 设置环境变量
    export SCRIPT_SERVICE_PORT="$port"
    export SCRIPT_SERVICE_CONFIG="$config_file"
    
    # 构建启动命令
    local cmd="${PROJECT_ROOT}/bin/${SERVICE_NAME}"
    
    if [[ "$daemon_mode" == "true" ]]; then
        # 守护进程模式
        log_info "以守护进程模式启动服务..."
        nohup "$cmd" > "$LOG_FILE" 2>&1 &
        local pid=$!
        echo $pid > "${PROJECT_ROOT}/data/${SERVICE_NAME}.pid"
        log_info "服务已启动，PID: $pid"
        log_info "日志文件: $LOG_FILE"
    else
        # 前台模式
        log_info "以前台模式启动服务..."
        exec "$cmd"
    fi
}

# 检查服务状态
check_service() {
    local port=${1:-8084}
    local max_attempts=30
    local attempt=0
    
    log_info "检查服务状态..."
    
    while [[ $attempt -lt $max_attempts ]]; do
        if curl -s "http://localhost:${port}/health" > /dev/null 2>&1; then
            log_info "服务健康检查通过"
            return 0
        fi
        
        ((attempt++))
        log_debug "健康检查尝试 $attempt/$max_attempts"
        sleep 2
    done
    
    log_error "服务健康检查失败"
    return 1
}

# 停止服务
stop_service() {
    log_info "停止脚本执行服务..."
    
    local pid_file="${PROJECT_ROOT}/data/${SERVICE_NAME}.pid"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            log_info "服务已停止，PID: $pid"
        else
            log_warn "进程 $pid 不存在"
        fi
        rm -f "$pid_file"
    else
        log_warn "PID文件不存在，尝试通过进程名停止服务"
        pkill -f "${SERVICE_NAME}" || true
    fi
}

# 清理数据和日志
cleanup() {
    log_info "清理数据和日志..."
    
    # 停止服务
    stop_service
    
    # 清理数据
    local cleanup_dirs=(
        "${PROJECT_ROOT}/data"
        "${PROJECT_ROOT}/logs"
    )
    
    for dir in "${cleanup_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            rm -rf "$dir"
            log_info "已清理目录: $dir"
        fi
    done
    
    log_info "清理完成"
}

# 主函数
main() {
    local build_flag=false
    local daemon_flag=false
    local skip_deps=false
    local clean_flag=false
    local port=8084
    local config_file=""
    local log_level=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--daemon)
                daemon_flag=true
                shift
                ;;
            -b|--build)
                build_flag=true
                shift
                ;;
            -c|--clean)
                clean_flag=true
                shift
                ;;
            -s|--skip-deps)
                skip_deps=true
                shift
                ;;
            --port)
                port="$2"
                shift 2
                ;;
            --config)
                config_file="$2"
                shift 2
                ;;
            --log-level)
                log_level="$2"
                shift 2
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置默认配置文件
    if [[ -z "$config_file" ]]; then
        config_file="${PROJECT_ROOT}/configs/${SERVICE_NAME}.yaml"
    fi
    
    # 设置日志级别环境变量
    if [[ -n "$log_level" ]]; then
        export LOG_LEVEL="$log_level"
    fi
    
    log_info "开始启动脚本执行服务..."
    
    # 清理
    if [[ "$clean_flag" == "true" ]]; then
        cleanup
        exit 0
    fi
    
    # 检查依赖
    if [[ "$skip_deps" != "true" ]]; then
        check_dependencies
    fi
    
    # 创建目录
    create_directories
    
    # 构建服务
    if [[ "$build_flag" == "true" ]] || [[ ! -f "${PROJECT_ROOT}/bin/${SERVICE_NAME}" ]]; then
        build_service
    fi
    
    # 检查端口
    check_port "$port"
    
    # 初始化数据库
    init_database
    
    # 启动服务
    start_service "$daemon_flag" "$port" "$config_file"
    
    # 检查服务状态（仅守护进程模式）
    if [[ "$daemon_flag" == "true" ]]; then
        if check_service "$port"; then
            log_info "脚本执行服务启动成功！"
            log_info "服务地址: http://localhost:${port}"
            log_info "API文档: http://localhost:${port}/docs"
            log_info "健康检查: http://localhost:${port}/health"
        else
            log_error "脚本执行服务启动失败，请检查日志: ${LOG_FILE}"
            exit 1
        fi
    fi
}

# 信号处理
trap 'log_info "收到中断信号，正在停止服务..."; stop_service; exit 0' INT TERM

# 执行主函数
main "$@"
