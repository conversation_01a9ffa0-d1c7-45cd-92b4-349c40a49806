#!/bin/bash

# 前端配置切换脚本
# 将前端API代理从直接连接App Manager切换到API Gateway

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示切换说明
show_switch_info() {
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                  前端配置切换工具                          ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  🎯 目标: 将前端API代理切换到API Gateway                  ║${NC}"
    echo -e "${CYAN}║  🔄 变更: 8081端口 -> 8080端口                            ║${NC}"
    echo -e "${CYAN}║  🏗️  架构: 前端 -> API Gateway -> 微服务                  ║${NC}"
    echo -e "${CYAN}║  ✅ 效果: 统一入口、职责清晰、可扩展                      ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 检查API Gateway状态
check_api_gateway() {
    log_info "检查API Gateway状态..."
    
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        log_success "✅ API Gateway (8080) 运行正常"
        
        # 获取API Gateway信息
        local gateway_info=$(curl -s http://localhost:8080/health 2>/dev/null || echo "{}")
        local service_name=$(echo "$gateway_info" | jq -r '.service // "unknown"' 2>/dev/null || echo "unknown")
        local dev_mode=$(echo "$gateway_info" | jq -r '.dev_mode // false' 2>/dev/null || echo "false")
        
        echo "    服务: $service_name"
        echo "    开发模式: $dev_mode"
        
        return 0
    else
        log_error "❌ API Gateway (8080) 未运行"
        log_error "请先启动API Gateway: ./scripts/migrate-to-user-service.sh migrate"
        return 1
    fi
}

# 检查User Service状态
check_user_service() {
    log_info "检查User Service状态..."
    
    if curl -s http://localhost:8085/health > /dev/null 2>&1; then
        log_success "✅ User Service (8085) 运行正常"
        return 0
    else
        log_error "❌ User Service (8085) 未运行"
        log_error "请先启动User Service: ./scripts/migrate-to-user-service.sh migrate"
        return 1
    fi
}

# 备份当前前端配置
backup_frontend_config() {
    log_info "备份当前前端配置..."
    
    local config_file="web/vite.config.ts"
    local backup_file="web/vite.config.ts.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [ -f "$config_file" ]; then
        cp "$config_file" "$backup_file"
        log_success "✅ 配置已备份到: $backup_file"
        echo "$backup_file" > /tmp/frontend_config_backup.txt
        return 0
    else
        log_error "❌ 前端配置文件不存在: $config_file"
        return 1
    fi
}

# 更新前端配置
update_frontend_config() {
    log_info "更新前端配置..."
    
    local config_file="web/vite.config.ts"
    
    # 检查当前配置
    if grep -q "localhost:8081" "$config_file"; then
        log_info "检测到当前配置指向App Manager (8081)"
        
        # 使用sed替换端口配置
        sed -i.tmp "s/localhost:8081/localhost:8080/g" "$config_file"
        
        # 验证替换结果
        if grep -q "localhost:8080" "$config_file"; then
            log_success "✅ 前端配置已更新为API Gateway (8080)"
            
            # 显示变更内容
            echo "    变更内容:"
            echo "    - target: 'http://localhost:8081' (App Manager)"
            echo "    + target: 'http://localhost:8080' (API Gateway)"
            
            # 清理临时文件
            rm -f "$config_file.tmp"
            return 0
        else
            log_error "❌ 配置更新失败"
            return 1
        fi
    elif grep -q "localhost:8080" "$config_file"; then
        log_info "✅ 前端配置已经指向API Gateway (8080)"
        return 0
    else
        log_warn "⚠️  无法识别当前前端配置，请手动更新"
        echo "    请将 web/vite.config.ts 中的代理目标改为: http://localhost:8080"
        return 1
    fi
}

# 测试前端配置
test_frontend_config() {
    log_info "测试前端配置..."
    
    # 检查前端依赖
    if [ ! -d "web/node_modules" ]; then
        log_warn "前端依赖未安装，跳过前端测试"
        return 0
    fi
    
    # 检查配置文件
    local config_file="web/vite.config.ts"
    
    if grep -q "localhost:8080" "$config_file"; then
        log_success "✅ 前端配置指向API Gateway"
        
        # 显示配置摘要
        echo "    代理配置: /api -> http://localhost:8080"
        echo "    目标服务: API Gateway"
        echo "    认证流程: 前端 -> API Gateway -> User Service"
        
        return 0
    else
        log_error "❌ 前端配置未正确更新"
        return 1
    fi
}

# 回滚前端配置
rollback_frontend_config() {
    log_warn "回滚前端配置..."
    
    if [ -f "/tmp/frontend_config_backup.txt" ]; then
        local backup_file=$(cat /tmp/frontend_config_backup.txt)
        
        if [ -f "$backup_file" ]; then
            cp "$backup_file" "web/vite.config.ts"
            log_success "✅ 前端配置已回滚"
            rm -f /tmp/frontend_config_backup.txt
        else
            log_error "❌ 备份文件不存在: $backup_file"
        fi
    else
        log_warn "⚠️  没有找到备份文件信息"
    fi
}

# 显示使用说明
show_usage_info() {
    echo -e "${CYAN}📋 前端配置切换完成${NC}"
    echo
    echo -e "${GREEN}✅ 配置变更:${NC}"
    echo "  • API代理目标: App Manager (8081) -> API Gateway (8080)"
    echo "  • 认证流程: 直连 -> 通过网关代理"
    echo "  • 架构模式: 单服务 -> 微服务"
    echo
    echo -e "${CYAN}🚀 下一步操作:${NC}"
    echo "  1. 启动前端开发服务器:"
    echo "     cd web && npm run dev"
    echo
    echo "  2. 访问前端应用:"
    echo "     http://localhost:3000"
    echo
    echo "  3. 测试登录功能:"
    echo "     - 用户名: 任意 (例如: admin)"
    echo "     - 密码: 任意 (例如: 123456)"
    echo
    echo -e "${YELLOW}⚠️  注意事项:${NC}"
    echo "  • 确保API Gateway和User Service都在运行"
    echo "  • 开发模式下任意用户名/密码都可以登录"
    echo "  • 查看浏览器开发者工具确认API请求路径"
    echo
    echo -e "${BLUE}🔧 故障排除:${NC}"
    echo "  • 如果登录失败，检查服务状态: ./scripts/test-new-architecture.sh"
    echo "  • 如果需要回滚，运行: ./scripts/switch-frontend-to-gateway.sh rollback"
    echo "  • 查看服务日志: tail -f logs/api-gateway.log logs/user-service.log"
    echo
}

# 主函数
main() {
    local action=${1:-"switch"}
    
    case $action in
        "switch")
            echo -e "${GREEN}🔄 切换前端配置到API Gateway${NC}"
            echo
            
            # 显示切换说明
            show_switch_info
            
            # 检查API Gateway状态
            if ! check_api_gateway; then
                exit 1
            fi
            
            # 检查User Service状态
            if ! check_user_service; then
                exit 1
            fi
            
            # 备份当前配置
            if ! backup_frontend_config; then
                exit 1
            fi
            
            # 更新前端配置
            if update_frontend_config; then
                # 测试配置
                if test_frontend_config; then
                    log_success "🎉 前端配置切换成功！"
                    show_usage_info
                else
                    log_error "❌ 前端配置测试失败，执行回滚..."
                    rollback_frontend_config
                    exit 1
                fi
            else
                log_error "❌ 前端配置更新失败，执行回滚..."
                rollback_frontend_config
                exit 1
            fi
            ;;
        "rollback")
            echo -e "${YELLOW}🔄 回滚前端配置${NC}"
            rollback_frontend_config
            ;;
        "test")
            echo -e "${BLUE}🧪 测试前端配置${NC}"
            test_frontend_config
            ;;
        "status")
            echo -e "${BLUE}📊 检查服务状态${NC}"
            check_api_gateway
            check_user_service
            test_frontend_config
            ;;
        *)
            echo "用法: $0 [switch|rollback|test|status]"
            echo
            echo "命令说明:"
            echo "  switch   - 切换前端配置到API Gateway"
            echo "  rollback - 回滚前端配置"
            echo "  test     - 测试前端配置"
            echo "  status   - 检查服务状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
