#!/bin/bash

# 应用管理服务停止脚本
# 作者: PaaS 平台开发团队
# 描述: 安全停止应用管理服务

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVICE_NAME="app-manager"
PID_FILE="$PROJECT_ROOT/data/${SERVICE_NAME}.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 停止服务
stop_service() {
    local force=${1:-false}
    
    log_info "停止 ${SERVICE_NAME} 服务..."
    
    # 检查 PID 文件是否存在
    if [ ! -f "$PID_FILE" ]; then
        log_warn "PID 文件不存在: $PID_FILE"
        
        # 尝试通过进程名查找
        local pids=$(pgrep -f "bin/${SERVICE_NAME}" || true)
        if [ -n "$pids" ]; then
            log_info "通过进程名找到运行中的服务进程: $pids"
            for pid in $pids; do
                stop_process $pid $force
            done
        else
            log_info "没有找到运行中的 ${SERVICE_NAME} 进程"
        fi
        return 0
    fi
    
    # 读取 PID
    local pid=$(cat "$PID_FILE")
    
    # 检查进程是否存在
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warn "进程 $pid 不存在，清理 PID 文件"
        rm -f "$PID_FILE"
        return 0
    fi
    
    stop_process $pid $force
    
    # 清理 PID 文件
    rm -f "$PID_FILE"
    log_info "已清理 PID 文件"
}

# 停止指定进程
stop_process() {
    local pid=$1
    local force=${2:-false}
    
    log_info "停止进程 $pid..."
    
    if [ "$force" = true ]; then
        # 强制停止
        log_warn "强制停止进程 $pid"
        kill -9 "$pid"
        log_info "进程 $pid 已被强制停止"
    else
        # 优雅停止
        log_debug "发送 TERM 信号到进程 $pid"
        kill -TERM "$pid"
        
        # 等待进程退出
        local count=0
        local max_wait=30
        
        while kill -0 "$pid" 2>/dev/null && [ $count -lt $max_wait ]; do
            log_debug "等待进程 $pid 退出... ($count/$max_wait)"
            sleep 1
            ((count++))
        done
        
        # 检查进程是否已退出
        if kill -0 "$pid" 2>/dev/null; then
            log_warn "进程 $pid 未在 $max_wait 秒内退出，强制停止"
            kill -9 "$pid"
            log_info "进程 $pid 已被强制停止"
        else
            log_info "进程 $pid 已优雅退出"
        fi
    fi
}

# 检查服务状态
check_status() {
    log_info "检查 ${SERVICE_NAME} 服务状态..."
    
    # 检查 PID 文件
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "服务正在运行 (PID: $pid)"
            return 0
        else
            log_warn "PID 文件存在但进程不存在，清理 PID 文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 通过进程名检查
    local pids=$(pgrep -f "bin/${SERVICE_NAME}" || true)
    if [ -n "$pids" ]; then
        log_warn "发现运行中的服务进程: $pids"
        return 0
    fi
    
    log_info "服务未运行"
    return 1
}

# 显示帮助信息
show_help() {
    echo "应用管理服务停止脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  -f, --force     强制停止服务"
    echo "  -s, --status    检查服务状态"
    echo "  --kill-all      停止所有相关进程"
    echo ""
    echo "示例:"
    echo "  $0              # 优雅停止服务"
    echo "  $0 -f           # 强制停止服务"
    echo "  $0 -s           # 检查服务状态"
    echo "  $0 --kill-all   # 停止所有相关进程"
    echo ""
}

# 停止所有相关进程
kill_all() {
    log_info "停止所有 ${SERVICE_NAME} 相关进程..."
    
    # 查找所有相关进程
    local pids=$(pgrep -f "${SERVICE_NAME}" || true)
    
    if [ -z "$pids" ]; then
        log_info "没有找到相关进程"
        return 0
    fi
    
    log_info "找到进程: $pids"
    
    for pid in $pids; do
        local cmd=$(ps -p $pid -o comm= 2>/dev/null || echo "未知")
        log_info "停止进程 $pid ($cmd)"
        kill -9 "$pid" 2>/dev/null || true
    done
    
    # 清理 PID 文件
    rm -f "$PID_FILE"
    
    log_info "所有相关进程已停止"
}

# 主函数
main() {
    local force=false
    local status_only=false
    local kill_all_flag=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                force=true
                shift
                ;;
            -s|--status)
                status_only=true
                shift
                ;;
            --kill-all)
                kill_all_flag=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "PaaS 平台应用管理服务停止脚本"
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 仅检查状态
    if [ "$status_only" = true ]; then
        check_status
        exit $?
    fi
    
    # 停止所有进程
    if [ "$kill_all_flag" = true ]; then
        kill_all
        exit 0
    fi
    
    # 停止服务
    stop_service $force
    
    # 验证停止结果
    sleep 1
    if check_status; then
        log_error "服务停止失败"
        exit 1
    else
        log_info "服务已成功停止"
    fi
}

# 捕获中断信号
trap 'log_info "收到中断信号，正在退出..."; exit 0' INT TERM

# 执行主函数
main "$@"
