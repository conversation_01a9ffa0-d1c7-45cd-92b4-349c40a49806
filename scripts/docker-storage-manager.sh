#!/bin/bash

# Docker存储管理脚本
# 用于配置和管理Docker daemon的存储设置

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly DOCKER_CONFIG_FILE="/etc/docker/daemon.json"
readonly DOCKER_CONFIG_BACKUP="/etc/docker/daemon.json.backup.$(date +%Y%m%d-%H%M%S)"
readonly PROJECT_CONFIG_FILE="$PROJECT_ROOT/configs/docker-daemon.json"
readonly LOG_FILE="$PROJECT_ROOT/logs/docker-storage-$(date +%Y%m%d-%H%M%S).log"

# 日志函数
log_info() {
    local msg="$1"
    echo -e "${BLUE}[INFO]${NC} $msg" | tee -a "$LOG_FILE"
}

log_success() {
    local msg="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $msg" | tee -a "$LOG_FILE"
}

log_warning() {
    local msg="$1"
    echo -e "${YELLOW}[WARNING]${NC} $msg" | tee -a "$LOG_FILE"
}

log_error() {
    local msg="$1"
    echo -e "${RED}[ERROR]${NC} $msg" | tee -a "$LOG_FILE"
}

print_title() {
    local title="$1"
    echo -e "\n${CYAN}🐳 $title${NC}\n" | tee -a "$LOG_FILE"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 初始化环境
init_environment() {
    mkdir -p "$(dirname "$LOG_FILE")"
    log_info "Docker存储管理脚本启动"
    log_info "日志文件: $LOG_FILE"
}

# 检查Docker状态
check_docker_status() {
    print_title "检查Docker状态"
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        return 1
    fi
    
    if systemctl is-active --quiet docker; then
        log_success "Docker服务正在运行"
        
        # 显示Docker信息
        log_info "Docker版本信息:"
        docker version --format "{{.Server.Version}}" | tee -a "$LOG_FILE"
        
        log_info "Docker系统信息:"
        docker system df | tee -a "$LOG_FILE"
        
    else
        log_warning "Docker服务未运行"
        return 1
    fi
}

# 分析当前存储配置
analyze_current_config() {
    print_title "分析当前存储配置"
    
    # 检查当前配置文件
    if [[ -f "$DOCKER_CONFIG_FILE" ]]; then
        log_info "当前Docker配置文件存在: $DOCKER_CONFIG_FILE"
        
        # 显示存储相关配置
        log_info "当前存储配置:"
        if command -v jq &> /dev/null; then
            jq -r '.["storage-driver"], .["data-root"], .["storage-opts"]' "$DOCKER_CONFIG_FILE" 2>/dev/null | tee -a "$LOG_FILE" || \
            cat "$DOCKER_CONFIG_FILE" | tee -a "$LOG_FILE"
        else
            cat "$DOCKER_CONFIG_FILE" | tee -a "$LOG_FILE"
        fi
    else
        log_warning "Docker配置文件不存在，使用默认配置"
    fi
    
    # 检查Docker根目录
    local docker_root=$(docker info --format '{{.DockerRootDir}}' 2>/dev/null || echo "/var/lib/docker")
    log_info "Docker根目录: $docker_root"
    
    # 检查存储驱动
    local storage_driver=$(docker info --format '{{.Driver}}' 2>/dev/null || echo "unknown")
    log_info "当前存储驱动: $storage_driver"
    
    # 检查磁盘使用情况
    log_info "磁盘使用情况:"
    df -h "$docker_root" | tee -a "$LOG_FILE"
    
    # 检查Docker存储使用情况
    log_info "Docker存储使用情况:"
    docker system df -v | tee -a "$LOG_FILE"
}

# 备份当前配置
backup_current_config() {
    print_title "备份当前配置"
    
    if [[ -f "$DOCKER_CONFIG_FILE" ]]; then
        cp "$DOCKER_CONFIG_FILE" "$DOCKER_CONFIG_BACKUP"
        log_success "配置文件已备份: $DOCKER_CONFIG_BACKUP"
    else
        log_info "没有现有配置文件需要备份"
    fi
}

# 应用新的存储配置
apply_storage_config() {
    print_title "应用新的存储配置"
    
    if [[ ! -f "$PROJECT_CONFIG_FILE" ]]; then
        log_error "项目配置文件不存在: $PROJECT_CONFIG_FILE"
        return 1
    fi
    
    # 验证配置文件格式
    if command -v jq &> /dev/null; then
        if ! jq empty "$PROJECT_CONFIG_FILE" 2>/dev/null; then
            log_error "配置文件JSON格式无效"
            return 1
        fi
        log_success "配置文件JSON格式验证通过"
    else
        log_warning "jq未安装，跳过JSON格式验证"
    fi
    
    # 创建Docker配置目录
    mkdir -p /etc/docker
    
    # 复制配置文件
    cp "$PROJECT_CONFIG_FILE" "$DOCKER_CONFIG_FILE"
    log_success "新配置文件已应用: $DOCKER_CONFIG_FILE"
    
    # 设置正确的权限
    chmod 644 "$DOCKER_CONFIG_FILE"
    chown root:root "$DOCKER_CONFIG_FILE"
    
    log_info "配置文件权限已设置"
}

# 重启Docker服务
restart_docker_service() {
    print_title "重启Docker服务"
    
    log_info "停止Docker服务..."
    systemctl stop docker
    
    # 等待服务完全停止
    sleep 5
    
    log_info "启动Docker服务..."
    systemctl start docker
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    if systemctl is-active --quiet docker; then
        log_success "Docker服务重启成功"
    else
        log_error "Docker服务重启失败"
        
        # 尝试恢复备份配置
        if [[ -f "$DOCKER_CONFIG_BACKUP" ]]; then
            log_warning "尝试恢复备份配置..."
            cp "$DOCKER_CONFIG_BACKUP" "$DOCKER_CONFIG_FILE"
            systemctl start docker
            
            if systemctl is-active --quiet docker; then
                log_warning "已恢复到备份配置"
            else
                log_error "恢复备份配置也失败"
            fi
        fi
        
        return 1
    fi
}

# 验证新配置
verify_new_config() {
    print_title "验证新配置"
    
    # 检查Docker是否正常运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker无法正常运行"
        return 1
    fi
    
    # 检查存储驱动
    local new_driver=$(docker info --format '{{.Driver}}' 2>/dev/null)
    log_info "新存储驱动: $new_driver"
    
    # 检查Docker根目录
    local new_root=$(docker info --format '{{.DockerRootDir}}' 2>/dev/null)
    log_info "新Docker根目录: $new_root"
    
    # 检查BuildKit是否启用
    if docker buildx version >/dev/null 2>&1; then
        log_success "BuildKit已启用"
    else
        log_warning "BuildKit未启用"
    fi
    
    # 测试基本功能
    log_info "测试Docker基本功能..."
    if docker run --rm hello-world >/dev/null 2>&1; then
        log_success "Docker基本功能测试通过"
    else
        log_error "Docker基本功能测试失败"
        return 1
    fi
    
    log_success "新配置验证通过"
}

# 配置存储清理策略
configure_cleanup_policies() {
    print_title "配置存储清理策略"
    
    # 启用BuildKit垃圾回收
    log_info "配置BuildKit垃圾回收..."
    
    # 创建BuildKit配置
    local buildkit_config="/etc/buildkit/buildkitd.toml"
    mkdir -p "$(dirname "$buildkit_config")"
    
    cat > "$buildkit_config" << 'EOF'
# BuildKit配置文件
# 用于配置构建缓存和垃圾回收

[worker.oci]
  enabled = true
  platforms = [ "linux/amd64" ]

[worker.containerd]
  enabled = false

# 垃圾回收配置
[worker.gc]
  # 启用垃圾回收
  enabled = true
  
  # 垃圾回收策略
  [[worker.gc.policy]]
    # 保留最近使用的缓存
    keepDuration = "168h"  # 7天
    keepBytes = "10GB"
    
  [[worker.gc.policy]]
    # 保留构建缓存
    keepDuration = "72h"   # 3天
    keepBytes = "5GB"
    filters = [ "type==source.local", "type==exec.cachemount" ]
    
  [[worker.gc.policy]]
    # 清理所有旧缓存
    all = true
    keepBytes = "20GB"

# 网络配置
[worker.oci.network]
  mode = "auto"

# 日志配置
[log]
  level = "info"
  format = "text"
EOF
    
    log_success "BuildKit配置已创建: $buildkit_config"
    
    # 配置Docker日志轮转
    log_info "配置Docker日志轮转..."
    
    local logrotate_config="/etc/logrotate.d/docker"
    cat > "$logrotate_config" << 'EOF'
/var/lib/docker/containers/*/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
    postrotate
        /bin/kill -USR1 $(cat /var/run/docker.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF
    
    log_success "Docker日志轮转配置已创建: $logrotate_config"
}

# 设置存储监控
setup_storage_monitoring() {
    print_title "设置存储监控"
    
    # 创建存储监控脚本
    local monitor_script="$SCRIPT_DIR/docker-storage-monitor.sh"
    
    cat > "$monitor_script" << 'EOF'
#!/bin/bash

# Docker存储监控脚本
# 监控Docker存储使用情况并发送告警

set -euo pipefail

# 配置
DOCKER_ROOT="/var/lib/docker"
THRESHOLD_PERCENT=80
WEBHOOK_URL="${WEBHOOK_URL:-}"
LOG_FILE="/var/log/docker-storage-monitor.log"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# 获取磁盘使用率
get_disk_usage() {
    df "$DOCKER_ROOT" | awk 'NR==2 {print $5}' | sed 's/%//'
}

# 获取Docker存储信息
get_docker_storage_info() {
    docker system df --format "table {{.Type}}\t{{.Total}}\t{{.Active}}\t{{.Size}}\t{{.Reclaimable}}" 2>/dev/null
}

# 发送告警
send_alert() {
    local message="$1"
    
    log_message "ALERT: $message"
    
    # 发送Webhook通知
    if [[ -n "$WEBHOOK_URL" ]]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"Docker存储告警: $message\"}" \
            2>/dev/null || log_message "Webhook发送失败"
    fi
    
    # 发送系统日志
    logger -t docker-storage-monitor "ALERT: $message"
}

# 主函数
main() {
    local usage=$(get_disk_usage)
    
    log_message "磁盘使用率检查: ${usage}%"
    
    if [[ $usage -gt $THRESHOLD_PERCENT ]]; then
        local storage_info=$(get_docker_storage_info)
        send_alert "磁盘使用率 ${usage}% 超过阈值 ${THRESHOLD_PERCENT}%"
        
        # 记录详细信息
        log_message "Docker存储信息:"
        echo "$storage_info" >> "$LOG_FILE"
        
        # 触发自动清理（如果配置了）
        if [[ -x "$(dirname "$0")/docker-image-cleanup.sh" ]]; then
            log_message "触发自动清理"
            "$(dirname "$0")/docker-image-cleanup.sh" --force dangling unused >> "$LOG_FILE" 2>&1
        fi
    fi
}

main "$@"
EOF
    
    chmod +x "$monitor_script"
    log_success "存储监控脚本已创建: $monitor_script"
    
    # 添加到cron任务
    local cron_entry="*/15 * * * * root $monitor_script"
    
    if ! crontab -l 2>/dev/null | grep -q "$monitor_script"; then
        echo "$cron_entry" >> /etc/cron.d/docker-storage-monitor
        log_success "存储监控已添加到cron任务"
    else
        log_info "存储监控cron任务已存在"
    fi
}

# 显示配置摘要
show_configuration_summary() {
    print_title "配置摘要"
    
    echo "📋 Docker存储配置摘要:" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    
    # 存储驱动
    local driver=$(docker info --format '{{.Driver}}' 2>/dev/null || echo "unknown")
    echo "🗄️  存储驱动: $driver" | tee -a "$LOG_FILE"
    
    # Docker根目录
    local root_dir=$(docker info --format '{{.DockerRootDir}}' 2>/dev/null || echo "unknown")
    echo "📁 Docker根目录: $root_dir" | tee -a "$LOG_FILE"
    
    # 磁盘使用情况
    echo "💾 磁盘使用情况:" | tee -a "$LOG_FILE"
    df -h "$root_dir" | tail -1 | awk '{print "   使用: " $3 "/" $2 " (" $5 ")"}' | tee -a "$LOG_FILE"
    
    # Docker存储使用情况
    echo "🐳 Docker存储使用:" | tee -a "$LOG_FILE"
    docker system df | tail -n +2 | while read -r line; do
        echo "   $line" | tee -a "$LOG_FILE"
    done
    
    # BuildKit状态
    if docker buildx version >/dev/null 2>&1; then
        echo "🔧 BuildKit: 已启用" | tee -a "$LOG_FILE"
    else
        echo "🔧 BuildKit: 未启用" | tee -a "$LOG_FILE"
    fi
    
    # 配置文件位置
    echo "⚙️  配置文件: $DOCKER_CONFIG_FILE" | tee -a "$LOG_FILE"
    echo "📄 备份文件: $DOCKER_CONFIG_BACKUP" | tee -a "$LOG_FILE"
    
    echo "" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker存储管理脚本

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -a, --analyze       仅分析当前配置
  -c, --configure     应用新的存储配置
  -m, --monitor       设置存储监控
  -r, --restart       重启Docker服务
  -s, --status        显示配置状态
  --backup            仅备份当前配置
  --restore           恢复备份配置

示例:
  $0                  # 完整的配置流程
  $0 --analyze        # 仅分析当前配置
  $0 --configure      # 应用新配置
  $0 --status         # 显示状态

EOF
}

# 恢复备份配置
restore_backup_config() {
    print_title "恢复备份配置"
    
    if [[ ! -f "$DOCKER_CONFIG_BACKUP" ]]; then
        log_error "备份文件不存在: $DOCKER_CONFIG_BACKUP"
        return 1
    fi
    
    cp "$DOCKER_CONFIG_BACKUP" "$DOCKER_CONFIG_FILE"
    log_success "配置已恢复: $DOCKER_CONFIG_FILE"
    
    restart_docker_service
}

# 主函数
main() {
    local action="full"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--analyze)
                action="analyze"
                shift
                ;;
            -c|--configure)
                action="configure"
                shift
                ;;
            -m|--monitor)
                action="monitor"
                shift
                ;;
            -r|--restart)
                action="restart"
                shift
                ;;
            -s|--status)
                action="status"
                shift
                ;;
            --backup)
                action="backup"
                shift
                ;;
            --restore)
                action="restore"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查权限
    check_permissions
    init_environment
    
    # 执行相应操作
    case "$action" in
        "analyze")
            check_docker_status
            analyze_current_config
            ;;
        "configure")
            check_docker_status
            analyze_current_config
            backup_current_config
            apply_storage_config
            restart_docker_service
            verify_new_config
            configure_cleanup_policies
            show_configuration_summary
            ;;
        "monitor")
            setup_storage_monitoring
            ;;
        "restart")
            restart_docker_service
            ;;
        "status")
            check_docker_status
            show_configuration_summary
            ;;
        "backup")
            backup_current_config
            ;;
        "restore")
            restore_backup_config
            ;;
        "full")
            check_docker_status
            analyze_current_config
            backup_current_config
            apply_storage_config
            restart_docker_service
            verify_new_config
            configure_cleanup_policies
            setup_storage_monitoring
            show_configuration_summary
            ;;
    esac
    
    log_success "Docker存储管理操作完成"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
