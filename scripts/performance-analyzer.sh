#!/bin/bash

# PaaS 平台性能分析工具
# 分析系统性能瓶颈、资源使用和优化建议

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ANALYSIS_DIR="$PROJECT_ROOT/performance-analysis"
REPORT_FILE="$ANALYSIS_DIR/performance-report-$(date +%Y%m%d-%H%M%S).md"

echo -e "${PURPLE}⚡ PaaS 平台性能分析工具${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：创建分析目录
create_analysis_dir() {
    mkdir -p "$ANALYSIS_DIR"
    print_success "分析目录已创建: $ANALYSIS_DIR"
}

# 函数：分析 CPU 性能
analyze_cpu_performance() {
    local namespace=${1:-"paas-platform"}
    
    print_title "分析 CPU 性能"
    
    {
        echo "## CPU 性能分析"
        echo ""
        echo "### 节点 CPU 使用情况"
        echo ""
        
        # 获取节点 CPU 使用情况
        if kubectl top nodes &> /dev/null; then
            echo '```'
            kubectl top nodes
            echo '```'
            echo ""
            
            # 分析 CPU 使用率
            local high_cpu_nodes=$(kubectl top nodes --no-headers | awk '$3 > 80 {print $1}')
            if [[ -n "$high_cpu_nodes" ]]; then
                echo "⚠️ **高 CPU 使用率节点:**"
                echo "$high_cpu_nodes" | while read -r node; do
                    echo "- $node"
                done
                echo ""
            fi
        else
            echo "❌ 无法获取节点 CPU 使用情况"
            echo ""
        fi
        
        echo "### Pod CPU 使用情况"
        echo ""
        
        if kubectl top pods -n "$namespace" &> /dev/null; then
            echo '```'
            kubectl top pods -n "$namespace" --sort-by=cpu
            echo '```'
            echo ""
            
            # 分析高 CPU 使用的 Pod
            local high_cpu_pods=$(kubectl top pods -n "$namespace" --no-headers | awk '$2 > 500 {print $1}')
            if [[ -n "$high_cpu_pods" ]]; then
                echo "⚠️ **高 CPU 使用率 Pod (>500m):**"
                echo "$high_cpu_pods" | while read -r pod; do
                    echo "- $pod"
                done
                echo ""
            fi
        else
            echo "❌ 无法获取 Pod CPU 使用情况"
            echo ""
        fi
        
        echo "### CPU 限制和请求分析"
        echo ""
        
        kubectl get pods -n "$namespace" -o json | jq -r '
            .items[] | 
            select(.spec.containers[].resources.limits.cpu or .spec.containers[].resources.requests.cpu) |
            {
                name: .metadata.name,
                containers: [
                    .spec.containers[] | {
                        name: .name,
                        cpu_request: .resources.requests.cpu // "未设置",
                        cpu_limit: .resources.limits.cpu // "未设置"
                    }
                ]
            }
        ' 2>/dev/null | jq -r '
            "| Pod | 容器 | CPU 请求 | CPU 限制 |",
            "|-----|------|----------|----------|",
            (.name as $pod | .containers[] | "| \($pod) | \(.name) | \(.cpu_request) | \(.cpu_limit) |")
        ' || echo "❌ 无法获取 CPU 资源配置"
        
        echo ""
        
    } >> "$REPORT_FILE"
    
    print_success "CPU 性能分析完成"
}

# 函数：分析内存性能
analyze_memory_performance() {
    local namespace=${1:-"paas-platform"}
    
    print_title "分析内存性能"
    
    {
        echo "## 内存性能分析"
        echo ""
        echo "### 节点内存使用情况"
        echo ""
        
        if kubectl top nodes &> /dev/null; then
            echo '```'
            kubectl top nodes
            echo '```'
            echo ""
            
            # 分析内存使用率
            local high_memory_nodes=$(kubectl top nodes --no-headers | awk '$5 > 80 {print $1}')
            if [[ -n "$high_memory_nodes" ]]; then
                echo "⚠️ **高内存使用率节点:**"
                echo "$high_memory_nodes" | while read -r node; do
                    echo "- $node"
                done
                echo ""
            fi
        else
            echo "❌ 无法获取节点内存使用情况"
            echo ""
        fi
        
        echo "### Pod 内存使用情况"
        echo ""
        
        if kubectl top pods -n "$namespace" &> /dev/null; then
            echo '```'
            kubectl top pods -n "$namespace" --sort-by=memory
            echo '```'
            echo ""
            
            # 分析高内存使用的 Pod
            local high_memory_pods=$(kubectl top pods -n "$namespace" --no-headers | awk '$3 > 512 {print $1}')
            if [[ -n "$high_memory_pods" ]]; then
                echo "⚠️ **高内存使用率 Pod (>512Mi):**"
                echo "$high_memory_pods" | while read -r pod; do
                    echo "- $pod"
                done
                echo ""
            fi
        else
            echo "❌ 无法获取 Pod 内存使用情况"
            echo ""
        fi
        
        echo "### 内存限制和请求分析"
        echo ""
        
        kubectl get pods -n "$namespace" -o json | jq -r '
            .items[] | 
            select(.spec.containers[].resources.limits.memory or .spec.containers[].resources.requests.memory) |
            {
                name: .metadata.name,
                containers: [
                    .spec.containers[] | {
                        name: .name,
                        memory_request: .resources.requests.memory // "未设置",
                        memory_limit: .resources.limits.memory // "未设置"
                    }
                ]
            }
        ' 2>/dev/null | jq -r '
            "| Pod | 容器 | 内存请求 | 内存限制 |",
            "|-----|------|----------|----------|",
            (.name as $pod | .containers[] | "| \($pod) | \(.name) | \(.memory_request) | \(.memory_limit) |")
        ' || echo "❌ 无法获取内存资源配置"
        
        echo ""
        
    } >> "$REPORT_FILE"
    
    print_success "内存性能分析完成"
}

# 函数：分析网络性能
analyze_network_performance() {
    local namespace=${1:-"paas-platform"}
    
    print_title "分析网络性能"
    
    {
        echo "## 网络性能分析"
        echo ""
        echo "### 服务连接状态"
        echo ""
        
        kubectl get services -n "$namespace" -o wide | while read -r line; do
            if [[ "$line" == *"NAME"* ]]; then
                echo '```'
                echo "$line"
                continue
            fi
            echo "$line"
        done
        echo '```'
        echo ""
        
        echo "### 端点状态"
        echo ""
        
        kubectl get endpoints -n "$namespace" | while read -r line; do
            if [[ "$line" == *"NAME"* ]]; then
                echo '```'
                echo "$line"
                continue
            fi
            echo "$line"
        done
        echo '```'
        echo ""
        
        echo "### Ingress 配置"
        echo ""
        
        if kubectl get ingress -n "$namespace" &> /dev/null; then
            echo '```'
            kubectl get ingress -n "$namespace" -o wide
            echo '```'
            echo ""
        else
            echo "❌ 没有 Ingress 配置"
            echo ""
        fi
        
        echo "### 网络策略"
        echo ""
        
        if kubectl get networkpolicies -n "$namespace" &> /dev/null; then
            echo '```'
            kubectl get networkpolicies -n "$namespace"
            echo '```'
            echo ""
        else
            echo "ℹ️ 没有网络策略配置"
            echo ""
        fi
        
    } >> "$REPORT_FILE"
    
    print_success "网络性能分析完成"
}

# 函数：分析存储性能
analyze_storage_performance() {
    local namespace=${1:-"paas-platform"}
    
    print_title "分析存储性能"
    
    {
        echo "## 存储性能分析"
        echo ""
        echo "### 持久卷声明状态"
        echo ""
        
        if kubectl get pvc -n "$namespace" &> /dev/null; then
            echo '```'
            kubectl get pvc -n "$namespace" -o wide
            echo '```'
            echo ""
            
            # 分析存储使用情况
            kubectl get pvc -n "$namespace" -o json | jq -r '
                .items[] | {
                    name: .metadata.name,
                    status: .status.phase,
                    capacity: .status.capacity.storage // "未知",
                    storageClass: .spec.storageClassName // "默认",
                    accessModes: .spec.accessModes
                }
            ' 2>/dev/null | jq -r '
                "| PVC | 状态 | 容量 | 存储类 | 访问模式 |",
                "|-----|------|------|--------|----------|",
                "| \(.name) | \(.status) | \(.capacity) | \(.storageClass) | \(.accessModes | join(",")) |"
            ' || echo "❌ 无法获取 PVC 详细信息"
            
        else
            echo "ℹ️ 没有持久卷声明"
            echo ""
        fi
        
        echo "### 存储类配置"
        echo ""
        
        if kubectl get storageclass &> /dev/null; then
            echo '```'
            kubectl get storageclass
            echo '```'
            echo ""
        else
            echo "❌ 无法获取存储类信息"
            echo ""
        fi
        
    } >> "$REPORT_FILE"
    
    print_success "存储性能分析完成"
}

# 函数：分析应用性能指标
analyze_application_metrics() {
    local namespace=${1:-"paas-platform"}
    
    print_title "分析应用性能指标"
    
    {
        echo "## 应用性能指标分析"
        echo ""
        
        # 检查是否有 Prometheus
        if kubectl get pods -n monitoring -l app=prometheus &> /dev/null; then
            echo "### Prometheus 指标查询"
            echo ""
            
            # 查询常见性能指标
            local prometheus_pod=$(kubectl get pods -n monitoring -l app=prometheus -o jsonpath='{.items[0].metadata.name}')
            
            if [[ -n "$prometheus_pod" ]]; then
                echo "#### HTTP 请求率"
                echo '```'
                kubectl exec -n monitoring "$prometheus_pod" -- \
                    promtool query instant 'rate(http_requests_total[5m])' 2>/dev/null || echo "无法查询 HTTP 请求率"
                echo '```'
                echo ""
                
                echo "#### 响应时间分布"
                echo '```'
                kubectl exec -n monitoring "$prometheus_pod" -- \
                    promtool query instant 'histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))' 2>/dev/null || echo "无法查询响应时间"
                echo '```'
                echo ""
                
                echo "#### 错误率"
                echo '```'
                kubectl exec -n monitoring "$prometheus_pod" -- \
                    promtool query instant 'rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])' 2>/dev/null || echo "无法查询错误率"
                echo '```'
                echo ""
            fi
        else
            echo "ℹ️ 未检测到 Prometheus，跳过指标分析"
            echo ""
        fi
        
        echo "### Pod 重启统计"
        echo ""
        
        kubectl get pods -n "$namespace" -o json | jq -r '
            .items[] | {
                name: .metadata.name,
                restarts: (.status.containerStatuses[]?.restartCount // 0),
                ready: .status.conditions[]? | select(.type=="Ready") | .status,
                age: .metadata.creationTimestamp
            }
        ' 2>/dev/null | jq -r '
            "| Pod | 重启次数 | 就绪状态 | 创建时间 |",
            "|-----|----------|----------|----------|",
            "| \(.name) | \(.restarts) | \(.ready) | \(.age) |"
        ' || echo "❌ 无法获取 Pod 重启统计"
        
        echo ""
        
    } >> "$REPORT_FILE"
    
    print_success "应用性能指标分析完成"
}

# 函数：生成优化建议
generate_optimization_recommendations() {
    local namespace=${1:-"paas-platform"}
    
    print_title "生成优化建议"
    
    {
        echo "## 性能优化建议"
        echo ""
        
        echo "### 资源配置优化"
        echo ""
        
        # 检查资源配置
        local pods_without_limits=$(kubectl get pods -n "$namespace" -o json | jq -r '
            .items[] | 
            select(.spec.containers[] | .resources.limits == null) | 
            .metadata.name
        ' 2>/dev/null)
        
        if [[ -n "$pods_without_limits" ]]; then
            echo "⚠️ **未设置资源限制的 Pod:**"
            echo "$pods_without_limits" | while read -r pod; do
                echo "- $pod"
            done
            echo ""
            echo "**建议:** 为所有 Pod 设置适当的 CPU 和内存限制，防止资源争用。"
            echo ""
        fi
        
        local pods_without_requests=$(kubectl get pods -n "$namespace" -o json | jq -r '
            .items[] | 
            select(.spec.containers[] | .resources.requests == null) | 
            .metadata.name
        ' 2>/dev/null)
        
        if [[ -n "$pods_without_requests" ]]; then
            echo "⚠️ **未设置资源请求的 Pod:**"
            echo "$pods_without_requests" | while read -r pod; do
                echo "- $pod"
            done
            echo ""
            echo "**建议:** 为所有 Pod 设置资源请求，确保调度器能够正确分配资源。"
            echo ""
        fi
        
        echo "### 扩缩容优化"
        echo ""
        
        # 检查 HPA 配置
        if kubectl get hpa -n "$namespace" &> /dev/null; then
            echo "✅ **已配置水平 Pod 自动扩缩容 (HPA)**"
            echo '```'
            kubectl get hpa -n "$namespace"
            echo '```'
            echo ""
        else
            echo "⚠️ **未配置水平 Pod 自动扩缩容**"
            echo ""
            echo "**建议:** 为关键服务配置 HPA，根据 CPU/内存使用率自动扩缩容。"
            echo ""
        fi
        
        echo "### 网络优化"
        echo ""
        
        # 检查服务类型
        local external_services=$(kubectl get services -n "$namespace" -o json | jq -r '
            .items[] | 
            select(.spec.type == "LoadBalancer" or .spec.type == "NodePort") | 
            .metadata.name
        ' 2>/dev/null)
        
        if [[ -n "$external_services" ]]; then
            echo "ℹ️ **外部暴露的服务:**"
            echo "$external_services" | while read -r service; do
                echo "- $service"
            done
            echo ""
            echo "**建议:** 考虑使用 Ingress 替代 LoadBalancer/NodePort，减少网络开销。"
            echo ""
        fi
        
        echo "### 存储优化"
        echo ""
        
        # 检查存储类
        local slow_storage=$(kubectl get pvc -n "$namespace" -o json | jq -r '
            .items[] | 
            select(.spec.storageClassName == "standard" or .spec.storageClassName == null) | 
            .metadata.name
        ' 2>/dev/null)
        
        if [[ -n "$slow_storage" ]]; then
            echo "⚠️ **使用标准存储的 PVC:**"
            echo "$slow_storage" | while read -r pvc; do
                echo "- $pvc"
            done
            echo ""
            echo "**建议:** 对于数据库等 I/O 密集型应用，考虑使用 SSD 存储类。"
            echo ""
        fi
        
        echo "### 监控和告警优化"
        echo ""
        
        if ! kubectl get servicemonitor -n "$namespace" &> /dev/null; then
            echo "⚠️ **未配置服务监控**"
            echo ""
            echo "**建议:** 配置 ServiceMonitor 以收集应用指标，便于性能分析。"
            echo ""
        fi
        
        echo "### 安全优化"
        echo ""
        
        # 检查安全上下文
        local pods_without_security_context=$(kubectl get pods -n "$namespace" -o json | jq -r '
            .items[] | 
            select(.spec.securityContext == null) | 
            .metadata.name
        ' 2>/dev/null)
        
        if [[ -n "$pods_without_security_context" ]]; then
            echo "⚠️ **未设置安全上下文的 Pod:**"
            echo "$pods_without_security_context" | while read -r pod; do
                echo "- $pod"
            done
            echo ""
            echo "**建议:** 为所有 Pod 设置适当的安全上下文，提高安全性。"
            echo ""
        fi
        
        echo "### 总体建议"
        echo ""
        echo "1. **资源管理:** 确保所有 Pod 都设置了适当的资源请求和限制"
        echo "2. **自动扩缩容:** 为关键服务配置 HPA 和 VPA"
        echo "3. **监控告警:** 建立完善的监控和告警体系"
        echo "4. **存储优化:** 根据应用特性选择合适的存储类型"
        echo "5. **网络优化:** 使用 Ingress 和服务网格优化网络性能"
        echo "6. **安全加固:** 实施最小权限原则和安全最佳实践"
        echo ""
        
    } >> "$REPORT_FILE"
    
    print_success "优化建议生成完成"
}

# 函数：生成性能测试脚本
generate_performance_test() {
    local test_script="$ANALYSIS_DIR/performance-test.sh"
    
    print_title "生成性能测试脚本"
    
    cat > "$test_script" << 'EOF'
#!/bin/bash

# PaaS 平台性能测试脚本
# 使用 Apache Bench (ab) 进行负载测试

set -e

# 配置变量
NAMESPACE=${1:-"paas-platform"}
SERVICE=${2:-"api-gateway"}
CONCURRENT_USERS=${3:-10}
TOTAL_REQUESTS=${4:-1000}
TEST_DURATION=${5:-60}

echo "开始性能测试..."
echo "命名空间: $NAMESPACE"
echo "服务: $SERVICE"
echo "并发用户: $CONCURRENT_USERS"
echo "总请求数: $TOTAL_REQUESTS"
echo "测试时长: ${TEST_DURATION}秒"
echo ""

# 获取服务端点
SERVICE_IP=$(kubectl get service "$SERVICE" -n "$NAMESPACE" -o jsonpath='{.spec.clusterIP}')
SERVICE_PORT=$(kubectl get service "$SERVICE" -n "$NAMESPACE" -o jsonpath='{.spec.ports[0].port}')

if [[ -z "$SERVICE_IP" || -z "$SERVICE_PORT" ]]; then
    echo "错误: 无法获取服务端点"
    exit 1
fi

SERVICE_URL="http://$SERVICE_IP:$SERVICE_PORT"

echo "测试端点: $SERVICE_URL"
echo ""

# 创建测试 Pod
kubectl run performance-test \
    --image=httpd:alpine \
    --rm -i --restart=Never \
    --namespace="$NAMESPACE" \
    --command -- sh -c "
        apk add --no-cache apache2-utils curl
        
        echo '开始预热测试...'
        curl -s '$SERVICE_URL/health' > /dev/null || echo '健康检查失败'
        
        echo '执行负载测试...'
        ab -n $TOTAL_REQUESTS -c $CONCURRENT_USERS -g results.tsv '$SERVICE_URL/'
        
        echo '测试完成'
        cat results.tsv
    "

echo ""
echo "性能测试完成"
EOF

    chmod +x "$test_script"
    print_success "性能测试脚本已生成: $test_script"
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台性能分析工具"
    echo ""
    echo "用法: $0 [选项] [命名空间]"
    echo ""
    echo "选项:"
    echo "  -a, --all               执行完整性能分析"
    echo "  -c, --cpu               分析 CPU 性能"
    echo "  -m, --memory            分析内存性能"
    echo "  -n, --network           分析网络性能"
    echo "  -s, --storage           分析存储性能"
    echo "  -r, --recommendations   生成优化建议"
    echo "  -t, --test              生成性能测试脚本"
    echo "  --help                  显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -a paas-production   # 完整分析生产环境"
    echo "  $0 -c paas-staging      # 分析测试环境 CPU 性能"
    echo "  $0 -r paas-platform     # 生成优化建议"
}

# 主函数
main() {
    local namespace="paas-platform"
    local analyze_all=false
    local analyze_cpu=false
    local analyze_memory=false
    local analyze_network=false
    local analyze_storage=false
    local generate_recommendations=false
    local generate_test=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--all)
                analyze_all=true
                shift
                ;;
            -c|--cpu)
                analyze_cpu=true
                shift
                ;;
            -m|--memory)
                analyze_memory=true
                shift
                ;;
            -n|--network)
                analyze_network=true
                shift
                ;;
            -s|--storage)
                analyze_storage=true
                shift
                ;;
            -r|--recommendations)
                generate_recommendations=true
                shift
                ;;
            -t|--test)
                generate_test=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                namespace="$1"
                shift
                ;;
        esac
    done
    
    # 创建分析目录
    create_analysis_dir
    
    # 初始化报告文件
    {
        echo "# PaaS 平台性能分析报告"
        echo ""
        echo "**生成时间:** $(date)"
        echo "**命名空间:** $namespace"
        echo "**分析工具:** PaaS Platform Performance Analyzer"
        echo ""
        echo "---"
        echo ""
    } > "$REPORT_FILE"
    
    print_info "开始性能分析，命名空间: $namespace"
    
    # 执行分析
    if [[ "$analyze_all" == "true" ]]; then
        analyze_cpu_performance "$namespace"
        analyze_memory_performance "$namespace"
        analyze_network_performance "$namespace"
        analyze_storage_performance "$namespace"
        analyze_application_metrics "$namespace"
        generate_optimization_recommendations "$namespace"
    else
        if [[ "$analyze_cpu" == "true" ]]; then
            analyze_cpu_performance "$namespace"
        fi
        
        if [[ "$analyze_memory" == "true" ]]; then
            analyze_memory_performance "$namespace"
        fi
        
        if [[ "$analyze_network" == "true" ]]; then
            analyze_network_performance "$namespace"
        fi
        
        if [[ "$analyze_storage" == "true" ]]; then
            analyze_storage_performance "$namespace"
        fi
        
        if [[ "$generate_recommendations" == "true" ]]; then
            generate_optimization_recommendations "$namespace"
        fi
    fi
    
    if [[ "$generate_test" == "true" ]]; then
        generate_performance_test
    fi
    
    # 如果没有指定任何选项，执行默认分析
    if [[ "$analyze_all" == "false" && "$analyze_cpu" == "false" && "$analyze_memory" == "false" && 
          "$analyze_network" == "false" && "$analyze_storage" == "false" && 
          "$generate_recommendations" == "false" && "$generate_test" == "false" ]]; then
        analyze_cpu_performance "$namespace"
        analyze_memory_performance "$namespace"
        generate_optimization_recommendations "$namespace"
    fi
    
    print_success "性能分析完成"
    print_info "分析报告: $REPORT_FILE"
}

# 脚本入口
main "$@"
