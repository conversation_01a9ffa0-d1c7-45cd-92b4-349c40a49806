#!/bin/bash

# Docker指标导出器
# 收集Docker存储相关指标并导出为Prometheus格式

set -euo pipefail

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly METRICS_FILE="/tmp/docker_storage_metrics.prom"
readonly METRICS_PORT="${METRICS_PORT:-9100}"
readonly METRICS_PATH="${METRICS_PATH:-/metrics/docker}"
readonly LOG_FILE="$PROJECT_ROOT/logs/docker-metrics-exporter.log"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOG_FILE"
}

# 初始化环境
init_environment() {
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$(dirname "$METRICS_FILE")"
    
    # 清空指标文件
    > "$METRICS_FILE"
    
    log_info "Docker指标导出器启动"
}

# 收集Docker基础指标
collect_docker_basic_metrics() {
    log_info "收集Docker基础指标"
    
    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，无法收集指标"
        return 1
    fi
    
    # 镜像指标
    local images_total=$(docker images -q | wc -l)
    local images_dangling=$(docker images -f "dangling=true" -q | wc -l)
    local images_size=$(docker images --format "table {{.Size}}" | tail -n +2 | \
        awk '{
            if ($1 ~ /GB/) size += $1 * 1024 * 1024 * 1024
            else if ($1 ~ /MB/) size += $1 * 1024 * 1024
            else if ($1 ~ /KB/) size += $1 * 1024
            else size += $1
        } END {print int(size)}' 2>/dev/null || echo "0")
    
    # 容器指标
    local containers_total=$(docker ps -aq | wc -l)
    local containers_running=$(docker ps -q | wc -l)
    local containers_stopped=$(docker ps -aq --filter "status=exited" | wc -l)
    local containers_paused=$(docker ps -aq --filter "status=paused" | wc -l)
    
    # 数据卷指标
    local volumes_total=$(docker volume ls -q | wc -l)
    local volumes_dangling=$(docker volume ls -qf dangling=true | wc -l)
    
    # 网络指标
    local networks_total=$(docker network ls -q | wc -l)
    local networks_custom=$(docker network ls --filter "type=custom" -q | wc -l)
    
    # 写入指标
    cat >> "$METRICS_FILE" << EOF
# HELP docker_images_total Total number of Docker images
# TYPE docker_images_total gauge
docker_images_total $images_total

# HELP docker_images_dangling_total Number of dangling Docker images
# TYPE docker_images_dangling_total gauge
docker_images_dangling_total $images_dangling

# HELP docker_images_size_bytes Total size of Docker images in bytes
# TYPE docker_images_size_bytes gauge
docker_images_size_bytes $images_size

# HELP docker_containers_total Total number of Docker containers
# TYPE docker_containers_total gauge
docker_containers_total $containers_total

# HELP docker_containers_running_total Number of running Docker containers
# TYPE docker_containers_running_total gauge
docker_containers_running_total $containers_running

# HELP docker_containers_stopped_total Number of stopped Docker containers
# TYPE docker_containers_stopped_total gauge
docker_containers_stopped_total $containers_stopped

# HELP docker_containers_paused_total Number of paused Docker containers
# TYPE docker_containers_paused_total gauge
docker_containers_paused_total $containers_paused

# HELP docker_volumes_total Total number of Docker volumes
# TYPE docker_volumes_total gauge
docker_volumes_total $volumes_total

# HELP docker_volumes_dangling_total Number of dangling Docker volumes
# TYPE docker_volumes_dangling_total gauge
docker_volumes_dangling_total $volumes_dangling

# HELP docker_networks_total Total number of Docker networks
# TYPE docker_networks_total gauge
docker_networks_total $networks_total

# HELP docker_networks_custom_total Number of custom Docker networks
# TYPE docker_networks_custom_total gauge
docker_networks_custom_total $networks_custom

EOF
}

# 收集Docker存储指标
collect_docker_storage_metrics() {
    log_info "收集Docker存储指标"
    
    # 获取Docker系统信息
    local docker_info=$(docker system df --format "{{.Type}}\t{{.TotalCount}}\t{{.Active}}\t{{.Size}}\t{{.Reclaimable}}" 2>/dev/null)
    
    # 解析存储信息
    while IFS=$'\t' read -r type total active size reclaimable; do
        case "$type" in
            "Images")
                local images_active=$active
                local images_size_str=$size
                local images_reclaimable_str=$reclaimable
                ;;
            "Containers")
                local containers_active=$active
                local containers_size_str=$size
                local containers_reclaimable_str=$reclaimable
                ;;
            "Local Volumes")
                local volumes_active=$active
                local volumes_size_str=$size
                local volumes_reclaimable_str=$reclaimable
                ;;
            "Build Cache")
                local cache_size_str=$size
                local cache_reclaimable_str=$reclaimable
                ;;
        esac
    done <<< "$docker_info"
    
    # 转换大小为字节
    convert_size_to_bytes() {
        local size_str="$1"
        if [[ "$size_str" == *"GB"* ]]; then
            echo "$size_str" | sed 's/GB//' | awk '{print int($1 * 1024 * 1024 * 1024)}'
        elif [[ "$size_str" == *"MB"* ]]; then
            echo "$size_str" | sed 's/MB//' | awk '{print int($1 * 1024 * 1024)}'
        elif [[ "$size_str" == *"KB"* ]]; then
            echo "$size_str" | sed 's/KB//' | awk '{print int($1 * 1024)}'
        else
            echo "0"
        fi
    }
    
    local images_size_bytes=$(convert_size_to_bytes "${images_size_str:-0}")
    local containers_size_bytes=$(convert_size_to_bytes "${containers_size_str:-0}")
    local volumes_size_bytes=$(convert_size_to_bytes "${volumes_size_str:-0}")
    local cache_size_bytes=$(convert_size_to_bytes "${cache_size_str:-0}")
    
    local images_reclaimable_bytes=$(convert_size_to_bytes "${images_reclaimable_str:-0}")
    local containers_reclaimable_bytes=$(convert_size_to_bytes "${containers_reclaimable_str:-0}")
    local volumes_reclaimable_bytes=$(convert_size_to_bytes "${volumes_reclaimable_str:-0}")
    local cache_reclaimable_bytes=$(convert_size_to_bytes "${cache_reclaimable_str:-0}")
    
    # 写入存储指标
    cat >> "$METRICS_FILE" << EOF
# HELP docker_images_active_total Number of active Docker images
# TYPE docker_images_active_total gauge
docker_images_active_total ${images_active:-0}

# HELP docker_containers_active_total Number of active Docker containers
# TYPE docker_containers_active_total gauge
docker_containers_active_total ${containers_active:-0}

# HELP docker_volumes_active_total Number of active Docker volumes
# TYPE docker_volumes_active_total gauge
docker_volumes_active_total ${volumes_active:-0}

# HELP docker_containers_size_bytes Total size of Docker containers in bytes
# TYPE docker_containers_size_bytes gauge
docker_containers_size_bytes $containers_size_bytes

# HELP docker_volumes_size_bytes Total size of Docker volumes in bytes
# TYPE docker_volumes_size_bytes gauge
docker_volumes_size_bytes $volumes_size_bytes

# HELP docker_build_cache_size_bytes Total size of Docker build cache in bytes
# TYPE docker_build_cache_size_bytes gauge
docker_build_cache_size_bytes $cache_size_bytes

# HELP docker_images_reclaimable_bytes Reclaimable space from Docker images in bytes
# TYPE docker_images_reclaimable_bytes gauge
docker_images_reclaimable_bytes $images_reclaimable_bytes

# HELP docker_containers_reclaimable_bytes Reclaimable space from Docker containers in bytes
# TYPE docker_containers_reclaimable_bytes gauge
docker_containers_reclaimable_bytes $containers_reclaimable_bytes

# HELP docker_volumes_reclaimable_bytes Reclaimable space from Docker volumes in bytes
# TYPE docker_volumes_reclaimable_bytes gauge
docker_volumes_reclaimable_bytes $volumes_reclaimable_bytes

# HELP docker_build_cache_reclaimable_bytes Reclaimable space from Docker build cache in bytes
# TYPE docker_build_cache_reclaimable_bytes gauge
docker_build_cache_reclaimable_bytes $cache_reclaimable_bytes

EOF
}

# 收集Docker性能指标
collect_docker_performance_metrics() {
    log_info "收集Docker性能指标"
    
    # Docker daemon进程信息
    local docker_pid=$(pgrep -f "dockerd" | head -1)
    
    if [[ -n "$docker_pid" ]]; then
        # CPU使用率
        local cpu_usage=$(ps -p "$docker_pid" -o %cpu --no-headers | tr -d ' ')
        
        # 内存使用
        local memory_usage=$(ps -p "$docker_pid" -o rss --no-headers | tr -d ' ')
        local memory_bytes=$((memory_usage * 1024))
        
        # 文件描述符数量
        local fd_count=$(ls /proc/"$docker_pid"/fd 2>/dev/null | wc -l)
        
        cat >> "$METRICS_FILE" << EOF
# HELP docker_daemon_cpu_usage_percent Docker daemon CPU usage percentage
# TYPE docker_daemon_cpu_usage_percent gauge
docker_daemon_cpu_usage_percent ${cpu_usage:-0}

# HELP docker_daemon_memory_usage_bytes Docker daemon memory usage in bytes
# TYPE docker_daemon_memory_usage_bytes gauge
docker_daemon_memory_usage_bytes $memory_bytes

# HELP docker_daemon_file_descriptors Docker daemon open file descriptors
# TYPE docker_daemon_file_descriptors gauge
docker_daemon_file_descriptors $fd_count

EOF
    fi
    
    # Docker根目录磁盘使用情况
    local docker_root=$(docker info --format '{{.DockerRootDir}}' 2>/dev/null || echo "/var/lib/docker")
    
    if [[ -d "$docker_root" ]]; then
        local disk_info=$(df "$docker_root" | tail -1)
        local total_bytes=$(echo "$disk_info" | awk '{print $2 * 1024}')
        local used_bytes=$(echo "$disk_info" | awk '{print $3 * 1024}')
        local available_bytes=$(echo "$disk_info" | awk '{print $4 * 1024}')
        local usage_percent=$(echo "$disk_info" | awk '{print $5}' | sed 's/%//')
        
        cat >> "$METRICS_FILE" << EOF
# HELP docker_root_filesystem_size_bytes Docker root filesystem size in bytes
# TYPE docker_root_filesystem_size_bytes gauge
docker_root_filesystem_size_bytes $total_bytes

# HELP docker_root_filesystem_used_bytes Docker root filesystem used space in bytes
# TYPE docker_root_filesystem_used_bytes gauge
docker_root_filesystem_used_bytes $used_bytes

# HELP docker_root_filesystem_available_bytes Docker root filesystem available space in bytes
# TYPE docker_root_filesystem_available_bytes gauge
docker_root_filesystem_available_bytes $available_bytes

# HELP docker_root_filesystem_usage_percent Docker root filesystem usage percentage
# TYPE docker_root_filesystem_usage_percent gauge
docker_root_filesystem_usage_percent $usage_percent

EOF
    fi
}

# 收集清理相关指标
collect_cleanup_metrics() {
    log_info "收集清理相关指标"
    
    # 检查是否有清理历史记录
    local cleanup_log="$PROJECT_ROOT/logs/docker-cleanup.log"
    local last_cleanup_timestamp=0
    
    if [[ -f "$cleanup_log" ]]; then
        # 获取最后一次清理时间
        local last_cleanup=$(tail -1 "$cleanup_log" 2>/dev/null | grep -o '\[.*\]' | tr -d '[]' || echo "")
        if [[ -n "$last_cleanup" ]]; then
            last_cleanup_timestamp=$(date -d "$last_cleanup" +%s 2>/dev/null || echo "0")
        fi
    fi
    
    # 计算需要清理的资源数量
    local unused_images=$(docker images -f "dangling=false" --format "{{.Repository}}:{{.Tag}}" | \
        while read -r image; do
            if ! docker ps -a --format "{{.Image}}" | grep -q "$image"; then
                echo "$image"
            fi
        done | wc -l)
    
    local unused_volumes=$(docker volume ls -qf dangling=true | wc -l)
    local unused_networks=$(docker network ls --filter "type=custom" -q | \
        while read -r network; do
            local containers=$(docker network inspect "$network" --format '{{len .Containers}}' 2>/dev/null || echo "1")
            if [[ "$containers" == "0" ]]; then
                echo "$network"
            fi
        done | wc -l)
    
    cat >> "$METRICS_FILE" << EOF
# HELP docker_cleanup_last_run_timestamp Timestamp of last Docker cleanup run
# TYPE docker_cleanup_last_run_timestamp gauge
docker_cleanup_last_run_timestamp $last_cleanup_timestamp

# HELP docker_images_unused_total Number of unused Docker images
# TYPE docker_images_unused_total gauge
docker_images_unused_total $unused_images

# HELP docker_volumes_unused_total Number of unused Docker volumes
# TYPE docker_volumes_unused_total gauge
docker_volumes_unused_total $unused_volumes

# HELP docker_networks_unused_total Number of unused Docker networks
# TYPE docker_networks_unused_total gauge
docker_networks_unused_total $unused_networks

EOF
}

# 启动HTTP服务器提供指标
start_metrics_server() {
    log_info "启动指标服务器在端口 $METRICS_PORT"
    
    # 创建简单的HTTP服务器
    while true; do
        # 更新指标
        collect_all_metrics
        
        # 启动HTTP服务器（使用nc或python）
        if command -v nc >/dev/null 2>&1; then
            # 使用netcat
            while true; do
                echo -e "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\n\r\n$(cat "$METRICS_FILE")" | nc -l -p "$METRICS_PORT" -q 1
            done &
        elif command -v python3 >/dev/null 2>&1; then
            # 使用Python HTTP服务器
            python3 -c "
import http.server
import socketserver
import os

class MetricsHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '$METRICS_PATH':
            self.send_response(200)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            with open('$METRICS_FILE', 'r') as f:
                self.wfile.write(f.read().encode())
        else:
            self.send_response(404)
            self.end_headers()

with socketserver.TCPServer(('', $METRICS_PORT), MetricsHandler) as httpd:
    httpd.serve_forever()
" &
        else
            log_error "无法启动HTTP服务器，需要nc或python3"
            return 1
        fi
        
        # 等待30秒后更新指标
        sleep 30
    done
}

# 收集所有指标
collect_all_metrics() {
    # 清空指标文件
    > "$METRICS_FILE"
    
    # 添加指标头部信息
    cat >> "$METRICS_FILE" << EOF
# Docker Storage Metrics
# Generated by docker-metrics-exporter.sh
# Timestamp: $(date)

EOF
    
    # 收集各类指标
    collect_docker_basic_metrics
    collect_docker_storage_metrics
    collect_docker_performance_metrics
    collect_cleanup_metrics
    
    log_info "指标收集完成，共 $(grep -c "^[^#]" "$METRICS_FILE") 个指标"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker指标导出器

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -o, --once          仅收集一次指标并退出
  -s, --server        启动HTTP服务器模式
  -p, --port PORT     指定HTTP服务器端口 (默认: $METRICS_PORT)
  -f, --file FILE     指定输出文件 (默认: $METRICS_FILE)

示例:
  $0 --once           # 收集一次指标
  $0 --server         # 启动HTTP服务器
  $0 -p 9200          # 在端口9200启动服务器

EOF
}

# 主函数
main() {
    local mode="once"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -o|--once)
                mode="once"
                shift
                ;;
            -s|--server)
                mode="server"
                shift
                ;;
            -p|--port)
                METRICS_PORT="$2"
                shift 2
                ;;
            -f|--file)
                METRICS_FILE="$2"
                shift 2
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 初始化环境
    init_environment
    
    # 执行相应模式
    case "$mode" in
        "once")
            collect_all_metrics
            log_info "指标已保存到: $METRICS_FILE"
            ;;
        "server")
            start_metrics_server
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
