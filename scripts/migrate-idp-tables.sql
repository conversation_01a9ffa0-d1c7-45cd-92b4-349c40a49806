-- IDP (Identity Provider) 混合认证数据库迁移脚本
-- 此脚本为现有的 PaaS 平台添加 IDP 集成支持
-- 执行前请备份数据库

-- ============================================================================
-- 1. 更新现有用户表，添加 IDP 相关字段
-- ============================================================================

-- 添加 IDP 相关字段到用户表
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS is_idp_user BOOLEAN DEFAULT FALSE COMMENT '是否为 IDP 用户',
ADD COLUMN IF NOT EXISTS primary_idp_id VARCHAR(36) COMMENT '主要 IDP 账号 ID',
ADD COLUMN IF NOT EXISTS idp_sync_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用 IDP 信息同步';

-- 修改密码字段为可空（IDP 用户可能没有本地密码）
ALTER TABLE users 
MODIFY COLUMN password_hash VARCHAR(255) NULL COMMENT '密码哈希（IDP 用户可为空）';

-- ============================================================================
-- 2. 创建 IDP 提供商表
-- ============================================================================

CREATE TABLE IF NOT EXISTS idp_providers (
    id VARCHAR(36) PRIMARY KEY COMMENT 'IDP 提供商 ID',
    name VARCHAR(100) NOT NULL COMMENT 'IDP 提供商名称',
    type VARCHAR(20) NOT NULL COMMENT 'IDP 类型：oidc, oauth2, saml, ldap',
    enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用',
    config JSON NOT NULL COMMENT 'IDP 配置信息',
    mapping JSON NOT NULL COMMENT '属性映射配置',
    tenant_id VARCHAR(36) NOT NULL COMMENT '租户 ID',
    priority INT DEFAULT 0 COMMENT '优先级，数字越小优先级越高',
    auto_create_user BOOLEAN DEFAULT TRUE COMMENT '是否自动创建用户',
    auto_link_user BOOLEAN DEFAULT FALSE COMMENT '是否自动关联现有用户',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_idp_provider_name_tenant (name, tenant_id),
    INDEX idx_idp_provider_tenant (tenant_id),
    INDEX idx_idp_provider_enabled (enabled),
    INDEX idx_idp_provider_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IDP 提供商配置表';

-- ============================================================================
-- 3. 创建 IDP 账号关联表
-- ============================================================================

CREATE TABLE IF NOT EXISTS idp_accounts (
    id VARCHAR(36) PRIMARY KEY COMMENT 'IDP 账号关联 ID',
    user_id VARCHAR(36) NOT NULL COMMENT '本地用户 ID',
    idp_provider_id VARCHAR(36) NOT NULL COMMENT 'IDP 提供商 ID',
    idp_user_id VARCHAR(255) NOT NULL COMMENT 'IDP 中的用户 ID',
    idp_username VARCHAR(255) COMMENT 'IDP 中的用户名',
    idp_email VARCHAR(255) COMMENT 'IDP 中的邮箱',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active, inactive, revoked',
    last_sync_at TIMESTAMP NULL COMMENT '最后同步时间',
    sync_data JSON COMMENT '同步的数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_idp_account_provider_user (idp_provider_id, idp_user_id),
    INDEX idx_idp_account_user (user_id),
    INDEX idx_idp_account_provider (idp_provider_id),
    INDEX idx_idp_account_status (status),
    INDEX idx_idp_account_sync (last_sync_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (idp_provider_id) REFERENCES idp_providers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IDP 账号关联表';

-- ============================================================================
-- 4. 创建 IDP 会话表
-- ============================================================================

CREATE TABLE IF NOT EXISTS idp_sessions (
    id VARCHAR(36) PRIMARY KEY COMMENT 'IDP 会话 ID',
    user_id VARCHAR(36) NOT NULL COMMENT '用户 ID',
    idp_provider_id VARCHAR(36) NOT NULL COMMENT 'IDP 提供商 ID',
    idp_session_id VARCHAR(255) COMMENT 'IDP 会话 ID',
    access_token TEXT COMMENT '访问令牌（加密存储）',
    refresh_token TEXT COMMENT '刷新令牌（加密存储）',
    token_type VARCHAR(50) COMMENT '令牌类型',
    expires_at TIMESTAMP NULL COMMENT '令牌过期时间',
    scope VARCHAR(500) COMMENT '授权范围',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_idp_session_user (user_id),
    INDEX idx_idp_session_provider (idp_provider_id),
    INDEX idx_idp_session_expires (expires_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (idp_provider_id) REFERENCES idp_providers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IDP 会话表';

-- ============================================================================
-- 5. 创建 IDP 审计日志表
-- ============================================================================

CREATE TABLE IF NOT EXISTS idp_audit_logs (
    id VARCHAR(36) PRIMARY KEY COMMENT '审计日志 ID',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型：login, sync, link, unlink',
    user_id VARCHAR(36) COMMENT '用户 ID',
    idp_provider_id VARCHAR(36) NOT NULL COMMENT 'IDP 提供商 ID',
    idp_user_id VARCHAR(255) COMMENT 'IDP 用户 ID',
    result VARCHAR(20) NOT NULL COMMENT '结果：success, failure',
    error_message TEXT COMMENT '错误消息',
    ip_address VARCHAR(45) COMMENT 'IP 地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    details JSON COMMENT '详细信息',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    
    INDEX idx_idp_audit_event (event_type),
    INDEX idx_idp_audit_user (user_id),
    INDEX idx_idp_audit_provider (idp_provider_id),
    INDEX idx_idp_audit_result (result),
    INDEX idx_idp_audit_timestamp (timestamp),
    INDEX idx_idp_audit_ip (ip_address),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (idp_provider_id) REFERENCES idp_providers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IDP 审计日志表';

-- ============================================================================
-- 6. 插入示例 IDP 提供商配置（可选）
-- ============================================================================

-- 插入示例 Azure AD 配置（默认禁用）
INSERT IGNORE INTO idp_providers (
    id, name, type, enabled, config, mapping, tenant_id, priority, auto_create_user, auto_link_user
) VALUES (
    'azure-ad-example', 
    'azure-ad', 
    'oidc', 
    FALSE,
    JSON_OBJECT(
        'client_id', 'your-azure-client-id',
        'client_secret', 'your-azure-client-secret',
        'issuer', 'https://login.microsoftonline.com/{tenant-id}/v2.0',
        'auth_url', 'https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/authorize',
        'token_url', 'https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/token',
        'user_info_url', 'https://graph.microsoft.com/v1.0/me',
        'jwks_url', 'https://login.microsoftonline.com/{tenant-id}/discovery/v2.0/keys',
        'scopes', JSON_ARRAY('openid', 'profile', 'email', 'User.Read'),
        'timeout', 30,
        'redirect_url', 'https://your-domain.com/api/v1/idp/callback/azure-ad'
    ),
    JSON_OBJECT(
        'user_id', 'id',
        'username', 'userPrincipalName',
        'email', 'mail',
        'first_name', 'givenName',
        'last_name', 'surname',
        'avatar', 'photo',
        'phone', 'mobilePhone',
        'roles', 'roles',
        'groups', 'groups'
    ),
    'default',
    1,
    TRUE,
    FALSE
);

-- ============================================================================
-- 7. 更新现有表的索引优化
-- ============================================================================

-- 为用户表添加 IDP 相关索引
CREATE INDEX IF NOT EXISTS idx_users_idp ON users(is_idp_user);
CREATE INDEX IF NOT EXISTS idx_users_primary_idp ON users(primary_idp_id);

-- ============================================================================
-- 8. 创建视图以便查询
-- ============================================================================

-- 创建用户 IDP 账号视图
CREATE OR REPLACE VIEW v_user_idp_accounts AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    u.is_idp_user,
    u.primary_idp_id,
    ia.id as idp_account_id,
    ia.idp_user_id,
    ia.idp_username,
    ia.idp_email,
    ia.status as idp_status,
    ia.last_sync_at,
    ip.name as provider_name,
    ip.type as provider_type,
    ip.enabled as provider_enabled
FROM users u
LEFT JOIN idp_accounts ia ON u.id = ia.user_id
LEFT JOIN idp_providers ip ON ia.idp_provider_id = ip.id;

-- ============================================================================
-- 9. 数据完整性检查和修复
-- ============================================================================

-- 检查并修复孤立的 primary_idp_id 引用
UPDATE users 
SET primary_idp_id = NULL 
WHERE primary_idp_id IS NOT NULL 
AND primary_idp_id NOT IN (SELECT id FROM idp_accounts);

-- ============================================================================
-- 迁移完成
-- ============================================================================

-- 记录迁移完成
INSERT INTO audit_logs (
    id, event_type, event_name, result, details, timestamp
) VALUES (
    UUID(),
    'system',
    'idp_migration_completed',
    'success',
    JSON_OBJECT(
        'migration_version', '1.0.0',
        'tables_created', JSON_ARRAY('idp_providers', 'idp_accounts', 'idp_sessions', 'idp_audit_logs'),
        'views_created', JSON_ARRAY('v_user_idp_accounts')
    ),
    NOW()
);

-- 显示迁移结果
SELECT 'IDP 混合认证数据库迁移完成' as status,
       'idp_providers, idp_accounts, idp_sessions, idp_audit_logs' as tables_created,
       'v_user_idp_accounts' as views_created;
