#!/bin/bash

# 性能优化脚本
# 自动分析和优化 PaaS 平台性能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE=${NAMESPACE:-"paas-prod"}
OPTIMIZATION_REPORT="$PROJECT_ROOT/performance-optimization-$(date +%Y%m%d_%H%M%S).md"

echo -e "${PURPLE}⚡ PaaS 平台性能优化工具${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：初始化报告
init_report() {
    cat > "$OPTIMIZATION_REPORT" <<EOF
# PaaS 平台性能优化报告

生成时间: $(date)
命名空间: $NAMESPACE
集群: $(kubectl config current-context)

## 执行摘要

本报告分析了 PaaS 平台的性能状况，并提供了优化建议。

## 分析结果

EOF
}

# 函数：添加到报告
add_to_report() {
    echo "$1" >> "$OPTIMIZATION_REPORT"
}

# 函数：分析资源使用情况
analyze_resource_usage() {
    print_title "分析资源使用情况"
    
    add_to_report "### 资源使用分析"
    add_to_report ""
    
    # 检查节点资源使用
    print_info "检查节点资源使用..."
    local node_usage=$(kubectl top nodes 2>/dev/null || echo "metrics-server 不可用")
    
    add_to_report "#### 节点资源使用"
    add_to_report '```'
    add_to_report "$node_usage"
    add_to_report '```'
    add_to_report ""
    
    # 检查 Pod 资源使用
    print_info "检查 Pod 资源使用..."
    local pod_usage=$(kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "metrics-server 不可用")
    
    add_to_report "#### Pod 资源使用"
    add_to_report '```'
    add_to_report "$pod_usage"
    add_to_report '```'
    add_to_report ""
    
    # 分析资源请求和限制
    print_info "分析资源请求和限制..."
    local resource_analysis=""
    
    kubectl get pods -n "$NAMESPACE" -o json | jq -r '
        .items[] | 
        select(.status.phase == "Running") |
        {
            name: .metadata.name,
            containers: [
                .spec.containers[] | {
                    name: .name,
                    requests: .resources.requests // {},
                    limits: .resources.limits // {}
                }
            ]
        }
    ' > /tmp/resource_analysis.json
    
    add_to_report "#### 资源配置分析"
    add_to_report ""
    
    # 检查没有设置资源限制的容器
    local no_limits=$(kubectl get pods -n "$NAMESPACE" -o json | jq -r '
        .items[] | 
        select(.status.phase == "Running") |
        select(.spec.containers[] | .resources.limits == null) |
        .metadata.name
    ')
    
    if [[ -n "$no_limits" ]]; then
        add_to_report "**⚠️ 警告：以下 Pod 没有设置资源限制：**"
        add_to_report '```'
        add_to_report "$no_limits"
        add_to_report '```'
        add_to_report ""
        print_warning "发现 $(echo "$no_limits" | wc -l) 个 Pod 没有设置资源限制"
    fi
    
    # 检查资源使用率
    if command -v kubectl &> /dev/null && kubectl top pods -n "$NAMESPACE" &> /dev/null; then
        local high_cpu_pods=$(kubectl top pods -n "$NAMESPACE" --no-headers | awk '$2 ~ /[0-9]+m/ && $2+0 > 500 {print $1, $2}')
        local high_memory_pods=$(kubectl top pods -n "$NAMESPACE" --no-headers | awk '$3 ~ /[0-9]+Mi/ && $3+0 > 1000 {print $1, $3}')
        
        if [[ -n "$high_cpu_pods" ]]; then
            add_to_report "**⚠️ 高 CPU 使用率 Pod (>500m)：**"
            add_to_report '```'
            add_to_report "$high_cpu_pods"
            add_to_report '```'
            add_to_report ""
        fi
        
        if [[ -n "$high_memory_pods" ]]; then
            add_to_report "**⚠️ 高内存使用率 Pod (>1000Mi)：**"
            add_to_report '```'
            add_to_report "$high_memory_pods"
            add_to_report '```'
            add_to_report ""
        fi
    fi
    
    print_success "资源使用分析完成"
}

# 函数：分析网络性能
analyze_network_performance() {
    print_title "分析网络性能"
    
    add_to_report "### 网络性能分析"
    add_to_report ""
    
    # 检查服务端点
    print_info "检查服务端点..."
    local services_without_endpoints=""
    
    kubectl get services -n "$NAMESPACE" --no-headers | while read -r line; do
        local service_name=$(echo "$line" | awk '{print $1}')
        local endpoints=$(kubectl get endpoints "$service_name" -n "$NAMESPACE" -o jsonpath='{.subsets[*].addresses[*].ip}' 2>/dev/null)
        
        if [[ -z "$endpoints" ]]; then
            services_without_endpoints="$services_without_endpoints\n$service_name"
        fi
    done
    
    if [[ -n "$services_without_endpoints" ]]; then
        add_to_report "**⚠️ 没有端点的服务：**"
        add_to_report '```'
        add_to_report "$services_without_endpoints"
        add_to_report '```'
        add_to_report ""
    fi
    
    # 检查网络策略
    print_info "检查网络策略..."
    local network_policies=$(kubectl get networkpolicies -n "$NAMESPACE" --no-headers | wc -l)
    
    add_to_report "#### 网络策略状态"
    add_to_report "- 网络策略数量: $network_policies"
    
    if [[ $network_policies -eq 0 ]]; then
        add_to_report "- **⚠️ 建议：启用网络策略以提高安全性和网络隔离**"
    fi
    add_to_report ""
    
    print_success "网络性能分析完成"
}

# 函数：分析存储性能
analyze_storage_performance() {
    print_title "分析存储性能"
    
    add_to_report "### 存储性能分析"
    add_to_report ""
    
    # 检查 PVC 状态
    print_info "检查持久卷状态..."
    local pvc_status=$(kubectl get pvc -n "$NAMESPACE" --no-headers 2>/dev/null)
    
    if [[ -n "$pvc_status" ]]; then
        add_to_report "#### 持久卷状态"
        add_to_report '```'
        add_to_report "$pvc_status"
        add_to_report '```'
        add_to_report ""
        
        # 检查未绑定的 PVC
        local unbound_pvcs=$(echo "$pvc_status" | awk '$2 != "Bound" {print $1, $2}')
        if [[ -n "$unbound_pvcs" ]]; then
            add_to_report "**⚠️ 未绑定的 PVC：**"
            add_to_report '```'
            add_to_report "$unbound_pvcs"
            add_to_report '```'
            add_to_report ""
        fi
    else
        add_to_report "#### 持久卷状态"
        add_to_report "- 没有找到持久卷声明"
        add_to_report ""
    fi
    
    # 检查存储类
    print_info "检查存储类配置..."
    local storage_classes=$(kubectl get storageclass --no-headers)
    
    add_to_report "#### 可用存储类"
    add_to_report '```'
    add_to_report "$storage_classes"
    add_to_report '```'
    add_to_report ""
    
    print_success "存储性能分析完成"
}

# 函数：分析应用性能
analyze_application_performance() {
    print_title "分析应用性能"
    
    add_to_report "### 应用性能分析"
    add_to_report ""
    
    # 检查 Pod 重启次数
    print_info "检查 Pod 重启情况..."
    local high_restart_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | awk '$4 > 5 {print $1, $4}')
    
    if [[ -n "$high_restart_pods" ]]; then
        add_to_report "**⚠️ 高重启次数 Pod (>5次)：**"
        add_to_report '```'
        add_to_report "$high_restart_pods"
        add_to_report '```'
        add_to_report ""
        print_warning "发现高重启次数的 Pod"
    fi
    
    # 检查就绪探针和存活探针
    print_info "检查健康检查配置..."
    local pods_without_probes=""
    
    kubectl get pods -n "$NAMESPACE" -o json | jq -r '
        .items[] | 
        select(.status.phase == "Running") |
        select(.spec.containers[] | (.livenessProbe == null or .readinessProbe == null)) |
        .metadata.name
    ' > /tmp/pods_without_probes.txt
    
    if [[ -s /tmp/pods_without_probes.txt ]]; then
        pods_without_probes=$(cat /tmp/pods_without_probes.txt)
        add_to_report "**⚠️ 缺少健康检查探针的 Pod：**"
        add_to_report '```'
        add_to_report "$pods_without_probes"
        add_to_report '```'
        add_to_report ""
    fi
    
    # 检查 HPA 配置
    print_info "检查自动扩缩配置..."
    local hpa_count=$(kubectl get hpa -n "$NAMESPACE" --no-headers 2>/dev/null | wc -l)
    
    add_to_report "#### 自动扩缩配置"
    add_to_report "- HPA 数量: $hpa_count"
    
    if [[ $hpa_count -eq 0 ]]; then
        add_to_report "- **💡 建议：为高负载服务配置 HPA**"
    else
        local hpa_status=$(kubectl get hpa -n "$NAMESPACE" --no-headers)
        add_to_report '```'
        add_to_report "$hpa_status"
        add_to_report '```'
    fi
    add_to_report ""
    
    print_success "应用性能分析完成"
}

# 函数：生成优化建议
generate_optimization_recommendations() {
    print_title "生成优化建议"
    
    add_to_report "## 优化建议"
    add_to_report ""
    
    # 资源优化建议
    add_to_report "### 资源优化"
    add_to_report ""
    add_to_report "1. **设置资源限制**"
    add_to_report "   - 为所有容器设置 CPU 和内存限制"
    add_to_report "   - 使用 VPA (Vertical Pod Autoscaler) 自动调整资源请求"
    add_to_report ""
    add_to_report "2. **优化资源请求**"
    add_to_report "   - 根据实际使用情况调整资源请求值"
    add_to_report "   - 避免过度分配资源"
    add_to_report ""
    
    # 性能优化建议
    add_to_report "### 性能优化"
    add_to_report ""
    add_to_report "1. **启用缓存**"
    add_to_report "   - 配置 Redis 缓存以减少数据库负载"
    add_to_report "   - 使用 CDN 加速静态资源"
    add_to_report ""
    add_to_report "2. **数据库优化**"
    add_to_report "   - 优化数据库查询和索引"
    add_to_report "   - 配置连接池以减少连接开销"
    add_to_report ""
    add_to_report "3. **网络优化**"
    add_to_report "   - 启用 gRPC 或 HTTP/2 以提高通信效率"
    add_to_report "   - 配置服务网格以优化服务间通信"
    add_to_report ""
    
    # 扩展性建议
    add_to_report "### 扩展性优化"
    add_to_report ""
    add_to_report "1. **自动扩缩**"
    add_to_report "   - 配置 HPA 基于 CPU/内存使用率自动扩缩"
    add_to_report "   - 配置 VPA 自动调整资源请求"
    add_to_report "   - 考虑使用 KEDA 基于自定义指标扩缩"
    add_to_report ""
    add_to_report "2. **负载均衡**"
    add_to_report "   - 优化负载均衡算法"
    add_to_report "   - 配置会话亲和性（如需要）"
    add_to_report ""
    
    # 监控建议
    add_to_report "### 监控和观察性"
    add_to_report ""
    add_to_report "1. **指标收集**"
    add_to_report "   - 配置详细的应用指标"
    add_to_report "   - 设置性能基线和告警阈值"
    add_to_report ""
    add_to_report "2. **分布式追踪**"
    add_to_report "   - 实施分布式追踪以识别性能瓶颈"
    add_to_report "   - 分析请求延迟和错误率"
    add_to_report ""
    
    print_success "优化建议生成完成"
}

# 函数：应用自动优化
apply_auto_optimizations() {
    print_title "应用自动优化"
    
    print_warning "这将修改现有的 Kubernetes 资源配置"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "自动优化已取消"
        return 0
    fi
    
    # 1. 为没有资源限制的 Pod 添加默认限制
    print_info "为 Pod 添加默认资源限制..."
    
    kubectl get deployments -n "$NAMESPACE" -o json | jq -r '
        .items[] | 
        select(.spec.template.spec.containers[] | .resources.limits == null) |
        .metadata.name
    ' | while read -r deployment; do
        if [[ -n "$deployment" ]]; then
            print_info "为 Deployment $deployment 添加资源限制..."
            kubectl patch deployment "$deployment" -n "$NAMESPACE" --type='merge' -p='{
                "spec": {
                    "template": {
                        "spec": {
                            "containers": [{
                                "name": "'$(kubectl get deployment "$deployment" -n "$NAMESPACE" -o jsonpath='{.spec.template.spec.containers[0].name}')'",
                                "resources": {
                                    "requests": {
                                        "memory": "256Mi",
                                        "cpu": "250m"
                                    },
                                    "limits": {
                                        "memory": "512Mi",
                                        "cpu": "500m"
                                    }
                                }
                            }]
                        }
                    }
                }
            }'
        fi
    done
    
    # 2. 为高负载服务创建 HPA
    print_info "为高负载服务创建 HPA..."
    
    local high_load_services=("api-gateway" "user-service" "app-manager")
    
    for service in "${high_load_services[@]}"; do
        if kubectl get deployment "$service" -n "$NAMESPACE" &>/dev/null; then
            if ! kubectl get hpa "$service-hpa" -n "$NAMESPACE" &>/dev/null; then
                print_info "为 $service 创建 HPA..."
                kubectl autoscale deployment "$service" -n "$NAMESPACE" \
                    --cpu-percent=70 \
                    --min=2 \
                    --max=10 \
                    --name="$service-hpa"
            fi
        fi
    done
    
    # 3. 优化 Pod 中断预算
    print_info "创建 Pod 中断预算..."
    
    for service in "${high_load_services[@]}"; do
        if kubectl get deployment "$service" -n "$NAMESPACE" &>/dev/null; then
            if ! kubectl get pdb "$service-pdb" -n "$NAMESPACE" &>/dev/null; then
                cat <<EOF | kubectl apply -f -
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: $service-pdb
  namespace: $NAMESPACE
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: $service
EOF
                print_info "为 $service 创建 PDB"
            fi
        fi
    done
    
    print_success "自动优化应用完成"
}

# 函数：性能测试
run_performance_test() {
    print_title "运行性能测试"
    
    # 检查是否有可用的性能测试工具
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl 未安装，无法运行性能测试"
        return 1
    fi
    
    print_info "创建性能测试 Pod..."
    
    # 创建简单的负载测试
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: performance-test
  namespace: $NAMESPACE
spec:
  containers:
  - name: curl
    image: curlimages/curl:latest
    command: ['sh', '-c']
    args:
    - |
      echo "开始性能测试..."
      
      # 测试 API Gateway
      echo "测试 API Gateway..."
      for i in {1..100}; do
        curl -s -w "%{time_total}\n" -o /dev/null http://api-gateway/health || echo "请求失败"
      done | awk '{sum+=\$1; count++} END {print "平均响应时间:", sum/count "秒"}'
      
      # 测试用户服务
      echo "测试用户服务..."
      for i in {1..100}; do
        curl -s -w "%{time_total}\n" -o /dev/null http://user-service/health || echo "请求失败"
      done | awk '{sum+=\$1; count++} END {print "平均响应时间:", sum/count "秒"}'
      
      echo "性能测试完成"
      sleep 3600
  restartPolicy: Never
EOF
    
    # 等待测试完成
    print_info "等待性能测试完成..."
    kubectl wait --for=condition=Ready pod/performance-test -n "$NAMESPACE" --timeout=60s
    
    # 获取测试结果
    sleep 10
    local test_results=$(kubectl logs performance-test -n "$NAMESPACE" 2>/dev/null || echo "无法获取测试结果")
    
    add_to_report "### 性能测试结果"
    add_to_report '```'
    add_to_report "$test_results"
    add_to_report '```'
    add_to_report ""
    
    # 清理测试 Pod
    kubectl delete pod performance-test -n "$NAMESPACE" --ignore-not-found=true
    
    print_success "性能测试完成"
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台性能优化工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -n, --namespace NS    指定 Kubernetes 命名空间"
    echo "  --analyze-only        仅分析，不应用优化"
    echo "  --auto-optimize       自动应用优化建议"
    echo "  --performance-test    运行性能测试"
    echo "  --help                显示帮助信息"
    echo ""
    echo "功能:"
    echo "  - 资源使用分析"
    echo "  - 网络性能分析"
    echo "  - 存储性能分析"
    echo "  - 应用性能分析"
    echo "  - 自动优化建议"
    echo "  - 性能测试"
    echo ""
    echo "示例:"
    echo "  $0                           # 完整分析和建议"
    echo "  $0 --analyze-only            # 仅分析"
    echo "  $0 --auto-optimize           # 自动应用优化"
    echo "  $0 --performance-test        # 运行性能测试"
}

# 主函数
main() {
    local analyze_only=false
    local auto_optimize=false
    local performance_test=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            --analyze-only)
                analyze_only=true
                shift
                ;;
            --auto-optimize)
                auto_optimize=true
                shift
                ;;
            --performance-test)
                performance_test=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "开始性能分析..."
    print_info "命名空间: $NAMESPACE"
    print_info "报告文件: $OPTIMIZATION_REPORT"
    echo ""
    
    # 初始化报告
    init_report
    
    # 执行分析
    analyze_resource_usage
    analyze_network_performance
    analyze_storage_performance
    analyze_application_performance
    
    # 生成建议
    generate_optimization_recommendations
    
    # 运行性能测试
    if [[ "$performance_test" == "true" ]]; then
        run_performance_test
    fi
    
    # 应用自动优化
    if [[ "$auto_optimize" == "true" && "$analyze_only" != "true" ]]; then
        apply_auto_optimizations
    fi
    
    # 完成报告
    add_to_report "## 总结"
    add_to_report ""
    add_to_report "性能分析已完成。请查看上述建议并根据实际情况应用优化措施。"
    add_to_report ""
    add_to_report "---"
    add_to_report "*报告生成时间: $(date)*"
    
    print_success "性能分析完成"
    print_info "详细报告已保存到: $OPTIMIZATION_REPORT"
    
    if [[ "$analyze_only" != "true" && "$auto_optimize" != "true" ]]; then
        echo ""
        print_info "运行 '$0 --auto-optimize' 以自动应用优化建议"
    fi
}

# 脚本入口
main "$@"
