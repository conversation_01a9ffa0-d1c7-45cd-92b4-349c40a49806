#!/bin/bash

# 应用管理服务启动脚本
# 作者: PaaS 平台开发团队
# 描述: 启动应用管理服务，自动处理数据库初始化和目录创建

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVICE_NAME="app-manager"
LOG_FILE="$PROJECT_ROOT/logs/${SERVICE_NAME}.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Go 环境
    if ! command -v go &> /dev/null; then
        log_error "Go 环境未安装，请先安装 Go 1.19 或更高版本"
        exit 1
    fi
    
    local go_version=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go 版本: $go_version"
    
    # 检查项目依赖
    if [ ! -f "$PROJECT_ROOT/go.mod" ]; then
        log_error "go.mod 文件不存在，请确保在正确的项目目录中运行"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    # 创建数据目录
    mkdir -p "$PROJECT_ROOT/data"
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/data/storage"
    
    log_info "目录创建完成"
}

# 检查端口占用
check_port() {
    local port=${1:-8082}
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 $port 已被占用，正在查找占用进程..."
        local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
        local process=$(ps -p $pid -o comm= 2>/dev/null || echo "未知进程")
        log_warn "占用进程: $process (PID: $pid)"
        
        read -p "是否终止占用进程并继续? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kill -9 $pid
            log_info "已终止占用进程"
        else
            log_error "端口被占用，无法启动服务"
            exit 1
        fi
    fi
}

# 构建应用
build_app() {
    log_info "构建应用..."
    
    cd "$PROJECT_ROOT"
    
    # 下载依赖
    log_debug "下载 Go 模块依赖..."
    go mod download
    
    # 构建应用
    log_debug "编译应用..."
    go build -o bin/${SERVICE_NAME} cmd/${SERVICE_NAME}/main.go
    
    log_info "应用构建完成"
}

# 启动服务
start_service() {
    log_info "启动 ${SERVICE_NAME} 服务..."
    
    cd "$PROJECT_ROOT"
    
    # 检查配置文件
    if [ ! -f "configs/${SERVICE_NAME}.yaml" ]; then
        log_warn "配置文件不存在，将使用默认配置"
    fi
    
    # 启动服务
    if [ "$1" = "background" ]; then
        log_info "后台模式启动服务..."
        nohup ./bin/${SERVICE_NAME} > "$LOG_FILE" 2>&1 &
        local pid=$!
        echo $pid > "$PROJECT_ROOT/data/${SERVICE_NAME}.pid"
        log_info "服务已在后台启动 (PID: $pid)"
        log_info "日志文件: $LOG_FILE"
        log_info "PID 文件: $PROJECT_ROOT/data/${SERVICE_NAME}.pid"
    else
        log_info "前台模式启动服务..."
        ./bin/${SERVICE_NAME}
    fi
}

# 健康检查
health_check() {
    local port=${1:-8082}
    local max_attempts=30
    local attempt=1
    
    log_info "等待服务启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
            log_info "服务健康检查通过"
            curl -s "http://localhost:$port/health" | jq . 2>/dev/null || curl -s "http://localhost:$port/health"
            return 0
        fi
        
        log_debug "健康检查失败，重试 $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    log_error "服务启动失败或健康检查超时"
    return 1
}

# 显示帮助信息
show_help() {
    echo "应用管理服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  -b, --build     仅构建应用，不启动"
    echo "  -d, --daemon    后台模式启动"
    echo "  -p, --port      指定端口 (默认: 8082)"
    echo "  --no-build      跳过构建步骤"
    echo "  --check-only    仅进行健康检查"
    echo ""
    echo "示例:"
    echo "  $0                    # 前台启动服务"
    echo "  $0 -d                 # 后台启动服务"
    echo "  $0 -b                 # 仅构建应用"
    echo "  $0 -p 8083            # 使用端口 8083 启动"
    echo ""
}

# 主函数
main() {
    local build_only=false
    local daemon_mode=false
    local skip_build=false
    local check_only=false
    local port=8082
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build)
                build_only=true
                shift
                ;;
            -d|--daemon)
                daemon_mode=true
                shift
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            --no-build)
                skip_build=true
                shift
                ;;
            --check-only)
                check_only=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "启动 PaaS 平台应用管理服务..."
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 仅健康检查
    if [ "$check_only" = true ]; then
        health_check $port
        exit $?
    fi
    
    # 检查依赖
    check_dependencies
    
    # 创建目录
    create_directories
    
    # 检查端口
    if [ "$build_only" = false ]; then
        check_port $port
    fi
    
    # 构建应用
    if [ "$skip_build" = false ]; then
        build_app
    fi
    
    # 仅构建模式
    if [ "$build_only" = true ]; then
        log_info "构建完成，退出"
        exit 0
    fi
    
    # 启动服务
    if [ "$daemon_mode" = true ]; then
        start_service background
        sleep 3
        health_check $port
    else
        start_service
    fi
}

# 捕获中断信号
trap 'log_info "收到中断信号，正在退出..."; exit 0' INT TERM

# 执行主函数
main "$@"
