#!/bin/bash

# PaaS 平台通知服务启动脚本
# 启动完整的通知和告警系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/configs"
LOG_DIR="$PROJECT_ROOT/logs"

echo -e "${BLUE}📢 PaaS 平台通知服务启动脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查通知服务依赖..."
    
    local missing_deps=()
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        missing_deps+=("go")
    fi
    
    # 检查 curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：创建必要目录
create_directories() {
    print_info "创建必要目录..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/bin"
    
    print_success "目录创建完成"
}

# 函数：生成通知配置文件
generate_notification_config() {
    print_info "生成通知配置文件..."
    
    local config_file="$CONFIG_DIR/notification.yaml"
    
    cat > "$config_file" << EOF
# PaaS 平台通知服务配置

# 服务配置
server:
  port: 8085
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file: "$LOG_DIR/notification.log"

# 数据库配置
database:
  host: "\${DB_HOST:localhost}"
  port: "\${DB_PORT:5432}"
  user: "\${DB_USER:postgres}"
  password: "\${DB_PASSWORD:password}"
  dbname: "\${DB_NAME:paas_platform}"
  sslmode: "disable"

# 通知渠道配置
notification:
  # 邮件配置
  email:
    enabled: true
    smtp_host: "\${SMTP_HOST:smtp.gmail.com}"
    smtp_port: 587
    username: "\${SMTP_USERNAME:}"
    password: "\${SMTP_PASSWORD:}"
    from: "\${SMTP_FROM:<EMAIL>}"
    tls: true

  # Slack 配置
  slack:
    enabled: false
    webhook_url: "\${SLACK_WEBHOOK_URL:}"
    channel: "#alerts"
    username: "PaaS Platform"

  # 钉钉配置
  dingtalk:
    enabled: false
    webhook_url: "\${DINGTALK_WEBHOOK_URL:}"
    secret: "\${DINGTALK_SECRET:}"

  # 企业微信配置
  wechat:
    enabled: false
    webhook_url: "\${WECHAT_WEBHOOK_URL:}"

  # 短信配置
  sms:
    enabled: false
    provider: "aliyun"
    access_key: "\${SMS_ACCESS_KEY:}"
    secret_key: "\${SMS_SECRET_KEY:}"
    sign_name: "PaaS平台"

# 告警配置
alerting:
  evaluation_interval: 30s
  notification_timeout: 10s
  max_retries: 3
  
  # 默认告警规则
  default_rules:
    - name: "CPU 使用率过高"
      metric: "cpu_usage"
      operator: ">"
      threshold: 80.0
      duration: 300s
      severity: "warning"
      channels: ["email"]
      enabled: true
      
    - name: "内存使用率过高"
      metric: "memory_usage"
      operator: ">"
      threshold: 85.0
      duration: 300s
      severity: "warning"
      channels: ["email"]
      enabled: true
      
    - name: "磁盘使用率过高"
      metric: "disk_usage"
      operator: ">"
      threshold: 90.0
      duration: 300s
      severity: "error"
      channels: ["email", "slack"]
      enabled: true
      
    - name: "服务不可用"
      metric: "service_availability"
      operator: "<"
      threshold: 1.0
      duration: 60s
      severity: "critical"
      channels: ["email", "slack", "sms"]
      enabled: true

# 监控配置
monitoring:
  metrics_port: 9090
  health_check_interval: 30s
  
# 安全配置
security:
  jwt_secret: "\${JWT_SECRET:your-secret-key}"
  api_key: "\${API_KEY:your-api-key}"
  
# 限流配置
rate_limiting:
  enabled: true
  requests_per_minute: 100
  burst_size: 20
EOF

    print_success "通知配置文件已生成: $config_file"
}

# 函数：构建通知服务
build_notification_service() {
    print_info "构建通知服务..."
    
    cd "$PROJECT_ROOT"
    
    # 构建通知服务
    go build -o bin/notification-service cmd/notification-service/main.go
    
    if [[ ! -f "bin/notification-service" ]]; then
        print_error "通知服务构建失败"
        exit 1
    fi
    
    print_success "通知服务构建完成"
}

# 函数：启动通知服务
start_notification_service() {
    print_info "启动通知服务..."
    
    cd "$PROJECT_ROOT"
    
    # 设置环境变量
    export CONFIG_FILE="$CONFIG_DIR/notification.yaml"
    export LOG_LEVEL="info"
    export GIN_MODE="release"
    
    # 启动服务
    ./bin/notification-service &
    NOTIFICATION_PID=$!
    
    # 等待服务启动
    print_info "等待通知服务启动..."
    sleep 10
    
    # 检查服务健康状态
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:8085/health" &> /dev/null; then
            print_success "通知服务启动成功"
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            print_error "通知服务启动失败"
            return 1
        fi
        
        print_info "等待通知服务启动... ($attempt/$max_attempts)"
        sleep 3
        ((attempt++))
    done
}

# 函数：测试通知功能
test_notification_features() {
    print_info "测试通知功能..."
    
    local base_url="http://localhost:8085/api/v1"
    
    # 测试获取通知渠道
    print_info "测试获取通知渠道..."
    if curl -s -f "$base_url/notifications/channels" > /dev/null; then
        print_success "通知渠道接口正常"
    else
        print_warning "通知渠道接口异常"
    fi
    
    # 测试创建告警规则
    print_info "测试创建告警规则..."
    local rule_data='{
        "name": "测试告警规则",
        "description": "这是一个测试告警规则",
        "metric": "test_metric",
        "operator": ">",
        "threshold": 50.0,
        "duration": 60,
        "severity": "warning",
        "channels": ["email"],
        "enabled": true
    }'
    
    if curl -s -f -X POST \
        -H "Content-Type: application/json" \
        -d "$rule_data" \
        "$base_url/alerts/rules" > /dev/null; then
        print_success "告警规则创建接口正常"
    else
        print_warning "告警规则创建接口异常"
    fi
    
    # 测试获取告警规则列表
    print_info "测试获取告警规则列表..."
    if curl -s -f "$base_url/alerts/rules" > /dev/null; then
        print_success "告警规则列表接口正常"
    else
        print_warning "告警规则列表接口异常"
    fi
    
    print_success "通知功能测试完成"
}

# 函数：显示服务信息
show_service_info() {
    echo ""
    echo "=================================================="
    print_success "📢 通知服务启动完成！"
    echo "=================================================="
    echo ""
    
    echo "🔗 服务地址："
    echo "  • 通知服务: http://localhost:8085"
    echo "  • 健康检查: http://localhost:8085/health"
    echo "  • API 文档: http://localhost:8085/swagger"
    echo "  • 指标监控: http://localhost:9090/metrics"
    echo ""
    
    echo "📋 API 端点："
    echo "  • 发送通知: POST /api/v1/notifications/send"
    echo "  • 获取渠道: GET /api/v1/notifications/channels"
    echo "  • 测试通知: POST /api/v1/notifications/test"
    echo "  • 创建规则: POST /api/v1/alerts/rules"
    echo "  • 规则列表: GET /api/v1/alerts/rules"
    echo "  • 触发告警: POST /api/v1/alerts/trigger"
    echo ""
    
    echo "📁 重要文件："
    echo "  • 配置文件: $CONFIG_DIR/notification.yaml"
    echo "  • 日志文件: $LOG_DIR/notification.log"
    echo "  • 服务进程: PID $NOTIFICATION_PID"
    echo ""
    
    echo "🔧 管理命令："
    echo "  • 查看日志: tail -f $LOG_DIR/notification.log"
    echo "  • 停止服务: kill $NOTIFICATION_PID"
    echo "  • 重启服务: $0 --restart"
    echo ""
}

# 函数：清理服务
cleanup_service() {
    print_info "清理通知服务..."
    
    if [[ -n "$NOTIFICATION_PID" ]]; then
        kill "$NOTIFICATION_PID" 2>/dev/null || true
        print_success "通知服务已停止"
    fi
}

# 主函数
main() {
    local restart=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --restart)
                restart=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --restart    重启服务"
                echo "  -h, --help   显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 设置清理陷阱
    trap cleanup_service EXIT
    
    # 如果是重启，先停止现有服务
    if [[ "$restart" == "true" ]]; then
        print_info "重启通知服务..."
        pkill -f "notification-service" || true
        sleep 3
    fi
    
    # 执行启动流程
    check_dependencies
    create_directories
    generate_notification_config
    build_notification_service
    
    if start_notification_service; then
        test_notification_features
        show_service_info
        
        # 保持服务运行
        print_info "通知服务正在运行，按 Ctrl+C 停止..."
        wait "$NOTIFICATION_PID"
    else
        print_error "通知服务启动失败"
        exit 1
    fi
}

# 脚本入口
main "$@"
