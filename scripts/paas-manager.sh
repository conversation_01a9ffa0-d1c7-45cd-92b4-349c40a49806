#!/bin/bash

# PaaS 平台综合管理脚本
# 提供一键启动、停止、重启、监控等功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PID_DIR="$PROJECT_ROOT/pids"
LOG_DIR="$PROJECT_ROOT/logs"

# 服务配置
declare -A SERVICES=(
    ["api-gateway"]="8080"
    ["user-service"]="8081"
    ["app-manager"]="8082"
    ["cicd-service"]="8083"
    ["monitor-service"]="8084"
    ["notification-service"]="8085"
)

echo -e "${PURPLE}🚀 PaaS 平台综合管理脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：创建必要目录
create_directories() {
    mkdir -p "$PID_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/bin"
    mkdir -p "$PROJECT_ROOT/configs"
}

# 函数：检查服务状态
check_service_status() {
    local service=$1
    local port=${SERVICES[$service]}
    local pid_file="$PID_DIR/$service.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            if curl -s -f "http://localhost:$port/health" >/dev/null 2>&1; then
                echo -e "${GREEN}运行中${NC}"
                return 0
            else
                echo -e "${YELLOW}进程存在但服务异常${NC}"
                return 1
            fi
        else
            echo -e "${RED}已停止${NC}"
            rm -f "$pid_file"
            return 1
        fi
    else
        echo -e "${RED}已停止${NC}"
        return 1
    fi
}

# 函数：启动单个服务
start_service() {
    local service=$1
    local port=${SERVICES[$service]}
    local pid_file="$PID_DIR/$service.pid"
    local log_file="$LOG_DIR/$service.log"
    
    print_info "启动 $service..."
    
    # 检查服务是否已运行
    if check_service_status "$service" >/dev/null 2>&1; then
        print_warning "$service 已在运行"
        return 0
    fi
    
    # 构建服务（如果需要）
    if [[ ! -f "$PROJECT_ROOT/bin/$service" ]]; then
        print_info "构建 $service..."
        cd "$PROJECT_ROOT"
        go build -o "bin/$service" "cmd/$service/main.go"
    fi
    
    # 启动服务
    cd "$PROJECT_ROOT"
    nohup "./bin/$service" > "$log_file" 2>&1 &
    local pid=$!
    echo "$pid" > "$pid_file"
    
    # 等待服务启动
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s -f "http://localhost:$port/health" >/dev/null 2>&1; then
            print_success "$service 启动成功 (PID: $pid, Port: $port)"
            return 0
        fi
        
        if ! kill -0 "$pid" 2>/dev/null; then
            print_error "$service 启动失败"
            rm -f "$pid_file"
            return 1
        fi
        
        sleep 2
        ((attempt++))
    done
    
    print_error "$service 启动超时"
    kill "$pid" 2>/dev/null || true
    rm -f "$pid_file"
    return 1
}

# 函数：停止单个服务
stop_service() {
    local service=$1
    local pid_file="$PID_DIR/$service.pid"
    
    print_info "停止 $service..."
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            
            # 等待进程结束
            local max_attempts=10
            local attempt=1
            
            while [[ $attempt -le $max_attempts ]]; do
                if ! kill -0 "$pid" 2>/dev/null; then
                    print_success "$service 已停止"
                    rm -f "$pid_file"
                    return 0
                fi
                sleep 1
                ((attempt++))
            done
            
            # 强制杀死进程
            kill -9 "$pid" 2>/dev/null || true
            print_warning "$service 强制停止"
        fi
        rm -f "$pid_file"
    else
        print_info "$service 未运行"
    fi
}

# 函数：重启单个服务
restart_service() {
    local service=$1
    print_info "重启 $service..."
    stop_service "$service"
    sleep 2
    start_service "$service"
}

# 函数：启动所有服务
start_all_services() {
    print_title "启动所有服务"
    
    create_directories
    
    # 按依赖顺序启动服务
    local services_order=("user-service" "app-manager" "cicd-service" "monitor-service" "notification-service" "api-gateway")
    
    for service in "${services_order[@]}"; do
        start_service "$service"
        sleep 3  # 服务间启动间隔
    done
    
    print_success "所有服务启动完成"
}

# 函数：停止所有服务
stop_all_services() {
    print_title "停止所有服务"
    
    # 按相反顺序停止服务
    local services_order=("api-gateway" "notification-service" "monitor-service" "cicd-service" "app-manager" "user-service")
    
    for service in "${services_order[@]}"; do
        stop_service "$service"
    done
    
    print_success "所有服务已停止"
}

# 函数：重启所有服务
restart_all_services() {
    print_title "重启所有服务"
    stop_all_services
    sleep 5
    start_all_services
}

# 函数：显示服务状态
show_status() {
    print_title "服务状态"
    
    printf "%-20s %-10s %-10s %-15s\n" "服务名称" "端口" "状态" "健康检查"
    echo "------------------------------------------------------------"
    
    for service in "${!SERVICES[@]}"; do
        local port=${SERVICES[$service]}
        local status
        local health="N/A"
        
        if check_service_status "$service" >/dev/null 2>&1; then
            status="${GREEN}运行中${NC}"
            if curl -s -f "http://localhost:$port/health" >/dev/null 2>&1; then
                health="${GREEN}正常${NC}"
            else
                health="${RED}异常${NC}"
            fi
        else
            status="${RED}已停止${NC}"
            health="${RED}不可用${NC}"
        fi
        
        printf "%-20s %-10s %-20s %-25s\n" "$service" "$port" "$status" "$health"
    done
    
    echo ""
}

# 函数：显示日志
show_logs() {
    local service=$1
    local lines=${2:-50}
    
    if [[ -z "$service" ]]; then
        print_error "请指定服务名称"
        return 1
    fi
    
    local log_file="$LOG_DIR/$service.log"
    
    if [[ -f "$log_file" ]]; then
        print_title "$service 日志 (最近 $lines 行)"
        tail -n "$lines" "$log_file"
    else
        print_error "$service 日志文件不存在"
    fi
}

# 函数：监控服务
monitor_services() {
    print_title "服务监控 (按 Ctrl+C 退出)"
    
    while true; do
        clear
        echo -e "${PURPLE}🚀 PaaS 平台服务监控${NC}"
        echo "=================================================="
        echo "更新时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo ""
        
        show_status
        
        # 显示系统资源使用情况
        print_title "系统资源"
        echo "CPU 使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
        echo "内存使用率: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
        echo "磁盘使用率: $(df -h / | awk 'NR==2{print $5}')"
        echo ""
        
        sleep 10
    done
}

# 函数：运行测试
run_tests() {
    local test_type=${1:-"all"}
    
    print_title "运行测试"
    
    cd "$PROJECT_ROOT"
    
    case $test_type in
        "unit")
            print_info "运行单元测试..."
            go test -v ./... -short
            ;;
        "integration")
            print_info "运行集成测试..."
            ./scripts/run-integration-tests.sh
            ;;
        "e2e")
            print_info "运行端到端测试..."
            ./scripts/run-e2e-tests.sh
            ;;
        "performance")
            print_info "运行性能测试..."
            ./scripts/run-performance-tests.sh
            ;;
        "all")
            print_info "运行所有测试..."
            go test -v ./... -short
            ./scripts/run-integration-tests.sh --skip-cleanup
            ./scripts/run-e2e-tests.sh --skip-cleanup
            ./scripts/run-performance-tests.sh --skip-cleanup
            ;;
        *)
            print_error "未知的测试类型: $test_type"
            print_info "可用类型: unit, integration, e2e, performance, all"
            return 1
            ;;
    esac
}

# 函数：构建所有服务
build_all() {
    print_title "构建所有服务"
    
    cd "$PROJECT_ROOT"
    
    for service in "${!SERVICES[@]}"; do
        print_info "构建 $service..."
        go build -o "bin/$service" "cmd/$service/main.go"
    done
    
    print_success "所有服务构建完成"
}

# 函数：清理资源
cleanup() {
    print_title "清理资源"
    
    # 停止所有服务
    stop_all_services
    
    # 清理 PID 文件
    rm -rf "$PID_DIR"
    
    # 清理构建文件
    rm -rf "$PROJECT_ROOT/bin"
    
    print_success "资源清理完成"
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  start [service]     启动服务 (不指定则启动所有服务)"
    echo "  stop [service]      停止服务 (不指定则停止所有服务)"
    echo "  restart [service]   重启服务 (不指定则重启所有服务)"
    echo "  status              显示服务状态"
    echo "  logs <service> [n]  显示服务日志 (n为行数，默认50)"
    echo "  monitor             监控服务状态"
    echo "  test [type]         运行测试 (unit|integration|e2e|performance|all)"
    echo "  build               构建所有服务"
    echo "  cleanup             清理资源"
    echo "  help                显示帮助信息"
    echo ""
    echo "可用服务:"
    for service in "${!SERVICES[@]}"; do
        echo "  - $service (端口: ${SERVICES[$service]})"
    done
    echo ""
    echo "示例:"
    echo "  $0 start                    # 启动所有服务"
    echo "  $0 start api-gateway        # 启动 API Gateway"
    echo "  $0 logs user-service 100    # 查看用户服务最近100行日志"
    echo "  $0 test unit                # 运行单元测试"
    echo "  $0 monitor                  # 监控服务状态"
}

# 主函数
main() {
    local command=${1:-"help"}
    local service=$2
    local option=$3
    
    case $command in
        "start")
            if [[ -n "$service" ]]; then
                if [[ -n "${SERVICES[$service]}" ]]; then
                    create_directories
                    start_service "$service"
                else
                    print_error "未知服务: $service"
                    exit 1
                fi
            else
                start_all_services
            fi
            ;;
        "stop")
            if [[ -n "$service" ]]; then
                if [[ -n "${SERVICES[$service]}" ]]; then
                    stop_service "$service"
                else
                    print_error "未知服务: $service"
                    exit 1
                fi
            else
                stop_all_services
            fi
            ;;
        "restart")
            if [[ -n "$service" ]]; then
                if [[ -n "${SERVICES[$service]}" ]]; then
                    restart_service "$service"
                else
                    print_error "未知服务: $service"
                    exit 1
                fi
            else
                restart_all_services
            fi
            ;;
        "status")
            show_status
            ;;
        "logs")
            if [[ -z "$service" ]]; then
                print_error "请指定服务名称"
                exit 1
            fi
            show_logs "$service" "$option"
            ;;
        "monitor")
            monitor_services
            ;;
        "test")
            run_tests "$service"
            ;;
        "build")
            build_all
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
