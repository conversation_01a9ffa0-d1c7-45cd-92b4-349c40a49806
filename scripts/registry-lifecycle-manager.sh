#!/bin/bash

# Docker镜像仓库生命周期管理脚本
# 用于管理私有镜像仓库的镜像生命周期和自动清理

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly CONFIG_FILE="$PROJECT_ROOT/configs/registry-lifecycle.yaml"
readonly LOG_FILE="$PROJECT_ROOT/logs/registry-lifecycle-$(date +%Y%m%d-%H%M%S).log"

# 默认配置
DEFAULT_REGISTRY_URL="localhost:5000"
DEFAULT_KEEP_VERSIONS=5
DEFAULT_KEEP_DAYS=30
DEFAULT_DRY_RUN=false
DEFAULT_FORCE=false

# 全局变量
REGISTRY_URL="$DEFAULT_REGISTRY_URL"
REGISTRY_USERNAME=""
REGISTRY_PASSWORD=""
KEEP_VERSIONS="$DEFAULT_KEEP_VERSIONS"
KEEP_DAYS="$DEFAULT_KEEP_DAYS"
DRY_RUN="$DEFAULT_DRY_RUN"
FORCE="$DEFAULT_FORCE"
VERBOSE=false

# 日志函数
log_info() {
    local msg="$1"
    echo -e "${BLUE}[INFO]${NC} $msg" | tee -a "$LOG_FILE"
}

log_success() {
    local msg="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $msg" | tee -a "$LOG_FILE"
}

log_warning() {
    local msg="$1"
    echo -e "${YELLOW}[WARNING]${NC} $msg" | tee -a "$LOG_FILE"
}

log_error() {
    local msg="$1"
    echo -e "${RED}[ERROR]${NC} $msg" | tee -a "$LOG_FILE"
}

log_debug() {
    local msg="$1"
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${CYAN}[DEBUG]${NC} $msg" | tee -a "$LOG_FILE"
    fi
}

print_title() {
    local title="$1"
    echo -e "\n${CYAN}🏪 $title${NC}\n" | tee -a "$LOG_FILE"
}

# 初始化环境
init_environment() {
    mkdir -p "$(dirname "$LOG_FILE")"
    log_info "镜像仓库生命周期管理脚本启动"
    log_info "日志文件: $LOG_FILE"
}

# 加载配置文件
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        log_info "加载配置文件: $CONFIG_FILE"
        
        # 解析YAML配置（简单实现）
        if command -v yq >/dev/null 2>&1; then
            REGISTRY_URL=$(yq eval '.registry.url' "$CONFIG_FILE" 2>/dev/null || echo "$DEFAULT_REGISTRY_URL")
            REGISTRY_USERNAME=$(yq eval '.registry.username' "$CONFIG_FILE" 2>/dev/null || echo "")
            REGISTRY_PASSWORD=$(yq eval '.registry.password' "$CONFIG_FILE" 2>/dev/null || echo "")
            KEEP_VERSIONS=$(yq eval '.lifecycle.keep_versions' "$CONFIG_FILE" 2>/dev/null || echo "$DEFAULT_KEEP_VERSIONS")
            KEEP_DAYS=$(yq eval '.lifecycle.keep_days' "$CONFIG_FILE" 2>/dev/null || echo "$DEFAULT_KEEP_DAYS")
        else
            log_warning "yq未安装，使用默认配置"
        fi
    else
        log_warning "配置文件不存在，使用默认配置"
    fi
    
    log_info "仓库地址: $REGISTRY_URL"
    log_info "保留版本数: $KEEP_VERSIONS"
    log_info "保留天数: $KEEP_DAYS"
}

# 检查仓库连接
check_registry_connection() {
    print_title "检查仓库连接"
    
    log_info "测试仓库连接: $REGISTRY_URL"
    
    # 构建认证参数
    local auth_params=""
    if [[ -n "$REGISTRY_USERNAME" ]] && [[ -n "$REGISTRY_PASSWORD" ]]; then
        auth_params="-u $REGISTRY_USERNAME:$REGISTRY_PASSWORD"
    fi
    
    # 测试仓库API
    local api_url="http://$REGISTRY_URL/v2/"
    
    if curl -s $auth_params "$api_url" >/dev/null 2>&1; then
        log_success "仓库连接正常"
    else
        log_error "仓库连接失败: $REGISTRY_URL"
        return 1
    fi
    
    # 获取仓库信息
    log_info "获取仓库信息..."
    local catalog_url="http://$REGISTRY_URL/v2/_catalog"
    local repositories=$(curl -s $auth_params "$catalog_url" | jq -r '.repositories[]?' 2>/dev/null || echo "")
    
    if [[ -n "$repositories" ]]; then
        local repo_count=$(echo "$repositories" | wc -l)
        log_info "仓库中共有 $repo_count 个镜像仓库"
        
        if [[ "$VERBOSE" == "true" ]]; then
            log_debug "仓库列表:"
            echo "$repositories" | while read -r repo; do
                echo "  - $repo" | tee -a "$LOG_FILE"
            done
        fi
    else
        log_info "仓库为空或无法获取仓库列表"
    fi
}

# 获取镜像标签列表
get_image_tags() {
    local repository="$1"
    local tags_url="http://$REGISTRY_URL/v2/$repository/tags/list"
    
    local auth_params=""
    if [[ -n "$REGISTRY_USERNAME" ]] && [[ -n "$REGISTRY_PASSWORD" ]]; then
        auth_params="-u $REGISTRY_USERNAME:$REGISTRY_PASSWORD"
    fi
    
    curl -s $auth_params "$tags_url" | jq -r '.tags[]?' 2>/dev/null || echo ""
}

# 获取镜像清单信息
get_image_manifest() {
    local repository="$1"
    local tag="$2"
    local manifest_url="http://$REGISTRY_URL/v2/$repository/manifests/$tag"
    
    local auth_params=""
    if [[ -n "$REGISTRY_USERNAME" ]] && [[ -n "$REGISTRY_PASSWORD" ]]; then
        auth_params="-u $REGISTRY_USERNAME:$REGISTRY_PASSWORD"
    fi
    
    curl -s $auth_params -H "Accept: application/vnd.docker.distribution.manifest.v2+json" "$manifest_url"
}

# 获取镜像创建时间
get_image_created_time() {
    local repository="$1"
    local tag="$2"
    
    local manifest=$(get_image_manifest "$repository" "$tag")
    local config_digest=$(echo "$manifest" | jq -r '.config.digest' 2>/dev/null || echo "")
    
    if [[ -n "$config_digest" ]] && [[ "$config_digest" != "null" ]]; then
        local blob_url="http://$REGISTRY_URL/v2/$repository/blobs/$config_digest"
        
        local auth_params=""
        if [[ -n "$REGISTRY_USERNAME" ]] && [[ -n "$REGISTRY_PASSWORD" ]]; then
            auth_params="-u $REGISTRY_USERNAME:$REGISTRY_PASSWORD"
        fi
        
        local config=$(curl -s $auth_params "$blob_url")
        local created=$(echo "$config" | jq -r '.created' 2>/dev/null || echo "")
        
        if [[ -n "$created" ]] && [[ "$created" != "null" ]]; then
            date -d "$created" +%s 2>/dev/null || echo "0"
        else
            echo "0"
        fi
    else
        echo "0"
    fi
}

# 删除镜像标签
delete_image_tag() {
    local repository="$1"
    local tag="$2"
    
    log_info "删除镜像标签: $repository:$tag"
    
    # 获取镜像清单的digest
    local manifest_url="http://$REGISTRY_URL/v2/$repository/manifests/$tag"
    
    local auth_params=""
    if [[ -n "$REGISTRY_USERNAME" ]] && [[ -n "$REGISTRY_PASSWORD" ]]; then
        auth_params="-u $REGISTRY_USERNAME:$REGISTRY_PASSWORD"
    fi
    
    local digest=$(curl -s $auth_params -I -H "Accept: application/vnd.docker.distribution.manifest.v2+json" "$manifest_url" | \
        grep -i "docker-content-digest" | awk '{print $2}' | tr -d '\r')
    
    if [[ -n "$digest" ]]; then
        # 删除镜像
        local delete_url="http://$REGISTRY_URL/v2/$repository/manifests/$digest"
        
        if curl -s $auth_params -X DELETE "$delete_url" >/dev/null 2>&1; then
            log_success "成功删除镜像标签: $repository:$tag"
            return 0
        else
            log_error "删除镜像标签失败: $repository:$tag"
            return 1
        fi
    else
        log_error "无法获取镜像digest: $repository:$tag"
        return 1
    fi
}

# 清理仓库中的过期镜像
cleanup_repository_images() {
    local repository="$1"
    
    print_title "清理仓库镜像: $repository"
    
    # 获取所有标签
    local tags=$(get_image_tags "$repository")
    
    if [[ -z "$tags" ]]; then
        log_info "仓库 $repository 没有标签"
        return 0
    fi
    
    local tag_count=$(echo "$tags" | wc -l)
    log_info "仓库 $repository 共有 $tag_count 个标签"
    
    # 收集标签信息
    local tag_info=()
    local current_time=$(date +%s)
    local cutoff_time=$((current_time - KEEP_DAYS * 24 * 3600))
    
    while read -r tag; do
        [[ -z "$tag" ]] && continue
        
        local created_time=$(get_image_created_time "$repository" "$tag")
        tag_info+=("$created_time:$tag")
        
        log_debug "标签 $tag 创建时间: $(date -d "@$created_time" 2>/dev/null || echo "unknown")"
    done <<< "$tags"
    
    # 按创建时间排序（最新的在前）
    IFS=$'\n' sorted_tags=($(printf '%s\n' "${tag_info[@]}" | sort -nr))
    unset IFS
    
    # 确定要删除的标签
    local tags_to_delete=()
    local kept_count=0
    
    for item in "${sorted_tags[@]}"; do
        local created_time="${item%%:*}"
        local tag="${item#*:}"
        
        # 跳过latest标签
        if [[ "$tag" == "latest" ]]; then
            log_debug "跳过latest标签"
            continue
        fi
        
        # 保留最新的几个版本
        if [[ $kept_count -lt $KEEP_VERSIONS ]]; then
            log_debug "保留标签: $tag (版本保留策略)"
            ((kept_count++))
            continue
        fi
        
        # 检查是否超过保留时间
        if [[ $created_time -lt $cutoff_time ]]; then
            tags_to_delete+=("$tag")
            log_debug "标记删除: $tag (超过保留时间)"
        else
            log_debug "保留标签: $tag (在保留时间内)"
        fi
    done
    
    # 执行删除
    if [[ ${#tags_to_delete[@]} -eq 0 ]]; then
        log_info "仓库 $repository 没有需要清理的标签"
        return 0
    fi
    
    log_info "仓库 $repository 需要清理 ${#tags_to_delete[@]} 个标签"
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_debug "待删除的标签:"
        for tag in "${tags_to_delete[@]}"; do
            echo "  - $tag" | tee -a "$LOG_FILE"
        done
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 将删除 ${#tags_to_delete[@]} 个标签"
        return 0
    fi
    
    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认删除仓库 $repository 中的 ${#tags_to_delete[@]} 个标签? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi
    
    local deleted_count=0
    for tag in "${tags_to_delete[@]}"; do
        if delete_image_tag "$repository" "$tag"; then
            ((deleted_count++))
        fi
    done
    
    log_success "仓库 $repository 成功删除 $deleted_count/${#tags_to_delete[@]} 个标签"
}

# 清理所有仓库
cleanup_all_repositories() {
    print_title "清理所有仓库"
    
    local catalog_url="http://$REGISTRY_URL/v2/_catalog"
    
    local auth_params=""
    if [[ -n "$REGISTRY_USERNAME" ]] && [[ -n "$REGISTRY_PASSWORD" ]]; then
        auth_params="-u $REGISTRY_USERNAME:$REGISTRY_PASSWORD"
    fi
    
    local repositories=$(curl -s $auth_params "$catalog_url" | jq -r '.repositories[]?' 2>/dev/null || echo "")
    
    if [[ -z "$repositories" ]]; then
        log_info "没有发现仓库"
        return 0
    fi
    
    local repo_count=$(echo "$repositories" | wc -l)
    log_info "开始清理 $repo_count 个仓库"
    
    local cleaned_repos=0
    while read -r repository; do
        [[ -z "$repository" ]] && continue
        
        cleanup_repository_images "$repository"
        ((cleaned_repos++))
        
        # 添加延迟避免过于频繁的API调用
        sleep 1
    done <<< "$repositories"
    
    log_success "完成清理 $cleaned_repos 个仓库"
}

# 执行仓库垃圾回收
run_registry_garbage_collection() {
    print_title "执行仓库垃圾回收"
    
    log_info "注意: 垃圾回收需要在仓库服务器上执行"
    log_info "请在仓库服务器上运行以下命令:"
    echo ""
    echo "  docker exec <registry-container> registry garbage-collect /etc/docker/registry/config.yml"
    echo ""
    log_info "或者如果使用docker-compose:"
    echo ""
    echo "  docker-compose exec registry registry garbage-collect /etc/docker/registry/config.yml"
    echo ""
    
    # 如果是本地仓库，尝试自动执行
    if [[ "$REGISTRY_URL" == "localhost:5000" ]] || [[ "$REGISTRY_URL" == "127.0.0.1:5000" ]]; then
        log_info "检测到本地仓库，尝试自动执行垃圾回收..."
        
        # 查找仓库容器
        local registry_container=$(docker ps --format "{{.Names}}" | grep -E "(registry|docker-registry)" | head -1)
        
        if [[ -n "$registry_container" ]]; then
            log_info "找到仓库容器: $registry_container"
            
            if [[ "$DRY_RUN" == "false" ]]; then
                if docker exec "$registry_container" registry garbage-collect /etc/docker/registry/config.yml; then
                    log_success "垃圾回收执行成功"
                else
                    log_warning "垃圾回收执行失败"
                fi
            else
                log_info "[试运行] 将在容器 $registry_container 中执行垃圾回收"
            fi
        else
            log_warning "未找到仓库容器"
        fi
    fi
}

# 生成清理报告
generate_cleanup_report() {
    print_title "生成清理报告"
    
    local report_file="$PROJECT_ROOT/reports/registry-cleanup-$(date +%Y%m%d-%H%M%S).md"
    mkdir -p "$(dirname "$report_file")"
    
    cat > "$report_file" << EOF
# 镜像仓库清理报告

**生成时间:** $(date)
**仓库地址:** $REGISTRY_URL
**保留策略:** 保留最新 $KEEP_VERSIONS 个版本，保留 $KEEP_DAYS 天内的镜像

## 清理配置

- **仓库地址:** $REGISTRY_URL
- **保留版本数:** $KEEP_VERSIONS
- **保留天数:** $KEEP_DAYS
- **试运行模式:** $DRY_RUN
- **强制模式:** $FORCE

## 清理统计

EOF
    
    # 获取当前仓库统计
    local catalog_url="http://$REGISTRY_URL/v2/_catalog"
    
    local auth_params=""
    if [[ -n "$REGISTRY_USERNAME" ]] && [[ -n "$REGISTRY_PASSWORD" ]]; then
        auth_params="-u $REGISTRY_USERNAME:$REGISTRY_PASSWORD"
    fi
    
    local repositories=$(curl -s $auth_params "$catalog_url" | jq -r '.repositories[]?' 2>/dev/null || echo "")
    
    if [[ -n "$repositories" ]]; then
        local repo_count=$(echo "$repositories" | wc -l)
        echo "- **仓库总数:** $repo_count" >> "$report_file"
        
        local total_tags=0
        echo "" >> "$report_file"
        echo "## 仓库详情" >> "$report_file"
        echo "" >> "$report_file"
        echo "| 仓库名称 | 标签数量 | 最新标签 |" >> "$report_file"
        echo "|----------|----------|----------|" >> "$report_file"
        
        while read -r repository; do
            [[ -z "$repository" ]] && continue
            
            local tags=$(get_image_tags "$repository")
            local tag_count=0
            local latest_tag="N/A"
            
            if [[ -n "$tags" ]]; then
                tag_count=$(echo "$tags" | wc -l)
                total_tags=$((total_tags + tag_count))
                latest_tag=$(echo "$tags" | head -1)
            fi
            
            echo "| $repository | $tag_count | $latest_tag |" >> "$report_file"
        done <<< "$repositories"
        
        echo "" >> "$report_file"
        echo "- **标签总数:** $total_tags" >> "$report_file"
    fi
    
    echo "" >> "$report_file"
    echo "## 建议" >> "$report_file"
    echo "" >> "$report_file"
    echo "1. 定期执行镜像清理以释放存储空间" >> "$report_file"
    echo "2. 根据实际需求调整保留策略" >> "$report_file"
    echo "3. 执行垃圾回收以彻底清理已删除的镜像层" >> "$report_file"
    echo "4. 监控仓库存储使用情况" >> "$report_file"
    
    log_success "清理报告已生成: $report_file"
}

# 显示帮助信息
show_help() {
    cat << EOF
镜像仓库生命周期管理脚本

用法: $0 [选项] [仓库名称]

选项:
  -h, --help              显示此帮助信息
  -r, --registry URL      指定仓库地址 (默认: $DEFAULT_REGISTRY_URL)
  -u, --username USER     仓库用户名
  -p, --password PASS     仓库密码
  -k, --keep-versions N   保留版本数 (默认: $DEFAULT_KEEP_VERSIONS)
  -t, --keep-days N       保留天数 (默认: $DEFAULT_KEEP_DAYS)
  -d, --dry-run           试运行模式
  -f, --force             强制执行，不询问确认
  -v, --verbose           详细输出
  -c, --config FILE       指定配置文件
  --gc                    执行垃圾回收
  --report                生成清理报告

示例:
  $0                              # 清理所有仓库
  $0 paas/api-gateway             # 清理指定仓库
  $0 --dry-run                    # 试运行模式
  $0 --keep-versions 10           # 保留10个版本
  $0 --gc                         # 执行垃圾回收

EOF
}

# 主函数
main() {
    local target_repository=""
    local run_gc=false
    local generate_report=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -r|--registry)
                REGISTRY_URL="$2"
                shift 2
                ;;
            -u|--username)
                REGISTRY_USERNAME="$2"
                shift 2
                ;;
            -p|--password)
                REGISTRY_PASSWORD="$2"
                shift 2
                ;;
            -k|--keep-versions)
                KEEP_VERSIONS="$2"
                shift 2
                ;;
            -t|--keep-days)
                KEEP_DAYS="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --gc)
                run_gc=true
                shift
                ;;
            --report)
                generate_report=true
                shift
                ;;
            *)
                if [[ -z "$target_repository" ]]; then
                    target_repository="$1"
                    shift
                else
                    log_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                ;;
        esac
    done
    
    # 初始化环境
    init_environment
    load_config
    
    # 检查仓库连接
    check_registry_connection
    
    # 执行相应操作
    if [[ "$generate_report" == "true" ]]; then
        generate_cleanup_report
    elif [[ "$run_gc" == "true" ]]; then
        run_registry_garbage_collection
    elif [[ -n "$target_repository" ]]; then
        cleanup_repository_images "$target_repository"
    else
        cleanup_all_repositories
    fi
    
    log_success "镜像仓库生命周期管理完成"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
