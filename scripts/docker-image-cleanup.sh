#!/bin/bash

# Docker镜像智能清理脚本
# 用于PaaS平台的Docker镜像存储空间管理
# 支持多种清理策略和安全检查机制

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly LOG_DIR="$PROJECT_ROOT/logs"
readonly CONFIG_FILE="$PROJECT_ROOT/configs/docker-cleanup.yaml"
readonly LOG_FILE="$LOG_DIR/docker-cleanup-$(date +%Y%m%d-%H%M%S).log"

# 默认配置
DEFAULT_KEEP_IMAGES=5              # 保留最新的镜像数量
DEFAULT_KEEP_DAYS=7                # 保留天数
DEFAULT_DRY_RUN=false              # 是否为试运行模式
DEFAULT_FORCE=false                # 是否强制删除
DEFAULT_EXCLUDE_PATTERNS=("paas-*:latest" "postgres:*" "redis:*" "nginx:*")

# 全局变量
KEEP_IMAGES=$DEFAULT_KEEP_IMAGES
KEEP_DAYS=$DEFAULT_KEEP_DAYS
DRY_RUN=$DEFAULT_DRY_RUN
FORCE=$DEFAULT_FORCE
EXCLUDE_PATTERNS=("${DEFAULT_EXCLUDE_PATTERNS[@]}")
VERBOSE=false
STATS_BEFORE=""
STATS_AFTER=""

# 日志函数
log_info() {
    local msg="$1"
    echo -e "${BLUE}[INFO]${NC} $msg" | tee -a "$LOG_FILE"
}

log_success() {
    local msg="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $msg" | tee -a "$LOG_FILE"
}

log_warning() {
    local msg="$1"
    echo -e "${YELLOW}[WARNING]${NC} $msg" | tee -a "$LOG_FILE"
}

log_error() {
    local msg="$1"
    echo -e "${RED}[ERROR]${NC} $msg" | tee -a "$LOG_FILE"
}

log_debug() {
    local msg="$1"
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${CYAN}[DEBUG]${NC} $msg" | tee -a "$LOG_FILE"
    fi
}

# 打印标题
print_title() {
    local title="$1"
    echo -e "\n${CYAN}🐳 $title${NC}\n" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker镜像智能清理脚本

用法: $0 [选项] [清理类型]

清理类型:
  all              执行所有清理操作（默认）
  dangling         清理悬空镜像
  unused           清理未使用的镜像
  old              清理过期镜像
  containers       清理停止的容器
  volumes          清理未使用的数据卷
  networks         清理未使用的网络
  system           执行系统级清理

选项:
  -h, --help              显示此帮助信息
  -d, --dry-run           试运行模式，仅显示将要删除的内容
  -f, --force             强制删除，不进行确认
  -v, --verbose           详细输出模式
  -k, --keep-images N     保留最新的N个镜像版本（默认: $DEFAULT_KEEP_IMAGES）
  -t, --keep-days N       保留N天内的镜像（默认: $DEFAULT_KEEP_DAYS）
  -e, --exclude PATTERN   排除匹配模式的镜像
  -c, --config FILE       指定配置文件路径
  --stats                 显示清理前后的统计信息

示例:
  $0                      # 执行所有清理操作
  $0 --dry-run            # 试运行模式查看将要清理的内容
  $0 dangling --force     # 强制清理悬空镜像
  $0 --keep-images 10     # 保留最新10个版本
  $0 --exclude "nginx:*"  # 排除nginx镜像

EOF
}

# 初始化环境
init_environment() {
    # 创建日志目录
    mkdir -p "$LOG_DIR"

    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行或无法连接"
        exit 1
    fi

    log_info "Docker镜像清理脚本启动"
    log_info "日志文件: $LOG_FILE"
    log_info "配置文件: $CONFIG_FILE"
}

# 加载配置文件
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        log_info "加载配置文件: $CONFIG_FILE"
        # 这里可以添加YAML配置解析逻辑
        # 暂时使用默认配置
    else
        log_warning "配置文件不存在，使用默认配置"
    fi
}

# 获取Docker存储统计信息
get_docker_stats() {
    local stats=""

    # 获取镜像统计
    local images_count=$(docker images -q | wc -l)
    local images_size=$(docker images --format "table {{.Size}}" | tail -n +2 | \
        awk '{sum += $1} END {printf "%.2f", sum}' 2>/dev/null || echo "0")

    # 获取容器统计
    local containers_count=$(docker ps -aq | wc -l)
    local running_containers=$(docker ps -q | wc -l)

    # 获取数据卷统计
    local volumes_count=$(docker volume ls -q | wc -l)

    # 获取网络统计
    local networks_count=$(docker network ls -q | wc -l)

    # 获取系统使用情况
    local system_df=$(docker system df --format "table {{.Type}}\t{{.Total}}\t{{.Active}}\t{{.Size}}\t{{.Reclaimable}}" 2>/dev/null || echo "")

    stats="镜像数量: $images_count, 镜像大小: ${images_size}GB, 容器数量: $containers_count, 运行中: $running_containers, 数据卷: $volumes_count, 网络: $networks_count"

    echo "$stats"
}

# 检查镜像是否被排除
is_excluded() {
    local image="$1"

    for pattern in "${EXCLUDE_PATTERNS[@]}"; do
        if [[ "$image" == $pattern ]]; then
            return 0
        fi
    done

    return 1
}

# 清理悬空镜像
cleanup_dangling_images() {
    print_title "清理悬空镜像"

    local dangling_images=$(docker images -f "dangling=true" -q)

    if [[ -z "$dangling_images" ]]; then
        log_info "没有发现悬空镜像"
        return 0
    fi

    local count=$(echo "$dangling_images" | wc -l)
    log_info "发现 $count 个悬空镜像"

    if [[ "$VERBOSE" == "true" ]]; then
        log_debug "悬空镜像列表:"
        docker images -f "dangling=true" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}" | tee -a "$LOG_FILE"
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 将删除 $count 个悬空镜像"
        return 0
    fi

    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认删除 $count 个悬空镜像? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi

    log_info "开始删除悬空镜像..."
    if docker rmi $dangling_images 2>/dev/null; then
        log_success "成功删除 $count 个悬空镜像"
    else
        log_warning "部分悬空镜像删除失败"
    fi
}

# 清理未使用的镜像
cleanup_unused_images() {
    print_title "清理未使用的镜像"

    # 获取所有镜像
    local all_images=$(docker images --format "{{.Repository}}:{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}")

    if [[ -z "$all_images" ]]; then
        log_info "没有发现镜像"
        return 0
    fi

    # 获取正在使用的镜像
    local used_images=$(docker ps -a --format "{{.Image}}" | sort | uniq)

    local unused_images=()
    local unused_count=0

    while IFS=$'\t' read -r image_name image_id created_at; do
        # 跳过排除的镜像
        if is_excluded "$image_name"; then
            log_debug "跳过排除的镜像: $image_name"
            continue
        fi

        # 检查镜像是否被使用
        local is_used=false
        for used_image in $used_images; do
            if [[ "$image_name" == "$used_image" ]] || [[ "$image_id" == "$used_image" ]]; then
                is_used=true
                break
            fi
        done

        if [[ "$is_used" == "false" ]]; then
            unused_images+=("$image_id:$image_name")
            ((unused_count++))
        fi
    done <<< "$all_images"

    if [[ $unused_count -eq 0 ]]; then
        log_info "没有发现未使用的镜像"
        return 0
    fi

    log_info "发现 $unused_count 个未使用的镜像"

    if [[ "$VERBOSE" == "true" ]]; then
        log_debug "未使用的镜像列表:"
        for item in "${unused_images[@]}"; do
            local id="${item%%:*}"
            local name="${item#*:}"
            echo "  $name ($id)" | tee -a "$LOG_FILE"
        done
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 将删除 $unused_count 个未使用的镜像"
        return 0
    fi

    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认删除 $unused_count 个未使用的镜像? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi

    log_info "开始删除未使用的镜像..."
    local deleted_count=0
    for item in "${unused_images[@]}"; do
        local id="${item%%:*}"
        local name="${item#*:}"

        if docker rmi "$id" 2>/dev/null; then
            log_debug "删除镜像: $name"
            ((deleted_count++))
        else
            log_warning "删除镜像失败: $name"
        fi
    done

    log_success "成功删除 $deleted_count/$unused_count 个未使用的镜像"
}

# 清理过期镜像
cleanup_old_images() {
    print_title "清理过期镜像"

    local cutoff_date=$(date -d "$KEEP_DAYS days ago" +%s)
    local old_images=()
    local old_count=0

    # 按仓库分组处理镜像
    local repositories=$(docker images --format "{{.Repository}}" | grep -v "<none>" | sort | uniq)

    for repo in $repositories; do
        # 跳过排除的仓库
        if is_excluded "$repo:*"; then
            log_debug "跳过排除的仓库: $repo"
            continue
        fi

        # 获取该仓库的所有镜像，按创建时间排序
        local repo_images=$(docker images "$repo" --format "{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}" | \
            sort -k3 -r)

        local keep_count=0
        while IFS=$'\t' read -r tag image_id created_at; do
            # 保留最新的几个版本
            if [[ $keep_count -lt $KEEP_IMAGES ]]; then
                ((keep_count++))
                log_debug "保留镜像: $repo:$tag"
                continue
            fi

            # 检查镜像创建时间
            local image_date=$(date -d "$created_at" +%s 2>/dev/null || echo "0")
            if [[ $image_date -lt $cutoff_date ]]; then
                old_images+=("$image_id:$repo:$tag")
                ((old_count++))
            fi
        done <<< "$repo_images"
    done

    if [[ $old_count -eq 0 ]]; then
        log_info "没有发现过期镜像"
        return 0
    fi

    log_info "发现 $old_count 个过期镜像（超过 $KEEP_DAYS 天且不在保留范围内）"

    if [[ "$VERBOSE" == "true" ]]; then
        log_debug "过期镜像列表:"
        for item in "${old_images[@]}"; do
            local id="${item%%:*}"
            local name="${item#*:}"
            echo "  $name ($id)" | tee -a "$LOG_FILE"
        done
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 将删除 $old_count 个过期镜像"
        return 0
    fi

    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认删除 $old_count 个过期镜像? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi

    log_info "开始删除过期镜像..."
    local deleted_count=0
    for item in "${old_images[@]}"; do
        local id="${item%%:*}"
        local name="${item#*:}"

        if docker rmi "$id" 2>/dev/null; then
            log_debug "删除镜像: $name"
            ((deleted_count++))
        else
            log_warning "删除镜像失败: $name"
        fi
    done

    log_success "成功删除 $deleted_count/$old_count 个过期镜像"
}

# 清理停止的容器
cleanup_stopped_containers() {
    print_title "清理停止的容器"

    local stopped_containers=$(docker ps -aq --filter "status=exited")

    if [[ -z "$stopped_containers" ]]; then
        log_info "没有发现停止的容器"
        return 0
    fi

    local count=$(echo "$stopped_containers" | wc -l)
    log_info "发现 $count 个停止的容器"

    if [[ "$VERBOSE" == "true" ]]; then
        log_debug "停止的容器列表:"
        docker ps -a --filter "status=exited" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | tee -a "$LOG_FILE"
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 将删除 $count 个停止的容器"
        return 0
    fi

    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认删除 $count 个停止的容器? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi

    log_info "开始删除停止的容器..."
    if docker rm $stopped_containers 2>/dev/null; then
        log_success "成功删除 $count 个停止的容器"
    else
        log_warning "部分容器删除失败"
    fi
}

# 清理未使用的数据卷
cleanup_unused_volumes() {
    print_title "清理未使用的数据卷"

    local unused_volumes=$(docker volume ls -qf dangling=true)

    if [[ -z "$unused_volumes" ]]; then
        log_info "没有发现未使用的数据卷"
        return 0
    fi

    local count=$(echo "$unused_volumes" | wc -l)
    log_info "发现 $count 个未使用的数据卷"

    if [[ "$VERBOSE" == "true" ]]; then
        log_debug "未使用的数据卷列表:"
        docker volume ls -f dangling=true --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}" | tee -a "$LOG_FILE"
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 将删除 $count 个未使用的数据卷"
        return 0
    fi

    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认删除 $count 个未使用的数据卷? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi

    log_info "开始删除未使用的数据卷..."
    if docker volume rm $unused_volumes 2>/dev/null; then
        log_success "成功删除 $count 个未使用的数据卷"
    else
        log_warning "部分数据卷删除失败"
    fi
}

# 清理未使用的网络
cleanup_unused_networks() {
    print_title "清理未使用的网络"

    # 获取所有自定义网络（排除默认网络）
    local custom_networks=$(docker network ls --filter "type=custom" -q)
    local unused_networks=()
    local unused_count=0

    for network_id in $custom_networks; do
        # 检查网络是否被使用
        local containers_using=$(docker network inspect "$network_id" --format '{{len .Containers}}' 2>/dev/null || echo "0")

        if [[ "$containers_using" == "0" ]]; then
            local network_name=$(docker network inspect "$network_id" --format '{{.Name}}' 2>/dev/null || echo "unknown")
            unused_networks+=("$network_id:$network_name")
            ((unused_count++))
        fi
    done

    if [[ $unused_count -eq 0 ]]; then
        log_info "没有发现未使用的网络"
        return 0
    fi

    log_info "发现 $unused_count 个未使用的网络"

    if [[ "$VERBOSE" == "true" ]]; then
        log_debug "未使用的网络列表:"
        for item in "${unused_networks[@]}"; do
            local name="${item#*:}"
            echo "  $name" | tee -a "$LOG_FILE"
        done
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 将删除 $unused_count 个未使用的网络"
        return 0
    fi

    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认删除 $unused_count 个未使用的网络? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi

    log_info "开始删除未使用的网络..."
    local deleted_count=0
    for item in "${unused_networks[@]}"; do
        local id="${item%%:*}"
        local name="${item#*:}"

        if docker network rm "$id" 2>/dev/null; then
            log_debug "删除网络: $name"
            ((deleted_count++))
        else
            log_warning "删除网络失败: $name"
        fi
    done

    log_success "成功删除 $deleted_count/$unused_count 个未使用的网络"
}

# 执行系统级清理
cleanup_system() {
    print_title "执行系统级清理"

    log_info "执行Docker系统清理..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[试运行] 系统清理预览:"
        docker system df | tee -a "$LOG_FILE"
        return 0
    fi

    if [[ "$FORCE" == "false" ]]; then
        echo -n "确认执行系统级清理? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            return 0
        fi
    fi

    # 执行系统清理
    if docker system prune -f 2>/dev/null; then
        log_success "系统级清理完成"
    else
        log_warning "系统级清理部分失败"
    fi
}

# 显示清理统计
show_cleanup_stats() {
    print_title "清理统计信息"

    log_info "清理前: $STATS_BEFORE"
    log_info "清理后: $STATS_AFTER"

    # 计算节省的空间
    echo "" | tee -a "$LOG_FILE"
    log_info "Docker系统使用情况:"
    docker system df | tee -a "$LOG_FILE"
}

# 生成清理配置文件
generate_config() {
    local config_file="$1"

    cat > "$config_file" << EOF
# Docker镜像清理配置文件
# 用于PaaS平台的镜像存储管理

# 清理策略配置
cleanup:
  # 保留策略
  keep:
    images: $DEFAULT_KEEP_IMAGES        # 保留最新的镜像数量
    days: $DEFAULT_KEEP_DAYS            # 保留天数

  # 排除模式
  exclude_patterns:
$(printf '    - "%s"\n' "${DEFAULT_EXCLUDE_PATTERNS[@]}")

  # 清理选项
  options:
    dry_run: $DEFAULT_DRY_RUN           # 默认试运行模式
    force: $DEFAULT_FORCE               # 默认强制模式
    verbose: false                      # 详细输出

  # 定时清理配置
  schedule:
    enabled: true                       # 启用定时清理
    cron: "0 2 * * *"                  # 每天凌晨2点执行
    cleanup_types:                      # 定时清理类型
      - "dangling"
      - "unused"
      - "old"

# 监控配置
monitoring:
  # 告警阈值
  thresholds:
    disk_usage_percent: 80              # 磁盘使用率告警阈值
    image_count: 100                    # 镜像数量告警阈值

  # 通知配置
  notifications:
    enabled: true
    webhook_url: ""                     # Webhook通知地址
    email: ""                           # 邮件通知地址

# 日志配置
logging:
  level: "info"                         # 日志级别
  file: "$LOG_DIR/docker-cleanup.log"   # 日志文件
  max_size: "100MB"                     # 最大日志文件大小
  max_files: 10                         # 保留日志文件数量
EOF

    log_success "配置文件已生成: $config_file"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -k|--keep-images)
                KEEP_IMAGES="$2"
                shift 2
                ;;
            -t|--keep-days)
                KEEP_DAYS="$2"
                shift 2
                ;;
            -e|--exclude)
                EXCLUDE_PATTERNS+=("$2")
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --stats)
                SHOW_STATS=true
                shift
                ;;
            --generate-config)
                generate_config "$CONFIG_FILE"
                exit 0
                ;;
            all|dangling|unused|old|containers|volumes|networks|system)
                CLEANUP_TYPE="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    local cleanup_type="${CLEANUP_TYPE:-all}"

    # 初始化环境
    init_environment
    load_config

    # 获取清理前统计信息
    STATS_BEFORE=$(get_docker_stats)

    log_info "开始Docker镜像清理"
    log_info "清理类型: $cleanup_type"
    log_info "试运行模式: $DRY_RUN"
    log_info "强制模式: $FORCE"
    log_info "保留镜像数量: $KEEP_IMAGES"
    log_info "保留天数: $KEEP_DAYS"

    # 执行清理操作
    case "$cleanup_type" in
        "all")
            cleanup_dangling_images
            cleanup_unused_images
            cleanup_old_images
            cleanup_stopped_containers
            cleanup_unused_volumes
            cleanup_unused_networks
            ;;
        "dangling")
            cleanup_dangling_images
            ;;
        "unused")
            cleanup_unused_images
            ;;
        "old")
            cleanup_old_images
            ;;
        "containers")
            cleanup_stopped_containers
            ;;
        "volumes")
            cleanup_unused_volumes
            ;;
        "networks")
            cleanup_unused_networks
            ;;
        "system")
            cleanup_system
            ;;
        *)
            log_error "未知的清理类型: $cleanup_type"
            exit 1
            ;;
    esac

    # 获取清理后统计信息
    STATS_AFTER=$(get_docker_stats)

    # 显示统计信息
    if [[ "${SHOW_STATS:-false}" == "true" ]] || [[ "$VERBOSE" == "true" ]]; then
        show_cleanup_stats
    fi

    log_success "Docker镜像清理完成"
    log_info "详细日志请查看: $LOG_FILE"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 解析参数
    parse_arguments "$@"

    # 执行主函数
    main
fi