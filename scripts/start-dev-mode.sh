#!/bin/bash

# PaaS平台开发环境启动脚本
# 此脚本用于在开发环境中启动所有服务，并自动配置认证跳过
# ⚠️ 警告：此脚本仅适用于开发环境，绝不能在生产环境使用！

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示开发模式警告
show_dev_warning() {
    echo -e "${RED}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${RED}║                    ⚠️  开发模式警告 ⚠️                      ║${NC}"
    echo -e "${RED}╠══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${RED}║  🔓 用户认证已禁用 - 所有API请求将跳过身份验证              ║${NC}"
    echo -e "${RED}║  🧑‍💻 使用默认开发用户身份 (admin权限)                      ║${NC}"
    echo -e "${RED}║  🚨 此配置仅适用于开发环境，请勿在生产环境使用！            ║${NC}"
    echo -e "${RED}║  📝 如需启用认证，请设置环境变量 PAAS_AUTH_ENABLED=true     ║${NC}"
    echo -e "${RED}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.21或更高版本"
        exit 1
    fi
    
    # 检查Docker (可选)
    if ! command -v docker &> /dev/null; then
        log_warn "Docker未安装，脚本服务可能无法正常工作"
    else
        # 检查Docker是否运行
        if ! docker info &> /dev/null; then
            log_warn "Docker未运行，脚本服务可能无法正常工作"
        fi
    fi
    
    # 检查Redis (可选)
    if ! command -v redis-cli &> /dev/null; then
        log_warn "Redis未安装，某些功能可能受限"
    fi
    
    log_info "工具检查完成"
}

# 设置开发环境变量
setup_dev_env() {
    log_info "设置开发环境变量..."
    
    # 导出开发环境变量
    export ENV=development
    export NODE_ENV=development
    export GO_ENV=development
    export DEBUG=true
    
    # 🔓 认证配置 - 开发环境跳过认证
    export PAAS_AUTH_ENABLED=false
    export PAAS_DEV_MODE=true
    export PAAS_DEV_TOKEN=dev-token-2024-secure
    export PAAS_JWT_SECRET=dev-jwt-secret-key-not-for-production
    
    # 开发用户信息
    export PAAS_DEV_USER_ID=dev-user-001
    export PAAS_DEV_USER_TENANT_ID=dev-tenant-001
    export PAAS_DEV_USER_USERNAME=开发者
    export PAAS_DEV_USER_EMAIL=developer@localhost
    export PAAS_DEV_USER_ROLES=admin,developer,script_executor,cicd_manager,config_manager
    
    # 显示警告
    export SHOW_DEV_MODE_WARNING=true
    export DEV_MODE_BANNER="🔓 开发模式已启用 - 认证已跳过"
    
    # 数据库配置
    export DB_DRIVER=sqlite
    export DB_LOG_LEVEL=info
    
    # 日志配置
    export LOG_LEVEL=debug
    export LOG_FORMAT=text
    export LOG_OUTPUT=stdout
    
    # Docker配置
    export DOCKER_HOST=unix:///var/run/docker.sock
    export DOCKER_API_VERSION=1.41
    
    # 监控配置
    export METRICS_ENABLED=true
    export HEALTH_CHECK_INTERVAL=10s
    
    log_info "环境变量设置完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p data
    mkdir -p data/storage
    mkdir -p data/workspaces
    mkdir -p data/artifacts
    mkdir -p data/templates
    mkdir -p data/configs
    mkdir -p logs
    
    log_info "目录创建完成"
}

# 构建服务
build_services() {
    log_info "构建服务..."
    
    # 构建应用管理服务 (包含认证功能)
    log_debug "构建应用管理服务 (包含认证API)..."
    go build -o bin/app-manager ./cmd/app-manager
    
    # 构建脚本执行服务
    log_debug "构建脚本执行服务..."
    go build -o bin/script-service ./cmd/script-service
    
    # 构建CI/CD服务
    log_debug "构建CI/CD服务..."
    go build -o bin/cicd-service ./cmd/cicd-service
    
    # 构建配置服务
    log_debug "构建配置服务..."
    go build -o bin/config-service ./cmd/config-service
    
    log_info "服务构建完成"
}

# 启动单个服务
start_service() {
    local service_name=$1
    local config_file=$2
    local port=$3
    local binary_path=$4
    
    log_info "启动 ${service_name}..."
    
    # 检查端口是否被占用
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口 $port 已被占用，尝试终止占用进程..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 启动服务 (后台运行)
    nohup $binary_path --config=$config_file > logs/${service_name}.log 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > logs/${service_name}.pid
    
    # 等待服务启动
    sleep 3
    
    # 检查服务是否启动成功
    if kill -0 $pid 2>/dev/null; then
        log_info "${service_name} 启动成功 (PID: $pid, Port: $port)"
        
        # 健康检查
        local health_url="http://localhost:$port/health"
        local retry_count=0
        local max_retries=10
        
        while [ $retry_count -lt $max_retries ]; do
            if curl -s $health_url > /dev/null 2>&1; then
                log_info "${service_name} 健康检查通过"
                break
            fi
            retry_count=$((retry_count + 1))
            sleep 1
        done
        
        if [ $retry_count -eq $max_retries ]; then
            log_warn "${service_name} 健康检查失败，但服务可能仍在启动中"
        fi
    else
        log_error "${service_name} 启动失败"
        return 1
    fi
}

# 启动所有服务
start_all_services() {
    log_info "启动所有服务..."
    
    # 启动应用管理服务
    start_service "app-manager" "configs/app-manager.dev.yaml" 8081 "./bin/app-manager"
    
    # 启动脚本执行服务
    start_service "script-service" "configs/script-service.dev.yaml" 8084 "./bin/script-service"
    
    # 启动CI/CD服务
    start_service "cicd-service" "configs/cicd-service.dev.yaml" 8082 "./bin/cicd-service"
    
    # 启动配置服务
    start_service "config-service" "configs/config-service.dev.yaml" 8083 "./bin/config-service"
    
    log_info "所有服务启动完成"
}

# 显示服务状态
show_service_status() {
    echo
    log_info "服务状态："
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                        服务状态                            ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  📱 应用管理服务:    http://localhost:8081                ║${NC}"
    echo -e "${CYAN}║  📜 脚本执行服务:    http://localhost:8084                ║${NC}"
    echo -e "${CYAN}║  🚀 CI/CD服务:       http://localhost:8082                ║${NC}"
    echo -e "${CYAN}║  ⚙️  配置服务:        http://localhost:8083                ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  📚 API文档:                                               ║${NC}"
    echo -e "${CYAN}║    - 应用管理: http://localhost:8081/swagger               ║${NC}"
    echo -e "${CYAN}║    - 脚本执行: http://localhost:8084/swagger               ║${NC}"
    echo -e "${CYAN}║    - CI/CD:    http://localhost:8082/swagger               ║${NC}"
    echo -e "${CYAN}║    - 配置服务: http://localhost:8083/swagger               ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 显示使用说明
show_usage_info() {
    echo
    log_info "开发环境使用说明："
    echo -e "${PURPLE}• 🔓 认证已禁用，所有API请求无需Token${NC}"
    echo -e "${PURPLE}• 🧑‍💻 默认使用开发用户身份 (admin权限)${NC}"
    echo -e "${PURPLE}• 📝 日志文件位于 logs/ 目录${NC}"
    echo -e "${PURPLE}• 🛑 停止服务: ./scripts/stop-dev-mode.sh${NC}"
    echo -e "${PURPLE}• 📊 查看日志: tail -f logs/[service-name].log${NC}"
    echo
    echo -e "${YELLOW}⚠️  注意：此为开发环境配置，请勿在生产环境使用！${NC}"
    echo
}

# 主函数
main() {
    echo -e "${GREEN}🚀 启动PaaS平台开发环境${NC}"
    echo
    
    # 显示开发模式警告
    show_dev_warning
    
    # 检查必要工具
    check_prerequisites
    
    # 设置环境变量
    setup_dev_env
    
    # 创建目录
    create_directories
    
    # 构建服务
    build_services
    
    # 启动服务
    start_all_services
    
    # 显示状态
    show_service_status
    
    # 显示使用说明
    show_usage_info
    
    log_info "开发环境启动完成！"
}

# 执行主函数
main "$@"
