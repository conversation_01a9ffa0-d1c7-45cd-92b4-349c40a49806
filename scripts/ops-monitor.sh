#!/bin/bash

# PaaS 平台运维监控脚本
# 实时监控系统状态、性能指标和健康状况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MONITOR_INTERVAL=30
LOG_FILE="/tmp/paas-monitor.log"

echo -e "${PURPLE}📊 PaaS 平台运维监控脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：记录日志
log_message() {
    local level=$1
    local message=$2
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$level] $message" >> "$LOG_FILE"
}

# 函数：检查 Kubernetes 集群状态
check_cluster_status() {
    print_title "检查 Kubernetes 集群状态"
    
    # 检查集群连接
    if kubectl cluster-info &> /dev/null; then
        print_success "Kubernetes 集群连接正常"
        log_message "INFO" "Kubernetes cluster connection OK"
    else
        print_error "无法连接到 Kubernetes 集群"
        log_message "ERROR" "Cannot connect to Kubernetes cluster"
        return 1
    fi
    
    # 检查节点状态
    print_info "检查节点状态..."
    local node_status=$(kubectl get nodes --no-headers | awk '{print $2}' | sort | uniq -c)
    echo "$node_status"
    
    local ready_nodes=$(kubectl get nodes --no-headers | grep -c "Ready" || echo "0")
    local total_nodes=$(kubectl get nodes --no-headers | wc -l)
    
    if [[ $ready_nodes -eq $total_nodes ]]; then
        print_success "所有节点状态正常 ($ready_nodes/$total_nodes)"
        log_message "INFO" "All nodes ready: $ready_nodes/$total_nodes"
    else
        print_warning "部分节点状态异常 ($ready_nodes/$total_nodes)"
        log_message "WARN" "Some nodes not ready: $ready_nodes/$total_nodes"
    fi
    
    # 检查系统组件
    print_info "检查系统组件..."
    kubectl get componentstatuses 2>/dev/null || print_warning "无法获取组件状态"
    
    echo ""
}

# 函数：检查应用状态
check_application_status() {
    local namespace=${1:-"paas-platform"}
    
    print_title "检查应用状态 (命名空间: $namespace)"
    
    # 检查命名空间是否存在
    if ! kubectl get namespace "$namespace" &> /dev/null; then
        print_error "命名空间不存在: $namespace"
        return 1
    fi
    
    # 检查 Pod 状态
    print_info "检查 Pod 状态..."
    local pod_status=$(kubectl get pods -n "$namespace" --no-headers 2>/dev/null)
    
    if [[ -z "$pod_status" ]]; then
        print_warning "命名空间中没有 Pod"
        return 0
    fi
    
    echo "$pod_status" | while read -r line; do
        local pod_name=$(echo "$line" | awk '{print $1}')
        local ready=$(echo "$line" | awk '{print $2}')
        local status=$(echo "$line" | awk '{print $3}')
        local restarts=$(echo "$line" | awk '{print $4}')
        local age=$(echo "$line" | awk '{print $5}')
        
        if [[ "$status" == "Running" ]]; then
            print_success "Pod $pod_name: $status ($ready) - $age"
        elif [[ "$status" == "Pending" ]]; then
            print_warning "Pod $pod_name: $status ($ready) - $age"
        else
            print_error "Pod $pod_name: $status ($ready) - $age"
            log_message "ERROR" "Pod $pod_name in $namespace has status: $status"
        fi
        
        # 检查重启次数
        if [[ $restarts -gt 5 ]]; then
            print_warning "Pod $pod_name 重启次数过多: $restarts"
            log_message "WARN" "Pod $pod_name in $namespace has high restart count: $restarts"
        fi
    done
    
    # 检查服务状态
    print_info "检查服务状态..."
    kubectl get services -n "$namespace" --no-headers 2>/dev/null | while read -r line; do
        local service_name=$(echo "$line" | awk '{print $1}')
        local service_type=$(echo "$line" | awk '{print $2}')
        local cluster_ip=$(echo "$line" | awk '{print $3}')
        local external_ip=$(echo "$line" | awk '{print $4}')
        
        print_info "Service $service_name: $service_type ($cluster_ip)"
    done
    
    # 检查部署状态
    print_info "检查部署状态..."
    kubectl get deployments -n "$namespace" --no-headers 2>/dev/null | while read -r line; do
        local deployment_name=$(echo "$line" | awk '{print $1}')
        local ready=$(echo "$line" | awk '{print $2}')
        local up_to_date=$(echo "$line" | awk '{print $3}')
        local available=$(echo "$line" | awk '{print $4}')
        local age=$(echo "$line" | awk '{print $5}')
        
        if [[ "$ready" == "$available" ]] && [[ "$available" != "0" ]]; then
            print_success "Deployment $deployment_name: $ready/$available ready"
        else
            print_warning "Deployment $deployment_name: $ready/$available ready"
            log_message "WARN" "Deployment $deployment_name in $namespace not fully ready: $ready/$available"
        fi
    done
    
    echo ""
}

# 函数：检查资源使用情况
check_resource_usage() {
    local namespace=${1:-"paas-platform"}
    
    print_title "检查资源使用情况"
    
    # 检查节点资源使用
    print_info "节点资源使用情况..."
    if command -v kubectl-top &> /dev/null || kubectl top nodes &> /dev/null; then
        kubectl top nodes 2>/dev/null || print_warning "无法获取节点资源使用情况"
    else
        print_warning "kubectl top 不可用，跳过资源使用检查"
    fi
    
    # 检查 Pod 资源使用
    print_info "Pod 资源使用情况..."
    if kubectl top pods -n "$namespace" &> /dev/null; then
        kubectl top pods -n "$namespace" 2>/dev/null | head -10
    else
        print_warning "无法获取 Pod 资源使用情况"
    fi
    
    # 检查存储使用
    print_info "存储使用情况..."
    kubectl get pvc -n "$namespace" --no-headers 2>/dev/null | while read -r line; do
        local pvc_name=$(echo "$line" | awk '{print $1}')
        local status=$(echo "$line" | awk '{print $2}')
        local volume=$(echo "$line" | awk '{print $3}')
        local capacity=$(echo "$line" | awk '{print $4}')
        local access_modes=$(echo "$line" | awk '{print $5}')
        local storage_class=$(echo "$line" | awk '{print $6}')
        local age=$(echo "$line" | awk '{print $7}')
        
        if [[ "$status" == "Bound" ]]; then
            print_success "PVC $pvc_name: $status ($capacity)"
        else
            print_warning "PVC $pvc_name: $status"
        fi
    done
    
    echo ""
}

# 函数：检查网络连通性
check_network_connectivity() {
    local namespace=${1:-"paas-platform"}
    
    print_title "检查网络连通性"
    
    # 检查服务端点
    print_info "检查服务端点..."
    kubectl get endpoints -n "$namespace" --no-headers 2>/dev/null | while read -r line; do
        local endpoint_name=$(echo "$line" | awk '{print $1}')
        local endpoints=$(echo "$line" | awk '{print $2}')
        local age=$(echo "$line" | awk '{print $3}')
        
        if [[ "$endpoints" == "<none>" ]]; then
            print_warning "Endpoint $endpoint_name: 无可用端点"
            log_message "WARN" "Endpoint $endpoint_name in $namespace has no endpoints"
        else
            print_success "Endpoint $endpoint_name: $endpoints"
        fi
    done
    
    # 检查 Ingress
    print_info "检查 Ingress..."
    kubectl get ingress -n "$namespace" --no-headers 2>/dev/null | while read -r line; do
        local ingress_name=$(echo "$line" | awk '{print $1}')
        local class=$(echo "$line" | awk '{print $2}')
        local hosts=$(echo "$line" | awk '{print $3}')
        local address=$(echo "$line" | awk '{print $4}')
        local ports=$(echo "$line" | awk '{print $5}')
        local age=$(echo "$line" | awk '{print $6}')
        
        if [[ -n "$address" ]]; then
            print_success "Ingress $ingress_name: $hosts -> $address"
        else
            print_warning "Ingress $ingress_name: $hosts (无地址)"
        fi
    done
    
    echo ""
}

# 函数：检查应用健康状态
check_application_health() {
    local namespace=${1:-"paas-platform"}
    
    print_title "检查应用健康状态"
    
    # 获取服务列表
    local services=$(kubectl get services -n "$namespace" --no-headers 2>/dev/null | awk '{print $1}' | grep -v kubernetes)
    
    for service in $services; do
        print_info "检查服务 $service 健康状态..."
        
        # 获取服务端口
        local port=$(kubectl get service "$service" -n "$namespace" -o jsonpath='{.spec.ports[0].port}' 2>/dev/null)
        
        if [[ -n "$port" ]]; then
            # 在集群内部检查健康状态
            kubectl run health-check-$service \
                --image=curlimages/curl:latest \
                --rm -i --restart=Never \
                --namespace="$namespace" \
                --timeout=30s \
                --command -- curl -f "http://$service:$port/health" &> /dev/null
            
            if [[ $? -eq 0 ]]; then
                print_success "服务 $service 健康检查通过"
            else
                print_warning "服务 $service 健康检查失败"
                log_message "WARN" "Health check failed for service $service in $namespace"
            fi
        else
            print_warning "无法获取服务 $service 的端口信息"
        fi
    done
    
    echo ""
}

# 函数：检查日志错误
check_logs_for_errors() {
    local namespace=${1:-"paas-platform"}
    
    print_title "检查应用日志错误"
    
    # 获取 Pod 列表
    local pods=$(kubectl get pods -n "$namespace" --no-headers 2>/dev/null | awk '{print $1}')
    
    for pod in $pods; do
        print_info "检查 Pod $pod 日志..."
        
        # 检查最近的错误日志
        local error_count=$(kubectl logs "$pod" -n "$namespace" --tail=100 2>/dev/null | grep -i "error\|exception\|fatal" | wc -l)
        
        if [[ $error_count -gt 0 ]]; then
            print_warning "Pod $pod 发现 $error_count 个错误日志"
            log_message "WARN" "Pod $pod in $namespace has $error_count error logs"
            
            # 显示最近的错误
            kubectl logs "$pod" -n "$namespace" --tail=100 2>/dev/null | grep -i "error\|exception\|fatal" | tail -3
        else
            print_success "Pod $pod 日志正常"
        fi
    done
    
    echo ""
}

# 函数：生成监控报告
generate_report() {
    local namespace=${1:-"paas-platform"}
    local report_file="/tmp/paas-monitor-report-$(date +%Y%m%d-%H%M%S).txt"
    
    print_title "生成监控报告"
    
    {
        echo "PaaS 平台监控报告"
        echo "生成时间: $(date)"
        echo "命名空间: $namespace"
        echo "=================================="
        echo ""
        
        echo "集群状态:"
        kubectl cluster-info 2>/dev/null || echo "集群连接失败"
        echo ""
        
        echo "节点状态:"
        kubectl get nodes 2>/dev/null || echo "无法获取节点信息"
        echo ""
        
        echo "Pod 状态:"
        kubectl get pods -n "$namespace" 2>/dev/null || echo "无法获取 Pod 信息"
        echo ""
        
        echo "服务状态:"
        kubectl get services -n "$namespace" 2>/dev/null || echo "无法获取服务信息"
        echo ""
        
        echo "部署状态:"
        kubectl get deployments -n "$namespace" 2>/dev/null || echo "无法获取部署信息"
        echo ""
        
        echo "资源使用:"
        kubectl top nodes 2>/dev/null || echo "无法获取资源使用信息"
        kubectl top pods -n "$namespace" 2>/dev/null || echo "无法获取 Pod 资源使用信息"
        echo ""
        
        echo "事件信息:"
        kubectl get events -n "$namespace" --sort-by='.lastTimestamp' | tail -20 2>/dev/null || echo "无法获取事件信息"
        
    } > "$report_file"
    
    print_success "监控报告已生成: $report_file"
    echo "$report_file"
}

# 函数：发送告警
send_alert() {
    local level=$1
    local message=$2
    local namespace=${3:-"paas-platform"}
    
    log_message "$level" "$message"
    
    # 发送 Slack 通知
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        local color="warning"
        local emoji="⚠️"
        
        case "$level" in
            "ERROR")
                color="danger"
                emoji="🚨"
                ;;
            "WARN")
                color="warning"
                emoji="⚠️"
                ;;
            "INFO")
                color="good"
                emoji="ℹ️"
                ;;
        esac
        
        curl -X POST "$SLACK_WEBHOOK_URL" \
            -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"$emoji PaaS 平台监控告警\",
                    \"fields\": [
                        {\"title\": \"级别\", \"value\": \"$level\", \"short\": true},
                        {\"title\": \"命名空间\", \"value\": \"$namespace\", \"short\": true},
                        {\"title\": \"消息\", \"value\": \"$message\", \"short\": false}
                    ],
                    \"footer\": \"PaaS Platform Monitor\",
                    \"ts\": $(date +%s)
                }]
            }" &> /dev/null
    fi
}

# 函数：持续监控
continuous_monitor() {
    local namespace=${1:-"paas-platform"}
    local interval=${2:-$MONITOR_INTERVAL}
    
    print_title "启动持续监控模式"
    print_info "监控间隔: ${interval}秒"
    print_info "命名空间: $namespace"
    print_info "按 Ctrl+C 停止监控"
    echo ""
    
    while true; do
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 执行监控检查..."
        
        # 执行各项检查
        check_cluster_status
        check_application_status "$namespace"
        check_resource_usage "$namespace"
        check_network_connectivity "$namespace"
        
        echo "等待 ${interval} 秒后进行下次检查..."
        echo "=================================================="
        sleep "$interval"
    done
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台运维监控脚本"
    echo ""
    echo "用法: $0 [选项] [命名空间]"
    echo ""
    echo "选项:"
    echo "  -c, --continuous         持续监控模式"
    echo "  -i, --interval <秒数>    监控间隔 (默认: 30)"
    echo "  -r, --report             生成监控报告"
    echo "  -h, --health             检查应用健康状态"
    echo "  -l, --logs               检查日志错误"
    echo "  --help                   显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  SLACK_WEBHOOK_URL        Slack 通知 Webhook URL"
    echo ""
    echo "示例:"
    echo "  $0                       # 执行一次性检查"
    echo "  $0 -c paas-production    # 持续监控生产环境"
    echo "  $0 -r paas-staging       # 生成测试环境报告"
    echo "  $0 -h paas-platform      # 检查应用健康状态"
}

# 主函数
main() {
    local namespace="paas-platform"
    local continuous=false
    local interval=$MONITOR_INTERVAL
    local generate_report_only=false
    local health_check_only=false
    local logs_check_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--continuous)
                continuous=true
                shift
                ;;
            -i|--interval)
                interval="$2"
                shift 2
                ;;
            -r|--report)
                generate_report_only=true
                shift
                ;;
            -h|--health)
                health_check_only=true
                shift
                ;;
            -l|--logs)
                logs_check_only=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                namespace="$1"
                shift
                ;;
        esac
    done
    
    # 创建日志文件
    touch "$LOG_FILE"
    
    print_info "开始监控命名空间: $namespace"
    log_message "INFO" "Starting monitor for namespace: $namespace"
    
    if [[ "$generate_report_only" == "true" ]]; then
        generate_report "$namespace"
    elif [[ "$health_check_only" == "true" ]]; then
        check_application_health "$namespace"
    elif [[ "$logs_check_only" == "true" ]]; then
        check_logs_for_errors "$namespace"
    elif [[ "$continuous" == "true" ]]; then
        continuous_monitor "$namespace" "$interval"
    else
        # 执行一次性检查
        check_cluster_status
        check_application_status "$namespace"
        check_resource_usage "$namespace"
        check_network_connectivity "$namespace"
        check_application_health "$namespace"
        check_logs_for_errors "$namespace"
        
        print_success "监控检查完成"
    fi
}

# 信号处理
trap 'print_info "监控已停止"; exit 0' INT TERM

# 脚本入口
main "$@"
