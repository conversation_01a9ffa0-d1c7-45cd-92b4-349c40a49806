#!/bin/bash

# PaaS 平台数据迁移验证脚本
# 验证用户数据从 App Manager 到 User Service 的迁移是否成功
#
# 使用方法：
#   chmod +x scripts/validate-data-migration.sh
#   ./scripts/validate-data-migration.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
SOURCE_DB_HOST="${SOURCE_DB_HOST:-localhost}"
SOURCE_DB_PORT="${SOURCE_DB_PORT:-5432}"
SOURCE_DB_NAME="${SOURCE_DB_NAME:-paas_app}"
SOURCE_DB_USER="${SOURCE_DB_USER:-paas_user}"

TARGET_DB_HOST="${TARGET_DB_HOST:-localhost}"
TARGET_DB_PORT="${TARGET_DB_PORT:-5432}"
TARGET_DB_NAME="${TARGET_DB_NAME:-paas_users}"
TARGET_DB_USER="${TARGET_DB_USER:-paas_user}"

echo -e "${BLUE}🔍 PaaS 平台数据迁移验证开始${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：执行SQL查询
execute_sql() {
    local host=$1
    local port=$2
    local db=$3
    local user=$4
    local sql=$5
    
    PGPASSWORD="${PGPASSWORD:-password}" psql -h "$host" -p "$port" -d "$db" -U "$user" -t -c "$sql" 2>/dev/null
}

# 函数：检查数据库连接
check_database_connection() {
    local db_name=$1
    local host=$2
    local port=$3
    local db=$4
    local user=$5
    
    print_info "检查 $db_name 数据库连接..."
    
    if execute_sql "$host" "$port" "$db" "$user" "SELECT 1;" > /dev/null 2>&1; then
        print_success "$db_name 数据库连接正常"
        return 0
    else
        print_error "$db_name 数据库连接失败"
        return 1
    fi
}

# 函数：验证表结构
validate_table_structure() {
    print_info "验证目标数据库表结构..."
    
    local required_tables=("users" "roles" "user_roles" "user_sessions")
    local missing_tables=()
    
    for table in "${required_tables[@]}"; do
        local exists=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '$table');")
        
        if [[ "$exists" =~ "t" ]]; then
            print_success "表 $table 存在"
        else
            print_error "表 $table 不存在"
            missing_tables+=("$table")
        fi
    done
    
    if [ ${#missing_tables[@]} -eq 0 ]; then
        print_success "所有必需的表都存在"
        return 0
    else
        print_error "缺少表: ${missing_tables[*]}"
        return 1
    fi
}

# 函数：验证数据完整性
validate_data_integrity() {
    print_info "验证数据完整性..."
    
    # 检查用户数据
    local source_user_count=$(execute_sql "$SOURCE_DB_HOST" "$SOURCE_DB_PORT" "$SOURCE_DB_NAME" "$SOURCE_DB_USER" \
        "SELECT COUNT(*) FROM users WHERE deleted_at IS NULL;" | tr -d ' ')
    
    local target_user_count=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM users;" | tr -d ' ')
    
    print_info "源数据库用户数: $source_user_count"
    print_info "目标数据库用户数: $target_user_count"
    
    if [ "$source_user_count" -eq "$target_user_count" ]; then
        print_success "用户数据数量一致"
    else
        print_warning "用户数据数量不一致，可能需要检查迁移逻辑"
    fi
    
    # 检查关键用户数据
    local admin_exists=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM users WHERE email LIKE '%admin%';" | tr -d ' ')
    
    if [ "$admin_exists" -gt 0 ]; then
        print_success "管理员用户存在"
    else
        print_warning "未找到管理员用户"
    fi
    
    return 0
}

# 函数：验证数据一致性
validate_data_consistency() {
    print_info "验证数据一致性..."
    
    # 检查用户角色关联的完整性
    local orphaned_user_roles=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM user_roles ur LEFT JOIN users u ON ur.user_id = u.id WHERE u.id IS NULL;" | tr -d ' ')
    
    if [ "$orphaned_user_roles" -eq 0 ]; then
        print_success "用户角色关联数据完整"
    else
        print_error "发现 $orphaned_user_roles 个孤立的用户角色关联"
    fi
    
    local orphaned_role_assignments=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM user_roles ur LEFT JOIN roles r ON ur.role_id = r.id WHERE r.id IS NULL;" | tr -d ' ')
    
    if [ "$orphaned_role_assignments" -eq 0 ]; then
        print_success "角色分配数据完整"
    else
        print_error "发现 $orphaned_role_assignments 个孤立的角色分配"
    fi
    
    return 0
}

# 函数：验证默认数据
validate_default_data() {
    print_info "验证默认数据..."
    
    # 检查默认角色
    local default_roles=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM roles WHERE is_system = true;" | tr -d ' ')
    
    if [ "$default_roles" -gt 0 ]; then
        print_success "默认系统角色存在 ($default_roles 个)"
    else
        print_warning "未找到默认系统角色"
    fi
    
    # 检查默认管理员
    local default_admin=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM users WHERE id = 'admin-user-001';" | tr -d ' ')
    
    if [ "$default_admin" -gt 0 ]; then
        print_success "默认管理员用户存在"
    else
        print_warning "未找到默认管理员用户"
    fi
    
    return 0
}

# 函数：性能测试
performance_test() {
    print_info "执行基本性能测试..."
    
    # 测试用户查询性能
    local start_time=$(date +%s%N)
    execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT * FROM users LIMIT 100;" > /dev/null
    local end_time=$(date +%s%N)
    
    local duration=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    if [ "$duration" -lt 100 ]; then
        print_success "用户查询性能良好 (${duration}ms)"
    elif [ "$duration" -lt 500 ]; then
        print_warning "用户查询性能一般 (${duration}ms)"
    else
        print_error "用户查询性能较差 (${duration}ms)"
    fi
    
    return 0
}

# 函数：生成迁移报告
generate_migration_report() {
    local total_checks=$1
    local passed_checks=$2
    local failed_checks=$((total_checks - passed_checks))
    
    echo ""
    echo "=================================================="
    echo -e "${BLUE}📊 数据迁移验证报告${NC}"
    echo "=================================================="
    
    # 基本统计
    echo "验证时间: $(date)"
    echo "总检查项: $total_checks"
    echo -e "通过: ${GREEN}$passed_checks${NC}"
    echo -e "失败: ${RED}$failed_checks${NC}"
    
    local success_rate=$((passed_checks * 100 / total_checks))
    echo "成功率: $success_rate%"
    
    # 数据统计
    echo ""
    echo "数据统计:"
    local user_count=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM users;" | tr -d ' ')
    local role_count=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM roles;" | tr -d ' ')
    local user_role_count=$(execute_sql "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER" \
        "SELECT COUNT(*) FROM user_roles;" | tr -d ' ')
    
    echo "- 用户总数: $user_count"
    echo "- 角色总数: $role_count"
    echo "- 用户角色关联: $user_role_count"
    
    # 结论
    echo ""
    if [ $success_rate -ge 90 ]; then
        echo -e "${GREEN}🎉 数据迁移验证成功！${NC}"
        echo "数据迁移质量优秀，可以继续后续步骤。"
        return 0
    elif [ $success_rate -ge 70 ]; then
        echo -e "${YELLOW}⚠️  数据迁移基本成功，但存在一些问题${NC}"
        echo "建议检查失败的项目并进行修复。"
        return 1
    else
        echo -e "${RED}❌ 数据迁移验证失败${NC}"
        echo "存在严重问题，需要重新检查迁移过程。"
        return 2
    fi
}

# 主验证流程
main() {
    local total_checks=0
    local passed_checks=0
    
    echo -e "${BLUE}第一阶段：数据库连接检查${NC}"
    echo "--------------------------------------------------"
    
    # 检查源数据库连接
    total_checks=$((total_checks + 1))
    if check_database_connection "源数据库 (App Manager)" "$SOURCE_DB_HOST" "$SOURCE_DB_PORT" "$SOURCE_DB_NAME" "$SOURCE_DB_USER"; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 检查目标数据库连接
    total_checks=$((total_checks + 1))
    if check_database_connection "目标数据库 (User Service)" "$TARGET_DB_HOST" "$TARGET_DB_PORT" "$TARGET_DB_NAME" "$TARGET_DB_USER"; then
        passed_checks=$((passed_checks + 1))
    fi
    
    echo ""
    echo -e "${BLUE}第二阶段：表结构验证${NC}"
    echo "--------------------------------------------------"
    
    # 验证表结构
    total_checks=$((total_checks + 1))
    if validate_table_structure; then
        passed_checks=$((passed_checks + 1))
    fi
    
    echo ""
    echo -e "${BLUE}第三阶段：数据完整性验证${NC}"
    echo "--------------------------------------------------"
    
    # 验证数据完整性
    total_checks=$((total_checks + 1))
    if validate_data_integrity; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 验证数据一致性
    total_checks=$((total_checks + 1))
    if validate_data_consistency; then
        passed_checks=$((passed_checks + 1))
    fi
    
    echo ""
    echo -e "${BLUE}第四阶段：默认数据验证${NC}"
    echo "--------------------------------------------------"
    
    # 验证默认数据
    total_checks=$((total_checks + 1))
    if validate_default_data; then
        passed_checks=$((passed_checks + 1))
    fi
    
    echo ""
    echo -e "${BLUE}第五阶段：性能测试${NC}"
    echo "--------------------------------------------------"
    
    # 性能测试
    total_checks=$((total_checks + 1))
    if performance_test; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 生成报告
    generate_migration_report $total_checks $passed_checks
}

# 检查依赖
check_dependencies() {
    if ! command -v psql &> /dev/null; then
        print_error "psql 命令未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
}

# 脚本入口
echo -e "${BLUE}🚀 开始数据迁移验证...${NC}"
echo ""

# 检查依赖
check_dependencies

# 执行主验证流程
main

exit_code=$?

echo ""
echo -e "${BLUE}📋 下一步建议：${NC}"
if [ $exit_code -eq 0 ]; then
    echo "1. 数据迁移验证成功，可以继续架构重构"
    echo "2. 更新应用配置，指向新的 User Service"
    echo "3. 重启相关服务并进行功能测试"
    echo "4. 备份原始数据库以备回滚"
elif [ $exit_code -eq 1 ]; then
    echo "1. 检查并修复失败的验证项"
    echo "2. 重新运行数据迁移脚本"
    echo "3. 验证数据库权限和网络连接"
    echo "4. 检查迁移脚本的逻辑"
else
    echo "1. 停止使用新的 User Service"
    echo "2. 检查数据迁移脚本是否正确执行"
    echo "3. 验证数据库配置和连接"
    echo "4. 联系技术支持进行故障排除"
fi

echo ""
echo -e "${BLUE}🔍 验证完成！${NC}"

exit $exit_code
