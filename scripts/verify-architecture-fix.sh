#!/bin/bash

# PaaS 平台架构重构验证脚本
# 验证认证服务分离和前端登录修复是否成功
#
# 使用方法：
#   chmod +x scripts/verify-architecture-fix.sh
#   ./scripts/verify-architecture-fix.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
USER_SERVICE_URL="http://localhost:8083"
APP_MANAGER_URL="http://localhost:8081"
API_GATEWAY_URL="http://localhost:8080"
FRONTEND_URL="http://localhost:3000"

echo -e "${BLUE}🔍 PaaS 平台架构重构验证开始${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查服务健康状态
check_service_health() {
    local service_name=$1
    local url=$2
    local expected_service=$3
    
    print_info "检查 $service_name 健康状态..."
    
    if response=$(curl -s -w "%{http_code}" "$url/health" 2>/dev/null); then
        http_code="${response: -3}"
        body="${response%???}"
        
        if [ "$http_code" = "200" ]; then
            # 解析JSON响应中的service字段
            if echo "$body" | grep -q "\"service\".*\"$expected_service\""; then
                print_success "$service_name 健康检查通过 (HTTP $http_code)"
                return 0
            else
                print_warning "$service_name 响应正常但服务标识不匹配"
                echo "  期望: $expected_service"
                echo "  响应: $body"
                return 1
            fi
        else
            print_error "$service_name 健康检查失败 (HTTP $http_code)"
            return 1
        fi
    else
        print_error "$service_name 无法连接"
        return 1
    fi
}

# 函数：测试认证API
test_auth_api() {
    print_info "测试认证API路由..."
    
    # 测试登录API（应该路由到User Service）
    local login_data='{"username":"<EMAIL>","password":"password"}'
    
    if response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$API_GATEWAY_URL/api/v1/auth/login" 2>/dev/null); then
        
        http_code="${response: -3}"
        body="${response%???}"
        
        if [ "$http_code" = "200" ] || [ "$http_code" = "401" ]; then
            print_success "认证API路由正常 (HTTP $http_code)"
            return 0
        else
            print_error "认证API路由异常 (HTTP $http_code)"
            echo "  响应: $body"
            return 1
        fi
    else
        print_error "认证API无法访问"
        return 1
    fi
}

# 函数：测试应用管理API
test_app_api() {
    print_info "测试应用管理API路由..."
    
    # 测试应用列表API（需要认证，但在开发模式下会跳过）
    if response=$(curl -s -w "%{http_code}" \
        "$API_GATEWAY_URL/api/v1/apps" 2>/dev/null); then
        
        http_code="${response: -3}"
        body="${response%???}"
        
        if [ "$http_code" = "200" ] || [ "$http_code" = "401" ]; then
            print_success "应用管理API路由正常 (HTTP $http_code)"
            return 0
        else
            print_error "应用管理API路由异常 (HTTP $http_code)"
            echo "  响应: $body"
            return 1
        fi
    else
        print_error "应用管理API无法访问"
        return 1
    fi
}

# 函数：验证App Manager不再处理认证
verify_app_manager_no_auth() {
    print_info "验证App Manager不再处理认证..."
    
    # 尝试直接访问App Manager的认证端点（应该不存在）
    if response=$(curl -s -w "%{http_code}" \
        "$APP_MANAGER_URL/api/v1/auth/login" 2>/dev/null); then
        
        http_code="${response: -3}"
        
        if [ "$http_code" = "404" ]; then
            print_success "App Manager已移除认证路由"
            return 0
        else
            print_warning "App Manager仍然存在认证路由 (HTTP $http_code)"
            return 1
        fi
    else
        print_error "无法连接到App Manager"
        return 1
    fi
}

# 函数：检查前端配置
check_frontend_config() {
    print_info "检查前端代理配置..."
    
    local vite_config="web/vite.config.ts"
    
    if [ -f "$vite_config" ]; then
        # 检查是否包含正确的代理配置
        if grep -q "'/api/v1/auth':" "$vite_config" && \
           grep -q "http://localhost:8083" "$vite_config"; then
            print_success "前端代理配置正确"
            return 0
        else
            print_error "前端代理配置不正确"
            return 1
        fi
    else
        print_error "前端配置文件不存在: $vite_config"
        return 1
    fi
}

# 函数：检查Token处理修复
check_token_fix() {
    print_info "检查Token处理修复..."
    
    local request_file="web/src/utils/request.ts"
    local dev_mode_file="web/src/utils/dev-mode.ts"
    
    if [ -f "$request_file" ] && [ -f "$dev_mode_file" ]; then
        # 检查是否包含修复的Token处理逻辑
        if grep -q "refreshTokenPromise" "$request_file" && \
           grep -q "dev_mode.*true" "$dev_mode_file"; then
            print_success "Token处理修复已应用"
            return 0
        else
            print_warning "Token处理修复可能不完整"
            return 1
        fi
    else
        print_error "前端Token处理文件不存在"
        return 1
    fi
}

# 函数：生成验证报告
generate_report() {
    local total_checks=$1
    local passed_checks=$2
    local failed_checks=$((total_checks - passed_checks))
    
    echo ""
    echo "=================================================="
    echo -e "${BLUE}📊 验证报告${NC}"
    echo "=================================================="
    echo "总检查项: $total_checks"
    echo -e "通过: ${GREEN}$passed_checks${NC}"
    echo -e "失败: ${RED}$failed_checks${NC}"
    
    local success_rate=$((passed_checks * 100 / total_checks))
    echo "成功率: $success_rate%"
    
    if [ $success_rate -ge 80 ]; then
        echo -e "${GREEN}🎉 架构重构基本成功！${NC}"
        return 0
    elif [ $success_rate -ge 60 ]; then
        echo -e "${YELLOW}⚠️  架构重构部分成功，需要进一步调整${NC}"
        return 1
    else
        echo -e "${RED}❌ 架构重构失败，需要重新检查配置${NC}"
        return 2
    fi
}

# 主验证流程
main() {
    local total_checks=0
    local passed_checks=0
    
    echo -e "${BLUE}第一阶段：服务健康检查${NC}"
    echo "--------------------------------------------------"
    
    # 检查User Service
    total_checks=$((total_checks + 1))
    if check_service_health "User Service" "$USER_SERVICE_URL" "user-service"; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 检查App Manager
    total_checks=$((total_checks + 1))
    if check_service_health "App Manager" "$APP_MANAGER_URL" "app-manager"; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 检查API Gateway
    total_checks=$((total_checks + 1))
    if check_service_health "API Gateway" "$API_GATEWAY_URL" "api-gateway"; then
        passed_checks=$((passed_checks + 1))
    fi
    
    echo ""
    echo -e "${BLUE}第二阶段：API路由验证${NC}"
    echo "--------------------------------------------------"
    
    # 测试认证API路由
    total_checks=$((total_checks + 1))
    if test_auth_api; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 测试应用管理API路由
    total_checks=$((total_checks + 1))
    if test_app_api; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 验证App Manager不再处理认证
    total_checks=$((total_checks + 1))
    if verify_app_manager_no_auth; then
        passed_checks=$((passed_checks + 1))
    fi
    
    echo ""
    echo -e "${BLUE}第三阶段：前端配置验证${NC}"
    echo "--------------------------------------------------"
    
    # 检查前端配置
    total_checks=$((total_checks + 1))
    if check_frontend_config; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 检查Token处理修复
    total_checks=$((total_checks + 1))
    if check_token_fix; then
        passed_checks=$((passed_checks + 1))
    fi
    
    # 生成报告
    generate_report $total_checks $passed_checks
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        print_error "curl 命令未找到，请安装 curl"
        exit 1
    fi
    
    if ! command -v grep &> /dev/null; then
        print_error "grep 命令未找到"
        exit 1
    fi
}

# 脚本入口
echo -e "${BLUE}🚀 开始验证架构重构...${NC}"
echo ""

# 检查依赖
check_dependencies

# 执行主验证流程
main

exit_code=$?

echo ""
echo -e "${BLUE}📋 下一步建议：${NC}"
if [ $exit_code -eq 0 ]; then
    echo "1. 启动所有服务进行完整测试"
    echo "2. 执行数据迁移脚本"
    echo "3. 测试前端登录功能"
    echo "4. 进行用户验收测试"
elif [ $exit_code -eq 1 ]; then
    echo "1. 检查失败的配置项"
    echo "2. 重新启动相关服务"
    echo "3. 验证网络连接"
    echo "4. 检查日志文件"
else
    echo "1. 重新检查架构重构步骤"
    echo "2. 验证代码修改是否正确"
    echo "3. 检查服务配置文件"
    echo "4. 联系技术支持"
fi

echo ""
echo -e "${BLUE}🔍 验证完成！${NC}"

exit $exit_code
