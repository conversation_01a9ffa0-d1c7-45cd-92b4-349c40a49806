#!/bin/bash

# Docker远程连接测试脚本
# 用于验证远程Docker连接配置是否正确

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker远程连接测试脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -H, --host HOST     Docker主机地址 (例如: tcp://*************:2376)
    --tls               启用TLS连接
    --tlsverify         启用TLS验证
    --tlscert PATH      TLS证书路径
    --tlskey PATH       TLS私钥路径
    --tlscacert PATH    TLS CA证书路径
    -v, --verbose       详细输出

示例:
    $0 -H tcp://*************:2376
    $0 -H tcp://*************:2376 --tls --tlsverify --tlscert /etc/docker/certs
    $0 --help

环境变量:
    DOCKER_HOST         Docker主机地址
    DOCKER_TLS_VERIFY   是否验证TLS证书
    DOCKER_CERT_PATH    TLS证书路径
EOF
}

# 测试网络连通性
test_network_connectivity() {
    local host=$1
    local port=$2
    
    log_info "测试网络连通性: $host:$port"
    
    if command -v nc &> /dev/null; then
        if nc -z -w5 "$host" "$port" 2>/dev/null; then
            log_info "网络连通性测试通过"
            return 0
        else
            log_error "网络连通性测试失败"
            return 1
        fi
    elif command -v telnet &> /dev/null; then
        if timeout 5 telnet "$host" "$port" </dev/null 2>/dev/null | grep -q "Connected"; then
            log_info "网络连通性测试通过"
            return 0
        else
            log_error "网络连通性测试失败"
            return 1
        fi
    else
        log_warn "未找到nc或telnet命令，跳过网络连通性测试"
        return 0
    fi
}

# 测试Docker连接
test_docker_connection() {
    log_info "测试Docker连接..."
    
    # 显示当前Docker环境变量
    log_debug "Docker环境变量:"
    log_debug "DOCKER_HOST=${DOCKER_HOST:-未设置}"
    log_debug "DOCKER_TLS_VERIFY=${DOCKER_TLS_VERIFY:-未设置}"
    log_debug "DOCKER_CERT_PATH=${DOCKER_CERT_PATH:-未设置}"
    
    # 测试Docker版本
    log_info "获取Docker版本信息..."
    if docker version; then
        log_info "Docker版本信息获取成功"
    else
        log_error "Docker版本信息获取失败"
        return 1
    fi
    
    # 测试Docker信息
    log_info "获取Docker系统信息..."
    if docker info; then
        log_info "Docker系统信息获取成功"
    else
        log_error "Docker系统信息获取失败"
        return 1
    fi
    
    # 测试镜像列表
    log_info "获取Docker镜像列表..."
    if docker images; then
        log_info "Docker镜像列表获取成功"
    else
        log_error "Docker镜像列表获取失败"
        return 1
    fi
    
    # 测试容器列表
    log_info "获取Docker容器列表..."
    if docker ps -a; then
        log_info "Docker容器列表获取成功"
    else
        log_error "Docker容器列表获取失败"
        return 1
    fi
    
    return 0
}

# 测试容器运行
test_container_run() {
    log_info "测试容器运行..."
    
    local test_image="hello-world"
    local container_name="docker-test-$(date +%s)"
    
    # 拉取测试镜像
    log_info "拉取测试镜像: $test_image"
    if docker pull "$test_image"; then
        log_info "镜像拉取成功"
    else
        log_error "镜像拉取失败"
        return 1
    fi
    
    # 运行测试容器
    log_info "运行测试容器: $container_name"
    if docker run --name "$container_name" --rm "$test_image"; then
        log_info "容器运行成功"
    else
        log_error "容器运行失败"
        return 1
    fi
    
    return 0
}

# 解析Docker主机地址
parse_docker_host() {
    local docker_host=${DOCKER_HOST:-}
    
    if [[ -z "$docker_host" ]]; then
        log_warn "DOCKER_HOST未设置，使用默认本地连接"
        return 0
    fi
    
    if [[ "$docker_host" =~ ^tcp://([^:]+):([0-9]+)$ ]]; then
        local host="${BASH_REMATCH[1]}"
        local port="${BASH_REMATCH[2]}"
        
        log_info "解析Docker主机: $host:$port"
        test_network_connectivity "$host" "$port"
    elif [[ "$docker_host" =~ ^unix:// ]]; then
        log_info "使用Unix socket连接: $docker_host"
    else
        log_warn "无法解析Docker主机地址: $docker_host"
    fi
}

# 检查TLS证书
check_tls_certificates() {
    if [[ "${DOCKER_TLS_VERIFY:-}" == "1" ]]; then
        local cert_path="${DOCKER_CERT_PATH:-}"
        
        if [[ -z "$cert_path" ]]; then
            log_error "启用了TLS验证但未设置DOCKER_CERT_PATH"
            return 1
        fi
        
        log_info "检查TLS证书: $cert_path"
        
        local required_files=("ca.pem" "cert.pem" "key.pem")
        for file in "${required_files[@]}"; do
            local file_path="$cert_path/$file"
            if [[ -f "$file_path" ]]; then
                log_info "证书文件存在: $file_path"
                
                # 检查文件权限
                local perms=$(stat -c "%a" "$file_path")
                if [[ "$file" == "key.pem" && "$perms" != "400" ]]; then
                    log_warn "私钥文件权限不安全: $file_path ($perms)，建议设置为400"
                fi
            else
                log_error "证书文件不存在: $file_path"
                return 1
            fi
        done
        
        log_info "TLS证书检查通过"
    else
        log_info "未启用TLS验证"
    fi
    
    return 0
}

# 生成配置建议
generate_config_suggestions() {
    log_info "生成配置建议..."
    
    cat << EOF

=== Docker远程连接配置建议 ===

1. 环境变量配置:
   export DOCKER_HOST="tcp://*************:2376"
   export DOCKER_TLS_VERIFY=1
   export DOCKER_CERT_PATH="/etc/docker/certs"

2. 配置文件配置 (configs/script-service.yaml):
   docker:
     host: "tcp://*************:2376"
     tls:
       enabled: true
       verify: true
       cert_path: "/etc/docker/certs"

3. 证书文件准备:
   mkdir -p /etc/docker/certs
   # 从远程Docker主机复制证书文件
   scp user@*************:/etc/docker/certs/*.pem /etc/docker/certs/
   chmod 400 /etc/docker/certs/key.pem
   chmod 444 /etc/docker/certs/ca.pem /etc/docker/certs/cert.pem

4. 远程Docker主机配置:
   # 启用Docker远程API
   sudo systemctl edit docker.service
   # 添加以下内容:
   [Service]
   ExecStart=
   ExecStart=/usr/bin/dockerd -H fd:// -H tcp://0.0.0.0:2376 --tlsverify --tlscacert=/etc/docker/certs/ca.pem --tlscert=/etc/docker/certs/server-cert.pem --tlskey=/etc/docker/certs/server-key.pem

5. 防火墙配置:
   sudo ufw allow from ***********/24 to any port 2376

EOF
}

# 主函数
main() {
    local docker_host=""
    local tls_enabled=false
    local tls_verify=false
    local tls_cert=""
    local tls_key=""
    local tls_ca=""
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -H|--host)
                docker_host="$2"
                shift 2
                ;;
            --tls)
                tls_enabled=true
                shift
                ;;
            --tlsverify)
                tls_verify=true
                shift
                ;;
            --tlscert)
                tls_cert="$2"
                shift 2
                ;;
            --tlskey)
                tls_key="$2"
                shift 2
                ;;
            --tlscacert)
                tls_ca="$2"
                shift 2
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始Docker远程连接测试..."
    
    # 设置环境变量
    if [[ -n "$docker_host" ]]; then
        export DOCKER_HOST="$docker_host"
    fi
    
    if [[ "$tls_verify" == "true" ]]; then
        export DOCKER_TLS_VERIFY=1
    fi
    
    if [[ -n "$tls_cert" ]]; then
        export DOCKER_CERT_PATH="$tls_cert"
    fi
    
    # 解析Docker主机
    parse_docker_host
    
    # 检查TLS证书
    if ! check_tls_certificates; then
        log_error "TLS证书检查失败"
        exit 1
    fi
    
    # 测试Docker连接
    if test_docker_connection; then
        log_info "Docker连接测试通过"
    else
        log_error "Docker连接测试失败"
        generate_config_suggestions
        exit 1
    fi
    
    # 测试容器运行
    if test_container_run; then
        log_info "容器运行测试通过"
    else
        log_error "容器运行测试失败"
        exit 1
    fi
    
    log_info "所有测试通过！Docker远程连接配置正确。"
    
    # 显示配置建议
    if [[ "$verbose" == "true" ]]; then
        generate_config_suggestions
    fi
}

# 执行主函数
main "$@"
