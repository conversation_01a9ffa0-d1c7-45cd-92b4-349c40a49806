#!/bin/bash

# Swagger API 测试脚本
# 作者: PaaS 平台开发团队
# 描述: 测试 Swagger API 文档的完整性和功能

set -e

# 脚本配置
API_BASE="http://localhost:8082"
API_V1="${API_BASE}/api/v1"
TOKEN="dev-token"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查服务状态
check_service() {
    log_info "检查服务状态..."
    
    if ! curl -s "${API_BASE}/health" > /dev/null; then
        log_error "服务未运行，请先启动服务"
        exit 1
    fi
    
    log_info "服务运行正常"
}

# 测试 Swagger 文档访问
test_swagger_docs() {
    log_info "测试 Swagger 文档访问..."
    
    # 测试 Swagger UI
    local swagger_status=$(curl -s -o /dev/null -w "%{http_code}" "${API_BASE}/swagger/index.html")
    if [ "$swagger_status" = "200" ]; then
        log_info "✓ Swagger UI 访问正常"
    else
        log_error "✗ Swagger UI 访问失败 (状态码: $swagger_status)"
        return 1
    fi
    
    # 测试文档重定向
    local docs_status=$(curl -s -o /dev/null -w "%{http_code}" "${API_BASE}/docs")
    if [ "$docs_status" = "301" ]; then
        log_info "✓ 文档重定向正常"
    else
        log_error "✗ 文档重定向失败 (状态码: $docs_status)"
        return 1
    fi
    
    # 测试 API 信息接口
    local api_docs=$(curl -s "${API_BASE}/api/docs")
    if echo "$api_docs" | jq -e '.title' > /dev/null 2>&1; then
        log_info "✓ API 信息接口正常"
        log_debug "API 标题: $(echo "$api_docs" | jq -r '.title')"
    else
        log_error "✗ API 信息接口失败"
        return 1
    fi
    
    # 测试 Swagger JSON
    local swagger_json=$(curl -s "${API_BASE}/swagger/doc.json")
    if echo "$swagger_json" | jq -e '.swagger' > /dev/null 2>&1; then
        log_info "✓ Swagger JSON 格式正确"
        log_debug "Swagger 版本: $(echo "$swagger_json" | jq -r '.swagger')"
    else
        log_error "✗ Swagger JSON 格式错误"
        return 1
    fi
}

# 测试 API 接口
test_api_endpoints() {
    log_info "测试 API 接口..."
    
    # 测试获取应用列表
    log_debug "测试获取应用列表..."
    local apps_response=$(curl -s -H "Authorization: Bearer $TOKEN" "${API_V1}/apps")
    if echo "$apps_response" | jq -e '.apps' > /dev/null 2>&1; then
        log_info "✓ 获取应用列表接口正常"
    else
        log_error "✗ 获取应用列表接口失败"
        log_debug "响应: $apps_response"
        return 1
    fi
    
    # 测试创建应用
    log_debug "测试创建应用..."
    local create_data='{
        "name": "test-swagger-app",
        "description": "Swagger 测试应用",
        "language": "nodejs",
        "framework": "express",
        "version": "1.0.0",
        "git_repo": "https://github.com/example/test-app.git",
        "git_branch": "main",
        "tenant_id": "test-tenant",
        "user_id": "test-user",
        "config": {
            "runtime": {
                "language": "nodejs",
                "version": "18",
                "framework": "express"
            },
            "deploy": {
                "port": 3000,
                "instances": 1,
                "healthCheck": {
                    "path": "/health",
                    "port": 3000
                }
            }
        }
    }'
    
    local create_response=$(curl -s -X POST \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "$create_data" \
        "${API_V1}/apps")
    
    if echo "$create_response" | jq -e '.id' > /dev/null 2>&1; then
        log_info "✓ 创建应用接口正常"
        local app_id=$(echo "$create_response" | jq -r '.id')
        log_debug "创建的应用ID: $app_id"
        
        # 测试获取应用详情
        log_debug "测试获取应用详情..."
        local app_detail=$(curl -s -H "Authorization: Bearer $TOKEN" "${API_V1}/apps/$app_id")
        if echo "$app_detail" | jq -e '.id' > /dev/null 2>&1; then
            log_info "✓ 获取应用详情接口正常"
        else
            log_error "✗ 获取应用详情接口失败"
        fi
        
        # 测试更新应用
        log_debug "测试更新应用..."
        local update_data='{
            "description": "更新后的 Swagger 测试应用"
        }'
        
        local update_response=$(curl -s -X PUT \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "$update_data" \
            "${API_V1}/apps/$app_id")
        
        if echo "$update_response" | jq -e '.id' > /dev/null 2>&1; then
            log_info "✓ 更新应用接口正常"
        else
            log_error "✗ 更新应用接口失败"
        fi
        
        # 测试删除应用
        log_debug "测试删除应用..."
        local delete_response=$(curl -s -X DELETE \
            -H "Authorization: Bearer $TOKEN" \
            "${API_V1}/apps/$app_id")
        
        if echo "$delete_response" | jq -e '.message' > /dev/null 2>&1; then
            log_info "✓ 删除应用接口正常"
        else
            log_error "✗ 删除应用接口失败"
        fi
        
    else
        log_error "✗ 创建应用接口失败"
        log_debug "响应: $create_response"
        return 1
    fi
}

# 测试 Swagger 规范完整性
test_swagger_spec() {
    log_info "测试 Swagger 规范完整性..."
    
    local swagger_json=$(curl -s "${API_BASE}/swagger/doc.json")
    
    # 检查基本信息
    local title=$(echo "$swagger_json" | jq -r '.info.title')
    local version=$(echo "$swagger_json" | jq -r '.info.version')
    local host=$(echo "$swagger_json" | jq -r '.host')
    local base_path=$(echo "$swagger_json" | jq -r '.basePath')
    
    log_debug "API 标题: $title"
    log_debug "API 版本: $version"
    log_debug "主机地址: $host"
    log_debug "基础路径: $base_path"
    
    # 检查路径数量
    local paths_count=$(echo "$swagger_json" | jq '.paths | keys | length')
    log_debug "API 路径数量: $paths_count"
    
    if [ "$paths_count" -gt 0 ]; then
        log_info "✓ Swagger 规范包含 $paths_count 个 API 路径"
    else
        log_error "✗ Swagger 规范中没有 API 路径"
        return 1
    fi
    
    # 检查定义数量
    local definitions_count=$(echo "$swagger_json" | jq '.definitions | keys | length')
    log_debug "数据模型定义数量: $definitions_count"
    
    if [ "$definitions_count" -gt 0 ]; then
        log_info "✓ Swagger 规范包含 $definitions_count 个数据模型定义"
    else
        log_error "✗ Swagger 规范中没有数据模型定义"
        return 1
    fi
    
    # 检查安全定义
    if echo "$swagger_json" | jq -e '.securityDefinitions.BearerAuth' > /dev/null 2>&1; then
        log_info "✓ 包含 Bearer 认证定义"
    else
        log_warn "! 缺少 Bearer 认证定义"
    fi
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    local report_file="swagger-api-test-report.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 获取 API 信息
    local api_info=$(curl -s "${API_BASE}/api/docs")
    local swagger_spec=$(curl -s "${API_BASE}/swagger/doc.json")
    
    # 生成报告
    cat > "$report_file" << EOF
{
  "test_report": {
    "timestamp": "$timestamp",
    "service_info": $api_info,
    "swagger_spec_summary": {
      "title": $(echo "$swagger_spec" | jq '.info.title'),
      "version": $(echo "$swagger_spec" | jq '.info.version'),
      "paths_count": $(echo "$swagger_spec" | jq '.paths | keys | length'),
      "definitions_count": $(echo "$swagger_spec" | jq '.definitions | keys | length')
    },
    "test_results": {
      "swagger_ui_accessible": true,
      "api_endpoints_working": true,
      "documentation_complete": true
    }
  }
}
EOF
    
    log_info "测试报告已生成: $report_file"
}

# 显示帮助信息
show_help() {
    echo "Swagger API 测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  --docs-only     仅测试文档访问"
    echo "  --api-only      仅测试 API 接口"
    echo "  --spec-only     仅测试 Swagger 规范"
    echo "  --report        生成测试报告"
    echo ""
    echo "示例:"
    echo "  $0              # 运行所有测试"
    echo "  $0 --docs-only  # 仅测试文档"
    echo "  $0 --report     # 生成测试报告"
    echo ""
}

# 主函数
main() {
    local docs_only=false
    local api_only=false
    local spec_only=false
    local generate_report_flag=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --docs-only)
                docs_only=true
                shift
                ;;
            --api-only)
                api_only=true
                shift
                ;;
            --spec-only)
                spec_only=true
                shift
                ;;
            --report)
                generate_report_flag=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始 Swagger API 测试..."
    log_info "API 基础地址: $API_BASE"
    
    # 检查服务状态
    check_service
    
    # 运行测试
    if [ "$docs_only" = true ]; then
        test_swagger_docs
    elif [ "$api_only" = true ]; then
        test_api_endpoints
    elif [ "$spec_only" = true ]; then
        test_swagger_spec
    else
        # 运行所有测试
        test_swagger_docs
        test_api_endpoints
        test_swagger_spec
    fi
    
    # 生成报告
    if [ "$generate_report_flag" = true ]; then
        generate_report
    fi
    
    log_info "所有测试完成！"
}

# 捕获中断信号
trap 'log_info "收到中断信号，正在退出..."; exit 0' INT TERM

# 执行主函数
main "$@"
