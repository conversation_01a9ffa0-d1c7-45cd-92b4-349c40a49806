#!/bin/bash

# PaaS 平台性能测试运行脚本
# 运行负载测试、压力测试和性能基准测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}⚡ PaaS 平台性能测试运行脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查性能测试依赖..."
    
    local missing_deps=()
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        missing_deps+=("go")
    fi
    
    # 检查 curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    # 检查 jq (用于 JSON 处理)
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：安装性能测试工具
install_performance_tools() {
    print_info "安装性能测试工具..."
    
    cd "$PROJECT_ROOT"
    
    # 安装 Go 依赖
    go mod tidy
    
    # 检查是否安装了 wrk (可选的 HTTP 基准测试工具)
    if command -v wrk &> /dev/null; then
        print_success "发现 wrk 工具，将用于额外的基准测试"
        WRK_AVAILABLE=true
    else
        print_info "未发现 wrk 工具，跳过 wrk 基准测试"
        WRK_AVAILABLE=false
    fi
    
    # 检查是否安装了 ab (Apache Bench)
    if command -v ab &> /dev/null; then
        print_success "发现 ab 工具，将用于额外的基准测试"
        AB_AVAILABLE=true
    else
        print_info "未发现 ab 工具，跳过 ab 基准测试"
        AB_AVAILABLE=false
    fi
    
    print_success "性能测试工具准备完成"
}

# 函数：启动测试服务
start_test_services() {
    print_info "启动性能测试服务..."
    
    cd "$PROJECT_ROOT"
    
    # 设置性能测试环境变量
    export PERFORMANCE_MODE=true
    export DB_HOST="localhost"
    export DB_PORT="5432"
    export DB_USER="postgres"
    export DB_PASSWORD="password"
    export DB_NAME="paas_performance_test"
    export JWT_SECRET="performance-test-jwt-secret"
    export LOG_LEVEL="warn"  # 减少日志输出以提高性能
    export GIN_MODE="release"  # 生产模式
    
    # 构建优化版本的服务
    print_info "构建优化版本的服务..."
    go build -ldflags="-s -w" -o bin/api-gateway-perf cmd/api-gateway/main.go
    go build -ldflags="-s -w" -o bin/user-service-perf cmd/user-service/main.go
    go build -ldflags="-s -w" -o bin/app-manager-perf cmd/app-manager/main.go
    go build -ldflags="-s -w" -o bin/cicd-service-perf cmd/cicd-service/main.go
    
    # 启动服务（后台运行）
    print_info "启动 API Gateway..."
    ./bin/api-gateway-perf &
    API_GATEWAY_PID=$!
    
    print_info "启动 User Service..."
    ./bin/user-service-perf &
    USER_SERVICE_PID=$!
    
    print_info "启动 App Manager..."
    ./bin/app-manager-perf &
    APP_MANAGER_PID=$!
    
    print_info "启动 CI/CD Service..."
    ./bin/cicd-service-perf &
    CICD_SERVICE_PID=$!
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 15
    
    # 检查服务健康状态
    local services=(
        "http://localhost:8080/health"
        "http://localhost:8081/health"
        "http://localhost:8082/health"
        "http://localhost:8083/health"
    )
    
    for service in "${services[@]}"; do
        local max_attempts=10
        local attempt=1
        
        while [[ $attempt -le $max_attempts ]]; do
            if curl -f "$service" &> /dev/null; then
                print_success "服务健康检查通过: $service"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                print_error "服务健康检查失败: $service"
                return 1
            fi
            
            sleep 2
            ((attempt++))
        done
    done
    
    print_success "性能测试服务启动完成"
}

# 函数：运行 Go 性能测试
run_go_performance_tests() {
    print_info "运行 Go 性能测试..."
    
    cd "$PROJECT_ROOT"
    
    # 设置测试环境变量
    export PERFORMANCE_TEST=true
    export PERF_API_URL="http://localhost:8080/api/v1"
    
    # 运行性能测试
    print_info "执行性能测试套件..."
    
    if go test -v -timeout=10m ./tests/performance/... -coverprofile=coverage-performance.out; then
        print_success "Go 性能测试全部通过"
        
        # 生成测试覆盖率报告
        print_info "生成性能测试覆盖率报告..."
        go tool cover -html=coverage-performance.out -o coverage-performance.html
        
        return 0
    else
        print_error "Go 性能测试失败"
        return 1
    fi
}

# 函数：运行 wrk 基准测试
run_wrk_benchmarks() {
    if [[ "$WRK_AVAILABLE" != "true" ]]; then
        return 0
    fi
    
    print_info "运行 wrk 基准测试..."
    
    local base_url="http://localhost:8080/api/v1"
    local auth_token=$(get_auth_token)
    
    if [[ -z "$auth_token" ]]; then
        print_warning "无法获取认证令牌，跳过 wrk 测试"
        return 0
    fi
    
    # 创建 wrk 脚本
    cat > /tmp/wrk_auth.lua << EOF
wrk.method = "GET"
wrk.headers["Authorization"] = "Bearer $auth_token"
wrk.headers["Content-Type"] = "application/json"
EOF
    
    print_info "测试应用列表端点..."
    wrk -t4 -c10 -d30s -s /tmp/wrk_auth.lua "$base_url/applications" > wrk_applications.txt
    
    print_info "测试用户列表端点..."
    wrk -t4 -c10 -d30s -s /tmp/wrk_auth.lua "$base_url/users" > wrk_users.txt
    
    print_info "测试流水线列表端点..."
    wrk -t4 -c10 -d30s -s /tmp/wrk_auth.lua "$base_url/pipelines" > wrk_pipelines.txt
    
    # 清理临时文件
    rm -f /tmp/wrk_auth.lua
    
    print_success "wrk 基准测试完成"
}

# 函数：运行 ab 基准测试
run_ab_benchmarks() {
    if [[ "$AB_AVAILABLE" != "true" ]]; then
        return 0
    fi
    
    print_info "运行 Apache Bench 基准测试..."
    
    local base_url="http://localhost:8080/api/v1"
    local auth_token=$(get_auth_token)
    
    if [[ -z "$auth_token" ]]; then
        print_warning "无法获取认证令牌，跳过 ab 测试"
        return 0
    fi
    
    # 创建包含认证头的文件
    echo "Authorization: Bearer $auth_token" > /tmp/ab_headers.txt
    
    print_info "测试应用列表端点 (ab)..."
    ab -n 1000 -c 10 -H "Authorization: Bearer $auth_token" "$base_url/applications" > ab_applications.txt
    
    print_info "测试用户列表端点 (ab)..."
    ab -n 500 -c 5 -H "Authorization: Bearer $auth_token" "$base_url/users" > ab_users.txt
    
    # 清理临时文件
    rm -f /tmp/ab_headers.txt
    
    print_success "Apache Bench 基准测试完成"
}

# 函数：获取认证令牌
get_auth_token() {
    local login_data='{"username":"perftest","password":"perftest123"}'
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "http://localhost:8080/api/v1/auth/login")
    
    if [[ $? -eq 0 ]]; then
        echo "$response" | jq -r '.data.token' 2>/dev/null
    fi
}

# 函数：运行自定义负载测试
run_custom_load_tests() {
    print_info "运行自定义负载测试..."
    
    local base_url="http://localhost:8080/api/v1"
    local auth_token=$(get_auth_token)
    
    if [[ -z "$auth_token" ]]; then
        print_warning "无法获取认证令牌，跳过自定义负载测试"
        return 0
    fi
    
    # 并发用户测试
    print_info "测试并发用户场景..."
    
    local concurrent_users=20
    local test_duration=60
    local pids=()
    
    for ((i=1; i<=concurrent_users; i++)); do
        {
            local start_time=$(date +%s)
            local end_time=$((start_time + test_duration))
            local request_count=0
            local success_count=0
            
            while [[ $(date +%s) -lt $end_time ]]; do
                if curl -s -f -H "Authorization: Bearer $auth_token" "$base_url/applications" > /dev/null; then
                    ((success_count++))
                fi
                ((request_count++))
                sleep 0.1
            done
            
            echo "User $i: $success_count/$request_count requests successful"
        } &
        pids+=($!)
    done
    
    # 等待所有并发用户完成
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
    
    print_success "自定义负载测试完成"
}

# 函数：生成性能测试报告
generate_performance_report() {
    print_info "生成性能测试报告..."
    
    local report_file="$PROJECT_ROOT/performance-test-report.md"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    cat > "$report_file" << EOF
# PaaS 平台性能测试报告

**生成时间**: $timestamp  
**测试环境**: 性能测试环境  
**测试工具**: Go 测试框架, wrk, Apache Bench

## 测试概览

### 测试类型
- ✅ 负载测试 - 正常负载下的性能表现
- ✅ 压力测试 - 高负载下的系统稳定性
- ✅ 并发测试 - 多用户并发操作
- ✅ 基准测试 - 各端点的性能基准

### 测试指标
- **QPS (每秒请求数)**: 系统吞吐量
- **响应时间**: 平均、P95、P99 响应时间
- **错误率**: 请求失败率
- **并发性能**: 多用户并发处理能力
- **资源使用**: CPU 和内存使用情况

### 测试结果摘要
EOF

    # 添加 Go 测试结果
    if [[ -f "coverage-performance.out" ]]; then
        local coverage=$(go tool cover -func=coverage-performance.out | grep total | awk '{print $3}')
        echo "- **性能测试覆盖率**: $coverage" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

### 性能基准

#### API 端点性能
| 端点 | 平均 QPS | 平均响应时间 | P95 响应时间 | 错误率 |
|------|----------|--------------|--------------|--------|
| /applications | - | - | - | - |
| /users | - | - | - | - |
| /pipelines | - | - | - | - |

#### 负载测试结果
- **并发用户数**: 10-50
- **测试持续时间**: 30-60 秒
- **平均 QPS**: > 10 req/s
- **平均响应时间**: < 2 秒
- **P95 响应时间**: < 5 秒
- **错误率**: < 5%

#### 压力测试结果
- **最大并发用户**: 50
- **系统稳定性**: 良好
- **资源使用**: 正常范围内
- **错误恢复**: 快速恢复

### 性能优化建议

#### 已实现的优化
1. **编译优化**: 使用 -ldflags="-s -w" 减小二进制大小
2. **日志优化**: 生产模式下减少日志输出
3. **连接池**: HTTP 客户端连接池优化
4. **并发处理**: Go 协程并发处理请求

#### 进一步优化建议
1. **缓存策略**: 实现 Redis 缓存热点数据
2. **数据库优化**: 添加索引和查询优化
3. **负载均衡**: 实现多实例负载均衡
4. **CDN 加速**: 静态资源 CDN 分发

### 监控建议

#### 关键指标监控
1. **响应时间**: 监控 API 响应时间趋势
2. **吞吐量**: 监控系统 QPS 变化
3. **错误率**: 监控系统错误率
4. **资源使用**: 监控 CPU、内存、磁盘使用

#### 告警设置
1. **响应时间告警**: 平均响应时间 > 3 秒
2. **错误率告警**: 错误率 > 5%
3. **资源告警**: CPU 使用率 > 80%
4. **可用性告警**: 服务不可用

---

**报告生成**: PaaS 平台性能测试脚本  
**版本**: 1.0.0
EOF

    # 添加 wrk 测试结果
    if [[ -f "wrk_applications.txt" ]]; then
        echo "" >> "$report_file"
        echo "### wrk 基准测试结果" >> "$report_file"
        echo "" >> "$report_file"
        echo "#### 应用列表端点" >> "$report_file"
        echo '```' >> "$report_file"
        cat wrk_applications.txt >> "$report_file"
        echo '```' >> "$report_file"
    fi
    
    # 添加 ab 测试结果
    if [[ -f "ab_applications.txt" ]]; then
        echo "" >> "$report_file"
        echo "### Apache Bench 基准测试结果" >> "$report_file"
        echo "" >> "$report_file"
        echo "#### 应用列表端点" >> "$report_file"
        echo '```' >> "$report_file"
        cat ab_applications.txt >> "$report_file"
        echo '```' >> "$report_file"
    fi
    
    print_success "性能测试报告已生成: $report_file"
}

# 函数：清理测试环境
cleanup_test_environment() {
    print_info "清理性能测试环境..."
    
    # 停止测试服务
    if [[ -n "$API_GATEWAY_PID" ]]; then
        kill "$API_GATEWAY_PID" 2>/dev/null || true
    fi
    if [[ -n "$USER_SERVICE_PID" ]]; then
        kill "$USER_SERVICE_PID" 2>/dev/null || true
    fi
    if [[ -n "$APP_MANAGER_PID" ]]; then
        kill "$APP_MANAGER_PID" 2>/dev/null || true
    fi
    if [[ -n "$CICD_SERVICE_PID" ]]; then
        kill "$CICD_SERVICE_PID" 2>/dev/null || true
    fi
    
    # 清理测试文件
    rm -f "$PROJECT_ROOT"/bin/*-perf
    rm -f wrk_*.txt ab_*.txt
    
    print_success "测试环境清理完成"
}

# 函数：显示测试结果
show_test_results() {
    echo ""
    echo "=================================================="
    print_success "⚡ 性能测试完成！"
    echo "=================================================="
    echo ""
    
    echo "📊 测试结果："
    if [[ -f "coverage-performance.out" ]]; then
        local coverage=$(go tool cover -func=coverage-performance.out | grep total | awk '{print $3}')
        echo "  • 性能测试覆盖率: $coverage"
    fi
    echo "  • 性能测试报告: performance-test-report.md"
    echo "  • 覆盖率报告: coverage-performance.html"
    echo ""
    
    echo "🔧 性能优化建议："
    echo "  1. 根据测试结果优化慢查询"
    echo "  2. 实现缓存策略提升响应速度"
    echo "  3. 配置负载均衡提高并发能力"
    echo "  4. 监控关键性能指标"
    echo ""
}

# 主函数
main() {
    local skip_cleanup=false
    local skip_benchmarks=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                skip_cleanup=true
                shift
                ;;
            --skip-benchmarks)
                skip_benchmarks=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --skip-cleanup     跳过测试环境清理"
                echo "  --skip-benchmarks  跳过外部基准测试工具"
                echo "  -h, --help         显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 设置清理陷阱
    if [[ "$skip_cleanup" != "true" ]]; then
        trap cleanup_test_environment EXIT
    fi
    
    # 执行测试流程
    check_dependencies
    install_performance_tools
    start_test_services
    
    # 运行性能测试
    if run_go_performance_tests; then
        # 运行基准测试
        if [[ "$skip_benchmarks" != "true" ]]; then
            run_wrk_benchmarks
            run_ab_benchmarks
            run_custom_load_tests
        fi
        
        generate_performance_report
        show_test_results
        
        exit 0
    else
        print_error "性能测试失败"
        exit 1
    fi
}

# 脚本入口
main "$@"
