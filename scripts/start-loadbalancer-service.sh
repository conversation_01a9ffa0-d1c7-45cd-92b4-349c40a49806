#!/bin/bash

# PaaS 平台负载均衡和服务发现服务启动脚本
# 启动完整的负载均衡、服务注册发现和健康检查系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/configs"
LOG_DIR="$PROJECT_ROOT/logs"

echo -e "${PURPLE}⚖️  PaaS 平台负载均衡服务启动脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查负载均衡服务依赖..."
    
    local missing_deps=()
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        missing_deps+=("go")
    fi
    
    # 检查 curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：创建必要目录
create_directories() {
    print_info "创建必要目录..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/bin"
    mkdir -p "$PROJECT_ROOT/data/registry"
    
    print_success "目录创建完成"
}

# 函数：生成负载均衡配置文件
generate_loadbalancer_config() {
    print_info "生成负载均衡配置文件..."
    
    local config_file="$CONFIG_DIR/loadbalancer.yaml"
    
    cat > "$config_file" << EOF
# PaaS 平台负载均衡服务配置

# 服务配置
server:
  port: 8086
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file: "$LOG_DIR/loadbalancer.log"

# 服务注册中心配置
registry:
  # 存储类型: memory, file, etcd, consul
  storage_type: "memory"
  
  # 内存存储配置
  memory:
    cleanup_interval: "5m"
    stale_threshold: "10m"
    
  # 文件存储配置
  file:
    data_dir: "$PROJECT_ROOT/data/registry"
    backup_interval: "1h"
    
  # 服务发现配置
  discovery:
    enabled: true
    broadcast_interval: "30s"
    
# 负载均衡配置
loadbalancer:
  # 默认算法: round_robin, weighted_round_robin, least_connections, ip_hash, random
  default_algorithm: "round_robin"
  
  # 代理配置
  proxy:
    timeout: "30s"
    keep_alive: "60s"
    max_idle_conns: 100
    max_idle_conns_per_host: 10
    
  # 重试配置
  retry:
    max_retries: 3
    retry_timeout: "1s"
    backoff_factor: 2
    
  # 会话保持
  session:
    sticky_enabled: false
    cookie_name: "PAAS_SESSION"
    cookie_ttl: "1h"
    
  # 熔断器配置
  circuit_breaker:
    enabled: false
    failure_threshold: 5
    recovery_timeout: "30s"
    half_open_max_calls: 3

# 健康检查配置
health_check:
  # 全局配置
  global:
    enabled: true
    default_interval: "30s"
    default_timeout: "5s"
    default_path: "/health"
    
  # 检查器配置
  checker:
    worker_count: 10
    queue_size: 1000
    batch_size: 50
    
  # 失败处理
  failure_handling:
    max_failures: 3
    failure_window: "5m"
    recovery_check_interval: "10s"

# 监控配置
monitoring:
  metrics_enabled: true
  metrics_port: 9091
  stats_interval: "10s"
  
  # 统计信息保留
  stats_retention:
    max_entries: 10000
    cleanup_interval: "1h"

# 安全配置
security:
  api_key: "\${LB_API_KEY:}"
  jwt_secret: "\${JWT_SECRET:}"
  
  # IP 白名单
  ip_whitelist:
    enabled: false
    allowed_ips:
      - "127.0.0.1"
      - "::1"
      
# 限流配置
rate_limiting:
  enabled: true
  requests_per_minute: 10000
  burst_size: 1000
  
  # 按服务限流
  per_service:
    enabled: true
    default_limit: 1000
    
# 缓存配置
cache:
  enabled: true
  ttl: "5m"
  max_size: 10000
  
# 日志记录
access_log:
  enabled: true
  format: "json"
  file: "$LOG_DIR/access.log"
  rotation:
    max_size: "100MB"
    max_files: 10
    max_age: "7d"
EOF

    print_success "负载均衡配置文件已生成: $config_file"
}

# 函数：构建负载均衡服务
build_loadbalancer_service() {
    print_info "构建负载均衡服务..."
    
    cd "$PROJECT_ROOT"
    
    # 构建负载均衡服务
    go build -o bin/loadbalancer-service cmd/loadbalancer-service/main.go
    
    if [[ ! -f "bin/loadbalancer-service" ]]; then
        print_error "负载均衡服务构建失败"
        exit 1
    fi
    
    print_success "负载均衡服务构建完成"
}

# 函数：启动负载均衡服务
start_loadbalancer_service() {
    print_info "启动负载均衡服务..."
    
    cd "$PROJECT_ROOT"
    
    # 设置环境变量
    export CONFIG_FILE="$CONFIG_DIR/loadbalancer.yaml"
    export LOG_LEVEL="info"
    export GIN_MODE="release"
    
    # 启动服务
    ./bin/loadbalancer-service &
    LOADBALANCER_PID=$!
    
    # 等待服务启动
    print_info "等待负载均衡服务启动..."
    sleep 10
    
    # 检查服务健康状态
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:8086/health" &> /dev/null; then
            print_success "负载均衡服务启动成功"
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            print_error "负载均衡服务启动失败"
            return 1
        fi
        
        print_info "等待负载均衡服务启动... ($attempt/$max_attempts)"
        sleep 3
        ((attempt++))
    done
}

# 函数：测试负载均衡功能
test_loadbalancer_features() {
    print_info "测试负载均衡功能..."
    
    local base_url="http://localhost:8086/api/v1/loadbalancer"
    
    # 测试获取服务列表
    print_info "测试获取服务列表..."
    if curl -s -f "$base_url/services" > /dev/null; then
        print_success "服务列表接口正常"
    else
        print_warning "服务列表接口异常"
    fi
    
    # 测试注册服务
    print_info "测试注册服务..."
    local service_data='{
        "service_name": "test-service",
        "instance_id": "test-instance-1",
        "host": "127.0.0.1",
        "port": 8080,
        "protocol": "http",
        "weight": 1,
        "health_check_url": "http://127.0.0.1:8080/health",
        "health_check_interval": "30s",
        "health_check_timeout": "5s"
    }'
    
    if curl -s -f -X POST \
        -H "Content-Type: application/json" \
        -d "$service_data" \
        "$base_url/services/register" > /dev/null; then
        print_success "服务注册接口正常"
    else
        print_warning "服务注册接口异常"
    fi
    
    # 测试获取负载均衡统计
    print_info "测试获取负载均衡统计..."
    if curl -s -f "$base_url/stats" > /dev/null; then
        print_success "负载均衡统计接口正常"
    else
        print_warning "负载均衡统计接口异常"
    fi
    
    # 测试获取健康检查统计
    print_info "测试获取健康检查统计..."
    if curl -s -f "$base_url/health/services" > /dev/null; then
        print_success "健康检查统计接口正常"
    else
        print_warning "健康检查统计接口异常"
    fi
    
    print_success "负载均衡功能测试完成"
}

# 函数：注册示例服务
register_example_services() {
    print_info "注册示例服务..."
    
    local base_url="http://localhost:8086/api/v1/loadbalancer"
    
    # 注册多个示例服务实例
    local services=(
        '{"service_name":"api-gateway","instance_id":"api-gateway-1","host":"127.0.0.1","port":8080,"weight":1}'
        '{"service_name":"api-gateway","instance_id":"api-gateway-2","host":"127.0.0.1","port":8080,"weight":2}'
        '{"service_name":"user-service","instance_id":"user-service-1","host":"127.0.0.1","port":8081,"weight":1}'
        '{"service_name":"app-manager","instance_id":"app-manager-1","host":"127.0.0.1","port":8082,"weight":1}'
    )
    
    for service_data in "${services[@]}"; do
        curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "$service_data" \
            "$base_url/services/register" > /dev/null || true
    done
    
    print_success "示例服务注册完成"
}

# 函数：显示服务信息
show_service_info() {
    echo ""
    echo "=================================================="
    print_success "⚖️  负载均衡服务启动完成！"
    echo "=================================================="
    echo ""
    
    echo "🔗 服务地址："
    echo "  • 负载均衡服务: http://localhost:8086"
    echo "  • 健康检查: http://localhost:8086/health"
    echo "  • 监控指标: http://localhost:9091/metrics"
    echo "  • 管理面板: http://localhost:8086/dashboard"
    echo ""
    
    echo "📋 API 端点："
    echo "  • 注册服务: POST /api/v1/loadbalancer/services/register"
    echo "  • 注销服务: DELETE /api/v1/loadbalancer/services/{service}/{instance}"
    echo "  • 服务列表: GET /api/v1/loadbalancer/services"
    echo "  • 服务详情: GET /api/v1/loadbalancer/services/{service}"
    echo "  • 健康检查: GET /api/v1/loadbalancer/health/services"
    echo "  • 负载均衡统计: GET /api/v1/loadbalancer/stats"
    echo "  • 代理请求: ANY /api/v1/loadbalancer/proxy/{service}/*"
    echo ""
    
    echo "🔧 负载均衡算法："
    echo "  • round_robin - 轮询算法"
    echo "  • weighted_round_robin - 加权轮询算法"
    echo "  • least_connections - 最少连接算法"
    echo "  • ip_hash - IP 哈希算法"
    echo "  • random - 随机算法"
    echo ""
    
    echo "📁 重要文件："
    echo "  • 配置文件: $CONFIG_DIR/loadbalancer.yaml"
    echo "  • 日志文件: $LOG_DIR/loadbalancer.log"
    echo "  • 访问日志: $LOG_DIR/access.log"
    echo "  • 服务进程: PID $LOADBALANCER_PID"
    echo ""
    
    echo "🔧 管理命令："
    echo "  • 查看日志: tail -f $LOG_DIR/loadbalancer.log"
    echo "  • 查看访问日志: tail -f $LOG_DIR/access.log"
    echo "  • 停止服务: kill $LOADBALANCER_PID"
    echo "  • 重启服务: $0 --restart"
    echo ""
    
    echo "📖 使用示例："
    echo "  # 注册服务"
    echo "  curl -X POST http://localhost:8086/api/v1/loadbalancer/services/register \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"service_name\":\"my-service\",\"instance_id\":\"instance-1\",\"host\":\"127.0.0.1\",\"port\":8080}'"
    echo ""
    echo "  # 代理请求"
    echo "  curl http://localhost:8086/api/v1/loadbalancer/proxy/my-service/api/users"
    echo ""
}

# 函数：清理服务
cleanup_service() {
    print_info "清理负载均衡服务..."
    
    if [[ -n "$LOADBALANCER_PID" ]]; then
        kill "$LOADBALANCER_PID" 2>/dev/null || true
        print_success "负载均衡服务已停止"
    fi
}

# 主函数
main() {
    local restart=false
    local skip_examples=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --restart)
                restart=true
                shift
                ;;
            --skip-examples)
                skip_examples=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --restart        重启服务"
                echo "  --skip-examples  跳过示例服务注册"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 设置清理陷阱
    trap cleanup_service EXIT
    
    # 如果是重启，先停止现有服务
    if [[ "$restart" == "true" ]]; then
        print_info "重启负载均衡服务..."
        pkill -f "loadbalancer-service" || true
        sleep 3
    fi
    
    # 执行启动流程
    check_dependencies
    create_directories
    generate_loadbalancer_config
    build_loadbalancer_service
    
    if start_loadbalancer_service; then
        test_loadbalancer_features
        
        if [[ "$skip_examples" != "true" ]]; then
            register_example_services
        fi
        
        show_service_info
        
        # 保持服务运行
        print_info "负载均衡服务正在运行，按 Ctrl+C 停止..."
        wait "$LOADBALANCER_PID"
    else
        print_error "负载均衡服务启动失败"
        exit 1
    fi
}

# 脚本入口
main "$@"
