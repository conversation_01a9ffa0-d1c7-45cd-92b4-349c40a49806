#!/bin/bash

# PaaS 平台监控系统启动脚本
# 启动完整的 APM 监控栈：Prometheus, Grafana, AlertManager, <PERSON><PERSON><PERSON>

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.monitoring.yml"
CONFIG_DIR="$PROJECT_ROOT/configs"

echo -e "${BLUE}🚀 PaaS 平台监控系统启动脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 服务状态
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    print_success "系统依赖检查通过"
}

# 函数：检查配置文件
check_config_files() {
    print_info "检查配置文件..."
    
    local required_configs=(
        "prometheus.yml"
        "alert-rules.yml"
        "alertmanager.yml"
        "apm-config.yaml"
    )
    
    local missing_configs=()
    
    for config in "${required_configs[@]}"; do
        if [[ ! -f "$CONFIG_DIR/$config" ]]; then
            missing_configs+=("$config")
        fi
    done
    
    if [[ ${#missing_configs[@]} -gt 0 ]]; then
        print_error "缺少配置文件: ${missing_configs[*]}"
        print_info "请确保以下配置文件存在于 $CONFIG_DIR 目录："
        for config in "${missing_configs[@]}"; do
            echo "  - $config"
        done
        exit 1
    fi
    
    print_success "配置文件检查通过"
}

# 函数：创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    
    local directories=(
        "$PROJECT_ROOT/data/prometheus"
        "$PROJECT_ROOT/data/grafana"
        "$PROJECT_ROOT/data/alertmanager"
        "$PROJECT_ROOT/logs/monitoring"
        "$CONFIG_DIR/grafana/provisioning/datasources"
        "$CONFIG_DIR/grafana/provisioning/dashboards"
        "$CONFIG_DIR/grafana/dashboards"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
    
    # 设置权限
    chmod -R 755 "$PROJECT_ROOT/data"
    chmod -R 755 "$PROJECT_ROOT/logs"
    
    print_success "目录创建完成"
}

# 函数：生成 Grafana 数据源配置
generate_grafana_datasources() {
    print_info "生成 Grafana 数据源配置..."
    
    cat > "$CONFIG_DIR/grafana/provisioning/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
    
  - name: AlertManager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    editable: true
    jsonData:
      implementation: "prometheus"
EOF
    
    print_success "Grafana 数据源配置生成完成"
}

# 函数：生成 Grafana 仪表板配置
generate_grafana_dashboards() {
    print_info "生成 Grafana 仪表板配置..."
    
    cat > "$CONFIG_DIR/grafana/provisioning/dashboards/paas.yml" << EOF
apiVersion: 1

providers:
  - name: 'PaaS Platform'
    orgId: 1
    folder: 'PaaS Platform'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF
    
    print_success "Grafana 仪表板配置生成完成"
}

# 函数：检查端口占用
check_ports() {
    print_info "检查端口占用情况..."
    
    local ports=(
        "9090:Prometheus"
        "3001:Grafana"
        "9093:AlertManager"
        "16686:Jaeger"
        "9100:Node Exporter"
        "8080:cAdvisor"
    )
    
    local occupied_ports=()
    
    for port_info in "${ports[@]}"; do
        local port="${port_info%%:*}"
        local service="${port_info##*:}"
        
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            occupied_ports+=("$port ($service)")
        fi
    done
    
    if [[ ${#occupied_ports[@]} -gt 0 ]]; then
        print_warning "以下端口已被占用: ${occupied_ports[*]}"
        print_info "如果这些端口被其他服务占用，监控服务可能无法正常启动"
        
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "启动已取消"
            exit 0
        fi
    else
        print_success "端口检查通过"
    fi
}

# 函数：启动监控服务
start_monitoring_services() {
    print_info "启动监控服务..."
    
    # 拉取最新镜像
    print_info "拉取 Docker 镜像..."
    docker-compose -f "$COMPOSE_FILE" pull
    
    # 启动服务
    print_info "启动监控栈..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    print_success "监控服务启动完成"
}

# 函数：等待服务就绪
wait_for_services() {
    print_info "等待服务就绪..."
    
    local services=(
        "http://localhost:9090/-/ready:Prometheus"
        "http://localhost:3001/api/health:Grafana"
        "http://localhost:9093/-/ready:AlertManager"
        "http://localhost:16686/:Jaeger"
    )
    
    local max_attempts=30
    local attempt=0
    
    for service_info in "${services[@]}"; do
        local url="${service_info%%:*}"
        local service="${service_info##*:}"
        
        print_info "等待 $service 服务就绪..."
        
        attempt=0
        while [[ $attempt -lt $max_attempts ]]; do
            if curl -s -f "$url" > /dev/null 2>&1; then
                print_success "$service 服务已就绪"
                break
            fi
            
            attempt=$((attempt + 1))
            if [[ $attempt -eq $max_attempts ]]; then
                print_warning "$service 服务启动超时，但继续执行"
                break
            fi
            
            sleep 2
        done
    done
}

# 函数：显示访问信息
show_access_info() {
    echo ""
    echo "=================================================="
    print_success "🎉 PaaS 平台监控系统启动完成！"
    echo "=================================================="
    echo ""
    echo "📊 监控服务访问地址："
    echo "  • Prometheus:   http://localhost:9090"
    echo "  • Grafana:      http://localhost:3001 (admin/admin123)"
    echo "  • AlertManager: http://localhost:9093"
    echo "  • Jaeger:       http://localhost:16686"
    echo ""
    echo "📈 系统指标："
    echo "  • Node Exporter:    http://localhost:9100/metrics"
    echo "  • cAdvisor:         http://localhost:8080"
    echo "  • PostgreSQL:       http://localhost:9187/metrics"
    echo "  • Redis:            http://localhost:9121/metrics"
    echo ""
    echo "🔧 管理命令："
    echo "  • 查看状态: docker-compose -f $COMPOSE_FILE ps"
    echo "  • 查看日志: docker-compose -f $COMPOSE_FILE logs -f [service]"
    echo "  • 停止服务: docker-compose -f $COMPOSE_FILE down"
    echo "  • 重启服务: docker-compose -f $COMPOSE_FILE restart [service]"
    echo ""
    echo "📝 下一步："
    echo "  1. 访问 Grafana 配置仪表板"
    echo "  2. 检查 Prometheus 目标状态"
    echo "  3. 验证告警规则配置"
    echo "  4. 测试告警通知功能"
    echo ""
}

# 函数：清理函数
cleanup() {
    if [[ $? -ne 0 ]]; then
        print_error "启动过程中出现错误"
        print_info "正在清理..."
        docker-compose -f "$COMPOSE_FILE" down 2>/dev/null || true
    fi
}

# 主函数
main() {
    # 设置错误处理
    trap cleanup EXIT
    
    # 检查参数
    case "${1:-}" in
        "stop")
            print_info "停止监控服务..."
            docker-compose -f "$COMPOSE_FILE" down
            print_success "监控服务已停止"
            exit 0
            ;;
        "restart")
            print_info "重启监控服务..."
            docker-compose -f "$COMPOSE_FILE" down
            sleep 2
            ;;
        "status")
            print_info "监控服务状态："
            docker-compose -f "$COMPOSE_FILE" ps
            exit 0
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f "${2:-}"
            exit 0
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令："
            echo "  start     启动监控服务 (默认)"
            echo "  stop      停止监控服务"
            echo "  restart   重启监控服务"
            echo "  status    查看服务状态"
            echo "  logs      查看服务日志"
            echo "  help      显示帮助信息"
            exit 0
            ;;
    esac
    
    # 执行启动流程
    check_dependencies
    check_config_files
    create_directories
    generate_grafana_datasources
    generate_grafana_dashboards
    check_ports
    start_monitoring_services
    wait_for_services
    show_access_info
    
    # 移除错误处理
    trap - EXIT
}

# 脚本入口
main "$@"
