#!/bin/bash

# PaaS平台认证架构迁移脚本
# 将认证功能从app-manager迁移到独立的user-service

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示迁移说明
show_migration_info() {
    echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                  认证架构迁移工具                          ║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║  🎯 目标: 将认证功能从app-manager迁移到user-service       ║${NC}"
    echo -e "${CYAN}║  🏗️  架构: 前端 -> API Gateway -> User Service            ║${NC}"
    echo -e "${CYAN}║  🔄 策略: 渐进式迁移，确保系统稳定性                      ║${NC}"
    echo -e "${CYAN}║  ⚠️  注意: 迁移过程中会重启相关服务                       ║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 检查当前服务状态
check_current_services() {
    log_step "检查当前服务状态..."
    
    local services=(
        "app-manager:8081"
        "script-service:8084"
        "cicd-service:8082"
        "config-service:8083"
    )
    
    local running_services=0
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service_name port <<< "$service_info"
        
        if curl -s http://localhost:$port/health > /dev/null 2>&1; then
            log_info "✅ ${service_name} (${port}) 运行正常"
            running_services=$((running_services + 1))
        else
            log_warn "❌ ${service_name} (${port}) 未运行"
        fi
    done
    
    if [ $running_services -eq 0 ]; then
        log_error "没有服务在运行，请先启动基础服务"
        log_info "运行: ./scripts/start-dev-mode.sh"
        exit 1
    fi
    
    echo
}

# 构建新服务
build_new_services() {
    log_step "构建新的认证架构服务..."
    
    # 构建User Service
    log_info "构建User Service..."
    if go build -o bin/user-service ./cmd/user-service; then
        log_info "✅ User Service构建成功"
    else
        log_error "❌ User Service构建失败"
        exit 1
    fi
    
    # 构建API Gateway
    log_info "构建API Gateway..."
    if go build -o bin/api-gateway ./cmd/api-gateway; then
        log_info "✅ API Gateway构建成功"
    else
        log_error "❌ API Gateway构建失败"
        exit 1
    fi
    
    echo
}

# 启动User Service
start_user_service() {
    log_step "启动User Service..."
    
    # 检查端口是否被占用
    if lsof -Pi :8085 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口8085已被占用，尝试终止占用进程..."
        lsof -ti:8085 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 创建必要目录
    mkdir -p data logs
    
    # 启动User Service
    log_info "启动User Service (端口8085)..."
    nohup ./bin/user-service --config=configs/user-service.dev.yaml > logs/user-service.log 2>&1 &
    local user_service_pid=$!
    echo $user_service_pid > data/user-service.pid
    
    # 等待服务启动
    sleep 3
    
    # 验证服务状态
    if curl -s http://localhost:8085/health > /dev/null 2>&1; then
        log_info "✅ User Service启动成功 (PID: $user_service_pid)"
    else
        log_error "❌ User Service启动失败"
        cat logs/user-service.log
        exit 1
    fi
    
    echo
}

# 启动API Gateway
start_api_gateway() {
    log_step "启动API Gateway..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warn "端口8080已被占用，尝试终止占用进程..."
        lsof -ti:8080 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 启动API Gateway
    log_info "启动API Gateway (端口8080)..."
    nohup ./bin/api-gateway --config=configs/api-gateway.dev.yaml > logs/api-gateway.log 2>&1 &
    local gateway_pid=$!
    echo $gateway_pid > data/api-gateway.pid
    
    # 等待服务启动
    sleep 3
    
    # 验证服务状态
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        log_info "✅ API Gateway启动成功 (PID: $gateway_pid)"
    else
        log_error "❌ API Gateway启动失败"
        cat logs/api-gateway.log
        exit 1
    fi
    
    echo
}

# 测试新架构
test_new_architecture() {
    log_step "测试新的认证架构..."
    
    # 1. 测试API Gateway健康检查
    log_info "1. 测试API Gateway健康检查..."
    if curl -s http://localhost:8080/health | grep -q "healthy"; then
        log_info "✅ API Gateway健康检查通过"
    else
        log_error "❌ API Gateway健康检查失败"
        return 1
    fi
    
    # 2. 测试User Service健康检查
    log_info "2. 测试User Service健康检查..."
    if curl -s http://localhost:8085/health | grep -q "healthy"; then
        log_info "✅ User Service健康检查通过"
    else
        log_error "❌ User Service健康检查失败"
        return 1
    fi
    
    # 3. 测试通过API Gateway的认证请求
    log_info "3. 测试通过API Gateway的认证请求..."
    local auth_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d '{"username":"test","password":"test"}' \
        http://localhost:8080/api/v1/auth/login 2>/dev/null || echo "HTTP_CODE:000")
    
    local auth_code=$(echo "$auth_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$auth_code" = "200" ] || [ "$auth_code" = "401" ]; then
        log_info "✅ API Gateway认证路由工作正常 (HTTP $auth_code)"
    else
        log_error "❌ API Gateway认证路由失败 (HTTP $auth_code)"
        return 1
    fi
    
    # 4. 测试直接访问User Service
    log_info "4. 测试直接访问User Service..."
    local direct_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
        -H "Content-Type: application/json" \
        -d '{"username":"test","password":"test"}' \
        http://localhost:8085/api/v1/auth/login 2>/dev/null || echo "HTTP_CODE:000")
    
    local direct_code=$(echo "$direct_response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$direct_code" = "200" ] || [ "$direct_code" = "401" ]; then
        log_info "✅ User Service直接访问正常 (HTTP $direct_code)"
    else
        log_error "❌ User Service直接访问失败 (HTTP $direct_code)"
        return 1
    fi
    
    echo
    return 0
}

# 更新前端配置
update_frontend_config() {
    log_step "更新前端配置..."
    
    # 备份原配置
    if [ -f "web/vite.config.ts" ]; then
        cp web/vite.config.ts web/vite.config.ts.backup
        log_info "已备份原配置文件"
    fi
    
    # 更新代理配置指向API Gateway
    log_info "更新前端代理配置指向API Gateway (8080)..."
    
    # 这里需要手动更新，因为配置文件结构复杂
    log_warn "⚠️  请手动更新 web/vite.config.ts 中的代理配置："
    echo -e "${YELLOW}将 target: 'http://localhost:8081' 改为 target: 'http://localhost:8080'${NC}"
    
    echo
}

# 显示迁移结果
show_migration_result() {
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                      迁移完成                              ║${NC}"
    echo -e "${BLUE}╠════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BLUE}║  🎉 新的认证架构已启动                                    ║${NC}"
    echo -e "${BLUE}║                                                            ║${NC}"
    echo -e "${BLUE}║  🌐 API Gateway:    http://localhost:8080                 ║${NC}"
    echo -e "${BLUE}║  🔐 User Service:   http://localhost:8085                 ║${NC}"
    echo -e "${BLUE}║  📱 App Manager:    http://localhost:8081                 ║${NC}"
    echo -e "${BLUE}║                                                            ║${NC}"
    echo -e "${BLUE}║  📋 下一步操作:                                           ║${NC}"
    echo -e "${BLUE}║  1. 更新前端代理配置 (web/vite.config.ts)                 ║${NC}"
    echo -e "${BLUE}║  2. 启动前端开发服务器                                    ║${NC}"
    echo -e "${BLUE}║  3. 测试登录功能                                          ║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    log_info "服务状态:"
    echo "  - API Gateway PID: $(cat data/api-gateway.pid 2>/dev/null || echo '未知')"
    echo "  - User Service PID: $(cat data/user-service.pid 2>/dev/null || echo '未知')"
    echo
    
    log_info "日志文件:"
    echo "  - API Gateway: logs/api-gateway.log"
    echo "  - User Service: logs/user-service.log"
    echo
    
    log_info "测试命令:"
    echo "  - 健康检查: curl http://localhost:8080/health"
    echo "  - 认证测试: curl -X POST -H 'Content-Type: application/json' -d '{\"username\":\"test\",\"password\":\"test\"}' http://localhost:8080/api/v1/auth/login"
    echo
}

# 回滚功能
rollback() {
    log_warn "执行回滚操作..."
    
    # 停止新服务
    if [ -f "data/api-gateway.pid" ]; then
        local gateway_pid=$(cat data/api-gateway.pid)
        kill $gateway_pid 2>/dev/null || true
        rm -f data/api-gateway.pid
        log_info "已停止API Gateway"
    fi
    
    if [ -f "data/user-service.pid" ]; then
        local user_service_pid=$(cat data/user-service.pid)
        kill $user_service_pid 2>/dev/null || true
        rm -f data/user-service.pid
        log_info "已停止User Service"
    fi
    
    # 恢复前端配置
    if [ -f "web/vite.config.ts.backup" ]; then
        mv web/vite.config.ts.backup web/vite.config.ts
        log_info "已恢复前端配置"
    fi
    
    log_info "回滚完成，系统恢复到迁移前状态"
}

# 主函数
main() {
    local mode=${1:-"migrate"}
    
    case $mode in
        "migrate")
            echo -e "${GREEN}🚀 开始认证架构迁移${NC}"
            echo
            
            # 显示迁移说明
            show_migration_info
            
            # 检查当前服务状态
            check_current_services
            
            # 构建新服务
            build_new_services
            
            # 启动User Service
            start_user_service
            
            # 启动API Gateway
            start_api_gateway
            
            # 测试新架构
            if test_new_architecture; then
                log_info "🎉 新架构测试通过！"
                
                # 更新前端配置
                update_frontend_config
                
                # 显示迁移结果
                show_migration_result
                
                log_info "✅ 认证架构迁移完成！"
                log_info "现在可以启动前端测试新的认证流程"
            else
                log_error "❌ 新架构测试失败，执行回滚..."
                rollback
                exit 1
            fi
            ;;
        "rollback")
            rollback
            ;;
        "test")
            log_info "🧪 测试新认证架构..."
            test_new_architecture
            ;;
        "status")
            log_info "📊 检查服务状态..."
            check_current_services
            
            # 检查新服务
            echo "新架构服务状态:"
            if curl -s http://localhost:8080/health > /dev/null 2>&1; then
                echo "  ✅ API Gateway (8080) 运行正常"
            else
                echo "  ❌ API Gateway (8080) 未运行"
            fi
            
            if curl -s http://localhost:8085/health > /dev/null 2>&1; then
                echo "  ✅ User Service (8085) 运行正常"
            else
                echo "  ❌ User Service (8085) 未运行"
            fi
            ;;
        *)
            echo "用法: $0 [migrate|rollback|test|status]"
            echo
            echo "命令说明:"
            echo "  migrate  - 执行认证架构迁移"
            echo "  rollback - 回滚到原架构"
            echo "  test     - 测试新架构"
            echo "  status   - 检查服务状态"
            exit 1
            ;;
    esac
}

# 捕获中断信号，执行回滚
trap 'log_warn "收到中断信号，执行回滚..."; rollback; exit 1' INT TERM

# 执行主函数
main "$@"
