#!/bin/bash

# PaaS 平台自动化部署脚本
# 支持多环境、多策略的自动化部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOY_CONFIG_DIR="$PROJECT_ROOT/deployments/configs"
BACKUP_DIR="$PROJECT_ROOT/backups"

# 默认配置
DEFAULT_ENVIRONMENT="staging"
DEFAULT_STRATEGY="rolling"
DEFAULT_TIMEOUT="600"

echo -e "${PURPLE}🚀 PaaS 平台自动化部署脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查部署依赖..."
    
    local missing_deps=()
    
    # 检查必要工具
    local required_tools=("git" "docker" "kubectl" "helm" "jq" "curl")
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_deps+=("$tool")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：加载部署配置
load_deploy_config() {
    local environment=$1
    local config_file="$DEPLOY_CONFIG_DIR/$environment.yaml"
    
    if [[ ! -f "$config_file" ]]; then
        print_error "部署配置文件不存在: $config_file"
        exit 1
    fi
    
    print_info "加载部署配置: $environment"
    
    # 解析配置文件（简化版，实际项目中可能需要更复杂的解析）
    export DEPLOY_NAMESPACE=$(yq eval '.namespace' "$config_file" 2>/dev/null || echo "paas-$environment")
    export DEPLOY_REPLICAS=$(yq eval '.replicas' "$config_file" 2>/dev/null || echo "3")
    export DEPLOY_RESOURCES_CPU=$(yq eval '.resources.cpu' "$config_file" 2>/dev/null || echo "500m")
    export DEPLOY_RESOURCES_MEMORY=$(yq eval '.resources.memory' "$config_file" 2>/dev/null || echo "512Mi")
    export DEPLOY_IMAGE_TAG=$(yq eval '.image.tag' "$config_file" 2>/dev/null || echo "latest")
    export DEPLOY_REGISTRY=$(yq eval '.image.registry' "$config_file" 2>/dev/null || echo "registry.example.com")
    
    print_success "部署配置加载完成"
}

# 函数：预部署检查
pre_deploy_check() {
    local environment=$1
    
    print_title "执行预部署检查"
    
    # 检查 Kubernetes 连接
    print_info "检查 Kubernetes 连接..."
    if ! kubectl cluster-info &> /dev/null; then
        print_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    print_success "Kubernetes 连接正常"
    
    # 检查命名空间
    print_info "检查命名空间: $DEPLOY_NAMESPACE"
    if ! kubectl get namespace "$DEPLOY_NAMESPACE" &> /dev/null; then
        print_warning "命名空间不存在，将自动创建"
        kubectl create namespace "$DEPLOY_NAMESPACE"
    fi
    print_success "命名空间检查完成"
    
    # 检查镜像
    print_info "检查镜像可用性..."
    local image="$DEPLOY_REGISTRY/paas-platform:$DEPLOY_IMAGE_TAG"
    if ! docker pull "$image" &> /dev/null; then
        print_warning "镜像拉取失败，将尝试构建新镜像"
        build_images "$DEPLOY_IMAGE_TAG"
    fi
    print_success "镜像检查完成"
    
    # 检查资源配额
    print_info "检查资源配额..."
    local cpu_request=$(kubectl get resourcequota -n "$DEPLOY_NAMESPACE" -o json 2>/dev/null | jq -r '.items[0].status.used["requests.cpu"] // "0"')
    local memory_request=$(kubectl get resourcequota -n "$DEPLOY_NAMESPACE" -o json 2>/dev/null | jq -r '.items[0].status.used["requests.memory"] // "0"')
    print_success "资源配额检查完成"
    
    print_success "预部署检查完成"
}

# 函数：构建镜像
build_images() {
    local tag=${1:-"latest"}
    
    print_title "构建应用镜像"
    
    cd "$PROJECT_ROOT"
    
    # 服务列表
    local services=(
        "api-gateway"
        "user-service"
        "app-manager"
        "cicd-service"
        "monitor-service"
        "notification-service"
        "loadbalancer-service"
    )
    
    for service in "${services[@]}"; do
        print_info "构建 $service 镜像..."
        
        local image_name="$DEPLOY_REGISTRY/paas-platform/$service:$tag"
        
        docker build \
            -f "deployments/docker/Dockerfile.multi-stage" \
            --build-arg SERVICE_NAME="$service" \
            --build-arg VERSION="$tag" \
            --build-arg BUILD_TIME="$(date -u +"%Y-%m-%dT%H:%M:%SZ")" \
            --build-arg GIT_COMMIT="$(git rev-parse --short HEAD)" \
            -t "$image_name" \
            .
        
        # 推送镜像
        if [[ "$PUSH_IMAGES" == "true" ]]; then
            print_info "推送 $service 镜像..."
            docker push "$image_name"
        fi
        
        print_success "$service 镜像构建完成"
    done
    
    print_success "所有镜像构建完成"
}

# 函数：创建备份
create_backup() {
    local environment=$1
    local backup_name="backup-$environment-$(date +%Y%m%d-%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    print_title "创建部署备份"
    
    mkdir -p "$backup_path"
    
    # 备份 Kubernetes 资源
    print_info "备份 Kubernetes 资源..."
    kubectl get all -n "$DEPLOY_NAMESPACE" -o yaml > "$backup_path/kubernetes-resources.yaml"
    kubectl get configmaps -n "$DEPLOY_NAMESPACE" -o yaml > "$backup_path/configmaps.yaml"
    kubectl get secrets -n "$DEPLOY_NAMESPACE" -o yaml > "$backup_path/secrets.yaml"
    kubectl get pvc -n "$DEPLOY_NAMESPACE" -o yaml > "$backup_path/persistent-volumes.yaml"
    
    # 备份数据库（如果需要）
    if [[ "$BACKUP_DATABASE" == "true" ]]; then
        print_info "备份数据库..."
        backup_database "$backup_path"
    fi
    
    # 创建备份元数据
    cat > "$backup_path/metadata.json" << EOF
{
  "backup_name": "$backup_name",
  "environment": "$environment",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "git_commit": "$(git rev-parse HEAD)",
  "image_tag": "$DEPLOY_IMAGE_TAG",
  "namespace": "$DEPLOY_NAMESPACE"
}
EOF
    
    print_success "备份创建完成: $backup_path"
    echo "$backup_path" > "$BACKUP_DIR/latest-backup.txt"
}

# 函数：备份数据库
backup_database() {
    local backup_path=$1
    
    # 获取数据库连接信息
    local db_host=$(kubectl get secret postgres-secret -n "$DEPLOY_NAMESPACE" -o jsonpath='{.data.host}' | base64 -d 2>/dev/null || echo "postgres")
    local db_user=$(kubectl get secret postgres-secret -n "$DEPLOY_NAMESPACE" -o jsonpath='{.data.username}' | base64 -d 2>/dev/null || echo "postgres")
    local db_password=$(kubectl get secret postgres-secret -n "$DEPLOY_NAMESPACE" -o jsonpath='{.data.password}' | base64 -d 2>/dev/null || echo "password")
    local db_name=$(kubectl get secret postgres-secret -n "$DEPLOY_NAMESPACE" -o jsonpath='{.data.database}' | base64 -d 2>/dev/null || echo "paas_platform")
    
    # 创建数据库备份
    kubectl run postgres-backup \
        --image=postgres:15-alpine \
        --rm -i --restart=Never \
        --namespace="$DEPLOY_NAMESPACE" \
        --env="PGPASSWORD=$db_password" \
        --command -- pg_dump \
        -h "$db_host" \
        -U "$db_user" \
        -d "$db_name" \
        --no-owner \
        --no-privileges \
        > "$backup_path/database.sql"
    
    print_success "数据库备份完成"
}

# 函数：滚动更新部署
rolling_deploy() {
    local environment=$1
    
    print_title "执行滚动更新部署"
    
    # 更新部署配置
    print_info "更新部署配置..."
    
    # 使用 Helm 进行部署
    if [[ -f "$PROJECT_ROOT/deployments/helm/paas-platform/Chart.yaml" ]]; then
        helm upgrade --install paas-platform \
            "$PROJECT_ROOT/deployments/helm/paas-platform" \
            --namespace "$DEPLOY_NAMESPACE" \
            --set image.tag="$DEPLOY_IMAGE_TAG" \
            --set image.registry="$DEPLOY_REGISTRY" \
            --set replicas="$DEPLOY_REPLICAS" \
            --set resources.cpu="$DEPLOY_RESOURCES_CPU" \
            --set resources.memory="$DEPLOY_RESOURCES_MEMORY" \
            --wait \
            --timeout="${DEPLOY_TIMEOUT}s"
    else
        # 使用 kubectl 进行部署
        kubectl apply -f "$PROJECT_ROOT/deployments/kubernetes/" -n "$DEPLOY_NAMESPACE"
        
        # 等待部署完成
        kubectl rollout status deployment --all -n "$DEPLOY_NAMESPACE" --timeout="${DEPLOY_TIMEOUT}s"
    fi
    
    print_success "滚动更新部署完成"
}

# 函数：蓝绿部署
blue_green_deploy() {
    local environment=$1
    
    print_title "执行蓝绿部署"
    
    # 获取当前活跃版本
    local current_version=$(kubectl get service paas-platform -n "$DEPLOY_NAMESPACE" -o jsonpath='{.spec.selector.version}' 2>/dev/null || echo "blue")
    local new_version=$([ "$current_version" = "blue" ] && echo "green" || echo "blue")
    
    print_info "当前版本: $current_version, 新版本: $new_version"
    
    # 部署新版本
    print_info "部署新版本: $new_version"
    kubectl apply -f "$PROJECT_ROOT/deployments/kubernetes/" -n "$DEPLOY_NAMESPACE"
    kubectl patch deployment paas-platform -n "$DEPLOY_NAMESPACE" -p '{"spec":{"selector":{"matchLabels":{"version":"'$new_version'"}},"template":{"metadata":{"labels":{"version":"'$new_version'"}}}}}'
    
    # 等待新版本就绪
    kubectl rollout status deployment paas-platform -n "$DEPLOY_NAMESPACE" --timeout="${DEPLOY_TIMEOUT}s"
    
    # 健康检查
    print_info "执行健康检查..."
    if health_check "$new_version"; then
        # 切换流量
        print_info "切换流量到新版本..."
        kubectl patch service paas-platform -n "$DEPLOY_NAMESPACE" -p '{"spec":{"selector":{"version":"'$new_version'"}}}'
        
        # 清理旧版本
        print_info "清理旧版本..."
        kubectl delete deployment "paas-platform-$current_version" -n "$DEPLOY_NAMESPACE" --ignore-not-found=true
        
        print_success "蓝绿部署完成"
    else
        print_error "健康检查失败，回滚到旧版本"
        kubectl delete deployment "paas-platform-$new_version" -n "$DEPLOY_NAMESPACE" --ignore-not-found=true
        exit 1
    fi
}

# 函数：金丝雀部署
canary_deploy() {
    local environment=$1
    local canary_percentage=${2:-10}
    
    print_title "执行金丝雀部署"
    
    print_info "部署金丝雀版本 (${canary_percentage}% 流量)..."
    
    # 部署金丝雀版本
    kubectl apply -f "$PROJECT_ROOT/deployments/kubernetes/canary/" -n "$DEPLOY_NAMESPACE"
    
    # 配置流量分割
    kubectl patch virtualservice paas-platform -n "$DEPLOY_NAMESPACE" --type='merge' -p='{"spec":{"http":[{"match":[{"headers":{"canary":{"exact":"true"}}}],"route":[{"destination":{"host":"paas-platform-canary"}}]},{"route":[{"destination":{"host":"paas-platform"},"weight":'$((100-canary_percentage))'},{"destination":{"host":"paas-platform-canary"},"weight":'$canary_percentage'}]}]}}'
    
    # 监控金丝雀版本
    print_info "监控金丝雀版本..."
    sleep 60
    
    # 检查指标
    if canary_health_check; then
        print_info "金丝雀版本表现良好，继续推广..."
        
        # 逐步增加流量
        for percentage in 25 50 75 100; do
            print_info "增加金丝雀流量到 ${percentage}%..."
            kubectl patch virtualservice paas-platform -n "$DEPLOY_NAMESPACE" --type='merge' -p='{"spec":{"http":[{"route":[{"destination":{"host":"paas-platform"},"weight":'$((100-percentage))'},{"destination":{"host":"paas-platform-canary"},"weight":'$percentage'}]}]}}'
            sleep 30
            
            if ! canary_health_check; then
                print_error "金丝雀版本出现问题，回滚..."
                rollback_canary
                exit 1
            fi
        done
        
        # 完成金丝雀部署
        kubectl delete deployment paas-platform -n "$DEPLOY_NAMESPACE"
        kubectl patch deployment paas-platform-canary -n "$DEPLOY_NAMESPACE" -p '{"metadata":{"name":"paas-platform"}}'
        
        print_success "金丝雀部署完成"
    else
        print_error "金丝雀版本健康检查失败，回滚..."
        rollback_canary
        exit 1
    fi
}

# 函数：健康检查
health_check() {
    local version=${1:-""}
    local max_attempts=10
    local attempt=1
    
    print_info "执行健康检查..."
    
    # 获取服务端点
    local service_url="http://$(kubectl get service paas-platform -n "$DEPLOY_NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}'):8080"
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "$service_url/health" &> /dev/null; then
            print_success "健康检查通过"
            return 0
        fi
        
        print_info "健康检查失败，重试 ($attempt/$max_attempts)..."
        sleep 10
        ((attempt++))
    done
    
    print_error "健康检查失败"
    return 1
}

# 函数：金丝雀健康检查
canary_health_check() {
    # 检查错误率
    local error_rate=$(kubectl exec -n "$DEPLOY_NAMESPACE" deployment/prometheus -- \
        promtool query instant 'rate(http_requests_total{job="paas-platform-canary",status=~"5.."}[5m]) / rate(http_requests_total{job="paas-platform-canary"}[5m])' | \
        grep -o '[0-9.]*' | head -1)
    
    if (( $(echo "$error_rate > 0.05" | bc -l) )); then
        print_error "金丝雀版本错误率过高: $error_rate"
        return 1
    fi
    
    # 检查响应时间
    local response_time=$(kubectl exec -n "$DEPLOY_NAMESPACE" deployment/prometheus -- \
        promtool query instant 'histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="paas-platform-canary"}[5m]))' | \
        grep -o '[0-9.]*' | head -1)
    
    if (( $(echo "$response_time > 1.0" | bc -l) )); then
        print_error "金丝雀版本响应时间过长: $response_time"
        return 1
    fi
    
    print_success "金丝雀版本指标正常"
    return 0
}

# 函数：回滚金丝雀
rollback_canary() {
    print_info "回滚金丝雀部署..."
    
    # 恢复流量到稳定版本
    kubectl patch virtualservice paas-platform -n "$DEPLOY_NAMESPACE" --type='merge' -p='{"spec":{"http":[{"route":[{"destination":{"host":"paas-platform"}}]}]}}'
    
    # 删除金丝雀版本
    kubectl delete deployment paas-platform-canary -n "$DEPLOY_NAMESPACE" --ignore-not-found=true
    
    print_success "金丝雀回滚完成"
}

# 函数：部署后验证
post_deploy_verification() {
    local environment=$1
    
    print_title "执行部署后验证"
    
    # 检查所有 Pod 状态
    print_info "检查 Pod 状态..."
    kubectl get pods -n "$DEPLOY_NAMESPACE"
    
    # 检查服务状态
    print_info "检查服务状态..."
    kubectl get services -n "$DEPLOY_NAMESPACE"
    
    # 执行冒烟测试
    print_info "执行冒烟测试..."
    if [[ -f "$PROJECT_ROOT/tests/smoke-test.sh" ]]; then
        bash "$PROJECT_ROOT/tests/smoke-test.sh" "$environment"
    fi
    
    # 检查关键指标
    print_info "检查关键指标..."
    sleep 30  # 等待指标收集
    
    # 验证数据库连接
    print_info "验证数据库连接..."
    kubectl run db-test \
        --image=postgres:15-alpine \
        --rm -i --restart=Never \
        --namespace="$DEPLOY_NAMESPACE" \
        --command -- psql \
        -h postgres \
        -U postgres \
        -d paas_platform \
        -c "SELECT 1;" &> /dev/null
    
    print_success "部署后验证完成"
}

# 函数：发送通知
send_notification() {
    local status=$1
    local environment=$2
    local message=$3
    
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        local color=$([ "$status" = "success" ] && echo "good" || echo "danger")
        local emoji=$([ "$status" = "success" ] && echo ":white_check_mark:" || echo ":x:")
        
        curl -X POST "$SLACK_WEBHOOK_URL" \
            -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"$emoji PaaS 平台部署通知\",
                    \"fields\": [
                        {\"title\": \"环境\", \"value\": \"$environment\", \"short\": true},
                        {\"title\": \"状态\", \"value\": \"$status\", \"short\": true},
                        {\"title\": \"消息\", \"value\": \"$message\", \"short\": false}
                    ],
                    \"footer\": \"PaaS Platform\",
                    \"ts\": $(date +%s)
                }]
            }" &> /dev/null
    fi
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台自动化部署脚本"
    echo ""
    echo "用法: $0 [选项] <环境>"
    echo ""
    echo "环境:"
    echo "  development    开发环境"
    echo "  staging        测试环境"
    echo "  production     生产环境"
    echo ""
    echo "选项:"
    echo "  -s, --strategy <策略>     部署策略 (rolling|blue-green|canary)"
    echo "  -t, --timeout <秒数>      部署超时时间"
    echo "  -b, --backup             创建部署前备份"
    echo "  --skip-build             跳过镜像构建"
    echo "  --skip-tests             跳过测试"
    echo "  --push-images            推送镜像到仓库"
    echo "  --dry-run                模拟运行"
    echo "  -h, --help               显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DEPLOY_TIMEOUT           部署超时时间 (默认: 600)"
    echo "  BACKUP_DATABASE          是否备份数据库 (true|false)"
    echo "  SLACK_WEBHOOK_URL        Slack 通知 Webhook URL"
    echo "  PUSH_IMAGES              是否推送镜像 (true|false)"
    echo ""
    echo "示例:"
    echo "  $0 staging                           # 部署到测试环境"
    echo "  $0 -s blue-green production         # 蓝绿部署到生产环境"
    echo "  $0 -s canary -b production          # 金丝雀部署到生产环境并创建备份"
}

# 主函数
main() {
    local environment=""
    local strategy="$DEFAULT_STRATEGY"
    local timeout="$DEFAULT_TIMEOUT"
    local create_backup=false
    local skip_build=false
    local skip_tests=false
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--strategy)
                strategy="$2"
                shift 2
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            -b|--backup)
                create_backup=true
                shift
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-tests)
                skip_tests=true
                shift
                ;;
            --push-images)
                export PUSH_IMAGES=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                environment="$1"
                shift
                ;;
        esac
    done
    
    # 验证参数
    if [[ -z "$environment" ]]; then
        print_error "请指定部署环境"
        show_help
        exit 1
    fi
    
    if [[ ! "$strategy" =~ ^(rolling|blue-green|canary)$ ]]; then
        print_error "不支持的部署策略: $strategy"
        exit 1
    fi
    
    # 设置环境变量
    export DEPLOY_TIMEOUT="$timeout"
    export DEPLOY_STRATEGY="$strategy"
    
    print_info "开始部署到 $environment 环境，使用 $strategy 策略"
    
    # 执行部署流程
    if [[ "$dry_run" == "true" ]]; then
        print_warning "模拟运行模式，不会执行实际部署"
        exit 0
    fi
    
    # 检查依赖
    check_dependencies
    
    # 加载配置
    load_deploy_config "$environment"
    
    # 预部署检查
    pre_deploy_check "$environment"
    
    # 构建镜像
    if [[ "$skip_build" != "true" ]]; then
        build_images "$DEPLOY_IMAGE_TAG"
    fi
    
    # 创建备份
    if [[ "$create_backup" == "true" ]]; then
        create_backup "$environment"
    fi
    
    # 执行部署
    case "$strategy" in
        "rolling")
            rolling_deploy "$environment"
            ;;
        "blue-green")
            blue_green_deploy "$environment"
            ;;
        "canary")
            canary_deploy "$environment"
            ;;
    esac
    
    # 部署后验证
    post_deploy_verification "$environment"
    
    # 发送成功通知
    send_notification "success" "$environment" "部署成功完成，使用 $strategy 策略"
    
    print_success "🎉 部署完成！环境: $environment, 策略: $strategy"
}

# 错误处理
trap 'send_notification "failure" "$environment" "部署失败: $BASH_COMMAND"; exit 1' ERR

# 脚本入口
main "$@"
