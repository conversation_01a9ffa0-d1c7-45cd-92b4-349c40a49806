#!/bin/bash

# PaaS 平台代码清理脚本
# 识别和清理代码中的重复、冗余和未使用的代码

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REPORTS_DIR="$PROJECT_ROOT/code-analysis-reports"

echo -e "${BLUE}🧹 PaaS 平台代码清理脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查依赖工具
check_dependencies() {
    print_info "检查代码分析工具..."
    
    local missing_tools=()
    
    # 检查 Go 工具
    if ! command -v go &> /dev/null; then
        missing_tools+=("go")
    fi
    
    # 检查 goimports
    if ! command -v goimports &> /dev/null; then
        print_info "安装 goimports..."
        go install golang.org/x/tools/cmd/goimports@latest
    fi
    
    # 检查 golangci-lint
    if ! command -v golangci-lint &> /dev/null; then
        print_info "安装 golangci-lint..."
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2
    fi
    
    # 检查 gocyclo
    if ! command -v gocyclo &> /dev/null; then
        print_info "安装 gocyclo..."
        go install github.com/fzipp/gocyclo/cmd/gocyclo@latest
    fi
    
    # 检查 deadcode
    if ! command -v deadcode &> /dev/null; then
        print_info "安装 deadcode..."
        go install golang.org/x/tools/cmd/deadcode@latest
    fi
    
    # 检查 Node.js 工具（前端）
    if command -v npm &> /dev/null; then
        if [[ ! -d "$PROJECT_ROOT/web/node_modules" ]]; then
            print_info "安装前端依赖..."
            cd "$PROJECT_ROOT/web" && npm install
        fi
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "缺少必要工具: ${missing_tools[*]}"
        exit 1
    fi
    
    print_success "代码分析工具检查完成"
}

# 函数：创建报告目录
setup_reports_directory() {
    print_info "创建报告目录..."
    
    mkdir -p "$REPORTS_DIR"
    rm -f "$REPORTS_DIR"/*.txt
    rm -f "$REPORTS_DIR"/*.json
    rm -f "$REPORTS_DIR"/*.html
    
    print_success "报告目录准备完成"
}

# 函数：分析未使用的导入
analyze_unused_imports() {
    print_info "分析未使用的导入..."
    
    cd "$PROJECT_ROOT"
    
    # 查找所有 Go 文件
    local go_files=$(find . -name "*.go" -not -path "./vendor/*" -not -path "./node_modules/*")
    local unused_imports_file="$REPORTS_DIR/unused-imports.txt"
    
    echo "# 未使用的导入分析报告" > "$unused_imports_file"
    echo "生成时间: $(date)" >> "$unused_imports_file"
    echo "" >> "$unused_imports_file"
    
    local issues_found=0
    
    for file in $go_files; do
        # 使用 goimports 检查未使用的导入
        local temp_file=$(mktemp)
        goimports "$file" > "$temp_file"
        
        if ! diff -q "$file" "$temp_file" > /dev/null; then
            echo "文件: $file" >> "$unused_imports_file"
            echo "建议修复:" >> "$unused_imports_file"
            diff -u "$file" "$temp_file" | head -20 >> "$unused_imports_file"
            echo "" >> "$unused_imports_file"
            issues_found=$((issues_found + 1))
        fi
        
        rm -f "$temp_file"
    done
    
    echo "发现 $issues_found 个文件存在未使用的导入问题" >> "$unused_imports_file"
    
    if [[ $issues_found -gt 0 ]]; then
        print_warning "发现 $issues_found 个文件存在未使用的导入"
    else
        print_success "未发现未使用的导入问题"
    fi
    
    print_info "报告已保存: $unused_imports_file"
}

# 函数：分析未使用的变量和函数
analyze_unused_code() {
    print_info "分析未使用的代码..."
    
    cd "$PROJECT_ROOT"
    
    local unused_code_file="$REPORTS_DIR/unused-code.txt"
    
    echo "# 未使用代码分析报告" > "$unused_code_file"
    echo "生成时间: $(date)" >> "$unused_code_file"
    echo "" >> "$unused_code_file"
    
    # 使用 deadcode 分析未使用的函数
    print_info "分析未使用的函数..."
    echo "## 未使用的函数" >> "$unused_code_file"
    
    if deadcode ./... 2>/dev/null >> "$unused_code_file"; then
        print_success "未使用函数分析完成"
    else
        print_warning "未使用函数分析可能不完整"
    fi
    
    echo "" >> "$unused_code_file"
    
    # 使用 golangci-lint 分析更多问题
    print_info "运行 golangci-lint 分析..."
    echo "## Golangci-lint 分析结果" >> "$unused_code_file"
    
    golangci-lint run --enable=unused,deadcode,varcheck,structcheck,ineffassign \
        --out-format=line-number \
        ./... >> "$unused_code_file" 2>/dev/null || true
    
    print_info "报告已保存: $unused_code_file"
}

# 函数：分析代码重复
analyze_code_duplication() {
    print_info "分析代码重复..."
    
    cd "$PROJECT_ROOT"
    
    local duplication_file="$REPORTS_DIR/code-duplication.txt"
    
    echo "# 代码重复分析报告" > "$duplication_file"
    echo "生成时间: $(date)" >> "$duplication_file"
    echo "" >> "$duplication_file"
    
    # 查找相似的函数名
    print_info "查找相似的函数名..."
    echo "## 相似的函数名" >> "$duplication_file"
    
    grep -r "func " --include="*.go" . | \
        grep -v vendor | \
        grep -v node_modules | \
        sed 's/.*func \([^(]*\).*/\1/' | \
        sort | \
        uniq -c | \
        sort -nr | \
        awk '$1 > 1 {print $0}' >> "$duplication_file"
    
    echo "" >> "$duplication_file"
    
    # 查找相似的结构体
    print_info "查找相似的结构体..."
    echo "## 相似的结构体" >> "$duplication_file"
    
    grep -r "type.*struct" --include="*.go" . | \
        grep -v vendor | \
        grep -v node_modules | \
        sed 's/.*type \([^ ]*\) struct.*/\1/' | \
        sort | \
        uniq -c | \
        sort -nr | \
        awk '$1 > 1 {print $0}' >> "$duplication_file"
    
    echo "" >> "$duplication_file"
    
    # 查找重复的常量定义
    print_info "查找重复的常量定义..."
    echo "## 重复的常量定义" >> "$duplication_file"
    
    grep -r "const " --include="*.go" . | \
        grep -v vendor | \
        grep -v node_modules | \
        grep -v "//" | \
        sed 's/.*const \([^= ]*\).*/\1/' | \
        sort | \
        uniq -c | \
        sort -nr | \
        awk '$1 > 1 {print $0}' >> "$duplication_file"
    
    print_info "报告已保存: $duplication_file"
}

# 函数：分析代码复杂度
analyze_code_complexity() {
    print_info "分析代码复杂度..."
    
    cd "$PROJECT_ROOT"
    
    local complexity_file="$REPORTS_DIR/code-complexity.txt"
    
    echo "# 代码复杂度分析报告" > "$complexity_file"
    echo "生成时间: $(date)" >> "$complexity_file"
    echo "" >> "$complexity_file"
    
    # 使用 gocyclo 分析圈复杂度
    print_info "分析圈复杂度..."
    echo "## 高复杂度函数 (复杂度 > 10)" >> "$complexity_file"
    
    gocyclo -over 10 . >> "$complexity_file" 2>/dev/null || true
    
    echo "" >> "$complexity_file"
    echo "## 所有函数复杂度统计" >> "$complexity_file"
    
    gocyclo . | sort -nr >> "$complexity_file" 2>/dev/null || true
    
    print_info "报告已保存: $complexity_file"
}

# 函数：分析前端代码问题
analyze_frontend_code() {
    print_info "分析前端代码..."
    
    if [[ ! -d "$PROJECT_ROOT/web" ]]; then
        print_warning "未找到前端项目目录"
        return 0
    fi
    
    cd "$PROJECT_ROOT/web"
    
    local frontend_file="$REPORTS_DIR/frontend-analysis.txt"
    
    echo "# 前端代码分析报告" > "$frontend_file"
    echo "生成时间: $(date)" >> "$frontend_file"
    echo "" >> "$frontend_file"
    
    # 检查未使用的依赖
    if command -v npm &> /dev/null && [[ -f "package.json" ]]; then
        print_info "检查未使用的 npm 依赖..."
        echo "## 未使用的 npm 依赖" >> "$frontend_file"
        
        if command -v npx &> /dev/null; then
            npx depcheck --json >> "$frontend_file" 2>/dev/null || true
        else
            echo "需要安装 depcheck: npm install -g depcheck" >> "$frontend_file"
        fi
        
        echo "" >> "$frontend_file"
    fi
    
    # 检查 TypeScript/JavaScript 问题
    if [[ -f "tsconfig.json" ]] || [[ -f "jsconfig.json" ]]; then
        print_info "检查 TypeScript/JavaScript 问题..."
        echo "## TypeScript/JavaScript 问题" >> "$frontend_file"
        
        # 查找未使用的导入
        find src -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | \
            xargs grep -l "import.*from" | \
            head -10 >> "$frontend_file" 2>/dev/null || true
        
        echo "" >> "$frontend_file"
    fi
    
    # 检查 CSS 问题
    print_info "检查 CSS 问题..."
    echo "## CSS 文件统计" >> "$frontend_file"
    
    find . -name "*.css" -o -name "*.scss" -o -name "*.less" | \
        wc -l >> "$frontend_file" 2>/dev/null || true
    
    print_info "报告已保存: $frontend_file"
}

# 函数：生成清理建议
generate_cleanup_suggestions() {
    print_info "生成清理建议..."
    
    local suggestions_file="$REPORTS_DIR/cleanup-suggestions.md"
    
    cat > "$suggestions_file" << 'EOF'
# 代码清理建议报告

## 📋 清理优先级

### 🔴 高优先级（立即处理）
1. **未使用的导入** - 影响编译性能和代码可读性
2. **未使用的变量** - 可能导致内存泄漏
3. **高复杂度函数** - 影响代码维护性

### 🟡 中优先级（近期处理）
1. **代码重复** - 影响代码维护性
2. **未使用的函数** - 增加代码库大小
3. **过时的依赖** - 可能存在安全风险

### 🟢 低优先级（有时间时处理）
1. **代码风格统一** - 提升代码可读性
2. **注释完善** - 提升代码可维护性
3. **文档更新** - 保持文档与代码同步

## 🛠️ 自动化清理命令

### Go 代码清理
```bash
# 修复导入
goimports -w .

# 格式化代码
gofmt -w .

# 运行 linter
golangci-lint run --fix
```

### 前端代码清理
```bash
# 进入前端目录
cd web

# 修复 ESLint 问题
npm run lint:fix

# 格式化代码
npm run format

# 清理未使用的依赖
npx depcheck
```

## 📊 清理效果评估

### 预期收益
- 🚀 **性能提升**: 减少编译时间 10-20%
- 📦 **包大小减少**: 减少最终包大小 5-15%
- 🧹 **代码质量**: 提升代码可读性和维护性
- 🔒 **安全性**: 减少潜在的安全风险

### 风险评估
- ⚠️ **低风险**: 删除未使用的导入和变量
- ⚠️ **中风险**: 重构高复杂度函数
- ⚠️ **高风险**: 删除看似未使用但实际被反射调用的代码

## 🎯 实施计划

### 第一阶段（本周）
1. 修复所有未使用的导入
2. 删除明确未使用的变量
3. 运行自动化代码格式化

### 第二阶段（下周）
1. 重构高复杂度函数
2. 合并重复的代码片段
3. 更新过时的依赖

### 第三阶段（下个月）
1. 完善代码注释和文档
2. 统一代码风格
3. 建立代码质量检查流程

## 📝 注意事项

1. **备份代码**: 在进行大规模清理前，确保代码已提交到版本控制
2. **测试验证**: 每次清理后运行完整的测试套件
3. **逐步进行**: 避免一次性进行大量修改
4. **团队协调**: 与团队成员协调，避免合并冲突
5. **文档更新**: 及时更新相关文档和注释

EOF
    
    print_success "清理建议已生成: $suggestions_file"
}

# 函数：执行自动化清理
perform_automatic_cleanup() {
    print_info "执行自动化清理..."
    
    cd "$PROJECT_ROOT"
    
    # 备份当前状态
    local backup_branch="code-cleanup-backup-$(date +%Y%m%d-%H%M%S)"
    if git rev-parse --git-dir > /dev/null 2>&1; then
        print_info "创建备份分支: $backup_branch"
        git checkout -b "$backup_branch" > /dev/null 2>&1 || true
        git checkout - > /dev/null 2>&1 || true
    fi
    
    # Go 代码清理
    print_info "清理 Go 代码..."
    
    # 修复导入
    find . -name "*.go" -not -path "./vendor/*" -not -path "./node_modules/*" | \
        xargs goimports -w
    
    # 格式化代码
    find . -name "*.go" -not -path "./vendor/*" -not -path "./node_modules/*" | \
        xargs gofmt -w
    
    # 前端代码清理
    if [[ -d "web" ]] && [[ -f "web/package.json" ]]; then
        print_info "清理前端代码..."
        cd web
        
        # 运行 ESLint 修复（如果配置了）
        if npm run lint:fix > /dev/null 2>&1; then
            print_success "ESLint 修复完成"
        fi
        
        # 运行 Prettier 格式化（如果配置了）
        if npm run format > /dev/null 2>&1; then
            print_success "Prettier 格式化完成"
        fi
        
        cd ..
    fi
    
    print_success "自动化清理完成"
}

# 函数：生成清理报告
generate_cleanup_report() {
    print_info "生成清理报告..."
    
    local report_file="$REPORTS_DIR/cleanup-summary.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 统计文件数量
    local go_files=$(find . -name "*.go" -not -path "./vendor/*" -not -path "./node_modules/*" | wc -l)
    local js_files=$(find . -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" | wc -l)
    local css_files=$(find . -name "*.css" -o -name "*.scss" -o -name "*.less" | wc -l)
    
    # 生成 JSON 报告
    cat > "$report_file" << EOF
{
  "cleanup_summary": {
    "timestamp": "$timestamp",
    "project_statistics": {
      "go_files": $go_files,
      "javascript_files": $js_files,
      "css_files": $css_files
    },
    "analysis_reports": {
      "unused_imports": "$REPORTS_DIR/unused-imports.txt",
      "unused_code": "$REPORTS_DIR/unused-code.txt",
      "code_duplication": "$REPORTS_DIR/code-duplication.txt",
      "code_complexity": "$REPORTS_DIR/code-complexity.txt",
      "frontend_analysis": "$REPORTS_DIR/frontend-analysis.txt",
      "cleanup_suggestions": "$REPORTS_DIR/cleanup-suggestions.md"
    },
    "recommendations": [
      "修复未使用的导入",
      "删除未使用的代码",
      "重构高复杂度函数",
      "合并重复代码",
      "更新依赖库"
    ]
  }
}
EOF
    
    print_success "清理报告已生成: $report_file"
}

# 函数：显示清理结果
show_cleanup_results() {
    echo ""
    echo "=================================================="
    print_success "🎉 代码清理分析完成！"
    echo "=================================================="
    echo ""
    
    echo "📊 分析报告："
    echo "  • 未使用导入:   $REPORTS_DIR/unused-imports.txt"
    echo "  • 未使用代码:   $REPORTS_DIR/unused-code.txt"
    echo "  • 代码重复:     $REPORTS_DIR/code-duplication.txt"
    echo "  • 代码复杂度:   $REPORTS_DIR/code-complexity.txt"
    echo "  • 前端分析:     $REPORTS_DIR/frontend-analysis.txt"
    echo "  • 清理建议:     $REPORTS_DIR/cleanup-suggestions.md"
    echo "  • 总结报告:     $REPORTS_DIR/cleanup-summary.json"
    echo ""
    
    echo "🔧 下一步操作："
    echo "  1. 查看分析报告，了解代码问题"
    echo "  2. 按优先级执行清理建议"
    echo "  3. 运行测试验证清理效果"
    echo "  4. 提交清理后的代码"
    echo ""
    
    echo "⚡ 快速清理命令："
    echo "  $0 --auto-fix    # 执行自动化修复"
    echo "  $0 --report-only # 只生成报告，不执行清理"
    echo ""
}

# 主函数
main() {
    local auto_fix=false
    local report_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --auto-fix)
                auto_fix=true
                shift
                ;;
            --report-only)
                report_only=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --auto-fix       执行自动化代码修复"
                echo "  --report-only    只生成分析报告"
                echo "  -h, --help       显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行清理流程
    check_dependencies
    setup_reports_directory
    
    # 分析阶段
    analyze_unused_imports
    analyze_unused_code
    analyze_code_duplication
    analyze_code_complexity
    analyze_frontend_code
    generate_cleanup_suggestions
    
    # 清理阶段
    if [[ "$auto_fix" == "true" && "$report_only" == "false" ]]; then
        perform_automatic_cleanup
    fi
    
    # 报告阶段
    generate_cleanup_report
    show_cleanup_results
    
    if [[ "$auto_fix" == "true" ]]; then
        print_success "自动化清理已完成，请运行测试验证结果"
    elif [[ "$report_only" == "true" ]]; then
        print_info "分析报告已生成，请查看建议并手动执行清理"
    else
        print_info "分析完成，使用 --auto-fix 参数执行自动化清理"
    fi
}

# 脚本入口
main "$@"
