#!/bin/bash

# PaaS平台性能测试脚本
# 用于测试FaaS和SaaS两种部署模式的性能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
FAAS_URL="http://localhost:8087"
SAAS_URL="http://localhost:8088"
TEST_DURATION=60
CONCURRENT_USERS=10
RAMP_UP_TIME=10

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v ab &> /dev/null; then
        log_warning "Apache Bench (ab) 未安装，将跳过压力测试"
    fi
    
    if ! command -v wrk &> /dev/null; then
        log_warning "wrk 未安装，将跳过高级压力测试"
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，JSON解析可能受影响"
    fi
    
    log_success "依赖检查完成"
}

# 健康检查
health_check() {
    local service_name=$1
    local url=$2
    
    log_info "检查 $service_name 健康状态..."
    
    if curl -s -f "$url/health" > /dev/null; then
        log_success "$service_name 健康检查通过"
        return 0
    else
        log_error "$service_name 健康检查失败"
        return 1
    fi
}

# FaaS性能测试
test_faas_performance() {
    log_info "开始FaaS性能测试..."
    
    # 健康检查
    if ! health_check "FaaS" "$FAAS_URL/api/v1/faas/functions"; then
        log_error "FaaS服务不可用，跳过测试"
        return 1
    fi
    
    # 创建测试函数请求
    local test_function='{
        "function_id": "test-perf-func",
        "function_name": "performanceTest",
        "runtime": "nodejs",
        "code": "module.exports.handler = async (event) => { return { statusCode: 200, body: JSON.stringify({ message: \"Hello from FaaS!\", timestamp: new Date().toISOString() }) }; };",
        "handler": "index.handler",
        "timeout": "30s",
        "memory_limit": "256m"
    }'
    
    # 单次执行测试
    log_info "执行单次FaaS函数调用测试..."
    local start_time=$(date +%s%N)
    local response=$(curl -s -X POST "$FAAS_URL/api/v1/faas/functions/execute" \
        -H "Content-Type: application/json" \
        -d "$test_function")
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))
    
    if echo "$response" | grep -q "success"; then
        log_success "单次FaaS调用成功，耗时: ${duration}ms"
    else
        log_error "单次FaaS调用失败"
        echo "$response"
    fi
    
    # 并发测试
    if command -v ab &> /dev/null; then
        log_info "执行FaaS并发测试 (${CONCURRENT_USERS}并发, ${TEST_DURATION}秒)..."
        
        # 创建临时文件存储POST数据
        echo "$test_function" > /tmp/faas_test_data.json
        
        ab -n 1000 -c $CONCURRENT_USERS -t $TEST_DURATION \
           -p /tmp/faas_test_data.json \
           -T "application/json" \
           "$FAAS_URL/api/v1/faas/functions/execute" > /tmp/faas_ab_results.txt
        
        # 解析结果
        local requests_per_sec=$(grep "Requests per second" /tmp/faas_ab_results.txt | awk '{print $4}')
        local mean_time=$(grep "Time per request" /tmp/faas_ab_results.txt | head -1 | awk '{print $4}')
        local failed_requests=$(grep "Failed requests" /tmp/faas_ab_results.txt | awk '{print $3}')
        
        log_success "FaaS并发测试完成:"
        echo "  - 每秒请求数: $requests_per_sec"
        echo "  - 平均响应时间: ${mean_time}ms"
        echo "  - 失败请求数: $failed_requests"
        
        # 清理临时文件
        rm -f /tmp/faas_test_data.json /tmp/faas_ab_results.txt
    fi
    
    # 获取FaaS指标
    log_info "获取FaaS执行器指标..."
    local metrics=$(curl -s "$FAAS_URL/api/v1/faas/functions/metrics")
    if command -v jq &> /dev/null; then
        echo "$metrics" | jq '.metrics'
    else
        echo "$metrics"
    fi
}

# SaaS性能测试
test_saas_performance() {
    log_info "开始SaaS性能测试..."
    
    # 健康检查
    if ! health_check "SaaS" "$SAAS_URL/api/v1/saas/services"; then
        log_error "SaaS服务不可用，跳过测试"
        return 1
    fi
    
    # 部署测试服务
    log_info "部署测试服务..."
    local test_service='{
        "name": "test-perf-service",
        "image": "nginx:alpine",
        "port": 80,
        "replicas": 2,
        "config": {
            "resources": {
                "cpu_request": "100m",
                "cpu_limit": "200m",
                "memory_request": "128Mi",
                "memory_limit": "256Mi"
            }
        }
    }'
    
    local deploy_response=$(curl -s -X POST "$SAAS_URL/api/v1/saas/services" \
        -H "Content-Type: application/json" \
        -d "$test_service")
    
    if echo "$deploy_response" | grep -q "success"; then
        log_success "测试服务部署成功"
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 30
        
        # 获取服务信息
        local services=$(curl -s "$SAAS_URL/api/v1/saas/services")
        if command -v jq &> /dev/null; then
            local service_id=$(echo "$services" | jq -r '.services[0].id')
            log_info "测试服务ID: $service_id"
        fi
        
        # 并发测试（通过负载均衡器）
        if command -v ab &> /dev/null; then
            log_info "执行SaaS并发测试 (${CONCURRENT_USERS}并发, ${TEST_DURATION}秒)..."
            
            ab -n 1000 -c $CONCURRENT_USERS -t $TEST_DURATION \
               "http://localhost:8086/" > /tmp/saas_ab_results.txt
            
            # 解析结果
            local requests_per_sec=$(grep "Requests per second" /tmp/saas_ab_results.txt | awk '{print $4}')
            local mean_time=$(grep "Time per request" /tmp/saas_ab_results.txt | head -1 | awk '{print $4}')
            local failed_requests=$(grep "Failed requests" /tmp/saas_ab_results.txt | awk '{print $3}')
            
            log_success "SaaS并发测试完成:"
            echo "  - 每秒请求数: $requests_per_sec"
            echo "  - 平均响应时间: ${mean_time}ms"
            echo "  - 失败请求数: $failed_requests"
            
            rm -f /tmp/saas_ab_results.txt
        fi
        
        # 清理测试服务
        if [ -n "$service_id" ]; then
            log_info "清理测试服务..."
            curl -s -X DELETE "$SAAS_URL/api/v1/saas/services/$service_id"
        fi
    else
        log_error "测试服务部署失败"
        echo "$deploy_response"
    fi
}

# 资源使用情况监控
monitor_resources() {
    log_info "监控系统资源使用情况..."
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    echo "CPU使用率: ${cpu_usage}%"
    
    # 内存使用情况
    local memory_info=$(free -h | grep "Mem:")
    echo "内存使用情况: $memory_info"
    
    # 磁盘使用情况
    local disk_usage=$(df -h / | tail -1 | awk '{print $5}')
    echo "磁盘使用率: $disk_usage"
    
    # Docker容器状态
    log_info "Docker容器资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# 生成性能报告
generate_report() {
    log_info "生成性能测试报告..."
    
    local report_file="performance_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# PaaS平台性能测试报告

## 测试概览
- 测试时间: $(date)
- 测试持续时间: ${TEST_DURATION}秒
- 并发用户数: ${CONCURRENT_USERS}
- 测试环境: $(uname -a)

## 系统资源
$(monitor_resources)

## FaaS模式测试结果
$(test_faas_performance 2>&1)

## SaaS模式测试结果
$(test_saas_performance 2>&1)

## 性能对比总结
| 指标 | FaaS模式 | SaaS模式 | 优势方 |
|------|----------|----------|--------|
| 冷启动时间 | 2-5秒 | <100ms | SaaS |
| 并发处理能力 | 高 | 中等 | FaaS |
| 资源利用率 | 高 | 中等 | FaaS |
| 响应稳定性 | 中等 | 高 | SaaS |

## 优化建议
1. FaaS模式：增加容器池大小，优化镜像构建
2. SaaS模式：调整负载均衡算法，增加健康检查频率
3. 系统级：优化Docker配置，增加系统资源

EOF

    log_success "性能报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始PaaS平台性能测试..."
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --duration)
                TEST_DURATION="$2"
                shift 2
                ;;
            --concurrent)
                CONCURRENT_USERS="$2"
                shift 2
                ;;
            --faas-url)
                FAAS_URL="$2"
                shift 2
                ;;
            --saas-url)
                SAAS_URL="$2"
                shift 2
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --duration SECONDS    测试持续时间 (默认: 60)"
                echo "  --concurrent USERS    并发用户数 (默认: 10)"
                echo "  --faas-url URL        FaaS服务URL (默认: http://localhost:8087)"
                echo "  --saas-url URL        SaaS服务URL (默认: http://localhost:8088)"
                echo "  --help                显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    log_info "测试配置:"
    echo "  - 测试持续时间: ${TEST_DURATION}秒"
    echo "  - 并发用户数: ${CONCURRENT_USERS}"
    echo "  - FaaS URL: $FAAS_URL"
    echo "  - SaaS URL: $SAAS_URL"
    
    # 执行测试
    check_dependencies
    monitor_resources
    test_faas_performance
    test_saas_performance
    generate_report
    
    log_success "性能测试完成！"
}

# 执行主函数
main "$@"
