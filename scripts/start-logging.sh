#!/bin/bash

# PaaS 平台日志聚合系统启动脚本
# 启动完整的 ELK 栈：Elasticsearch, Logstash, Kibana, Filebeat

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.logging.yml"
CONFIG_DIR="$PROJECT_ROOT/configs"

echo -e "${BLUE}📊 PaaS 平台日志聚合系统启动脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查系统要求
check_system_requirements() {
    print_info "检查系统要求..."
    
    # 检查内存
    local total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [[ $total_mem -lt 4096 ]]; then
        print_warning "系统内存 ${total_mem}MB 可能不足，建议至少 4GB"
        print_info "Elasticsearch 需要较多内存，可能影响性能"
    else
        print_success "系统内存检查通过 (${total_mem}MB)"
    fi
    
    # 检查磁盘空间
    local disk_space=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $disk_space -lt 10 ]]; then
        print_warning "磁盘空间 ${disk_space}GB 可能不足，建议至少 10GB"
    else
        print_success "磁盘空间检查通过 (${disk_space}GB 可用)"
    fi
    
    # 检查 vm.max_map_count (Elasticsearch 要求)
    local max_map_count=$(sysctl -n vm.max_map_count 2>/dev/null || echo "0")
    if [[ $max_map_count -lt 262144 ]]; then
        print_warning "vm.max_map_count 值过低 ($max_map_count)，Elasticsearch 可能无法启动"
        print_info "建议执行: sudo sysctl -w vm.max_map_count=262144"
        
        # 尝试自动设置
        if [[ $EUID -eq 0 ]]; then
            sysctl -w vm.max_map_count=262144
            print_success "已自动设置 vm.max_map_count=262144"
        else
            print_info "需要 root 权限设置 vm.max_map_count"
        fi
    else
        print_success "vm.max_map_count 检查通过 ($max_map_count)"
    fi
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Docker 服务状态
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    print_success "系统依赖检查通过"
}

# 函数：创建必要的目录和配置
setup_directories() {
    print_info "创建必要的目录..."
    
    local directories=(
        "$PROJECT_ROOT/data/elasticsearch"
        "$PROJECT_ROOT/data/kibana"
        "$PROJECT_ROOT/data/logstash"
        "$PROJECT_ROOT/logs/user-service"
        "$PROJECT_ROOT/logs/app-manager"
        "$PROJECT_ROOT/logs/api-gateway"
        "$PROJECT_ROOT/logs/cicd-service"
        "$PROJECT_ROOT/logs/config-service"
        "$PROJECT_ROOT/logs/script-service"
        "$CONFIG_DIR/elasticsearch"
        "$CONFIG_DIR/kibana"
        "$CONFIG_DIR/logstash/pipeline"
        "$CONFIG_DIR/filebeat"
        "$CONFIG_DIR/curator"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
    
    # 设置 Elasticsearch 数据目录权限
    if [[ -d "$PROJECT_ROOT/data/elasticsearch" ]]; then
        chmod 777 "$PROJECT_ROOT/data/elasticsearch"
    fi
    
    print_success "目录创建完成"
}

# 函数：生成缺失的配置文件
generate_missing_configs() {
    print_info "检查并生成缺失的配置文件..."
    
    # Elasticsearch 配置
    if [[ ! -f "$CONFIG_DIR/elasticsearch/elasticsearch.yml" ]]; then
        cat > "$CONFIG_DIR/elasticsearch/elasticsearch.yml" << EOF
cluster.name: paas-logs-cluster
node.name: paas-elasticsearch-node
network.host: 0.0.0.0
http.port: 9200
discovery.type: single-node
xpack.security.enabled: false
xpack.monitoring.collection.enabled: true
EOF
        print_info "生成 Elasticsearch 配置文件"
    fi
    
    # Kibana 配置
    if [[ ! -f "$CONFIG_DIR/kibana/kibana.yml" ]]; then
        cat > "$CONFIG_DIR/kibana/kibana.yml" << EOF
server.name: paas-kibana
server.host: 0.0.0.0
server.port: 5601
elasticsearch.hosts: ["http://elasticsearch:9200"]
xpack.security.enabled: false
xpack.monitoring.enabled: true
logging.root.level: warn
EOF
        print_info "生成 Kibana 配置文件"
    fi
    
    print_success "配置文件检查完成"
}

# 函数：检查端口占用
check_ports() {
    print_info "检查端口占用情况..."
    
    local ports=(
        "9200:Elasticsearch"
        "5601:Kibana"
        "5044:Logstash Beats"
        "9600:Logstash API"
        "8200:APM Server"
        "3100:Loki"
    )
    
    local occupied_ports=()
    
    for port_info in "${ports[@]}"; do
        local port="${port_info%%:*}"
        local service="${port_info##*:}"
        
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            occupied_ports+=("$port ($service)")
        fi
    done
    
    if [[ ${#occupied_ports[@]} -gt 0 ]]; then
        print_warning "以下端口已被占用: ${occupied_ports[*]}"
        print_info "如果这些端口被其他服务占用，日志系统可能无法正常启动"
        
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "启动已取消"
            exit 0
        fi
    else
        print_success "端口检查通过"
    fi
}

# 函数：启动日志服务
start_logging_services() {
    print_info "启动日志聚合服务..."
    
    # 拉取最新镜像
    print_info "拉取 Docker 镜像..."
    docker-compose -f "$COMPOSE_FILE" pull
    
    # 分阶段启动服务
    print_info "启动 Elasticsearch..."
    docker-compose -f "$COMPOSE_FILE" up -d elasticsearch
    
    # 等待 Elasticsearch 启动
    print_info "等待 Elasticsearch 就绪..."
    local max_attempts=60
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if curl -s -f "http://localhost:9200/_cluster/health" > /dev/null 2>&1; then
            print_success "Elasticsearch 已就绪"
            break
        fi
        
        attempt=$((attempt + 1))
        if [[ $attempt -eq $max_attempts ]]; then
            print_error "Elasticsearch 启动超时"
            exit 1
        fi
        
        sleep 5
    done
    
    # 启动其他服务
    print_info "启动其他日志服务..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    print_success "日志聚合服务启动完成"
}

# 函数：等待服务就绪
wait_for_services() {
    print_info "等待所有服务就绪..."
    
    local services=(
        "http://localhost:9200/_cluster/health:Elasticsearch"
        "http://localhost:5601/api/status:Kibana"
        "http://localhost:9600:Logstash"
    )
    
    for service_info in "${services[@]}"; do
        local url="${service_info%%:*}"
        local service="${service_info##*:}"
        
        print_info "等待 $service 服务就绪..."
        
        local max_attempts=30
        local attempt=0
        
        while [[ $attempt -lt $max_attempts ]]; do
            if curl -s -f "$url" > /dev/null 2>&1; then
                print_success "$service 服务已就绪"
                break
            fi
            
            attempt=$((attempt + 1))
            if [[ $attempt -eq $max_attempts ]]; then
                print_warning "$service 服务启动超时，但继续执行"
                break
            fi
            
            sleep 5
        done
    done
}

# 函数：创建 Kibana 索引模式
setup_kibana_index_patterns() {
    print_info "设置 Kibana 索引模式..."
    
    # 等待 Kibana 完全启动
    sleep 30
    
    # 创建索引模式
    local index_patterns=(
        "paas-app-logs-*"
        "paas-error-logs-*"
        "paas-access-logs-*"
        "paas-system-logs-*"
    )
    
    for pattern in "${index_patterns[@]}"; do
        curl -X POST "http://localhost:5601/api/saved_objects/index-pattern/$pattern" \
             -H "Content-Type: application/json" \
             -H "kbn-xsrf: true" \
             -d "{
               \"attributes\": {
                 \"title\": \"$pattern\",
                 \"timeFieldName\": \"@timestamp\"
               }
             }" > /dev/null 2>&1
        
        print_info "创建索引模式: $pattern"
    done
    
    print_success "Kibana 索引模式设置完成"
}

# 函数：显示访问信息
show_access_info() {
    echo ""
    echo "=================================================="
    print_success "🎉 PaaS 平台日志聚合系统启动完成！"
    echo "=================================================="
    echo ""
    echo "📊 日志服务访问地址："
    echo "  • Elasticsearch: http://localhost:9200"
    echo "  • Kibana:        http://localhost:5601"
    echo "  • Logstash API:  http://localhost:9600"
    echo "  • APM Server:    http://localhost:8200"
    echo "  • Loki:          http://localhost:3100"
    echo ""
    echo "🔍 常用 API 端点："
    echo "  • 集群健康:      http://localhost:9200/_cluster/health"
    echo "  • 索引列表:      http://localhost:9200/_cat/indices?v"
    echo "  • Kibana 状态:   http://localhost:5601/api/status"
    echo "  • Logstash 状态: http://localhost:9600/_node/stats"
    echo ""
    echo "🔧 管理命令："
    echo "  • 查看状态: docker-compose -f $COMPOSE_FILE ps"
    echo "  • 查看日志: docker-compose -f $COMPOSE_FILE logs -f [service]"
    echo "  • 停止服务: docker-compose -f $COMPOSE_FILE down"
    echo "  • 重启服务: docker-compose -f $COMPOSE_FILE restart [service]"
    echo ""
    echo "📝 下一步："
    echo "  1. 访问 Kibana 创建仪表板"
    echo "  2. 配置日志收集规则"
    echo "  3. 设置告警和通知"
    echo "  4. 验证日志数据流"
    echo ""
}

# 函数：清理函数
cleanup() {
    if [[ $? -ne 0 ]]; then
        print_error "启动过程中出现错误"
        print_info "正在清理..."
        docker-compose -f "$COMPOSE_FILE" down 2>/dev/null || true
    fi
}

# 主函数
main() {
    # 设置错误处理
    trap cleanup EXIT
    
    # 检查参数
    case "${1:-}" in
        "stop")
            print_info "停止日志聚合服务..."
            docker-compose -f "$COMPOSE_FILE" down
            print_success "日志聚合服务已停止"
            exit 0
            ;;
        "restart")
            print_info "重启日志聚合服务..."
            docker-compose -f "$COMPOSE_FILE" down
            sleep 5
            ;;
        "status")
            print_info "日志聚合服务状态："
            docker-compose -f "$COMPOSE_FILE" ps
            exit 0
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f "${2:-}"
            exit 0
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令："
            echo "  start     启动日志聚合服务 (默认)"
            echo "  stop      停止日志聚合服务"
            echo "  restart   重启日志聚合服务"
            echo "  status    查看服务状态"
            echo "  logs      查看服务日志"
            echo "  help      显示帮助信息"
            exit 0
            ;;
    esac
    
    # 执行启动流程
    check_system_requirements
    check_dependencies
    setup_directories
    generate_missing_configs
    check_ports
    start_logging_services
    wait_for_services
    setup_kibana_index_patterns
    show_access_info
    
    # 移除错误处理
    trap - EXIT
}

# 脚本入口
main "$@"
