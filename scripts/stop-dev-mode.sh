#!/bin/bash

# PaaS平台开发环境停止脚本
# 此脚本用于停止所有开发环境中运行的服务

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止单个服务
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止 ${service_name} (PID: $pid)..."
            kill -TERM "$pid" 2>/dev/null || true
            
            # 等待进程优雅退出
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制终止
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "强制终止 ${service_name}..."
                kill -KILL "$pid" 2>/dev/null || true
            fi
            
            log_info "${service_name} 已停止"
        else
            log_warn "${service_name} 进程不存在 (PID: $pid)"
        fi
        
        # 删除PID文件
        rm -f "$pid_file"
    else
        log_warn "${service_name} PID文件不存在"
    fi
}

# 通过端口停止服务
stop_service_by_port() {
    local port=$1
    local service_name=$2
    
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        log_info "停止占用端口 $port 的 ${service_name} 进程..."
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        
        # 等待进程退出
        sleep 2
        
        # 检查是否还有进程占用端口
        local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            log_warn "强制终止占用端口 $port 的进程..."
            echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
        fi
        
        log_info "端口 $port 已释放"
    else
        log_info "端口 $port 未被占用"
    fi
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有开发环境服务..."
    
    # 通过PID文件停止服务
    stop_service "app-manager"
    stop_service "script-service"
    stop_service "cicd-service"
    stop_service "config-service"
    
    # 通过端口停止服务 (备用方法)
    stop_service_by_port 8081 "app-manager"
    stop_service_by_port 8082 "cicd-service"
    stop_service_by_port 8083 "config-service"
    stop_service_by_port 8084 "script-service"
    
    # 停止可能的pprof端口
    stop_service_by_port 6061 "app-manager-pprof"
    stop_service_by_port 6062 "cicd-service-pprof"
    stop_service_by_port 6063 "config-service-pprof"
    stop_service_by_port 6064 "script-service-pprof"
    
    log_info "所有服务已停止"
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理PID文件
    rm -f logs/*.pid
    
    # 清理可能的锁文件
    rm -f data/*.lock
    
    # 清理临时工作空间 (可选)
    if [ "$1" = "--clean-workspace" ]; then
        log_warn "清理工作空间数据..."
        rm -rf data/workspaces/*
        rm -rf data/artifacts/*
    fi
    
    log_info "临时文件清理完成"
}

# 显示停止状态
show_stop_status() {
    echo
    log_info "服务停止状态："
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                      服务停止状态                          ║${NC}"
    echo -e "${BLUE}╠════════════════════════════════════════════════════════════╣${NC}"
    
    # 检查各服务端口状态
    local ports=(8081 8082 8083 8084)
    local services=("应用管理服务" "CI/CD服务" "配置服务" "脚本执行服务")
    
    for i in "${!ports[@]}"; do
        local port=${ports[$i]}
        local service=${services[$i]}
        
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo -e "${BLUE}║  ❌ ${service}: 端口 $port 仍被占用                    ║${NC}"
        else
            echo -e "${BLUE}║  ✅ ${service}: 端口 $port 已释放                      ║${NC}"
        fi
    done
    
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --clean-workspace    同时清理工作空间数据"
    echo "  --help              显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0                   # 停止所有服务"
    echo "  $0 --clean-workspace # 停止服务并清理工作空间"
    echo
}

# 主函数
main() {
    echo -e "${GREEN}🛑 停止PaaS平台开发环境${NC}"
    echo
    
    # 解析命令行参数
    local clean_workspace=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean-workspace)
                clean_workspace=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 停止所有服务
    stop_all_services
    
    # 清理临时文件
    if [ "$clean_workspace" = true ]; then
        cleanup_temp_files --clean-workspace
    else
        cleanup_temp_files
    fi
    
    # 显示停止状态
    show_stop_status
    
    log_info "开发环境已停止"
    echo
    echo -e "${YELLOW}💡 提示：${NC}"
    echo -e "${YELLOW}• 重新启动开发环境: ./scripts/start-dev-mode.sh${NC}"
    echo -e "${YELLOW}• 查看日志文件: ls -la logs/${NC}"
    echo -e "${YELLOW}• 清理所有数据: rm -rf data/ logs/${NC}"
    echo
}

# 执行主函数
main "$@"
