#!/bin/bash

# Docker镜像存储管理系统部署脚本
# 一键部署完整的Docker镜像存储空间管理解决方案

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 配置变量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly LOG_FILE="$PROJECT_ROOT/logs/deployment-$(date +%Y%m%d-%H%M%S).log"

# 部署选项
DEPLOY_CLEANUP=true
DEPLOY_OPTIMIZATION=true
DEPLOY_STORAGE_CONFIG=true
DEPLOY_MONITORING=true
DEPLOY_REGISTRY_LIFECYCLE=true
SETUP_CRON=true
RESTART_SERVICES=false
DRY_RUN=false

# 日志函数
log_info() {
    local msg="$1"
    echo -e "${BLUE}[INFO]${NC} $msg" | tee -a "$LOG_FILE"
}

log_success() {
    local msg="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $msg" | tee -a "$LOG_FILE"
}

log_warning() {
    local msg="$1"
    echo -e "${YELLOW}[WARNING]${NC} $msg" | tee -a "$LOG_FILE"
}

log_error() {
    local msg="$1"
    echo -e "${RED}[ERROR]${NC} $msg" | tee -a "$LOG_FILE"
}

print_title() {
    local title="$1"
    echo -e "\n${CYAN}🚀 $title${NC}\n" | tee -a "$LOG_FILE"
}

print_banner() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Docker镜像存储管理系统部署工具                              ║
║                                                                              ║
║  🐳 智能镜像清理    📊 存储监控告警    🏪 仓库生命周期管理                      ║
║  🚀 镜像优化技术    ⚙️ 存储配置管理    📈 Grafana仪表板                       ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
}

# 检查系统要求
check_system_requirements() {
    print_title "检查系统要求"
    
    local requirements_met=true
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_warning "建议在Linux系统上运行"
    fi
    
    # 检查权限
    if [[ $EUID -ne 0 ]]; then
        log_warning "某些功能需要root权限，建议使用sudo运行"
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        requirements_met=false
    else
        local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_info "Docker版本: $docker_version"
        
        if ! docker info >/dev/null 2>&1; then
            log_error "Docker未运行"
            requirements_met=false
        fi
    fi
    
    # 检查docker-compose
    if ! command -v docker-compose &> /dev/null; then
        log_warning "docker-compose未安装，某些功能可能不可用"
    fi
    
    # 检查必要工具
    local tools=("curl" "jq" "bc" "crontab")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_warning "$tool 未安装，某些功能可能不可用"
        fi
    done
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    local available_gb=$((available_space / 1024 / 1024))
    
    if [[ $available_gb -lt 5 ]]; then
        log_warning "可用磁盘空间不足5GB，建议清理磁盘空间"
    else
        log_info "可用磁盘空间: ${available_gb}GB"
    fi
    
    if [[ "$requirements_met" == "false" ]]; then
        log_error "系统要求检查失败，请解决上述问题后重试"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 初始化环境
init_environment() {
    print_title "初始化环境"
    
    # 创建必要目录
    local directories=(
        "$PROJECT_ROOT/logs"
        "$PROJECT_ROOT/reports"
        "$PROJECT_ROOT/data"
        "$PROJECT_ROOT/backups"
        "$PROJECT_ROOT/tmp"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    # 设置脚本权限
    local scripts=(
        "$SCRIPT_DIR/docker-image-cleanup.sh"
        "$SCRIPT_DIR/docker-image-optimizer.sh"
        "$SCRIPT_DIR/docker-image-analyzer.sh"
        "$SCRIPT_DIR/docker-storage-manager.sh"
        "$SCRIPT_DIR/docker-metrics-exporter.sh"
        "$SCRIPT_DIR/registry-lifecycle-manager.sh"
        "$SCRIPT_DIR/setup-docker-cleanup-cron.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            chmod +x "$script"
            log_info "设置脚本权限: $(basename "$script")"
        fi
    done
    
    log_success "环境初始化完成"
}

# 部署镜像清理系统
deploy_cleanup_system() {
    if [[ "$DEPLOY_CLEANUP" != "true" ]]; then
        return 0
    fi
    
    print_title "部署镜像清理系统"
    
    # 测试清理脚本
    if [[ -x "$SCRIPT_DIR/docker-image-cleanup.sh" ]]; then
        log_info "测试镜像清理脚本..."
        if "$SCRIPT_DIR/docker-image-cleanup.sh" --dry-run --help >/dev/null 2>&1; then
            log_success "镜像清理脚本测试通过"
        else
            log_error "镜像清理脚本测试失败"
            return 1
        fi
    else
        log_error "镜像清理脚本不存在"
        return 1
    fi
    
    # 生成默认配置
    if [[ ! -f "$PROJECT_ROOT/configs/docker-cleanup.yaml" ]]; then
        log_info "生成默认清理配置..."
        "$SCRIPT_DIR/docker-image-cleanup.sh" --generate-config
    fi
    
    log_success "镜像清理系统部署完成"
}

# 部署镜像优化系统
deploy_optimization_system() {
    if [[ "$DEPLOY_OPTIMIZATION" != "true" ]]; then
        return 0
    fi
    
    print_title "部署镜像优化系统"
    
    # 运行镜像优化分析
    if [[ -x "$SCRIPT_DIR/docker-image-optimizer.sh" ]]; then
        log_info "运行镜像优化分析..."
        "$SCRIPT_DIR/docker-image-optimizer.sh" analyze
        
        if [[ "$DRY_RUN" == "false" ]]; then
            log_info "应用镜像优化..."
            "$SCRIPT_DIR/docker-image-optimizer.sh" optimize
        else
            log_info "[试运行] 跳过镜像优化应用"
        fi
    else
        log_error "镜像优化脚本不存在"
        return 1
    fi
    
    log_success "镜像优化系统部署完成"
}

# 部署存储配置管理
deploy_storage_config() {
    if [[ "$DEPLOY_STORAGE_CONFIG" != "true" ]]; then
        return 0
    fi
    
    print_title "部署存储配置管理"
    
    # 分析当前存储配置
    if [[ -x "$SCRIPT_DIR/docker-storage-manager.sh" ]]; then
        log_info "分析当前存储配置..."
        "$SCRIPT_DIR/docker-storage-manager.sh" --analyze
        
        if [[ "$DRY_RUN" == "false" ]] && [[ $EUID -eq 0 ]]; then
            log_info "应用存储配置..."
            "$SCRIPT_DIR/docker-storage-manager.sh" --configure
        else
            log_info "[试运行或权限不足] 跳过存储配置应用"
        fi
    else
        log_error "存储管理脚本不存在"
        return 1
    fi
    
    log_success "存储配置管理部署完成"
}

# 部署监控系统
deploy_monitoring_system() {
    if [[ "$DEPLOY_MONITORING" != "true" ]]; then
        return 0
    fi
    
    print_title "部署监控系统"
    
    # 启动指标导出器
    if [[ -x "$SCRIPT_DIR/docker-metrics-exporter.sh" ]]; then
        log_info "启动Docker指标导出器..."
        
        # 检查是否已经在运行
        if pgrep -f "docker-metrics-exporter.sh" >/dev/null; then
            log_info "指标导出器已在运行"
        else
            if [[ "$DRY_RUN" == "false" ]]; then
                nohup "$SCRIPT_DIR/docker-metrics-exporter.sh" --server > "$PROJECT_ROOT/logs/metrics-exporter.log" 2>&1 &
                log_success "指标导出器已启动"
            else
                log_info "[试运行] 跳过指标导出器启动"
            fi
        fi
    fi
    
    # 更新Prometheus配置
    if [[ -f "$PROJECT_ROOT/configs/prometheus.yml" ]]; then
        log_info "Prometheus配置已更新"
    else
        log_warning "Prometheus配置文件不存在"
    fi
    
    # 部署Grafana仪表板
    if [[ -f "$PROJECT_ROOT/monitoring/grafana/dashboards/docker-storage-dashboard.json" ]]; then
        log_info "Grafana仪表板配置已准备"
    else
        log_warning "Grafana仪表板配置不存在"
    fi
    
    log_success "监控系统部署完成"
}

# 部署仓库生命周期管理
deploy_registry_lifecycle() {
    if [[ "$DEPLOY_REGISTRY_LIFECYCLE" != "true" ]]; then
        return 0
    fi
    
    print_title "部署仓库生命周期管理"
    
    # 测试仓库连接
    if [[ -x "$SCRIPT_DIR/registry-lifecycle-manager.sh" ]]; then
        log_info "测试仓库生命周期管理..."
        
        # 检查是否有本地仓库运行
        if docker ps --format "{{.Names}}" | grep -q registry; then
            log_info "检测到本地Docker仓库"
            
            if [[ "$DRY_RUN" == "false" ]]; then
                "$SCRIPT_DIR/registry-lifecycle-manager.sh" --dry-run --report
            else
                log_info "[试运行] 跳过仓库生命周期管理测试"
            fi
        else
            log_info "未检测到本地Docker仓库，跳过测试"
        fi
    else
        log_error "仓库生命周期管理脚本不存在"
        return 1
    fi
    
    log_success "仓库生命周期管理部署完成"
}

# 设置定时任务
setup_cron_jobs() {
    if [[ "$SETUP_CRON" != "true" ]]; then
        return 0
    fi
    
    print_title "设置定时任务"
    
    if [[ -x "$SCRIPT_DIR/setup-docker-cleanup-cron.sh" ]]; then
        if [[ "$DRY_RUN" == "false" ]] && [[ $EUID -eq 0 ]]; then
            log_info "设置Docker清理定时任务..."
            "$SCRIPT_DIR/setup-docker-cleanup-cron.sh"
        else
            log_info "[试运行或权限不足] 跳过定时任务设置"
        fi
    else
        log_error "定时任务设置脚本不存在"
        return 1
    fi
    
    log_success "定时任务设置完成"
}

# 重启相关服务
restart_services() {
    if [[ "$RESTART_SERVICES" != "true" ]]; then
        return 0
    fi
    
    print_title "重启相关服务"
    
    if [[ "$DRY_RUN" == "false" ]] && [[ $EUID -eq 0 ]]; then
        # 重启Docker服务（如果配置有更改）
        if [[ -f "/etc/docker/daemon.json.backup"* ]]; then
            log_info "重启Docker服务..."
            systemctl restart docker
            sleep 10
            
            if systemctl is-active --quiet docker; then
                log_success "Docker服务重启成功"
            else
                log_error "Docker服务重启失败"
                return 1
            fi
        fi
        
        # 重启Prometheus（如果在运行）
        if docker ps --format "{{.Names}}" | grep -q prometheus; then
            log_info "重启Prometheus服务..."
            docker restart prometheus || log_warning "Prometheus重启失败"
        fi
        
        # 重启Grafana（如果在运行）
        if docker ps --format "{{.Names}}" | grep -q grafana; then
            log_info "重启Grafana服务..."
            docker restart grafana || log_warning "Grafana重启失败"
        fi
    else
        log_info "[试运行或权限不足] 跳过服务重启"
    fi
    
    log_success "服务重启完成"
}

# 验证部署
verify_deployment() {
    print_title "验证部署"
    
    local verification_passed=true
    
    # 检查脚本可执行性
    local scripts=(
        "docker-image-cleanup.sh"
        "docker-image-optimizer.sh"
        "docker-storage-manager.sh"
        "registry-lifecycle-manager.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -x "$SCRIPT_DIR/$script" ]]; then
            log_success "✓ $script 可执行"
        else
            log_error "✗ $script 不可执行"
            verification_passed=false
        fi
    done
    
    # 检查配置文件
    local configs=(
        "configs/docker-cleanup.yaml"
        "configs/docker-daemon.json"
        "configs/registry-lifecycle.yaml"
    )
    
    for config in "${configs[@]}"; do
        if [[ -f "$PROJECT_ROOT/$config" ]]; then
            log_success "✓ $config 存在"
        else
            log_warning "⚠ $config 不存在"
        fi
    done
    
    # 检查Docker状态
    if docker info >/dev/null 2>&1; then
        log_success "✓ Docker运行正常"
    else
        log_error "✗ Docker未运行"
        verification_passed=false
    fi
    
    # 检查指标导出器
    if pgrep -f "docker-metrics-exporter.sh" >/dev/null; then
        log_success "✓ 指标导出器运行中"
    else
        log_warning "⚠ 指标导出器未运行"
    fi
    
    # 检查定时任务
    if [[ $EUID -eq 0 ]] && crontab -l 2>/dev/null | grep -q "docker-cleanup"; then
        log_success "✓ 定时任务已设置"
    else
        log_warning "⚠ 定时任务未设置或权限不足"
    fi
    
    if [[ "$verification_passed" == "true" ]]; then
        log_success "部署验证通过"
    else
        log_warning "部署验证发现问题，请检查上述错误"
    fi
}

# 生成部署报告
generate_deployment_report() {
    print_title "生成部署报告"
    
    local report_file="$PROJECT_ROOT/reports/deployment-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << EOF
# Docker镜像存储管理系统部署报告

**部署时间:** $(date)
**部署模式:** $(if [[ "$DRY_RUN" == "true" ]]; then echo "试运行"; else echo "正式部署"; fi)

## 部署组件

- [$(if [[ "$DEPLOY_CLEANUP" == "true" ]]; then echo "x"; else echo " "; fi)] 镜像清理系统
- [$(if [[ "$DEPLOY_OPTIMIZATION" == "true" ]]; then echo "x"; else echo " "; fi)] 镜像优化系统
- [$(if [[ "$DEPLOY_STORAGE_CONFIG" == "true" ]]; then echo "x"; else echo " "; fi)] 存储配置管理
- [$(if [[ "$DEPLOY_MONITORING" == "true" ]]; then echo "x"; else echo " "; fi)] 监控系统
- [$(if [[ "$DEPLOY_REGISTRY_LIFECYCLE" == "true" ]]; then echo "x"; else echo " "; fi)] 仓库生命周期管理
- [$(if [[ "$SETUP_CRON" == "true" ]]; then echo "x"; else echo " "; fi)] 定时任务

## 系统信息

- **操作系统:** $(uname -s) $(uname -r)
- **Docker版本:** $(docker --version 2>/dev/null || echo "未安装")
- **可用磁盘空间:** $(df -h "$PROJECT_ROOT" | awk 'NR==2 {print $4}')

## 部署文件

### 脚本文件
$(find "$SCRIPT_DIR" -name "*.sh" -type f | while read -r file; do echo "- $(basename "$file")"; done)

### 配置文件
$(find "$PROJECT_ROOT/configs" -name "*.yaml" -o -name "*.yml" -o -name "*.json" 2>/dev/null | while read -r file; do echo "- ${file#$PROJECT_ROOT/}"; done)

### 监控文件
$(find "$PROJECT_ROOT/monitoring" -type f 2>/dev/null | while read -r file; do echo "- ${file#$PROJECT_ROOT/}"; done)

## 使用说明

### 手动执行清理
\`\`\`bash
# 试运行模式查看将要清理的内容
./scripts/docker-image-cleanup.sh --dry-run

# 执行清理
./scripts/docker-image-cleanup.sh

# 清理特定类型
./scripts/docker-image-cleanup.sh dangling
\`\`\`

### 镜像优化
\`\`\`bash
# 分析现有镜像
./scripts/docker-image-optimizer.sh analyze

# 应用优化
./scripts/docker-image-optimizer.sh optimize
\`\`\`

### 仓库生命周期管理
\`\`\`bash
# 生成清理报告
./scripts/registry-lifecycle-manager.sh --report

# 清理仓库
./scripts/registry-lifecycle-manager.sh --dry-run
\`\`\`

## 监控访问

- **Prometheus:** http://localhost:9090
- **Grafana:** http://localhost:3000
- **Docker指标:** http://localhost:9100/metrics/docker

## 注意事项

1. 定期检查清理日志: \`$PROJECT_ROOT/logs/\`
2. 监控磁盘使用情况
3. 根据实际需求调整清理策略
4. 定期更新配置文件

## 故障排除

如遇问题，请检查：
1. Docker服务状态: \`systemctl status docker\`
2. 脚本权限: \`ls -la scripts/\`
3. 配置文件语法: \`yamllint configs/\`
4. 日志文件: \`tail -f logs/\*.log\`

---
*报告生成时间: $(date)*
EOF
    
    log_success "部署报告已生成: $report_file"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker镜像存储管理系统部署脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -d, --dry-run           试运行模式，不执行实际操作
  --no-cleanup            跳过镜像清理系统部署
  --no-optimization       跳过镜像优化系统部署
  --no-storage-config     跳过存储配置管理部署
  --no-monitoring         跳过监控系统部署
  --no-registry           跳过仓库生命周期管理部署
  --no-cron               跳过定时任务设置
  --restart-services      重启相关服务
  --verify-only           仅执行部署验证

示例:
  $0                      # 完整部署
  $0 --dry-run            # 试运行模式
  $0 --no-cron            # 跳过定时任务设置
  $0 --verify-only        # 仅验证部署

EOF
}

# 主函数
main() {
    local verify_only=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            --no-cleanup)
                DEPLOY_CLEANUP=false
                shift
                ;;
            --no-optimization)
                DEPLOY_OPTIMIZATION=false
                shift
                ;;
            --no-storage-config)
                DEPLOY_STORAGE_CONFIG=false
                shift
                ;;
            --no-monitoring)
                DEPLOY_MONITORING=false
                shift
                ;;
            --no-registry)
                DEPLOY_REGISTRY_LIFECYCLE=false
                shift
                ;;
            --no-cron)
                SETUP_CRON=false
                shift
                ;;
            --restart-services)
                RESTART_SERVICES=true
                shift
                ;;
            --verify-only)
                verify_only=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示横幅
    print_banner
    
    # 初始化日志
    mkdir -p "$(dirname "$LOG_FILE")"
    log_info "Docker镜像存储管理系统部署开始"
    log_info "部署模式: $(if [[ "$DRY_RUN" == "true" ]]; then echo "试运行"; else echo "正式部署"; fi)"
    
    # 仅验证模式
    if [[ "$verify_only" == "true" ]]; then
        verify_deployment
        exit 0
    fi
    
    # 执行部署步骤
    check_system_requirements
    init_environment
    deploy_cleanup_system
    deploy_optimization_system
    deploy_storage_config
    deploy_monitoring_system
    deploy_registry_lifecycle
    setup_cron_jobs
    restart_services
    verify_deployment
    generate_deployment_report
    
    # 显示完成信息
    print_title "部署完成"
    
    log_success "🎉 Docker镜像存储管理系统部署完成！"
    echo ""
    log_info "📋 部署报告: $PROJECT_ROOT/reports/"
    log_info "📊 监控面板: http://localhost:3000 (Grafana)"
    log_info "📈 指标数据: http://localhost:9090 (Prometheus)"
    log_info "🔧 配置文件: $PROJECT_ROOT/configs/"
    log_info "📝 日志文件: $PROJECT_ROOT/logs/"
    echo ""
    log_info "💡 使用 './scripts/docker-image-cleanup.sh --help' 查看清理选项"
    log_info "💡 使用 './scripts/registry-lifecycle-manager.sh --help' 查看仓库管理选项"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo ""
        log_warning "⚠️  这是试运行模式，实际部署请移除 --dry-run 参数"
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
