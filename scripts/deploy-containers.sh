#!/bin/bash

# PaaS 平台容器化部署管理脚本
# 支持 Docker Compose 和 Kubernetes 部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENTS_DIR="$PROJECT_ROOT/deployments"
DOCKER_DIR="$DEPLOYMENTS_DIR/docker"
K8S_DIR="$DEPLOYMENTS_DIR/kubernetes"

# 默认配置
DEFAULT_PLATFORM="docker"
DEFAULT_ENVIRONMENT="development"
DEFAULT_VERSION="latest"

echo -e "${PURPLE}🚀 PaaS 平台容器化部署管理脚本${NC}"
echo "=================================================="

# 函数：打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告消息
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：打印信息消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 函数：检查依赖
check_dependencies() {
    local platform=$1
    
    print_info "检查 $platform 部署依赖..."
    
    local missing_deps=()
    
    case $platform in
        "docker")
            if ! command -v docker &> /dev/null; then
                missing_deps+=("docker")
            fi
            if ! command -v docker-compose &> /dev/null; then
                missing_deps+=("docker-compose")
            fi
            ;;
        "kubernetes"|"k8s")
            if ! command -v kubectl &> /dev/null; then
                missing_deps+=("kubectl")
            fi
            if ! command -v helm &> /dev/null; then
                missing_deps+=("helm")
            fi
            ;;
    esac
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        print_info "请安装缺少的依赖后重试"
        exit 1
    fi
    
    print_success "$platform 依赖检查完成"
}

# 函数：构建 Docker 镜像
build_docker_images() {
    local version=${1:-$DEFAULT_VERSION}
    local build_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    print_title "构建 Docker 镜像"
    
    cd "$PROJECT_ROOT"
    
    # 服务列表
    local services=(
        "api-gateway"
        "user-service"
        "app-manager"
        "cicd-service"
        "monitor-service"
        "notification-service"
        "loadbalancer-service"
    )
    
    for service in "${services[@]}"; do
        print_info "构建 $service 镜像..."
        
        docker build \
            -f "$DOCKER_DIR/Dockerfile.multi-stage" \
            --build-arg SERVICE_NAME="$service" \
            --build-arg VERSION="$version" \
            --build-arg BUILD_TIME="$build_time" \
            --build-arg GIT_COMMIT="$git_commit" \
            -t "paas-platform/$service:$version" \
            -t "paas-platform/$service:latest" \
            .
        
        print_success "$service 镜像构建完成"
    done
    
    print_success "所有 Docker 镜像构建完成"
}

# 函数：Docker Compose 部署
deploy_docker_compose() {
    local environment=${1:-$DEFAULT_ENVIRONMENT}
    local version=${2:-$DEFAULT_VERSION}
    
    print_title "Docker Compose 部署 ($environment)"
    
    cd "$DOCKER_DIR"
    
    # 选择配置文件
    local compose_file="docker-compose.yml"
    if [[ "$environment" == "production" ]]; then
        compose_file="docker-compose.production.yml"
    fi
    
    if [[ ! -f "$compose_file" ]]; then
        print_error "Docker Compose 配置文件不存在: $compose_file"
        exit 1
    fi
    
    # 设置环境变量
    export VERSION="$version"
    export BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    export GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    export COMPOSE_PROJECT_NAME="paas-platform"
    
    # 部署服务
    print_info "启动 Docker Compose 服务..."
    docker-compose -f "$compose_file" up -d
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    print_info "检查服务状态..."
    docker-compose -f "$compose_file" ps
    
    print_success "Docker Compose 部署完成"
}

# 函数：Kubernetes 部署
deploy_kubernetes() {
    local environment=${1:-$DEFAULT_ENVIRONMENT}
    local version=${2:-$DEFAULT_VERSION}
    
    print_title "Kubernetes 部署 ($environment)"
    
    cd "$K8S_DIR"
    
    # 检查 Kubernetes 连接
    if ! kubectl cluster-info &> /dev/null; then
        print_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    # 创建命名空间
    print_info "创建命名空间..."
    kubectl apply -f namespace.yaml
    
    # 部署基础设施组件
    print_info "部署基础设施组件..."
    if [[ -f "infrastructure.yaml" ]]; then
        kubectl apply -f infrastructure.yaml
    fi
    
    # 部署配置
    print_info "部署配置..."
    if [[ -f "configmap.yaml" ]]; then
        kubectl apply -f configmap.yaml
    fi
    if [[ -f "secret.yaml" ]]; then
        kubectl apply -f secret.yaml
    fi
    
    # 部署服务
    print_info "部署服务..."
    local services=(
        "postgres"
        "redis"
        "user-service"
        "app-manager"
        "cicd-service"
        "monitor-service"
        "notification-service"
        "loadbalancer-service"
        "api-gateway"
    )
    
    for service in "${services[@]}"; do
        local service_file="${service}.yaml"
        if [[ -f "$service_file" ]]; then
            print_info "部署 $service..."
            kubectl apply -f "$service_file"
        else
            print_warning "服务配置文件不存在: $service_file"
        fi
    done
    
    # 等待部署完成
    print_info "等待部署完成..."
    kubectl wait --for=condition=available --timeout=300s deployment --all -n paas-platform
    
    # 检查部署状态
    print_info "检查部署状态..."
    kubectl get pods -n paas-platform
    kubectl get services -n paas-platform
    
    print_success "Kubernetes 部署完成"
}

# 函数：停止服务
stop_services() {
    local platform=$1
    local environment=${2:-$DEFAULT_ENVIRONMENT}
    
    print_title "停止 $platform 服务"
    
    case $platform in
        "docker")
            cd "$DOCKER_DIR"
            local compose_file="docker-compose.yml"
            if [[ "$environment" == "production" ]]; then
                compose_file="docker-compose.production.yml"
            fi
            
            if [[ -f "$compose_file" ]]; then
                docker-compose -f "$compose_file" down
                print_success "Docker Compose 服务已停止"
            else
                print_error "Docker Compose 配置文件不存在: $compose_file"
            fi
            ;;
        "kubernetes"|"k8s")
            kubectl delete namespace paas-platform --ignore-not-found=true
            print_success "Kubernetes 服务已停止"
            ;;
    esac
}

# 函数：查看服务状态
show_status() {
    local platform=$1
    local environment=${2:-$DEFAULT_ENVIRONMENT}
    
    print_title "$platform 服务状态"
    
    case $platform in
        "docker")
            cd "$DOCKER_DIR"
            local compose_file="docker-compose.yml"
            if [[ "$environment" == "production" ]]; then
                compose_file="docker-compose.production.yml"
            fi
            
            if [[ -f "$compose_file" ]]; then
                docker-compose -f "$compose_file" ps
                echo ""
                docker-compose -f "$compose_file" logs --tail=10
            else
                print_error "Docker Compose 配置文件不存在: $compose_file"
            fi
            ;;
        "kubernetes"|"k8s")
            echo "Pods:"
            kubectl get pods -n paas-platform
            echo ""
            echo "Services:"
            kubectl get services -n paas-platform
            echo ""
            echo "Ingresses:"
            kubectl get ingresses -n paas-platform
            ;;
    esac
}

# 函数：清理资源
cleanup() {
    local platform=$1
    
    print_title "清理 $platform 资源"
    
    case $platform in
        "docker")
            print_info "清理 Docker 资源..."
            docker system prune -f
            docker volume prune -f
            print_success "Docker 资源清理完成"
            ;;
        "kubernetes"|"k8s")
            print_info "清理 Kubernetes 资源..."
            kubectl delete namespace paas-platform --ignore-not-found=true
            print_success "Kubernetes 资源清理完成"
            ;;
    esac
}

# 函数：显示帮助信息
show_help() {
    echo "PaaS 平台容器化部署管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  build [version]                构建 Docker 镜像"
    echo "  deploy <platform> [env] [ver]  部署服务"
    echo "  stop <platform> [env]          停止服务"
    echo "  status <platform> [env]        查看服务状态"
    echo "  cleanup <platform>             清理资源"
    echo "  help                           显示帮助信息"
    echo ""
    echo "平台选项:"
    echo "  docker                         Docker Compose 部署"
    echo "  kubernetes, k8s                Kubernetes 部署"
    echo ""
    echo "环境选项:"
    echo "  development                    开发环境 (默认)"
    echo "  production                     生产环境"
    echo ""
    echo "示例:"
    echo "  $0 build v1.0.0                # 构建版本 v1.0.0 的镜像"
    echo "  $0 deploy docker development   # Docker 开发环境部署"
    echo "  $0 deploy k8s production       # Kubernetes 生产环境部署"
    echo "  $0 status docker               # 查看 Docker 服务状态"
    echo "  $0 stop k8s                    # 停止 Kubernetes 服务"
    echo "  $0 cleanup docker              # 清理 Docker 资源"
}

# 主函数
main() {
    local command=${1:-"help"}
    local platform=${2:-$DEFAULT_PLATFORM}
    local environment=${3:-$DEFAULT_ENVIRONMENT}
    local version=${4:-$DEFAULT_VERSION}
    
    case $command in
        "build")
            version=${2:-$DEFAULT_VERSION}
            check_dependencies "docker"
            build_docker_images "$version"
            ;;
        "deploy")
            check_dependencies "$platform"
            case $platform in
                "docker")
                    build_docker_images "$version"
                    deploy_docker_compose "$environment" "$version"
                    ;;
                "kubernetes"|"k8s")
                    deploy_kubernetes "$environment" "$version"
                    ;;
                *)
                    print_error "不支持的平台: $platform"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        "stop")
            check_dependencies "$platform"
            stop_services "$platform" "$environment"
            ;;
        "status")
            check_dependencies "$platform"
            show_status "$platform" "$environment"
            ;;
        "cleanup")
            check_dependencies "$platform"
            cleanup "$platform"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
