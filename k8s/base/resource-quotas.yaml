# Kubernetes 资源配额和限制配置
# 确保资源的合理分配和使用

# 命名空间资源配额
apiVersion: v1
kind: ResourceQuota
metadata:
  name: paas-prod-quota
  namespace: paas-prod
  labels:
    environment: production
spec:
  hard:
    # 计算资源限制
    requests.cpu: "20"
    requests.memory: 40Gi
    requests.ephemeral-storage: 100Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    limits.ephemeral-storage: 200Gi
    
    # 对象数量限制
    pods: "100"
    persistentvolumeclaims: "20"
    services: "20"
    secrets: "50"
    configmaps: "50"
    replicationcontrollers: "0"
    
    # 存储限制
    requests.storage: 500Gi
    
    # 网络限制
    services.loadbalancers: "5"
    services.nodeports: "10"

---
# 限制范围配置
apiVersion: v1
kind: LimitRange
metadata:
  name: paas-prod-limits
  namespace: paas-prod
  labels:
    environment: production
spec:
  limits:
  # Pod 限制
  - type: Pod
    max:
      cpu: "4"
      memory: 8Gi
      ephemeral-storage: 10Gi
    min:
      cpu: 100m
      memory: 128Mi
      ephemeral-storage: 1Gi
    maxLimitRequestRatio:
      cpu: "4"
      memory: "4"
      ephemeral-storage: "4"
  
  # 容器限制
  - type: Container
    default:
      cpu: 500m
      memory: 512Mi
      ephemeral-storage: 2Gi
    defaultRequest:
      cpu: 250m
      memory: 256Mi
      ephemeral-storage: 1Gi
    max:
      cpu: "2"
      memory: 4Gi
      ephemeral-storage: 5Gi
    min:
      cpu: 100m
      memory: 128Mi
      ephemeral-storage: 500Mi
    maxLimitRequestRatio:
      cpu: "4"
      memory: "4"
      ephemeral-storage: "4"
  
  # PVC 限制
  - type: PersistentVolumeClaim
    max:
      storage: 100Gi
    min:
      storage: 1Gi

---
# 优先级类配置
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: high-priority
  labels:
    environment: production
value: 1000
globalDefault: false
description: "High priority class for critical services"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: medium-priority
  labels:
    environment: production
value: 500
globalDefault: true
description: "Medium priority class for normal services"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: low-priority
  labels:
    environment: production
value: 100
globalDefault: false
description: "Low priority class for batch jobs"

---
# 垂直 Pod 自动扩缩配置示例
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: api-gateway-vpa
  namespace: paas-prod
  labels:
    app: api-gateway
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: api-gateway
      maxAllowed:
        cpu: "2"
        memory: 4Gi
      minAllowed:
        cpu: 100m
        memory: 128Mi
      controlledResources:
      - cpu
      - memory

---
# 服务账户配置
apiVersion: v1
kind: ServiceAccount
metadata:
  name: api-gateway
  namespace: paas-prod
  labels:
    app: api-gateway
automountServiceAccountToken: false

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: user-service
  namespace: paas-prod
  labels:
    app: user-service
automountServiceAccountToken: false

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: app-manager
  namespace: paas-prod
  labels:
    app: app-manager
automountServiceAccountToken: false

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ci-cd-service
  namespace: paas-prod
  labels:
    app: ci-cd-service
automountServiceAccountToken: true

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: monitoring-service
  namespace: paas-prod
  labels:
    app: monitoring-service
automountServiceAccountToken: true

---
# RBAC 配置
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ci-cd-role
  namespace: paas-prod
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ci-cd-binding
  namespace: paas-prod
subjects:
- kind: ServiceAccount
  name: ci-cd-service
  namespace: paas-prod
roleRef:
  kind: Role
  name: ci-cd-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: monitoring-role
  namespace: paas-prod
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "nodes", "nodes/metrics"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: monitoring-binding
  namespace: paas-prod
subjects:
- kind: ServiceAccount
  name: monitoring-service
  namespace: paas-prod
roleRef:
  kind: Role
  name: monitoring-role
  apiGroup: rbac.authorization.k8s.io

---
# Pod 安全策略（如果启用）
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: paas-prod-psp
  labels:
    environment: production
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  allowedCapabilities:
    - NET_BIND_SERVICE
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: true
  seLinux:
    rule: RunAsAny
  seccompProfile:
    type: RuntimeDefault
