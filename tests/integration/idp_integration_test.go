package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"paas-platform/internal/auth"
	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// IDPIntegrationTestSuite IDP 集成测试套件
type IDPIntegrationTestSuite struct {
	suite.Suite
	db         *gorm.DB
	router     *gin.Engine
	idpService auth.IDPService
	idpHandler *auth.IDPHandler
	logger     logger.Logger
}

// SetupSuite 设置测试套件
func (suite *IDPIntegrationTestSuite) SetupSuite() {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移
	err = db.AutoMigrate(
		&auth.Tenant{},
		&auth.User{},
		&auth.IDPProvider{},
		&auth.IDPAccount{},
		&auth.IDPSession{},
		&auth.IDPAuditLog{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// 创建日志记录器
	suite.logger = logger.NewLogger()

	// 创建服务
	suite.idpService = auth.NewIDPService(db, suite.logger)

	// 创建处理器
	authService := &MockAuthService{}
	suite.idpHandler = auth.NewIDPHandler(suite.idpService, authService, suite.logger)

	// 设置路由
	suite.router = gin.New()
	v1 := suite.router.Group("/api/v1")
	suite.idpHandler.RegisterRoutes(v1)

	// 创建测试数据
	suite.createTestData()
}

// createTestData 创建测试数据
func (suite *IDPIntegrationTestSuite) createTestData() {
	// 创建测试租户
	tenant := &auth.Tenant{
		ID:     "test-tenant-001",
		Name:   "测试租户",
		Status: auth.TenantStatusActive,
	}
	suite.db.Create(tenant)

	// 创建测试用户
	user := &auth.User{
		ID:       "test-user-001",
		TenantID: tenant.ID,
		Username: "testuser",
		Email:    "<EMAIL>",
		Status:   auth.UserStatusActive,
	}
	suite.db.Create(user)

	// 创建测试 IDP 提供商
	provider := &auth.IDPProvider{
		ID:       "test-provider-001",
		Name:     "test-oidc",
		Type:     auth.IDPProviderOIDC,
		Enabled:  true,
		TenantID: tenant.ID,
		Config: auth.IDPConfig{
			ClientID:     "test-client-id",
			ClientSecret: "test-client-secret",
			AuthURL:      "https://example.com/auth",
			TokenURL:     "https://example.com/token",
			UserInfoURL:  "https://example.com/userinfo",
		},
		Mapping: auth.IDPAttributeMapping{
			UserID:    "sub",
			Username:  "preferred_username",
			Email:     "email",
			FirstName: "given_name",
			LastName:  "family_name",
		},
		Priority:       1,
		AutoCreateUser: true,
		AutoLinkUser:   false,
	}
	suite.db.Create(provider)
}

// TestCreateIDPProvider 测试创建 IDP 提供商 API
func (suite *IDPIntegrationTestSuite) TestCreateIDPProvider() {
	request := auth.CreateIDPProviderRequest{
		Name:     "test-google",
		Type:     auth.IDPProviderOIDC,
		Enabled:  true,
		TenantID: "test-tenant-001",
		Config: auth.IDPConfig{
			ClientID:     "google-client-id",
			ClientSecret: "google-client-secret",
			AuthURL:      "https://accounts.google.com/o/oauth2/v2/auth",
			TokenURL:     "https://oauth2.googleapis.com/token",
			UserInfoURL:  "https://www.googleapis.com/oauth2/v2/userinfo",
		},
		Mapping: auth.IDPAttributeMapping{
			UserID:    "id",
			Username:  "email",
			Email:     "email",
			FirstName: "given_name",
			LastName:  "family_name",
		},
		Priority:       2,
		AutoCreateUser: true,
		AutoLinkUser:   false,
	}

	body, _ := json.Marshal(request)
	req := httptest.NewRequest("POST", "/api/v1/admin/idp/providers", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusCreated, w.Code)

	var response auth.IDPProvider
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(request.Name, response.Name)
	suite.Equal(request.Type, response.Type)
}

// TestInitiateIDPLogin 测试发起 IDP 登录 API
func (suite *IDPIntegrationTestSuite) TestInitiateIDPLogin() {
	request := auth.IDPLoginRequest{
		ProviderName: "test-oidc",
		TenantID:     "test-tenant-001",
		RedirectURL:  "https://example.com/callback",
	}

	body, _ := json.Marshal(request)
	req := httptest.NewRequest("POST", "/api/v1/idp/login/test-oidc", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response auth.IDPAuthInitResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotEmpty(response.AuthURL)
	suite.NotEmpty(response.State)
}

// TestLinkIDPAccount 测试关联 IDP 账号 API
func (suite *IDPIntegrationTestSuite) TestLinkIDPAccount() {
	request := auth.LinkIDPAccountRequest{
		UserID:        "test-user-001",
		IDPProviderID: "test-provider-001",
		IDPUserID:     "idp-user-123",
		IDPUsername:   "idp-username",
		IDPEmail:      "<EMAIL>",
	}

	body, _ := json.Marshal(request)
	req := httptest.NewRequest("POST", "/api/v1/idp/link", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response auth.IDPAccount
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(request.UserID, response.UserID)
	suite.Equal(request.IDPProviderID, response.IDPProviderID)
	suite.Equal(request.IDPUserID, response.IDPUserID)
}

// TestGetIDPProviders 测试获取 IDP 提供商列表 API
func (suite *IDPIntegrationTestSuite) TestGetIDPProviders() {
	req := httptest.NewRequest("GET", "/api/v1/admin/idp/providers?tenant_id=test-tenant-001", nil)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response []*auth.IDPProvider
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Len(response, 1)
	suite.Equal("test-oidc", response[0].Name)
}

// TestIDPProviderNotFound 测试 IDP 提供商不存在的情况
func (suite *IDPIntegrationTestSuite) TestIDPProviderNotFound() {
	request := auth.IDPLoginRequest{
		ProviderName: "non-existent-provider",
		TenantID:     "test-tenant-001",
	}

	body, _ := json.Marshal(request)
	req := httptest.NewRequest("POST", "/api/v1/idp/login/non-existent-provider", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusNotFound, w.Code)

	var response auth.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("PROVIDER_NOT_FOUND", response.Code)
}

// TestInvalidIDPConfiguration 测试无效的 IDP 配置
func (suite *IDPIntegrationTestSuite) TestInvalidIDPConfiguration() {
	request := auth.CreateIDPProviderRequest{
		Name:     "invalid-provider",
		Type:     auth.IDPProviderOIDC,
		TenantID: "test-tenant-001",
		Config: auth.IDPConfig{
			// 缺少必要的配置
		},
		Mapping: auth.IDPAttributeMapping{
			Email: "email",
		},
	}

	body, _ := json.Marshal(request)
	req := httptest.NewRequest("POST", "/api/v1/admin/idp/providers", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusBadRequest, w.Code)

	var response auth.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("INVALID_REQUEST", response.Code)
}

// MockAuthService 模拟认证服务
type MockAuthService struct{}

func (m *MockAuthService) Login(ctx context.Context, req *auth.LoginRequest) (*auth.LoginResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (m *MockAuthService) Logout(ctx context.Context, userID string, sessionID string) error {
	return fmt.Errorf("not implemented")
}

func (m *MockAuthService) RefreshToken(ctx context.Context, refreshToken string) (*auth.TokenPair, error) {
	return nil, fmt.Errorf("not implemented")
}

// 实现其他必要的方法...

// TearDownSuite 清理测试套件
func (suite *IDPIntegrationTestSuite) TearDownSuite() {
	// 清理资源
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// TestIDPIntegrationTestSuite 运行集成测试套件
func TestIDPIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(IDPIntegrationTestSuite))
}
