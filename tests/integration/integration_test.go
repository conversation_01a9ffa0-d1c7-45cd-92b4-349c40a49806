package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"paas-platform/internal/auth"
	"paas-platform/internal/models"
	"paas-platform/pkg/config"
	"paas-platform/pkg/logger"
)

// IntegrationTestSuite 集成测试套件
type IntegrationTestSuite struct {
	suite.Suite
	db           *gorm.DB
	logger       logger.Logger
	config       *config.Config
	authToken    string
	testUser     *models.User
	testApp      *models.Application
	baseURL      string
	httpClient   *http.Client
}

// SetupSuite 测试套件初始化
func (suite *IntegrationTestSuite) SetupSuite() {
	// 初始化配置
	suite.config = &config.Config{
		Database: config.DatabaseConfig{
			Host:     getEnv("TEST_DB_HOST", "localhost"),
			Port:     getEnv("TEST_DB_PORT", "5432"),
			User:     getEnv("TEST_DB_USER", "postgres"),
			Password: getEnv("TEST_DB_PASSWORD", "password"),
			DBName:   getEnv("TEST_DB_NAME", "paas_test"),
		},
		JWT: config.JWTConfig{
			Secret:     "test-secret-key-for-integration-tests",
			ExpiryTime: 24 * time.Hour,
		},
	}

	// 初始化日志
	suite.logger = logger.NewLogger("integration-test", "debug")

	// 初始化数据库连接
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		suite.config.Database.Host,
		suite.config.Database.User,
		suite.config.Database.Password,
		suite.config.Database.DBName,
		suite.config.Database.Port,
	)

	var err error
	suite.db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	suite.Require().NoError(err, "数据库连接失败")

	// 自动迁移测试表
	err = suite.db.AutoMigrate(
		&models.User{},
		&models.Application{},
		&models.Pipeline{},
		&models.Deployment{},
		&models.Config{},
	)
	suite.Require().NoError(err, "数据库迁移失败")

	// 设置基础 URL
	suite.baseURL = getEnv("TEST_BASE_URL", "http://localhost:8080/api/v1")

	// 初始化 HTTP 客户端
	suite.httpClient = &http.Client{
		Timeout: 30 * time.Second,
	}

	// 清理测试数据
	suite.cleanupTestData()

	// 创建测试用户
	suite.createTestUser()

	// 获取认证令牌
	suite.authenticateTestUser()

	suite.logger.Info("集成测试套件初始化完成")
}

// TearDownSuite 测试套件清理
func (suite *IntegrationTestSuite) TearDownSuite() {
	// 清理测试数据
	suite.cleanupTestData()

	// 关闭数据库连接
	if sqlDB, err := suite.db.DB(); err == nil {
		sqlDB.Close()
	}

	suite.logger.Info("集成测试套件清理完成")
}

// SetupTest 每个测试前的准备
func (suite *IntegrationTestSuite) SetupTest() {
	// 清理应用相关的测试数据
	suite.db.Where("owner_id = ?", suite.testUser.ID).Delete(&models.Application{})
	suite.db.Where("user_id = ?", suite.testUser.ID).Delete(&models.Pipeline{})
}

// cleanupTestData 清理测试数据
func (suite *IntegrationTestSuite) cleanupTestData() {
	// 删除测试数据（按依赖关系顺序）
	suite.db.Where("email LIKE ?", "%@test.com").Delete(&models.User{})
	suite.db.Where("name LIKE ?", "test-%").Delete(&models.Application{})
	suite.db.Where("name LIKE ?", "test-%").Delete(&models.Pipeline{})
}

// createTestUser 创建测试用户
func (suite *IntegrationTestSuite) createTestUser() {
	hashedPassword, err := auth.HashPassword("test123456")
	suite.Require().NoError(err)

	suite.testUser = &models.User{
		Username:     "testuser",
		Email:        "<EMAIL>",
		PasswordHash: hashedPassword,
		RealName:     "测试用户",
		Status:       "active",
		Roles:        []string{"developer"},
	}

	err = suite.db.Create(suite.testUser).Error
	suite.Require().NoError(err, "创建测试用户失败")
}

// authenticateTestUser 认证测试用户
func (suite *IntegrationTestSuite) authenticateTestUser() {
	loginData := map[string]string{
		"username": suite.testUser.Username,
		"password": "test123456",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := suite.httpClient.Post(
		suite.baseURL+"/auth/login",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	suite.Require().NoError(err, "登录请求失败")
	defer resp.Body.Close()

	suite.Require().Equal(http.StatusOK, resp.StatusCode, "登录失败")

	var loginResp struct {
		Code int `json:"code"`
		Data struct {
			Token string `json:"token"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&loginResp)
	suite.Require().NoError(err, "解析登录响应失败")

	suite.authToken = loginResp.Data.Token
	suite.Require().NotEmpty(suite.authToken, "获取认证令牌失败")
}

// makeAuthenticatedRequest 发送认证请求
func (suite *IntegrationTestSuite) makeAuthenticatedRequest(method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	var req *http.Request
	var err error
	if reqBody != nil {
		req, err = http.NewRequest(method, suite.baseURL+endpoint, reqBody)
	} else {
		req, err = http.NewRequest(method, suite.baseURL+endpoint, nil)
	}

	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)

	return suite.httpClient.Do(req)
}

// TestUserAuthenticationFlow 测试用户认证流程
func (suite *IntegrationTestSuite) TestUserAuthenticationFlow() {
	suite.T().Log("测试用户认证流程")

	// 测试获取当前用户信息
	resp, err := suite.makeAuthenticatedRequest("GET", "/auth/me", nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	var userResp struct {
		Code int `json:"code"`
		Data struct {
			Username string   `json:"username"`
			Email    string   `json:"email"`
			Roles    []string `json:"roles"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&userResp)
	suite.Require().NoError(err)

	suite.Assert().Equal(suite.testUser.Username, userResp.Data.Username)
	suite.Assert().Equal(suite.testUser.Email, userResp.Data.Email)
}

// TestApplicationLifecycle 测试应用生命周期
func (suite *IntegrationTestSuite) TestApplicationLifecycle() {
	suite.T().Log("测试应用生命周期管理")

	// 1. 创建应用
	appData := map[string]interface{}{
		"name":           "test-app-integration",
		"description":    "集成测试应用",
		"repository_url": "https://github.com/test/test-app.git",
		"branch":         "main",
		"environment":    "development",
		"config": map[string]interface{}{
			"port": 3000,
			"env_vars": map[string]string{
				"NODE_ENV": "development",
			},
		},
	}

	resp, err := suite.makeAuthenticatedRequest("POST", "/applications", appData)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusCreated, resp.StatusCode)

	var createResp struct {
		Code int `json:"code"`
		Data struct {
			ID          string `json:"id"`
			Name        string `json:"name"`
			Status      string `json:"status"`
			Environment string `json:"environment"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&createResp)
	suite.Require().NoError(err)

	appID := createResp.Data.ID
	suite.Assert().NotEmpty(appID)
	suite.Assert().Equal("test-app-integration", createResp.Data.Name)

	// 2. 获取应用详情
	resp, err = suite.makeAuthenticatedRequest("GET", "/applications/"+appID, nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 3. 更新应用
	updateData := map[string]interface{}{
		"description": "更新后的集成测试应用",
		"config": map[string]interface{}{
			"port": 3001,
			"env_vars": map[string]string{
				"NODE_ENV": "development",
				"DEBUG":    "true",
			},
		},
	}

	resp, err = suite.makeAuthenticatedRequest("PUT", "/applications/"+appID, updateData)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 4. 启动应用
	resp, err = suite.makeAuthenticatedRequest("POST", "/applications/"+appID+"/start", nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 5. 停止应用
	resp, err = suite.makeAuthenticatedRequest("POST", "/applications/"+appID+"/stop", nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 6. 删除应用
	resp, err = suite.makeAuthenticatedRequest("DELETE", "/applications/"+appID, nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 7. 验证应用已删除
	resp, err = suite.makeAuthenticatedRequest("GET", "/applications/"+appID, nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusNotFound, resp.StatusCode)
}

// TestPipelineIntegration 测试流水线集成
func (suite *IntegrationTestSuite) TestPipelineIntegration() {
	suite.T().Log("测试 CI/CD 流水线集成")

	// 首先创建一个应用
	appData := map[string]interface{}{
		"name":           "test-pipeline-app",
		"description":    "流水线测试应用",
		"repository_url": "https://github.com/test/pipeline-app.git",
		"branch":         "main",
		"environment":    "development",
	}

	resp, err := suite.makeAuthenticatedRequest("POST", "/applications", appData)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	var appResp struct {
		Code int `json:"code"`
		Data struct {
			ID string `json:"id"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&appResp)
	suite.Require().NoError(err)

	appID := appResp.Data.ID

	// 创建流水线
	pipelineData := map[string]interface{}{
		"name":    "test-pipeline-integration",
		"app_id":  appID,
		"branch":  "main",
		"trigger": "manual",
		"stages": []map[string]interface{}{
			{
				"name":   "代码检出",
				"type":   "checkout",
				"config": map[string]interface{}{},
			},
			{
				"name": "构建",
				"type": "build",
				"config": map[string]interface{}{
					"command": "npm run build",
				},
			},
			{
				"name": "测试",
				"type": "test",
				"config": map[string]interface{}{
					"command": "npm test",
				},
			},
		},
	}

	resp, err = suite.makeAuthenticatedRequest("POST", "/pipelines", pipelineData)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusCreated, resp.StatusCode)

	var pipelineResp struct {
		Code int `json:"code"`
		Data struct {
			ID   string `json:"id"`
			Name string `json:"name"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&pipelineResp)
	suite.Require().NoError(err)

	pipelineID := pipelineResp.Data.ID
	suite.Assert().NotEmpty(pipelineID)

	// 运行流水线
	resp, err = suite.makeAuthenticatedRequest("POST", "/pipelines/"+pipelineID+"/run", nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 获取流水线执行历史
	resp, err = suite.makeAuthenticatedRequest("GET", "/pipelines/"+pipelineID+"/executions", nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 清理：删除流水线和应用
	suite.makeAuthenticatedRequest("DELETE", "/pipelines/"+pipelineID, nil)
	suite.makeAuthenticatedRequest("DELETE", "/applications/"+appID, nil)
}

// TestUserManagementIntegration 测试用户管理集成
func (suite *IntegrationTestSuite) TestUserManagementIntegration() {
	suite.T().Log("测试用户管理集成")

	// 创建新用户
	userData := map[string]interface{}{
		"username":  "integration-test-user",
		"email":     "<EMAIL>",
		"password":  "password123",
		"real_name": "集成测试用户",
		"roles":     []string{"user"},
	}

	resp, err := suite.makeAuthenticatedRequest("POST", "/users", userData)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusCreated, resp.StatusCode)

	var userResp struct {
		Code int `json:"code"`
		Data struct {
			ID       string   `json:"id"`
			Username string   `json:"username"`
			Email    string   `json:"email"`
			Roles    []string `json:"roles"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&userResp)
	suite.Require().NoError(err)

	newUserID := userResp.Data.ID
	suite.Assert().NotEmpty(newUserID)

	// 获取用户列表
	resp, err = suite.makeAuthenticatedRequest("GET", "/users?page=1&page_size=10", nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 更新用户
	updateData := map[string]interface{}{
		"real_name": "更新的集成测试用户",
		"roles":     []string{"developer"},
	}

	resp, err = suite.makeAuthenticatedRequest("PUT", "/users/"+newUserID, updateData)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)

	// 删除用户
	resp, err = suite.makeAuthenticatedRequest("DELETE", "/users/"+newUserID, nil)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	suite.Assert().Equal(http.StatusOK, resp.StatusCode)
}

// TestCrossServiceDataConsistency 测试跨服务数据一致性
func (suite *IntegrationTestSuite) TestCrossServiceDataConsistency() {
	suite.T().Log("测试跨服务数据一致性")

	// 创建应用
	appData := map[string]interface{}{
		"name":           "consistency-test-app",
		"description":    "数据一致性测试应用",
		"repository_url": "https://github.com/test/consistency-app.git",
		"branch":         "main",
		"environment":    "development",
	}

	resp, err := suite.makeAuthenticatedRequest("POST", "/applications", appData)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	var appResp struct {
		Code int `json:"code"`
		Data struct {
			ID      string `json:"id"`
			OwnerID string `json:"owner_id"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&appResp)
	suite.Require().NoError(err)

	// 验证应用的所有者是当前用户
	suite.Assert().Equal(suite.testUser.ID, appResp.Data.OwnerID)

	// 直接从数据库验证数据一致性
	var dbApp models.Application
	err = suite.db.Where("id = ?", appResp.Data.ID).First(&dbApp).Error
	suite.Require().NoError(err)

	suite.Assert().Equal("consistency-test-app", dbApp.Name)
	suite.Assert().Equal(suite.testUser.ID, dbApp.OwnerID)

	// 清理
	suite.makeAuthenticatedRequest("DELETE", "/applications/"+appResp.Data.ID, nil)
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// TestIntegrationSuite 运行集成测试套件
func TestIntegrationSuite(t *testing.T) {
	// 检查是否启用集成测试
	if os.Getenv("INTEGRATION_TEST") != "true" {
		t.Skip("跳过集成测试，设置 INTEGRATION_TEST=true 来运行")
	}

	suite.Run(t, new(IntegrationTestSuite))
}
