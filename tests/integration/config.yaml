# PaaS 平台集成测试配置文件

# 测试环境配置
environment:
  name: "integration-test"
  description: "集成测试环境配置"

# 数据库配置
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "paas_integration_test"
  sslmode: "disable"
  max_connections: 10
  connection_timeout: 30

# 服务配置
services:
  api_gateway:
    host: "localhost"
    port: 8080
    health_endpoint: "/health"
    timeout: 30
    
  user_service:
    host: "localhost"
    port: 8081
    health_endpoint: "/health"
    timeout: 30
    
  app_manager:
    host: "localhost"
    port: 8082
    health_endpoint: "/health"
    timeout: 30
    
  cicd_service:
    host: "localhost"
    port: 8083
    health_endpoint: "/health"
    timeout: 30

# JWT 配置
jwt:
  secret: "test-jwt-secret-for-integration-tests"
  expiry: "24h"
  issuer: "paas-platform-test"

# 测试配置
test:
  # 测试超时时间
  timeout: "10m"
  
  # 测试并发数
  concurrency: 5
  
  # 测试重试次数
  retry_count: 3
  
  # 测试数据清理
  cleanup_data: true
  
  # 测试用户配置
  test_users:
    - username: "testuser1"
      email: "<EMAIL>"
      password: "test123456"
      real_name: "测试用户1"
      roles: ["developer"]
      
    - username: "testuser2"
      email: "<EMAIL>"
      password: "test123456"
      real_name: "测试用户2"
      roles: ["user"]
      
    - username: "testadmin"
      email: "<EMAIL>"
      password: "admin123456"
      real_name: "测试管理员"
      roles: ["admin"]

  # 测试应用配置
  test_applications:
    - name: "test-app-1"
      description: "集成测试应用1"
      repository_url: "https://github.com/test/app1.git"
      branch: "main"
      environment: "development"
      config:
        port: 3000
        env_vars:
          NODE_ENV: "development"
          DEBUG: "true"
        resources:
          cpu: "500m"
          memory: "512Mi"
          
    - name: "test-app-2"
      description: "集成测试应用2"
      repository_url: "https://github.com/test/app2.git"
      branch: "develop"
      environment: "testing"
      config:
        port: 3001
        env_vars:
          NODE_ENV: "testing"
        resources:
          cpu: "1000m"
          memory: "1Gi"

  # 测试流水线配置
  test_pipelines:
    - name: "test-pipeline-1"
      description: "集成测试流水线1"
      trigger: "manual"
      stages:
        - name: "代码检出"
          type: "checkout"
          config: {}
          
        - name: "安装依赖"
          type: "script"
          config:
            command: "npm install"
            
        - name: "运行测试"
          type: "test"
          config:
            command: "npm test"
            coverage: true
            
        - name: "构建应用"
          type: "build"
          config:
            command: "npm run build"
            artifacts: ["dist/"]
            
        - name: "部署应用"
          type: "deploy"
          config:
            environment: "development"
            strategy: "rolling"

# 测试场景配置
scenarios:
  # 用户认证测试场景
  authentication:
    enabled: true
    tests:
      - name: "用户登录"
        description: "测试用户登录流程"
        steps:
          - action: "login"
            username: "testuser1"
            password: "test123456"
          - action: "verify_token"
          - action: "get_user_info"
          
      - name: "Token 验证"
        description: "测试 JWT Token 验证"
        steps:
          - action: "login"
          - action: "access_protected_resource"
          - action: "verify_permissions"

  # 应用管理测试场景
  application_management:
    enabled: true
    tests:
      - name: "应用生命周期"
        description: "测试应用的完整生命周期"
        steps:
          - action: "create_application"
            app_config: "test-app-1"
          - action: "get_application"
          - action: "update_application"
          - action: "start_application"
          - action: "stop_application"
          - action: "delete_application"
          
      - name: "应用权限控制"
        description: "测试应用的权限控制"
        steps:
          - action: "create_application_as_user"
          - action: "access_application_as_other_user"
          - action: "verify_access_denied"

  # CI/CD 流水线测试场景
  cicd_pipeline:
    enabled: true
    tests:
      - name: "流水线执行"
        description: "测试流水线的创建和执行"
        steps:
          - action: "create_application"
          - action: "create_pipeline"
            pipeline_config: "test-pipeline-1"
          - action: "run_pipeline"
          - action: "monitor_execution"
          - action: "verify_completion"
          
      - name: "流水线管理"
        description: "测试流水线的管理功能"
        steps:
          - action: "create_pipeline"
          - action: "update_pipeline"
          - action: "get_pipeline_history"
          - action: "delete_pipeline"

  # 数据一致性测试场景
  data_consistency:
    enabled: true
    tests:
      - name: "跨服务数据同步"
        description: "测试跨服务的数据一致性"
        steps:
          - action: "create_user"
          - action: "create_application_for_user"
          - action: "verify_ownership"
          - action: "delete_user"
          - action: "verify_cascade_delete"
          
      - name: "并发操作"
        description: "测试并发操作的数据一致性"
        steps:
          - action: "concurrent_create_applications"
          - action: "verify_all_created"
          - action: "concurrent_update_applications"
          - action: "verify_all_updated"

# 性能测试配置
performance:
  enabled: false
  load_test:
    concurrent_users: 10
    duration: "5m"
    ramp_up: "1m"
    
  stress_test:
    concurrent_users: 50
    duration: "10m"
    ramp_up: "2m"
    
  endpoints:
    - path: "/api/v1/applications"
      method: "GET"
      weight: 40
      
    - path: "/api/v1/applications"
      method: "POST"
      weight: 20
      
    - path: "/api/v1/users"
      method: "GET"
      weight: 30
      
    - path: "/api/v1/pipelines"
      method: "GET"
      weight: 10

# 报告配置
reporting:
  # 输出格式
  formats: ["json", "html", "markdown"]
  
  # 报告文件路径
  output_dir: "./test-reports"
  
  # 包含的指标
  metrics:
    - "test_count"
    - "pass_rate"
    - "execution_time"
    - "coverage"
    - "error_rate"
    
  # 生成图表
  charts:
    enabled: true
    types: ["bar", "line", "pie"]

# 日志配置
logging:
  level: "debug"
  format: "json"
  output: "stdout"
  file: "./logs/integration-test.log"
  
  # 日志轮转
  rotation:
    enabled: true
    max_size: "100MB"
    max_files: 5
    max_age: "7d"
