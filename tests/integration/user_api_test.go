package integration

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/suite"
)

// UserAPIIntegrationTestSuite 用户 API 集成测试套件
type UserAPIIntegrationTestSuite struct {
	IntegrationTestSuite
	httpHelper *HTTPTestHelper
	dbHelper   *DatabaseTestHelper
}

// SetupSuite 测试套件初始化
func (suite *UserAPIIntegrationTestSuite) SetupSuite() {
	suite.IntegrationTestSuite.SetupSuite()
	
	// 迁移用户相关表
	err := suite.DB.AutoMigrate(
		&User{},
		&Role{},
		&UserRole{},
		&UserSession{},
	)
	suite.Require().NoError(err)
	
	// 初始化辅助工具
	suite.httpHelper = NewHTTPTestHelper(&suite.IntegrationTestSuite)
	suite.dbHelper = NewDatabaseTestHelper(suite.DB)
	
	// 注册用户 API 路由
	suite.registerUserAPIRoutes()
}

// SetupTest 每个测试前的初始化
func (suite *UserAPIIntegrationTestSuite) SetupTest() {
	suite.IntegrationTestSuite.SetupTest()
	
	// 填充测试数据
	err := suite.dbHelper.SeedTestData()
	suite.Require().NoError(err)
}

// registerUserAPIRoutes 注册用户 API 路由
func (suite *UserAPIIntegrationTestSuite) registerUserAPIRoutes() {
	v1 := suite.Router.Group("/api/v1")
	
	// 用户注册
	v1.POST("/auth/register", func(c *gin.Context) {
		var req RegisterRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误"})
			return
		}
		
		// 模拟用户注册逻辑
		user := &User{
			ID:        generateUUID(),
			Name:      req.Name,
			Email:     req.Email,
			Status:    "active",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		// 检查邮箱是否已存在
		var existingUser User
		if err := suite.DB.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "邮箱已存在"})
			return
		}
		
		// 创建用户
		if err := suite.DB.Create(user).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "创建用户失败"})
			return
		}
		
		c.JSON(http.StatusCreated, gin.H{
			"message": "用户注册成功",
			"user": gin.H{
				"id":    user.ID,
				"name":  user.Name,
				"email": user.Email,
			},
		})
	})
	
	// 用户登录
	v1.POST("/auth/login", func(c *gin.Context) {
		var req LoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误"})
			return
		}
		
		// 查找用户
		var user User
		if err := suite.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "用户不存在或密码错误"})
			return
		}
		
		// 模拟密码验证（实际应该验证哈希密码）
		if req.Password != "password123" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "用户不存在或密码错误"})
			return
		}
		
		// 生成 JWT Token（模拟）
		token := "mock-jwt-token-" + user.ID
		
		c.JSON(http.StatusOK, gin.H{
			"message": "登录成功",
			"token":   token,
			"user": gin.H{
				"id":    user.ID,
				"name":  user.Name,
				"email": user.Email,
			},
		})
	})
	
	// 获取用户信息
	v1.GET("/users/:id", func(c *gin.Context) {
		userID := c.Param("id")
		
		var user User
		if err := suite.DB.Where("id = ?", userID).First(&user).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
			return
		}
		
		c.JSON(http.StatusOK, gin.H{
			"user": gin.H{
				"id":         user.ID,
				"name":       user.Name,
				"email":      user.Email,
				"status":     user.Status,
				"created_at": user.CreatedAt,
				"updated_at": user.UpdatedAt,
			},
		})
	})
	
	// 更新用户信息
	v1.PUT("/users/:id", func(c *gin.Context) {
		userID := c.Param("id")
		
		var user User
		if err := suite.DB.Where("id = ?", userID).First(&user).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
			return
		}
		
		var req UpdateUserRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误"})
			return
		}
		
		// 更新用户信息
		if req.Name != "" {
			user.Name = req.Name
		}
		if req.Email != "" {
			user.Email = req.Email
		}
		user.UpdatedAt = time.Now()
		
		if err := suite.DB.Save(&user).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "更新用户失败"})
			return
		}
		
		c.JSON(http.StatusOK, gin.H{
			"message": "用户更新成功",
			"user": gin.H{
				"id":         user.ID,
				"name":       user.Name,
				"email":      user.Email,
				"updated_at": user.UpdatedAt,
			},
		})
	})
	
	// 删除用户
	v1.DELETE("/users/:id", func(c *gin.Context) {
		userID := c.Param("id")
		
		var user User
		if err := suite.DB.Where("id = ?", userID).First(&user).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
			return
		}
		
		// 软删除用户
		user.Status = "deleted"
		user.UpdatedAt = time.Now()
		
		if err := suite.DB.Save(&user).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "删除用户失败"})
			return
		}
		
		c.JSON(http.StatusOK, gin.H{"message": "用户删除成功"})
	})
}

// TestUserRegistration 测试用户注册
func (suite *UserAPIIntegrationTestSuite) TestUserRegistration() {
	suite.Run("成功注册用户", func() {
		// Given
		req := RegisterRequest{
			Name:     "新用户",
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		// When
		resp, err := suite.httpHelper.POST("/api/v1/auth/register", req)
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusCreated)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		suite.Equal("用户注册成功", response["message"])
		suite.Contains(response, "user")
		
		// 验证数据库中的用户
		suite.dbHelper.AssertRecordExists(suite.T(), "users", map[string]interface{}{
			"email": req.Email,
			"name":  req.Name,
		})
	})
	
	suite.Run("邮箱已存在", func() {
		// Given
		req := RegisterRequest{
			Name:     "重复用户",
			Email:    "<EMAIL>", // 已存在的邮箱
			Password: "password123",
		}
		
		// When
		resp, err := suite.httpHelper.POST("/api/v1/auth/register", req)
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusConflict)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		suite.Equal("邮箱已存在", response["error"])
	})
	
	suite.Run("请求参数错误", func() {
		// Given
		invalidReq := map[string]interface{}{
			"name": "用户名",
			// 缺少必需的 email 和 password
		}
		
		// When
		resp, err := suite.httpHelper.POST("/api/v1/auth/register", invalidReq)
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusBadRequest)
	})
}

// TestUserLogin 测试用户登录
func (suite *UserAPIIntegrationTestSuite) TestUserLogin() {
	suite.Run("成功登录", func() {
		// Given
		req := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		// When
		resp, err := suite.httpHelper.POST("/api/v1/auth/login", req)
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusOK)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		suite.Equal("登录成功", response["message"])
		suite.Contains(response, "token")
		suite.Contains(response, "user")
		
		// 验证 token 格式
		token := response["token"].(string)
		suite.Contains(token, "mock-jwt-token-")
	})
	
	suite.Run("用户不存在", func() {
		// Given
		req := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		// When
		resp, err := suite.httpHelper.POST("/api/v1/auth/login", req)
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusUnauthorized)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		suite.Equal("用户不存在或密码错误", response["error"])
	})
	
	suite.Run("密码错误", func() {
		// Given
		req := LoginRequest{
			Email:    "<EMAIL>",
			Password: "wrongpassword",
		}
		
		// When
		resp, err := suite.httpHelper.POST("/api/v1/auth/login", req)
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusUnauthorized)
	})
}

// TestUserCRUD 测试用户 CRUD 操作
func (suite *UserAPIIntegrationTestSuite) TestUserCRUD() {
	suite.Run("获取用户信息", func() {
		// When
		resp, err := suite.httpHelper.GET("/api/v1/users/test-user-001")
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusOK)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		user := response["user"].(map[string]interface{})
		suite.Equal("test-user-001", user["id"])
		suite.Equal("测试用户1", user["name"])
		suite.Equal("<EMAIL>", user["email"])
	})
	
	suite.Run("更新用户信息", func() {
		// Given
		req := UpdateUserRequest{
			Name:  "更新后的用户名",
			Email: "<EMAIL>",
		}
		
		// When
		resp, err := suite.httpHelper.PUT("/api/v1/users/test-user-001", req)
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusOK)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		suite.Equal("用户更新成功", response["message"])
		
		user := response["user"].(map[string]interface{})
		suite.Equal(req.Name, user["name"])
		suite.Equal(req.Email, user["email"])
		
		// 验证数据库中的更新
		suite.dbHelper.AssertRecordExists(suite.T(), "users", map[string]interface{}{
			"id":    "test-user-001",
			"name":  req.Name,
			"email": req.Email,
		})
	})
	
	suite.Run("删除用户", func() {
		// When
		resp, err := suite.httpHelper.DELETE("/api/v1/users/test-user-002")
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusOK)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		suite.Equal("用户删除成功", response["message"])
		
		// 验证用户状态已更新为 deleted
		suite.dbHelper.AssertRecordExists(suite.T(), "users", map[string]interface{}{
			"id":     "test-user-002",
			"status": "deleted",
		})
	})
	
	suite.Run("获取不存在的用户", func() {
		// When
		resp, err := suite.httpHelper.GET("/api/v1/users/nonexistent-user")
		
		// Then
		suite.Require().NoError(err)
		suite.httpHelper.AssertStatusCode(suite.T(), resp, http.StatusNotFound)
		
		var response map[string]interface{}
		err = suite.httpHelper.GetJSONResponse(resp, &response)
		suite.Require().NoError(err)
		
		suite.Equal("用户不存在", response["error"])
	})
}

// 数据结构定义
type User struct {
	ID        string    `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name"`
	Email     string    `json:"email" gorm:"uniqueIndex"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Role struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type UserRole struct {
	UserID string `json:"user_id" gorm:"primaryKey"`
	RoleID string `json:"role_id" gorm:"primaryKey"`
}

type UserSession struct {
	ID        string    `json:"id" gorm:"primaryKey"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
}

type RegisterRequest struct {
	Name     string `json:"name" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

type UpdateUserRequest struct {
	Name  string `json:"name,omitempty"`
	Email string `json:"email,omitempty"`
}

// 辅助函数
func generateUUID() string {
	return fmt.Sprintf("user-%d", time.Now().UnixNano())
}

// 运行集成测试
func TestUserAPIIntegration(t *testing.T) {
	suite.Run(t, new(UserAPIIntegrationTestSuite))
}
