package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"paas-platform/pkg/config"
	appLogger "paas-platform/pkg/logger"
)

// IntegrationTestSuite 集成测试套件基类
type IntegrationTestSuite struct {
	suite.Suite
	DB         *gorm.DB
	Logger     appLogger.Logger
	HTTPClient *http.Client
	BaseURL    string
	Server     *httptest.Server
	Router     *gin.Engine
	ctx        context.Context
}

// SetupSuite 集成测试套件初始化
func (its *IntegrationTestSuite) SetupSuite() {
	// 设置测试环境
	os.Setenv("ENV", "integration")
	os.Setenv("LOG_LEVEL", "info")
	
	// 初始化数据库
	its.setupDatabase()
	
	// 初始化日志
	its.setupLogger()
	
	// 初始化 HTTP 客户端
	its.setupHTTPClient()
	
	// 设置上下文
	its.ctx = context.Background()
	
	// 初始化路由
	its.setupRouter()
	
	// 启动测试服务器
	its.startTestServer()
}

// TearDownSuite 集成测试套件清理
func (its *IntegrationTestSuite) TearDownSuite() {
	if its.Server != nil {
		its.Server.Close()
	}
	
	if its.DB != nil {
		sqlDB, _ := its.DB.DB()
		sqlDB.Close()
	}
}

// SetupTest 每个测试前的初始化
func (its *IntegrationTestSuite) SetupTest() {
	// 清理测试数据
	its.cleanupTestData()
	
	// 重置数据库状态
	its.resetDatabaseState()
}

// TearDownTest 每个测试后的清理
func (its *IntegrationTestSuite) TearDownTest() {
	// 清理测试数据
	its.cleanupTestData()
}

// setupDatabase 设置测试数据库
func (its *IntegrationTestSuite) setupDatabase() {
	var db *gorm.DB
	var err error
	
	// 根据环境变量选择数据库
	dbURL := os.Getenv("TEST_DATABASE_URL")
	if dbURL == "" {
		// 使用内存数据库
		db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Silent),
		})
	} else {
		// 使用 PostgreSQL 测试数据库
		db, err = gorm.Open(postgres.Open(dbURL), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		})
	}
	
	its.Require().NoError(err, "数据库连接失败")
	its.DB = db
	
	// 自动迁移测试表
	its.migrateTestTables()
}

// setupLogger 设置测试日志
func (its *IntegrationTestSuite) setupLogger() {
	its.Logger = appLogger.NewLogger("integration-test", "info")
}

// setupHTTPClient 设置 HTTP 客户端
func (its *IntegrationTestSuite) setupHTTPClient() {
	its.HTTPClient = &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        10,
			IdleConnTimeout:     30 * time.Second,
			DisableCompression:  true,
		},
	}
}

// setupRouter 设置测试路由
func (its *IntegrationTestSuite) setupRouter() {
	gin.SetMode(gin.TestMode)
	its.Router = gin.New()
	
	// 添加中间件
	its.Router.Use(gin.Recovery())
	its.Router.Use(its.loggingMiddleware())
	its.Router.Use(its.corsMiddleware())
	
	// 注册测试路由
	its.registerTestRoutes()
}

// startTestServer 启动测试服务器
func (its *IntegrationTestSuite) startTestServer() {
	its.Server = httptest.NewServer(its.Router)
	its.BaseURL = its.Server.URL
	
	its.Logger.Info("测试服务器启动", "url", its.BaseURL)
}

// migrateTestTables 迁移测试表
func (its *IntegrationTestSuite) migrateTestTables() {
	// 这里应该包含所有需要的表结构
	// 子类可以重写此方法来添加特定的表
	
	its.Logger.Info("迁移测试表结构")
}

// cleanupTestData 清理测试数据
func (its *IntegrationTestSuite) cleanupTestData() {
	if its.DB == nil {
		return
	}
	
	// 获取所有表名并清空
	var tables []string
	
	// SQLite 和 PostgreSQL 的查询方式不同
	if its.DB.Dialector.Name() == "sqlite" {
		its.DB.Raw("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").Scan(&tables)
	} else {
		its.DB.Raw("SELECT tablename FROM pg_tables WHERE schemaname = 'public'").Scan(&tables)
	}
	
	// 清空所有表
	for _, table := range tables {
		its.DB.Exec(fmt.Sprintf("DELETE FROM %s", table))
	}
}

// resetDatabaseState 重置数据库状态
func (its *IntegrationTestSuite) resetDatabaseState() {
	// 重置自增序列等
	if its.DB.Dialector.Name() == "postgres" {
		// PostgreSQL 重置序列
		var sequences []string
		its.DB.Raw("SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public'").Scan(&sequences)
		
		for _, seq := range sequences {
			its.DB.Exec(fmt.Sprintf("ALTER SEQUENCE %s RESTART WITH 1", seq))
		}
	}
}

// loggingMiddleware 日志中间件
func (its *IntegrationTestSuite) loggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		
		its.Logger.Info("HTTP请求",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"status", c.Writer.Status(),
			"duration", duration,
			"client_ip", c.ClientIP())
	}
}

// corsMiddleware CORS 中间件
func (its *IntegrationTestSuite) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}
		
		c.Next()
	}
}

// registerTestRoutes 注册测试路由
func (its *IntegrationTestSuite) registerTestRoutes() {
	// 健康检查
	its.Router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	})
	
	// 测试端点
	its.Router.GET("/test/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "pong"})
	})
}

// HTTPTestHelper HTTP 测试辅助工具
type HTTPTestHelper struct {
	suite     *IntegrationTestSuite
	client    *http.Client
	baseURL   string
	authToken string
}

// NewHTTPTestHelper 创建 HTTP 测试辅助工具
func NewHTTPTestHelper(suite *IntegrationTestSuite) *HTTPTestHelper {
	return &HTTPTestHelper{
		suite:   suite,
		client:  suite.HTTPClient,
		baseURL: suite.BaseURL,
	}
}

// SetAuthToken 设置认证令牌
func (h *HTTPTestHelper) SetAuthToken(token string) {
	h.authToken = token
}

// GET 发送 GET 请求
func (h *HTTPTestHelper) GET(path string) (*http.Response, error) {
	return h.request("GET", path, nil, nil)
}

// POST 发送 POST 请求
func (h *HTTPTestHelper) POST(path string, body interface{}) (*http.Response, error) {
	return h.request("POST", path, body, nil)
}

// PUT 发送 PUT 请求
func (h *HTTPTestHelper) PUT(path string, body interface{}) (*http.Response, error) {
	return h.request("PUT", path, body, nil)
}

// DELETE 发送 DELETE 请求
func (h *HTTPTestHelper) DELETE(path string) (*http.Response, error) {
	return h.request("DELETE", path, nil, nil)
}

// request 发送 HTTP 请求
func (h *HTTPTestHelper) request(method, path string, body interface{}, headers map[string]string) (*http.Response, error) {
	url := h.baseURL + path
	
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}
	
	req, err := http.NewRequest(method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	
	// 设置默认头部
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	
	// 设置认证头部
	if h.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+h.authToken)
	}
	
	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	
	return h.client.Do(req)
}

// AssertStatusCode 断言状态码
func (h *HTTPTestHelper) AssertStatusCode(t *testing.T, resp *http.Response, expectedCode int) {
	assert.Equal(t, expectedCode, resp.StatusCode,
		"期望状态码 %d，实际状态码 %d", expectedCode, resp.StatusCode)
}

// AssertJSONResponse 断言 JSON 响应
func (h *HTTPTestHelper) AssertJSONResponse(t *testing.T, resp *http.Response, expected interface{}) {
	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err, "读取响应体失败")
	defer resp.Body.Close()
	
	var actual interface{}
	err = json.Unmarshal(body, &actual)
	assert.NoError(t, err, "解析 JSON 响应失败")
	
	assert.Equal(t, expected, actual, "响应内容不匹配")
}

// GetJSONResponse 获取 JSON 响应
func (h *HTTPTestHelper) GetJSONResponse(resp *http.Response, target interface{}) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err)
	}
	defer resp.Body.Close()
	
	return json.Unmarshal(body, target)
}

// DatabaseTestHelper 数据库测试辅助工具
type DatabaseTestHelper struct {
	db *gorm.DB
}

// NewDatabaseTestHelper 创建数据库测试辅助工具
func NewDatabaseTestHelper(db *gorm.DB) *DatabaseTestHelper {
	return &DatabaseTestHelper{db: db}
}

// SeedTestData 填充测试数据
func (h *DatabaseTestHelper) SeedTestData() error {
	// 创建测试用户
	users := []map[string]interface{}{
		{
			"id":       "test-user-001",
			"name":     "测试用户1",
			"email":    "<EMAIL>",
			"status":   "active",
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		{
			"id":       "test-user-002",
			"name":     "测试用户2",
			"email":    "<EMAIL>",
			"status":   "active",
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
	}
	
	for _, user := range users {
		if err := h.db.Table("users").Create(user).Error; err != nil {
			return fmt.Errorf("创建测试用户失败: %w", err)
		}
	}
	
	// 创建测试应用
	apps := []map[string]interface{}{
		{
			"id":          "test-app-001",
			"name":        "测试应用1",
			"description": "集成测试应用",
			"status":      "active",
			"owner_id":    "test-user-001",
			"created_at":  time.Now(),
			"updated_at":  time.Now(),
		},
	}
	
	for _, app := range apps {
		if err := h.db.Table("applications").Create(app).Error; err != nil {
			return fmt.Errorf("创建测试应用失败: %w", err)
		}
	}
	
	return nil
}

// AssertRecordExists 断言记录存在
func (h *DatabaseTestHelper) AssertRecordExists(t *testing.T, table string, conditions map[string]interface{}) {
	var count int64
	query := h.db.Table(table)
	
	for k, v := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", k), v)
	}
	
	err := query.Count(&count).Error
	assert.NoError(t, err, "查询记录失败")
	assert.Greater(t, count, int64(0), "记录不存在")
}

// AssertRecordNotExists 断言记录不存在
func (h *DatabaseTestHelper) AssertRecordNotExists(t *testing.T, table string, conditions map[string]interface{}) {
	var count int64
	query := h.db.Table(table)
	
	for k, v := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", k), v)
	}
	
	err := query.Count(&count).Error
	assert.NoError(t, err, "查询记录失败")
	assert.Equal(t, int64(0), count, "记录不应该存在")
}

// TestConfig 集成测试配置
type TestConfig struct {
	DatabaseURL     string
	ServerPort      int
	LogLevel        string
	TestTimeout     time.Duration
	EnableCleanup   bool
	SeedTestData    bool
}

// DefaultTestConfig 默认集成测试配置
func DefaultTestConfig() *TestConfig {
	return &TestConfig{
		DatabaseURL:   ":memory:",
		ServerPort:    0, // 随机端口
		LogLevel:      "info",
		TestTimeout:   5 * time.Minute,
		EnableCleanup: true,
		SeedTestData:  true,
	}
}
