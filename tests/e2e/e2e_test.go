package e2e

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/chromedp/chromedp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"paas-platform/pkg/logger"
)

// E2ETestSuite 端到端测试套件
type E2ETestSuite struct {
	suite.Suite
	logger     logger.Logger
	baseURL    string
	webURL     string
	httpClient *http.Client
	ctx        context.Context
	cancel     context.CancelFunc
}

// SetupSuite 测试套件初始化
func (suite *E2ETestSuite) SetupSuite() {
	// 初始化日志
	suite.logger = logger.NewLogger("e2e-test", "debug")

	// 设置基础 URL
	suite.baseURL = getEnv("E2E_API_URL", "http://localhost:8080/api/v1")
	suite.webURL = getEnv("E2E_WEB_URL", "http://localhost:3000")

	// 初始化 HTTP 客户端
	suite.httpClient = &http.Client{
		Timeout: 30 * time.Second,
	}

	// 初始化 Chrome 上下文
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", getEnv("E2E_HEADLESS", "true") == "true"),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.WindowSize(1920, 1080),
	)

	allocCtx, _ := chromedp.NewExecAllocator(context.Background(), opts...)
	suite.ctx, suite.cancel = chromedp.NewContext(allocCtx)

	// 等待服务启动
	suite.waitForServices()

	suite.logger.Info("端到端测试套件初始化完成")
}

// TearDownSuite 测试套件清理
func (suite *E2ETestSuite) TearDownSuite() {
	if suite.cancel != nil {
		suite.cancel()
	}
	suite.logger.Info("端到端测试套件清理完成")
}

// waitForServices 等待服务启动
func (suite *E2ETestSuite) waitForServices() {
	suite.logger.Info("等待服务启动...")

	// 等待后端 API 服务
	suite.waitForService(suite.baseURL+"/health", "后端 API")

	// 等待前端服务
	suite.waitForService(suite.webURL, "前端服务")

	suite.logger.Info("所有服务已启动")
}

// waitForService 等待单个服务启动
func (suite *E2ETestSuite) waitForService(url, name string) {
	maxAttempts := 30
	for i := 0; i < maxAttempts; i++ {
		resp, err := suite.httpClient.Get(url)
		if err == nil && resp.StatusCode < 500 {
			resp.Body.Close()
			suite.logger.Info(fmt.Sprintf("%s 服务已启动", name))
			return
		}
		if resp != nil {
			resp.Body.Close()
		}

		suite.logger.Info(fmt.Sprintf("等待 %s 服务启动... (%d/%d)", name, i+1, maxAttempts))
		time.Sleep(2 * time.Second)
	}

	suite.T().Fatalf("%s 服务启动超时", name)
}

// TestUserLoginFlow 测试用户登录流程
func (suite *E2ETestSuite) TestUserLoginFlow() {
	suite.T().Log("测试用户登录流程")

	// 首先通过 API 创建测试用户
	testUser := map[string]interface{}{
		"username":  "e2euser",
		"email":     "<EMAIL>",
		"password":  "password123",
		"real_name": "E2E测试用户",
		"roles":     []string{"developer"},
	}

	suite.createUserViaAPI(testUser)
	defer suite.deleteUserViaAPI("e2euser")

	// 使用浏览器进行登录测试
	err := chromedp.Run(suite.ctx,
		// 导航到登录页面
		chromedp.Navigate(suite.webURL+"/login"),
		chromedp.WaitVisible(`input[name="username"]`, chromedp.ByQuery),

		// 输入用户名和密码
		chromedp.SendKeys(`input[name="username"]`, "e2euser", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="password"]`, "password123", chromedp.ByQuery),

		// 点击登录按钮
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),

		// 等待登录成功，跳转到仪表板
		chromedp.WaitVisible(`.dashboard`, chromedp.ByQuery),
	)

	suite.Assert().NoError(err, "用户登录流程失败")

	// 验证登录后的页面元素
	var pageTitle string
	err = chromedp.Run(suite.ctx,
		chromedp.Title(&pageTitle),
	)

	suite.Assert().NoError(err)
	suite.Assert().Contains(pageTitle, "PaaS 平台", "页面标题不正确")

	suite.logger.Info("用户登录流程测试完成")
}

// TestApplicationManagementFlow 测试应用管理流程
func (suite *E2ETestSuite) TestApplicationManagementFlow() {
	suite.T().Log("测试应用管理流程")

	// 先登录
	suite.loginAsTestUser()

	// 导航到应用管理页面
	err := chromedp.Run(suite.ctx,
		chromedp.Navigate(suite.webURL+"/applications"),
		chromedp.WaitVisible(`.application-list`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "导航到应用管理页面失败")

	// 创建新应用
	err = chromedp.Run(suite.ctx,
		// 点击创建应用按钮
		chromedp.Click(`button[data-test="create-app"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.app-form`, chromedp.ByQuery),

		// 填写应用信息
		chromedp.SendKeys(`input[name="name"]`, "e2e-test-app", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="description"]`, "E2E测试应用", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="repository_url"]`, "https://github.com/test/e2e-app.git", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="branch"]`, "main", chromedp.ByQuery),

		// 选择环境
		chromedp.Click(`select[name="environment"]`, chromedp.ByQuery),
		chromedp.Click(`option[value="development"]`, chromedp.ByQuery),

		// 提交表单
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),

		// 等待创建成功
		chromedp.WaitVisible(`.success-message`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "创建应用失败")

	// 验证应用出现在列表中
	var appExists bool
	err = chromedp.Run(suite.ctx,
		chromedp.WaitVisible(`[data-test="app-e2e-test-app"]`, chromedp.ByQuery),
		chromedp.Evaluate(`document.querySelector('[data-test="app-e2e-test-app"]') !== null`, &appExists),
	)
	suite.Assert().NoError(err)
	suite.Assert().True(appExists, "应用未出现在列表中")

	// 测试应用操作
	err = chromedp.Run(suite.ctx,
		// 点击应用详情
		chromedp.Click(`[data-test="app-e2e-test-app"] .app-detail-btn`, chromedp.ByQuery),
		chromedp.WaitVisible(`.app-detail`, chromedp.ByQuery),

		// 启动应用
		chromedp.Click(`button[data-test="start-app"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.status-running`, chromedp.ByQuery),

		// 停止应用
		chromedp.Click(`button[data-test="stop-app"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.status-stopped`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "应用操作失败")

	// 删除应用
	err = chromedp.Run(suite.ctx,
		chromedp.Click(`button[data-test="delete-app"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.confirm-dialog`, chromedp.ByQuery),
		chromedp.Click(`button[data-test="confirm-delete"]`, chromedp.ByQuery),
		chromedp.WaitNotPresent(`[data-test="app-e2e-test-app"]`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "删除应用失败")

	suite.logger.Info("应用管理流程测试完成")
}

// TestPipelineManagementFlow 测试流水线管理流程
func (suite *E2ETestSuite) TestPipelineManagementFlow() {
	suite.T().Log("测试流水线管理流程")

	// 先登录
	suite.loginAsTestUser()

	// 先创建一个应用（流水线需要关联应用）
	appID := suite.createAppViaAPI("e2e-pipeline-app")
	defer suite.deleteAppViaAPI(appID)

	// 导航到流水线管理页面
	err := chromedp.Run(suite.ctx,
		chromedp.Navigate(suite.webURL+"/pipelines"),
		chromedp.WaitVisible(`.pipeline-list`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "导航到流水线管理页面失败")

	// 创建新流水线
	err = chromedp.Run(suite.ctx,
		// 点击创建流水线按钮
		chromedp.Click(`button[data-test="create-pipeline"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.pipeline-form`, chromedp.ByQuery),

		// 填写流水线信息
		chromedp.SendKeys(`input[name="name"]`, "e2e-test-pipeline", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="branch"]`, "main", chromedp.ByQuery),

		// 选择关联应用
		chromedp.Click(`select[name="app_id"]`, chromedp.ByQuery),
		chromedp.Click(`option[value="`+appID+`"]`, chromedp.ByQuery),

		// 添加构建阶段
		chromedp.Click(`button[data-test="add-stage"]`, chromedp.ByQuery),
		chromedp.SendKeys(`input[name="stage_name"]`, "构建", chromedp.ByQuery),
		chromedp.Click(`select[name="stage_type"]`, chromedp.ByQuery),
		chromedp.Click(`option[value="build"]`, chromedp.ByQuery),

		// 提交表单
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),

		// 等待创建成功
		chromedp.WaitVisible(`.success-message`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "创建流水线失败")

	// 运行流水线
	err = chromedp.Run(suite.ctx,
		// 找到创建的流水线
		chromedp.WaitVisible(`[data-test="pipeline-e2e-test-pipeline"]`, chromedp.ByQuery),

		// 点击运行按钮
		chromedp.Click(`[data-test="pipeline-e2e-test-pipeline"] button[data-test="run-pipeline"]`, chromedp.ByQuery),

		// 等待执行开始
		chromedp.WaitVisible(`.execution-status.running`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "运行流水线失败")

	// 查看执行历史
	err = chromedp.Run(suite.ctx,
		chromedp.Click(`[data-test="pipeline-e2e-test-pipeline"] .history-btn`, chromedp.ByQuery),
		chromedp.WaitVisible(`.execution-history`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "查看执行历史失败")

	suite.logger.Info("流水线管理流程测试完成")
}

// TestUserManagementFlow 测试用户管理流程
func (suite *E2ETestSuite) TestUserManagementFlow() {
	suite.T().Log("测试用户管理流程")

	// 以管理员身份登录
	suite.loginAsAdmin()

	// 导航到用户管理页面
	err := chromedp.Run(suite.ctx,
		chromedp.Navigate(suite.webURL+"/users"),
		chromedp.WaitVisible(`.user-list`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "导航到用户管理页面失败")

	// 创建新用户
	err = chromedp.Run(suite.ctx,
		// 点击创建用户按钮
		chromedp.Click(`button[data-test="create-user"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.user-form`, chromedp.ByQuery),

		// 填写用户信息
		chromedp.SendKeys(`input[name="username"]`, "e2e-new-user", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="email"]`, "<EMAIL>", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="password"]`, "password123", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="real_name"]`, "E2E新用户", chromedp.ByQuery),

		// 选择角色
		chromedp.Click(`input[value="developer"]`, chromedp.ByQuery),

		// 提交表单
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),

		// 等待创建成功
		chromedp.WaitVisible(`.success-message`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "创建用户失败")

	// 验证用户出现在列表中
	var userExists bool
	err = chromedp.Run(suite.ctx,
		chromedp.WaitVisible(`[data-test="user-e2e-new-user"]`, chromedp.ByQuery),
		chromedp.Evaluate(`document.querySelector('[data-test="user-e2e-new-user"]') !== null`, &userExists),
	)
	suite.Assert().NoError(err)
	suite.Assert().True(userExists, "用户未出现在列表中")

	// 编辑用户
	err = chromedp.Run(suite.ctx,
		chromedp.Click(`[data-test="user-e2e-new-user"] .edit-btn`, chromedp.ByQuery),
		chromedp.WaitVisible(`.user-edit-form`, chromedp.ByQuery),

		// 修改用户信息
		chromedp.Clear(`input[name="real_name"]`),
		chromedp.SendKeys(`input[name="real_name"]`, "E2E更新用户", chromedp.ByQuery),

		// 保存修改
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.success-message`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "编辑用户失败")

	// 删除用户
	err = chromedp.Run(suite.ctx,
		chromedp.Click(`[data-test="user-e2e-new-user"] .delete-btn`, chromedp.ByQuery),
		chromedp.WaitVisible(`.confirm-dialog`, chromedp.ByQuery),
		chromedp.Click(`button[data-test="confirm-delete"]`, chromedp.ByQuery),
		chromedp.WaitNotPresent(`[data-test="user-e2e-new-user"]`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "删除用户失败")

	suite.logger.Info("用户管理流程测试完成")
}

// TestMonitoringDashboard 测试监控面板
func (suite *E2ETestSuite) TestMonitoringDashboard() {
	suite.T().Log("测试监控面板")

	// 先登录
	suite.loginAsTestUser()

	// 导航到监控面板
	err := chromedp.Run(suite.ctx,
		chromedp.Navigate(suite.webURL+"/monitoring"),
		chromedp.WaitVisible(`.monitoring-dashboard`, chromedp.ByQuery),
	)
	suite.Assert().NoError(err, "导航到监控面板失败")

	// 验证关键监控组件
	var componentsExist bool
	err = chromedp.Run(suite.ctx,
		// 检查系统指标卡片
		chromedp.WaitVisible(`.system-metrics`, chromedp.ByQuery),

		// 检查应用状态图表
		chromedp.WaitVisible(`.app-status-chart`, chromedp.ByQuery),

		// 检查性能图表
		chromedp.WaitVisible(`.performance-chart`, chromedp.ByQuery),

		// 检查告警列表
		chromedp.WaitVisible(`.alert-list`, chromedp.ByQuery),

		chromedp.Evaluate(`
			document.querySelector('.system-metrics') !== null &&
			document.querySelector('.app-status-chart') !== null &&
			document.querySelector('.performance-chart') !== null &&
			document.querySelector('.alert-list') !== null
		`, &componentsExist),
	)

	suite.Assert().NoError(err)
	suite.Assert().True(componentsExist, "监控面板组件不完整")

	// 测试时间范围切换
	err = chromedp.Run(suite.ctx,
		chromedp.Click(`button[data-test="time-range-1h"]`, chromedp.ByQuery),
		chromedp.Sleep(2*time.Second), // 等待图表更新

		chromedp.Click(`button[data-test="time-range-24h"]`, chromedp.ByQuery),
		chromedp.Sleep(2*time.Second), // 等待图表更新
	)
	suite.Assert().NoError(err, "时间范围切换失败")

	suite.logger.Info("监控面板测试完成")
}

// 辅助方法

// loginAsTestUser 以测试用户身份登录
func (suite *E2ETestSuite) loginAsTestUser() {
	// 创建测试用户
	testUser := map[string]interface{}{
		"username":  "e2etestuser",
		"email":     "<EMAIL>",
		"password":  "password123",
		"real_name": "E2E测试用户",
		"roles":     []string{"developer"},
	}
	suite.createUserViaAPI(testUser)

	// 登录
	err := chromedp.Run(suite.ctx,
		chromedp.Navigate(suite.webURL+"/login"),
		chromedp.WaitVisible(`input[name="username"]`, chromedp.ByQuery),
		chromedp.SendKeys(`input[name="username"]`, "e2etestuser", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="password"]`, "password123", chromedp.ByQuery),
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.dashboard`, chromedp.ByQuery),
	)
	suite.Require().NoError(err, "测试用户登录失败")
}

// loginAsAdmin 以管理员身份登录
func (suite *E2ETestSuite) loginAsAdmin() {
	// 创建管理员用户
	adminUser := map[string]interface{}{
		"username":  "e2eadmin",
		"email":     "<EMAIL>",
		"password":  "admin123",
		"real_name": "E2E管理员",
		"roles":     []string{"admin"},
	}
	suite.createUserViaAPI(adminUser)

	// 登录
	err := chromedp.Run(suite.ctx,
		chromedp.Navigate(suite.webURL+"/login"),
		chromedp.WaitVisible(`input[name="username"]`, chromedp.ByQuery),
		chromedp.SendKeys(`input[name="username"]`, "e2eadmin", chromedp.ByQuery),
		chromedp.SendKeys(`input[name="password"]`, "admin123", chromedp.ByQuery),
		chromedp.Click(`button[type="submit"]`, chromedp.ByQuery),
		chromedp.WaitVisible(`.dashboard`, chromedp.ByQuery),
	)
	suite.Require().NoError(err, "管理员登录失败")
}

// createUserViaAPI 通过 API 创建用户
func (suite *E2ETestSuite) createUserViaAPI(userData map[string]interface{}) {
	jsonData, _ := json.Marshal(userData)
	resp, err := suite.httpClient.Post(
		suite.baseURL+"/users",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	suite.Require().NoError(err)
	defer resp.Body.Close()
}

// deleteUserViaAPI 通过 API 删除用户
func (suite *E2ETestSuite) deleteUserViaAPI(username string) {
	// 这里需要先获取用户ID，然后删除
	// 简化实现，实际应该通过用户名查找用户ID
}

// createAppViaAPI 通过 API 创建应用
func (suite *E2ETestSuite) createAppViaAPI(appName string) string {
	appData := map[string]interface{}{
		"name":           appName,
		"description":    "E2E测试应用",
		"repository_url": "https://github.com/test/" + appName + ".git",
		"branch":         "main",
		"environment":    "development",
	}

	jsonData, _ := json.Marshal(appData)
	resp, err := suite.httpClient.Post(
		suite.baseURL+"/applications",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	suite.Require().NoError(err)
	defer resp.Body.Close()

	var appResp struct {
		Data struct {
			ID string `json:"id"`
		} `json:"data"`
	}
	json.NewDecoder(resp.Body).Decode(&appResp)
	return appResp.Data.ID
}

// deleteAppViaAPI 通过 API 删除应用
func (suite *E2ETestSuite) deleteAppViaAPI(appID string) {
	req, _ := http.NewRequest("DELETE", suite.baseURL+"/applications/"+appID, nil)
	suite.httpClient.Do(req)
}

// getEnv 获取环境变量
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// TestE2ESuite 运行端到端测试套件
func TestE2ESuite(t *testing.T) {
	// 检查是否启用端到端测试
	if os.Getenv("E2E_TEST") != "true" {
		t.Skip("跳过端到端测试，设置 E2E_TEST=true 来运行")
	}

	suite.Run(t, new(E2ETestSuite))
}
