package e2e

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"github.com/tebeka/selenium"
	"github.com/tebeka/selenium/chrome"

	"paas-platform/pkg/logger"
)

// E2ETestSuite 端到端测试套件基类
type E2ETestSuite struct {
	suite.Suite
	
	// Selenium WebDriver
	driver   selenium.WebDriver
	service  *selenium.Service
	
	// 测试配置
	config *E2EConfig
	logger logger.Logger
	
	// 服务进程
	services map[string]*ServiceProcess
	
	// 测试上下文
	ctx context.Context
}

// E2EConfig 端到端测试配置
type E2EConfig struct {
	// 浏览器配置
	BrowserName    string        `yaml:"browser_name"`    // chrome, firefox
	Headless       bool          `yaml:"headless"`        // 无头模式
	WindowSize     string        `yaml:"window_size"`     // 窗口大小
	ImplicitWait   time.Duration `yaml:"implicit_wait"`   // 隐式等待时间
	PageLoadWait   time.Duration `yaml:"page_load_wait"`  // 页面加载等待时间
	
	// 应用配置
	BaseURL        string        `yaml:"base_url"`        // 应用基础URL
	APIBaseURL     string        `yaml:"api_base_url"`    // API基础URL
	
	// 服务配置
	Services       []ServiceConfig `yaml:"services"`      // 需要启动的服务
	
	// 测试配置
	TestTimeout    time.Duration `yaml:"test_timeout"`    // 测试超时时间
	ScreenshotDir  string        `yaml:"screenshot_dir"`  // 截图保存目录
	VideoDir       string        `yaml:"video_dir"`       // 视频保存目录
	EnableVideo    bool          `yaml:"enable_video"`    // 是否启用视频录制
	
	// 数据配置
	TestDataDir    string        `yaml:"test_data_dir"`   // 测试数据目录
	CleanupData    bool          `yaml:"cleanup_data"`    // 是否清理测试数据
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	Name        string            `yaml:"name"`         // 服务名称
	Command     string            `yaml:"command"`      // 启动命令
	Args        []string          `yaml:"args"`         // 命令参数
	Env         map[string]string `yaml:"env"`          // 环境变量
	WorkDir     string            `yaml:"work_dir"`     // 工作目录
	Port        int               `yaml:"port"`         // 服务端口
	HealthCheck string            `yaml:"health_check"` // 健康检查URL
	StartupTime time.Duration     `yaml:"startup_time"` // 启动等待时间
}

// ServiceProcess 服务进程
type ServiceProcess struct {
	Config  ServiceConfig
	Process *exec.Cmd
	Started bool
}

// SetupSuite 端到端测试套件初始化
func (suite *E2ETestSuite) SetupSuite() {
	// 初始化配置
	suite.setupConfig()
	
	// 初始化日志
	suite.setupLogger()
	
	// 设置测试上下文
	suite.ctx = context.Background()
	
	// 初始化服务管理
	suite.services = make(map[string]*ServiceProcess)
	
	// 启动依赖服务
	suite.startServices()
	
	// 初始化 WebDriver
	suite.setupWebDriver()
	
	// 创建测试目录
	suite.createTestDirectories()
}

// TearDownSuite 端到端测试套件清理
func (suite *E2ETestSuite) TearDownSuite() {
	// 关闭 WebDriver
	if suite.driver != nil {
		suite.driver.Quit()
	}
	
	// 停止 Selenium 服务
	if suite.service != nil {
		suite.service.Stop()
	}
	
	// 停止所有服务
	suite.stopServices()
	
	// 清理测试数据
	if suite.config.CleanupData {
		suite.cleanupTestData()
	}
}

// SetupTest 每个测试前的初始化
func (suite *E2ETestSuite) SetupTest() {
	// 清理浏览器状态
	suite.resetBrowserState()
	
	// 清理测试数据
	suite.cleanupTestData()
	
	// 准备测试数据
	suite.prepareTestData()
}

// TearDownTest 每个测试后的清理
func (suite *E2ETestSuite) TearDownTest() {
	// 截图（如果测试失败）
	if suite.T().Failed() {
		suite.takeScreenshot(suite.T().Name())
	}
	
	// 清理浏览器状态
	suite.resetBrowserState()
}

// setupConfig 设置配置
func (suite *E2ETestSuite) setupConfig() {
	suite.config = &E2EConfig{
		BrowserName:   "chrome",
		Headless:      true,
		WindowSize:    "1920,1080",
		ImplicitWait:  10 * time.Second,
		PageLoadWait:  30 * time.Second,
		BaseURL:       "http://localhost:3000",
		APIBaseURL:    "http://localhost:8080",
		TestTimeout:   5 * time.Minute,
		ScreenshotDir: "test-results/screenshots",
		VideoDir:      "test-results/videos",
		EnableVideo:   false,
		TestDataDir:   "test-data",
		CleanupData:   true,
		Services: []ServiceConfig{
			{
				Name:        "frontend",
				Command:     "npm",
				Args:        []string{"run", "dev"},
				WorkDir:     "web",
				Port:        3000,
				HealthCheck: "http://localhost:3000",
				StartupTime: 30 * time.Second,
			},
			{
				Name:        "api-gateway",
				Command:     "./bin/api-gateway",
				Args:        []string{},
				WorkDir:     ".",
				Port:        8080,
				HealthCheck: "http://localhost:8080/health",
				StartupTime: 10 * time.Second,
			},
		},
	}
	
	// 从环境变量覆盖配置
	if baseURL := os.Getenv("E2E_BASE_URL"); baseURL != "" {
		suite.config.BaseURL = baseURL
	}
	
	if headless := os.Getenv("E2E_HEADLESS"); headless == "false" {
		suite.config.Headless = false
	}
}

// setupLogger 设置日志
func (suite *E2ETestSuite) setupLogger() {
	suite.logger = logger.NewLogger("e2e-test", "info")
}

// setupWebDriver 设置 WebDriver
func (suite *E2ETestSuite) setupWebDriver() {
	// 启动 Selenium 服务
	opts := []selenium.ServiceOption{}
	selenium.SetDebug(false)
	
	service, err := selenium.NewChromeDriverService("./chromedriver", 9515, opts...)
	suite.Require().NoError(err, "启动 ChromeDriver 服务失败")
	suite.service = service
	
	// 配置浏览器选项
	caps := selenium.Capabilities{"browserName": suite.config.BrowserName}
	
	if suite.config.BrowserName == "chrome" {
		chromeCaps := chrome.Capabilities{
			Path: "",
			Args: []string{
				"--no-sandbox",
				"--disable-dev-shm-usage",
				"--disable-gpu",
				"--disable-extensions",
				"--disable-background-timer-throttling",
				"--disable-renderer-backgrounding",
				"--disable-backgrounding-occluded-windows",
				fmt.Sprintf("--window-size=%s", suite.config.WindowSize),
			},
		}
		
		if suite.config.Headless {
			chromeCaps.Args = append(chromeCaps.Args, "--headless")
		}
		
		caps.AddChrome(chromeCaps)
	}
	
	// 创建 WebDriver
	driver, err := selenium.NewRemote(caps, fmt.Sprintf("http://localhost:%d/wd/hub", 9515))
	suite.Require().NoError(err, "创建 WebDriver 失败")
	suite.driver = driver
	
	// 设置隐式等待
	err = suite.driver.SetImplicitWaitTimeout(suite.config.ImplicitWait)
	suite.Require().NoError(err, "设置隐式等待失败")
	
	// 设置页面加载超时
	err = suite.driver.SetPageLoadTimeout(suite.config.PageLoadWait)
	suite.Require().NoError(err, "设置页面加载超时失败")
	
	suite.logger.Info("WebDriver 初始化完成", "browser", suite.config.BrowserName, "headless", suite.config.Headless)
}

// startServices 启动服务
func (suite *E2ETestSuite) startServices() {
	for _, serviceConfig := range suite.config.Services {
		suite.startService(serviceConfig)
	}
}

// startService 启动单个服务
func (suite *E2ETestSuite) startService(config ServiceConfig) {
	suite.logger.Info("启动服务", "name", config.Name, "command", config.Command)
	
	// 创建命令
	cmd := exec.Command(config.Command, config.Args...)
	
	// 设置工作目录
	if config.WorkDir != "" {
		cmd.Dir = config.WorkDir
	}
	
	// 设置环境变量
	cmd.Env = os.Environ()
	for key, value := range config.Env {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", key, value))
	}
	
	// 启动进程
	err := cmd.Start()
	suite.Require().NoError(err, "启动服务失败: %s", config.Name)
	
	// 保存进程信息
	suite.services[config.Name] = &ServiceProcess{
		Config:  config,
		Process: cmd,
		Started: true,
	}
	
	// 等待服务启动
	suite.waitForService(config)
	
	suite.logger.Info("服务启动成功", "name", config.Name, "pid", cmd.Process.Pid)
}

// waitForService 等待服务启动
func (suite *E2ETestSuite) waitForService(config ServiceConfig) {
	if config.HealthCheck == "" {
		// 如果没有健康检查URL，只等待固定时间
		time.Sleep(config.StartupTime)
		return
	}
	
	// 等待健康检查通过
	timeout := time.After(config.StartupTime)
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-timeout:
			suite.Fail("服务启动超时", "service: %s, health_check: %s", config.Name, config.HealthCheck)
			return
		case <-ticker.C:
			resp, err := http.Get(config.HealthCheck)
			if err == nil && resp.StatusCode == http.StatusOK {
				resp.Body.Close()
				suite.logger.Info("服务健康检查通过", "name", config.Name)
				return
			}
			if resp != nil {
				resp.Body.Close()
			}
		}
	}
}

// stopServices 停止所有服务
func (suite *E2ETestSuite) stopServices() {
	for name, service := range suite.services {
		if service.Started && service.Process != nil {
			suite.logger.Info("停止服务", "name", name, "pid", service.Process.Process.Pid)
			
			// 尝试优雅停止
			if err := service.Process.Process.Kill(); err != nil {
				suite.logger.Error("停止服务失败", "name", name, "error", err)
			}
			
			// 等待进程结束
			service.Process.Wait()
			service.Started = false
		}
	}
}

// createTestDirectories 创建测试目录
func (suite *E2ETestSuite) createTestDirectories() {
	dirs := []string{
		suite.config.ScreenshotDir,
		suite.config.VideoDir,
		suite.config.TestDataDir,
	}
	
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			suite.logger.Error("创建测试目录失败", "dir", dir, "error", err)
		}
	}
}

// resetBrowserState 重置浏览器状态
func (suite *E2ETestSuite) resetBrowserState() {
	if suite.driver == nil {
		return
	}
	
	// 删除所有 cookies
	suite.driver.DeleteAllCookies()
	
	// 清除本地存储
	suite.driver.ExecuteScript("localStorage.clear();", nil)
	suite.driver.ExecuteScript("sessionStorage.clear();", nil)
}

// prepareTestData 准备测试数据
func (suite *E2ETestSuite) prepareTestData() {
	// 子类可以重写此方法来准备特定的测试数据
}

// cleanupTestData 清理测试数据
func (suite *E2ETestSuite) cleanupTestData() {
	// 子类可以重写此方法来清理特定的测试数据
}

// takeScreenshot 截图
func (suite *E2ETestSuite) takeScreenshot(testName string) {
	if suite.driver == nil {
		return
	}
	
	screenshot, err := suite.driver.Screenshot()
	if err != nil {
		suite.logger.Error("截图失败", "test", testName, "error", err)
		return
	}
	
	filename := fmt.Sprintf("%s/%s_%d.png", 
		suite.config.ScreenshotDir, 
		testName, 
		time.Now().Unix())
	
	if err := os.WriteFile(filename, screenshot, 0644); err != nil {
		suite.logger.Error("保存截图失败", "file", filename, "error", err)
		return
	}
	
	suite.logger.Info("截图已保存", "file", filename)
}

// PageHelper 页面操作辅助工具
type PageHelper struct {
	driver selenium.WebDriver
	logger logger.Logger
}

// NewPageHelper 创建页面辅助工具
func NewPageHelper(driver selenium.WebDriver, logger logger.Logger) *PageHelper {
	return &PageHelper{
		driver: driver,
		logger: logger,
	}
}

// NavigateTo 导航到指定页面
func (ph *PageHelper) NavigateTo(url string) error {
	ph.logger.Info("导航到页面", "url", url)
	return ph.driver.Get(url)
}

// WaitForElement 等待元素出现
func (ph *PageHelper) WaitForElement(selector string, timeout time.Duration) (selenium.WebElement, error) {
	ph.logger.Debug("等待元素", "selector", selector, "timeout", timeout)
	
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		element, err := ph.driver.FindElement(selenium.ByCSSSelector, selector)
		if err == nil {
			return element, nil
		}
		time.Sleep(100 * time.Millisecond)
	}
	
	return nil, fmt.Errorf("等待元素超时: %s", selector)
}

// ClickElement 点击元素
func (ph *PageHelper) ClickElement(selector string) error {
	element, err := ph.WaitForElement(selector, 10*time.Second)
	if err != nil {
		return fmt.Errorf("查找元素失败: %w", err)
	}
	
	ph.logger.Debug("点击元素", "selector", selector)
	return element.Click()
}

// InputText 输入文本
func (ph *PageHelper) InputText(selector, text string) error {
	element, err := ph.WaitForElement(selector, 10*time.Second)
	if err != nil {
		return fmt.Errorf("查找元素失败: %w", err)
	}
	
	// 清空输入框
	element.Clear()
	
	ph.logger.Debug("输入文本", "selector", selector, "text", text)
	return element.SendKeys(text)
}

// GetText 获取元素文本
func (ph *PageHelper) GetText(selector string) (string, error) {
	element, err := ph.WaitForElement(selector, 10*time.Second)
	if err != nil {
		return "", fmt.Errorf("查找元素失败: %w", err)
	}
	
	return element.Text()
}

// AssertElementExists 断言元素存在
func (ph *PageHelper) AssertElementExists(t *testing.T, selector string) {
	_, err := ph.WaitForElement(selector, 5*time.Second)
	assert.NoError(t, err, "元素应该存在: %s", selector)
}

// AssertElementNotExists 断言元素不存在
func (ph *PageHelper) AssertElementNotExists(t *testing.T, selector string) {
	_, err := ph.driver.FindElement(selenium.ByCSSSelector, selector)
	assert.Error(t, err, "元素不应该存在: %s", selector)
}

// AssertPageTitle 断言页面标题
func (ph *PageHelper) AssertPageTitle(t *testing.T, expectedTitle string) {
	title, err := ph.driver.Title()
	assert.NoError(t, err, "获取页面标题失败")
	assert.Equal(t, expectedTitle, title, "页面标题不匹配")
}

// AssertCurrentURL 断言当前URL
func (ph *PageHelper) AssertCurrentURL(t *testing.T, expectedURL string) {
	currentURL, err := ph.driver.CurrentURL()
	assert.NoError(t, err, "获取当前URL失败")
	assert.Equal(t, expectedURL, currentURL, "当前URL不匹配")
}

// DefaultE2EConfig 默认端到端测试配置
func DefaultE2EConfig() *E2EConfig {
	return &E2EConfig{
		BrowserName:   "chrome",
		Headless:      true,
		WindowSize:    "1920,1080",
		ImplicitWait:  10 * time.Second,
		PageLoadWait:  30 * time.Second,
		BaseURL:       "http://localhost:3000",
		APIBaseURL:    "http://localhost:8080",
		TestTimeout:   5 * time.Minute,
		ScreenshotDir: "test-results/screenshots",
		VideoDir:      "test-results/videos",
		EnableVideo:   false,
		TestDataDir:   "test-data",
		CleanupData:   true,
	}
}
