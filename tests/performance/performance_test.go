package performance

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"paas-platform/pkg/logger"
)

// PerformanceTestSuite 性能测试套件
type PerformanceTestSuite struct {
	suite.Suite
	logger     logger.Logger
	baseURL    string
	httpClient *http.Client
	authToken  string
}

// TestMetrics 测试指标
type TestMetrics struct {
	TotalRequests    int64         `json:"total_requests"`
	SuccessRequests  int64         `json:"success_requests"`
	FailedRequests   int64         `json:"failed_requests"`
	TotalDuration    time.Duration `json:"total_duration"`
	MinResponseTime  time.Duration `json:"min_response_time"`
	MaxResponseTime  time.Duration `json:"max_response_time"`
	AvgResponseTime  time.Duration `json:"avg_response_time"`
	P95ResponseTime  time.Duration `json:"p95_response_time"`
	P99ResponseTime  time.Duration `json:"p99_response_time"`
	RequestsPerSec   float64       `json:"requests_per_sec"`
	ErrorRate        float64       `json:"error_rate"`
	ResponseTimes    []time.Duration
	mutex            sync.RWMutex
}

// LoadTestConfig 负载测试配置
type LoadTestConfig struct {
	ConcurrentUsers int           `json:"concurrent_users"`
	Duration        time.Duration `json:"duration"`
	RampUpTime      time.Duration `json:"ramp_up_time"`
	RequestDelay    time.Duration `json:"request_delay"`
	Endpoint        string        `json:"endpoint"`
	Method          string        `json:"method"`
	Body            interface{}   `json:"body"`
	Headers         map[string]string
}

// SetupSuite 测试套件初始化
func (suite *PerformanceTestSuite) SetupSuite() {
	// 初始化日志
	suite.logger = logger.NewLogger("performance-test", "info")

	// 设置基础 URL
	suite.baseURL = getEnv("PERF_API_URL", "http://localhost:8080/api/v1")

	// 初始化 HTTP 客户端
	suite.httpClient = &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// 获取认证令牌
	suite.authenticateTestUser()

	suite.logger.Info("性能测试套件初始化完成")
}

// TearDownSuite 测试套件清理
func (suite *PerformanceTestSuite) TearDownSuite() {
	suite.logger.Info("性能测试套件清理完成")
}

// authenticateTestUser 认证测试用户
func (suite *PerformanceTestSuite) authenticateTestUser() {
	loginData := map[string]string{
		"username": "perftest",
		"password": "perftest123",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := suite.httpClient.Post(
		suite.baseURL+"/auth/login",
		"application/json",
		bytes.NewBuffer(jsonData),
	)

	if err != nil || resp.StatusCode != http.StatusOK {
		// 如果登录失败，创建测试用户
		suite.createTestUser()
		// 重新尝试登录
		resp, err = suite.httpClient.Post(
			suite.baseURL+"/auth/login",
			"application/json",
			bytes.NewBuffer(jsonData),
		)
	}

	suite.Require().NoError(err, "登录请求失败")
	defer resp.Body.Close()

	var loginResp struct {
		Code int `json:"code"`
		Data struct {
			Token string `json:"token"`
		} `json:"data"`
	}

	err = json.NewDecoder(resp.Body).Decode(&loginResp)
	suite.Require().NoError(err, "解析登录响应失败")

	suite.authToken = loginResp.Data.Token
	suite.Require().NotEmpty(suite.authToken, "获取认证令牌失败")
}

// createTestUser 创建测试用户
func (suite *PerformanceTestSuite) createTestUser() {
	userData := map[string]interface{}{
		"username":  "perftest",
		"email":     "<EMAIL>",
		"password":  "perftest123",
		"real_name": "性能测试用户",
		"roles":     []string{"developer"},
	}

	jsonData, _ := json.Marshal(userData)
	suite.httpClient.Post(
		suite.baseURL+"/users",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
}

// makeRequest 发送 HTTP 请求
func (suite *PerformanceTestSuite) makeRequest(config LoadTestConfig) (time.Duration, error) {
	var reqBody *bytes.Buffer
	if config.Body != nil {
		jsonData, err := json.Marshal(config.Body)
		if err != nil {
			return 0, err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	var req *http.Request
	var err error
	if reqBody != nil {
		req, err = http.NewRequest(config.Method, suite.baseURL+config.Endpoint, reqBody)
	} else {
		req, err = http.NewRequest(config.Method, suite.baseURL+config.Endpoint, nil)
	}

	if err != nil {
		return 0, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	for key, value := range config.Headers {
		req.Header.Set(key, value)
	}

	start := time.Now()
	resp, err := suite.httpClient.Do(req)
	duration := time.Since(start)

	if err != nil {
		return duration, err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return duration, fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	return duration, nil
}

// runLoadTest 运行负载测试
func (suite *PerformanceTestSuite) runLoadTest(config LoadTestConfig) *TestMetrics {
	metrics := &TestMetrics{
		MinResponseTime: time.Hour, // 初始化为一个很大的值
		ResponseTimes:   make([]time.Duration, 0),
	}

	ctx, cancel := context.WithTimeout(context.Background(), config.Duration)
	defer cancel()

	var wg sync.WaitGroup
	startTime := time.Now()

	// 计算每个用户的启动间隔
	userStartInterval := config.RampUpTime / time.Duration(config.ConcurrentUsers)

	// 启动并发用户
	for i := 0; i < config.ConcurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()

			// 渐进式启动用户
			time.Sleep(time.Duration(userID) * userStartInterval)

			for {
				select {
				case <-ctx.Done():
					return
				default:
					// 发送请求
					duration, err := suite.makeRequest(config)

					// 更新指标
					metrics.mutex.Lock()
					atomic.AddInt64(&metrics.TotalRequests, 1)
					metrics.ResponseTimes = append(metrics.ResponseTimes, duration)

					if err != nil {
						atomic.AddInt64(&metrics.FailedRequests, 1)
					} else {
						atomic.AddInt64(&metrics.SuccessRequests, 1)
					}

					if duration < metrics.MinResponseTime {
						metrics.MinResponseTime = duration
					}
					if duration > metrics.MaxResponseTime {
						metrics.MaxResponseTime = duration
					}
					metrics.mutex.Unlock()

					// 请求间隔
					if config.RequestDelay > 0 {
						time.Sleep(config.RequestDelay)
					}
				}
			}
		}(i)
	}

	wg.Wait()
	metrics.TotalDuration = time.Since(startTime)

	// 计算统计指标
	suite.calculateMetrics(metrics)

	return metrics
}

// calculateMetrics 计算测试指标
func (suite *PerformanceTestSuite) calculateMetrics(metrics *TestMetrics) {
	if metrics.TotalRequests == 0 {
		return
	}

	// 计算平均响应时间
	var totalTime time.Duration
	for _, rt := range metrics.ResponseTimes {
		totalTime += rt
	}
	metrics.AvgResponseTime = totalTime / time.Duration(len(metrics.ResponseTimes))

	// 计算 P95 和 P99
	if len(metrics.ResponseTimes) > 0 {
		// 简单排序计算百分位数
		times := make([]time.Duration, len(metrics.ResponseTimes))
		copy(times, metrics.ResponseTimes)

		// 冒泡排序（简单实现）
		for i := 0; i < len(times); i++ {
			for j := 0; j < len(times)-1-i; j++ {
				if times[j] > times[j+1] {
					times[j], times[j+1] = times[j+1], times[j]
				}
			}
		}

		p95Index := int(float64(len(times)) * 0.95)
		p99Index := int(float64(len(times)) * 0.99)

		if p95Index < len(times) {
			metrics.P95ResponseTime = times[p95Index]
		}
		if p99Index < len(times) {
			metrics.P99ResponseTime = times[p99Index]
		}
	}

	// 计算 QPS
	metrics.RequestsPerSec = float64(metrics.TotalRequests) / metrics.TotalDuration.Seconds()

	// 计算错误率
	metrics.ErrorRate = float64(metrics.FailedRequests) / float64(metrics.TotalRequests) * 100
}

// TestAPIEndpointsLoad 测试 API 端点负载
func (suite *PerformanceTestSuite) TestAPIEndpointsLoad() {
	suite.T().Log("测试 API 端点负载性能")

	testCases := []struct {
		name   string
		config LoadTestConfig
	}{
		{
			name: "获取应用列表",
			config: LoadTestConfig{
				ConcurrentUsers: 10,
				Duration:        30 * time.Second,
				RampUpTime:      5 * time.Second,
				RequestDelay:    100 * time.Millisecond,
				Endpoint:        "/applications",
				Method:          "GET",
			},
		},
		{
			name: "获取用户列表",
			config: LoadTestConfig{
				ConcurrentUsers: 5,
				Duration:        20 * time.Second,
				RampUpTime:      3 * time.Second,
				RequestDelay:    200 * time.Millisecond,
				Endpoint:        "/users",
				Method:          "GET",
			},
		},
		{
			name: "获取流水线列表",
			config: LoadTestConfig{
				ConcurrentUsers: 8,
				Duration:        25 * time.Second,
				RampUpTime:      4 * time.Second,
				RequestDelay:    150 * time.Millisecond,
				Endpoint:        "/pipelines",
				Method:          "GET",
			},
		},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			suite.logger.Info(fmt.Sprintf("开始负载测试: %s", tc.name))

			metrics := suite.runLoadTest(tc.config)

			// 验证性能指标
			assert.Greater(t, metrics.RequestsPerSec, 1.0, "QPS 应该大于 1")
			assert.Less(t, metrics.ErrorRate, 5.0, "错误率应该小于 5%")
			assert.Less(t, metrics.AvgResponseTime, 2*time.Second, "平均响应时间应该小于 2 秒")
			assert.Less(t, metrics.P95ResponseTime, 5*time.Second, "P95 响应时间应该小于 5 秒")

			// 输出测试结果
			suite.logger.Info(fmt.Sprintf("负载测试结果 - %s:", tc.name))
			suite.logger.Info(fmt.Sprintf("  总请求数: %d", metrics.TotalRequests))
			suite.logger.Info(fmt.Sprintf("  成功请求: %d", metrics.SuccessRequests))
			suite.logger.Info(fmt.Sprintf("  失败请求: %d", metrics.FailedRequests))
			suite.logger.Info(fmt.Sprintf("  QPS: %.2f", metrics.RequestsPerSec))
			suite.logger.Info(fmt.Sprintf("  错误率: %.2f%%", metrics.ErrorRate))
			suite.logger.Info(fmt.Sprintf("  平均响应时间: %v", metrics.AvgResponseTime))
			suite.logger.Info(fmt.Sprintf("  P95 响应时间: %v", metrics.P95ResponseTime))
			suite.logger.Info(fmt.Sprintf("  P99 响应时间: %v", metrics.P99ResponseTime))
		})
	}
}

// TestStressTest 压力测试
func (suite *PerformanceTestSuite) TestStressTest() {
	suite.T().Log("运行系统压力测试")

	// 高并发压力测试配置
	config := LoadTestConfig{
		ConcurrentUsers: 50,
		Duration:        60 * time.Second,
		RampUpTime:      10 * time.Second,
		RequestDelay:    50 * time.Millisecond,
		Endpoint:        "/applications",
		Method:          "GET",
	}

	suite.logger.Info("开始压力测试...")
	metrics := suite.runLoadTest(config)

	// 压力测试的验证标准更宽松
	suite.Assert().Greater(metrics.RequestsPerSec, 5.0, "压力测试 QPS 应该大于 5")
	suite.Assert().Less(metrics.ErrorRate, 10.0, "压力测试错误率应该小于 10%")
	suite.Assert().Less(metrics.AvgResponseTime, 5*time.Second, "压力测试平均响应时间应该小于 5 秒")

	// 输出压力测试结果
	suite.logger.Info("压力测试结果:")
	suite.logger.Info(fmt.Sprintf("  总请求数: %d", metrics.TotalRequests))
	suite.logger.Info(fmt.Sprintf("  成功请求: %d", metrics.SuccessRequests))
	suite.logger.Info(fmt.Sprintf("  失败请求: %d", metrics.FailedRequests))
	suite.logger.Info(fmt.Sprintf("  QPS: %.2f", metrics.RequestsPerSec))
	suite.logger.Info(fmt.Sprintf("  错误率: %.2f%%", metrics.ErrorRate))
	suite.logger.Info(fmt.Sprintf("  平均响应时间: %v", metrics.AvgResponseTime))
	suite.logger.Info(fmt.Sprintf("  最小响应时间: %v", metrics.MinResponseTime))
	suite.logger.Info(fmt.Sprintf("  最大响应时间: %v", metrics.MaxResponseTime))
	suite.logger.Info(fmt.Sprintf("  P95 响应时间: %v", metrics.P95ResponseTime))
	suite.logger.Info(fmt.Sprintf("  P99 响应时间: %v", metrics.P99ResponseTime))
}

// TestConcurrentOperations 测试并发操作
func (suite *PerformanceTestSuite) TestConcurrentOperations() {
	suite.T().Log("测试并发操作性能")

	// 测试并发创建应用
	suite.T().Run("并发创建应用", func(t *testing.T) {
		concurrency := 10
		var wg sync.WaitGroup
		var successCount int64
		var errorCount int64

		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				appData := map[string]interface{}{
					"name":           fmt.Sprintf("perf-test-app-%d", id),
					"description":    fmt.Sprintf("性能测试应用 %d", id),
					"repository_url": "https://github.com/test/perf-app.git",
					"branch":         "main",
					"environment":    "development",
				}

				_, err := suite.makeRequest(LoadTestConfig{
					Method:   "POST",
					Endpoint: "/applications",
					Body:     appData,
				})

				if err != nil {
					atomic.AddInt64(&errorCount, 1)
				} else {
					atomic.AddInt64(&successCount, 1)
				}
			}(i)
		}

		wg.Wait()

		suite.logger.Info(fmt.Sprintf("并发创建应用结果: 成功 %d, 失败 %d", successCount, errorCount))
		assert.Greater(t, successCount, int64(concurrency/2), "成功创建的应用应该超过一半")
	})

	// 测试并发用户操作
	suite.T().Run("并发用户操作", func(t *testing.T) {
		concurrency := 5
		var wg sync.WaitGroup
		var successCount int64

		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				// 获取当前用户信息
				_, err := suite.makeRequest(LoadTestConfig{
					Method:   "GET",
					Endpoint: "/auth/me",
				})

				if err == nil {
					atomic.AddInt64(&successCount, 1)
				}
			}(i)
		}

		wg.Wait()

		suite.logger.Info(fmt.Sprintf("并发用户操作结果: 成功 %d", successCount))
		assert.Equal(t, int64(concurrency), successCount, "所有并发用户操作都应该成功")
	})
}

// TestMemoryUsage 测试内存使用
func (suite *PerformanceTestSuite) TestMemoryUsage() {
	suite.T().Log("测试内存使用情况")

	// 创建大量请求来测试内存使用
	config := LoadTestConfig{
		ConcurrentUsers: 20,
		Duration:        30 * time.Second,
		RampUpTime:      5 * time.Second,
		RequestDelay:    10 * time.Millisecond,
		Endpoint:        "/applications",
		Method:          "GET",
	}

	metrics := suite.runLoadTest(config)

	// 验证在高频请求下系统仍能正常响应
	suite.Assert().Greater(metrics.RequestsPerSec, 10.0, "高频请求下 QPS 应该大于 10")
	suite.Assert().Less(metrics.ErrorRate, 5.0, "高频请求下错误率应该小于 5%")

	suite.logger.Info("内存使用测试完成")
}

// getEnv 获取环境变量
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// TestPerformanceSuite 运行性能测试套件
func TestPerformanceSuite(t *testing.T) {
	// 检查是否启用性能测试
	if os.Getenv("PERFORMANCE_TEST") != "true" {
		t.Skip("跳过性能测试，设置 PERFORMANCE_TEST=true 来运行")
	}

	suite.Run(t, new(PerformanceTestSuite))
}
