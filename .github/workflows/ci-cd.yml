# GitHub Actions CI/CD 工作流
# 实现自动化构建、测试和部署

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  NAMESPACE: paas
  GO_VERSION: '1.21'
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  code-quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        args: --timeout=10m

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: web/package-lock.json

    - name: Install frontend dependencies
      run: |
        cd web
        npm ci

    - name: Run frontend linting
      run: |
        cd web
        npm run lint
        npm run type-check

  # 后端测试
  test-backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_pass
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Run unit tests
      env:
        DATABASE_URL: postgres://test_user:test_pass@localhost:5432/test_db?sslmode=disable
        REDIS_URL: redis://localhost:6379
      run: |
        make test-unit
        make test-integration

    - name: Generate coverage report
      run: go tool cover -html=coverage.out -o coverage.html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: backend
        name: backend-coverage

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: backend-coverage
        path: coverage.html

  # 前端测试
  test-frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: web/package-lock.json

    - name: Install dependencies
      run: |
        cd web
        npm ci

    - name: Run unit tests
      run: |
        cd web
        npm run test:unit
        npm run test:coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./web/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-coverage
        path: web/coverage/

  # 端到端测试
  e2e-test:
    name: E2E Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: web/package-lock.json

    - name: Start services
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30

    - name: Install dependencies
      run: |
        cd web
        npm ci

    - name: Run E2E tests
      run: |
        cd web
        npm run test:e2e:headless

    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-results
        path: |
          web/cypress/screenshots/
          web/cypress/videos/

    - name: Stop services
      if: always()
      run: docker-compose -f docker-compose.test.yml down

  # 构建镜像
  build:
    name: Build Images
    runs-on: ubuntu-latest
    needs: [code-quality, test-backend, test-frontend]
    if: github.event_name == 'push'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}

    - name: Build and push backend services
      run: |
        for service in api-gateway user-service app-manager ci-cd-service monitoring-service notification-service load-balancer; do
          if [ -d "services/$service" ]; then
            echo "Building $service..."
            docker buildx build \
              --platform linux/amd64,linux/arm64 \
              --build-arg VERSION=${{ github.sha }} \
              --build-arg BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ) \
              --build-arg GIT_COMMIT=${{ github.sha }} \
              -t ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/$service:${{ github.sha }} \
              -t ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/$service:latest \
              -f docker/Dockerfile.go-service \
              --push \
              services/$service
          fi
        done

    - name: Build and push frontend
      uses: docker/build-push-action@v5
      with:
        context: ./web
        file: ./docker/Dockerfile.frontend
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/web:${{ github.sha }}
          ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/web:latest
        build-args: |
          VERSION=${{ github.sha }}
          BUILD_TIME=${{ steps.meta.outputs.labels['org.opencontainers.image.created'] }}
          GIT_COMMIT=${{ github.sha }}

  # 安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push'
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.NAMESPACE }}/api-gateway:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到开发环境
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    environment:
      name: development
      url: https://dev.paas.example.com
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Helm
      uses: azure/setup-helm@v3
      with:
        version: 'latest'

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG_DEV }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

    - name: Deploy to development
      run: |
        helm upgrade --install paas-dev ./helm/paas \
          --namespace paas-dev \
          --create-namespace \
          --set image.tag=${{ github.sha }} \
          --set image.registry=${{ env.REGISTRY }} \
          --set environment=development \
          --set replicaCount=1 \
          --set ingress.hosts[0].host=dev.paas.example.com \
          --wait \
          --timeout=10m

  # 部署到预发布环境
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging.paas.example.com
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Helm
      uses: azure/setup-helm@v3
      with:
        version: 'latest'

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG_STAGING }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

    - name: Deploy to staging
      run: |
        helm upgrade --install paas-staging ./helm/paas \
          --namespace paas-staging \
          --create-namespace \
          --set image.tag=${{ github.sha }} \
          --set image.registry=${{ env.REGISTRY }} \
          --set environment=staging \
          --set replicaCount=2 \
          --set ingress.hosts[0].host=staging.paas.example.com \
          --wait \
          --timeout=10m

  # 部署到生产环境
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production
      url: https://paas.example.com
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG_PROD }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

    - name: Deploy to production
      run: |
        chmod +x scripts/deploy-production.sh
        export VERSION=${{ github.sha }}
        export ENVIRONMENT=production
        export NAMESPACE=paas-prod
        export DEPLOYMENT_TYPE=rolling
        ./scripts/deploy-production.sh

  # 通知
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-staging, deploy-prod]
    if: always()
    steps:
    - name: Notify Slack on success
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: '✅ PaaS 平台部署成功'
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Slack on failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: '❌ PaaS 平台部署失败'
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
