package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"runtime"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/net"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	metrics     *SystemMetrics
	httpMetrics *HTTPMetrics
	mutex       sync.RWMutex
	startTime   time.Time
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	Timestamp    time.Time `json:"timestamp"`
	CPUUsage     float64   `json:"cpu_usage"`
	MemoryUsage  float64   `json:"memory_usage"`
	MemoryTotal  uint64    `json:"memory_total"`
	MemoryUsed   uint64    `json:"memory_used"`
	DiskUsage    float64   `json:"disk_usage"`
	DiskTotal    uint64    `json:"disk_total"`
	DiskUsed     uint64    `json:"disk_used"`
	NetworkIn    uint64    `json:"network_in"`
	NetworkOut   uint64    `json:"network_out"`
	Goroutines   int       `json:"goroutines"`
	GCPauseTotal uint64    `json:"gc_pause_total"`
	HeapAlloc    uint64    `json:"heap_alloc"`
	HeapSys      uint64    `json:"heap_sys"`
}

// HTTPMetrics HTTP 指标
type HTTPMetrics struct {
	TotalRequests    int64             `json:"total_requests"`
	RequestsPerSec   float64           `json:"requests_per_sec"`
	AvgResponseTime  time.Duration     `json:"avg_response_time"`
	StatusCodes      map[int]int64     `json:"status_codes"`
	EndpointMetrics  map[string]*EndpointMetric `json:"endpoint_metrics"`
	mutex            sync.RWMutex
}

// EndpointMetric 端点指标
type EndpointMetric struct {
	Path            string        `json:"path"`
	Method          string        `json:"method"`
	Count           int64         `json:"count"`
	TotalTime       time.Duration `json:"total_time"`
	AvgTime         time.Duration `json:"avg_time"`
	MinTime         time.Duration `json:"min_time"`
	MaxTime         time.Duration `json:"max_time"`
	ErrorCount      int64         `json:"error_count"`
	LastAccessed    time.Time     `json:"last_accessed"`
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{
		metrics: &SystemMetrics{},
		httpMetrics: &HTTPMetrics{
			StatusCodes:     make(map[int]int64),
			EndpointMetrics: make(map[string]*EndpointMetric),
		},
		startTime: time.Now(),
	}
}

// collectSystemMetrics 收集系统指标
func (pm *PerformanceMonitor) collectSystemMetrics() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.metrics.Timestamp = time.Now()

	// CPU 使用率
	if cpuPercent, err := cpu.Percent(time.Second, false); err == nil && len(cpuPercent) > 0 {
		pm.metrics.CPUUsage = cpuPercent[0]
	}

	// 内存使用情况
	if memInfo, err := mem.VirtualMemory(); err == nil {
		pm.metrics.MemoryUsage = memInfo.UsedPercent
		pm.metrics.MemoryTotal = memInfo.Total
		pm.metrics.MemoryUsed = memInfo.Used
	}

	// 磁盘使用情况
	if diskInfo, err := disk.Usage("/"); err == nil {
		pm.metrics.DiskUsage = diskInfo.UsedPercent
		pm.metrics.DiskTotal = diskInfo.Total
		pm.metrics.DiskUsed = diskInfo.Used
	}

	// 网络统计
	if netStats, err := net.IOCounters(false); err == nil && len(netStats) > 0 {
		pm.metrics.NetworkIn = netStats[0].BytesRecv
		pm.metrics.NetworkOut = netStats[0].BytesSent
	}

	// Go 运行时指标
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	pm.metrics.Goroutines = runtime.NumGoroutine()
	pm.metrics.GCPauseTotal = memStats.PauseTotalNs
	pm.metrics.HeapAlloc = memStats.HeapAlloc
	pm.metrics.HeapSys = memStats.HeapSys
}

// recordHTTPRequest 记录 HTTP 请求
func (pm *PerformanceMonitor) recordHTTPRequest(method, path string, statusCode int, duration time.Duration) {
	pm.httpMetrics.mutex.Lock()
	defer pm.httpMetrics.mutex.Unlock()

	// 更新总请求数
	pm.httpMetrics.TotalRequests++

	// 更新状态码统计
	pm.httpMetrics.StatusCodes[statusCode]++

	// 更新端点指标
	key := fmt.Sprintf("%s %s", method, path)
	if metric, exists := pm.httpMetrics.EndpointMetrics[key]; exists {
		metric.Count++
		metric.TotalTime += duration
		metric.AvgTime = metric.TotalTime / time.Duration(metric.Count)
		
		if duration < metric.MinTime {
			metric.MinTime = duration
		}
		if duration > metric.MaxTime {
			metric.MaxTime = duration
		}
		
		if statusCode >= 400 {
			metric.ErrorCount++
		}
		
		metric.LastAccessed = time.Now()
	} else {
		pm.httpMetrics.EndpointMetrics[key] = &EndpointMetric{
			Path:         path,
			Method:       method,
			Count:        1,
			TotalTime:    duration,
			AvgTime:      duration,
			MinTime:      duration,
			MaxTime:      duration,
			ErrorCount:   0,
			LastAccessed: time.Now(),
		}
		
		if statusCode >= 400 {
			pm.httpMetrics.EndpointMetrics[key].ErrorCount = 1
		}
	}

	// 计算 QPS
	elapsed := time.Since(pm.startTime).Seconds()
	if elapsed > 0 {
		pm.httpMetrics.RequestsPerSec = float64(pm.httpMetrics.TotalRequests) / elapsed
	}
}

// HTTPMiddleware HTTP 中间件
func (pm *PerformanceMonitor) HTTPMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		
		// 创建响应写入器包装器来捕获状态码
		wrapper := &responseWriter{ResponseWriter: w, statusCode: 200}
		
		// 执行下一个处理器
		next.ServeHTTP(wrapper, r)
		
		// 记录请求指标
		duration := time.Since(start)
		pm.recordHTTPRequest(r.Method, r.URL.Path, wrapper.statusCode, duration)
	})
}

// responseWriter 响应写入器包装器
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// startMetricsCollection 启动指标收集
func (pm *PerformanceMonitor) startMetricsCollection() {
	ticker := time.NewTicker(5 * time.Second)
	go func() {
		for range ticker.C {
			pm.collectSystemMetrics()
		}
	}()
}

// getSystemMetrics 获取系统指标
func (pm *PerformanceMonitor) getSystemMetrics(w http.ResponseWriter, r *http.Request) {
	pm.mutex.RLock()
	metrics := *pm.metrics
	pm.mutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "success",
		"data":   metrics,
	})
}

// getHTTPMetrics 获取 HTTP 指标
func (pm *PerformanceMonitor) getHTTPMetrics(w http.ResponseWriter, r *http.Request) {
	pm.httpMetrics.mutex.RLock()
	metrics := *pm.httpMetrics
	pm.httpMetrics.mutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "success",
		"data":   metrics,
	})
}

// getAllMetrics 获取所有指标
func (pm *PerformanceMonitor) getAllMetrics(w http.ResponseWriter, r *http.Request) {
	pm.mutex.RLock()
	systemMetrics := *pm.metrics
	pm.mutex.RUnlock()

	pm.httpMetrics.mutex.RLock()
	httpMetrics := *pm.httpMetrics
	pm.httpMetrics.mutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "success",
		"data": map[string]interface{}{
			"system": systemMetrics,
			"http":   httpMetrics,
			"uptime": time.Since(pm.startTime).Seconds(),
		},
	})
}

// getHealthCheck 健康检查
func (pm *PerformanceMonitor) getHealthCheck(w http.ResponseWriter, r *http.Request) {
	pm.mutex.RLock()
	cpuUsage := pm.metrics.CPUUsage
	memoryUsage := pm.metrics.MemoryUsage
	pm.mutex.RUnlock()

	status := "healthy"
	if cpuUsage > 90 || memoryUsage > 90 {
		status = "unhealthy"
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": status,
		"data": map[string]interface{}{
			"cpu_usage":    cpuUsage,
			"memory_usage": memoryUsage,
			"uptime":       time.Since(pm.startTime).Seconds(),
		},
	})
}

// resetMetrics 重置指标
func (pm *PerformanceMonitor) resetMetrics(w http.ResponseWriter, r *http.Request) {
	pm.httpMetrics.mutex.Lock()
	pm.httpMetrics.TotalRequests = 0
	pm.httpMetrics.RequestsPerSec = 0
	pm.httpMetrics.StatusCodes = make(map[int]int64)
	pm.httpMetrics.EndpointMetrics = make(map[string]*EndpointMetric)
	pm.httpMetrics.mutex.Unlock()

	pm.startTime = time.Now()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":  "success",
		"message": "指标已重置",
	})
}

// setupRoutes 设置路由
func (pm *PerformanceMonitor) setupRoutes() *mux.Router {
	router := mux.NewRouter()

	// API 路由
	api := router.PathPrefix("/api/v1").Subrouter()
	api.HandleFunc("/metrics/system", pm.getSystemMetrics).Methods("GET")
	api.HandleFunc("/metrics/http", pm.getHTTPMetrics).Methods("GET")
	api.HandleFunc("/metrics/all", pm.getAllMetrics).Methods("GET")
	api.HandleFunc("/health", pm.getHealthCheck).Methods("GET")
	api.HandleFunc("/metrics/reset", pm.resetMetrics).Methods("POST")

	// 静态文件服务（监控面板）
	router.PathPrefix("/").Handler(http.FileServer(http.Dir("./web/dist/")))

	return router
}

// generateDashboard 生成监控面板 HTML
func generateDashboard() {
	dashboardHTML := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaaS 平台性能监控</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #333; }
        .metric-label { color: #666; margin-top: 5px; }
        .chart-container { height: 300px; margin-top: 20px; }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-danger { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PaaS 平台性能监控</h1>
            <p>实时系统性能和 HTTP 请求监控</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="cpu-usage">-</div>
                <div class="metric-label">CPU 使用率 (%)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="memory-usage">-</div>
                <div class="metric-label">内存使用率 (%)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="requests-per-sec">-</div>
                <div class="metric-label">每秒请求数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="total-requests">-</div>
                <div class="metric-label">总请求数</div>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="performance-chart"></canvas>
        </div>
    </div>

    <script>
        // 初始化图表
        const ctx = document.getElementById('performance-chart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU 使用率',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }, {
                    label: '内存使用率',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 更新指标
        function updateMetrics() {
            fetch('/api/v1/metrics/all')
                .then(response => response.json())
                .then(data => {
                    const system = data.data.system;
                    const http = data.data.http;
                    
                    document.getElementById('cpu-usage').textContent = system.cpu_usage.toFixed(1);
                    document.getElementById('memory-usage').textContent = system.memory_usage.toFixed(1);
                    document.getElementById('requests-per-sec').textContent = http.requests_per_sec.toFixed(1);
                    document.getElementById('total-requests').textContent = http.total_requests;
                    
                    // 更新图表
                    const now = new Date().toLocaleTimeString();
                    chart.data.labels.push(now);
                    chart.data.datasets[0].data.push(system.cpu_usage);
                    chart.data.datasets[1].data.push(system.memory_usage);
                    
                    // 保持最近 20 个数据点
                    if (chart.data.labels.length > 20) {
                        chart.data.labels.shift();
                        chart.data.datasets[0].data.shift();
                        chart.data.datasets[1].data.shift();
                    }
                    
                    chart.update();
                })
                .catch(error => console.error('Error fetching metrics:', error));
        }

        // 每 5 秒更新一次
        setInterval(updateMetrics, 5000);
        updateMetrics(); // 立即执行一次
    </script>
</body>
</html>`

	// 创建 web 目录
	os.MkdirAll("web/dist", 0755)
	
	// 写入 HTML 文件
	if err := os.WriteFile("web/dist/index.html", []byte(dashboardHTML), 0644); err != nil {
		log.Printf("创建监控面板失败: %v", err)
	}
}

func main() {
	// 创建性能监控器
	monitor := NewPerformanceMonitor()

	// 生成监控面板
	generateDashboard()

	// 启动指标收集
	monitor.startMetricsCollection()

	// 设置路由
	router := monitor.setupRoutes()

	// 添加中间件
	router.Use(monitor.HTTPMiddleware)

	// 获取端口
	port := os.Getenv("MONITOR_PORT")
	if port == "" {
		port = "9090"
	}

	fmt.Printf("性能监控器启动在端口 %s\n", port)
	fmt.Printf("监控面板: http://localhost:%s\n", port)
	fmt.Printf("API 端点: http://localhost:%s/api/v1/metrics/all\n", port)

	log.Fatal(http.ListenAndServe(":"+port, router))
}
