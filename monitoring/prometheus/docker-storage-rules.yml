# Docker存储监控告警规则
# 用于监控Docker镜像存储使用情况和触发告警

groups:
  # Docker存储告警组
  - name: docker.storage.rules
    rules:
      # Docker根目录磁盘使用率告警
      - alert: DockerRootDiskUsageHigh
        expr: (1 - (node_filesystem_avail_bytes{mountpoint="/var/lib/docker"} / node_filesystem_size_bytes{mountpoint="/var/lib/docker"})) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: storage
          component: docker
        annotations:
          summary: "Docker根目录磁盘使用率过高"
          description: "Docker根目录 /var/lib/docker 磁盘使用率为 {{ $value }}%，超过 80% 阈值"
          runbook_url: "https://docs.docker.com/config/pruning/"

      - alert: DockerRootDiskUsageCritical
        expr: (1 - (node_filesystem_avail_bytes{mountpoint="/var/lib/docker"} / node_filesystem_size_bytes{mountpoint="/var/lib/docker"})) * 100 > 90
        for: 2m
        labels:
          severity: critical
          category: storage
          component: docker
        annotations:
          summary: "Docker根目录磁盘使用率严重过高"
          description: "Docker根目录 /var/lib/docker 磁盘使用率为 {{ $value }}%，超过 90% 阈值，需要立即清理"
          runbook_url: "https://docs.docker.com/config/pruning/"

      # Docker镜像数量告警
      - alert: DockerImageCountHigh
        expr: docker_images_total > 100
        for: 10m
        labels:
          severity: warning
          category: storage
          component: docker
        annotations:
          summary: "Docker镜像数量过多"
          description: "Docker镜像总数为 {{ $value }}，超过 100 个，建议清理未使用的镜像"

      - alert: DockerImageCountCritical
        expr: docker_images_total > 200
        for: 5m
        labels:
          severity: critical
          category: storage
          component: docker
        annotations:
          summary: "Docker镜像数量严重过多"
          description: "Docker镜像总数为 {{ $value }}，超过 200 个，需要立即清理"

      # Docker容器数量告警
      - alert: DockerContainerCountHigh
        expr: docker_containers_total > 150
        for: 10m
        labels:
          severity: warning
          category: storage
          component: docker
        annotations:
          summary: "Docker容器数量过多"
          description: "Docker容器总数为 {{ $value }}，超过 150 个，建议清理停止的容器"

      # Docker数据卷数量告警
      - alert: DockerVolumeCountHigh
        expr: docker_volumes_total > 50
        for: 15m
        labels:
          severity: warning
          category: storage
          component: docker
        annotations:
          summary: "Docker数据卷数量过多"
          description: "Docker数据卷总数为 {{ $value }}，超过 50 个，建议清理未使用的数据卷"

      # Docker网络数量告警
      - alert: DockerNetworkCountHigh
        expr: docker_networks_total > 30
        for: 15m
        labels:
          severity: warning
          category: storage
          component: docker
        annotations:
          summary: "Docker网络数量过多"
          description: "Docker网络总数为 {{ $value }}，超过 30 个，建议清理未使用的网络"

      # Docker存储空间增长率告警
      - alert: DockerStorageGrowthRateHigh
        expr: increase(node_filesystem_size_bytes{mountpoint="/var/lib/docker"}[1h]) > 1073741824  # 1GB
        for: 30m
        labels:
          severity: warning
          category: storage
          component: docker
        annotations:
          summary: "Docker存储空间增长过快"
          description: "Docker存储空间在过去1小时内增长了 {{ $value | humanize1024 }}，增长速度过快"

      # Docker daemon不可用告警
      - alert: DockerDaemonDown
        expr: up{job="docker"} == 0
        for: 1m
        labels:
          severity: critical
          category: service
          component: docker
        annotations:
          summary: "Docker daemon不可用"
          description: "Docker daemon在实例 {{ $labels.instance }} 上不可用"

      # Docker镜像拉取失败率告警
      - alert: DockerImagePullFailureRateHigh
        expr: rate(docker_image_pulls_failed_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          category: docker
          component: registry
        annotations:
          summary: "Docker镜像拉取失败率过高"
          description: "Docker镜像拉取失败率为 {{ $value | humanizePercentage }}，可能存在网络或仓库问题"

      # BuildKit缓存大小告警
      - alert: BuildKitCacheSizeHigh
        expr: buildkit_cache_size_bytes > 21474836480  # 20GB
        for: 10m
        labels:
          severity: warning
          category: storage
          component: buildkit
        annotations:
          summary: "BuildKit缓存大小过大"
          description: "BuildKit缓存大小为 {{ $value | humanize1024 }}，超过 20GB，建议清理"

      # Docker镜像层数过多告警
      - alert: DockerImageLayersHigh
        expr: docker_image_layers_total > 1000
        for: 15m
        labels:
          severity: warning
          category: storage
          component: docker
        annotations:
          summary: "Docker镜像层数过多"
          description: "Docker镜像总层数为 {{ $value }}，超过 1000 层，可能影响性能"

  # Docker性能告警组
  - name: docker.performance.rules
    rules:
      # Docker daemon CPU使用率告警
      - alert: DockerDaemonCPUUsageHigh
        expr: rate(process_cpu_seconds_total{job="docker"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          category: performance
          component: docker
        annotations:
          summary: "Docker daemon CPU使用率过高"
          description: "Docker daemon CPU使用率为 {{ $value }}%，超过 80%"

      # Docker daemon内存使用率告警
      - alert: DockerDaemonMemoryUsageHigh
        expr: process_resident_memory_bytes{job="docker"} / 1024 / 1024 / 1024 > 2
        for: 10m
        labels:
          severity: warning
          category: performance
          component: docker
        annotations:
          summary: "Docker daemon内存使用过高"
          description: "Docker daemon内存使用为 {{ $value }}GB，超过 2GB"

      # Docker容器重启频率告警
      - alert: DockerContainerRestartRateHigh
        expr: rate(docker_container_restarts_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          category: stability
          component: docker
        annotations:
          summary: "Docker容器重启频率过高"
          description: "Docker容器重启频率为 {{ $value }}/秒，可能存在稳定性问题"

  # Docker安全告警组
  - name: docker.security.rules
    rules:
      # 特权容器运行告警
      - alert: PrivilegedContainerRunning
        expr: docker_container_privileged_total > 0
        for: 1m
        labels:
          severity: warning
          category: security
          component: docker
        annotations:
          summary: "检测到特权容器运行"
          description: "当前有 {{ $value }} 个特权容器在运行，存在安全风险"

      # Root用户容器告警
      - alert: RootUserContainerRunning
        expr: docker_container_root_user_total > 5
        for: 5m
        labels:
          severity: warning
          category: security
          component: docker
        annotations:
          summary: "过多容器以root用户运行"
          description: "当前有 {{ $value }} 个容器以root用户运行，建议使用非特权用户"

  # Docker清理建议规则
  - name: docker.cleanup.rules
    rules:
      # 悬空镜像清理建议
      - alert: DanglingImagesCleanupNeeded
        expr: docker_images_dangling_total > 10
        for: 30m
        labels:
          severity: info
          category: maintenance
          component: docker
        annotations:
          summary: "建议清理悬空镜像"
          description: "当前有 {{ $value }} 个悬空镜像，建议执行清理操作"

      # 停止容器清理建议
      - alert: StoppedContainersCleanupNeeded
        expr: docker_containers_stopped_total > 20
        for: 1h
        labels:
          severity: info
          category: maintenance
          component: docker
        annotations:
          summary: "建议清理停止的容器"
          description: "当前有 {{ $value }} 个停止的容器，建议执行清理操作"

      # 未使用数据卷清理建议
      - alert: UnusedVolumesCleanupNeeded
        expr: docker_volumes_unused_total > 10
        for: 2h
        labels:
          severity: info
          category: maintenance
          component: docker
        annotations:
          summary: "建议清理未使用的数据卷"
          description: "当前有 {{ $value }} 个未使用的数据卷，建议执行清理操作"

  # Docker镜像仓库告警组
  - name: docker.registry.rules
    rules:
      # 镜像仓库连接失败告警
      - alert: DockerRegistryConnectionFailed
        expr: docker_registry_connection_failures_total > 0
        for: 5m
        labels:
          severity: warning
          category: connectivity
          component: registry
        annotations:
          summary: "Docker镜像仓库连接失败"
          description: "Docker镜像仓库连接失败次数为 {{ $value }}，请检查网络和仓库状态"

      # 镜像推送失败告警
      - alert: DockerImagePushFailureRateHigh
        expr: rate(docker_image_pushes_failed_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          category: docker
          component: registry
        annotations:
          summary: "Docker镜像推送失败率过高"
          description: "Docker镜像推送失败率为 {{ $value | humanizePercentage }}，可能存在权限或网络问题"

      # 镜像仓库存储空间告警
      - alert: DockerRegistryStorageUsageHigh
        expr: docker_registry_storage_usage_percent > 85
        for: 10m
        labels:
          severity: warning
          category: storage
          component: registry
        annotations:
          summary: "Docker镜像仓库存储使用率过高"
          description: "Docker镜像仓库存储使用率为 {{ $value }}%，超过 85%，需要清理或扩容"

  # Docker构建告警组
  - name: docker.build.rules
    rules:
      # 构建失败率告警
      - alert: DockerBuildFailureRateHigh
        expr: rate(docker_builds_failed_total[10m]) > 0.2
        for: 10m
        labels:
          severity: warning
          category: build
          component: docker
        annotations:
          summary: "Docker构建失败率过高"
          description: "Docker构建失败率为 {{ $value | humanizePercentage }}，请检查构建配置和依赖"

      # 构建时间过长告警
      - alert: DockerBuildDurationHigh
        expr: docker_build_duration_seconds > 1800  # 30分钟
        for: 5m
        labels:
          severity: warning
          category: performance
          component: docker
        annotations:
          summary: "Docker构建时间过长"
          description: "Docker构建时间为 {{ $value | humanizeDuration }}，超过 30 分钟，建议优化构建过程"

      # BuildKit缓存命中率低告警
      - alert: BuildKitCacheHitRateLow
        expr: buildkit_cache_hit_rate < 0.5
        for: 15m
        labels:
          severity: info
          category: performance
          component: buildkit
        annotations:
          summary: "BuildKit缓存命中率较低"
          description: "BuildKit缓存命中率为 {{ $value | humanizePercentage }}，低于 50%，建议优化Dockerfile"
