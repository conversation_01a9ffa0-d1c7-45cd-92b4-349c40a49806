# Prometheus 告警规则配置
# 为 PaaS 平台提供全面的监控告警

groups:
  # 系统级告警
  - name: system.rules
    rules:
      # 节点资源告警
      - alert: NodeCPUUsageHigh
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "节点 CPU 使用率过高"
          description: "节点 {{ $labels.instance }} CPU 使用率为 {{ $value }}%，超过 80% 阈值"

      - alert: NodeCPUUsageCritical
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "节点 CPU 使用率严重过高"
          description: "节点 {{ $labels.instance }} CPU 使用率为 {{ $value }}%，超过 90% 阈值"

      - alert: NodeMemoryUsageHigh
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "节点内存使用率过高"
          description: "节点 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过 80% 阈值"

      - alert: NodeMemoryUsageCritical
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 90
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "节点内存使用率严重过高"
          description: "节点 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过 90% 阈值"

      - alert: NodeDiskUsageHigh
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "节点磁盘使用率过高"
          description: "节点 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率为 {{ $value }}%，超过 80% 阈值"

      - alert: NodeDiskUsageCritical
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 90
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "节点磁盘使用率严重过高"
          description: "节点 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率为 {{ $value }}%，超过 90% 阈值"

      - alert: NodeDown
        expr: up{job="node-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "节点宕机"
          description: "节点 {{ $labels.instance }} 已宕机超过 1 分钟"

  # Kubernetes 集群告警
  - name: kubernetes.rules
    rules:
      - alert: KubernetesPodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
          category: kubernetes
        annotations:
          summary: "Pod 频繁重启"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} 在过去 15 分钟内重启了 {{ $value }} 次"

      - alert: KubernetesPodNotReady
        expr: kube_pod_status_ready{condition="false"} == 1
        for: 10m
        labels:
          severity: warning
          category: kubernetes
        annotations:
          summary: "Pod 未就绪"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} 已超过 10 分钟未就绪"

      - alert: KubernetesDeploymentReplicasMismatch
        expr: kube_deployment_spec_replicas != kube_deployment_status_available_replicas
        for: 10m
        labels:
          severity: warning
          category: kubernetes
        annotations:
          summary: "Deployment 副本数不匹配"
          description: "Deployment {{ $labels.namespace }}/{{ $labels.deployment }} 期望副本数 {{ $labels.spec_replicas }}，实际可用副本数 {{ $labels.available_replicas }}"

      - alert: KubernetesNodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
          category: kubernetes
        annotations:
          summary: "Kubernetes 节点未就绪"
          description: "节点 {{ $labels.node }} 已超过 5 分钟未就绪"

      - alert: KubernetesPersistentVolumeClaimPending
        expr: kube_persistentvolumeclaim_status_phase{phase="Pending"} == 1
        for: 10m
        labels:
          severity: warning
          category: kubernetes
        annotations:
          summary: "PVC 处于 Pending 状态"
          description: "PVC {{ $labels.namespace }}/{{ $labels.persistentvolumeclaim }} 已超过 10 分钟处于 Pending 状态"

  # 应用服务告警
  - name: application.rules
    rules:
      - alert: ApplicationHighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "应用错误率过高"
          description: "服务 {{ $labels.service }} 错误率为 {{ $value }}%，超过 5% 阈值"

      - alert: ApplicationCriticalErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 10
        for: 2m
        labels:
          severity: critical
          category: application
        annotations:
          summary: "应用错误率严重过高"
          description: "服务 {{ $labels.service }} 错误率为 {{ $value }}%，超过 10% 阈值"

      - alert: ApplicationHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "应用响应时间过长"
          description: "服务 {{ $labels.service }} 95% 响应时间为 {{ $value }}s，超过 1s 阈值"

      - alert: ApplicationCriticalLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 3
        for: 2m
        labels:
          severity: critical
          category: application
        annotations:
          summary: "应用响应时间严重过长"
          description: "服务 {{ $labels.service }} 95% 响应时间为 {{ $value }}s，超过 3s 阈值"

      - alert: ApplicationLowThroughput
        expr: rate(http_requests_total[5m]) < 1
        for: 10m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "应用吞吐量过低"
          description: "服务 {{ $labels.service }} 吞吐量为 {{ $value }} req/s，低于 1 req/s 阈值"

      - alert: ApplicationDown
        expr: up{job=~"paas-.*"} == 0
        for: 1m
        labels:
          severity: critical
          category: application
        annotations:
          summary: "应用服务宕机"
          description: "服务 {{ $labels.job }} 已宕机超过 1 分钟"

  # 数据库告警
  - name: database.rules
    rules:
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "PostgreSQL 数据库宕机"
          description: "PostgreSQL 实例 {{ $labels.instance }} 已宕机超过 1 分钟"

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL 连接数过高"
          description: "PostgreSQL 实例 {{ $labels.instance }} 连接使用率为 {{ $value }}%，超过 80% 阈值"

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL 查询效率低"
          description: "PostgreSQL 实例 {{ $labels.instance }} 查询效率为 {{ $value }}，低于 0.1 阈值"

      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "Redis 服务宕机"
          description: "Redis 实例 {{ $labels.instance }} 已宕机超过 1 分钟"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Redis 内存使用率过高"
          description: "Redis 实例 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过 80% 阈值"

      - alert: RedisHighConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Redis 连接数过高"
          description: "Redis 实例 {{ $labels.instance }} 连接数为 {{ $value }}，超过 100 阈值"

  # 网络告警
  - name: network.rules
    rules:
      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) + rate(node_network_transmit_bytes_total[5m]) > 100 * 1024 * 1024
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "网络流量过高"
          description: "节点 {{ $labels.instance }} 网络接口 {{ $labels.device }} 流量为 {{ $value | humanize }}B/s，超过 100MB/s 阈值"

      - alert: HighNetworkErrors
        expr: rate(node_network_receive_errs_total[5m]) + rate(node_network_transmit_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "网络错误率过高"
          description: "节点 {{ $labels.instance }} 网络接口 {{ $labels.device }} 错误率为 {{ $value }} errors/s，超过 10 errors/s 阈值"

      - alert: LoadBalancerDown
        expr: up{job="load-balancer"} == 0
        for: 1m
        labels:
          severity: critical
          category: network
        annotations:
          summary: "负载均衡器宕机"
          description: "负载均衡器 {{ $labels.instance }} 已宕机超过 1 分钟"

  # 安全告警
  - name: security.rules
    rules:
      - alert: HighFailedLoginAttempts
        expr: rate(failed_login_attempts_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "登录失败次数过多"
          description: "来自 {{ $labels.source_ip }} 的登录失败次数为 {{ $value }} attempts/s，超过 10 attempts/s 阈值"

      - alert: SuspiciousAPIActivity
        expr: rate(http_requests_total{status="401"}[5m]) > 50
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "可疑 API 活动"
          description: "服务 {{ $labels.service }} 401 错误率为 {{ $value }} req/s，超过 50 req/s 阈值"

      - alert: CertificateExpiringSoon
        expr: (ssl_certificate_expiry_seconds - time()) / 86400 < 30
        for: 1h
        labels:
          severity: warning
          category: security
        annotations:
          summary: "SSL 证书即将过期"
          description: "域名 {{ $labels.domain }} 的 SSL 证书将在 {{ $value }} 天后过期"

      - alert: CertificateExpired
        expr: ssl_certificate_expiry_seconds - time() < 0
        for: 1m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "SSL 证书已过期"
          description: "域名 {{ $labels.domain }} 的 SSL 证书已过期"

  # 业务指标告警
  - name: business.rules
    rules:
      - alert: LowUserRegistrations
        expr: rate(user_registrations_total[1h]) < 1
        for: 2h
        labels:
          severity: warning
          category: business
        annotations:
          summary: "用户注册量过低"
          description: "过去 1 小时用户注册量为 {{ $value }} users/h，低于 1 users/h 阈值"

      - alert: HighApplicationCreationFailures
        expr: rate(application_creation_failures_total[5m]) / rate(application_creation_attempts_total[5m]) * 100 > 10
        for: 10m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "应用创建失败率过高"
          description: "应用创建失败率为 {{ $value }}%，超过 10% 阈值"

      - alert: DeploymentFailureSpike
        expr: rate(deployment_failures_total[5m]) > 5
        for: 5m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "部署失败激增"
          description: "部署失败率为 {{ $value }} failures/s，超过 5 failures/s 阈值"

  # 存储告警
  - name: storage.rules
    rules:
      - alert: PersistentVolumeUsageHigh
        expr: (kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: storage
        annotations:
          summary: "持久卷使用率过高"
          description: "PV {{ $labels.persistentvolumeclaim }} 使用率为 {{ $value }}%，超过 80% 阈值"

      - alert: PersistentVolumeUsageCritical
        expr: (kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes) * 100 > 90
        for: 2m
        labels:
          severity: critical
          category: storage
        annotations:
          summary: "持久卷使用率严重过高"
          description: "PV {{ $labels.persistentvolumeclaim }} 使用率为 {{ $value }}%，超过 90% 阈值"

      - alert: PersistentVolumeFull
        expr: (kubelet_volume_stats_used_bytes / kubelet_volume_stats_capacity_bytes) * 100 > 95
        for: 1m
        labels:
          severity: critical
          category: storage
        annotations:
          summary: "持久卷几乎满了"
          description: "PV {{ $labels.persistentvolumeclaim }} 使用率为 {{ $value }}%，超过 95% 阈值"
