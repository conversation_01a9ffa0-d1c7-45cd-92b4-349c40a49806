{"dashboard": {"id": null, "title": "Docker存储监控仪表板", "tags": ["docker", "storage", "paas"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"docker\"}, instance)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true, "current": {"text": "All", "value": "$__all"}}]}, "panels": [], "annotations": {"list": [{"name": "Docker清理事件", "datasource": "Prometheus", "enable": true, "expr": "changes(docker_cleanup_last_run_timestamp[1h]) > 0", "iconColor": "rgba(0, 211, 255, 1)", "titleFormat": "Docker清理执行", "textFormat": "在 {{$labels.instance}} 上执行了Docker清理操作"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "schemaVersion": 27, "version": 1, "weekStart": ""}}