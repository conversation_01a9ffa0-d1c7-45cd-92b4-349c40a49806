# CI/CD 流水线规范文档

## 1. 构建配置文件格式

### 1.1 .paas-ci.yaml 配置文件

每个代码仓库可以包含 `.paas-ci.yaml` 文件来定义自定义的构建流水线。

```yaml
# .paas-ci.yaml 示例
apiVersion: v1
kind: Pipeline
metadata:
  name: my-app-pipeline
  description: "我的应用构建流水线"

spec:
  # 触发条件
  triggers:
    - type: push                # 推送触发
      branches: ["main", "develop"]
    - type: pull_request        # PR 触发
      branches: ["main"]
    - type: tag                 # 标签触发
      pattern: "v*"
    - type: schedule            # 定时触发
      cron: "0 2 * * *"         # 每天凌晨2点

  # 环境变量
  env:
    NODE_ENV: production
    BUILD_ENV: ci
    
  # 密钥引用
  secrets:
    - name: docker-registry
      keys: ["username", "password"]
    - name: api-keys
      keys: ["github_token"]

  # 构建阶段
  stages:
    # 准备阶段
    - name: prepare
      steps:
        - name: checkout
          type: git_checkout
          config:
            depth: 1            # 浅克隆深度
            submodules: false   # 是否包含子模块
            
        - name: setup-cache
          type: cache
          config:
            key: "deps-{{ checksum 'package-lock.json' }}"
            paths: ["node_modules"]
            
    # 构建阶段
    - name: build
      depends_on: [prepare]
      steps:
        - name: install-deps
          type: shell
          config:
            commands:
              - "npm ci"
              
        - name: lint
          type: shell
          config:
            commands:
              - "npm run lint"
            allow_failure: true  # 允许失败
            
        - name: test
          type: shell
          config:
            commands:
              - "npm run test"
            artifacts:           # 构建产物
              paths: ["coverage/"]
              expire_in: "1 week"
              
        - name: build-app
          type: shell
          config:
            commands:
              - "npm run build"
            artifacts:
              paths: ["dist/"]
              
    # 镜像构建阶段
    - name: docker
      depends_on: [build]
      steps:
        - name: build-image
          type: docker_build
          config:
            dockerfile: Dockerfile
            context: .
            tags:
              - "${DOCKER_REGISTRY}/${APP_NAME}:${BUILD_NUMBER}"
              - "${DOCKER_REGISTRY}/${APP_NAME}:latest"
            build_args:
              NODE_ENV: production
              
        - name: push-image
          type: docker_push
          config:
            tags:
              - "${DOCKER_REGISTRY}/${APP_NAME}:${BUILD_NUMBER}"
              - "${DOCKER_REGISTRY}/${APP_NAME}:latest"
            registry:
              url: "${DOCKER_REGISTRY}"
              username: "${DOCKER_USERNAME}"
              password: "${DOCKER_PASSWORD}"
              
    # 部署阶段
    - name: deploy
      depends_on: [docker]
      when:
        branch: main            # 仅在 main 分支部署
      steps:
        - name: deploy-staging
          type: deploy
          config:
            environment: staging
            image: "${DOCKER_REGISTRY}/${APP_NAME}:${BUILD_NUMBER}"
            
        - name: smoke-test
          type: shell
          config:
            commands:
              - "curl -f http://staging.example.com/health"
              
        - name: deploy-production
          type: deploy
          config:
            environment: production
            image: "${DOCKER_REGISTRY}/${APP_NAME}:${BUILD_NUMBER}"
            strategy: rolling     # 部署策略: rolling, blue_green, canary
            approval: manual      # 需要手动批准
            
  # 通知配置
  notifications:
    - type: webhook
      url: "https://hooks.slack.com/services/..."
      events: ["build_success", "build_failure", "deploy_success", "deploy_failure"]
      
    - type: email
      recipients: ["<EMAIL>"]
      events: ["build_failure", "deploy_failure"]

  # 缓存配置
  cache:
    - key: "node-modules-{{ checksum 'package-lock.json' }}"
      paths: ["node_modules/"]
    - key: "pip-cache-{{ checksum 'requirements.txt' }}"
      paths: [".pip-cache/"]

  # 服务依赖 (用于集成测试)
  services:
    - name: postgres
      image: postgres:15
      env:
        POSTGRES_DB: test_db
        POSTGRES_USER: test
        POSTGRES_PASSWORD: test123
        
    - name: redis
      image: redis:7-alpine
```

### 1.2 Python 应用 CI/CD 配置示例

```yaml
apiVersion: v1
kind: Pipeline
metadata:
  name: python-flask-pipeline

spec:
  triggers:
    - type: push
      branches: ["main", "develop"]

  env:
    PYTHON_ENV: production
    
  stages:
    - name: prepare
      steps:
        - name: checkout
          type: git_checkout
          
        - name: setup-python
          type: setup_python
          config:
            version: "3.11"
            
    - name: build
      steps:
        - name: install-deps
          type: shell
          config:
            commands:
              - "pip install -r requirements.txt"
              
        - name: lint
          type: shell
          config:
            commands:
              - "flake8 ."
              - "black --check ."
              
        - name: test
          type: shell
          config:
            commands:
              - "pytest --cov=. --cov-report=xml"
            artifacts:
              paths: ["coverage.xml", "htmlcov/"]
              
    - name: docker
      steps:
        - name: build-image
          type: docker_build
          config:
            dockerfile: Dockerfile
            tags:
              - "${DOCKER_REGISTRY}/${APP_NAME}:${BUILD_NUMBER}"
```

## 2. 构建阶段定义

### 2.1 标准构建阶段

1. **准备阶段 (Prepare)**
   - 代码检出
   - 环境设置
   - 缓存恢复

2. **构建阶段 (Build)**
   - 依赖安装
   - 代码检查 (Lint)
   - 单元测试
   - 应用构建

3. **镜像阶段 (Docker)**
   - Docker 镜像构建
   - 镜像推送
   - 安全扫描

4. **部署阶段 (Deploy)**
   - 环境部署
   - 集成测试
   - 生产部署

### 2.2 步骤类型定义

#### git_checkout
```yaml
- name: checkout
  type: git_checkout
  config:
    repository: "${GIT_REPO}"
    branch: "${GIT_BRANCH}"
    depth: 1
    submodules: false
```

#### shell
```yaml
- name: run-tests
  type: shell
  config:
    commands:
      - "npm test"
    working_directory: "/workspace"
    timeout: "10m"
    allow_failure: false
```

#### docker_build
```yaml
- name: build-image
  type: docker_build
  config:
    dockerfile: Dockerfile
    context: .
    tags: ["${REGISTRY}/${APP}:${BUILD_NUMBER}"]
    build_args:
      NODE_ENV: production
```

#### docker_push
```yaml
- name: push-image
  type: docker_push
  config:
    tags: ["${REGISTRY}/${APP}:${BUILD_NUMBER}"]
    registry:
      url: "${DOCKER_REGISTRY}"
      username: "${DOCKER_USERNAME}"
      password: "${DOCKER_PASSWORD}"
```

#### deploy
```yaml
- name: deploy-app
  type: deploy
  config:
    environment: production
    image: "${REGISTRY}/${APP}:${BUILD_NUMBER}"
    strategy: rolling
    timeout: "5m"
```

## 3. 环境变量和密钥管理

### 3.1 内置环境变量

系统自动提供以下环境变量：

```bash
# 构建信息
BUILD_NUMBER=123              # 构建编号
BUILD_ID=build-uuid-123       # 构建ID
BUILD_URL=http://...          # 构建URL

# Git 信息
GIT_REPO=https://git.example.com/user/repo.git
GIT_BRANCH=main
GIT_COMMIT=abc123def456
GIT_COMMIT_MESSAGE="feat: 添加新功能"
GIT_AUTHOR_NAME="张三"
GIT_AUTHOR_EMAIL="<EMAIL>"

# 应用信息
APP_NAME=my-app
APP_ID=app-uuid-123
APP_VERSION=1.0.0

# 平台信息
PAAS_PLATFORM=paas-platform
PAAS_VERSION=1.0.0
DOCKER_REGISTRY=localhost:5000

# 环境信息
ENVIRONMENT=staging           # staging, production
TENANT_ID=tenant-123
USER_ID=user-123
```

### 3.2 密钥管理

密钥通过配置服务统一管理，支持以下类型：

- **文本密钥**: API Key, Token 等
- **文件密钥**: 证书文件, SSH 密钥等
- **键值对密钥**: 数据库连接信息等

```yaml
# 密钥引用示例
secrets:
  - name: docker-registry      # 密钥名称
    keys: ["username", "password"]  # 引用的密钥键
    
  - name: ssh-key
    keys: ["private_key"]
    mount_path: "/root/.ssh"   # 文件密钥挂载路径
```

## 4. 部署策略

### 4.1 滚动更新 (Rolling Update)

逐步替换旧版本实例，确保服务不中断：

```yaml
strategy:
  type: rolling
  config:
    max_unavailable: 1         # 最大不可用实例数
    max_surge: 1               # 最大超出实例数
    timeout: "5m"              # 部署超时
```

### 4.2 蓝绿部署 (Blue-Green)

创建完整的新环境，验证后切换流量：

```yaml
strategy:
  type: blue_green
  config:
    auto_promote: false        # 是否自动切换
    promotion_timeout: "10m"   # 切换超时
    rollback_timeout: "5m"     # 回滚超时
```

### 4.3 金丝雀发布 (Canary)

小流量验证新版本：

```yaml
strategy:
  type: canary
  config:
    steps:
      - traffic: 10            # 10% 流量
        duration: "5m"
      - traffic: 50            # 50% 流量
        duration: "10m"
      - traffic: 100           # 100% 流量
    auto_promote: true
    analysis:                  # 自动分析
      success_rate: 99.5       # 成功率阈值
      avg_response_time: 500   # 平均响应时间阈值(ms)
```

## 5. 构建调度器设计

### 5.1 任务队列

使用 Redis 作为任务队列，支持：

- 任务优先级
- 任务重试
- 任务超时
- 并发控制

### 5.2 构建节点

支持多个构建节点，实现负载均衡：

```yaml
# 构建节点配置
build_nodes:
  - name: node-1
    capacity: 5               # 并发构建数
    labels:                   # 节点标签
      os: linux
      arch: amd64
      type: standard
      
  - name: node-2
    capacity: 3
    labels:
      os: linux
      arch: arm64
      type: gpu
```

### 5.3 构建隔离

每个构建任务在独立的容器中执行，确保：

- 环境隔离
- 资源限制
- 安全隔离
- 清理机制

## 6. 集成组件

### 6.1 Gitea 集成

#### Webhook 配置

```json
{
  "url": "http://paas-platform:8082/api/v1/webhooks/gitea",
  "content_type": "json",
  "secret": "webhook-secret-key",
  "events": [
    "push",
    "pull_request",
    "tag"
  ]
}
```

#### API 调用

```go
// Gitea API 客户端
type GiteaClient struct {
    BaseURL string
    Token   string
}

// 获取仓库信息
func (c *GiteaClient) GetRepository(owner, repo string) (*Repository, error)

// 获取提交信息
func (c *GiteaClient) GetCommit(owner, repo, sha string) (*Commit, error)

// 创建 Webhook
func (c *GiteaClient) CreateWebhook(owner, repo string, webhook *Webhook) error
```

### 6.2 Docker 集成

#### 镜像构建

```go
// Docker 客户端
type DockerClient struct {
    Client *docker.Client
}

// 构建镜像
func (c *DockerClient) BuildImage(ctx context.Context, buildContext io.Reader, options BuildOptions) error

// 推送镜像
func (c *DockerClient) PushImage(ctx context.Context, image string, options PushOptions) error

// 运行容器
func (c *DockerClient) RunContainer(ctx context.Context, config *ContainerConfig) (*Container, error)
```

#### Dockerfile 模板

**Node.js 应用模板**:
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["npm", "start"]
```

**Python 应用模板**:
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非 root 用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# 启动应用
CMD ["python", "app.py"]
```

## 7. 构建日志和状态管理

### 7.1 日志格式

构建日志采用结构化格式：

```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "INFO",
  "stage": "build",
  "step": "install-deps",
  "message": "正在安装依赖包...",
  "build_id": "build-123",
  "app_id": "app-456"
}
```

### 7.2 状态流转

```
PENDING -> RUNNING -> SUCCESS/FAILED/CANCELED
   │         │           │
   │         │           └─> RETRYING -> RUNNING
   │         │
   │         └─> TIMEOUT -> FAILED
   │
   └─> CANCELED
```

### 7.3 构建产物管理

- 构建日志存储
- 测试报告存储
- 代码覆盖率报告
- 安全扫描报告
- Docker 镜像信息

## 8. 多环境部署

### 8.1 环境定义

```yaml
environments:
  development:
    cluster: dev-cluster
    namespace: dev
    replicas: 1
    resources:
      cpu: "100m"
      memory: "128Mi"
      
  staging:
    cluster: staging-cluster
    namespace: staging
    replicas: 2
    resources:
      cpu: "200m"
      memory: "256Mi"
      
  production:
    cluster: prod-cluster
    namespace: production
    replicas: 3
    resources:
      cpu: "500m"
      memory: "512Mi"
    approval: manual          # 需要手动批准
```

### 8.2 环境变量管理

不同环境使用不同的配置：

```yaml
# 开发环境
env_development:
  DATABASE_URL: "sqlite:///tmp/dev.db"
  LOG_LEVEL: "debug"
  
# 生产环境
env_production:
  DATABASE_URL: "${secret:prod-db.url}"
  LOG_LEVEL: "info"
```

## 9. 构建优化

### 9.1 缓存策略

- 依赖缓存: package.json, requirements.txt
- 构建缓存: Docker 层缓存
- 测试缓存: 测试结果缓存

### 9.2 并行构建

支持阶段内步骤并行执行：

```yaml
- name: parallel-tests
  steps:
    - name: unit-tests
      type: shell
      config:
        commands: ["npm run test:unit"]
      parallel: true
      
    - name: integration-tests
      type: shell
      config:
        commands: ["npm run test:integration"]
      parallel: true
      
    - name: e2e-tests
      type: shell
      config:
        commands: ["npm run test:e2e"]
      parallel: true
```

### 9.3 构建加速

- 使用构建缓存
- 增量构建
- 分布式构建
- 构建节点亲和性
