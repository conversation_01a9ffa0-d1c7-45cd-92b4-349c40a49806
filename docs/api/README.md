# PaaS 平台 API 文档

## 📋 概述

PaaS 平台提供了完整的 RESTful API，支持应用管理、用户认证、CI/CD 流水线、监控等核心功能。本文档详细描述了所有可用的 API 接口。

## 🚀 快速开始

### 基础信息

- **API 基础 URL**: `https://api.paas-platform.com/api/v1`
- **API 版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Bearer Token

### 认证

所有 API 请求都需要在请求头中包含有效的 JWT Token：

```http
Authorization: Bearer <your-jwt-token>
```

### 获取 Token

```http
POST /auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400,
    "user": {
      "id": "1",
      "username": "admin",
      "email": "<EMAIL>",
      "roles": ["admin"]
    }
  }
}
```

## 📊 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-01-15T14:30:25Z"
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "ValidationError",
    "details": [
      {
        "field": "username",
        "message": "用户名不能为空"
      }
    ]
  },
  "timestamp": "2024-01-15T14:30:25Z"
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 🔐 用户认证 API

### 用户登录

```http
POST /auth/login
```

**请求参数**:
```json
{
  "username": "string",     // 用户名
  "password": "string"      // 密码
}
```

### 用户注册

```http
POST /auth/register
```

**请求参数**:
```json
{
  "username": "string",     // 用户名
  "email": "string",        // 邮箱
  "password": "string",     // 密码
  "real_name": "string"     // 真实姓名（可选）
}
```

### 刷新 Token

```http
POST /auth/refresh
```

**请求参数**:
```json
{
  "refresh_token": "string"
}
```

### 用户登出

```http
POST /auth/logout
```

### 获取当前用户信息

```http
GET /auth/me
```

## 👥 用户管理 API

### 获取用户列表

```http
GET /users
```

**查询参数**:
- `page`: 页码（默认: 1）
- `page_size`: 每页数量（默认: 20）
- `search`: 搜索关键词
- `status`: 用户状态（active, disabled, pending）
- `role`: 用户角色

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "users": [
      {
        "id": "1",
        "username": "admin",
        "email": "<EMAIL>",
        "real_name": "系统管理员",
        "status": "active",
        "roles": ["admin"],
        "created_at": "2024-01-01T00:00:00Z",
        "last_login_at": "2024-01-15T14:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

### 创建用户

```http
POST /users
```

**请求参数**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "real_name": "string",
  "phone": "string",
  "department": "string",
  "position": "string",
  "roles": ["string"]
}
```

### 获取用户详情

```http
GET /users/{id}
```

### 更新用户

```http
PUT /users/{id}
```

### 删除用户

```http
DELETE /users/{id}
```

### 重置用户密码

```http
POST /users/{id}/reset-password
```

## 📱 应用管理 API

### 获取应用列表

```http
GET /applications
```

**查询参数**:
- `page`: 页码
- `page_size`: 每页数量
- `search`: 搜索关键词
- `status`: 应用状态（running, stopped, deploying, error）
- `environment`: 环境（development, testing, production）

### 创建应用

```http
POST /applications
```

**请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "repository_url": "string",
  "branch": "string",
  "environment": "string",
  "config": {
    "port": 8080,
    "env_vars": {
      "NODE_ENV": "production"
    },
    "resources": {
      "cpu": "500m",
      "memory": "512Mi"
    }
  }
}
```

### 获取应用详情

```http
GET /applications/{id}
```

### 更新应用

```http
PUT /applications/{id}
```

### 删除应用

```http
DELETE /applications/{id}
```

### 启动应用

```http
POST /applications/{id}/start
```

### 停止应用

```http
POST /applications/{id}/stop
```

### 重启应用

```http
POST /applications/{id}/restart
```

### 获取应用日志

```http
GET /applications/{id}/logs
```

**查询参数**:
- `lines`: 日志行数（默认: 100）
- `since`: 开始时间
- `until`: 结束时间
- `level`: 日志级别

### 获取应用指标

```http
GET /applications/{id}/metrics
```

## 🔄 CI/CD 流水线 API

### 获取流水线列表

```http
GET /pipelines
```

### 创建流水线

```http
POST /pipelines
```

**请求参数**:
```json
{
  "name": "string",
  "app_id": "string",
  "branch": "string",
  "trigger": "string",
  "stages": [
    {
      "name": "string",
      "type": "string",
      "config": {}
    }
  ]
}
```

### 获取流水线详情

```http
GET /pipelines/{id}
```

### 运行流水线

```http
POST /pipelines/{id}/run
```

### 停止流水线

```http
POST /pipelines/{id}/stop
```

### 获取流水线执行历史

```http
GET /pipelines/{id}/executions
```

### 获取流水线执行详情

```http
GET /pipelines/{id}/executions/{execution_id}
```

## 🛠️ 配置管理 API

### 获取配置列表

```http
GET /configs
```

### 创建配置

```http
POST /configs
```

**请求参数**:
```json
{
  "name": "string",
  "app_id": "string",
  "environment": "string",
  "type": "string",
  "content": "string",
  "description": "string"
}
```

### 获取配置详情

```http
GET /configs/{id}
```

### 更新配置

```http
PUT /configs/{id}
```

### 删除配置

```http
DELETE /configs/{id}
```

### 获取配置历史版本

```http
GET /configs/{id}/versions
```

### 回滚配置

```http
POST /configs/{id}/rollback
```

**请求参数**:
```json
{
  "version": "string"
}
```

## 📊 监控 API

### 获取系统指标

```http
GET /monitor/metrics
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "system": {
      "cpu_usage": 45.2,
      "memory_usage": 67.8,
      "disk_usage": 34.5
    },
    "applications": {
      "total": 24,
      "running": 20,
      "stopped": 4
    },
    "requests": {
      "total": 1234567,
      "per_second": 245,
      "error_rate": 0.5
    }
  }
}
```

### 获取告警列表

```http
GET /monitor/alerts
```

### 创建告警规则

```http
POST /monitor/alert-rules
```

### 获取日志

```http
GET /monitor/logs
```

**查询参数**:
- `app_id`: 应用ID
- `level`: 日志级别
- `start_time`: 开始时间
- `end_time`: 结束时间
- `keyword`: 关键词

## 📜 脚本管理 API

### 获取脚本列表

```http
GET /scripts
```

### 创建脚本

```http
POST /scripts
```

**请求参数**:
```json
{
  "name": "string",
  "description": "string",
  "content": "string",
  "type": "string",
  "timeout": 300
}
```

### 执行脚本

```http
POST /scripts/{id}/execute
```

**请求参数**:
```json
{
  "parameters": {
    "key": "value"
  }
}
```

### 获取脚本执行历史

```http
GET /scripts/{id}/executions
```

## 🔔 通知 API

### 获取通知列表

```http
GET /notifications
```

### 发送通知

```http
POST /notifications
```

**请求参数**:
```json
{
  "title": "string",
  "content": "string",
  "type": "string",
  "channels": ["email", "slack"],
  "recipients": ["user1", "user2"]
}
```

## 📝 Webhook API

### 获取 Webhook 列表

```http
GET /webhooks
```

### 创建 Webhook

```http
POST /webhooks
```

**请求参数**:
```json
{
  "name": "string",
  "url": "string",
  "events": ["app.deployed", "pipeline.completed"],
  "secret": "string",
  "active": true
}
```

## 🔍 搜索 API

### 全局搜索

```http
GET /search
```

**查询参数**:
- `q`: 搜索关键词
- `type`: 搜索类型（apps, users, pipelines, configs）
- `limit`: 结果数量限制

## 📈 统计 API

### 获取仪表板统计

```http
GET /stats/dashboard
```

### 获取使用统计

```http
GET /stats/usage
```

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `granularity`: 粒度（hour, day, week, month）

## 🔧 系统管理 API

### 获取系统信息

```http
GET /system/info
```

### 获取系统健康状态

```http
GET /system/health
```

### 系统备份

```http
POST /system/backup
```

### 系统恢复

```http
POST /system/restore
```

## 📚 SDK 和示例

### JavaScript SDK

```javascript
import { PaaSClient } from '@paas-platform/sdk';

const client = new PaaSClient({
  baseURL: 'https://api.paas-platform.com/api/v1',
  token: 'your-jwt-token'
});

// 获取应用列表
const apps = await client.applications.list();

// 创建应用
const newApp = await client.applications.create({
  name: 'my-app',
  repository_url: 'https://github.com/user/repo.git'
});
```

### Python SDK

```python
from paas_platform import PaaSClient

client = PaaSClient(
    base_url='https://api.paas-platform.com/api/v1',
    token='your-jwt-token'
)

# 获取应用列表
apps = client.applications.list()

# 创建应用
new_app = client.applications.create({
    'name': 'my-app',
    'repository_url': 'https://github.com/user/repo.git'
})
```

### Go SDK

```go
package main

import (
    "github.com/paas-platform/go-sdk"
)

func main() {
    client := paas.NewClient(&paas.Config{
        BaseURL: "https://api.paas-platform.com/api/v1",
        Token:   "your-jwt-token",
    })
    
    // 获取应用列表
    apps, err := client.Applications.List()
    
    // 创建应用
    newApp, err := client.Applications.Create(&paas.Application{
        Name:          "my-app",
        RepositoryURL: "https://github.com/user/repo.git",
    })
}
```

## 🚨 错误处理

### 常见错误码

| 错误码 | 错误类型 | 说明 |
|--------|----------|------|
| 1001 | AuthenticationError | 认证失败 |
| 1002 | AuthorizationError | 权限不足 |
| 1003 | ValidationError | 参数验证失败 |
| 1004 | ResourceNotFound | 资源不存在 |
| 1005 | ResourceConflict | 资源冲突 |
| 1006 | RateLimitExceeded | 请求频率超限 |
| 1007 | ServiceUnavailable | 服务不可用 |

### 错误处理示例

```javascript
try {
  const app = await client.applications.create(appData);
} catch (error) {
  if (error.code === 1003) {
    // 处理验证错误
    console.log('验证失败:', error.details);
  } else if (error.code === 1005) {
    // 处理资源冲突
    console.log('应用名称已存在');
  } else {
    // 处理其他错误
    console.log('操作失败:', error.message);
  }
}
```

## 📞 支持与反馈

- **API 文档**: https://docs.paas-platform.com/api
- **SDK 文档**: https://docs.paas-platform.com/sdk
- **问题反馈**: https://github.com/paas-platform/issues
- **技术支持**: <EMAIL>

## 📋 API 接口清单

### 认证相关
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `POST /auth/refresh` - 刷新Token
- `POST /auth/logout` - 用户登出
- `GET /auth/me` - 获取当前用户信息

### 用户管理
- `GET /users` - 获取用户列表
- `POST /users` - 创建用户
- `GET /users/{id}` - 获取用户详情
- `PUT /users/{id}` - 更新用户
- `DELETE /users/{id}` - 删除用户
- `POST /users/{id}/reset-password` - 重置密码

### 应用管理
- `GET /applications` - 获取应用列表
- `POST /applications` - 创建应用
- `GET /applications/{id}` - 获取应用详情
- `PUT /applications/{id}` - 更新应用
- `DELETE /applications/{id}` - 删除应用
- `POST /applications/{id}/start` - 启动应用
- `POST /applications/{id}/stop` - 停止应用
- `POST /applications/{id}/restart` - 重启应用
- `GET /applications/{id}/logs` - 获取应用日志
- `GET /applications/{id}/metrics` - 获取应用指标

### CI/CD 流水线
- `GET /pipelines` - 获取流水线列表
- `POST /pipelines` - 创建流水线
- `GET /pipelines/{id}` - 获取流水线详情
- `PUT /pipelines/{id}` - 更新流水线
- `DELETE /pipelines/{id}` - 删除流水线
- `POST /pipelines/{id}/run` - 运行流水线
- `POST /pipelines/{id}/stop` - 停止流水线
- `GET /pipelines/{id}/executions` - 获取执行历史

### 配置管理
- `GET /configs` - 获取配置列表
- `POST /configs` - 创建配置
- `GET /configs/{id}` - 获取配置详情
- `PUT /configs/{id}` - 更新配置
- `DELETE /configs/{id}` - 删除配置
- `GET /configs/{id}/versions` - 获取配置版本
- `POST /configs/{id}/rollback` - 回滚配置

### 监控告警
- `GET /monitor/metrics` - 获取系统指标
- `GET /monitor/alerts` - 获取告警列表
- `POST /monitor/alert-rules` - 创建告警规则
- `GET /monitor/logs` - 获取日志

### 脚本管理
- `GET /scripts` - 获取脚本列表
- `POST /scripts` - 创建脚本
- `GET /scripts/{id}` - 获取脚本详情
- `PUT /scripts/{id}` - 更新脚本
- `DELETE /scripts/{id}` - 删除脚本
- `POST /scripts/{id}/execute` - 执行脚本
- `GET /scripts/{id}/executions` - 获取执行历史

## 🔄 API 版本控制

### 版本策略
- 使用语义化版本控制 (Semantic Versioning)
- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 版本指定
```http
# 在 URL 中指定版本
GET /api/v1/applications

# 在请求头中指定版本
GET /api/applications
Accept: application/vnd.paas.v1+json
```

### 版本兼容性
- v1.x 版本将持续维护至 2025年12月
- 新功能优先在最新版本中实现
- 废弃功能将提前 6 个月通知

---

**版本**: v1.0.0
**更新时间**: 2024-01-15
**维护团队**: PaaS 平台开发团队
