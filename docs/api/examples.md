# PaaS 平台 API 使用示例

本文档提供了 PaaS 平台 API 的详细使用示例，包括各种编程语言的代码示例和常见使用场景。

## 📋 目录

- [认证示例](#认证示例)
- [用户管理示例](#用户管理示例)
- [应用管理示例](#应用管理示例)
- [CI/CD 流水线示例](#cicd-流水线示例)
- [监控示例](#监控示例)
- [错误处理示例](#错误处理示例)
- [批量操作示例](#批量操作示例)

## 🔐 认证示例

### JavaScript/Node.js

```javascript
const axios = require('axios');

class PaaSClient {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.token = null;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 请求拦截器 - 自动添加认证头
    this.client.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器 - 处理错误和自动刷新 token
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && this.refreshToken) {
          try {
            await this.refresh();
            // 重试原请求
            return this.client.request(error.config);
          } catch (refreshError) {
            // 刷新失败，清除认证信息
            this.logout();
            throw refreshError;
          }
        }
        throw error;
      }
    );
  }

  // 用户登录
  async login(username, password) {
    try {
      const response = await this.client.post('/auth/login', {
        username,
        password
      });

      const { token, refresh_token, user } = response.data.data;
      this.token = token;
      this.refreshToken = refresh_token;
      this.user = user;

      console.log('登录成功:', user.username);
      return { token, user };
    } catch (error) {
      console.error('登录失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  // 刷新 token
  async refresh() {
    if (!this.refreshToken) {
      throw new Error('没有刷新令牌');
    }

    const response = await this.client.post('/auth/refresh', {
      refresh_token: this.refreshToken
    });

    const { token, refresh_token } = response.data.data;
    this.token = token;
    this.refreshToken = refresh_token;

    return token;
  }

  // 用户登出
  async logout() {
    try {
      if (this.token) {
        await this.client.post('/auth/logout');
      }
    } catch (error) {
      console.error('登出失败:', error.message);
    } finally {
      this.token = null;
      this.refreshToken = null;
      this.user = null;
    }
  }

  // 获取当前用户信息
  async getCurrentUser() {
    const response = await this.client.get('/auth/me');
    return response.data.data;
  }
}

// 使用示例
async function main() {
  const client = new PaaSClient('http://localhost:8080/api/v1');

  try {
    // 登录
    await client.login('admin', 'admin123');

    // 获取当前用户信息
    const user = await client.getCurrentUser();
    console.log('当前用户:', user);

    // 登出
    await client.logout();
  } catch (error) {
    console.error('操作失败:', error.message);
  }
}

main();
```

### Python

```python
import requests
import json
from typing import Optional, Dict, Any

class PaaSClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """发送 HTTP 请求"""
        url = f"{self.base_url}{endpoint}"
        
        # 添加认证头
        if self.token:
            self.session.headers['Authorization'] = f'Bearer {self.token}'
        
        response = self.session.request(method, url, **kwargs)
        
        # 处理 401 错误，尝试刷新 token
        if response.status_code == 401 and self.refresh_token:
            try:
                self.refresh()
                # 重试请求
                self.session.headers['Authorization'] = f'Bearer {self.token}'
                response = self.session.request(method, url, **kwargs)
            except Exception:
                self.logout()
                raise
        
        response.raise_for_status()
        return response.json()

    def login(self, username: str, password: str) -> Dict[str, Any]:
        """用户登录"""
        data = {
            'username': username,
            'password': password
        }
        
        response = self._make_request('POST', '/auth/login', json=data)
        
        token_data = response['data']
        self.token = token_data['token']
        self.refresh_token = token_data['refresh_token']
        
        print(f"登录成功: {token_data['user']['username']}")
        return token_data

    def refresh(self) -> str:
        """刷新访问令牌"""
        if not self.refresh_token:
            raise ValueError('没有刷新令牌')
        
        data = {'refresh_token': self.refresh_token}
        response = self._make_request('POST', '/auth/refresh', json=data)
        
        token_data = response['data']
        self.token = token_data['token']
        self.refresh_token = token_data['refresh_token']
        
        return self.token

    def logout(self):
        """用户登出"""
        try:
            if self.token:
                self._make_request('POST', '/auth/logout')
        except Exception as e:
            print(f"登出失败: {e}")
        finally:
            self.token = None
            self.refresh_token = None
            if 'Authorization' in self.session.headers:
                del self.session.headers['Authorization']

    def get_current_user(self) -> Dict[str, Any]:
        """获取当前用户信息"""
        response = self._make_request('GET', '/auth/me')
        return response['data']

# 使用示例
def main():
    client = PaaSClient('http://localhost:8080/api/v1')
    
    try:
        # 登录
        client.login('admin', 'admin123')
        
        # 获取当前用户信息
        user = client.get_current_user()
        print(f"当前用户: {user}")
        
        # 登出
        client.logout()
        
    except Exception as e:
        print(f"操作失败: {e}")

if __name__ == '__main__':
    main()
```

### Go

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

type PaaSClient struct {
    BaseURL      string
    Token        string
    RefreshToken string
    HTTPClient   *http.Client
}

type LoginRequest struct {
    Username string `json:"username"`
    Password string `json:"password"`
}

type LoginResponse struct {
    Code int `json:"code"`
    Data struct {
        Token        string `json:"token"`
        RefreshToken string `json:"refresh_token"`
        ExpiresIn    int    `json:"expires_in"`
        User         User   `json:"user"`
    } `json:"data"`
}

type User struct {
    ID       string   `json:"id"`
    Username string   `json:"username"`
    Email    string   `json:"email"`
    Roles    []string `json:"roles"`
}

func NewPaaSClient(baseURL string) *PaaSClient {
    return &PaaSClient{
        BaseURL: baseURL,
        HTTPClient: &http.Client{
            Timeout: 10 * time.Second,
        },
    }
}

func (c *PaaSClient) makeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
    var reqBody io.Reader
    
    if body != nil {
        jsonData, err := json.Marshal(body)
        if err != nil {
            return nil, err
        }
        reqBody = bytes.NewBuffer(jsonData)
    }
    
    req, err := http.NewRequest(method, c.BaseURL+endpoint, reqBody)
    if err != nil {
        return nil, err
    }
    
    req.Header.Set("Content-Type", "application/json")
    
    // 添加认证头
    if c.Token != "" {
        req.Header.Set("Authorization", "Bearer "+c.Token)
    }
    
    resp, err := c.HTTPClient.Do(req)
    if err != nil {
        return nil, err
    }
    
    // 处理 401 错误，尝试刷新 token
    if resp.StatusCode == 401 && c.RefreshToken != "" {
        resp.Body.Close()
        
        if err := c.Refresh(); err != nil {
            return nil, err
        }
        
        // 重试请求
        req.Header.Set("Authorization", "Bearer "+c.Token)
        return c.HTTPClient.Do(req)
    }
    
    return resp, nil
}

func (c *PaaSClient) Login(username, password string) (*User, error) {
    loginReq := LoginRequest{
        Username: username,
        Password: password,
    }
    
    resp, err := c.makeRequest("POST", "/auth/login", loginReq)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("登录失败，状态码: %d", resp.StatusCode)
    }
    
    var loginResp LoginResponse
    if err := json.NewDecoder(resp.Body).Decode(&loginResp); err != nil {
        return nil, err
    }
    
    c.Token = loginResp.Data.Token
    c.RefreshToken = loginResp.Data.RefreshToken
    
    fmt.Printf("登录成功: %s\n", loginResp.Data.User.Username)
    return &loginResp.Data.User, nil
}

func (c *PaaSClient) Refresh() error {
    refreshReq := map[string]string{
        "refresh_token": c.RefreshToken,
    }
    
    resp, err := c.makeRequest("POST", "/auth/refresh", refreshReq)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("刷新 token 失败，状态码: %d", resp.StatusCode)
    }
    
    var refreshResp LoginResponse
    if err := json.NewDecoder(resp.Body).Decode(&refreshResp); err != nil {
        return err
    }
    
    c.Token = refreshResp.Data.Token
    c.RefreshToken = refreshResp.Data.RefreshToken
    
    return nil
}

func (c *PaaSClient) Logout() error {
    if c.Token == "" {
        return nil
    }
    
    resp, err := c.makeRequest("POST", "/auth/logout", nil)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    c.Token = ""
    c.RefreshToken = ""
    
    return nil
}

func (c *PaaSClient) GetCurrentUser() (*User, error) {
    resp, err := c.makeRequest("GET", "/auth/me", nil)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("获取用户信息失败，状态码: %d", resp.StatusCode)
    }
    
    var userResp struct {
        Code int  `json:"code"`
        Data User `json:"data"`
    }
    
    if err := json.NewDecoder(resp.Body).Decode(&userResp); err != nil {
        return nil, err
    }
    
    return &userResp.Data, nil
}

func main() {
    client := NewPaaSClient("http://localhost:8080/api/v1")
    
    // 登录
    user, err := client.Login("admin", "admin123")
    if err != nil {
        fmt.Printf("登录失败: %v\n", err)
        return
    }
    
    // 获取当前用户信息
    currentUser, err := client.GetCurrentUser()
    if err != nil {
        fmt.Printf("获取用户信息失败: %v\n", err)
        return
    }
    
    fmt.Printf("当前用户: %+v\n", currentUser)
    
    // 登出
    if err := client.Logout(); err != nil {
        fmt.Printf("登出失败: %v\n", err)
    }
}
```

## 👥 用户管理示例

### 创建和管理用户

```javascript
// 扩展 PaaSClient 类
class PaaSClient {
  // ... 之前的代码 ...

  // 获取用户列表
  async getUsers(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `/users${queryString ? '?' + queryString : ''}`;
    
    const response = await this.client.get(url);
    return response.data.data;
  }

  // 创建用户
  async createUser(userData) {
    const response = await this.client.post('/users', userData);
    return response.data.data;
  }

  // 获取用户详情
  async getUser(userId) {
    const response = await this.client.get(`/users/${userId}`);
    return response.data.data;
  }

  // 更新用户
  async updateUser(userId, userData) {
    const response = await this.client.put(`/users/${userId}`, userData);
    return response.data.data;
  }

  // 删除用户
  async deleteUser(userId) {
    const response = await this.client.delete(`/users/${userId}`);
    return response.data;
  }

  // 重置用户密码
  async resetUserPassword(userId) {
    const response = await this.client.post(`/users/${userId}/reset-password`);
    return response.data;
  }
}

// 使用示例
async function userManagementExample() {
  const client = new PaaSClient('http://localhost:8080/api/v1');
  
  try {
    // 登录
    await client.login('admin', 'admin123');

    // 获取用户列表（分页）
    const userList = await client.getUsers({
      page: 1,
      page_size: 20,
      search: 'test',
      status: 'active'
    });
    console.log('用户列表:', userList);

    // 创建新用户
    const newUser = await client.createUser({
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password123',
      real_name: '新用户',
      roles: ['user']
    });
    console.log('创建的用户:', newUser);

    // 更新用户信息
    const updatedUser = await client.updateUser(newUser.id, {
      real_name: '更新的用户名',
      department: '开发部门'
    });
    console.log('更新的用户:', updatedUser);

    // 重置用户密码
    await client.resetUserPassword(newUser.id);
    console.log('密码重置成功');

    // 删除用户
    await client.deleteUser(newUser.id);
    console.log('用户删除成功');

  } catch (error) {
    console.error('用户管理操作失败:', error.response?.data || error.message);
  }
}
```

## 📱 应用管理示例

### 完整的应用生命周期管理

```javascript
// 应用管理相关方法
class PaaSClient {
  // ... 之前的代码 ...

  // 获取应用列表
  async getApplications(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `/applications${queryString ? '?' + queryString : ''}`;
    
    const response = await this.client.get(url);
    return response.data.data;
  }

  // 创建应用
  async createApplication(appData) {
    const response = await this.client.post('/applications', appData);
    return response.data.data;
  }

  // 获取应用详情
  async getApplication(appId) {
    const response = await this.client.get(`/applications/${appId}`);
    return response.data.data;
  }

  // 更新应用
  async updateApplication(appId, appData) {
    const response = await this.client.put(`/applications/${appId}`, appData);
    return response.data.data;
  }

  // 删除应用
  async deleteApplication(appId) {
    const response = await this.client.delete(`/applications/${appId}`);
    return response.data;
  }

  // 启动应用
  async startApplication(appId) {
    const response = await this.client.post(`/applications/${appId}/start`);
    return response.data;
  }

  // 停止应用
  async stopApplication(appId) {
    const response = await this.client.post(`/applications/${appId}/stop`);
    return response.data;
  }

  // 重启应用
  async restartApplication(appId) {
    const response = await this.client.post(`/applications/${appId}/restart`);
    return response.data;
  }

  // 获取应用日志
  async getApplicationLogs(appId, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `/applications/${appId}/logs${queryString ? '?' + queryString : ''}`;
    
    const response = await this.client.get(url);
    return response.data.data;
  }

  // 获取应用指标
  async getApplicationMetrics(appId) {
    const response = await this.client.get(`/applications/${appId}/metrics`);
    return response.data.data;
  }
}

// 使用示例
async function applicationManagementExample() {
  const client = new PaaSClient('http://localhost:8080/api/v1');
  
  try {
    await client.login('admin', 'admin123');

    // 创建应用
    const newApp = await client.createApplication({
      name: 'my-web-app',
      description: '我的 Web 应用',
      repository_url: 'https://github.com/user/my-web-app.git',
      branch: 'main',
      environment: 'development',
      config: {
        port: 3000,
        env_vars: {
          NODE_ENV: 'development',
          DATABASE_URL: 'postgresql://localhost:5432/myapp'
        },
        resources: {
          cpu: '500m',
          memory: '512Mi'
        }
      }
    });
    console.log('应用创建成功:', newApp);

    // 启动应用
    await client.startApplication(newApp.id);
    console.log('应用启动成功');

    // 等待应用启动
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 获取应用状态
    const appStatus = await client.getApplication(newApp.id);
    console.log('应用状态:', appStatus.status);

    // 获取应用指标
    const metrics = await client.getApplicationMetrics(newApp.id);
    console.log('应用指标:', metrics);

    // 获取应用日志
    const logs = await client.getApplicationLogs(newApp.id, {
      lines: 100,
      level: 'info'
    });
    console.log('应用日志:', logs);

    // 更新应用配置
    const updatedApp = await client.updateApplication(newApp.id, {
      config: {
        ...newApp.config,
        env_vars: {
          ...newApp.config.env_vars,
          DEBUG: 'true'
        }
      }
    });
    console.log('应用配置更新成功');

    // 重启应用以应用新配置
    await client.restartApplication(newApp.id);
    console.log('应用重启成功');

  } catch (error) {
    console.error('应用管理操作失败:', error.response?.data || error.message);
  }
}
```

## 🔄 CI/CD 流水线示例

### 创建和执行流水线

```javascript
// CI/CD 相关方法
class PaaSClient {
  // ... 之前的代码 ...

  // 获取流水线列表
  async getPipelines(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `/pipelines${queryString ? '?' + queryString : ''}`;
    
    const response = await this.client.get(url);
    return response.data.data;
  }

  // 创建流水线
  async createPipeline(pipelineData) {
    const response = await this.client.post('/pipelines', pipelineData);
    return response.data.data;
  }

  // 获取流水线详情
  async getPipeline(pipelineId) {
    const response = await this.client.get(`/pipelines/${pipelineId}`);
    return response.data.data;
  }

  // 运行流水线
  async runPipeline(pipelineId, params = {}) {
    const response = await this.client.post(`/pipelines/${pipelineId}/run`, params);
    return response.data.data;
  }

  // 停止流水线
  async stopPipeline(pipelineId) {
    const response = await this.client.post(`/pipelines/${pipelineId}/stop`);
    return response.data;
  }

  // 获取流水线执行历史
  async getPipelineExecutions(pipelineId, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `/pipelines/${pipelineId}/executions${queryString ? '?' + queryString : ''}`;
    
    const response = await this.client.get(url);
    return response.data.data;
  }

  // 获取流水线执行详情
  async getPipelineExecution(pipelineId, executionId) {
    const response = await this.client.get(`/pipelines/${pipelineId}/executions/${executionId}`);
    return response.data.data;
  }
}

// 使用示例
async function cicdExample() {
  const client = new PaaSClient('http://localhost:8080/api/v1');
  
  try {
    await client.login('admin', 'admin123');

    // 假设已有应用
    const apps = await client.getApplications({ page: 1, page_size: 1 });
    const appId = apps.applications[0].id;

    // 创建流水线
    const pipeline = await client.createPipeline({
      name: '自动化部署流水线',
      app_id: appId,
      branch: 'main',
      trigger: 'push',
      stages: [
        {
          name: '代码检出',
          type: 'checkout',
          config: {
            repository: 'https://github.com/user/repo.git',
            branch: 'main'
          }
        },
        {
          name: '安装依赖',
          type: 'script',
          config: {
            command: 'npm install'
          }
        },
        {
          name: '运行测试',
          type: 'test',
          config: {
            command: 'npm test',
            coverage: true
          }
        },
        {
          name: '构建应用',
          type: 'build',
          config: {
            command: 'npm run build',
            artifacts: ['dist/']
          }
        },
        {
          name: '部署到开发环境',
          type: 'deploy',
          config: {
            environment: 'development',
            strategy: 'rolling'
          }
        }
      ]
    });
    console.log('流水线创建成功:', pipeline);

    // 运行流水线
    const execution = await client.runPipeline(pipeline.id, {
      branch: 'main',
      commit: 'latest'
    });
    console.log('流水线开始执行:', execution);

    // 轮询执行状态
    let executionStatus = execution;
    while (executionStatus.status === 'running') {
      await new Promise(resolve => setTimeout(resolve, 5000));
      executionStatus = await client.getPipelineExecution(pipeline.id, execution.id);
      console.log('执行状态:', executionStatus.status);
      
      // 显示各阶段状态
      executionStatus.stages.forEach(stage => {
        console.log(`  ${stage.name}: ${stage.status}`);
      });
    }

    console.log('流水线执行完成:', executionStatus.status);

    // 获取执行历史
    const executions = await client.getPipelineExecutions(pipeline.id, {
      page: 1,
      page_size: 10
    });
    console.log('执行历史:', executions);

  } catch (error) {
    console.error('CI/CD 操作失败:', error.response?.data || error.message);
  }
}
```

## 📊 监控示例

### 获取系统指标和告警

```javascript
// 监控相关方法
class PaaSClient {
  // ... 之前的代码 ...

  // 获取系统指标
  async getSystemMetrics() {
    const response = await this.client.get('/monitor/metrics');
    return response.data.data;
  }

  // 获取告警列表
  async getAlerts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `/monitor/alerts${queryString ? '?' + queryString : ''}`;
    
    const response = await this.client.get(url);
    return response.data.data;
  }

  // 创建告警规则
  async createAlertRule(ruleData) {
    const response = await this.client.post('/monitor/alert-rules', ruleData);
    return response.data.data;
  }

  // 获取日志
  async getLogs(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = `/monitor/logs${queryString ? '?' + queryString : ''}`;
    
    const response = await this.client.get(url);
    return response.data.data;
  }
}

// 使用示例
async function monitoringExample() {
  const client = new PaaSClient('http://localhost:8080/api/v1');
  
  try {
    await client.login('admin', 'admin123');

    // 获取系统指标
    const metrics = await client.getSystemMetrics();
    console.log('系统指标:', metrics);

    // 检查系统健康状态
    if (metrics.system.cpu_usage > 80) {
      console.warn('⚠️ CPU 使用率过高:', metrics.system.cpu_usage + '%');
    }

    if (metrics.system.memory_usage > 85) {
      console.warn('⚠️ 内存使用率过高:', metrics.system.memory_usage + '%');
    }

    // 获取最近的告警
    const alerts = await client.getAlerts({
      page: 1,
      page_size: 10,
      level: 'warning'
    });
    console.log('最近告警:', alerts);

    // 创建告警规则
    const alertRule = await client.createAlertRule({
      name: 'CPU 使用率告警',
      description: '当 CPU 使用率超过 80% 时触发告警',
      metric: 'cpu_usage',
      operator: 'greater_than',
      threshold: 80,
      duration: '5m',
      severity: 'warning',
      channels: ['email', 'slack']
    });
    console.log('告警规则创建成功:', alertRule);

    // 获取应用日志
    const logs = await client.getLogs({
      app_id: 'app-123',
      level: 'error',
      start_time: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      end_time: new Date().toISOString(),
      lines: 100
    });
    console.log('应用错误日志:', logs);

  } catch (error) {
    console.error('监控操作失败:', error.response?.data || error.message);
  }
}
```

## ❌ 错误处理示例

### 统一错误处理和重试机制

```javascript
class APIError extends Error {
  constructor(response) {
    super(response.data?.message || '未知错误');
    this.name = 'APIError';
    this.code = response.data?.code || response.status;
    this.type = response.data?.error?.type;
    this.details = response.data?.error?.details;
    this.status = response.status;
  }
}

class PaaSClient {
  // ... 之前的代码 ...

  // 重试机制
  async withRetry(operation, maxRetries = 3, delay = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }

        // 只对特定错误进行重试
        if (this.shouldRetry(error)) {
          console.log(`操作失败，第 ${attempt} 次重试...`);
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        } else {
          throw error;
        }
      }
    }
  }

  shouldRetry(error) {
    // 网络错误或服务器错误可以重试
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT' ||
      (error.response && error.response.status >= 500)
    );
  }

  // 错误处理包装器
  async safeRequest(operation, errorHandler) {
    try {
      return await this.withRetry(operation);
    } catch (error) {
      const apiError = new APIError(error.response || { status: 0, data: { message: error.message } });
      
      if (errorHandler) {
        return errorHandler(apiError);
      }
      
      this.handleError(apiError);
      throw apiError;
    }
  }

  handleError(error) {
    switch (error.code) {
      case 401:
        console.error('认证失败，请重新登录');
        this.logout();
        break;
      case 403:
        console.error('权限不足，无法执行此操作');
        break;
      case 404:
        console.error('请求的资源不存在');
        break;
      case 409:
        console.error('资源冲突:', error.message);
        break;
      case 422:
        console.error('数据验证失败:');
        if (error.details) {
          error.details.forEach(detail => {
            console.error(`  ${detail.field}: ${detail.message}`);
          });
        }
        break;
      case 429:
        console.error('请求频率过高，请稍后再试');
        break;
      case 500:
        console.error('服务器内部错误，请联系管理员');
        break;
      default:
        console.error('操作失败:', error.message);
    }
  }
}

// 使用示例
async function errorHandlingExample() {
  const client = new PaaSClient('http://localhost:8080/api/v1');
  
  try {
    await client.login('admin', 'admin123');

    // 使用安全请求包装器
    const users = await client.safeRequest(
      () => client.getUsers(),
      (error) => {
        if (error.code === 403) {
          console.log('没有权限查看用户列表，返回空列表');
          return { users: [], pagination: { total: 0 } };
        }
        return null; // 继续抛出错误
      }
    );

    console.log('用户列表:', users);

    // 批量操作错误处理
    const userIds = ['1', '2', '3', 'invalid-id'];
    const results = await Promise.allSettled(
      userIds.map(id => client.safeRequest(() => client.getUser(id)))
    );

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(`用户 ${userIds[index]}:`, result.value.username);
      } else {
        console.error(`获取用户 ${userIds[index]} 失败:`, result.reason.message);
      }
    });

  } catch (error) {
    console.error('示例执行失败:', error.message);
  }
}
```

## 🔄 批量操作示例

### 批量创建和管理资源

```javascript
// 批量操作工具类
class BatchOperations {
  constructor(client) {
    this.client = client;
  }

  // 批量创建用户
  async createUsersInBatch(users, batchSize = 5) {
    const results = [];
    
    for (let i = 0; i < users.length; i += batchSize) {
      const batch = users.slice(i, i + batchSize);
      
      console.log(`处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(users.length / batchSize)}`);
      
      const batchResults = await Promise.allSettled(
        batch.map(user => this.client.createUser(user))
      );
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push({ success: true, data: result.value });
        } else {
          results.push({ 
            success: false, 
            error: result.reason.message,
            userData: batch[index]
          });
        }
      });
      
      // 批次间延迟，避免过载
      if (i + batchSize < users.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }

  // 批量部署应用
  async deployApplicationsInBatch(appIds, environment = 'production') {
    const deploymentResults = [];
    
    for (const appId of appIds) {
      try {
        console.log(`开始部署应用 ${appId} 到 ${environment} 环境`);
        
        // 创建部署流水线
        const pipeline = await this.client.createPipeline({
          name: `批量部署-${appId}-${Date.now()}`,
          app_id: appId,
          branch: 'main',
          trigger: 'manual',
          stages: [
            { name: '代码检出', type: 'checkout', config: {} },
            { name: '构建', type: 'build', config: {} },
            { name: '测试', type: 'test', config: {} },
            { name: '部署', type: 'deploy', config: { environment } }
          ]
        });
        
        // 运行流水线
        const execution = await this.client.runPipeline(pipeline.id);
        
        deploymentResults.push({
          appId,
          pipelineId: pipeline.id,
          executionId: execution.id,
          status: 'started'
        });
        
      } catch (error) {
        deploymentResults.push({
          appId,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    return deploymentResults;
  }

  // 监控批量部署状态
  async monitorBatchDeployments(deployments) {
    const completedDeployments = [];
    let pendingDeployments = [...deployments.filter(d => d.status === 'started')];
    
    while (pendingDeployments.length > 0) {
      console.log(`监控 ${pendingDeployments.length} 个部署任务...`);
      
      for (let i = pendingDeployments.length - 1; i >= 0; i--) {
        const deployment = pendingDeployments[i];
        
        try {
          const execution = await this.client.getPipelineExecution(
            deployment.pipelineId,
            deployment.executionId
          );
          
          if (execution.status !== 'running') {
            deployment.status = execution.status;
            deployment.completedAt = new Date();
            
            completedDeployments.push(deployment);
            pendingDeployments.splice(i, 1);
            
            console.log(`应用 ${deployment.appId} 部署${execution.status === 'success' ? '成功' : '失败'}`);
          }
        } catch (error) {
          console.error(`监控应用 ${deployment.appId} 部署状态失败:`, error.message);
        }
      }
      
      if (pendingDeployments.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 10000)); // 等待 10 秒
      }
    }
    
    return completedDeployments;
  }
}

// 使用示例
async function batchOperationsExample() {
  const client = new PaaSClient('http://localhost:8080/api/v1');
  const batchOps = new BatchOperations(client);
  
  try {
    await client.login('admin', 'admin123');

    // 批量创建用户
    const usersToCreate = [
      { username: 'user1', email: '<EMAIL>', password: 'pass123', roles: ['user'] },
      { username: 'user2', email: '<EMAIL>', password: 'pass123', roles: ['user'] },
      { username: 'user3', email: '<EMAIL>', password: 'pass123', roles: ['developer'] },
      { username: 'user4', email: '<EMAIL>', password: 'pass123', roles: ['user'] },
      { username: 'user5', email: '<EMAIL>', password: 'pass123', roles: ['operator'] }
    ];

    console.log('开始批量创建用户...');
    const userResults = await batchOps.createUsersInBatch(usersToCreate, 2);
    
    const successCount = userResults.filter(r => r.success).length;
    const failCount = userResults.filter(r => !r.success).length;
    
    console.log(`用户创建完成: 成功 ${successCount} 个，失败 ${failCount} 个`);

    // 批量部署应用
    const appIds = ['app-1', 'app-2', 'app-3'];
    
    console.log('开始批量部署应用...');
    const deployments = await batchOps.deployApplicationsInBatch(appIds, 'staging');
    
    console.log('监控部署进度...');
    const completedDeployments = await batchOps.monitorBatchDeployments(deployments);
    
    const successfulDeployments = completedDeployments.filter(d => d.status === 'success');
    const failedDeployments = completedDeployments.filter(d => d.status !== 'success');
    
    console.log(`部署完成: 成功 ${successfulDeployments.length} 个，失败 ${failedDeployments.length} 个`);

  } catch (error) {
    console.error('批量操作失败:', error.message);
  }
}
```

## 🔧 高级用法示例

### WebSocket 实时通信

```javascript
class PaaSWebSocketClient {
  constructor(baseURL, token) {
    this.baseURL = baseURL.replace('http', 'ws');
    this.token = token;
    this.ws = null;
    this.eventHandlers = new Map();
  }

  connect() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(`${this.baseURL}/ws?token=${this.token}`);
      
      this.ws.onopen = () => {
        console.log('WebSocket 连接已建立');
        resolve();
      };
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('解析 WebSocket 消息失败:', error);
        }
      };
      
      this.ws.onerror = (error) => {
        console.error('WebSocket 错误:', error);
        reject(error);
      };
      
      this.ws.onclose = () => {
        console.log('WebSocket 连接已关闭');
        // 自动重连
        setTimeout(() => this.connect(), 5000);
      };
    });
  }

  handleMessage(data) {
    const { type, payload } = data;
    
    if (this.eventHandlers.has(type)) {
      this.eventHandlers.get(type).forEach(handler => {
        try {
          handler(payload);
        } catch (error) {
          console.error(`处理 ${type} 事件失败:`, error);
        }
      });
    }
  }

  on(eventType, handler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType).push(handler);
  }

  off(eventType, handler) {
    if (this.eventHandlers.has(eventType)) {
      const handlers = this.eventHandlers.get(eventType);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  send(type, payload) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type, payload }));
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// 使用示例
async function realtimeExample() {
  const client = new PaaSClient('http://localhost:8080/api/v1');
  
  try {
    const { token } = await client.login('admin', 'admin123');
    
    // 建立 WebSocket 连接
    const wsClient = new PaaSWebSocketClient('ws://localhost:8080/api/v1', token);
    await wsClient.connect();
    
    // 监听应用状态变化
    wsClient.on('app.status.changed', (data) => {
      console.log(`应用 ${data.app_name} 状态变更: ${data.old_status} -> ${data.new_status}`);
    });
    
    // 监听流水线执行状态
    wsClient.on('pipeline.execution.updated', (data) => {
      console.log(`流水线 ${data.pipeline_name} 执行状态: ${data.status}`);
      if (data.stage) {
        console.log(`  当前阶段: ${data.stage.name} (${data.stage.status})`);
      }
    });
    
    // 监听系统告警
    wsClient.on('system.alert', (data) => {
      console.warn(`🚨 系统告警: ${data.title}`);
      console.warn(`   详情: ${data.description}`);
      console.warn(`   级别: ${data.level}`);
    });
    
    // 订阅特定应用的事件
    wsClient.send('subscribe', {
      events: ['app.logs', 'app.metrics'],
      filters: {
        app_id: 'app-123'
      }
    });
    
    // 保持连接
    console.log('WebSocket 连接已建立，监听实时事件...');
    
  } catch (error) {
    console.error('实时通信示例失败:', error.message);
  }
}
```

这些示例展示了 PaaS 平台 API 的各种使用场景，包括基础的 CRUD 操作、复杂的业务流程、错误处理、批量操作和实时通信。开发者可以根据这些示例快速集成 PaaS 平台的功能到自己的应用中。
