{"info": {"name": "PaaS 平台 API", "description": "PaaS 平台完整的 API 测试集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8080/api/v1", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "app_id", "value": "", "type": "string"}], "item": [{"name": "认证", "item": [{"name": "用户登录", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"登录成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.token).to.be.a('string');", "    ", "    // 保存 token 到环境变量", "    pm.collectionVariables.set('jwt_token', response.data.token);", "    pm.collectionVariables.set('user_id', response.data.user.id);", "});"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "获取当前用户信息", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取用户信息成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.username).to.be.a('string');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}}}, {"name": "用户登出", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"登出成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}]}, {"name": "用户管理", "item": [{"name": "获取用户列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取用户列表成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.users).to.be.an('array');", "    pm.expect(response.data.pagination).to.be.an('object');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users?page=1&page_size=20", "host": ["{{base_url}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}}, {"name": "创建用户", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"创建用户成功\", function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(201);", "    pm.expect(response.data.id).to.be.a('string');", "    ", "    // 保存新用户ID", "    pm.collectionVariables.set('new_user_id', response.data.id);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"real_name\": \"测试用户\",\n  \"roles\": [\"user\"]\n}"}, "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}, {"name": "获取用户详情", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取用户详情成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.id).to.be.a('string');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"]}}}]}, {"name": "应用管理", "item": [{"name": "获取应用列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取应用列表成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.applications).to.be.an('array');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/applications?page=1&page_size=20", "host": ["{{base_url}}"], "path": ["applications"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}}, {"name": "创建应用", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"创建应用成功\", function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(201);", "    pm.expect(response.data.id).to.be.a('string');", "    ", "    // 保存应用ID", "    pm.collectionVariables.set('app_id', response.data.id);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"test-app\",\n  \"description\": \"测试应用\",\n  \"repository_url\": \"https://github.com/user/test-app.git\",\n  \"branch\": \"main\",\n  \"environment\": \"development\",\n  \"config\": {\n    \"port\": 8080,\n    \"env_vars\": {\n      \"NODE_ENV\": \"development\"\n    },\n    \"resources\": {\n      \"cpu\": \"500m\",\n      \"memory\": \"512Mi\"\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/applications", "host": ["{{base_url}}"], "path": ["applications"]}}}, {"name": "获取应用详情", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取应用详情成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.id).to.be.a('string');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/applications/{{app_id}}", "host": ["{{base_url}}"], "path": ["applications", "{{app_id}}"]}}}, {"name": "启动应用", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"启动应用成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/applications/{{app_id}}/start", "host": ["{{base_url}}"], "path": ["applications", "{{app_id}}", "start"]}}}, {"name": "停止应用", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"停止应用成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/applications/{{app_id}}/stop", "host": ["{{base_url}}"], "path": ["applications", "{{app_id}}", "stop"]}}}, {"name": "获取应用日志", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取应用日志成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/applications/{{app_id}}/logs?lines=100", "host": ["{{base_url}}"], "path": ["applications", "{{app_id}}", "logs"], "query": [{"key": "lines", "value": "100"}]}}}]}, {"name": "监控", "item": [{"name": "获取系统指标", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取系统指标成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.system).to.be.an('object');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/monitor/metrics", "host": ["{{base_url}}"], "path": ["monitor", "metrics"]}}}, {"name": "获取告警列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取告警列表成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/monitor/alerts", "host": ["{{base_url}}"], "path": ["monitor", "alerts"]}}}, {"name": "获取日志", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取日志成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/monitor/logs?level=info&lines=100", "host": ["{{base_url}}"], "path": ["monitor", "logs"], "query": [{"key": "level", "value": "info"}, {"key": "lines", "value": "100"}]}}}]}, {"name": "CI/CD", "item": [{"name": "获取流水线列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取流水线列表成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/pipelines", "host": ["{{base_url}}"], "path": ["pipelines"]}}}, {"name": "创建流水线", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"创建流水线成功\", function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(201);", "    pm.expect(response.data.id).to.be.a('string');", "    ", "    // 保存流水线ID", "    pm.collectionVariables.set('pipeline_id', response.data.id);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"测试流水线\",\n  \"app_id\": \"{{app_id}}\",\n  \"branch\": \"main\",\n  \"trigger\": \"manual\",\n  \"stages\": [\n    {\n      \"name\": \"代码检出\",\n      \"type\": \"checkout\",\n      \"config\": {}\n    },\n    {\n      \"name\": \"构建\",\n      \"type\": \"build\",\n      \"config\": {\n        \"command\": \"npm run build\"\n      }\n    },\n    {\n      \"name\": \"测试\",\n      \"type\": \"test\",\n      \"config\": {\n        \"command\": \"npm test\"\n      }\n    },\n    {\n      \"name\": \"部署\",\n      \"type\": \"deploy\",\n      \"config\": {\n        \"environment\": \"development\"\n      }\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/pipelines", "host": ["{{base_url}}"], "path": ["pipelines"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 在每个请求前检查是否需要认证", "const authExcludePaths = ['/auth/login', '/auth/register'];", "const currentPath = pm.request.url.getPath();", "", "if (!authExcludePaths.some(path => currentPath.includes(path))) {", "    const token = pm.collectionVariables.get('jwt_token');", "    if (!token) {", "        console.log('警告: 未设置 JWT Token，请先登录');", "    }", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试脚本", "pm.test(\"响应时间小于 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test(\"响应格式正确\", function () {", "    pm.response.to.have.header('Content-Type');", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// 检查错误响应格式", "if (pm.response.code >= 400) {", "    pm.test(\"错误响应格式正确\", function () {", "        const response = pm.response.json();", "        pm.expect(response).to.have.property('code');", "        pm.expect(response).to.have.property('message');", "        pm.expect(response).to.have.property('timestamp');", "    });", "}"]}}]}