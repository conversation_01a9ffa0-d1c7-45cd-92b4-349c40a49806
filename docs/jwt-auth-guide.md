# JWT 认证可配置开关使用指南

## 概述

本项目实现了 JWT 认证的可配置开关机制，支持在不同环境下灵活控制认证策略。可以根据需要启用或禁用 JWT 校验，并提供开发模式和匿名访问等替代方案。

## 特性

- **可配置开关**: 通过配置文件控制 JWT 认证的启用状态
- **开发模式**: 支持开发环境下的简化认证
- **路径跳过**: 配置无需认证的路径
- **优雅降级**: 禁用认证时的安全替代方案
- **权限控制**: 细粒度的角色和权限管理
- **审计日志**: 记录认证和授权操作

## 配置说明

### 配置文件示例 (configs/app-manager.yaml)

```yaml
# 安全配置
security:
  jwt:
    enabled: true                     # 是否启用 JWT 校验 (false 时禁用认证)
    secret: "your-jwt-secret-key"     # JWT 密钥
    expires_in: 24h                   # Token 过期时间
    refresh_expires_in: 168h          # 刷新 Token 过期时间
    dev_mode: false                   # 开发模式 (true 时使用默认用户)
    dev_token: "dev-token"            # 开发模式令牌
    dev_user:                         # 开发模式用户信息
      id: "dev-user-123"
      tenant_id: "dev-tenant-123"
      username: "开发用户"
      email: "<EMAIL>"
      roles: ["admin"]
    skip_paths:                       # 跳过认证的路径
      - "/health"
      - "/ready"
      - "/metrics"
      - "/api/v1/auth/login"
      - "/api/v1/auth/register"
      - "/api/v1/auth/refresh"
```

### 环境变量配置

```bash
# JWT 认证配置
JWT_ENABLED=true
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=168h
JWT_DEV_MODE=false
JWT_DEV_TOKEN=dev-token

# 开发模式用户配置
JWT_DEV_USER_ID=dev-user-123
JWT_DEV_USER_TENANT_ID=dev-tenant-123
JWT_DEV_USER_USERNAME=开发用户
JWT_DEV_USER_EMAIL=<EMAIL>
JWT_DEV_USER_ROLES=admin
```

## 使用方法

### 1. 基本使用

```go
package main

import (
    "paas-platform/internal/auth"
    "paas-platform/pkg/middleware"
    "paas-platform/pkg/logger"
    
    "github.com/gin-gonic/gin"
)

func main() {
    // 初始化 JWT 服务
    jwtService := auth.NewJWTService("your-secret", 24*time.Hour, 7*24*time.Hour)
    
    // 初始化日志记录器
    logger := logger.NewLogger()
    
    // 创建认证中间件
    authMiddleware := middleware.NewAuthMiddleware(jwtService, logger)
    
    // 创建 Gin 路由
    router := gin.New()
    
    // 应用认证中间件
    router.Use(authMiddleware.Handler())
    
    // 定义路由
    router.GET("/api/v1/users", middleware.RequireAuth(), getUsersHandler)
    router.GET("/api/v1/admin/users", middleware.RequireAdmin(), getAdminUsersHandler)
    
    router.Run(":8080")
}
```

### 2. 不同认证模式

#### 启用 JWT 认证（生产模式）

```yaml
security:
  jwt:
    enabled: true
    dev_mode: false
```

客户端需要在请求头中包含有效的 JWT token：

```bash
curl -H "Authorization: Bearer <jwt-token>" \
     http://localhost:8080/api/v1/users
```

#### 禁用 JWT 认证

```yaml
security:
  jwt:
    enabled: false
    dev_mode: true  # 使用开发用户
```

所有请求都会使用配置的开发用户身份：

```bash
curl http://localhost:8080/api/v1/users
# 自动使用开发用户身份
```

#### 开发模式

```yaml
security:
  jwt:
    enabled: true
    dev_mode: true
    dev_token: "dev-token"
```

可以使用开发令牌或正常的 JWT token：

```bash
# 使用开发令牌
curl -H "Authorization: Bearer dev-token" \
     http://localhost:8080/api/v1/users

# 或使用正常的 JWT token
curl -H "Authorization: Bearer <jwt-token>" \
     http://localhost:8080/api/v1/users
```

### 3. 权限控制

```go
// 要求用户已认证
router.GET("/profile", middleware.RequireAuth(), getProfileHandler)

// 要求特定角色
router.GET("/admin", middleware.RequireRole("admin"), getAdminHandler)
router.GET("/editor", middleware.RequireRole("editor", "admin"), getEditorHandler)

// 要求特定权限
router.POST("/articles", middleware.RequirePermission("article:create"), createArticleHandler)

// 要求管理员权限
router.DELETE("/users/:id", middleware.RequireAdmin(), deleteUserHandler)

// 要求租户权限
router.GET("/tenant/:tenant_id/data", 
    middleware.RequireTenantFromParam("tenant_id"), 
    getTenantDataHandler)

// 条件认证（根据配置决定）
router.GET("/api/v1/data", 
    middleware.ConditionalAuth(authMiddleware), 
    getDataHandler)
```

### 4. 获取用户信息

```go
func getUserHandler(c *gin.Context) {
    // 从上下文获取用户信息
    user := middleware.GetUserFromContext(c)
    
    // 检查用户状态
    if !user.Authenticated {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
        return
    }
    
    // 检查权限
    if !user.HasPermission("user:read") {
        c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
        return
    }
    
    // 返回用户信息
    c.JSON(http.StatusOK, gin.H{
        "user_id":     user.UserID,
        "username":    user.Username,
        "roles":       user.Roles,
        "auth_method": user.AuthMethod,
    })
}
```

## 安全考虑

### 1. 生产环境配置

```yaml
security:
  jwt:
    enabled: true          # 生产环境必须启用
    dev_mode: false        # 生产环境必须禁用开发模式
    secret: "complex-secret-key-with-sufficient-entropy"
    expires_in: 1h         # 较短的过期时间
    refresh_expires_in: 24h
```

### 2. 开发环境配置

```yaml
security:
  jwt:
    enabled: false         # 可以禁用以简化开发
    dev_mode: true         # 启用开发模式
    dev_user:
      roles: ["developer"] # 限制开发用户权限
```

### 3. 测试环境配置

```yaml
security:
  jwt:
    enabled: true          # 测试真实的认证流程
    dev_mode: true         # 允许使用开发令牌
    expires_in: 10m        # 较短的过期时间用于测试
```

## 最佳实践

### 1. 环境隔离

```go
// 根据环境设置不同的认证策略
func setupAuth() *middleware.AuthMiddleware {
    env := os.Getenv("ENV")
    
    switch env {
    case "production":
        // 生产环境：严格的 JWT 认证
        viper.Set("security.jwt.enabled", true)
        viper.Set("security.jwt.dev_mode", false)
        
    case "development":
        // 开发环境：可选的认证
        viper.Set("security.jwt.enabled", false)
        viper.Set("security.jwt.dev_mode", true)
        
    case "testing":
        // 测试环境：启用认证但允许开发令牌
        viper.Set("security.jwt.enabled", true)
        viper.Set("security.jwt.dev_mode", true)
    }
    
    return middleware.NewAuthMiddleware(jwtService, logger)
}
```

### 2. 路径配置

```yaml
security:
  jwt:
    skip_paths:
      # 健康检查
      - "/health"
      - "/ready"
      - "/metrics"
      
      # 认证相关
      - "/api/v1/auth/login"
      - "/api/v1/auth/register"
      - "/api/v1/auth/refresh"
      - "/api/v1/auth/forgot-password"
      
      # 公开 API
      - "/api/v1/public/"
      
      # 静态资源
      - "/static/"
      - "/assets/"
```

### 3. 错误处理

```go
func authErrorHandler(c *gin.Context) {
    user := middleware.GetUserFromContext(c)
    
    // 记录认证失败
    if !user.Authenticated {
        logger.Warn("认证失败", 
            "path", c.Request.URL.Path,
            "ip", c.ClientIP(),
            "user_agent", c.Request.UserAgent())
    }
    
    // 统一的错误响应
    c.JSON(http.StatusUnauthorized, gin.H{
        "code":      "AUTHENTICATION_REQUIRED",
        "message":   "需要用户认证",
        "timestamp": time.Now().Format(time.RFC3339),
    })
}
```

### 4. 审计日志

```go
// 启用审计日志中间件
router.Use(middleware.AuditLog())

// 自定义审计日志
func customAuditLog(c *gin.Context) {
    user := middleware.GetUserFromContext(c)
    
    // 记录敏感操作
    if isSensitiveOperation(c.Request.Method, c.Request.URL.Path) {
        auditLogger.Info("敏感操作",
            "user_id", user.UserID,
            "action", c.Request.Method,
            "resource", c.Request.URL.Path,
            "ip", c.ClientIP(),
            "timestamp", time.Now())
    }
}
```

## 监控和调试

### 1. 认证状态检查

```bash
# 检查认证配置
curl http://localhost:8080/api/v1/auth/status

# 响应示例
{
  "jwt_enabled": true,
  "dev_mode": false,
  "skip_paths": ["/health", "/metrics"],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2. 用户信息调试

```bash
# 获取当前用户信息
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/auth/me

# 响应示例
{
  "user_id": "user-123",
  "username": "张三",
  "roles": ["user"],
  "permissions": ["read", "write"],
  "auth_method": "jwt",
  "authenticated": true
}
```

### 3. 日志监控

```bash
# 查看认证相关日志
tail -f /var/log/app/auth.log | grep "JWT"

# 示例日志
2024-01-01T12:00:00Z INFO JWT认证成功 user_id=user-123 username=张三
2024-01-01T12:01:00Z WARN JWT令牌验证失败 error="token expired"
2024-01-01T12:02:00Z DEBUG JWT校验已禁用 path=/api/v1/data
```

## 故障排除

### 常见问题

1. **JWT 校验失败**
   - 检查 token 是否过期
   - 验证 JWT 密钥配置
   - 确认 token 格式正确

2. **权限不足**
   - 检查用户角色配置
   - 验证权限映射
   - 确认路由权限要求

3. **开发模式不生效**
   - 检查 `dev_mode` 配置
   - 验证开发用户配置
   - 确认环境变量设置

### 调试命令

```bash
# 检查配置
curl http://localhost:8080/debug/config

# 验证 token
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/debug/token

# 检查权限
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/debug/permissions
```
