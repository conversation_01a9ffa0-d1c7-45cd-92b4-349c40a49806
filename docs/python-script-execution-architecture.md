# Python脚本快速执行架构设计

## 1. 架构概述

本文档设计了一个专门用于Python脚本快速部署和执行的架构组件，支持完整的工作流：应用创建 → 代码部署 → 自动构建 → 应用部署 → 接口调用 → 动态执行 → 结果返回 → 资源回收。

### 1.1 核心设计原则

- **按需执行**: 容器按需启动，执行完成后自动销毁
- **快速响应**: 优化冷启动时间，支持预热机制
- **资源高效**: 智能资源调度和回收
- **结果可靠**: 完整的执行结果收集和管理
- **安全隔离**: 每个执行任务独立的容器环境

### 1.2 架构组件

```
┌─────────────────────────────────────────────────────────────┐
│                    Python脚本执行平台                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  任务调度器  │  │  执行引擎   │  │ 结果收集器  │         │
│  │ TaskScheduler│  │ExecutionEngine│ │ResultCollector│      │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 容器管理器  │  │ 资源监控器  │  │ 缓存管理器  │         │
│  │ContainerMgr │  │ResourceMonitor│ │ CacheManager│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层                                │
│  Docker Engine + Registry + Database + Message Queue       │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心组件设计

### 2.1 任务调度器 (TaskScheduler)

**职责**:
- 接收Python脚本执行请求
- 任务队列管理和优先级调度
- 负载均衡和资源分配
- 执行状态跟踪

**核心功能**:
```go
type TaskScheduler interface {
    // 提交执行任务
    SubmitTask(ctx context.Context, req *ExecutionRequest) (*Task, error)
    
    // 获取任务状态
    GetTaskStatus(ctx context.Context, taskID string) (*TaskStatus, error)
    
    // 取消任务
    CancelTask(ctx context.Context, taskID string) error
    
    // 获取任务结果
    GetTaskResult(ctx context.Context, taskID string) (*TaskResult, error)
}

type ExecutionRequest struct {
    AppID       string                 `json:"app_id"`       // 应用ID
    ScriptPath  string                 `json:"script_path"`  // 脚本路径
    Parameters  map[string]interface{} `json:"parameters"`   // 执行参数
    Environment map[string]string      `json:"environment"`  // 环境变量
    Timeout     time.Duration          `json:"timeout"`      // 超时时间
    Priority    int                    `json:"priority"`     // 优先级
    Callback    string                 `json:"callback"`     // 回调URL
}
```

### 2.2 执行引擎 (ExecutionEngine)

**职责**:
- 容器镜像准备和启动
- Python脚本执行管理
- 实时日志收集
- 执行状态监控

**核心功能**:
```go
type ExecutionEngine interface {
    // 执行Python脚本
    ExecuteScript(ctx context.Context, task *Task) (*ExecutionResult, error)
    
    // 获取执行日志
    GetExecutionLogs(ctx context.Context, taskID string) ([]LogEntry, error)
    
    // 停止执行
    StopExecution(ctx context.Context, taskID string) error
}

type ExecutionResult struct {
    TaskID      string                 `json:"task_id"`
    Status      ExecutionStatus        `json:"status"`
    ExitCode    int                    `json:"exit_code"`
    Output      string                 `json:"output"`
    Error       string                 `json:"error"`
    StartTime   time.Time              `json:"start_time"`
    EndTime     time.Time              `json:"end_time"`
    Duration    time.Duration          `json:"duration"`
    Resources   ResourceUsage          `json:"resources"`
    Artifacts   []Artifact             `json:"artifacts"`
}
```

### 2.3 结果收集器 (ResultCollector)

**职责**:
- 执行结果数据收集
- 输出文件管理
- 结果数据持久化
- 结果查询和导出

**核心功能**:
```go
type ResultCollector interface {
    // 收集执行结果
    CollectResult(ctx context.Context, taskID string, result *ExecutionResult) error
    
    // 收集输出文件
    CollectArtifacts(ctx context.Context, taskID string, artifacts []Artifact) error
    
    // 查询执行历史
    QueryExecutionHistory(ctx context.Context, filter *HistoryFilter) ([]*ExecutionRecord, error)
    
    // 清理过期结果
    CleanupExpiredResults(ctx context.Context, retentionDays int) error
}
```

### 2.4 容器管理器 (ContainerManager)

**职责**:
- 容器生命周期管理
- 镜像缓存和预热
- 资源配额控制
- 容器健康检查

**核心功能**:
```go
type ContainerManager interface {
    // 创建执行容器
    CreateContainer(ctx context.Context, spec *ContainerSpec) (*Container, error)
    
    // 启动容器
    StartContainer(ctx context.Context, containerID string) error
    
    // 停止并删除容器
    DestroyContainer(ctx context.Context, containerID string) error
    
    // 获取容器状态
    GetContainerStatus(ctx context.Context, containerID string) (*ContainerStatus, error)
    
    // 预热镜像
    WarmupImage(ctx context.Context, imageTag string) error
}
```

## 3. 工作流程设计

### 3.1 完整执行流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 脚本执行API
    participant Scheduler as 任务调度器
    participant Engine as 执行引擎
    participant Container as 容器管理器
    participant Collector as 结果收集器
    
    Client->>API: 提交脚本执行请求
    API->>Scheduler: 创建执行任务
    Scheduler->>Engine: 分配执行任务
    Engine->>Container: 创建执行容器
    Container->>Container: 启动Python容器
    Engine->>Container: 执行Python脚本
    Container->>Engine: 返回执行结果
    Engine->>Collector: 收集执行结果
    Collector->>Collector: 持久化结果数据
    Engine->>Container: 销毁容器
    Engine->>Scheduler: 更新任务状态
    Scheduler->>API: 返回执行结果
    API->>Client: 响应执行结果
```

### 3.2 任务状态流转

```
PENDING → QUEUED → RUNNING → COMPLETED
    ↓         ↓        ↓         ↓
  FAILED   FAILED   FAILED   SUCCESS
    ↓         ↓        ↓         ↓
 CLEANUP  CLEANUP  CLEANUP   CLEANUP
```

## 4. 数据模型设计

### 4.1 执行任务模型

```go
type Task struct {
    ID           string                 `json:"id" gorm:"primaryKey"`
    AppID        string                 `json:"app_id" gorm:"not null"`
    UserID       string                 `json:"user_id" gorm:"not null"`
    TenantID     string                 `json:"tenant_id" gorm:"not null"`
    ScriptPath   string                 `json:"script_path" gorm:"not null"`
    Parameters   datatypes.JSON         `json:"parameters"`
    Environment  datatypes.JSON         `json:"environment"`
    Status       TaskStatus             `json:"status" gorm:"not null"`
    Priority     int                    `json:"priority" gorm:"default:0"`
    Timeout      int                    `json:"timeout" gorm:"default:300"` // 秒
    ContainerID  string                 `json:"container_id"`
    ImageTag     string                 `json:"image_tag"`
    StartTime    *time.Time             `json:"start_time"`
    EndTime      *time.Time             `json:"end_time"`
    Duration     int                    `json:"duration"` // 秒
    ExitCode     *int                   `json:"exit_code"`
    Output       string                 `json:"output" gorm:"type:text"`
    Error        string                 `json:"error" gorm:"type:text"`
    Resources    datatypes.JSON         `json:"resources"`
    Callback     string                 `json:"callback"`
    CreatedAt    time.Time              `json:"created_at"`
    UpdatedAt    time.Time              `json:"updated_at"`
}

type TaskStatus string

const (
    TaskStatusPending   TaskStatus = "pending"
    TaskStatusQueued    TaskStatus = "queued"
    TaskStatusRunning   TaskStatus = "running"
    TaskStatusCompleted TaskStatus = "completed"
    TaskStatusFailed    TaskStatus = "failed"
    TaskStatusCanceled  TaskStatus = "canceled"
    TaskStatusTimeout   TaskStatus = "timeout"
)
```

### 4.2 执行结果模型

```go
type ExecutionRecord struct {
    ID          string         `json:"id" gorm:"primaryKey"`
    TaskID      string         `json:"task_id" gorm:"not null;index"`
    AppID       string         `json:"app_id" gorm:"not null;index"`
    UserID      string         `json:"user_id" gorm:"not null;index"`
    TenantID    string         `json:"tenant_id" gorm:"not null;index"`
    Status      TaskStatus     `json:"status" gorm:"not null"`
    ExitCode    int            `json:"exit_code"`
    Output      string         `json:"output" gorm:"type:text"`
    Error       string         `json:"error" gorm:"type:text"`
    StartTime   time.Time      `json:"start_time"`
    EndTime     time.Time      `json:"end_time"`
    Duration    int            `json:"duration"`
    Resources   datatypes.JSON `json:"resources"`
    Artifacts   datatypes.JSON `json:"artifacts"`
    CreatedAt   time.Time      `json:"created_at"`
}

type Artifact struct {
    Name     string `json:"name"`
    Path     string `json:"path"`
    Size     int64  `json:"size"`
    MimeType string `json:"mime_type"`
    URL      string `json:"url"`
}
```

## 5. API接口设计

### 5.1 脚本执行API

```http
POST /api/v1/scripts/execute
Content-Type: application/json

{
    "app_id": "app-123",
    "script_path": "main.py",
    "parameters": {
        "input_file": "data.csv",
        "output_format": "json"
    },
    "environment": {
        "PYTHONPATH": "/app/lib"
    },
    "timeout": 300,
    "priority": 1,
    "callback": "https://example.com/webhook"
}
```

### 5.2 任务状态查询API

```http
GET /api/v1/scripts/tasks/{task_id}

Response:
{
    "id": "task-456",
    "status": "running",
    "progress": 65,
    "start_time": "2024-01-01T10:00:00Z",
    "estimated_completion": "2024-01-01T10:05:00Z"
}
```

### 5.3 结果获取API

```http
GET /api/v1/scripts/tasks/{task_id}/result

Response:
{
    "task_id": "task-456",
    "status": "completed",
    "exit_code": 0,
    "output": "Processing completed successfully",
    "duration": 245,
    "artifacts": [
        {
            "name": "result.json",
            "url": "/api/v1/scripts/tasks/task-456/artifacts/result.json",
            "size": 1024
        }
    ]
}
```

## 6. 性能优化策略

### 6.1 镜像预热机制

- 常用Python镜像预加载
- 依赖包缓存层优化
- 镜像分层构建策略

### 6.2 容器池管理

- 预创建容器池
- 容器复用机制
- 智能容器调度

### 6.3 资源优化

- 内存和CPU配额动态调整
- 并发执行限制
- 资源使用监控和告警

## 7. 安全考虑

### 7.1 执行环境隔离

- 容器网络隔离
- 文件系统只读挂载
- 用户权限限制

### 7.2 代码安全

- 脚本内容扫描
- 危险操作检测
- 资源使用限制

### 7.3 数据安全

- 执行参数加密存储
- 结果数据访问控制
- 审计日志记录

## 8. 监控和运维

### 8.1 指标监控

- 任务执行成功率
- 平均执行时间
- 资源使用率
- 错误率统计

### 8.2 日志管理

- 结构化日志输出
- 日志聚合和分析
- 错误日志告警

### 8.3 故障处理

- 自动重试机制
- 故障转移策略
- 容器健康检查
