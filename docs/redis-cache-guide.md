# Redis 缓存条件启用机制使用指南

## 概述

本项目实现了 Redis 缓存的条件启用机制，当 Redis 不可用时会自动降级到内存缓存，确保应用在没有 Redis 的情况下仍能正常运行。

## 特性

- **条件启用**: 自动检测 Redis 配置和连接状态
- **优雅降级**: Redis 不可用时自动切换到内存缓存
- **配置灵活**: 支持通过配置文件控制启用状态
- **健康检查**: 提供缓存服务健康检查功能
- **统计信息**: 提供详细的缓存使用统计

## 配置说明

### 配置文件示例 (configs/app-manager.yaml)

```yaml
# Redis 配置 (用于缓存和消息队列)
# 支持条件启用：当 Redis 不可用时自动降级到内存缓存
redis:
  enabled: true                 # 是否启用 Redis 缓存 (false 时使用内存缓存)
  addr: "localhost:6379"        # Redis 地址
  password: ""                  # Redis 密码
  db: 0                         # Redis 数据库编号
  pool_size: 10                 # 连接池大小
  min_idle_conns: 5             # 最小空闲连接数
  dial_timeout: 5s              # 连接超时
  read_timeout: 3s              # 读取超时
  write_timeout: 3s             # 写入超时
```

### 环境变量配置

```bash
# Redis 配置
REDIS_ENABLED=true
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5
REDIS_DIAL_TIMEOUT=5s
REDIS_READ_TIMEOUT=3s
REDIS_WRITE_TIMEOUT=3s
```

## 使用方法

### 1. 基本使用

```go
package main

import (
    "context"
    "time"
    "paas-platform/pkg/cache"
)

func main() {
    // 初始化 Redis 客户端（支持条件启用）
    redisClient := cache.NewRedisClient()
    defer redisClient.Close()
    
    // 创建内存缓存作为降级方案
    memoryCache := cache.NewMemoryCache()
    defer memoryCache.Close()
    
    // 创建缓存包装器，提供优雅降级
    cacheWrapper := cache.NewCacheWrapper(redisClient, memoryCache)
    
    // 创建缓存服务
    cacheService := cache.NewCacheService(cacheWrapper, "myapp", 10*time.Minute)
    
    ctx := context.Background()
    
    // 设置缓存
    user := map[string]interface{}{
        "id":   "123",
        "name": "张三",
        "role": "admin",
    }
    
    if err := cacheService.Set(ctx, "user:123", user); err != nil {
        log.Printf("设置缓存失败: %v", err)
    }
    
    // 获取缓存
    var cachedUser map[string]interface{}
    if err := cacheService.Get(ctx, "user:123", &cachedUser); err != nil {
        if err == cache.ErrCacheMiss {
            log.Println("缓存未命中")
        } else {
            log.Printf("获取缓存失败: %v", err)
        }
    } else {
        log.Printf("缓存命中: %+v", cachedUser)
    }
}
```

### 2. GetOrSet 模式

```go
// 使用 GetOrSet 模式，缓存未命中时自动从数据源获取
var userData map[string]interface{}
err := cacheService.GetOrSet(ctx, "user:456", &userData, func() (interface{}, error) {
    // 从数据库获取数据
    return getUserFromDatabase("456")
})

if err != nil {
    log.Printf("GetOrSet 失败: %v", err)
} else {
    log.Printf("获取用户数据: %+v", userData)
}
```

### 3. 应用级缓存服务

```go
// 创建应用级缓存服务
appCacheService := cache.NewApplicationCacheService()
defer appCacheService.Close()

// 获取应用信息（带缓存）
app, err := appCacheService.GetApplication(ctx, "app-123", func(appID string) (*cache.Application, error) {
    // 从数据库获取应用信息
    return getApplicationFromDB(appID)
})

if err != nil {
    log.Printf("获取应用信息失败: %v", err)
} else {
    log.Printf("应用信息: %+v", app)
}

// 缓存应用配置
config := map[string]string{
    "env":     "production",
    "version": "1.0.0",
}
err = appCacheService.CacheApplicationConfig(ctx, "app-123", config)
if err != nil {
    log.Printf("缓存配置失败: %v", err)
}

// 使应用缓存失效
err = appCacheService.InvalidateApplication(ctx, "app-123")
if err != nil {
    log.Printf("缓存失效失败: %v", err)
}
```

## 降级机制

### 自动降级场景

1. **配置禁用**: `redis.enabled = false`
2. **地址未配置**: `redis.addr` 为空
3. **连接失败**: Redis 服务器不可达
4. **认证失败**: Redis 密码错误
5. **运行时错误**: Redis 服务器运行时出现问题

### 降级行为

- **写操作**: 自动切换到内存缓存
- **读操作**: 从内存缓存读取
- **删除操作**: 从内存缓存删除
- **健康检查**: 返回内存缓存状态

## 健康检查

```go
// 检查缓存服务状态
if cacheService.IsEnabled() {
    log.Println("缓存服务已启用")
} else {
    log.Println("缓存服务已禁用")
}

// 执行健康检查
if err := cacheService.HealthCheck(ctx); err != nil {
    log.Printf("缓存健康检查失败: %v", err)
} else {
    log.Println("缓存健康检查通过")
}
```

## 统计信息

```go
// 获取缓存统计信息
stats := appCacheService.GetCacheStats()
log.Printf("缓存统计: %+v", stats)

// 示例输出:
// {
//   "enabled": true,
//   "prefix": "app",
//   "redis": {
//     "enabled": true,
//     "config": {...},
//     "pool_stats": {
//       "hits": 1234,
//       "misses": 56,
//       "timeouts": 0,
//       "total_conns": 10,
//       "idle_conns": 8,
//       "stale_conns": 0
//     }
//   }
// }
```

## 最佳实践

### 1. 错误处理

```go
// 缓存操作失败不应该影响业务逻辑
user, err := getUserFromCache(ctx, userID)
if err != nil {
    // 缓存失败，从数据库获取
    user, err = getUserFromDatabase(userID)
    if err != nil {
        return nil, err
    }
    
    // 异步更新缓存
    go func() {
        cacheService.Set(context.Background(), fmt.Sprintf("user:%s", userID), user)
    }()
}
```

### 2. 缓存键命名

```go
// 使用有意义的前缀和层次结构
const (
    UserCachePrefix   = "user"
    AppCachePrefix    = "app"
    ConfigCachePrefix = "config"
)

// 构建缓存键
userKey := fmt.Sprintf("%s:info:%s", UserCachePrefix, userID)
appConfigKey := fmt.Sprintf("%s:config:%s", AppCachePrefix, appID)
```

### 3. TTL 设置

```go
// 根据数据特性设置合适的 TTL
const (
    UserInfoTTL    = 30 * time.Minute  // 用户信息
    AppConfigTTL   = 1 * time.Hour     // 应用配置
    TempDataTTL    = 5 * time.Minute   // 临时数据
)
```

### 4. 批量操作

```go
// 批量删除相关缓存
keys := []string{
    fmt.Sprintf("user:info:%s", userID),
    fmt.Sprintf("user:profile:%s", userID),
    fmt.Sprintf("user:permissions:%s", userID),
}
cacheService.Delete(ctx, keys...)
```

## 监控和调试

### 1. 日志记录

缓存服务会自动记录以下信息：
- Redis 连接状态
- 降级事件
- 错误信息

### 2. 指标监控

建议监控以下指标：
- 缓存命中率
- 连接池状态
- 错误率
- 响应时间

### 3. 调试模式

```go
// 在开发环境启用详细日志
if os.Getenv("DEBUG") == "true" {
    // 启用 Redis 客户端调试日志
}
```

## 故障排除

### 常见问题

1. **Redis 连接失败**
   - 检查 Redis 服务器是否运行
   - 验证网络连接
   - 确认认证信息

2. **缓存未命中率高**
   - 检查 TTL 设置
   - 验证缓存键命名
   - 确认数据一致性

3. **内存使用过高**
   - 检查内存缓存大小
   - 设置合适的清理策略
   - 监控缓存项数量

### 调试命令

```bash
# 检查 Redis 连接
redis-cli -h localhost -p 6379 ping

# 查看 Redis 信息
redis-cli -h localhost -p 6379 info

# 监控 Redis 命令
redis-cli -h localhost -p 6379 monitor
```
