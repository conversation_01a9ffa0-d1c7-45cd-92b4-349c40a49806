{"swagger": "2.0", "info": {"description": "PaaS 平台的应用管理服务，提供应用生命周期管理功能", "title": "PaaS 平台应用管理服务 API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8082", "basePath": "/api/v1", "paths": {"/apps": {"get": {"description": "获取应用列表，支持分页和过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用管理"], "summary": "获取应用列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页大小", "name": "page_size", "in": "query"}, {"type": "string", "description": "应用名称过滤", "name": "name", "in": "query"}, {"type": "string", "description": "状态过滤", "name": "status", "in": "query"}], "responses": {"200": {"description": "获取成功", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/Application"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "page_size": {"type": "integer"}}}, "message": {"type": "string"}}}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "post": {"description": "创建新的应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用管理"], "summary": "创建应用", "parameters": [{"description": "创建应用请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateAppRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"$ref": "#/definitions/Application"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/apps/{id}": {"get": {"description": "根据应用ID获取详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用管理"], "summary": "获取应用详情", "parameters": [{"type": "string", "description": "应用ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"$ref": "#/definitions/Application"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "put": {"description": "更新应用信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用管理"], "summary": "更新应用", "parameters": [{"type": "string", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"description": "更新应用请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateAppRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/Application"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "delete": {"description": "删除指定的应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用管理"], "summary": "删除应用", "parameters": [{"type": "string", "description": "应用ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"type": "object", "properties": {"message": {"type": "string"}}}}, "404": {"description": "应用不存在", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}}, "definitions": {"Application": {"type": "object", "properties": {"id": {"type": "string", "description": "应用ID"}, "name": {"type": "string", "description": "应用名称"}, "description": {"type": "string", "description": "应用描述"}, "status": {"type": "string", "description": "应用状态"}, "language": {"type": "string", "description": "编程语言"}, "framework": {"type": "string", "description": "应用框架"}, "created_at": {"type": "string", "description": "创建时间"}, "updated_at": {"type": "string", "description": "更新时间"}}}, "CreateAppRequest": {"type": "object", "required": ["name", "language"], "properties": {"name": {"type": "string", "description": "应用名称"}, "description": {"type": "string", "description": "应用描述"}, "language": {"type": "string", "description": "编程语言"}, "framework": {"type": "string", "description": "应用框架"}}}, "UpdateAppRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "应用名称"}, "description": {"type": "string", "description": "应用描述"}, "framework": {"type": "string", "description": "应用框架"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "错误代码"}, "message": {"type": "string", "description": "错误消息"}, "details": {"type": "string", "description": "错误详情"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}