# 应用规范文档

## 1. 应用配置文件格式

### 1.1 paas.yaml 配置文件

每个应用的根目录必须包含 `paas.yaml` 配置文件，定义应用的基本信息和运行配置。

```yaml
# paas.yaml 示例
apiVersion: v1
kind: Application
metadata:
  name: my-nodejs-app           # 应用名称，必须唯一
  description: "我的 Node.js 应用"  # 应用描述
  version: "1.0.0"              # 应用版本
  labels:                       # 应用标签
    team: backend
    env: production

spec:
  # 应用运行时配置
  runtime:
    language: nodejs            # 支持: nodejs, python
    version: "18"               # 语言版本
    framework: express          # 框架类型
    
  # 构建配置
  build:
    dockerfile: Dockerfile      # Dockerfile 路径 (可选)
    buildArgs:                  # 构建参数
      NODE_ENV: production
    excludes:                   # 构建时排除的文件
      - "*.log"
      - "node_modules"
      - ".git"
    
  # 运行配置
  deploy:
    port: 3000                  # 应用监听端口
    instances: 2                # 实例数量
    resources:                  # 资源限制
      cpu: "500m"               # CPU 限制 (毫核)
      memory: "512Mi"           # 内存限制
      disk: "1Gi"               # 磁盘限制
    
    # 环境变量
    env:
      NODE_ENV: production
      DATABASE_URL: "${DATABASE_URL}"  # 支持变量替换
      
    # 密钥引用
    secrets:
      - name: db-password
        key: password
        env: DB_PASSWORD
    
    # 健康检查
    healthCheck:
      path: "/health"           # 健康检查路径
      port: 3000                # 健康检查端口
      initialDelaySeconds: 30   # 初始延迟
      periodSeconds: 10         # 检查间隔
      timeoutSeconds: 5         # 超时时间
      failureThreshold: 3       # 失败阈值
    
    # 自动扩缩容
    autoscaling:
      enabled: true
      minReplicas: 1
      maxReplicas: 10
      targetCPUUtilization: 70  # CPU 使用率阈值
      targetMemoryUtilization: 80  # 内存使用率阈值

  # 服务配置
  service:
    type: ClusterIP             # 服务类型: ClusterIP, NodePort, LoadBalancer
    ports:
      - name: http
        port: 80
        targetPort: 3000
        protocol: TCP
    
  # 路由配置
  ingress:
    enabled: true
    host: my-app.example.com    # 域名
    path: "/"                   # 路径
    tls:                        # TLS 配置
      enabled: true
      secretName: my-app-tls

  # 存储配置
  volumes:
    - name: app-data
      type: persistent          # persistent, temporary
      size: "10Gi"
      mountPath: "/app/data"
    - name: logs
      type: temporary
      size: "1Gi"
      mountPath: "/app/logs"
```

### 1.2 Python 应用配置示例

```yaml
apiVersion: v1
kind: Application
metadata:
  name: my-python-app
  description: "我的 Python Flask 应用"
  version: "1.0.0"

spec:
  runtime:
    language: python
    version: "3.11"
    framework: flask
    
  build:
    requirements: requirements.txt  # Python 依赖文件
    buildArgs:
      PYTHON_ENV: production
    
  deploy:
    port: 5000
    instances: 3
    command: ["python", "app.py"]  # 启动命令
    workingDir: "/app"
    
    resources:
      cpu: "300m"
      memory: "256Mi"
      
    env:
      FLASK_ENV: production
      DATABASE_URL: "${DATABASE_URL}"
      
    healthCheck:
      path: "/health"
      port: 5000
      initialDelaySeconds: 20
      periodSeconds: 10
```

## 2. 应用生命周期管理规范

### 2.1 生命周期状态

```
创建 -> 构建中 -> 构建失败/成功 -> 部署中 -> 运行中 -> 停止 -> 删除
  │        │         │              │        │      │      │
  └────────┴─────────┴──────────────┴────────┴──────┴──────┘
```

### 2.2 状态定义

- **CREATED**: 应用已创建，等待构建
- **BUILDING**: 正在构建应用镜像
- **BUILD_FAILED**: 构建失败
- **BUILD_SUCCESS**: 构建成功
- **DEPLOYING**: 正在部署应用
- **RUNNING**: 应用正常运行
- **STOPPED**: 应用已停止
- **ERROR**: 应用运行异常
- **DELETED**: 应用已删除

### 2.3 生命周期操作

#### 部署操作
1. 验证 paas.yaml 配置
2. 创建应用记录
3. 触发构建流程
4. 创建容器实例
5. 配置负载均衡
6. 启动健康检查

#### 更新操作
1. 验证新配置
2. 创建新版本
3. 滚动更新实例
4. 验证部署结果
5. 清理旧版本

#### 回滚操作
1. 选择回滚版本
2. 停止当前版本
3. 启动目标版本
4. 验证回滚结果
5. 更新路由配置

## 3. 资源限制和环境变量配置规范

### 3.1 资源限制格式

```yaml
resources:
  requests:                     # 资源请求 (最小保证)
    cpu: "100m"                # CPU: 毫核为单位
    memory: "128Mi"            # 内存: Mi/Gi 为单位
    disk: "1Gi"                # 磁盘: Mi/Gi 为单位
  limits:                      # 资源限制 (最大使用)
    cpu: "500m"
    memory: "512Mi"
    disk: "5Gi"
```

### 3.2 环境变量配置

```yaml
env:
  # 直接配置
  NODE_ENV: production
  PORT: "3000"
  
  # 引用配置服务
  DATABASE_URL: "${config:database.url}"
  
  # 引用密钥
  API_KEY: "${secret:api-keys.primary}"
  
  # 系统变量
  POD_NAME: "${system:pod.name}"
  POD_IP: "${system:pod.ip}"
```

## 4. 健康检查和监控接口规范

### 4.1 健康检查接口

应用必须实现以下健康检查接口：

#### GET /health
```json
{
  "status": "healthy",          // healthy, unhealthy, unknown
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "external_api": "unhealthy"
  },
  "uptime": 3600               // 运行时间 (秒)
}
```

#### GET /ready
```json
{
  "ready": true,               // 是否准备就绪
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 4.2 监控指标接口

#### GET /metrics
```
# HELP app_requests_total 应用请求总数
# TYPE app_requests_total counter
app_requests_total{method="GET",status="200"} 1234

# HELP app_request_duration_seconds 请求处理时间
# TYPE app_request_duration_seconds histogram
app_request_duration_seconds_bucket{le="0.1"} 100
app_request_duration_seconds_bucket{le="0.5"} 200
app_request_duration_seconds_bucket{le="1.0"} 300

# HELP app_memory_usage_bytes 内存使用量
# TYPE app_memory_usage_bytes gauge
app_memory_usage_bytes 134217728
```

### 4.3 日志格式规范

应用日志必须采用结构化 JSON 格式：

```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "INFO",             // DEBUG, INFO, WARN, ERROR, FATAL
  "message": "用户登录成功",
  "service": "my-nodejs-app",
  "version": "1.0.0",
  "trace_id": "abc123",        // 链路追踪 ID
  "user_id": "user123",
  "request_id": "req456",
  "extra": {                   // 额外字段
    "ip": "***********",
    "user_agent": "Mozilla/5.0..."
  }
}
```

## 5. 应用类型支持

### 5.1 Node.js 应用

#### 检测规则
- 存在 `package.json` 文件
- 存在 `node_modules` 目录或 `package-lock.json`

#### 构建流程
1. 检测 Node.js 版本
2. 安装依赖: `npm install` 或 `yarn install`
3. 运行测试: `npm test` (可选)
4. 构建应用: `npm run build` (可选)
5. 创建 Docker 镜像

#### 默认启动命令
- `npm start` 或 `node index.js`

### 5.2 Python 应用

#### 检测规则
- 存在 `requirements.txt` 或 `pyproject.toml`
- 存在 `app.py` 或 `main.py`

#### 构建流程
1. 检测 Python 版本
2. 创建虚拟环境
3. 安装依赖: `pip install -r requirements.txt`
4. 运行测试: `pytest` (可选)
5. 创建 Docker 镜像

#### 默认启动命令
- `python app.py` 或 `gunicorn app:app`

## 6. 版本管理规范

### 6.1 版本号格式
- 语义化版本: `major.minor.patch`
- Git 标签版本: `v1.0.0`
- 构建版本: `1.0.0-build.123`

### 6.2 版本策略
- **蓝绿部署**: 零停机更新
- **滚动更新**: 逐步替换实例
- **金丝雀发布**: 小流量验证

### 6.3 回滚机制
- 支持回滚到任意历史版本
- 自动保留最近 10 个版本
- 快速回滚 (< 30 秒)
