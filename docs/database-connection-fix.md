# 数据库连接问题修复指南

## 问题描述

在运行 `go run cmd/app-manager/main.go` 时遇到数据库连接错误：

```
unable to open database file: no such file or directory
```

错误发生在 `/internal/database/database.go:77` 行。

## 问题分析

### 根本原因

1. **数据库文件路径不存在**：应用配置使用 SQLite 数据库，默认路径为 `./data/app-manager.db`
2. **目录未创建**：`data` 目录不存在，导致 SQLite 无法创建数据库文件
3. **缺少目录检查逻辑**：原始代码没有检查和创建必要的目录结构

### 配置分析

**默认配置** (`cmd/app-manager/main.go`):
```go
viper.SetDefault("database.driver", "sqlite")
viper.SetDefault("database.dsn", "./data/app-manager.db")
```

**配置文件** (`configs/app-manager.yaml`):
```yaml
database:
  driver: sqlite
  dsn: "./data/app-manager.db"
```

## 解决方案

### 1. 代码修复

#### 修改 `internal/database/database.go`

**添加导入包**:
```go
import (
    "fmt"
    "os"           // 新增
    "path/filepath" // 新增
    "time"
    // ... 其他导入
)
```

**修改数据库连接逻辑**:
```go
// NewConnectionWithConfig 使用指定配置创建数据库连接
func NewConnectionWithConfig(config *Config) (*gorm.DB, error) {
    var dialector gorm.Dialector
    
    // 根据驱动类型选择方言
    switch config.Driver {
    case "sqlite":
        // 确保 SQLite 数据库文件的目录存在
        if err := ensureSQLiteDir(config.DSN); err != nil {
            return nil, fmt.Errorf("创建 SQLite 数据库目录失败: %w", err)
        }
        dialector = sqlite.Open(config.DSN)
    case "postgres":
        dialector = postgres.Open(config.DSN)
    default:
        return nil, fmt.Errorf("不支持的数据库驱动: %s", config.Driver)
    }
    // ... 其余代码保持不变
}
```

**添加目录检查函数**:
```go
// ensureSQLiteDir 确保 SQLite 数据库文件的目录存在
func ensureSQLiteDir(dsn string) error {
    // 获取数据库文件的目录路径
    dir := filepath.Dir(dsn)
    
    // 如果目录不存在，创建目录
    if _, err := os.Stat(dir); os.IsNotExist(err) {
        if err := os.MkdirAll(dir, 0755); err != nil {
            return fmt.Errorf("创建目录 %s 失败: %w", dir, err)
        }
        fmt.Printf("已创建数据库目录: %s\n", dir)
    }
    
    return nil
}
```

### 2. 手动创建目录

如果不想修改代码，也可以手动创建必要的目录：

```bash
# 创建数据目录
mkdir -p data

# 创建日志目录
mkdir -p logs

# 创建存储目录
mkdir -p data/storage
```

### 3. 使用启动脚本

推荐使用提供的启动脚本，它会自动处理所有必要的初始化：

```bash
# 使用启动脚本
./scripts/start-app-manager.sh

# 后台启动
./scripts/start-app-manager.sh -d

# 仅构建
./scripts/start-app-manager.sh -b

# 查看帮助
./scripts/start-app-manager.sh -h
```

## 验证修复

### 1. 检查目录结构

修复后的目录结构应该如下：

```
paas/
├── data/
│   ├── app-manager.db      # SQLite 数据库文件
│   └── storage/            # 存储目录
├── logs/                   # 日志目录
├── configs/
│   └── app-manager.yaml    # 配置文件
└── ...
```

### 2. 启动应用

```bash
go run cmd/app-manager/main.go
```

成功启动后应该看到类似输出：

```
[GIN-debug] [WARNING] Running in "debug" mode.
[GIN-debug] GET    /health --> main.setupRouter.func1 (4 handlers)
...
{"timestamp":"2025-08-10T07:35:50Z","level":"INFO","service":"paas-platform","message":"应用管理服务启动","fields":{"port":8082}}
```

### 3. 健康检查

```bash
curl http://localhost:8082/health
```

预期响应：

```json
{
  "service": "app-manager",
  "status": "healthy",
  "timestamp": "2025-08-10T07:36:17Z",
  "version": "1.0.0"
}
```

### 4. 检查数据库文件

```bash
ls -la data/
```

应该看到 `app-manager.db` 文件已创建。

## 配置选项

### 环境变量配置

可以通过环境变量覆盖默认配置：

```bash
# 使用不同的数据库路径
export APP_MANAGER_DATABASE_DSN="./custom/path/database.db"

# 使用不同的端口
export APP_MANAGER_SERVER_PORT=8083

# 启动应用
go run cmd/app-manager/main.go
```

### PostgreSQL 配置

如果需要使用 PostgreSQL 而不是 SQLite：

```yaml
database:
  driver: postgres
  dsn: "host=localhost user=paas password=paas123 dbname=paas_platform port=5432 sslmode=disable TimeZone=Asia/Shanghai"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600
  log_level: error
```

## 常见问题

### Q1: 端口被占用

**错误**: `listen tcp :8082: bind: address already in use`

**解决方案**:
```bash
# 查找占用进程
lsof -i :8082

# 终止占用进程
kill -9 <PID>

# 或使用不同端口
export APP_MANAGER_SERVER_PORT=8083
```

### Q2: 权限问题

**错误**: `permission denied`

**解决方案**:
```bash
# 确保有写入权限
chmod 755 data/
chmod 644 data/app-manager.db
```

### Q3: 数据库迁移失败

**错误**: `数据库迁移失败`

**解决方案**:
```bash
# 删除现有数据库文件重新创建
rm data/app-manager.db

# 重新启动应用
go run cmd/app-manager/main.go
```

## 最佳实践

1. **使用启动脚本**：推荐使用 `scripts/start-app-manager.sh` 进行应用启动
2. **环境隔离**：不同环境使用不同的数据库配置
3. **备份数据**：定期备份 SQLite 数据库文件
4. **监控日志**：关注应用日志以及时发现问题
5. **健康检查**：定期检查 `/health` 接口确保服务正常

## 相关文件

- `internal/database/database.go` - 数据库连接逻辑
- `cmd/app-manager/main.go` - 应用入口和配置初始化
- `configs/app-manager.yaml` - 应用配置文件
- `scripts/start-app-manager.sh` - 启动脚本
- `docs/database-specification.md` - 数据库规范文档
